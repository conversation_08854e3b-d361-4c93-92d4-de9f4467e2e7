package com.onecity.os.system.api;
import com.onecity.os.common.core.constant.ServiceNameConstants;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.system.api.domain.MqMessageReqVo;
import com.onecity.os.system.api.factory.MqFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 日志服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "mqService", value = ServiceNameConstants.MQ_SERVICE, fallbackFactory = MqFallbackFactory.class)
public interface MqService
{
    /**
     * 发送消息
     *
     * @param reqVo 消息实体
     * @return 结果
     */
    @PostMapping("/message/sendMessage")
    BaseResult sendMessage(@RequestBody MqMessageReqVo reqVo);

}
