package com.onecity.os.system.api.domain;

import com.onecity.os.common.core.utils.StringUtils;

/**
 * 路由显示信息
 * 
 * <AUTHOR>
 */
public class MetaVo
{
    /**
     * 设置该路由在侧边栏和面包屑中展示的名字
     */
    private String title;

    /**
     * 设置该路由的图标，对应路径src/assets/icons/svg
     */
    private String icon;

    /**
     */
    private boolean keepAlive;

    /**
     * 内链地址（http(s)://开头）
     */
    private String link;
    /** true 外链 */
    private boolean internalOrExternal;
    public MetaVo()
    {
    }

    public MetaVo(String title, String icon)
    {
        this.title = title;
        this.icon = icon;
    }

    public MetaVo(String title, String icon, boolean keepAlive)
    {
        this.title = title;
        this.icon = icon;
        this.keepAlive = keepAlive;
    }

    public MetaVo(String title, String icon, String link)
    {
        this.title = title;
        this.icon = icon;
        this.link = link;
    }

    public MetaVo(String title, String icon, boolean keepAlive, String link, boolean internalOrExternal)
    {
        this.title = title;
        this.icon = icon;
        this.keepAlive = keepAlive;
        this.internalOrExternal = internalOrExternal;
        if (StringUtils.ishttp(link))
        {
            this.link = link;
        }
    }

    public boolean isKeepAlive() {
        return keepAlive;
    }

    public void setKeepAlive(boolean keepAlive) {
        this.keepAlive = keepAlive;
    }

    public String getTitle()
    {
        return title;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getIcon()
    {
        return icon;
    }

    public void setIcon(String icon)
    {
        this.icon = icon;
    }

    public String getLink()
    {
        return link;
    }

    public void setLink(String link)
    {
        this.link = link;
    }

    public boolean isInternalOrExternal() {
        return internalOrExternal;
    }

    public void setInternalOrExternal(boolean internalOrExternal) {
        this.internalOrExternal = internalOrExternal;
    }
}
