package com.onecity.os.system.api.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2022-07-19 17:39
 */
@Data
public class MqMessageReqVo<T> {
    /**
     * 交换机名称
     */
    @ApiModelProperty("交换机名称")
    private String exChangeName;
    /**
     * 交换机类型  普通交换机direct，主题交换机topic，广播交换机fanout
     */
    @ApiModelProperty("交换机类型  普通交换机direct，主题交换机topic，广播交换机fanout")
    private String exChangeType;
    /**
     * 路由键
     */
    @ApiModelProperty("路由键")
    private String routingKey;
    /**
     * 队列名称
     */
    @ApiModelProperty("队列名称")
    private String queueName;

    @NotNull(message = "消息内容不能为空")
    @ApiModelProperty("消息内容")
    private T messageContent;
}
