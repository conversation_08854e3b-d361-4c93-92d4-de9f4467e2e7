package com.onecity.os.system.api.factory;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.system.api.MqService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 日志服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class MqFallbackFactory implements FallbackFactory<MqService>
{
    private static final Logger log = LoggerFactory.getLogger(MqFallbackFactory.class);

    @Override
    public MqService create(Throwable throwable)
    {
        log.error("消息队列服务调用失败:{}", throwable.getMessage());
        return reqVo -> {
            log.error("消息队列服务调用失败:{}", throwable.getMessage());
            return BaseResult.fail("消息队列服务调用失败:{}"+throwable.getMessage());
        };

    }
}
