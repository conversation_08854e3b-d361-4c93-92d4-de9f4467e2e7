package com.onecity.os.system.api.factory;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.system.api.RemoteIndicatorService;
import com.onecity.os.system.api.model.IndicatorDataExcel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteIndicatorFallbackFactory implements FallbackFactory<RemoteIndicatorService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteIndicatorFallbackFactory.class);

    @Override
    public RemoteIndicatorService create(Throwable throwable)
    {
        log.error("PC端指标服务调用失败:{}", throwable.getMessage());
        return new RemoteIndicatorService() {
            @Override
            public BaseResult insertIndicatorDataExcel(List<IndicatorDataExcel> listDatas, String indicatorId) {
                return BaseResult.fail("添加指标数据失败！:{}" + throwable.getMessage());
            }
        };
    }
}
