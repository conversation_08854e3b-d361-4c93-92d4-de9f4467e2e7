package com.onecity.os.system.api.factory;

import com.onecity.os.system.api.RemoteLogService;
import com.onecity.os.system.api.domain.DingOperateDataExcel;
import com.onecity.os.system.api.domain.SysOperLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.system.api.domain.SysLogininfor;

import java.util.List;

/**
 * 日志服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteLogFallbackFactory implements FallbackFactory<RemoteLogService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteLogFallbackFactory.class);

    @Override
    public RemoteLogService create(Throwable throwable)
    {
        log.error("日志服务调用失败:{}", throwable.getMessage());
        return new RemoteLogService()
        {
            @Override
            public BaseResult<Boolean> saveLog(SysOperLog sysOperLog)
            {
                log.error("日志服务调用失败:{}", throwable.getMessage());
                return BaseResult.fail("日志服务调用失败:{}"+throwable.getMessage());
            }

            @Override
            public BaseResult<Boolean> saveLogininfor(SysLogininfor sysLogininfor)
            {
                return null;
            }

            @Override
            public List<DingOperateDataExcel> exportOperateLog(String dayStart, String dayEnd) {
                return null;
            }
        };

    }
}
