package com.onecity.os.system.api.factory;

import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.system.api.RemoteUserService;
import com.onecity.os.system.api.domain.*;
import com.onecity.os.system.api.domain.dto.DepartUserListDto;
import com.onecity.os.system.api.domain.dto.UserNameDto;
import com.onecity.os.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import com.onecity.os.common.core.domain.BaseResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 用户服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteUserFallbackFactory implements FallbackFactory<RemoteUserService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteUserFallbackFactory.class);

    @Override
    public RemoteUserService create(Throwable throwable)
    {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new RemoteUserService()
        {
            @Override
            public BaseResult<LoginUser> getUserInfo(String username, String source)
            {
                return BaseResult.fail("获取用户失败:" + throwable.getMessage());
            }
            @Override
            public BaseResult<LoginUser> getUserInfo1(String username, String source)
            {
                return BaseResult.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public BaseResult<LoginUser> getUserInfoByPhoneNumber(String mobile, String source) {
                return BaseResult.fail("获取用户失败:" + throwable.getMessage());
            }
            @Override
            public BaseResult<List<SysMenu>> getMenuByUserId(Long userId) {
                return BaseResult.fail("获取菜单失败:" + throwable.getMessage());
            }
            @Override
            public BaseResult<Boolean> registerUserInfo(SysUser sysUser, String source)
            {
                return BaseResult.fail("注册用户失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult add(SysUser user) {
                log.error("新增用户列表失败！:{}", throwable.getMessage());
                return AjaxResult.error("新增用户列表失败！:{}", throwable.getMessage());
            }

            @Override
            public AjaxResult edit(SysUser user) {
                log.error("修改用户失败！:{}", throwable.getMessage());
                return AjaxResult.error("修改用户失败！:{}", throwable.getMessage());
            }

            @Override
            public TableDataInfo list(SysUser user) {
                log.error("分页获取用户列表失败！:{}", throwable.getMessage());
                return null;
            }

            @Override
            public BaseResult getInfoByNameAndParentId(String name, String parentId) {
                return BaseResult.fail("查询厅局菜单失败！:{}" + throwable.getMessage());
            }
            /**
             * 跟据厅局编码查找现在存在的厅局菜单
             * @param sourceSimpleName
             * @param path
             * @return
             */
            @Override
            public BaseResult<SysMenu> getInfoBySourceSimpleNameAndPath(@RequestParam(name = "sourceSimpleName") String sourceSimpleName, @RequestParam(name = "path") String path){
                return BaseResult.fail("查询厅局菜单失败！:{}" + throwable.getMessage());
            }


            /**
             * 跟据厅局编码与菜单父级id更新菜单名称
             * @param menuName
             * @param sourceSimpleName
             * @param path
             * @return
             */
            @Override
            public BaseResult updateNameBySourceSimpleNameAndPath(@RequestParam(name = "menuName") String menuName,@RequestParam(name = "sourceSimpleName") String sourceSimpleName, @RequestParam(name = "path") String path) {
                return BaseResult.fail("更新菜单状态失败！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult updateStatusByParam(String name, Long parentId, String status) {
                return BaseResult.fail("更新菜单状态失败！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult updateStatusBySourceSimpleNameAndPath(String status, String sourceSimpleName, String path) {
                return BaseResult.fail("更新菜单状态失败！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult addMenu(SysMenu menu) {
                return BaseResult.fail("新增菜单失败！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult<String> getPcUserNamesByPcUserIds(String[] ids) {
                return BaseResult.fail("获取PC端用户名称失败！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult<String> getPcUserIdsByPcUserNames(String[] names) {
                return BaseResult.fail("获取PC端用户ID失败！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult<String> getNickNameByName(String userName) {
                return BaseResult.fail("获取PC端用户真实姓名失败！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult<String> getDeptNamesById(String[] ids) {
                return BaseResult.fail("获取部门名称失败！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult<String> getUserIdsByRoles(String[] roleIds) {
                return BaseResult.fail("获取用户id失败！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult<List<SysUser>> getUsersByRoleIds(String[] roleIds) {
                return BaseResult.fail("通过角色ids获取用户失败！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult<List<SysRole>> getRoleNameByRoleIds(String[] roleIds) {
                return BaseResult.fail("获取用户角色名失败！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult<List<DepartUserListDto>> getUserNameByName(String name) {
                return BaseResult.fail("根据姓名查询有部门的人员信息！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult<List<DepartUserListDto>> getUserNameByNameAndSourceSimpleName(String name, String sourceSimpleName) {
                return BaseResult.fail("根据姓名和版块编码查询有部门的人员信息！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult<List<UserNameDto>> getUserNameByNameAndDepartId(String name) {
                return BaseResult.fail("根据姓名,查询没有部门的人员信息！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult<List<UserNameDto>> getUserNameNoDepart(String name, String sourceSimpleName) {
                return BaseResult.fail("根据姓名,版块编码查询没有部门的人员信息！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult<List<String>> getUserIdByMenu(String menu) {
                return BaseResult.fail("根据菜单没有查询到人员信息！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult<List<SysUser>> getUserByPcUserIds(String[] userIds) {
                return BaseResult.fail("根据用户ids查询到人员信息！:{}" + throwable.getMessage());
            }
            @Override
            public BaseResult<SysUser> getUserByOneCodeId(String oneCodeUserId) {
                return BaseResult.fail("根据oneCodeUserId查询到人员信息失败！:{}" + throwable.getMessage());
            }
            @Override
            public BaseResult<String> updateUserByOneCodeId(@RequestBody SysUser sysUser) {
                return BaseResult.fail("根据oneCodeUserId更新人员信息失败！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult getAuditUserList(@RequestParam(name = "deptId" , required = false) Long deptId,@RequestParam(name = "name") String name,@RequestParam(name = "sourceSimpleName") String sourceSimpleName){
                return BaseResult.fail("查询审核人列表信息失败！:{}" + throwable.getMessage());
            }

            @Override
            public BaseResult<List<Long>> getRoleIdsByUserId(Long userId) {
                return BaseResult.fail("根据用户ids查询到人员信息！:{}" + throwable.getMessage());
            }
            @Override
            public BaseResult<List<SysDictData>> dictType(@PathVariable("dictType") String dictType){
                return BaseResult.fail("根据字典编码查询到的字典数据信息！:{}" + throwable.getMessage());
            }
            @Override
            public BaseResult getInfoByType(@PathVariable("dictType") String dictType){
                return BaseResult.fail("增加数据字典类型失败:" + throwable.getMessage());
            }
            @Override
            public AjaxResult addDictType(SysDictType dict) {
                return AjaxResult.error("增加数据字典类型失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult addDictData(SysDictData dict) {
                return AjaxResult.error("增加数据字典数据失败:" + throwable.getMessage());
            }
            @Override
            public AjaxResult back(@PathVariable("dictId") Long dictId){
                return AjaxResult.error("数据字典取回失败:" + throwable.getMessage());
            }
            @Override
            public BaseResult<Boolean> getUserIndicatorTitlePerms(String sourceSimpleName, Long userId) {
                return BaseResult.fail("询用户指标头操作权限失败！:{}" + throwable.getMessage());
            }
            @Override
            public BaseResult<Boolean> getUserArchivePerms(Long userId) {
                return BaseResult.fail("询用户归档操作权限失败！:{}" + throwable.getMessage());
            }
            @Override
            public BaseResult<Boolean> getInformCleanPerms(Long userId) {
                return BaseResult.fail("询用户强制删除权限失败！:{}" + throwable.getMessage());
            }
        };
    }
}
