package com.ruoyi.auth.controller;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.JwtUtils;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.auth.form.LoginBody;
import com.ruoyi.auth.form.RegisterBody;
import com.ruoyi.auth.service.SysLoginService;
import com.ruoyi.auth.utils.RSAUtil;
import com.ruoyi.common.security.auth.AuthUtil;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * token 控制
 * 
 * <AUTHOR>
 */
@RestController
public class TokenController
{
    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysLoginService sysLoginService;
    @Value("${login.privateKey}")
    String privateKey;

    @PostMapping("login")
    public BaseResult<?> login(@RequestBody LoginBody form)
    {
        LoginUser userInfo = null;
        if(StringUtils.isNotEmpty(form.getSmscode())) {
            userInfo = sysLoginService.loginByMobile(form.getUsername(),form.getSmscode());
        }else {
            String password =  new RSAUtil().decrypt(privateKey,form.getPassword());
            userInfo = sysLoginService.login(form.getUsername(), password);
        }
        // 获取登录token
        return BaseResult.ok(tokenService.createToken(userInfo));
    }

    @DeleteMapping("logout")
    public BaseResult<?> logout(HttpServletRequest request)
    {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token))
        {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtil.logoutByToken(token);
            // 记录用户退出日志
            sysLoginService.logout(username);
        }
        return BaseResult.ok();
    }

    @PostMapping("refresh")
    public BaseResult<?> refresh(HttpServletRequest request)
    {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser))
        {
            // 刷新令牌有效期
            tokenService.refreshToken(loginUser);
            return BaseResult.ok();
        }
        return BaseResult.ok();
    }

    @PostMapping("register")
    public BaseResult<?> register(@RequestBody RegisterBody registerBody)
    {
        // 用户注册
        sysLoginService.register(registerBody.getUsername(), registerBody.getPassword(),
                registerBody.getNickName(),registerBody.getPhonenumber(),registerBody.getIdcode(),registerBody.getSmscode());
        return BaseResult.ok("注册成功！");
    }
}
