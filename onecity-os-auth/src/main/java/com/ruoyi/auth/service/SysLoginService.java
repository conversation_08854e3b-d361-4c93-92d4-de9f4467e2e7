package com.ruoyi.auth.service;

import com.onecity.os.common.core.utils.RegexUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.onecity.os.common.core.constant.Constants;
import com.onecity.os.common.core.constant.SecurityConstants;
import com.onecity.os.common.core.constant.UserConstants;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.enums.UserStatus;
import com.onecity.os.common.core.exception.ServiceException;
import com.onecity.os.common.core.utils.ServletUtils;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.common.core.utils.ip.IpUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.onecity.os.system.api.RemoteLogService;
import com.onecity.os.system.api.RemoteUserService;
import com.onecity.os.system.api.domain.SysLogininfor;
import com.onecity.os.system.api.domain.SysUser;
import com.onecity.os.system.api.model.LoginUser;

import java.util.Date;

/**
 * 登录校验方法
 * 
 * <AUTHOR>
 */
@Component
public class SysLoginService
{
    @Autowired
    private RemoteLogService remoteLogService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 登录
     */
    public LoginUser login(String userName, String password)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(userName, password))
        {
            recordLogininfor(userName, Constants.LOGIN_FAIL, "用户/密码必须填写");
//            throw new ServiceException("用户/密码必须填写");
            throw new ServiceException("用户名或密码错误");
        }
        // 密码如果不在指定范围内 错误
        if (!RegexUtils.checkPassword(password)) {
            recordLogininfor(userName, Constants.LOGIN_FAIL, "用户密码不符合要求");
            throw new ServiceException("用户名或密码错误");
        }
        // 用户名不符合要求 错误
        if (!RegexUtils.checkUserName(userName)) {
            recordLogininfor(userName, Constants.LOGIN_FAIL, "用户名不符合要求");
            throw new ServiceException("用户名或密码错误");
        }
        // 查询用户信息
        BaseResult<LoginUser> userResult = remoteUserService.getUserInfo1(userName, SecurityConstants.INNER);

        if (BaseResult.FAIL == userResult.getCode())
        {
            throw new ServiceException("用户名或密码错误");
        }

        if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData()))
        {
            recordLogininfor(userName, Constants.LOGIN_FAIL, "登录用户不存在");
//            throw new ServiceException("登录用户：" + username + " 不存在");
            throw new ServiceException("用户名或密码错误");
        }
        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
            recordLogininfor(userName, Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
//            throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
            throw new ServiceException("用户名或密码错误");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            recordLogininfor(userName, Constants.LOGIN_FAIL, "用户已停用，请联系管理员");
//            throw new ServiceException("对不起，您的账号：" + username + " 已停用");
            throw new ServiceException("用户名或密码错误");
        }
        if (!SecurityUtils.matchesPassword(password, user.getPassword()))
        {
            recordLogininfor(userName, Constants.LOGIN_FAIL, "用户密码错误");
//            throw new ServiceException("用户不存在/密码错误");
            throw new ServiceException("用户名或密码错误");
        }
        recordLogininfor(userName, Constants.LOGIN_SUCCESS, "登录成功");
        return userInfo;
    }

    /**
     * 登录
     */
    public LoginUser loginByMobile(String mobile,String smsCode)
    {
        if(!"999999".equals(smsCode)) {
            recordLogininfor(mobile, Constants.LOGIN_FAIL, "短信验证码错误");
            throw new ServiceException("短信验证码错误");
        }
        // 手机号为空 错误
        if (StringUtils.isAnyBlank(mobile))
        {
            recordLogininfor(mobile, Constants.LOGIN_FAIL, "手机号必须填写");
            throw new ServiceException("手机号必须填写");
        }
        // 用户名不在指定范围内 错误
        if (mobile.length() != 11)
        {
            recordLogininfor(mobile, Constants.LOGIN_FAIL, "手机号不在指定范围");
            throw new ServiceException("手机号不在指定范围");
        }
        // 查询用户信息
        BaseResult<LoginUser> userResult = remoteUserService.getUserInfoByPhoneNumber(mobile,SecurityConstants.INNER);

        if (BaseResult.FAIL == userResult.getCode())
        {
            throw new ServiceException(userResult.getMsg());
        }

        if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData()))
        {
            recordLogininfor(mobile, Constants.LOGIN_FAIL, "登录用户不存在");
            throw new ServiceException("登录用户：" + mobile + " 不存在");
        }
        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
            recordLogininfor(mobile, Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
            throw new ServiceException("对不起，您的账号：" + mobile + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            recordLogininfor(mobile, Constants.LOGIN_FAIL, "用户已停用，请联系管理员");
            throw new ServiceException("对不起，您的账号：" + mobile + " 已停用");
        }
//        if (!SecurityUtils.matchesPassword(password, user.getPassword()))
//        {
//            recordLogininfor(mobile, Constants.LOGIN_FAIL, "用户密码错误");
//            throw new ServiceException("用户不存在/密码错误");
//        }
        recordLogininfor(mobile, Constants.LOGIN_SUCCESS, "登录成功");
        return userInfo;
    }

    public void logout(String loginName)
    {
        recordLogininfor(loginName, Constants.LOGOUT, "退出成功");
    }

    /**
     * 注册
     */
    public void register(String username, String password,String nickName, String phonenumber,String IDCode,String smsCode)
    {
        if(!"999999".equals(smsCode)) {
            throw new ServiceException("短信验证码错误");
        }
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password))
        {
            throw new ServiceException("用户/密码必须填写");
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            throw new ServiceException("账户长度必须在2到20个字符之间");
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            throw new ServiceException("密码长度必须在5到20个字符之间");
        }

        // 注册用户信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        sysUser.setNickName(nickName);
        sysUser.setPhonenumber(phonenumber);
        sysUser.setIDCode(IDCode);
        sysUser.setPassword(SecurityUtils.encryptPassword(password));
        BaseResult<?> registerResult = remoteUserService.registerUserInfo(sysUser, SecurityConstants.INNER);

        if (BaseResult.FAIL == registerResult.getCode())
        {
            throw new ServiceException(registerResult.getMsg());
        }
        recordLogininfor(username, Constants.REGISTER, "注册成功");
    }

    /**
     * 记录登录信息
     * 
     * @param username 用户名
     * @param status 状态
     * @param message 消息内容
     */
    public void recordLogininfor(String username, String status, String message)
    {
        SysLogininfor logininfor = new SysLogininfor();
        logininfor.setUserName(username);
        logininfor.setIpaddr(IpUtils.getIpAddr(ServletUtils.getRequest()));
        logininfor.setMsg(message);
        logininfor.setSource("PC");
        logininfor.setAccessTime(new Date());
        BaseResult<LoginUser> userResult = remoteUserService.getUserInfo(username,SecurityConstants.INNER);
        if (ObjectUtils.isNotEmpty(userResult)){
            if (ObjectUtils.isNotEmpty(userResult.getData())) {
                LoginUser loginUser = userResult.getData();
                logininfor.setRealName(loginUser.getSysUser().getNickName());
                logininfor.setPhone(loginUser.getPhone());
            }
        }
        // 日志状态
        if (StringUtils.equalsAny(status, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER))
        {
            logininfor.setStatus("0");
        }
        else if (Constants.LOGIN_FAIL.equals(status))
        {
            logininfor.setStatus("1");
        }
//        remoteLogService.saveLogininfor(logininfor, SecurityConstants.INNER);
        remoteLogService.saveLogininfor(logininfor);
    }
}