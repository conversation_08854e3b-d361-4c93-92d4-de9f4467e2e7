package com.ruoyi.auth.utils;

import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: yuk
 * @menu
 * @Description:
 * @Date: 2022/7/13 9:35
 * @Version: 1.0.0
 */
@Slf4j
public class RSAUtil {
//    public static void main(String[] args) throws Exception {
//        String privateKeyBase64 = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAKunJa0Ld2ggOovlC43dbhnYsisbHTTtB9uaNXysBRYofJ1i/AKZffhXfaCzXDW1JXm3OfgccqAUNAskaWVQrG3P/CGgGO+2a5bPjvosw5tJrMNrXvWXejWCSI5fbgZM4GBRMHygGzVrfeRwIE5XEUX72kfDcDynr1jN1S08icOlAgMBAAECgYAAmXrA6HkLZMI5MKmCKz0e6DCMt8sGnzKEVCN0r7fsbf0r1Y34xycQyElyV4rUzmQQat3pqm74dSmbXyfirs3zXZKhXg0Wd+9dF3RchwGPtbJm1tLW0uDtsrlaH0yKlsONyGi/WrtxJJ320k4y4rw797TAsIKoZAbKL/MNFhFMXwJBANIrosWGnx1/+plfKYDSS8ZkEcNEbjpAW275SwxGuKOfS0lcHmVwfxMFFNggVt54PeFaFM1RKI6i9wCh1S/8IjcCQQDRFVf0WD+SgnOX/JztKFFZVNgZ3k5rCR2t7VoVPDsXCFPQUsmBdxH83hgZfK1lOjBSD+rdzKbZNb9BW8o3SwsDAkBmSyrMbTugxhuY7sJqmvCavTYQDox/+bcHCntGnF1d9jhUmmuI//o5ra15jDP70BZ+SrP5Qtk0o4kJW7tBwbx9AkA7R3bhcyfLDSti2x9O6hBuaGzAoNPQ6wu8JUBWJvaF6DmDJgW1rCGwdPOWPlAXFDFhAtxPOFfR27g2+qRRmvgrAkBk3yb6sBlrPHTdGFiDWG4WqkmfboajkBRuPBjnIvZPRWiMhMV9SKIVTf/7rPDEdgb3mLRsDw7Oir6F3MwnysFP";
//        String publicKeyBase64 = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCrpyWtC3doIDqL5QuN3W4Z2LIrGx007QfbmjV8rAUWKHydYvwCmX34V32gs1w1tSV5tzn4HHKgFDQLJGllUKxtz/whoBjvtmuWz476LMObSazDa171l3o1gkiOX24GTOBgUTB8oBs1a33kcCBOVxFF+9pHw3A8p69YzdUtPInDpQIDAQAB";
//
////        RSA rsa = new RSA();
////        privateKeyBase64 = rsa.getPrivateKeyBase64();
////        System.out.println(privateKeyBase64);
////        publicKeyBase64 = rsa.getPublicKeyBase64();
////        System.out.println(publicKeyBase64);
////        PrivateKey privateKey = rsa.getPrivateKey();
////        PublicKey publicKey = rsa.getPublicKey();
//
//        String data = "12345678abc%";
//        RSAUtil rsaUtil = new RSAUtil();
//
////        System.out.println(rsaUtil.encrypt(publicKeyBase64,data));
////        System.out.println(rsaUtil.decrypt(privateKeyBase64,rsaUtil.encrypt(publicKeyBase64,data)));
//        System.out.println(rsaUtil.decrypt(privateKeyBase64,"STPRGS8Ob+71oH5osmUX69PDUvUwJGjUjADNpx3uzgr1ieJhOb2n34B6B4gdAna+3NwGA9/BKq1evb0AEfsIoxI4otdn260irKWfxNN8ArPoDJYmC4uuKxwQw9ZjmJPGkEDilpYPuI6mslCSUP6O/9i49DbdgbdSZbDSz89CLGc="));
//    }


    public String encrypt(String publicKey,String obj) throws Exception {
        RSA pubRsa = new RSA(null, publicKey);
        String encodeStr = pubRsa.encryptBase64(obj, KeyType.PublicKey);
        return encodeStr;
    }

    public String decrypt(String privateKey,String obj){
//        log.info("开始解密，------密码为：：" + obj);
        // 初始化RSA工具并设置私钥
        RSA priRsa = new RSA(privateKey, null);
        // 私钥解密
        String decodeStr = priRsa.decryptStr(obj, KeyType.PrivateKey);
        log.info("解密完成，密码为：：" + decodeStr);
        return decodeStr;
    }
}