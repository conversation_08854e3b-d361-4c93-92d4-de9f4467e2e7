package com.onecity.os.common.core.configure;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.env.PropertySource;

import java.util.HashMap;
import java.util.Map;

/**
 * CustomEnvironmentPostProcessor
 *
 * <AUTHOR>
 * @since 2024/9/29 11:27
 */
public class CustomEnvironmentPostProcessor implements EnvironmentPostProcessor {


    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {

        if (EnvironmentCache.cache.size() > 0) {
            for (String name : EnvironmentCache.cache.keySet()) {
                Map<String, Object> source = EnvironmentCache.cache.get(name);
                String customName = "CACHE_DECODE_" + name;
                Map<String, Object> properties = new HashMap<>();
                for (Map.Entry<String, Object> entry : source.entrySet()) {
                    PatternMatch.decode(entry, properties);
                }
                if (properties.size() > 0) {
                    MapPropertySource property= new MapPropertySource(customName, properties);
                    environment.getPropertySources().addFirst(property);
                }
            }
        }

        for (PropertySource<?> propertySource : environment.getPropertySources()) {
            if (propertySource instanceof MapPropertySource) {
                String customName = "LOCAL_DECODE_" + propertySource.getName();
                Map<String, Object> properties = new HashMap<>();
                Map<String, Object> source = ((MapPropertySource) propertySource).getSource();
                for (Map.Entry<String, Object> entry : source.entrySet()) {
                   PatternMatch.decode(entry, properties);
                }
                if (properties.size() > 0) {
                    MapPropertySource property= new MapPropertySource(customName, properties);
                    environment.getPropertySources().addFirst(property);
                    EnvironmentCache.cache.put(propertySource.getName(), source);
                }
            }
        }

//        StringBuilder configInfo = new StringBuilder();
//        for (PropertySource<?> propertySource : environment.getPropertySources()) {
//
//            if (!propertySource.getName().contains(".yml")) {
//                continue;
//            }
//            configInfo.append("Property Source: ").append(propertySource.getName()).append("\n");
//
//            if (propertySource instanceof MapPropertySource) {
//                Map<String, Object> source = ((MapPropertySource) propertySource).getSource();
//                for (Map.Entry<String, Object> entry : source.entrySet()) {
//                    configInfo.append("  ").append(entry.getKey()).append(" = ").append(entry.getValue()).append("\n");
//                }
//            }
//        }
//        System.out.println(configInfo.toString());
    }

    public static void main(String[] args) {
//        RSACoder.encryptByPrivateKey();
//        RSACoder.decryptByPrivateKey();
    }
}
