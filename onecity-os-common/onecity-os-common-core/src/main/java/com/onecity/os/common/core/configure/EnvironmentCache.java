package com.onecity.os.common.core.configure;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * EnvironmentCache
 *
 * <AUTHOR>
 * @since 2024/9/30 11:21
 */
@Component
public class EnvironmentCache {

   public static Map<String, Map<String, Object>> cache = new HashMap<>();

}
