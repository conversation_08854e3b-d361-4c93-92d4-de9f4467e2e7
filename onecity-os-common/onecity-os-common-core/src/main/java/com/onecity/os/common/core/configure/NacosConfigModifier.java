package com.onecity.os.common.core.configure;

import org.springframework.cloud.bootstrap.config.BootstrapPropertySource;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.Ordered;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.env.PropertySource;

import java.util.AbstractMap;
import java.util.HashMap;
import java.util.Map;

/**
 * NacosConfigModifier
 *
 * <AUTHOR>
 * @since 2024/9/30 16:53
 */
public class NacosConfigModifier implements ApplicationContextInitializer<ConfigurableApplicationContext>, Ordered {
    @Override
    public void initialize(ConfigurableApplicationContext applicationContext) {
        ConfigurableEnvironment environment = applicationContext.getEnvironment();
        for (PropertySource<?> propertySource : environment.getPropertySources()) {
            if (propertySource instanceof BootstrapPropertySource) {
                String customName = "ONLINE_DECODE_" + propertySource.getName();
                Map<String, Object> properties = new HashMap<>();
                String[] propertyNames = ((BootstrapPropertySource) propertySource).getPropertyNames();
                for (String propertyName : propertyNames) {
                    Object value = ((BootstrapPropertySource) propertySource).getProperty(propertyName);
                    Map.Entry<String, Object> entry = new AbstractMap.SimpleEntry<>(propertyName, value);
                    PatternMatch.decode(entry, properties);
                }
                if (properties.size() > 0) {
                    MapPropertySource property= new MapPropertySource(customName, properties);
                    environment.getPropertySources().addFirst(property);
                }
            }
        }
    }

    @Override
    public int getOrder() {
        return 100000;
    }
}
