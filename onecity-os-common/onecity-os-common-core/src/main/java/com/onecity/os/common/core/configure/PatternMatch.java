package com.onecity.os.common.core.configure;

import com.onecity.os.common.core.utils.RSA.RSATools;
import com.onecity.os.common.core.utils.StringUtils;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * PatternMatch
 *
 * <AUTHOR>
 * @since 2024/9/30 15:35
 */
public class PatternMatch {

    private static String PATTERN_ENC_PREFIX = "^DONT\\((.*?)\\)$";

    private static Pattern r = Pattern.compile(PATTERN_ENC_PREFIX);

    public static void decode(Map.Entry<String, Object> entry, Map<String, Object> properties) {
        String key = entry.getKey();
        String value =  entry.getValue().toString();
        if (StringUtils.isBlank(value)) {
            return;
        }
        Matcher m = r.matcher(value);
        // 查找匹配项
        if (m.find()) {
            String enString = m.group(1);
            String deString = RSATools.decrypt(enString);
            properties.put(key, deString);
        }
    }
}
