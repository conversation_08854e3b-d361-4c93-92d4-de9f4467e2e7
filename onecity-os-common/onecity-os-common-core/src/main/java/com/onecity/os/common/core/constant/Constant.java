package com.onecity.os.common.core.constant;

/**
 * 系统用常量
 *
 * <AUTHOR>
 */
public interface Constant {

    // 关注直联
    String attention = "关注";
    String connection = "直联";

    /**
     * 是否删除
     */
    Integer DELETE_NO = 0;
    Integer DELETE_YES = 1;

    /**
     * 是否启用
     */
    Integer START_NO = 0;
    Integer START_YES = 1;

    /**
     * 是否展示
     */
    Integer SHOW_NO = 0;
    Integer SHOW_YES = 1;

    /**
     * 是否展示筛选框
     */
    Integer SCREEN_NO = 0;
    Integer SCREEN_YES = 1;

    /**
     * 一级指标
     */
    String PARENT_ID_ONE_LEVEL = "0";

    String SOURCE_ID = "SOURCE_ID";

    String GENERAL_INDICATOR_TALBE_SUFFIX = "_general_indicator";
    String GENERAL_INDICATOR_DATA_TALBE_SUFFIX = "_general_indicator_data";

    /**
     * 是否有指标详情
     * 1:是
     */
    Integer HAS_INDICATOR_DETAIL = 1;
    /**
     * 核心和解读指标
     */
    Integer CORE_DATA = 0;
    Integer DETAIL_DATA = 1;

    /**
     * Tab和正常指标
     */
    Integer GENERAL_INDICATOR = 0;
    Integer TAB_INDICATOR = 1;


    /**
     * 是否当前数据
     */
    Integer CURRENT_DATA_YES = 1;
    Integer CURRENT_DATA_NO = 0;

    /**
     * 是否折行显示 0：否1：是
     */
    Integer FOLD_NO = 0;
    Integer FOLD_YES = 1;

    String SUCCESS = "success";

    String SOURCE_COMPONENT = "layouts/RouteView";
    String SOURCE_URL = "/tjzt/";
    String INDICATOR_COMPONENT = "tjzt/target/index";
    String INDICATOR_URL = "/tjzt/target/index/";
    String INDICATOR_DATA_COMPONENT = "tjzt/dataTarget/index";
    String INDICATOR_DATA_URL = "/tjzt/dataTarget/index/";
    String MESSAGE_INDICATOR_COMPONENT = "tjzt/xxjb/index";
    String MESSAGE_INDICATOR_URL = "/tjzt/xxjb/index/";
    String INDICATOR_AUDIT_URL = "/tjzt/dataTarget/checking/";
    String INDICATOR_AUDIT_COMPONENT = "tjzt/dataTarget/checking";
    String INDICATOR_AUDIT_RECORD_URL = "/tjzt/tjshjl/index/";
    String INDICATOR_AUDIT_RECORD_COMPONENT = "tjzt/tjshjl/index";
    String INDICATOR_UPDATE_CYCLE_URL = "/tjzt/dataUpdate/index/";
    String INDICATOR_UPDATE_CYCLE_COMPONENT = "tjzt/dataUpdate/index";
    String COMPREHENSIVE_ANALYSIS_URL = "/comprehensiveAnalysis/index/";
    String COMPREHENSIVE_ANALYSIS_COMPONENT = "comprehensiveAnalysis/index";
    //招商引资路径
    String INVESTMENT_PROMOTION_URL = "/attractInvestment/";
    //招商引资组件
    String INVESTMENT_PROMOTION_COMPONENT = "layouts/RouteView";
    //项目管理路径
    String PROJECT_MANAGEMENT_URL = "/attractInvestment/projectManage/";
    //项目管理组件
    String PROJECT_MANAGEMENT_COMPONENT = "attractInvestment/projectManage";
    //项目进展管理路径
    String PROJECT_PROGRESS_URL = "/attractInvestment/projectProgressManage/";
    //项目进展管理组件
    String PROJECT_PROGRESS_COMPONENT = "attractInvestment/projectProgressManage";
    String INDICATOR_MENU_ICON = "area-chart";
    //菜单不缓存
    String MENU_NO_CACHE = "1";
    /**
     * 厅局编码长度
     */
    Integer SOURCE_SIMPLE_NAME_LENGTH = 10;

    String INDICATOR_MENU = "指标项管理";
    String INDICATOR_DATA_MENU = "指标数据管理";
    String MESSAGE_INDICATOR_MENU = "信息简报";
    String INDICATOR_AUDIT_MENU = "指标数据审核";
    String INDICATOR_AUDIT_RECORD_MENU = "提交审核记录";
    String INDICATOR_UPDATE_CYCLE_MENU = "指标更新周期管理";
    String INDICATOR_UPDATE_TITLE_BUTTON = "指标数据列增删";
    String PROJECT_MANAGEMENT = "项目管理";
    String PROJECT_PROGRESS = "项目进展管理";
    //招商引资板块字典编码项目类型后缀
    String PROJECT_TAGS = "_tags";
    //招商引资板块字典编码地区后缀
    String PROJECT_SITE = "_site";

    /**
     * 内容通用长度限制
     */
    Integer CONTEST_MAX = 10000;
}
