package com.onecity.os.common.core.constant;

/**
 * 服务名称
 * 
 * <AUTHOR>
 */
public class ServiceNameConstants
{
    /**
     * 认证服务的serviceid
     */
    public static final String AUTH_SERVICE = "onecity-os-auth";

    /**
     * 系统模块的serviceid
     */
    public static final String SYSTEM_SERVICE = "onecity-os-system";

    /**
     * 文件服务的serviceid
     */
    public static final String FILE_SERVICE = "onecity-os-file";

    /**
     * 工作流服务的serviceid
     */
    public static final String ACTIVITI_SERVICE = "onecity-os-activiti";


    /**
     * 指标计算服务的serviceid
     */
    public static final String CALCULATE_SERVICE = "onecity-os-calculate";

    /**
     * 批示服务的serviceid
     */
    public static final String DISPOSAL_SERVICE = "onecity-os-disposal";

    /**
     * 公文服务的serviceid
     */
    public static final String DOCUMENT_SERVICE = "onecity-os-document";

    /**
     * 定时任务服务的serviceid
     */
    public static final String JOB_SERVICE = "onecity-os-job";

    /**
     * 消息服务的serviceid
     */
    public static final String MESSAGE_SERVICE = "onecity-os-message";

    /**
     * mq服务的serviceid
     */
    public static final String MQ_SERVICE = "onecity-os-mq";

    /**
     * 通知服务的serviceid
     */
    public static final String NOTICE_SERVICE = "onecity-os-notice";

    /**
     * 大屏设计器服务的serviceid
     */
    public static final String ONESCREEN_SERVICE = "onecity-os-onescreen";

    /**
     * 问卷调查服务的serviceid
     */
    public static final String QUESTIONAIRESURVEY_SERVICE = "onecity-os-questionairesurvey";

    /**
     * 督办服务的serviceid
     */
    public static final String SUPERVISE_SERVICE = "onecity-os-supervise";

    /**
     * 填报服务的serviceid
     */
    public static final String SUPERVISE_MODULE_SYSTEM = "onecity-os-management";


    /**
     * gateway通过header传递根路径 basePath
     */
    public static final String X_GATEWAY_BASE_PATH = "X_GATEWAY_BASE_PATH";
}
