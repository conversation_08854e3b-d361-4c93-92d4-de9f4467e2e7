package com.onecity.os.common.core.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.onecity.os.common.core.constant.Constants;
import com.onecity.os.common.core.utils.IdGen;
import com.onecity.os.common.core.utils.ServletUtils;
import com.onecity.os.common.core.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Transient;
import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Entity基类
 *
 * <AUTHOR>
 */
@ApiModel(value = "基础实体", description = "其它对象集成此基础实体")
@Data
public class BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 实体编号（唯一标识）
     */
    @ApiModelProperty(value = "唯一标识")
    @Id
    protected String id;

    /**
     * 搜索值
     */
    @ApiModelProperty(value = "搜索值")
    @Transient
    private String searchValue;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 创建者
     */
    @ApiModelProperty(hidden = true)
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    @ApiModelProperty(hidden = true)
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标识 0 正常 1 删除
     */
    @ApiModelProperty(value = "删除标识:'0'正常,'1'删除")
    private String delFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 开始时间
     */
    @ApiModelProperty(hidden = true)
    @Transient
    private String beginTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(hidden = true)
    @Transient
    private String endTime;

    /**
     * 请求参数
     */
    @ApiModelProperty(value = "请求参数")
    @Transient
    private Map<String, Object> params = new HashMap<>();

    /**
     * 是否是新记录（默认：false），调用setIsNewRecord()设置新记录，使用自定义ID。
     * 设置为true后强制执行插入语句，ID不会自动生成，需从手动传入。
     */
    @ApiModelProperty(hidden = true)
    protected boolean isNewRecord = false;

    /**
     * 是否是新记录（默认：false），调用setIsNewRecord()设置新记录，使用自定义ID。
     * 设置为true后强制执行插入语句，ID不会自动生成，需从手动传入。
     *
     * @return
     */
    public boolean getIsNewRecord() {
        return isNewRecord || StringUtils.isBlank(getId());
    }

    /**
     * 是否是新记录（默认：false），调用setIsNewRecord()设置新记录，使用自定义ID。
     * 设置为true后强制执行插入语句，ID不会自动生成，需从手动传入。
     */
    public void setIsNewRecord(boolean isNewRecord) {
        this.isNewRecord = isNewRecord;
    }

    /**
     * 插入之前执行方法，需要手动调用
     */
    public void preInsert() {
        // 不限制ID为UUID，调用setIsNewRecord()使用自定义ID
//        if (!this.isNewRecord) {
        setId(IdGen.uuid());
//        }
//        User user = UserUtils.getUser();
//        if (StringUtils.isNotBlank(user.getId())) {
//            this.updateBy = user;
//            this.createBy = user;
//        }
        this.updateBy = this.createBy;
        this.updateTime = new Date();
        this.createTime = this.updateTime;
        // 获取当前的用户
        HttpServletRequest request = ServletUtils.getRequest();
        this.tenantId = request.getHeader(Constants.CURRENT_TENANT);
    }

    /**
     * 更新之前执行方法，需要手动调用
     */
    public void preUpdate() {
//        User user = UserUtils.getUser();
//        if (StringUtils.isNotBlank(user.getId())) {
//            this.updateBy = user;
//        }
        this.updateTime = new Date();
//        数据更新操作不涉及租户更新
//        HttpServletRequest request = ServletUtils.getRequest();
//        this.tenantId = request.getHeader("tenant");
    }


    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSearchValue() {
        return searchValue;
    }

    public void setSearchValue(String searchValue) {
        this.searchValue = searchValue;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    public boolean isNewRecord() {
        return isNewRecord;
    }

    public void setNewRecord(boolean newRecord) {
        isNewRecord = newRecord;
    }
}
