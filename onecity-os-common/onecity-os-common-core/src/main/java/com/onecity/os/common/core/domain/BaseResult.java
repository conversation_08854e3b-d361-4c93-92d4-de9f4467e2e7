package com.onecity.os.common.core.domain;

import java.io.Serializable;
import java.lang.reflect.Type;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.onecity.os.common.core.constant.Constants;
import com.onecity.os.common.core.utils.XssUtil;

/**
 * 响应信息主体
 *
 * <AUTHOR>
 */
public class BaseResult<T> implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 成功 */
    public static final int SUCCESS = Constants.SUCCESS;

    /** 失败 */
    public static final int FAIL = Constants.FAIL;

    private int code;

    private String msg;

    private T data;

    @JsonIgnore
    private String onlTable;

    public static <T> BaseResult<T> ok()
    {
        return restResult(null, SUCCESS, null);
    }

    public static <T> BaseResult<T> ok(T data)
    {
        return restResult(data, SUCCESS, null);
    }

    public static <T> BaseResult<T> ok(T data, boolean bRich) {
        return restResult(data, SUCCESS, null, true);
    }

    public static <T> BaseResult<T> ok(T data, String msg)
    {
        return restResult(data, SUCCESS, msg);
    }

    public static <T> BaseResult<T> fail()
    {
        return restResult(null, FAIL, null);
    }

    public static <T> BaseResult<T> fail(String msg)
    {
        return restResult(null, FAIL, msg);
    }

    public static <T> BaseResult<T> fail(T data)
    {
        return restResult(data, FAIL, null);
    }

    public static <T> BaseResult<T> fail(T data, String msg)
    {
        return restResult(data, FAIL, msg);
    }

    public static <T> BaseResult<T> fail(int code, String msg)
    {
        return restResult(null, code, msg);
    }

    private static <T> BaseResult<T> restResult(T data, int code, String msg)
    {
        return restResult(data, code, msg, false);
    }

    private static <T> BaseResult<T> restResult(T data, int code, String msg, boolean bRich)
    {
        BaseResult<T> apiResult = new BaseResult<>();
        apiResult.setCode(code);
        apiResult.setData(data);
        apiResult.setMsg(msg);

        if (bRich) {
            return XssUtil.filterXssRichTextResult(apiResult);
        } else {
            return XssUtil.filterXssResult(apiResult);
        }
    }

    public int getCode()
    {
        return code;
    }

    public void setCode(int code)
    {
        this.code = code;
    }

    public String getMsg()
    {
        return msg;
    }

    public void setMsg(String msg)
    {
        this.msg = msg;
    }

    public T getData()
    {
        return data;
    }

    public void setData(T data)
    {
        this.data = data;
    }

    public String getOnlTable() {
        return onlTable;
    }

    public void setOnlTable(String onlTable) {
        this.onlTable = onlTable;
    }
}
