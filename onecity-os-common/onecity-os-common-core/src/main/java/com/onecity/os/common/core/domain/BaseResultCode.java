package com.onecity.os.common.core.domain;

/**
 * 接口请求返回状态码表
 */
public enum BaseResultCode {

    COMMON_SUCCESS(0,"操作成功。"),
    COMMON_FAILURE(10001,"操作失败。"),
    COMMON_PARAMETER_ERROR(10002,"请求参数错误。"),
    COMMON_NAME_EXISTED(10003,"名称已存在。"),
    COMMON_NOT_FOUND(10004,"未找到。"),
    COMMON_PERMISSION_DENIED(10005,"没有权限"),


    COMMON_FILE_READ_ERROR(10006,"文件读取错误"),
    COMMON_FILE_TYPE_ERROR(10007,"文件类型错误"),
    COMMON_FILE_SIZE_ERROR(10008,"文件大小错误");

    private int code;
    private String msg;

    private BaseResultCode(int code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public int getCode(){
        return this.code;
    }
    public String getMsg(){
        return this.msg;
    }


}
