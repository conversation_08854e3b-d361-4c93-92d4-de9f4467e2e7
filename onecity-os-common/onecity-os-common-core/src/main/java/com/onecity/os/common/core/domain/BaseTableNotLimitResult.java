package com.onecity.os.common.core.domain;

import com.onecity.os.common.core.constant.HttpStatus;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 前端tables框架返回模板
 *
 * <AUTHOR>
 */
public class BaseTableNotLimitResult<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "状态码",example = "0")
    private int code;
    @ApiModelProperty(value = "返回消息",example = "操作成功")
    private String msg;
    @ApiModelProperty(value = "返回数据")
    private List<T> rows;
    @ApiModelProperty(value = "数据总数",example="1")
    private Integer total;


    public BaseTableNotLimitResult() {
        rows = new ArrayList<>();
    }

    public BaseTableNotLimitResult success() {
        this.code = HttpStatus.SUCCESS;
        this.msg = "操作成功";
        return this;
    }

    /**
     * 不分页Tables数据格式
     * @param rows
     * @return
     */
    public BaseTableNotLimitResult success(List<T> rows) {
        this.code = HttpStatus.SUCCESS;
        this.rows = rows;
        this.msg = "操作成功";
        this.total = rows.size();
        return this;
    }


    /**
     * 不分页Tables数据格式
     * @param rows
     * @param msg
     * @return
     */
    public BaseTableNotLimitResult success(List<T> rows, String msg) {
        this.code = HttpStatus.SUCCESS;
        this.rows = rows;
        this.msg = msg;
        this.total = rows.size();
        return this;
    }

    public BaseTableNotLimitResult error(String msg) {
        this.code = HttpStatus.ERROR;
        this.msg = msg;
        return this;
    }

    public BaseTableNotLimitResult error() {
        this.code = HttpStatus.ERROR;
        this.msg = "系统内部错误";
        return this;
    }

    public BaseTableNotLimitResult error(Integer code, String message) {
        this.code=code;
        this.msg = message;
        return this;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public List<T> getRows() {
        return rows;
    }

    public void setRows(List<T> rows) {
        this.rows = rows;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }
}
