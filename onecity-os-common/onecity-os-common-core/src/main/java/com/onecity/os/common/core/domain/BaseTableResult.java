package com.onecity.os.common.core.domain;

import com.onecity.os.common.core.constant.HttpStatus;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 前端tables框架返回模板
 *
 * <AUTHOR>
 */
public class BaseTableResult<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "状态码",example = "0")
    private int code;
    @ApiModelProperty(value = "返回消息",example = "操作成功")
    private String msg;
    @ApiModelProperty(value = "返回数据")
    private List<T> rows;
    @ApiModelProperty(value = "数据总数",example="1")
    private Integer total;
    @ApiModelProperty(value = "页数",example="1")
    private int pageNum;
    @ApiModelProperty(value = "长度",example="20")
    private int pageSize;


    public BaseTableResult() {
        rows = new ArrayList<>();
    }

    /**
     * 分页参数返回
     * @param pageNum
     * @param pageSize
     */
    public BaseTableResult(Integer pageNum, Integer pageSize) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }

    public BaseTableResult(R r) {
        setRows((List<T>) r.get("rows"));
        setPageNum((Integer) r.get("pageNum"));
        setTotal(Integer.valueOf(r.get("total").toString()));
    }

    public BaseTableResult success() {
        this.code = HttpStatus.SUCCESS;
        this.msg = "操作成功";
        return this;
    }

    /**
     * 不分页Tables数据格式
     * @param rows
     * @return
     */
    public BaseTableResult success(List<T> rows) {
        this.code = HttpStatus.SUCCESS;
        this.rows = rows;
        this.msg = "操作成功";
        if (CollectionUtils.isNotEmpty(rows)) {
            this.total = rows.size();
        } else {
            this.total = 0;
        }

        return this;
    }


    /**
     * 不分页Tables数据格式
     * @param rows
     * @param msg
     * @return
     */
    public BaseTableResult success(List<T> rows, String msg) {
        this.code = HttpStatus.SUCCESS;
        this.rows = rows;
        this.msg = msg;
        this.total = rows.size();
        return this;
    }

    public BaseTableResult error(String msg) {
        this.code = HttpStatus.ERROR;
        this.msg = msg;
        return this;
    }

    public BaseTableResult error() {
        this.code = HttpStatus.ERROR;
        this.msg = "系统内部错误";
        return this;
    }

    public BaseTableResult error(Integer code, String message) {
        this.code=code;
        this.msg = message;
        return this;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public List<T> getRows() {
        return rows;
    }

    public void setRows(List<T> rows) {
        this.rows = rows;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}
