package com.onecity.os.common.core.utils;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Sets;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * JsEvalCheckUtil
 *
 * <AUTHOR>
 * @since 2024/5/30 9:52
 */
public class JsEvalCheckUtil {
    private static final Set<String> blacklist = Sets.newHashSet(
            // Java 全限定类名
            "java.io.File", "java.io.RandomAccessFile", "java.io.FileInputStream", "java.io.FileOutputStream",
            "java.lang.Class", "java.lang.ClassLoader", "java.lang.Runtime", "java.lang.System", "System.getProperty",
            "java.lang.Thread", "java.lang.ThreadGroup", "java.lang.reflect.AccessibleObject", "java.net.InetAddress",
            "java.net.DatagramSocket", "java.net.DatagramSocket", "java.net.Socket", "java.net.ServerSocket",
            "java.net.MulticastSocket", "java.net.MulticastSocket", "java.net.URL", "java.net.HttpURLConnection",
            "java.security.AccessControlContext", "java.lang.ProcessBuilder",
            //反射关键字
            "invoke","newinstance",
            // JavaScript 方法
            "eval", "new function",
            //引擎特性
            "Java.type","importPackage","importClass","JavaImporter"
    );

    /**
     * 检查jscode是否包含危险命令
     * @param code
     * @return true is safe
     */
    public static boolean isSafe(String code) {
        // 去除注释
        String removeComment = StringUtils.replacePattern(code, "(?:/\\*(?:[^*]|(?:\\*+[^*/]))*\\*+/)|(?://.*[\n\r\u2029\u2028])", " ");
        //去除特殊字符
        removeComment =StringUtils.replacePattern(removeComment,"[\u2028\u2029\u00a0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\ufeff]","");
        // 去除空格
        String removeWhitespace = StringUtils.replacePattern(removeComment, "\\s+", "");
        // 多个空格替换为一个
        String oneWhiteSpace = StringUtils.replacePattern(removeComment, "\\s+", " ");

        Set<String> insecure = blacklist.stream().filter(s -> StringUtils.containsIgnoreCase(removeWhitespace, s) ||
                StringUtils.containsIgnoreCase(oneWhiteSpace, s)).collect(Collectors.toSet());

        if (!CollectionUtils.isEmpty(insecure)) {
            return false;
        }
        return true;
    }
}
