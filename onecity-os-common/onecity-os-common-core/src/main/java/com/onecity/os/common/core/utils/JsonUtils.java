package com.onecity.os.common.core.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.onecity.os.common.core.exception.ServiceException;

public class JsonUtils {

    // 创建一个 ObjectMapper 实例，用于转换对象
    private static final ObjectMapper objectMapper = new ObjectMapper();

    // 将对象转换为 JSON 字符串
    public static String toJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);  // 使用 ObjectMapper 将对象转成 JSON
        } catch (JsonProcessingException e) {
            e.printStackTrace();  // 打印异常
            throw new ServiceException("对象转换成JSON字符串失败！");
        }
    }

    // 将 JSON 字符串转换为对象
    public static <T> T fromJson(String jsonString, Class<T> valueType) {
        try {
            return objectMapper.readValue(jsonString, valueType);  // 使用 ObjectMapper 将 JSON 转回对象
        } catch (JsonProcessingException e) {
            e.printStackTrace();  // 打印异常
            throw new ServiceException("JSON字符串转换成指定对象失败！");
        }
    }
}