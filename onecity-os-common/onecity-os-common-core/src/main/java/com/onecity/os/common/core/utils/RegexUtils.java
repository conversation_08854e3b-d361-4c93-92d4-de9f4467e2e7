package com.onecity.os.common.core.utils;


import org.apache.commons.lang3.StringUtils;


/**
 * 手机号、邮箱、密码、用户名的正则表达式验证
 *
 * <AUTHOR>
 */
public class RegexUtils {

    public static final String PHONE_REGEX = "1[3-9]\\d{9}";
    /**
     * 邮箱正则
     * {@code @See} <a href="https://www.sojson.com/regex/check.html">...</a>
     */
    public static final String EMAIL_REGEX = "\\w[-\\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\\.)+[A-Za-z]{2,14}";

    /**
     * 密码正则
     * 密码规则：8-16位字母及数字组合
     */
    public static final String PASSWORD_REGEX = "^(?=.*\\d)(?=.*[a-zA-Z])(?=.*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,<\\.>\\/?])[A-Za-z\\d!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,<\\.>\\/?]{8,16}$";

    /**
     * 用户名正则
     */
    public static final String USERNAME_REGEX = "^[a-zA-Z0-9]{2,20}$";

    /**
     * 是否符合手机格式
     *
     * @param phone 要校验的手机号
     * @return true:符合，false：不符合
     */
    public static boolean isPhone(String phone) {
        return matches(phone, PHONE_REGEX);
    }

    /**
     * 是否符合邮箱格式
     *
     * @param email 要校验的邮箱
     * @return true:符合，false：不符合
     */
    public static boolean isEmail(String email) {
        return matches(email, EMAIL_REGEX);
    }

    /**
     * 是否符合密码强度
     *
     * @param password 要校验的密码
     * @return true:符合，false：不符合
     */
    public static boolean checkPassword(String password) {
        return matches(password, PASSWORD_REGEX);
    }

    /**
     * 是否符合用户名规则
     *
     * @param userName 要校验的userName
     * @return true:符合，false：不符合
     */
    public static boolean checkUserName(String userName) {
        return matches(userName, USERNAME_REGEX);
    }

    private static boolean matches(String str, String regex) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        return str.matches(regex);
    }
}
