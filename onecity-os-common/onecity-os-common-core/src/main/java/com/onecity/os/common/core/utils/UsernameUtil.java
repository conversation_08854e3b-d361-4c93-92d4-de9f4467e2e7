package com.onecity.os.common.core.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 用户名验证工具类
 * <AUTHOR>
 */
@Slf4j
public class UsernameUtil {

    /**
     * 由字母数字下划线组成且开头必须是字母，不能超过16位
     */
    private static final Pattern pUsername = Pattern.compile("[a-zA-Z]{1}[a-zA-Z0-9_]{1,15}");

    /**
     * 手机号
     */
    private static final Pattern pMobile = Pattern.compile("^1[3|4|5|8][0-9]\\d{8}$");

    /**
     * 邮箱
     */
    private static final Pattern pEmail = Pattern.compile("^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.[a-zA-Z0-9]{2,6}$");
    /**
    * 数字
    */
    private static final Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
    public static boolean Username(String v){

        Matcher m = pUsername.matcher(v);
        if(m.matches()){
            return true;
        }
        return false;
    }

    public static boolean Mobile(String v){

        Matcher m = pMobile.matcher(v);
        if(m.matches()){
            return true;
        }
        return false;
    }

    public static boolean Email(String v){

        Matcher m = pEmail.matcher(v);
        if(m.matches()){
            return true;
        }
        return false;
    }

    /**
     *
     * 功能描述: 去掉括号里面的内容
     *
     * @param: [context]
     * @return: java.lang.String
     * @date: 2018/7/12 0012 11:18
     */
    public static String ClearBracket(String context) {
        // 修改原来的逻辑，防止右括号出现在左括号前面的位置
        int head = context.indexOf('('); // 标记第一个使用左括号的位置
        if (head == -1) {
            return context; // 如果context中不存在括号，什么也不做，直接跑到函数底端返回初值str
        } else {
            int next = head + 1; // 从head+1起检查每个字符
            int count = 1; // 记录括号情况
            do {

                if (context.charAt(next) == '(') {
                    count++;
                } else if (context.charAt(next) == ')') {
                    count--;
                }
                next++; // 更新即将读取的下一个字符的位置
                if (count == 0) { // 已经找到匹配的括号
                    String temp = context.substring(head, next);
                    String temp1 = context.substring(head+1, next-1);
                    if (pattern.matcher(temp1).matches()){
                        context = context.replace(temp, ""); // 用空内容替换，复制给context
                        head = context.indexOf('(',context.indexOf('(')+1); // 找寻下一个左括号
                        next = head + 1; // 标记下一个左括号后的字符位置
                        count = 1; // count的值还原成1
                    }else {
                        // 如果context的括号内容不为纯数字，检查是否有下一个括号
                        head = context.indexOf('(',context.indexOf('(')+1); // 找寻下一个左括号
                        next = head + 1; // 标记下一个左括号后的字符位置
                        count = 1; // count的值还原成1
                    }
                }
            } while (head != -1); // 如果在该段落中找不到左括号了，就终止循环
        }
        return context; // 返回更新后的context
    }
}
