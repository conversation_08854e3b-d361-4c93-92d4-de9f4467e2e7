package com.onecity.os.common.core.web.controller;

import java.beans.PropertyEditorSupport;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.onecity.os.common.core.constant.Constants;
import com.onecity.os.common.core.constant.HttpStatus;
import com.onecity.os.common.core.domain.R;
import com.onecity.os.common.core.utils.DateUtils;
import com.onecity.os.common.core.utils.ServletUtils;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.common.core.utils.XssUtil;
import com.onecity.os.common.core.utils.sql.SqlUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.core.web.page.PageDomain;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.core.web.page.TableSupport;

/**
 * web层通用数据处理
 *
 * <AUTHOR>
 */
public class BaseController
{
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder)
    {
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport()
        {
            @Override
            public void setAsText(String text)
            {
                setValue(DateUtils.parseDate(text));
            }
        });
    }

    /**
     * 设置请求分页数据
     */
    protected void startPage()
    {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize))
        {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            // pageDomain.setReasonable(false);
            Boolean reasonable = pageDomain.getReasonable();
            PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);
        }
    }

    /**
     * 设置请求分页数据
     */
    protected void startPage(Integer pageNum,Integer pageSize)
    {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        pageNum = pageDomain.getPageNum()==null?pageNum:pageDomain.getPageNum();
        pageSize = pageDomain.getPageSize()==null?pageSize:pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize))
        {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            Boolean reasonable = pageDomain.getReasonable();
            PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);
        }
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    protected TableDataInfo getDataTable(List<?> list)
    {
        return getDataTable(list, false);
    }

    /**
     * 响应请求分页数据
     *
     * @param bRich true 表示富文本过滤方式
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    protected TableDataInfo getDataTable(List<?> list, boolean bRich)
    {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(list);
        rspData.setMsg("查询成功");
        rspData.setTotal(new PageInfo(list).getTotal());
        if (bRich) {
            return XssUtil.filterXssRichTextResult(rspData);
        } else {
            return XssUtil.filterXssResult(rspData);
        }
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows)
    {
        return rows > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    protected AjaxResult toAjax(boolean result)
    {
        return result ? success() : error();
    }

    /**
     * 返回成功
     */
    public AjaxResult success()
    {
        return AjaxResult.success();
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error()
    {
        return AjaxResult.error();
    }

    /**
     * 返回成功消息
     */
    public AjaxResult success(String message)
    {
        return AjaxResult.success(message);
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error(String message)
    {
        return AjaxResult.error(message);
    }

    /*
     * 获取当前数据请求的登录账号
     */
    public String getLoginName() {
        return ServletUtils.getRequest().getHeader(Constants.CURRENT_USERNAME);
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    protected R result(List<?> list) {
        PageInfo<?> pageInfo = new PageInfo(list);
        Map<String, Object> m = new HashMap<String, Object>();
        m.put("rows", list);
        m.put("pageNum", pageInfo.getPageNum());
        m.put("total", pageInfo.getTotal());
        return R.ok(m);
    }

    protected R result(List<?> list, Integer pageNum, Integer pageSize, Integer total) {
        PageInfo<?> pageInfo = new PageInfo(list);
        Map<String, Object> m = new HashMap();
        m.put("rows", list);
        m.put("pageNum", pageNum);
        m.put("pageSize", pageSize);
        m.put("total", total);
        return R.ok(m);
    }
}
