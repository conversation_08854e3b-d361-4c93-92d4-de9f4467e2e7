<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.onecity.os</groupId>
        <artifactId>onecity-os-common</artifactId>
        <version>3.2.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    
    <artifactId>onecity-os-common-log</artifactId>
	
    <description>
        onecity-os-common-log日志记录
    </description>

    <dependencies>
        <!-- RuoYi Common Security -->
        <dependency>
            <groupId>com.onecity.os</groupId>
            <artifactId>onecity-os-common-security</artifactId>
        </dependency>
        <!--RabbitMQ-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
    </dependencies>
</project>