package com.onecity.os.common.constant;

/**
 * <AUTHOR>
 * @Date 2022-07-18 15:55
 */
public interface MqConstant {
    /**
     * 普通交换器
     */
   String DIRECT_EXCHANGE = "onecity.direct.exchange";
    /**
     * 主题交换器
     */
    String TOPIC_EXCHANGE = "onecity.topic.exchange";
    /**
     * 广播交换器
     */
    String FANOUT_EXCHANGE = "onecity.fanout.exchange";

    /**
     * 默认队列名称
     */
    String DEFAULT_QUEUE_NAME = "onecity.topic.queue";
    /**
     * 默认路由键
     */
    String DEFAULT_ROUTING_KEY = "onecity.routingkey";


    /**
     * Direct exchange.
     */
    String DIRECT = "direct";

    /**
     * Topic exchange.
     */
    String TOPIC = "topic";

    /**
     * Fanout exchange.
     */
    String FANOUT = "fanout";
}
