<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.onecity.os</groupId>
        <artifactId>onecity-os-common</artifactId>
        <version>3.2.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    
    <artifactId>onecity-os-common-redis</artifactId>
	
    <description>
        onecity-os-common-redis缓存服务
    </description>

    <dependencies>
		
        <!-- SpringBoot Boot Redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <!-- RuoYi Common Core-->
        <dependency>
            <groupId>com.onecity.os</groupId>
            <artifactId>onecity-os-common-core</artifactId>
        </dependency>

    </dependencies>
</project>