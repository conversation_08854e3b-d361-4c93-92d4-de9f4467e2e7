package com.ruoyi.gateway.filter;

import com.onecity.os.common.core.constant.HttpStatus;
import com.onecity.os.common.core.utils.ServletUtils;
import com.onecity.os.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.gateway.config.properties.CheckSessionProperties;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Base64;


public class ApiFileFilter implements GlobalFilter, Ordered {
    private static final Logger log = LoggerFactory.getLogger(ApiFileFilter.class);

    @Autowired
    private CheckSessionProperties checkSessionProperties;
    @Autowired
    private RedisService redisService;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpRequest.Builder mutate = request.mutate();
        String url = request.getURI().getPath();

        if(StringUtils.matches(url,checkSessionProperties.getSessionPaths())){
            // 获取 Session
            String sessionId = exchange.getSession().block(Duration.ofMillis(1)).getId();
            boolean islogin = redisService.hasKey(sessionId);
            if(!islogin){
                return unauthorizedResponse(exchange,"资源未找到");
            }
        }

        return chain.filter(exchange);
    }
    private Mono<Void> unauthorizedResponse(ServerWebExchange exchange, String msg)
    {
        log.error("[鉴权异常处理]请求路径:{}", exchange.getRequest().getPath());
        return ServletUtils.webFluxResponseWriter(exchange.getResponse(), msg, HttpStatus.NOT_FOUND);
    }
    @Override
    public int getOrder() {
        return 0;
    }
}
