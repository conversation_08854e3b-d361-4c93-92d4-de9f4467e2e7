package com.ruoyi.gateway.filter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.constant.CacheConstants;
import com.onecity.os.common.core.constant.HttpStatus;
import com.onecity.os.common.core.constant.SecurityConstants;
import com.onecity.os.common.core.constant.TokenConstants;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.JwtUtils;
import com.onecity.os.common.core.utils.ServletUtils;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.gateway.FeignClient.ClientHolder;
import com.ruoyi.gateway.config.properties.IgnoreWhiteProperties;
import io.jsonwebtoken.Claims;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.Locale;
import java.util.concurrent.Future;

/**
 * 网关鉴权
 * 
 * <AUTHOR>
 */
@Component
public class AuthFilter implements GlobalFilter, Ordered
{
    private static final Logger log = LoggerFactory.getLogger(AuthFilter.class);

    @Value("${sm2.request.privateKey:819a90e45f19bbd8b59594bc2057224e3083e7d87a61f6603da1707a6d6cf8df}")
    String privateKey;
    @Value("${sm2.request.publicKey:04d12d994d6d298d3c37a7b685d072a720afcc91c2df985cb09e04063bf3b82a12ce279aa209f1b0faa71c3acd4c21524eef403d39b3f53aaa109e0f7baeef0e3b}")
    String publicKey;
    /**
     * 前端请求时间误差值，单位毫秒，默认前后共5分钟 即值为 2.5min
     */
    private static final long HALF_FIVE_MINUTES_IN_MILLIS = 5 * 30 * 1000;

    // 排除过滤的 uri 地址，nacos自行添加
    @Autowired
    private IgnoreWhiteProperties ignoreWhite;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ClientHolder clientHolder;

    /**
     * 图片文件(普通文件,图片,)后缀 支持的类型
     */
    private static final String[] FILE_SUFFIX_SUPPORT = {".jpg", ".jpeg", ".png"};


    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain)
    {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpRequest.Builder mutate = request.mutate();
        String url = request.getURI().getPath();
        // 跳过不需要验证的路径
        if (StringUtils.matches(url, ignoreWhite.getWhites()))
        {
            return chain.filter(exchange);
        }
        // 跳过app 端接口
        String source = request.getHeaders().getFirst(SecurityConstants.REQUEST_SOURCE);
        if(SecurityConstants.APP.equals(source)){
            return chain.filter(exchange);
        }
        //跳过图片相关请求
        int index = url.lastIndexOf('.');
        if(index > 0){
            String suffix = url.substring(index);
            if(Arrays.asList(FILE_SUFFIX_SUPPORT).contains(suffix.toLowerCase(Locale.ROOT))){
                return chain.filter(exchange);
            }
        }
        // 获取客户端随机串
        String decryptToken = getDecryptToken(request);
        String nonce = getNonce(request);
        if(StringUtils.isBlank(nonce)){
            log.error("AuthFilter.filter encrypToken:{} 过滤获取nonce为空:{}", decryptToken, nonce);
            return unauthorizedResponse(exchange, "令牌不能为空");
        }
        // 判断客户端随机串是否使用过
        if (redisService.hasKey(nonce)) {
            long expire = redisService.getExpire(nonce);
            log.error("AuthFilter.filter 过滤获取nonce已被使用:{},有效时间:{}",JSONObject.toJSONString(nonce), JSONObject.toJSONString(expire));

            return unauthorizedResponse(exchange, "登录状态已过期");
        }
        // 使用分隔符分割字符串
        String[] parts = decryptToken.split(nonce);
        if (parts.length != 2) {
            // 如果分割后的部分不是两个，说明格式不正确
            return unauthorizedResponse(exchange, "令牌不能为空");
        }
        // 检查时间戳是否在当前时间前后共5分钟之内 即允许共 5min 时间偏差
        if (!checkTimestamp(parts[0])) {
            // 时间戳在5分钟之内，允许执行下一步
            log.error("AuthFilter.filter 过滤获取nonce:{},请求时间已超过5分钟误差范围:{}", JSONObject.toJSONString(nonce), JSONObject.toJSONString(parts[0]));
            return unauthorizedResponse(exchange, "令牌已过期！");
        }
        String token = parts[1];
        if (StringUtils.isEmpty(token)) {
            log.error("AuthFilter.filter 过滤获取nonce:{},token为空:{}",JSONObject.toJSONString(nonce), JSONObject.toJSONString(token));
            return unauthorizedResponse(exchange, "令牌不能为空");
        }
        Claims claims = JwtUtils.parseToken(token);
        if (claims == null) {
            return unauthorizedResponse(exchange, "令牌已过期或验证不正确！");
        }
        String userkey = JwtUtils.getUserKey(claims);
        boolean islogin = redisService.hasKey(getTokenKey(userkey));
        if (!islogin) {
            return unauthorizedResponse(exchange, "登录状态已过期");
        }
        String userid = JwtUtils.getUserId(claims);
        String username = JwtUtils.getUserName(claims);
        if (StringUtils.isEmpty(userid) || StringUtils.isEmpty(username))
        {
            return unauthorizedResponse(exchange, "令牌验证失败");
        }else {
            //验证用户是否被冻结
            // 使用Client
            Future<BaseResult<LoginUser>> stringFuture = clientHolder.systemClientDo(username,SecurityConstants.INNER);
            try {
                log.info("stringFuture.rs :{}", stringFuture.get());
                BaseResult<LoginUser> userResult = stringFuture.get();
                LoginUser userInfo = userResult.getData();
                if ("1".equals(userInfo.getSysUser().getStatus())){
                    return unauthorizedResponse(exchange,"该用户已被冻结");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        // 存入 Nonce 使用过标识到 redis 用于判断 非重复登录
        redisService.setCacheObject(nonce, 1);
        // 设置用户信息到请求
        addHeader(mutate, SecurityConstants.USER_KEY, userkey);
        addHeader(mutate, SecurityConstants.DETAILS_USER_ID, userid);
        addHeader(mutate, SecurityConstants.DETAILS_USERNAME, username);
        // 内部请求来源参数清除
        removeHeader(mutate, SecurityConstants.FROM_SOURCE);
        return chain.filter(exchange.mutate().request(mutate.build()).build());
    }

    private void addHeader(ServerHttpRequest.Builder mutate, String name, Object value)
    {
        if (value == null)
        {
            return;
        }
        String valueStr = value.toString();
        String valueEncode = ServletUtils.urlEncode(valueStr);
        mutate.header(name, valueEncode);
    }

    private void removeHeader(ServerHttpRequest.Builder mutate, String name)
    {
        mutate.headers(httpHeaders -> httpHeaders.remove(name)).build();
    }

    private Mono<Void> unauthorizedResponse(ServerWebExchange exchange, String msg)
    {
        log.error("[鉴权异常处理]请求路径:{}", exchange.getRequest().getPath());
        return ServletUtils.webFluxResponseWriter(exchange.getResponse(), msg, HttpStatus.UNAUTHORIZED);
    }

    /**
     * 获取缓存key
     */
    private String getTokenKey(String token)
    {
        return CacheConstants.LOGIN_TOKEN_KEY + token;
    }

    /**
     * 获取解密请求token
     */
    private String getDecryptToken(ServerHttpRequest request) {
        String token = request.getHeaders().getFirst(TokenConstants.AUTHENTICATION);
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.PREFIX)) {
            token = token.replaceFirst(TokenConstants.PREFIX, StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(token)) {
            return StringUtils.EMPTY;
        }
        // 解密密文 获取组合token  时间戳+nonce+token
        SM2 sm2 = SmUtil.sm2(privateKey, publicKey);
        String decryptToken = null;
        try {
            decryptToken = StrUtil.utf8Str(sm2.decrypt(token, KeyType.PrivateKey));
        } catch (Exception e) {
            log.error("AuthFilter.getDecryptToken 获取decryptToken:{},异常", decryptToken, e);
            return StringUtils.EMPTY;
        }
        return StringUtils.isNotBlank(decryptToken) ? decryptToken : StringUtils.EMPTY;
    }


    /**
     * 获取请求nonce
     */
    private String getNonce(ServerHttpRequest request) {
        return request.getHeaders().getFirst(TokenConstants.NONCE);
    }

    /**
     * 检查给定的时间戳是否在当前时间前后共5分钟之内
     * 即允许前后各 2.5min 偏差
     * @param time 时间戳，以字符串形式表示，单位为毫秒
     * @return 如果时间戳在当前时间的前后共5分钟之内，则返回true；否则返回false
     *         如果输入的时间戳格式不正确或为null，则也返回false
     */
    private boolean checkTimestamp(String time) {
        try {
            long now = System.currentTimeMillis();
            // 转换时间戳示例 1729498372106
            long timestamp = Long.parseLong(time);
            // 检查时间戳是否在当前时间前后5分钟内
            if (Math.abs(now - timestamp) < HALF_FIVE_MINUTES_IN_MILLIS) {
                // 时间戳在当前时间前后5分钟内，返回 true
                return true;
            }
        } catch (NumberFormatException | NullPointerException e) {
            // 如果时间戳格式不正确或为null，则视为不满足条件
            log.error("AuthFilter.checkTimestamp 时间戳转换异常time:{},异常", time, e);
            return false;
        }
        // 其他情况，返回false
        return false;
    }



    @Override
    public int getOrder()
    {
        return -200;
    }
}