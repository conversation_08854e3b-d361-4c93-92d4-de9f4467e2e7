package com.ruoyi.gateway.filter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import com.onecity.os.common.core.utils.StringUtils;
import com.ruoyi.gateway.util.EncryptHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.filter.factory.rewrite.ModifyRequestBodyGatewayFilterFactory;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import java.net.URI;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

@Component
@Slf4j
public class EncryptionParamFilter implements GlobalFilter, Ordered {
    @Value("${sm2.request.privateKey:819a90e45f19bbd8b59594bc2057224e3083e7d87a61f6603da1707a6d6cf8df}")
    String privateKey;
    @Value("${sm2.request.publicKey:04d12d994d6d298d3c37a7b685d072a720afcc91c2df985cb09e04063bf3b82a12ce279aa209f1b0faa71c3acd4c21524eef403d39b3f53aaa109e0f7baeef0e3b}")
    String publicKey;

    private static final List<String> whiteList = Arrays.asList("/system-portal/**", "/home-portal/**", "/app-portal/**");


    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        //第三种方式，使用 ModifyRequestBodyGatewayFilterFactory
        ServerHttpRequest request = exchange.getRequest();

        String url = request.getURI().getPath();
        // 跳过不需要验证的路径
        if (StringUtils.matches(url, whiteList)) {
            return chain.filter(exchange);
        }
        if (logRequestBody(request)) {
            return chain.filter(exchange);
        }
        SM2 sm2 = SmUtil.sm2(privateKey, publicKey);

        ModifyRequestBodyGatewayFilterFactory.Config modifyRequestConfig = new ModifyRequestBodyGatewayFilterFactory.Config()
                .setContentType(ContentType.APPLICATION_JSON.getMimeType())
                .setRewriteFunction(String.class, String.class, (exchange1, originalRequestBody) -> {
                    log.info("gateway request log; requestBody:{}", originalRequestBody);
                    try {
                        String encFlag = exchange.getRequest().getHeaders().getFirst("encFlag");
                        String encFlag1 = exchange.getRequest().getHeaders().getFirst("Enc-Flag");
                        if ("1".equals(encFlag) || "1".equals(encFlag1)) {
                            String str = StrUtil.utf8Str(sm2.decrypt(originalRequestBody, KeyType.PrivateKey));
                            return Mono.just(str);
                        }
                    } catch (Exception e) {
                        log.info("gateway request log; decrypt failed, e:", e);
                    }
                    return Mono.just(originalRequestBody);
                });


        return new ModifyRequestBodyGatewayFilterFactory().apply(modifyRequestConfig).filter(exchange, chain);
    }

    @Override
    public int getOrder() {
        return -2;
    }

    private boolean logRequestBody(ServerHttpRequest request) {
        boolean isFileUpload = false;
        AtomicReference<Boolean> bodyIsEmpty = new AtomicReference<>(true);
        URI requestUri = request.getURI();
        String uriQuery = requestUri.getQuery();
        String url = requestUri.getPath() + (StringUtils.isNotBlank(uriQuery) ? "?" + uriQuery : "");
        HttpHeaders headers = request.getHeaders();
        MediaType mediaType = headers.getContentType();
        String schema = requestUri.getScheme();
        String method = request.getMethodValue().toUpperCase();
        if ((!"http".equals(schema) && !"https".equals(schema))) {
            return isFileUpload;
        }
        //如果body为空则不加密
        if(method.equals("POST")&&"0".equals(request.getHeaders().getFirst("encFlag"))){
            return true;
        }
        String reqBody = null;
        if (Objects.nonNull(mediaType) && EncryptHelper.isUploadFile(mediaType)) {
            isFileUpload = true;
            reqBody = "上传文件";
        } else {
            if (method.equals("GET") || method.equals("DELETE")) {
                isFileUpload = true;
                if (StringUtils.isNotBlank(uriQuery)) {
                    reqBody = uriQuery;
                }
            }
        }
        log.info("gateway request log; url:{}, method:{}, body:{}, header:{}", url, method, reqBody, headers);
        return isFileUpload;
    }
}
