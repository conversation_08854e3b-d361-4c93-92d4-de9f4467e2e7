package com.ruoyi.gateway.util;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/10/14
 **/
public class ContentType {
    static Map<String, String> CONTENT_TYPE = new HashMap<>();

    static Map<String, String> FILE_CONTENT_TYPE = new HashMap<>();

    /**
     * 通过key获取ContentType值
     * @param ext 后缀
     * @return CONTENT_TYPE
     */
    public static String getContentType(String ext) {
        return CONTENT_TYPE.get(ext);
    }

    public static Map<String, String> getAll() {
        return new HashMap<>(CONTENT_TYPE);
    }

    public static Map<String, String> getAllFile() {
        return new HashMap<>(FILE_CONTENT_TYPE);
    }

    static {
        CONTENT_TYPE.put(".load" , "text/html");
        CONTENT_TYPE.put(".123" , "application/vnd.lotus-1-2-3");
        CONTENT_TYPE.put(".3ds" , "image/x-3ds");
        CONTENT_TYPE.put(".3g2" , "video/3gpp");
        CONTENT_TYPE.put(".3ga" , "video/3gpp");
        CONTENT_TYPE.put(".3gp" , "video/3gpp");
        CONTENT_TYPE.put(".3gpp" , "video/3gpp");
        CONTENT_TYPE.put(".602" , "application/x-t602");
        CONTENT_TYPE.put(".669" , "audio/x-mod");
        CONTENT_TYPE.put(".7z" , "application/x-7z-compressed");
        CONTENT_TYPE.put(".a" , "application/x-archive");
        CONTENT_TYPE.put(".aac" , "audio/mp4");
        CONTENT_TYPE.put(".abw" , "application/x-abiword");
        CONTENT_TYPE.put(".abw.crashed" , "application/x-abiword");
        CONTENT_TYPE.put(".abw.gz" , "application/x-abiword");
        CONTENT_TYPE.put(".ac3" , "audio/ac3");
        CONTENT_TYPE.put(".ace" , "application/x-ace");
        CONTENT_TYPE.put(".adb" , "text/x-adasrc");
        CONTENT_TYPE.put(".ads" , "text/x-adasrc");
        CONTENT_TYPE.put(".afm" , "application/x-font-afm");
        CONTENT_TYPE.put(".ag" , "image/x-applix-graphics");
        CONTENT_TYPE.put(".ai" , "application/illustrator");
        CONTENT_TYPE.put(".aif" , "audio/x-aiff");
        CONTENT_TYPE.put(".aifc" , "audio/x-aiff");
        CONTENT_TYPE.put(".aiff" , "audio/x-aiff");
        CONTENT_TYPE.put(".al" , "application/x-perl");
        CONTENT_TYPE.put(".alz" , "application/x-alz");
        CONTENT_TYPE.put(".amr" , "audio/amr");
        CONTENT_TYPE.put(".ani" , "application/x-navi-animation");
        CONTENT_TYPE.put(".anim[1-9j]" , "video/x-anim");
        CONTENT_TYPE.put(".anx" , "application/annodex");
        CONTENT_TYPE.put(".ape" , "audio/x-ape");
        CONTENT_TYPE.put(".arj" , "application/x-arj");
        CONTENT_TYPE.put(".arw" , "image/x-sony-arw");
        CONTENT_TYPE.put(".as" , "application/x-applix-spreadsheet");
        CONTENT_TYPE.put(".asc" , "text/plain");
        CONTENT_TYPE.put(".asf" , "video/x-ms-asf");
        CONTENT_TYPE.put(".asp" , "application/x-asp");
        CONTENT_TYPE.put(".ass" , "text/x-ssa");
        CONTENT_TYPE.put(".asx" , "audio/x-ms-asx");
        CONTENT_TYPE.put(".atom" , "application/atom+xml");
        CONTENT_TYPE.put(".au" , "audio/basic");
        CONTENT_TYPE.put(".avi" , "video/x-msvideo");
        CONTENT_TYPE.put(".aw" , "application/x-applix-word");
        CONTENT_TYPE.put(".awb" , "audio/amr-wb");
        CONTENT_TYPE.put(".awk" , "application/x-awk");
        CONTENT_TYPE.put(".axa" , "audio/annodex");
        CONTENT_TYPE.put(".axv" , "video/annodex");
        CONTENT_TYPE.put(".bak" , "application/x-trash");
        CONTENT_TYPE.put(".bcpio" , "application/x-bcpio");
        CONTENT_TYPE.put(".bdf" , "application/x-font-bdf");
        CONTENT_TYPE.put(".bib" , "text/x-bibtex");
        CONTENT_TYPE.put(".bin" , "application/octet-stream");
        CONTENT_TYPE.put(".blend" , "application/x-blender");
        CONTENT_TYPE.put(".blender" , "application/x-blender");
        CONTENT_TYPE.put(".bmp" , "image/bmp");
        CONTENT_TYPE.put(".bz" , "application/x-bzip");
        CONTENT_TYPE.put(".bz2" , "application/x-bzip");
        CONTENT_TYPE.put(".c" , "text/x-csrc");
        CONTENT_TYPE.put(".c++" , "text/x-c++src");
        CONTENT_TYPE.put(".cab" , "application/vnd.ms-cab-compressed");
        CONTENT_TYPE.put(".cb7" , "application/x-cb7");
        CONTENT_TYPE.put(".cbr" , "application/x-cbr");
        CONTENT_TYPE.put(".cbt" , "application/x-cbt");
        CONTENT_TYPE.put(".cbz" , "application/x-cbz");
        CONTENT_TYPE.put(".cc" , "text/x-c++src");
        CONTENT_TYPE.put(".cdf" , "application/x-netcdf");
        CONTENT_TYPE.put(".cdr" , "application/vnd.corel-draw");
        CONTENT_TYPE.put(".cer" , "application/x-x509-ca-cert");
        CONTENT_TYPE.put(".cert" , "application/x-x509-ca-cert");
        CONTENT_TYPE.put(".cgm" , "image/cgm");
        CONTENT_TYPE.put(".chm" , "application/x-chm");
        CONTENT_TYPE.put(".chrt" , "application/x-kchart");
        CONTENT_TYPE.put(".class" , "application/x-java");
        CONTENT_TYPE.put(".cls" , "text/x-tex");
        CONTENT_TYPE.put(".cmake" , "text/x-cmake");
        CONTENT_TYPE.put(".cpio" , "application/x-cpio");
        CONTENT_TYPE.put(".cpio.gz" , "application/x-cpio-compressed");
        CONTENT_TYPE.put(".cpp" , "text/x-c++src");
        CONTENT_TYPE.put(".cr2" , "image/x-canon-cr2");
        CONTENT_TYPE.put(".crt" , "application/x-x509-ca-cert");
        CONTENT_TYPE.put(".crw" , "image/x-canon-crw");
        CONTENT_TYPE.put(".cs" , "text/x-csharp");
        CONTENT_TYPE.put(".csh" , "application/x-csh");
        CONTENT_TYPE.put(".css" , "text/css");
        CONTENT_TYPE.put(".cssl" , "text/css");
        CONTENT_TYPE.put(".csv" , "text/csv");
        CONTENT_TYPE.put(".cue" , "application/x-cue");
        CONTENT_TYPE.put(".cur" , "image/x-win-bitmap");
        CONTENT_TYPE.put(".cxx" , "text/x-c++src");
        CONTENT_TYPE.put(".d" , "text/x-dsrc");
        CONTENT_TYPE.put(".dar" , "application/x-dar");
        CONTENT_TYPE.put(".dbf" , "application/x-dbf");
        CONTENT_TYPE.put(".dc" , "application/x-dc-rom");
        CONTENT_TYPE.put(".dcl" , "text/x-dcl");
        CONTENT_TYPE.put(".dcm" , "application/dicom");
        CONTENT_TYPE.put(".dcr" , "image/x-kodak-dcr");
        CONTENT_TYPE.put(".dds" , "image/x-dds");
        CONTENT_TYPE.put(".deb" , "application/x-deb");
        CONTENT_TYPE.put(".der" , "application/x-x509-ca-cert");
        CONTENT_TYPE.put(".desktop" , "application/x-desktop");
        CONTENT_TYPE.put(".dia" , "application/x-dia-diagram");
        CONTENT_TYPE.put(".diff" , "text/x-patch");
        CONTENT_TYPE.put(".divx" , "video/x-msvideo");
        CONTENT_TYPE.put(".djv" , "image/vnd.djvu");
        CONTENT_TYPE.put(".djvu" , "image/vnd.djvu");
        CONTENT_TYPE.put(".dng" , "image/x-adobe-dng");
        CONTENT_TYPE.put(".doc" , "application/msword");
        CONTENT_TYPE.put(".docbook" , "application/docbook+xml");
        CONTENT_TYPE.put(".docm" , "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        CONTENT_TYPE.put(".docx" , "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        CONTENT_TYPE.put(".dot" , "text/vnd.graphviz");
        CONTENT_TYPE.put(".dsl" , "text/x-dsl");
        CONTENT_TYPE.put(".dtd" , "application/xml-dtd");
        CONTENT_TYPE.put(".dtx" , "text/x-tex");
        CONTENT_TYPE.put(".dv" , "video/dv");
        CONTENT_TYPE.put(".dvi" , "application/x-dvi");
        CONTENT_TYPE.put(".dvi.bz2" , "application/x-bzdvi");
        CONTENT_TYPE.put(".dvi.gz" , "application/x-gzdvi");
        CONTENT_TYPE.put(".dwg" , "image/vnd.dwg");
        CONTENT_TYPE.put(".dxf" , "image/vnd.dxf");
        CONTENT_TYPE.put(".e" , "text/x-eiffel");
        CONTENT_TYPE.put(".egon" , "application/x-egon");
        CONTENT_TYPE.put(".eif" , "text/x-eiffel");
        CONTENT_TYPE.put(".el" , "text/x-emacs-lisp");
        CONTENT_TYPE.put(".emf" , "image/x-emf");
        CONTENT_TYPE.put(".emp" , "application/vnd.emusic-emusic_package");
        CONTENT_TYPE.put(".ent" , "application/xml-external-parsed-entity");
        CONTENT_TYPE.put(".eps" , "image/x-eps");
        CONTENT_TYPE.put(".eps.bz2" , "image/x-bzeps");
        CONTENT_TYPE.put(".eps.gz" , "image/x-gzeps");
        CONTENT_TYPE.put(".epsf" , "image/x-eps");
        CONTENT_TYPE.put(".epsf.bz2" , "image/x-bzeps");
        CONTENT_TYPE.put(".epsf.gz" , "image/x-gzeps");
        CONTENT_TYPE.put(".epsi" , "image/x-eps");
        CONTENT_TYPE.put(".epsi.bz2" , "image/x-bzeps");
        CONTENT_TYPE.put(".epsi.gz" , "image/x-gzeps");
        CONTENT_TYPE.put(".epub" , "application/epub+zip");
        CONTENT_TYPE.put(".erl" , "text/x-erlang");
        CONTENT_TYPE.put(".es" , "application/ecmascript");
        CONTENT_TYPE.put(".etheme" , "application/x-e-theme");
        CONTENT_TYPE.put(".etx" , "text/x-setext");
        CONTENT_TYPE.put(".exe" , "application/x-ms-dos-executable");
        CONTENT_TYPE.put(".exr" , "image/x-exr");
        CONTENT_TYPE.put(".ez" , "application/andrew-inset");
        CONTENT_TYPE.put(".f" , "text/x-fortran");
        CONTENT_TYPE.put(".f90" , "text/x-fortran");
        CONTENT_TYPE.put(".f95" , "text/x-fortran");
        CONTENT_TYPE.put(".fb2" , "application/x-fictionbook+xml");
        CONTENT_TYPE.put(".fig" , "image/x-xfig");
        CONTENT_TYPE.put(".fits" , "image/fits");
        CONTENT_TYPE.put(".fl" , "application/x-fluid");
        CONTENT_TYPE.put(".flac" , "audio/x-flac");
        CONTENT_TYPE.put(".flc" , "video/x-flic");
        CONTENT_TYPE.put(".fli" , "video/x-flic");
        CONTENT_TYPE.put(".flv" , "video/x-flv");
        CONTENT_TYPE.put(".flw" , "application/x-kivio");
        CONTENT_TYPE.put(".fo" , "text/x-xslfo");
        CONTENT_TYPE.put(".for" , "text/x-fortran");
        CONTENT_TYPE.put(".g3" , "image/fax-g3");
        CONTENT_TYPE.put(".gb" , "application/x-gameboy-rom");
        CONTENT_TYPE.put(".gba" , "application/x-gba-rom");
        CONTENT_TYPE.put(".gcrd" , "text/directory");
        CONTENT_TYPE.put(".ged" , "application/x-gedcom");
        CONTENT_TYPE.put(".gedcom" , "application/x-gedcom");
        CONTENT_TYPE.put(".gen" , "application/x-genesis-rom");
        CONTENT_TYPE.put(".gf" , "application/x-tex-gf");
        CONTENT_TYPE.put(".gg" , "application/x-sms-rom");
        CONTENT_TYPE.put(".gif" , "image/gif");
        CONTENT_TYPE.put(".glade" , "application/x-glade");
        CONTENT_TYPE.put(".gmo" , "application/x-gettext-translation");
        CONTENT_TYPE.put(".gnc" , "application/x-gnucash");
        CONTENT_TYPE.put(".gnd" , "application/gnunet-directory");
        CONTENT_TYPE.put(".gnucash" , "application/x-gnucash");
        CONTENT_TYPE.put(".gnumeric" , "application/x-gnumeric");
        CONTENT_TYPE.put(".gnuplot" , "application/x-gnuplot");
        CONTENT_TYPE.put(".gp" , "application/x-gnuplot");
        CONTENT_TYPE.put(".gpg" , "application/pgp-encrypted");
        CONTENT_TYPE.put(".gplt" , "application/x-gnuplot");
        CONTENT_TYPE.put(".gra" , "application/x-graphite");
        CONTENT_TYPE.put(".gsf" , "application/x-font-type1");
        CONTENT_TYPE.put(".gsm" , "audio/x-gsm");
        CONTENT_TYPE.put(".gtar" , "application/x-tar");
        CONTENT_TYPE.put(".gv" , "text/vnd.graphviz");
        CONTENT_TYPE.put(".gvp" , "text/x-google-video-pointer");
        CONTENT_TYPE.put(".gz" , "application/x-gzip");
        CONTENT_TYPE.put(".h" , "text/x-chdr");
        CONTENT_TYPE.put(".h++" , "text/x-c++hdr");
        CONTENT_TYPE.put(".hdf" , "application/x-hdf");
        CONTENT_TYPE.put(".hh" , "text/x-c++hdr");
        CONTENT_TYPE.put(".hp" , "text/x-c++hdr");
        CONTENT_TYPE.put(".hpgl" , "application/vnd.hp-hpgl");
        CONTENT_TYPE.put(".hpp" , "text/x-c++hdr");
        CONTENT_TYPE.put(".hs" , "text/x-haskell");
        CONTENT_TYPE.put(".htm" , "text/html");
        CONTENT_TYPE.put(".html" , "text/html");
        CONTENT_TYPE.put(".hwp" , "application/x-hwp");
        CONTENT_TYPE.put(".hwt" , "application/x-hwt");
        CONTENT_TYPE.put(".hxx" , "text/x-c++hdr");
        CONTENT_TYPE.put(".ica" , "application/x-ica");
        CONTENT_TYPE.put(".icb" , "image/x-tga");
        CONTENT_TYPE.put(".icns" , "image/x-icns");
        CONTENT_TYPE.put(".ico" , "image/vnd.microsoft.icon");
        CONTENT_TYPE.put(".ics" , "text/calendar");
        CONTENT_TYPE.put(".idl" , "text/x-idl");
        CONTENT_TYPE.put(".ief" , "image/ief");
        CONTENT_TYPE.put(".iff" , "image/x-iff");
        CONTENT_TYPE.put(".ilbm" , "image/x-ilbm");
        CONTENT_TYPE.put(".ime" , "text/x-imelody");
        CONTENT_TYPE.put(".imy" , "text/x-imelody");
        CONTENT_TYPE.put(".ins" , "text/x-tex");
        CONTENT_TYPE.put(".iptables" , "text/x-iptables");
        CONTENT_TYPE.put(".iso" , "application/x-cd-image");
        CONTENT_TYPE.put(".iso9660" , "application/x-cd-image");
        CONTENT_TYPE.put(".it" , "audio/x-it");
        CONTENT_TYPE.put(".j2k" , "image/jp2");
        CONTENT_TYPE.put(".jad" , "text/vnd.sun.j2me.app-descriptor");
        CONTENT_TYPE.put(".jar" , "application/x-java-archive");
        CONTENT_TYPE.put(".java" , "text/x-java");
        CONTENT_TYPE.put(".jng" , "image/x-jng");
        CONTENT_TYPE.put(".jnlp" , "application/x-java-jnlp-file");
        CONTENT_TYPE.put(".jp2" , "image/jp2");
        CONTENT_TYPE.put(".jpc" , "image/jp2");
        CONTENT_TYPE.put(".jpe" , "image/jpeg");
        CONTENT_TYPE.put(".jpeg" , "image/jpeg");
        CONTENT_TYPE.put(".jpf" , "image/jp2");
        CONTENT_TYPE.put(".jpg" , "image/jpeg");
        CONTENT_TYPE.put(".jpr" , "application/x-jbuilder-project");
        CONTENT_TYPE.put(".jpx" , "image/jp2");
        CONTENT_TYPE.put(".js" , "application/javascript");
        CONTENT_TYPE.put(".json" , "application/json");
        CONTENT_TYPE.put(".jsonp" , "application/jsonp");
        CONTENT_TYPE.put(".k25" , "image/x-kodak-k25");
        CONTENT_TYPE.put(".kar" , "audio/midi");
        CONTENT_TYPE.put(".karbon" , "application/x-karbon");
        CONTENT_TYPE.put(".kdc" , "image/x-kodak-kdc");
        CONTENT_TYPE.put(".kdelnk" , "application/x-desktop");
        CONTENT_TYPE.put(".kexi" , "application/x-kexiproject-sqlite3");
        CONTENT_TYPE.put(".kexic" , "application/x-kexi-connectiondata");
        CONTENT_TYPE.put(".kexis" , "application/x-kexiproject-shortcut");
        CONTENT_TYPE.put(".kfo" , "application/x-kformula");
        CONTENT_TYPE.put(".kil" , "application/x-killustrator");
        CONTENT_TYPE.put(".kino" , "application/smil");
        CONTENT_TYPE.put(".kml" , "application/vnd.google-earth.kml+xml");
        CONTENT_TYPE.put(".kmz" , "application/vnd.google-earth.kmz");
        CONTENT_TYPE.put(".kon" , "application/x-kontour");
        CONTENT_TYPE.put(".kpm" , "application/x-kpovmodeler");
        CONTENT_TYPE.put(".kpr" , "application/x-kpresenter");
        CONTENT_TYPE.put(".kpt" , "application/x-kpresenter");
        CONTENT_TYPE.put(".kra" , "application/x-krita");
        CONTENT_TYPE.put(".ksp" , "application/x-kspread");
        CONTENT_TYPE.put(".kud" , "application/x-kugar");
        CONTENT_TYPE.put(".kwd" , "application/x-kword");
        CONTENT_TYPE.put(".kwt" , "application/x-kword");
        CONTENT_TYPE.put(".la" , "application/x-shared-library-la");
        CONTENT_TYPE.put(".latex" , "text/x-tex");
        CONTENT_TYPE.put(".ldif" , "text/x-ldif");
        CONTENT_TYPE.put(".lha" , "application/x-lha");
        CONTENT_TYPE.put(".lhs" , "text/x-literate-haskell");
        CONTENT_TYPE.put(".lhz" , "application/x-lhz");
        CONTENT_TYPE.put(".log" , "text/x-log");
        CONTENT_TYPE.put(".ltx" , "text/x-tex");
        CONTENT_TYPE.put(".lua" , "text/x-lua");
        CONTENT_TYPE.put(".lwo" , "image/x-lwo");
        CONTENT_TYPE.put(".lwob" , "image/x-lwo");
        CONTENT_TYPE.put(".lws" , "image/x-lws");
        CONTENT_TYPE.put(".ly" , "text/x-lilypond");
        CONTENT_TYPE.put(".lyx" , "application/x-lyx");
        CONTENT_TYPE.put(".lz" , "application/x-lzip");
        CONTENT_TYPE.put(".lzh" , "application/x-lha");
        CONTENT_TYPE.put(".lzma" , "application/x-lzma");
        CONTENT_TYPE.put(".lzo" , "application/x-lzop");
        CONTENT_TYPE.put(".m" , "text/x-matlab");
        CONTENT_TYPE.put(".m15" , "audio/x-mod");
        CONTENT_TYPE.put(".m2t" , "video/mpeg");
        CONTENT_TYPE.put(".m3u" , "audio/x-mpegurl");
        CONTENT_TYPE.put(".m3u8" , "audio/x-mpegurl");
        CONTENT_TYPE.put(".m4" , "application/x-m4");
        CONTENT_TYPE.put(".m4a" , "audio/mp4");
        CONTENT_TYPE.put(".m4b" , "audio/x-m4b");
        CONTENT_TYPE.put(".m4v" , "video/mp4");
        CONTENT_TYPE.put(".mab" , "application/x-markaby");
        CONTENT_TYPE.put(".man" , "application/x-troff-man");
        CONTENT_TYPE.put(".mbox" , "application/mbox");
        CONTENT_TYPE.put(".md" , "application/x-genesis-rom");
        CONTENT_TYPE.put(".mdb" , "application/vnd.ms-access");
        CONTENT_TYPE.put(".mdi" , "image/vnd.ms-modi");
        CONTENT_TYPE.put(".me" , "text/x-troff-me");
        CONTENT_TYPE.put(".med" , "audio/x-mod");
        CONTENT_TYPE.put(".metalink" , "application/metalink+xml");
        CONTENT_TYPE.put(".mgp" , "application/x-magicpoint");
        CONTENT_TYPE.put(".mid" , "audio/midi");
        CONTENT_TYPE.put(".midi" , "audio/midi");
        CONTENT_TYPE.put(".mif" , "application/x-mif");
        CONTENT_TYPE.put(".minipsf" , "audio/x-minipsf");
        CONTENT_TYPE.put(".mka" , "audio/x-matroska");
        CONTENT_TYPE.put(".mkv" , "video/x-matroska");
        CONTENT_TYPE.put(".ml" , "text/x-ocaml");
        CONTENT_TYPE.put(".mli" , "text/x-ocaml");
        CONTENT_TYPE.put(".mm" , "text/x-troff-mm");
        CONTENT_TYPE.put(".mmf" , "application/x-smaf");
        CONTENT_TYPE.put(".mml" , "text/mathml");
        CONTENT_TYPE.put(".mng" , "video/x-mng");
        CONTENT_TYPE.put(".mo" , "application/x-gettext-translation");
        CONTENT_TYPE.put(".mo3" , "audio/x-mo3");
        CONTENT_TYPE.put(".moc" , "text/x-moc");
        CONTENT_TYPE.put(".mod" , "audio/x-mod");
        CONTENT_TYPE.put(".mof" , "text/x-mof");
        CONTENT_TYPE.put(".moov" , "video/quicktime");
        CONTENT_TYPE.put(".mov" , "video/quicktime");
        CONTENT_TYPE.put(".movie" , "video/x-sgi-movie");
        CONTENT_TYPE.put(".mp+" , "audio/x-musepack");
        CONTENT_TYPE.put(".mp2" , "video/mpeg");
        CONTENT_TYPE.put(".mp3" , "audio/mpeg");
        CONTENT_TYPE.put(".mp4" , "video/mp4");
        CONTENT_TYPE.put(".mpc" , "audio/x-musepack");
        CONTENT_TYPE.put(".mpe" , "video/mpeg");
        CONTENT_TYPE.put(".mpeg" , "video/mpeg");
        CONTENT_TYPE.put(".mpg" , "video/mpeg");
        CONTENT_TYPE.put(".mpga" , "audio/mpeg");
        CONTENT_TYPE.put(".mpp" , "audio/x-musepack");
        CONTENT_TYPE.put(".mrl" , "text/x-mrml");
        CONTENT_TYPE.put(".mrml" , "text/x-mrml");
        CONTENT_TYPE.put(".mrw" , "image/x-minolta-mrw");
        CONTENT_TYPE.put(".ms" , "text/x-troff-ms");
        CONTENT_TYPE.put(".msi" , "application/x-msi");
        CONTENT_TYPE.put(".msod" , "image/x-msod");
        CONTENT_TYPE.put(".msx" , "application/x-msx-rom");
        CONTENT_TYPE.put(".mtm" , "audio/x-mod");
        CONTENT_TYPE.put(".mup" , "text/x-mup");
        CONTENT_TYPE.put(".mxf" , "application/mxf");
        CONTENT_TYPE.put(".n64" , "application/x-n64-rom");
        CONTENT_TYPE.put(".nb" , "application/mathematica");
        CONTENT_TYPE.put(".nc" , "application/x-netcdf");
        CONTENT_TYPE.put(".nds" , "application/x-nintendo-ds-rom");
        CONTENT_TYPE.put(".nef" , "image/x-nikon-nef");
        CONTENT_TYPE.put(".nes" , "application/x-nes-rom");
        CONTENT_TYPE.put(".nfo" , "text/x-nfo");
        CONTENT_TYPE.put(".not" , "text/x-mup");
        CONTENT_TYPE.put(".nsc" , "application/x-netshow-channel");
        CONTENT_TYPE.put(".nsv" , "video/x-nsv");
        CONTENT_TYPE.put(".o" , "application/x-object");
        CONTENT_TYPE.put(".obj" , "application/x-tgif");
        CONTENT_TYPE.put(".ocl" , "text/x-ocl");
        CONTENT_TYPE.put(".oda" , "application/oda");
        CONTENT_TYPE.put(".odb" , "application/vnd.oasis.opendocument.database");
        CONTENT_TYPE.put(".odc" , "application/vnd.oasis.opendocument.chart");
        CONTENT_TYPE.put(".odf" , "application/vnd.oasis.opendocument.formula");
        CONTENT_TYPE.put(".odg" , "application/vnd.oasis.opendocument.graphics");
        CONTENT_TYPE.put(".odi" , "application/vnd.oasis.opendocument.image");
        CONTENT_TYPE.put(".odm" , "application/vnd.oasis.opendocument.text-master");
        CONTENT_TYPE.put(".odp" , "application/vnd.oasis.opendocument.presentation");
        CONTENT_TYPE.put(".ods" , "application/vnd.oasis.opendocument.spreadsheet");
        CONTENT_TYPE.put(".odt" , "application/vnd.oasis.opendocument.text");
        CONTENT_TYPE.put(".oga" , "audio/ogg");
        CONTENT_TYPE.put(".ogg" , "video/x-theora+ogg");
        CONTENT_TYPE.put(".ogm" , "video/x-ogm+ogg");
        CONTENT_TYPE.put(".ogv" , "video/ogg");
        CONTENT_TYPE.put(".ogx" , "application/ogg");
        CONTENT_TYPE.put(".old" , "application/x-trash");
        CONTENT_TYPE.put(".oleo" , "application/x-oleo");
        CONTENT_TYPE.put(".opml" , "text/x-opml+xml");
        CONTENT_TYPE.put(".ora" , "image/openraster");
        CONTENT_TYPE.put(".orf" , "image/x-olympus-orf");
        CONTENT_TYPE.put(".otc" , "application/vnd.oasis.opendocument.chart-template");
        CONTENT_TYPE.put(".otf" , "application/x-font-otf");
        CONTENT_TYPE.put(".otg" , "application/vnd.oasis.opendocument.graphics-template");
        CONTENT_TYPE.put(".oth" , "application/vnd.oasis.opendocument.text-web");
        CONTENT_TYPE.put(".otp" , "application/vnd.oasis.opendocument.presentation-template");
        CONTENT_TYPE.put(".ots" , "application/vnd.oasis.opendocument.spreadsheet-template");
        CONTENT_TYPE.put(".ott" , "application/vnd.oasis.opendocument.text-template");
        CONTENT_TYPE.put(".owl" , "application/rdf+xml");
        CONTENT_TYPE.put(".oxt" , "application/vnd.openofficeorg.extension");
        CONTENT_TYPE.put(".p" , "text/x-pascal");
        CONTENT_TYPE.put(".p10" , "application/pkcs10");
        CONTENT_TYPE.put(".p12" , "application/x-pkcs12");
        CONTENT_TYPE.put(".p7b" , "application/x-pkcs7-certificates");
        CONTENT_TYPE.put(".p7s" , "application/pkcs7-signature");
        CONTENT_TYPE.put(".pack" , "application/x-java-pack200");
        CONTENT_TYPE.put(".pak" , "application/x-pak");
        CONTENT_TYPE.put(".par2" , "application/x-par2");
        CONTENT_TYPE.put(".pas" , "text/x-pascal");
        CONTENT_TYPE.put(".patch" , "text/x-patch");
        CONTENT_TYPE.put(".pbm" , "image/x-portable-bitmap");
        CONTENT_TYPE.put(".pcd" , "image/x-photo-cd");
        CONTENT_TYPE.put(".pcf" , "application/x-cisco-vpn-settings");
        CONTENT_TYPE.put(".pcf.gz" , "application/x-font-pcf");
        CONTENT_TYPE.put(".pcf.z" , "application/x-font-pcf");
        CONTENT_TYPE.put(".pcl" , "application/vnd.hp-pcl");
        CONTENT_TYPE.put(".pcx" , "image/x-pcx");
        CONTENT_TYPE.put(".pdb" , "chemical/x-pdb");
        CONTENT_TYPE.put(".pdc" , "application/x-aportisdoc");
        CONTENT_TYPE.put(".pdf" , "application/pdf");
        CONTENT_TYPE.put(".pdf.bz2" , "application/x-bzpdf");
        CONTENT_TYPE.put(".pdf.gz" , "application/x-gzpdf");
        CONTENT_TYPE.put(".pef" , "image/x-pentax-pef");
        CONTENT_TYPE.put(".pem" , "application/x-x509-ca-cert");
        CONTENT_TYPE.put(".perl" , "application/x-perl");
        CONTENT_TYPE.put(".pfa" , "application/x-font-type1");
        CONTENT_TYPE.put(".pfb" , "application/x-font-type1");
        CONTENT_TYPE.put(".pfx" , "application/x-pkcs12");
        CONTENT_TYPE.put(".pgm" , "image/x-portable-graymap");
        CONTENT_TYPE.put(".pgn" , "application/x-chess-pgn");
        CONTENT_TYPE.put(".pgp" , "application/pgp-encrypted");
        CONTENT_TYPE.put(".php" , "application/x-php");
        CONTENT_TYPE.put(".php3" , "application/x-php");
        CONTENT_TYPE.put(".php4" , "application/x-php");
        CONTENT_TYPE.put(".pict" , "image/x-pict");
        CONTENT_TYPE.put(".pict1" , "image/x-pict");
        CONTENT_TYPE.put(".pict2" , "image/x-pict");
        CONTENT_TYPE.put(".pickle" , "application/python-pickle");
        CONTENT_TYPE.put(".pk" , "application/x-tex-pk");
        CONTENT_TYPE.put(".pkipath" , "application/pkix-pkipath");
        CONTENT_TYPE.put(".pkr" , "application/pgp-keys");
        CONTENT_TYPE.put(".pl" , "application/x-perl");
        CONTENT_TYPE.put(".pla" , "audio/x-iriver-pla");
        CONTENT_TYPE.put(".pln" , "application/x-planperfect");
        CONTENT_TYPE.put(".pls" , "audio/x-scpls");
        CONTENT_TYPE.put(".pm" , "application/x-perl");
        CONTENT_TYPE.put(".png" , "image/png");
        CONTENT_TYPE.put(".pnm" , "image/x-portable-anymap");
        CONTENT_TYPE.put(".pntg" , "image/x-macpaint");
        CONTENT_TYPE.put(".po" , "text/x-gettext-translation");
        CONTENT_TYPE.put(".por" , "application/x-spss-por");
        CONTENT_TYPE.put(".pot" , "text/x-gettext-translation-template");
        CONTENT_TYPE.put(".ppm" , "image/x-portable-pixmap");
        CONTENT_TYPE.put(".pps" , "application/vnd.ms-powerpoint");
        CONTENT_TYPE.put(".ppt" , "application/vnd.ms-powerpoint");
        CONTENT_TYPE.put(".pptm" , "application/vnd.openxmlformats-officedocument.presentationml.presentation");
        CONTENT_TYPE.put(".pptx" , "application/vnd.openxmlformats-officedocument.presentationml.presentation");
        CONTENT_TYPE.put(".ppz" , "application/vnd.ms-powerpoint");
        CONTENT_TYPE.put(".prc" , "application/x-palm-database");
        CONTENT_TYPE.put(".ps" , "application/postscript");
        CONTENT_TYPE.put(".ps.bz2" , "application/x-bzpostscript");
        CONTENT_TYPE.put(".ps.gz" , "application/x-gzpostscript");
        CONTENT_TYPE.put(".psd" , "image/vnd.adobe.photoshop");
        CONTENT_TYPE.put(".psf" , "audio/x-psf");
        CONTENT_TYPE.put(".psf.gz" , "application/x-gz-font-linux-psf");
        CONTENT_TYPE.put(".psflib" , "audio/x-psflib");
        CONTENT_TYPE.put(".psid" , "audio/prs.sid");
        CONTENT_TYPE.put(".psw" , "application/x-pocket-word");
        CONTENT_TYPE.put(".pw" , "application/x-pw");
        CONTENT_TYPE.put(".py" , "text/x-python");
        CONTENT_TYPE.put(".pyc" , "application/x-python-bytecode");
        CONTENT_TYPE.put(".pyo" , "application/x-python-bytecode");
        CONTENT_TYPE.put(".qif" , "image/x-quicktime");
        CONTENT_TYPE.put(".qt" , "video/quicktime");
        CONTENT_TYPE.put(".qtif" , "image/x-quicktime");
        CONTENT_TYPE.put(".qtl" , "application/x-quicktime-media-link");
        CONTENT_TYPE.put(".qtvr" , "video/quicktime");
        CONTENT_TYPE.put(".ra" , "audio/vnd.rn-realaudio");
        CONTENT_TYPE.put(".raf" , "image/x-fuji-raf");
        CONTENT_TYPE.put(".ram" , "application/ram");
        CONTENT_TYPE.put(".rar" , "application/x-rar");
        CONTENT_TYPE.put(".ras" , "image/x-cmu-raster");
        CONTENT_TYPE.put(".raw" , "image/x-panasonic-raw");
        CONTENT_TYPE.put(".rax" , "audio/vnd.rn-realaudio");
        CONTENT_TYPE.put(".rb" , "application/x-ruby");
        CONTENT_TYPE.put(".rdf" , "application/rdf+xml");
        CONTENT_TYPE.put(".rdfs" , "application/rdf+xml");
        CONTENT_TYPE.put(".reg" , "text/x-ms-regedit");
        CONTENT_TYPE.put(".rej" , "application/x-reject");
        CONTENT_TYPE.put(".rgb" , "image/x-rgb");
        CONTENT_TYPE.put(".rle" , "image/rle");
        CONTENT_TYPE.put(".rm" , "application/vnd.rn-realmedia");
        CONTENT_TYPE.put(".rmj" , "application/vnd.rn-realmedia");
        CONTENT_TYPE.put(".rmm" , "application/vnd.rn-realmedia");
        CONTENT_TYPE.put(".rms" , "application/vnd.rn-realmedia");
        CONTENT_TYPE.put(".rmvb" , "application/vnd.rn-realmedia");
        CONTENT_TYPE.put(".rmx" , "application/vnd.rn-realmedia");
        CONTENT_TYPE.put(".roff" , "text/troff");
        CONTENT_TYPE.put(".rp" , "image/vnd.rn-realpix");
        CONTENT_TYPE.put(".rpm" , "application/x-rpm");
        CONTENT_TYPE.put(".rss" , "application/rss+xml");
        CONTENT_TYPE.put(".rt" , "text/vnd.rn-realtext");
        CONTENT_TYPE.put(".rtf" , "application/rtf");
        CONTENT_TYPE.put(".rtx" , "text/richtext");
        CONTENT_TYPE.put(".rv" , "video/vnd.rn-realvideo");
        CONTENT_TYPE.put(".rvx" , "video/vnd.rn-realvideo");
        CONTENT_TYPE.put(".s3m" , "audio/x-s3m");
        CONTENT_TYPE.put(".sam" , "application/x-amipro");
        CONTENT_TYPE.put(".sami" , "application/x-sami");
        CONTENT_TYPE.put(".sav" , "application/x-spss-sav");
        CONTENT_TYPE.put(".scm" , "text/x-scheme");
        CONTENT_TYPE.put(".sda" , "application/vnd.stardivision.draw");
        CONTENT_TYPE.put(".sdc" , "application/vnd.stardivision.calc");
        CONTENT_TYPE.put(".sdd" , "application/vnd.stardivision.impress");
        CONTENT_TYPE.put(".sdp" , "application/sdp");
        CONTENT_TYPE.put(".sds" , "application/vnd.stardivision.chart");
        CONTENT_TYPE.put(".sdw" , "application/vnd.stardivision.writer");
        CONTENT_TYPE.put(".sgf" , "application/x-go-sgf");
        CONTENT_TYPE.put(".sgi" , "image/x-sgi");
        CONTENT_TYPE.put(".sgl" , "application/vnd.stardivision.writer");
        CONTENT_TYPE.put(".sgm" , "text/sgml");
        CONTENT_TYPE.put(".sgml" , "text/sgml");
        CONTENT_TYPE.put(".sh" , "application/x-shellscript");
        CONTENT_TYPE.put(".shar" , "application/x-shar");
        CONTENT_TYPE.put(".shn" , "application/x-shorten");
        CONTENT_TYPE.put(".siag" , "application/x-siag");
        CONTENT_TYPE.put(".sid" , "audio/prs.sid");
        CONTENT_TYPE.put(".sik" , "application/x-trash");
        CONTENT_TYPE.put(".sis" , "application/vnd.symbian.install");
        CONTENT_TYPE.put(".sisx" , "x-epoc/x-sisx-app");
        CONTENT_TYPE.put(".sit" , "application/x-stuffit");
        CONTENT_TYPE.put(".siv" , "application/sieve");
        CONTENT_TYPE.put(".sk" , "image/x-skencil");
        CONTENT_TYPE.put(".sk1" , "image/x-skencil");
        CONTENT_TYPE.put(".skr" , "application/pgp-keys");
        CONTENT_TYPE.put(".slk" , "text/spreadsheet");
        CONTENT_TYPE.put(".smaf" , "application/x-smaf");
        CONTENT_TYPE.put(".smc" , "application/x-snes-rom");
        CONTENT_TYPE.put(".smd" , "application/vnd.stardivision.mail");
        CONTENT_TYPE.put(".smf" , "application/vnd.stardivision.math");
        CONTENT_TYPE.put(".smi" , "application/x-sami");
        CONTENT_TYPE.put(".smil" , "application/smil");
        CONTENT_TYPE.put(".sml" , "application/smil");
        CONTENT_TYPE.put(".sms" , "application/x-sms-rom");
        CONTENT_TYPE.put(".snd" , "audio/basic");
        CONTENT_TYPE.put(".so" , "application/x-sharedlib");
        CONTENT_TYPE.put(".spc" , "application/x-pkcs7-certificates");
        CONTENT_TYPE.put(".spd" , "application/x-font-speedo");
        CONTENT_TYPE.put(".spec" , "text/x-rpm-spec");
        CONTENT_TYPE.put(".spl" , "application/x-shockwave-flash");
        CONTENT_TYPE.put(".spx" , "audio/x-speex");
        CONTENT_TYPE.put(".sql" , "text/x-sql");
        CONTENT_TYPE.put(".sr2" , "image/x-sony-sr2");
        CONTENT_TYPE.put(".src" , "application/x-wais-source");
        CONTENT_TYPE.put(".srf" , "image/x-sony-srf");
        CONTENT_TYPE.put(".srt" , "application/x-subrip");
        CONTENT_TYPE.put(".ssa" , "text/x-ssa");
        CONTENT_TYPE.put(".stc" , "application/vnd.sun.xml.calc.template");
        CONTENT_TYPE.put(".std" , "application/vnd.sun.xml.draw.template");
        CONTENT_TYPE.put(".sti" , "application/vnd.sun.xml.impress.template");
        CONTENT_TYPE.put(".stm" , "audio/x-stm");
        CONTENT_TYPE.put(".stw" , "application/vnd.sun.xml.writer.template");
        CONTENT_TYPE.put(".sty" , "text/x-tex");
        CONTENT_TYPE.put(".sub" , "text/x-subviewer");
        CONTENT_TYPE.put(".sun" , "image/x-sun-raster");
        CONTENT_TYPE.put(".sv4cpio" , "application/x-sv4cpio");
        CONTENT_TYPE.put(".sv4crc" , "application/x-sv4crc");
        CONTENT_TYPE.put(".svg" , "image/svg+xml");
        CONTENT_TYPE.put(".svgz" , "image/svg+xml-compressed");
        CONTENT_TYPE.put(".swf" , "application/x-shockwave-flash");
        CONTENT_TYPE.put(".sxc" , "application/vnd.sun.xml.calc");
        CONTENT_TYPE.put(".sxd" , "application/vnd.sun.xml.draw");
        CONTENT_TYPE.put(".sxg" , "application/vnd.sun.xml.writer.global");
        CONTENT_TYPE.put(".sxi" , "application/vnd.sun.xml.impress");
        CONTENT_TYPE.put(".sxm" , "application/vnd.sun.xml.math");
        CONTENT_TYPE.put(".sxw" , "application/vnd.sun.xml.writer");
        CONTENT_TYPE.put(".sylk" , "text/spreadsheet");
        CONTENT_TYPE.put(".t" , "text/troff");
        CONTENT_TYPE.put(".t2t" , "text/x-txt2tags");
        CONTENT_TYPE.put(".tar" , "application/x-tar");
        CONTENT_TYPE.put(".tar.bz" , "application/x-bzip-compressed-tar");
        CONTENT_TYPE.put(".tar.bz2" , "application/x-bzip-compressed-tar");
        CONTENT_TYPE.put(".tar.gz" , "application/x-compressed-tar");
        CONTENT_TYPE.put(".tar.lzma" , "application/x-lzma-compressed-tar");
        CONTENT_TYPE.put(".tar.lzo" , "application/x-tzo");
        CONTENT_TYPE.put(".tar.xz" , "application/x-xz-compressed-tar");
        CONTENT_TYPE.put(".tar.z" , "application/x-tarz");
        CONTENT_TYPE.put(".tbz" , "application/x-bzip-compressed-tar");
        CONTENT_TYPE.put(".tbz2" , "application/x-bzip-compressed-tar");
        CONTENT_TYPE.put(".tcl" , "text/x-tcl");
        CONTENT_TYPE.put(".tex" , "text/x-tex");
        CONTENT_TYPE.put(".texi" , "text/x-texinfo");
        CONTENT_TYPE.put(".texinfo" , "text/x-texinfo");
        CONTENT_TYPE.put(".tga" , "image/x-tga");
        CONTENT_TYPE.put(".tgz" , "application/x-compressed-tar");
        CONTENT_TYPE.put(".theme" , "application/x-theme");
        CONTENT_TYPE.put(".themepack" , "application/x-windows-themepack");
        CONTENT_TYPE.put(".tif" , "image/tiff");
        CONTENT_TYPE.put(".tiff" , "image/tiff");
        CONTENT_TYPE.put(".tk" , "text/x-tcl");
        CONTENT_TYPE.put(".tlz" , "application/x-lzma-compressed-tar");
        CONTENT_TYPE.put(".tnef" , "application/vnd.ms-tnef");
        CONTENT_TYPE.put(".tnf" , "application/vnd.ms-tnef");
        CONTENT_TYPE.put(".toc" , "application/x-cdrdao-toc");
        CONTENT_TYPE.put(".torrent" , "application/x-bittorrent");
        CONTENT_TYPE.put(".tpic" , "image/x-tga");
        CONTENT_TYPE.put(".tr" , "text/troff");
        CONTENT_TYPE.put(".ts" , "application/x-linguist");
        CONTENT_TYPE.put(".tsv" , "text/tab-separated-values");
        CONTENT_TYPE.put(".tta" , "audio/x-tta");
        CONTENT_TYPE.put(".ttc" , "application/x-font-ttf");
        CONTENT_TYPE.put(".ttf" , "application/x-font-ttf");
        CONTENT_TYPE.put(".ttx" , "application/x-font-ttx");
        CONTENT_TYPE.put(".txt" , "text/plain");
        CONTENT_TYPE.put(".txz" , "application/x-xz-compressed-tar");
        CONTENT_TYPE.put(".tzo" , "application/x-tzo");
        CONTENT_TYPE.put(".ufraw" , "application/x-ufraw");
        CONTENT_TYPE.put(".ui" , "application/x-designer");
        CONTENT_TYPE.put(".uil" , "text/x-uil");
        CONTENT_TYPE.put(".ult" , "audio/x-mod");
        CONTENT_TYPE.put(".uni" , "audio/x-mod");
        CONTENT_TYPE.put(".uri" , "text/x-uri");
        CONTENT_TYPE.put(".url" , "text/x-uri");
        CONTENT_TYPE.put(".ustar" , "application/x-ustar");
        CONTENT_TYPE.put(".vala" , "text/x-vala");
        CONTENT_TYPE.put(".vapi" , "text/x-vala");
        CONTENT_TYPE.put(".vcf" , "text/directory");
        CONTENT_TYPE.put(".vcs" , "text/calendar");
        CONTENT_TYPE.put(".vct" , "text/directory");
        CONTENT_TYPE.put(".vda" , "image/x-tga");
        CONTENT_TYPE.put(".vhd" , "text/x-vhdl");
        CONTENT_TYPE.put(".vhdl" , "text/x-vhdl");
        CONTENT_TYPE.put(".viv" , "video/vivo");
        CONTENT_TYPE.put(".vivo" , "video/vivo");
        CONTENT_TYPE.put(".vlc" , "audio/x-mpegurl");
        CONTENT_TYPE.put(".vob" , "video/mpeg");
        CONTENT_TYPE.put(".voc" , "audio/x-voc");
        CONTENT_TYPE.put(".vor" , "application/vnd.stardivision.writer");
        CONTENT_TYPE.put(".vst" , "image/x-tga");
        CONTENT_TYPE.put(".wav" , "audio/x-wav");
        CONTENT_TYPE.put(".wax" , "audio/x-ms-asx");
        CONTENT_TYPE.put(".wb1" , "application/x-quattropro");
        CONTENT_TYPE.put(".wb2" , "application/x-quattropro");
        CONTENT_TYPE.put(".wb3" , "application/x-quattropro");
        CONTENT_TYPE.put(".wbmp" , "image/vnd.wap.wbmp");
        CONTENT_TYPE.put(".wcm" , "application/vnd.ms-works");
        CONTENT_TYPE.put(".wdb" , "application/vnd.ms-works");
        CONTENT_TYPE.put(".webm" , "video/webm");
        CONTENT_TYPE.put(".wk1" , "application/vnd.lotus-1-2-3");
        CONTENT_TYPE.put(".wk3" , "application/vnd.lotus-1-2-3");
        CONTENT_TYPE.put(".wk4" , "application/vnd.lotus-1-2-3");
        CONTENT_TYPE.put(".wks" , "application/vnd.ms-works");
        CONTENT_TYPE.put(".wma" , "audio/x-ms-wma");
        CONTENT_TYPE.put(".wmf" , "image/x-wmf");
        CONTENT_TYPE.put(".wml" , "text/vnd.wap.wml");
        CONTENT_TYPE.put(".wmls" , "text/vnd.wap.wmlscript");
        CONTENT_TYPE.put(".wmv" , "video/x-ms-wmv");
        CONTENT_TYPE.put(".wmx" , "audio/x-ms-asx");
        CONTENT_TYPE.put(".wp" , "application/vnd.wordperfect");
        CONTENT_TYPE.put(".wp4" , "application/vnd.wordperfect");
        CONTENT_TYPE.put(".wp5" , "application/vnd.wordperfect");
        CONTENT_TYPE.put(".wp6" , "application/vnd.wordperfect");
        CONTENT_TYPE.put(".wpd" , "application/vnd.wordperfect");
        CONTENT_TYPE.put(".wpg" , "application/x-wpg");
        CONTENT_TYPE.put(".wpl" , "application/vnd.ms-wpl");
        CONTENT_TYPE.put(".wpp" , "application/vnd.wordperfect");
        CONTENT_TYPE.put(".wps" , "application/vnd.ms-works");
        CONTENT_TYPE.put(".wri" , "application/x-mswrite");
        CONTENT_TYPE.put(".wrl" , "model/vrml");
        CONTENT_TYPE.put(".wv" , "audio/x-wavpack");
        CONTENT_TYPE.put(".wvc" , "audio/x-wavpack-correction");
        CONTENT_TYPE.put(".wvp" , "audio/x-wavpack");
        CONTENT_TYPE.put(".wvx" , "audio/x-ms-asx");
        CONTENT_TYPE.put(".x3f" , "image/x-sigma-x3f");
        CONTENT_TYPE.put(".xac" , "application/x-gnucash");
        CONTENT_TYPE.put(".xbel" , "application/x-xbel");
        CONTENT_TYPE.put(".xbl" , "application/xml");
        CONTENT_TYPE.put(".xbm" , "image/x-xbitmap");
        CONTENT_TYPE.put(".xcf" , "image/x-xcf");
        CONTENT_TYPE.put(".xcf.bz2" , "image/x-compressed-xcf");
        CONTENT_TYPE.put(".xcf.gz" , "image/x-compressed-xcf");
        CONTENT_TYPE.put(".xhtml" , "application/xhtml+xml");
        CONTENT_TYPE.put(".xi" , "audio/x-xi");
        CONTENT_TYPE.put(".xla" , "application/vnd.ms-excel");
        CONTENT_TYPE.put(".xlc" , "application/vnd.ms-excel");
        CONTENT_TYPE.put(".xld" , "application/vnd.ms-excel");
        CONTENT_TYPE.put(".xlf" , "application/x-xliff");
        CONTENT_TYPE.put(".xliff" , "application/x-xliff");
        CONTENT_TYPE.put(".xll" , "application/vnd.ms-excel");
        CONTENT_TYPE.put(".xlm" , "application/vnd.ms-excel");
        CONTENT_TYPE.put(".xls" , "application/vnd.ms-excel");
        CONTENT_TYPE.put(".xlsm" , "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        CONTENT_TYPE.put(".xlsx" , "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        CONTENT_TYPE.put(".xlt" , "application/vnd.ms-excel");
        CONTENT_TYPE.put(".xlw" , "application/vnd.ms-excel");
        CONTENT_TYPE.put(".xm" , "audio/x-xm");
        CONTENT_TYPE.put(".xmf" , "audio/x-xmf");
        CONTENT_TYPE.put(".xmi" , "text/x-xmi");
        CONTENT_TYPE.put(".xml" , "application/xml");
        CONTENT_TYPE.put(".xpm" , "image/x-xpixmap");
        CONTENT_TYPE.put(".xps" , "application/vnd.ms-xpsdocument");
        CONTENT_TYPE.put(".xsl" , "application/xml");
        CONTENT_TYPE.put(".xslfo" , "text/x-xslfo");
        CONTENT_TYPE.put(".xslt" , "application/xml");
        CONTENT_TYPE.put(".xspf" , "application/xspf+xml");
        CONTENT_TYPE.put(".xul" , "application/vnd.mozilla.xul+xml");
        CONTENT_TYPE.put(".xwd" , "image/x-xwindowdump");
        CONTENT_TYPE.put(".xyz" , "chemical/x-pdb");
        CONTENT_TYPE.put(".xz" , "application/x-xz");
        CONTENT_TYPE.put(".w2p" , "application/w2p");
        CONTENT_TYPE.put(".z" , "application/x-compress");
        CONTENT_TYPE.put(".zabw" , "application/x-abiword");
        CONTENT_TYPE.put(".zip" , "application/zip");
        CONTENT_TYPE.put("form-data" , "multipart/form-data");
        CONTENT_TYPE.put("mixed" , "multipart/mixed");


        // 各种文件类型（包括multipart/form-data）
        FILE_CONTENT_TYPE.put(".load" , "text/html");
        FILE_CONTENT_TYPE.put(".123" , "application/vnd.lotus-1-2-3");
        FILE_CONTENT_TYPE.put(".3ds" , "image/x-3ds");
        FILE_CONTENT_TYPE.put(".3g2" , "video/3gpp");
        FILE_CONTENT_TYPE.put(".3ga" , "video/3gpp");
        FILE_CONTENT_TYPE.put(".3gp" , "video/3gpp");
        FILE_CONTENT_TYPE.put(".3gpp" , "video/3gpp");
        FILE_CONTENT_TYPE.put(".602" , "application/x-t602");
        FILE_CONTENT_TYPE.put(".669" , "audio/x-mod");
        FILE_CONTENT_TYPE.put(".7z" , "application/x-7z-compressed");
        FILE_CONTENT_TYPE.put(".a" , "application/x-archive");
        FILE_CONTENT_TYPE.put(".aac" , "audio/mp4");
        FILE_CONTENT_TYPE.put(".abw" , "application/x-abiword");
        FILE_CONTENT_TYPE.put(".abw.crashed" , "application/x-abiword");
        FILE_CONTENT_TYPE.put(".abw.gz" , "application/x-abiword");
        FILE_CONTENT_TYPE.put(".ac3" , "audio/ac3");
        FILE_CONTENT_TYPE.put(".ace" , "application/x-ace");
        FILE_CONTENT_TYPE.put(".adb" , "text/x-adasrc");
        FILE_CONTENT_TYPE.put(".ads" , "text/x-adasrc");
        FILE_CONTENT_TYPE.put(".afm" , "application/x-font-afm");
        FILE_CONTENT_TYPE.put(".ag" , "image/x-applix-graphics");
        FILE_CONTENT_TYPE.put(".ai" , "application/illustrator");
        FILE_CONTENT_TYPE.put(".aif" , "audio/x-aiff");
        FILE_CONTENT_TYPE.put(".aifc" , "audio/x-aiff");
        FILE_CONTENT_TYPE.put(".aiff" , "audio/x-aiff");
        FILE_CONTENT_TYPE.put(".al" , "application/x-perl");
        FILE_CONTENT_TYPE.put(".alz" , "application/x-alz");
        FILE_CONTENT_TYPE.put(".amr" , "audio/amr");
        FILE_CONTENT_TYPE.put(".ani" , "application/x-navi-animation");
        FILE_CONTENT_TYPE.put(".anim[1-9j]" , "video/x-anim");
        FILE_CONTENT_TYPE.put(".anx" , "application/annodex");
        FILE_CONTENT_TYPE.put(".ape" , "audio/x-ape");
        FILE_CONTENT_TYPE.put(".arj" , "application/x-arj");
        FILE_CONTENT_TYPE.put(".arw" , "image/x-sony-arw");
        FILE_CONTENT_TYPE.put(".as" , "application/x-applix-spreadsheet");
        FILE_CONTENT_TYPE.put(".asc" , "text/plain");
        FILE_CONTENT_TYPE.put(".asf" , "video/x-ms-asf");
        FILE_CONTENT_TYPE.put(".asp" , "application/x-asp");
        FILE_CONTENT_TYPE.put(".ass" , "text/x-ssa");
        FILE_CONTENT_TYPE.put(".asx" , "audio/x-ms-asx");
        FILE_CONTENT_TYPE.put(".atom" , "application/atom+xml");
        FILE_CONTENT_TYPE.put(".au" , "audio/basic");
        FILE_CONTENT_TYPE.put(".avi" , "video/x-msvideo");
        FILE_CONTENT_TYPE.put(".aw" , "application/x-applix-word");
        FILE_CONTENT_TYPE.put(".awb" , "audio/amr-wb");
        FILE_CONTENT_TYPE.put(".awk" , "application/x-awk");
        FILE_CONTENT_TYPE.put(".axa" , "audio/annodex");
        FILE_CONTENT_TYPE.put(".axv" , "video/annodex");
        FILE_CONTENT_TYPE.put(".bak" , "application/x-trash");
        FILE_CONTENT_TYPE.put(".bcpio" , "application/x-bcpio");
        FILE_CONTENT_TYPE.put(".bdf" , "application/x-font-bdf");
        FILE_CONTENT_TYPE.put(".bib" , "text/x-bibtex");
        FILE_CONTENT_TYPE.put(".bin" , "application/octet-stream");
        FILE_CONTENT_TYPE.put(".blend" , "application/x-blender");
        FILE_CONTENT_TYPE.put(".blender" , "application/x-blender");
        FILE_CONTENT_TYPE.put(".bmp" , "image/bmp");
        FILE_CONTENT_TYPE.put(".bz" , "application/x-bzip");
        FILE_CONTENT_TYPE.put(".bz2" , "application/x-bzip");
        FILE_CONTENT_TYPE.put(".c" , "text/x-csrc");
        FILE_CONTENT_TYPE.put(".c++" , "text/x-c++src");
        FILE_CONTENT_TYPE.put(".cab" , "application/vnd.ms-cab-compressed");
        FILE_CONTENT_TYPE.put(".cb7" , "application/x-cb7");
        FILE_CONTENT_TYPE.put(".cbr" , "application/x-cbr");
        FILE_CONTENT_TYPE.put(".cbt" , "application/x-cbt");
        FILE_CONTENT_TYPE.put(".cbz" , "application/x-cbz");
        FILE_CONTENT_TYPE.put(".cc" , "text/x-c++src");
        FILE_CONTENT_TYPE.put(".cdf" , "application/x-netcdf");
        FILE_CONTENT_TYPE.put(".cdr" , "application/vnd.corel-draw");
        FILE_CONTENT_TYPE.put(".cer" , "application/x-x509-ca-cert");
        FILE_CONTENT_TYPE.put(".cert" , "application/x-x509-ca-cert");
        FILE_CONTENT_TYPE.put(".cgm" , "image/cgm");
        FILE_CONTENT_TYPE.put(".chm" , "application/x-chm");
        FILE_CONTENT_TYPE.put(".chrt" , "application/x-kchart");
        FILE_CONTENT_TYPE.put(".class" , "application/x-java");
        FILE_CONTENT_TYPE.put(".cls" , "text/x-tex");
        FILE_CONTENT_TYPE.put(".cmake" , "text/x-cmake");
        FILE_CONTENT_TYPE.put(".cpio" , "application/x-cpio");
        FILE_CONTENT_TYPE.put(".cpio.gz" , "application/x-cpio-compressed");
        FILE_CONTENT_TYPE.put(".cpp" , "text/x-c++src");
        FILE_CONTENT_TYPE.put(".cr2" , "image/x-canon-cr2");
        FILE_CONTENT_TYPE.put(".crt" , "application/x-x509-ca-cert");
        FILE_CONTENT_TYPE.put(".crw" , "image/x-canon-crw");
        FILE_CONTENT_TYPE.put(".cs" , "text/x-csharp");
        FILE_CONTENT_TYPE.put(".csh" , "application/x-csh");
        FILE_CONTENT_TYPE.put(".css" , "text/css");
        FILE_CONTENT_TYPE.put(".cssl" , "text/css");
        FILE_CONTENT_TYPE.put(".csv" , "text/csv");
        FILE_CONTENT_TYPE.put(".cue" , "application/x-cue");
        FILE_CONTENT_TYPE.put(".cur" , "image/x-win-bitmap");
        FILE_CONTENT_TYPE.put(".cxx" , "text/x-c++src");
        FILE_CONTENT_TYPE.put(".d" , "text/x-dsrc");
        FILE_CONTENT_TYPE.put(".dar" , "application/x-dar");
        FILE_CONTENT_TYPE.put(".dbf" , "application/x-dbf");
        FILE_CONTENT_TYPE.put(".dc" , "application/x-dc-rom");
        FILE_CONTENT_TYPE.put(".dcl" , "text/x-dcl");
        FILE_CONTENT_TYPE.put(".dcm" , "application/dicom");
        FILE_CONTENT_TYPE.put(".dcr" , "image/x-kodak-dcr");
        FILE_CONTENT_TYPE.put(".dds" , "image/x-dds");
        FILE_CONTENT_TYPE.put(".deb" , "application/x-deb");
        FILE_CONTENT_TYPE.put(".der" , "application/x-x509-ca-cert");
        FILE_CONTENT_TYPE.put(".desktop" , "application/x-desktop");
        FILE_CONTENT_TYPE.put(".dia" , "application/x-dia-diagram");
        FILE_CONTENT_TYPE.put(".diff" , "text/x-patch");
        FILE_CONTENT_TYPE.put(".divx" , "video/x-msvideo");
        FILE_CONTENT_TYPE.put(".djv" , "image/vnd.djvu");
        FILE_CONTENT_TYPE.put(".djvu" , "image/vnd.djvu");
        FILE_CONTENT_TYPE.put(".dng" , "image/x-adobe-dng");
        FILE_CONTENT_TYPE.put(".doc" , "application/msword");
        FILE_CONTENT_TYPE.put(".docbook" , "application/docbook+xml");
        FILE_CONTENT_TYPE.put(".docm" , "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        FILE_CONTENT_TYPE.put(".docx" , "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        FILE_CONTENT_TYPE.put(".dot" , "text/vnd.graphviz");
        FILE_CONTENT_TYPE.put(".dsl" , "text/x-dsl");
        FILE_CONTENT_TYPE.put(".dtd" , "application/xml-dtd");
        FILE_CONTENT_TYPE.put(".dtx" , "text/x-tex");
        FILE_CONTENT_TYPE.put(".dv" , "video/dv");
        FILE_CONTENT_TYPE.put(".dvi" , "application/x-dvi");
        FILE_CONTENT_TYPE.put(".dvi.bz2" , "application/x-bzdvi");
        FILE_CONTENT_TYPE.put(".dvi.gz" , "application/x-gzdvi");
        FILE_CONTENT_TYPE.put(".dwg" , "image/vnd.dwg");
        FILE_CONTENT_TYPE.put(".dxf" , "image/vnd.dxf");
        FILE_CONTENT_TYPE.put(".e" , "text/x-eiffel");
        FILE_CONTENT_TYPE.put(".egon" , "application/x-egon");
        FILE_CONTENT_TYPE.put(".eif" , "text/x-eiffel");
        FILE_CONTENT_TYPE.put(".el" , "text/x-emacs-lisp");
        FILE_CONTENT_TYPE.put(".emf" , "image/x-emf");
        FILE_CONTENT_TYPE.put(".emp" , "application/vnd.emusic-emusic_package");
        FILE_CONTENT_TYPE.put(".ent" , "application/xml-external-parsed-entity");
        FILE_CONTENT_TYPE.put(".eps" , "image/x-eps");
        FILE_CONTENT_TYPE.put(".eps.bz2" , "image/x-bzeps");
        FILE_CONTENT_TYPE.put(".eps.gz" , "image/x-gzeps");
        FILE_CONTENT_TYPE.put(".epsf" , "image/x-eps");
        FILE_CONTENT_TYPE.put(".epsf.bz2" , "image/x-bzeps");
        FILE_CONTENT_TYPE.put(".epsf.gz" , "image/x-gzeps");
        FILE_CONTENT_TYPE.put(".epsi" , "image/x-eps");
        FILE_CONTENT_TYPE.put(".epsi.bz2" , "image/x-bzeps");
        FILE_CONTENT_TYPE.put(".epsi.gz" , "image/x-gzeps");
        FILE_CONTENT_TYPE.put(".epub" , "application/epub+zip");
        FILE_CONTENT_TYPE.put(".erl" , "text/x-erlang");
        FILE_CONTENT_TYPE.put(".es" , "application/ecmascript");
        FILE_CONTENT_TYPE.put(".etheme" , "application/x-e-theme");
        FILE_CONTENT_TYPE.put(".etx" , "text/x-setext");
        FILE_CONTENT_TYPE.put(".exe" , "application/x-ms-dos-executable");
        FILE_CONTENT_TYPE.put(".exr" , "image/x-exr");
        FILE_CONTENT_TYPE.put(".ez" , "application/andrew-inset");
        FILE_CONTENT_TYPE.put(".f" , "text/x-fortran");
        FILE_CONTENT_TYPE.put(".f90" , "text/x-fortran");
        FILE_CONTENT_TYPE.put(".f95" , "text/x-fortran");
        FILE_CONTENT_TYPE.put(".fb2" , "application/x-fictionbook+xml");
        FILE_CONTENT_TYPE.put(".fig" , "image/x-xfig");
        FILE_CONTENT_TYPE.put(".fits" , "image/fits");
        FILE_CONTENT_TYPE.put(".fl" , "application/x-fluid");
        FILE_CONTENT_TYPE.put(".flac" , "audio/x-flac");
        FILE_CONTENT_TYPE.put(".flc" , "video/x-flic");
        FILE_CONTENT_TYPE.put(".fli" , "video/x-flic");
        FILE_CONTENT_TYPE.put(".flv" , "video/x-flv");
        FILE_CONTENT_TYPE.put(".flw" , "application/x-kivio");
        FILE_CONTENT_TYPE.put(".fo" , "text/x-xslfo");
        FILE_CONTENT_TYPE.put(".for" , "text/x-fortran");
        FILE_CONTENT_TYPE.put(".g3" , "image/fax-g3");
        FILE_CONTENT_TYPE.put(".gb" , "application/x-gameboy-rom");
        FILE_CONTENT_TYPE.put(".gba" , "application/x-gba-rom");
        FILE_CONTENT_TYPE.put(".gcrd" , "text/directory");
        FILE_CONTENT_TYPE.put(".ged" , "application/x-gedcom");
        FILE_CONTENT_TYPE.put(".gedcom" , "application/x-gedcom");
        FILE_CONTENT_TYPE.put(".gen" , "application/x-genesis-rom");
        FILE_CONTENT_TYPE.put(".gf" , "application/x-tex-gf");
        FILE_CONTENT_TYPE.put(".gg" , "application/x-sms-rom");
        FILE_CONTENT_TYPE.put(".gif" , "image/gif");
        FILE_CONTENT_TYPE.put(".glade" , "application/x-glade");
        FILE_CONTENT_TYPE.put(".gmo" , "application/x-gettext-translation");
        FILE_CONTENT_TYPE.put(".gnc" , "application/x-gnucash");
        FILE_CONTENT_TYPE.put(".gnd" , "application/gnunet-directory");
        FILE_CONTENT_TYPE.put(".gnucash" , "application/x-gnucash");
        FILE_CONTENT_TYPE.put(".gnumeric" , "application/x-gnumeric");
        FILE_CONTENT_TYPE.put(".gnuplot" , "application/x-gnuplot");
        FILE_CONTENT_TYPE.put(".gp" , "application/x-gnuplot");
        FILE_CONTENT_TYPE.put(".gpg" , "application/pgp-encrypted");
        FILE_CONTENT_TYPE.put(".gplt" , "application/x-gnuplot");
        FILE_CONTENT_TYPE.put(".gra" , "application/x-graphite");
        FILE_CONTENT_TYPE.put(".gsf" , "application/x-font-type1");
        FILE_CONTENT_TYPE.put(".gsm" , "audio/x-gsm");
        FILE_CONTENT_TYPE.put(".gtar" , "application/x-tar");
        FILE_CONTENT_TYPE.put(".gv" , "text/vnd.graphviz");
        FILE_CONTENT_TYPE.put(".gvp" , "text/x-google-video-pointer");
        FILE_CONTENT_TYPE.put(".gz" , "application/x-gzip");
        FILE_CONTENT_TYPE.put(".h" , "text/x-chdr");
        FILE_CONTENT_TYPE.put(".h++" , "text/x-c++hdr");
        FILE_CONTENT_TYPE.put(".hdf" , "application/x-hdf");
        FILE_CONTENT_TYPE.put(".hh" , "text/x-c++hdr");
        FILE_CONTENT_TYPE.put(".hp" , "text/x-c++hdr");
        FILE_CONTENT_TYPE.put(".hpgl" , "application/vnd.hp-hpgl");
        FILE_CONTENT_TYPE.put(".hpp" , "text/x-c++hdr");
        FILE_CONTENT_TYPE.put(".hs" , "text/x-haskell");
        FILE_CONTENT_TYPE.put(".htm" , "text/html");
        FILE_CONTENT_TYPE.put(".html" , "text/html");
        FILE_CONTENT_TYPE.put(".hwp" , "application/x-hwp");
        FILE_CONTENT_TYPE.put(".hwt" , "application/x-hwt");
        FILE_CONTENT_TYPE.put(".hxx" , "text/x-c++hdr");
        FILE_CONTENT_TYPE.put(".ica" , "application/x-ica");
        FILE_CONTENT_TYPE.put(".icb" , "image/x-tga");
        FILE_CONTENT_TYPE.put(".icns" , "image/x-icns");
        FILE_CONTENT_TYPE.put(".ico" , "image/vnd.microsoft.icon");
        FILE_CONTENT_TYPE.put(".ics" , "text/calendar");
        FILE_CONTENT_TYPE.put(".idl" , "text/x-idl");
        FILE_CONTENT_TYPE.put(".ief" , "image/ief");
        FILE_CONTENT_TYPE.put(".iff" , "image/x-iff");
        FILE_CONTENT_TYPE.put(".ilbm" , "image/x-ilbm");
        FILE_CONTENT_TYPE.put(".ime" , "text/x-imelody");
        FILE_CONTENT_TYPE.put(".imy" , "text/x-imelody");
        FILE_CONTENT_TYPE.put(".ins" , "text/x-tex");
        FILE_CONTENT_TYPE.put(".iptables" , "text/x-iptables");
        FILE_CONTENT_TYPE.put(".iso" , "application/x-cd-image");
        FILE_CONTENT_TYPE.put(".iso9660" , "application/x-cd-image");
        FILE_CONTENT_TYPE.put(".it" , "audio/x-it");
        FILE_CONTENT_TYPE.put(".j2k" , "image/jp2");
        FILE_CONTENT_TYPE.put(".jad" , "text/vnd.sun.j2me.app-descriptor");
        FILE_CONTENT_TYPE.put(".jar" , "application/x-java-archive");
        FILE_CONTENT_TYPE.put(".java" , "text/x-java");
        FILE_CONTENT_TYPE.put(".jng" , "image/x-jng");
        FILE_CONTENT_TYPE.put(".jnlp" , "application/x-java-jnlp-file");
        FILE_CONTENT_TYPE.put(".jp2" , "image/jp2");
        FILE_CONTENT_TYPE.put(".jpc" , "image/jp2");
        FILE_CONTENT_TYPE.put(".jpe" , "image/jpeg");
        FILE_CONTENT_TYPE.put(".jpeg" , "image/jpeg");
        FILE_CONTENT_TYPE.put(".jpf" , "image/jp2");
        FILE_CONTENT_TYPE.put(".jpg" , "image/jpeg");
        FILE_CONTENT_TYPE.put(".jpr" , "application/x-jbuilder-project");
        FILE_CONTENT_TYPE.put(".jpx" , "image/jp2");
        FILE_CONTENT_TYPE.put(".js" , "application/javascript");
//        FILE_CONTENT_TYPE.put(".json" , "application/json");
//        FILE_CONTENT_TYPE.put(".jsonp" , "application/jsonp");
        FILE_CONTENT_TYPE.put(".k25" , "image/x-kodak-k25");
        FILE_CONTENT_TYPE.put(".kar" , "audio/midi");
        FILE_CONTENT_TYPE.put(".karbon" , "application/x-karbon");
        FILE_CONTENT_TYPE.put(".kdc" , "image/x-kodak-kdc");
        FILE_CONTENT_TYPE.put(".kdelnk" , "application/x-desktop");
        FILE_CONTENT_TYPE.put(".kexi" , "application/x-kexiproject-sqlite3");
        FILE_CONTENT_TYPE.put(".kexic" , "application/x-kexi-connectiondata");
        FILE_CONTENT_TYPE.put(".kexis" , "application/x-kexiproject-shortcut");
        FILE_CONTENT_TYPE.put(".kfo" , "application/x-kformula");
        FILE_CONTENT_TYPE.put(".kil" , "application/x-killustrator");
        FILE_CONTENT_TYPE.put(".kino" , "application/smil");
        FILE_CONTENT_TYPE.put(".kml" , "application/vnd.google-earth.kml+xml");
        FILE_CONTENT_TYPE.put(".kmz" , "application/vnd.google-earth.kmz");
        FILE_CONTENT_TYPE.put(".kon" , "application/x-kontour");
        FILE_CONTENT_TYPE.put(".kpm" , "application/x-kpovmodeler");
        FILE_CONTENT_TYPE.put(".kpr" , "application/x-kpresenter");
        FILE_CONTENT_TYPE.put(".kpt" , "application/x-kpresenter");
        FILE_CONTENT_TYPE.put(".kra" , "application/x-krita");
        FILE_CONTENT_TYPE.put(".ksp" , "application/x-kspread");
        FILE_CONTENT_TYPE.put(".kud" , "application/x-kugar");
        FILE_CONTENT_TYPE.put(".kwd" , "application/x-kword");
        FILE_CONTENT_TYPE.put(".kwt" , "application/x-kword");
        FILE_CONTENT_TYPE.put(".la" , "application/x-shared-library-la");
        FILE_CONTENT_TYPE.put(".latex" , "text/x-tex");
        FILE_CONTENT_TYPE.put(".ldif" , "text/x-ldif");
        FILE_CONTENT_TYPE.put(".lha" , "application/x-lha");
        FILE_CONTENT_TYPE.put(".lhs" , "text/x-literate-haskell");
        FILE_CONTENT_TYPE.put(".lhz" , "application/x-lhz");
        FILE_CONTENT_TYPE.put(".log" , "text/x-log");
        FILE_CONTENT_TYPE.put(".ltx" , "text/x-tex");
        FILE_CONTENT_TYPE.put(".lua" , "text/x-lua");
        FILE_CONTENT_TYPE.put(".lwo" , "image/x-lwo");
        FILE_CONTENT_TYPE.put(".lwob" , "image/x-lwo");
        FILE_CONTENT_TYPE.put(".lws" , "image/x-lws");
        FILE_CONTENT_TYPE.put(".ly" , "text/x-lilypond");
        FILE_CONTENT_TYPE.put(".lyx" , "application/x-lyx");
        FILE_CONTENT_TYPE.put(".lz" , "application/x-lzip");
        FILE_CONTENT_TYPE.put(".lzh" , "application/x-lha");
        FILE_CONTENT_TYPE.put(".lzma" , "application/x-lzma");
        FILE_CONTENT_TYPE.put(".lzo" , "application/x-lzop");
        FILE_CONTENT_TYPE.put(".m" , "text/x-matlab");
        FILE_CONTENT_TYPE.put(".m15" , "audio/x-mod");
        FILE_CONTENT_TYPE.put(".m2t" , "video/mpeg");
        FILE_CONTENT_TYPE.put(".m3u" , "audio/x-mpegurl");
        FILE_CONTENT_TYPE.put(".m3u8" , "audio/x-mpegurl");
        FILE_CONTENT_TYPE.put(".m4" , "application/x-m4");
        FILE_CONTENT_TYPE.put(".m4a" , "audio/mp4");
        FILE_CONTENT_TYPE.put(".m4b" , "audio/x-m4b");
        FILE_CONTENT_TYPE.put(".m4v" , "video/mp4");
        FILE_CONTENT_TYPE.put(".mab" , "application/x-markaby");
        FILE_CONTENT_TYPE.put(".man" , "application/x-troff-man");
        FILE_CONTENT_TYPE.put(".mbox" , "application/mbox");
        FILE_CONTENT_TYPE.put(".md" , "application/x-genesis-rom");
        FILE_CONTENT_TYPE.put(".mdb" , "application/vnd.ms-access");
        FILE_CONTENT_TYPE.put(".mdi" , "image/vnd.ms-modi");
        FILE_CONTENT_TYPE.put(".me" , "text/x-troff-me");
        FILE_CONTENT_TYPE.put(".med" , "audio/x-mod");
        FILE_CONTENT_TYPE.put(".metalink" , "application/metalink+xml");
        FILE_CONTENT_TYPE.put(".mgp" , "application/x-magicpoint");
        FILE_CONTENT_TYPE.put(".mid" , "audio/midi");
        FILE_CONTENT_TYPE.put(".midi" , "audio/midi");
        FILE_CONTENT_TYPE.put(".mif" , "application/x-mif");
        FILE_CONTENT_TYPE.put(".minipsf" , "audio/x-minipsf");
        FILE_CONTENT_TYPE.put(".mka" , "audio/x-matroska");
        FILE_CONTENT_TYPE.put(".mkv" , "video/x-matroska");
        FILE_CONTENT_TYPE.put(".ml" , "text/x-ocaml");
        FILE_CONTENT_TYPE.put(".mli" , "text/x-ocaml");
        FILE_CONTENT_TYPE.put(".mm" , "text/x-troff-mm");
        FILE_CONTENT_TYPE.put(".mmf" , "application/x-smaf");
        FILE_CONTENT_TYPE.put(".mml" , "text/mathml");
        FILE_CONTENT_TYPE.put(".mng" , "video/x-mng");
        FILE_CONTENT_TYPE.put(".mo" , "application/x-gettext-translation");
        FILE_CONTENT_TYPE.put(".mo3" , "audio/x-mo3");
        FILE_CONTENT_TYPE.put(".moc" , "text/x-moc");
        FILE_CONTENT_TYPE.put(".mod" , "audio/x-mod");
        FILE_CONTENT_TYPE.put(".mof" , "text/x-mof");
        FILE_CONTENT_TYPE.put(".moov" , "video/quicktime");
        FILE_CONTENT_TYPE.put(".mov" , "video/quicktime");
        FILE_CONTENT_TYPE.put(".movie" , "video/x-sgi-movie");
        FILE_CONTENT_TYPE.put(".mp+" , "audio/x-musepack");
        FILE_CONTENT_TYPE.put(".mp2" , "video/mpeg");
        FILE_CONTENT_TYPE.put(".mp3" , "audio/mpeg");
        FILE_CONTENT_TYPE.put(".mp4" , "video/mp4");
        FILE_CONTENT_TYPE.put(".mpc" , "audio/x-musepack");
        FILE_CONTENT_TYPE.put(".mpe" , "video/mpeg");
        FILE_CONTENT_TYPE.put(".mpeg" , "video/mpeg");
        FILE_CONTENT_TYPE.put(".mpg" , "video/mpeg");
        FILE_CONTENT_TYPE.put(".mpga" , "audio/mpeg");
        FILE_CONTENT_TYPE.put(".mpp" , "audio/x-musepack");
        FILE_CONTENT_TYPE.put(".mrl" , "text/x-mrml");
        FILE_CONTENT_TYPE.put(".mrml" , "text/x-mrml");
        FILE_CONTENT_TYPE.put(".mrw" , "image/x-minolta-mrw");
        FILE_CONTENT_TYPE.put(".ms" , "text/x-troff-ms");
        FILE_CONTENT_TYPE.put(".msi" , "application/x-msi");
        FILE_CONTENT_TYPE.put(".msod" , "image/x-msod");
        FILE_CONTENT_TYPE.put(".msx" , "application/x-msx-rom");
        FILE_CONTENT_TYPE.put(".mtm" , "audio/x-mod");
        FILE_CONTENT_TYPE.put(".mup" , "text/x-mup");
        FILE_CONTENT_TYPE.put(".mxf" , "application/mxf");
        FILE_CONTENT_TYPE.put(".n64" , "application/x-n64-rom");
        FILE_CONTENT_TYPE.put(".nb" , "application/mathematica");
        FILE_CONTENT_TYPE.put(".nc" , "application/x-netcdf");
        FILE_CONTENT_TYPE.put(".nds" , "application/x-nintendo-ds-rom");
        FILE_CONTENT_TYPE.put(".nef" , "image/x-nikon-nef");
        FILE_CONTENT_TYPE.put(".nes" , "application/x-nes-rom");
        FILE_CONTENT_TYPE.put(".nfo" , "text/x-nfo");
        FILE_CONTENT_TYPE.put(".not" , "text/x-mup");
        FILE_CONTENT_TYPE.put(".nsc" , "application/x-netshow-channel");
        FILE_CONTENT_TYPE.put(".nsv" , "video/x-nsv");
        FILE_CONTENT_TYPE.put(".o" , "application/x-object");
        FILE_CONTENT_TYPE.put(".obj" , "application/x-tgif");
        FILE_CONTENT_TYPE.put(".ocl" , "text/x-ocl");
        FILE_CONTENT_TYPE.put(".oda" , "application/oda");
        FILE_CONTENT_TYPE.put(".odb" , "application/vnd.oasis.opendocument.database");
        FILE_CONTENT_TYPE.put(".odc" , "application/vnd.oasis.opendocument.chart");
        FILE_CONTENT_TYPE.put(".odf" , "application/vnd.oasis.opendocument.formula");
        FILE_CONTENT_TYPE.put(".odg" , "application/vnd.oasis.opendocument.graphics");
        FILE_CONTENT_TYPE.put(".odi" , "application/vnd.oasis.opendocument.image");
        FILE_CONTENT_TYPE.put(".odm" , "application/vnd.oasis.opendocument.text-master");
        FILE_CONTENT_TYPE.put(".odp" , "application/vnd.oasis.opendocument.presentation");
        FILE_CONTENT_TYPE.put(".ods" , "application/vnd.oasis.opendocument.spreadsheet");
        FILE_CONTENT_TYPE.put(".odt" , "application/vnd.oasis.opendocument.text");
        FILE_CONTENT_TYPE.put(".oga" , "audio/ogg");
        FILE_CONTENT_TYPE.put(".ogg" , "video/x-theora+ogg");
        FILE_CONTENT_TYPE.put(".ogm" , "video/x-ogm+ogg");
        FILE_CONTENT_TYPE.put(".ogv" , "video/ogg");
        FILE_CONTENT_TYPE.put(".ogx" , "application/ogg");
        FILE_CONTENT_TYPE.put(".old" , "application/x-trash");
        FILE_CONTENT_TYPE.put(".oleo" , "application/x-oleo");
        FILE_CONTENT_TYPE.put(".opml" , "text/x-opml+xml");
        FILE_CONTENT_TYPE.put(".ora" , "image/openraster");
        FILE_CONTENT_TYPE.put(".orf" , "image/x-olympus-orf");
        FILE_CONTENT_TYPE.put(".otc" , "application/vnd.oasis.opendocument.chart-template");
        FILE_CONTENT_TYPE.put(".otf" , "application/x-font-otf");
        FILE_CONTENT_TYPE.put(".otg" , "application/vnd.oasis.opendocument.graphics-template");
        FILE_CONTENT_TYPE.put(".oth" , "application/vnd.oasis.opendocument.text-web");
        FILE_CONTENT_TYPE.put(".otp" , "application/vnd.oasis.opendocument.presentation-template");
        FILE_CONTENT_TYPE.put(".ots" , "application/vnd.oasis.opendocument.spreadsheet-template");
        FILE_CONTENT_TYPE.put(".ott" , "application/vnd.oasis.opendocument.text-template");
        FILE_CONTENT_TYPE.put(".owl" , "application/rdf+xml");
        FILE_CONTENT_TYPE.put(".oxt" , "application/vnd.openofficeorg.extension");
        FILE_CONTENT_TYPE.put(".p" , "text/x-pascal");
        FILE_CONTENT_TYPE.put(".p10" , "application/pkcs10");
        FILE_CONTENT_TYPE.put(".p12" , "application/x-pkcs12");
        FILE_CONTENT_TYPE.put(".p7b" , "application/x-pkcs7-certificates");
        FILE_CONTENT_TYPE.put(".p7s" , "application/pkcs7-signature");
        FILE_CONTENT_TYPE.put(".pack" , "application/x-java-pack200");
        FILE_CONTENT_TYPE.put(".pak" , "application/x-pak");
        FILE_CONTENT_TYPE.put(".par2" , "application/x-par2");
        FILE_CONTENT_TYPE.put(".pas" , "text/x-pascal");
        FILE_CONTENT_TYPE.put(".patch" , "text/x-patch");
        FILE_CONTENT_TYPE.put(".pbm" , "image/x-portable-bitmap");
        FILE_CONTENT_TYPE.put(".pcd" , "image/x-photo-cd");
        FILE_CONTENT_TYPE.put(".pcf" , "application/x-cisco-vpn-settings");
        FILE_CONTENT_TYPE.put(".pcf.gz" , "application/x-font-pcf");
        FILE_CONTENT_TYPE.put(".pcf.z" , "application/x-font-pcf");
        FILE_CONTENT_TYPE.put(".pcl" , "application/vnd.hp-pcl");
        FILE_CONTENT_TYPE.put(".pcx" , "image/x-pcx");
        FILE_CONTENT_TYPE.put(".pdb" , "chemical/x-pdb");
        FILE_CONTENT_TYPE.put(".pdc" , "application/x-aportisdoc");
        FILE_CONTENT_TYPE.put(".pdf" , "application/pdf");
        FILE_CONTENT_TYPE.put(".pdf.bz2" , "application/x-bzpdf");
        FILE_CONTENT_TYPE.put(".pdf.gz" , "application/x-gzpdf");
        FILE_CONTENT_TYPE.put(".pef" , "image/x-pentax-pef");
        FILE_CONTENT_TYPE.put(".pem" , "application/x-x509-ca-cert");
        FILE_CONTENT_TYPE.put(".perl" , "application/x-perl");
        FILE_CONTENT_TYPE.put(".pfa" , "application/x-font-type1");
        FILE_CONTENT_TYPE.put(".pfb" , "application/x-font-type1");
        FILE_CONTENT_TYPE.put(".pfx" , "application/x-pkcs12");
        FILE_CONTENT_TYPE.put(".pgm" , "image/x-portable-graymap");
        FILE_CONTENT_TYPE.put(".pgn" , "application/x-chess-pgn");
        FILE_CONTENT_TYPE.put(".pgp" , "application/pgp-encrypted");
        FILE_CONTENT_TYPE.put(".php" , "application/x-php");
        FILE_CONTENT_TYPE.put(".php3" , "application/x-php");
        FILE_CONTENT_TYPE.put(".php4" , "application/x-php");
        FILE_CONTENT_TYPE.put(".pict" , "image/x-pict");
        FILE_CONTENT_TYPE.put(".pict1" , "image/x-pict");
        FILE_CONTENT_TYPE.put(".pict2" , "image/x-pict");
        FILE_CONTENT_TYPE.put(".pickle" , "application/python-pickle");
        FILE_CONTENT_TYPE.put(".pk" , "application/x-tex-pk");
        FILE_CONTENT_TYPE.put(".pkipath" , "application/pkix-pkipath");
        FILE_CONTENT_TYPE.put(".pkr" , "application/pgp-keys");
        FILE_CONTENT_TYPE.put(".pl" , "application/x-perl");
        FILE_CONTENT_TYPE.put(".pla" , "audio/x-iriver-pla");
        FILE_CONTENT_TYPE.put(".pln" , "application/x-planperfect");
        FILE_CONTENT_TYPE.put(".pls" , "audio/x-scpls");
        FILE_CONTENT_TYPE.put(".pm" , "application/x-perl");
        FILE_CONTENT_TYPE.put(".png" , "image/png");
        FILE_CONTENT_TYPE.put(".pnm" , "image/x-portable-anymap");
        FILE_CONTENT_TYPE.put(".pntg" , "image/x-macpaint");
        FILE_CONTENT_TYPE.put(".po" , "text/x-gettext-translation");
        FILE_CONTENT_TYPE.put(".por" , "application/x-spss-por");
        FILE_CONTENT_TYPE.put(".pot" , "text/x-gettext-translation-template");
        FILE_CONTENT_TYPE.put(".ppm" , "image/x-portable-pixmap");
        FILE_CONTENT_TYPE.put(".pps" , "application/vnd.ms-powerpoint");
        FILE_CONTENT_TYPE.put(".ppt" , "application/vnd.ms-powerpoint");
        FILE_CONTENT_TYPE.put(".pptm" , "application/vnd.openxmlformats-officedocument.presentationml.presentation");
        FILE_CONTENT_TYPE.put(".pptx" , "application/vnd.openxmlformats-officedocument.presentationml.presentation");
        FILE_CONTENT_TYPE.put(".ppz" , "application/vnd.ms-powerpoint");
        FILE_CONTENT_TYPE.put(".prc" , "application/x-palm-database");
        FILE_CONTENT_TYPE.put(".ps" , "application/postscript");
        FILE_CONTENT_TYPE.put(".ps.bz2" , "application/x-bzpostscript");
        FILE_CONTENT_TYPE.put(".ps.gz" , "application/x-gzpostscript");
        FILE_CONTENT_TYPE.put(".psd" , "image/vnd.adobe.photoshop");
        FILE_CONTENT_TYPE.put(".psf" , "audio/x-psf");
        FILE_CONTENT_TYPE.put(".psf.gz" , "application/x-gz-font-linux-psf");
        FILE_CONTENT_TYPE.put(".psflib" , "audio/x-psflib");
        FILE_CONTENT_TYPE.put(".psid" , "audio/prs.sid");
        FILE_CONTENT_TYPE.put(".psw" , "application/x-pocket-word");
        FILE_CONTENT_TYPE.put(".pw" , "application/x-pw");
        FILE_CONTENT_TYPE.put(".py" , "text/x-python");
        FILE_CONTENT_TYPE.put(".pyc" , "application/x-python-bytecode");
        FILE_CONTENT_TYPE.put(".pyo" , "application/x-python-bytecode");
        FILE_CONTENT_TYPE.put(".qif" , "image/x-quicktime");
        FILE_CONTENT_TYPE.put(".qt" , "video/quicktime");
        FILE_CONTENT_TYPE.put(".qtif" , "image/x-quicktime");
        FILE_CONTENT_TYPE.put(".qtl" , "application/x-quicktime-media-link");
        FILE_CONTENT_TYPE.put(".qtvr" , "video/quicktime");
        FILE_CONTENT_TYPE.put(".ra" , "audio/vnd.rn-realaudio");
        FILE_CONTENT_TYPE.put(".raf" , "image/x-fuji-raf");
        FILE_CONTENT_TYPE.put(".ram" , "application/ram");
        FILE_CONTENT_TYPE.put(".rar" , "application/x-rar");
        FILE_CONTENT_TYPE.put(".ras" , "image/x-cmu-raster");
        FILE_CONTENT_TYPE.put(".raw" , "image/x-panasonic-raw");
        FILE_CONTENT_TYPE.put(".rax" , "audio/vnd.rn-realaudio");
        FILE_CONTENT_TYPE.put(".rb" , "application/x-ruby");
        FILE_CONTENT_TYPE.put(".rdf" , "application/rdf+xml");
        FILE_CONTENT_TYPE.put(".rdfs" , "application/rdf+xml");
        FILE_CONTENT_TYPE.put(".reg" , "text/x-ms-regedit");
        FILE_CONTENT_TYPE.put(".rej" , "application/x-reject");
        FILE_CONTENT_TYPE.put(".rgb" , "image/x-rgb");
        FILE_CONTENT_TYPE.put(".rle" , "image/rle");
        FILE_CONTENT_TYPE.put(".rm" , "application/vnd.rn-realmedia");
        FILE_CONTENT_TYPE.put(".rmj" , "application/vnd.rn-realmedia");
        FILE_CONTENT_TYPE.put(".rmm" , "application/vnd.rn-realmedia");
        FILE_CONTENT_TYPE.put(".rms" , "application/vnd.rn-realmedia");
        FILE_CONTENT_TYPE.put(".rmvb" , "application/vnd.rn-realmedia");
        FILE_CONTENT_TYPE.put(".rmx" , "application/vnd.rn-realmedia");
        FILE_CONTENT_TYPE.put(".roff" , "text/troff");
        FILE_CONTENT_TYPE.put(".rp" , "image/vnd.rn-realpix");
        FILE_CONTENT_TYPE.put(".rpm" , "application/x-rpm");
        FILE_CONTENT_TYPE.put(".rss" , "application/rss+xml");
        FILE_CONTENT_TYPE.put(".rt" , "text/vnd.rn-realtext");
        FILE_CONTENT_TYPE.put(".rtf" , "application/rtf");
        FILE_CONTENT_TYPE.put(".rtx" , "text/richtext");
        FILE_CONTENT_TYPE.put(".rv" , "video/vnd.rn-realvideo");
        FILE_CONTENT_TYPE.put(".rvx" , "video/vnd.rn-realvideo");
        FILE_CONTENT_TYPE.put(".s3m" , "audio/x-s3m");
        FILE_CONTENT_TYPE.put(".sam" , "application/x-amipro");
        FILE_CONTENT_TYPE.put(".sami" , "application/x-sami");
        FILE_CONTENT_TYPE.put(".sav" , "application/x-spss-sav");
        FILE_CONTENT_TYPE.put(".scm" , "text/x-scheme");
        FILE_CONTENT_TYPE.put(".sda" , "application/vnd.stardivision.draw");
        FILE_CONTENT_TYPE.put(".sdc" , "application/vnd.stardivision.calc");
        FILE_CONTENT_TYPE.put(".sdd" , "application/vnd.stardivision.impress");
        FILE_CONTENT_TYPE.put(".sdp" , "application/sdp");
        FILE_CONTENT_TYPE.put(".sds" , "application/vnd.stardivision.chart");
        FILE_CONTENT_TYPE.put(".sdw" , "application/vnd.stardivision.writer");
        FILE_CONTENT_TYPE.put(".sgf" , "application/x-go-sgf");
        FILE_CONTENT_TYPE.put(".sgi" , "image/x-sgi");
        FILE_CONTENT_TYPE.put(".sgl" , "application/vnd.stardivision.writer");
        FILE_CONTENT_TYPE.put(".sgm" , "text/sgml");
        FILE_CONTENT_TYPE.put(".sgml" , "text/sgml");
        FILE_CONTENT_TYPE.put(".sh" , "application/x-shellscript");
        FILE_CONTENT_TYPE.put(".shar" , "application/x-shar");
        FILE_CONTENT_TYPE.put(".shn" , "application/x-shorten");
        FILE_CONTENT_TYPE.put(".siag" , "application/x-siag");
        FILE_CONTENT_TYPE.put(".sid" , "audio/prs.sid");
        FILE_CONTENT_TYPE.put(".sik" , "application/x-trash");
        FILE_CONTENT_TYPE.put(".sis" , "application/vnd.symbian.install");
        FILE_CONTENT_TYPE.put(".sisx" , "x-epoc/x-sisx-app");
        FILE_CONTENT_TYPE.put(".sit" , "application/x-stuffit");
        FILE_CONTENT_TYPE.put(".siv" , "application/sieve");
        FILE_CONTENT_TYPE.put(".sk" , "image/x-skencil");
        FILE_CONTENT_TYPE.put(".sk1" , "image/x-skencil");
        FILE_CONTENT_TYPE.put(".skr" , "application/pgp-keys");
        FILE_CONTENT_TYPE.put(".slk" , "text/spreadsheet");
        FILE_CONTENT_TYPE.put(".smaf" , "application/x-smaf");
        FILE_CONTENT_TYPE.put(".smc" , "application/x-snes-rom");
        FILE_CONTENT_TYPE.put(".smd" , "application/vnd.stardivision.mail");
        FILE_CONTENT_TYPE.put(".smf" , "application/vnd.stardivision.math");
        FILE_CONTENT_TYPE.put(".smi" , "application/x-sami");
        FILE_CONTENT_TYPE.put(".smil" , "application/smil");
        FILE_CONTENT_TYPE.put(".sml" , "application/smil");
        FILE_CONTENT_TYPE.put(".sms" , "application/x-sms-rom");
        FILE_CONTENT_TYPE.put(".snd" , "audio/basic");
        FILE_CONTENT_TYPE.put(".so" , "application/x-sharedlib");
        FILE_CONTENT_TYPE.put(".spc" , "application/x-pkcs7-certificates");
        FILE_CONTENT_TYPE.put(".spd" , "application/x-font-speedo");
        FILE_CONTENT_TYPE.put(".spec" , "text/x-rpm-spec");
        FILE_CONTENT_TYPE.put(".spl" , "application/x-shockwave-flash");
        FILE_CONTENT_TYPE.put(".spx" , "audio/x-speex");
        FILE_CONTENT_TYPE.put(".sql" , "text/x-sql");
        FILE_CONTENT_TYPE.put(".sr2" , "image/x-sony-sr2");
        FILE_CONTENT_TYPE.put(".src" , "application/x-wais-source");
        FILE_CONTENT_TYPE.put(".srf" , "image/x-sony-srf");
        FILE_CONTENT_TYPE.put(".srt" , "application/x-subrip");
        FILE_CONTENT_TYPE.put(".ssa" , "text/x-ssa");
        FILE_CONTENT_TYPE.put(".stc" , "application/vnd.sun.xml.calc.template");
        FILE_CONTENT_TYPE.put(".std" , "application/vnd.sun.xml.draw.template");
        FILE_CONTENT_TYPE.put(".sti" , "application/vnd.sun.xml.impress.template");
        FILE_CONTENT_TYPE.put(".stm" , "audio/x-stm");
        FILE_CONTENT_TYPE.put(".stw" , "application/vnd.sun.xml.writer.template");
        FILE_CONTENT_TYPE.put(".sty" , "text/x-tex");
        FILE_CONTENT_TYPE.put(".sub" , "text/x-subviewer");
        FILE_CONTENT_TYPE.put(".sun" , "image/x-sun-raster");
        FILE_CONTENT_TYPE.put(".sv4cpio" , "application/x-sv4cpio");
        FILE_CONTENT_TYPE.put(".sv4crc" , "application/x-sv4crc");
        FILE_CONTENT_TYPE.put(".svg" , "image/svg+xml");
        FILE_CONTENT_TYPE.put(".svgz" , "image/svg+xml-compressed");
        FILE_CONTENT_TYPE.put(".swf" , "application/x-shockwave-flash");
        FILE_CONTENT_TYPE.put(".sxc" , "application/vnd.sun.xml.calc");
        FILE_CONTENT_TYPE.put(".sxd" , "application/vnd.sun.xml.draw");
        FILE_CONTENT_TYPE.put(".sxg" , "application/vnd.sun.xml.writer.global");
        FILE_CONTENT_TYPE.put(".sxi" , "application/vnd.sun.xml.impress");
        FILE_CONTENT_TYPE.put(".sxm" , "application/vnd.sun.xml.math");
        FILE_CONTENT_TYPE.put(".sxw" , "application/vnd.sun.xml.writer");
        FILE_CONTENT_TYPE.put(".sylk" , "text/spreadsheet");
        FILE_CONTENT_TYPE.put(".t" , "text/troff");
        FILE_CONTENT_TYPE.put(".t2t" , "text/x-txt2tags");
        FILE_CONTENT_TYPE.put(".tar" , "application/x-tar");
        FILE_CONTENT_TYPE.put(".tar.bz" , "application/x-bzip-compressed-tar");
        FILE_CONTENT_TYPE.put(".tar.bz2" , "application/x-bzip-compressed-tar");
        FILE_CONTENT_TYPE.put(".tar.gz" , "application/x-compressed-tar");
        FILE_CONTENT_TYPE.put(".tar.lzma" , "application/x-lzma-compressed-tar");
        FILE_CONTENT_TYPE.put(".tar.lzo" , "application/x-tzo");
        FILE_CONTENT_TYPE.put(".tar.xz" , "application/x-xz-compressed-tar");
        FILE_CONTENT_TYPE.put(".tar.z" , "application/x-tarz");
        FILE_CONTENT_TYPE.put(".tbz" , "application/x-bzip-compressed-tar");
        FILE_CONTENT_TYPE.put(".tbz2" , "application/x-bzip-compressed-tar");
        FILE_CONTENT_TYPE.put(".tcl" , "text/x-tcl");
        FILE_CONTENT_TYPE.put(".tex" , "text/x-tex");
        FILE_CONTENT_TYPE.put(".texi" , "text/x-texinfo");
        FILE_CONTENT_TYPE.put(".texinfo" , "text/x-texinfo");
        FILE_CONTENT_TYPE.put(".tga" , "image/x-tga");
        FILE_CONTENT_TYPE.put(".tgz" , "application/x-compressed-tar");
        FILE_CONTENT_TYPE.put(".theme" , "application/x-theme");
        FILE_CONTENT_TYPE.put(".themepack" , "application/x-windows-themepack");
        FILE_CONTENT_TYPE.put(".tif" , "image/tiff");
        FILE_CONTENT_TYPE.put(".tiff" , "image/tiff");
        FILE_CONTENT_TYPE.put(".tk" , "text/x-tcl");
        FILE_CONTENT_TYPE.put(".tlz" , "application/x-lzma-compressed-tar");
        FILE_CONTENT_TYPE.put(".tnef" , "application/vnd.ms-tnef");
        FILE_CONTENT_TYPE.put(".tnf" , "application/vnd.ms-tnef");
        FILE_CONTENT_TYPE.put(".toc" , "application/x-cdrdao-toc");
        FILE_CONTENT_TYPE.put(".torrent" , "application/x-bittorrent");
        FILE_CONTENT_TYPE.put(".tpic" , "image/x-tga");
        FILE_CONTENT_TYPE.put(".tr" , "text/troff");
        FILE_CONTENT_TYPE.put(".ts" , "application/x-linguist");
        FILE_CONTENT_TYPE.put(".tsv" , "text/tab-separated-values");
        FILE_CONTENT_TYPE.put(".tta" , "audio/x-tta");
        FILE_CONTENT_TYPE.put(".ttc" , "application/x-font-ttf");
        FILE_CONTENT_TYPE.put(".ttf" , "application/x-font-ttf");
        FILE_CONTENT_TYPE.put(".ttx" , "application/x-font-ttx");
        FILE_CONTENT_TYPE.put(".txt" , "text/plain");
        FILE_CONTENT_TYPE.put(".txz" , "application/x-xz-compressed-tar");
        FILE_CONTENT_TYPE.put(".tzo" , "application/x-tzo");
        FILE_CONTENT_TYPE.put(".ufraw" , "application/x-ufraw");
        FILE_CONTENT_TYPE.put(".ui" , "application/x-designer");
        FILE_CONTENT_TYPE.put(".uil" , "text/x-uil");
        FILE_CONTENT_TYPE.put(".ult" , "audio/x-mod");
        FILE_CONTENT_TYPE.put(".uni" , "audio/x-mod");
        FILE_CONTENT_TYPE.put(".uri" , "text/x-uri");
        FILE_CONTENT_TYPE.put(".url" , "text/x-uri");
        FILE_CONTENT_TYPE.put(".ustar" , "application/x-ustar");
        FILE_CONTENT_TYPE.put(".vala" , "text/x-vala");
        FILE_CONTENT_TYPE.put(".vapi" , "text/x-vala");
        FILE_CONTENT_TYPE.put(".vcf" , "text/directory");
        FILE_CONTENT_TYPE.put(".vcs" , "text/calendar");
        FILE_CONTENT_TYPE.put(".vct" , "text/directory");
        FILE_CONTENT_TYPE.put(".vda" , "image/x-tga");
        FILE_CONTENT_TYPE.put(".vhd" , "text/x-vhdl");
        FILE_CONTENT_TYPE.put(".vhdl" , "text/x-vhdl");
        FILE_CONTENT_TYPE.put(".viv" , "video/vivo");
        FILE_CONTENT_TYPE.put(".vivo" , "video/vivo");
        FILE_CONTENT_TYPE.put(".vlc" , "audio/x-mpegurl");
        FILE_CONTENT_TYPE.put(".vob" , "video/mpeg");
        FILE_CONTENT_TYPE.put(".voc" , "audio/x-voc");
        FILE_CONTENT_TYPE.put(".vor" , "application/vnd.stardivision.writer");
        FILE_CONTENT_TYPE.put(".vst" , "image/x-tga");
        FILE_CONTENT_TYPE.put(".wav" , "audio/x-wav");
        FILE_CONTENT_TYPE.put(".wax" , "audio/x-ms-asx");
        FILE_CONTENT_TYPE.put(".wb1" , "application/x-quattropro");
        FILE_CONTENT_TYPE.put(".wb2" , "application/x-quattropro");
        FILE_CONTENT_TYPE.put(".wb3" , "application/x-quattropro");
        FILE_CONTENT_TYPE.put(".wbmp" , "image/vnd.wap.wbmp");
        FILE_CONTENT_TYPE.put(".wcm" , "application/vnd.ms-works");
        FILE_CONTENT_TYPE.put(".wdb" , "application/vnd.ms-works");
        FILE_CONTENT_TYPE.put(".webm" , "video/webm");
        FILE_CONTENT_TYPE.put(".wk1" , "application/vnd.lotus-1-2-3");
        FILE_CONTENT_TYPE.put(".wk3" , "application/vnd.lotus-1-2-3");
        FILE_CONTENT_TYPE.put(".wk4" , "application/vnd.lotus-1-2-3");
        FILE_CONTENT_TYPE.put(".wks" , "application/vnd.ms-works");
        FILE_CONTENT_TYPE.put(".wma" , "audio/x-ms-wma");
        FILE_CONTENT_TYPE.put(".wmf" , "image/x-wmf");
        FILE_CONTENT_TYPE.put(".wml" , "text/vnd.wap.wml");
        FILE_CONTENT_TYPE.put(".wmls" , "text/vnd.wap.wmlscript");
        FILE_CONTENT_TYPE.put(".wmv" , "video/x-ms-wmv");
        FILE_CONTENT_TYPE.put(".wmx" , "audio/x-ms-asx");
        FILE_CONTENT_TYPE.put(".wp" , "application/vnd.wordperfect");
        FILE_CONTENT_TYPE.put(".wp4" , "application/vnd.wordperfect");
        FILE_CONTENT_TYPE.put(".wp5" , "application/vnd.wordperfect");
        FILE_CONTENT_TYPE.put(".wp6" , "application/vnd.wordperfect");
        FILE_CONTENT_TYPE.put(".wpd" , "application/vnd.wordperfect");
        FILE_CONTENT_TYPE.put(".wpg" , "application/x-wpg");
        FILE_CONTENT_TYPE.put(".wpl" , "application/vnd.ms-wpl");
        FILE_CONTENT_TYPE.put(".wpp" , "application/vnd.wordperfect");
        FILE_CONTENT_TYPE.put(".wps" , "application/vnd.ms-works");
        FILE_CONTENT_TYPE.put(".wri" , "application/x-mswrite");
        FILE_CONTENT_TYPE.put(".wrl" , "model/vrml");
        FILE_CONTENT_TYPE.put(".wv" , "audio/x-wavpack");
        FILE_CONTENT_TYPE.put(".wvc" , "audio/x-wavpack-correction");
        FILE_CONTENT_TYPE.put(".wvp" , "audio/x-wavpack");
        FILE_CONTENT_TYPE.put(".wvx" , "audio/x-ms-asx");
        FILE_CONTENT_TYPE.put(".x3f" , "image/x-sigma-x3f");
        FILE_CONTENT_TYPE.put(".xac" , "application/x-gnucash");
        FILE_CONTENT_TYPE.put(".xbel" , "application/x-xbel");
        FILE_CONTENT_TYPE.put(".xbl" , "application/xml");
        FILE_CONTENT_TYPE.put(".xbm" , "image/x-xbitmap");
        FILE_CONTENT_TYPE.put(".xcf" , "image/x-xcf");
        FILE_CONTENT_TYPE.put(".xcf.bz2" , "image/x-compressed-xcf");
        FILE_CONTENT_TYPE.put(".xcf.gz" , "image/x-compressed-xcf");
        FILE_CONTENT_TYPE.put(".xhtml" , "application/xhtml+xml");
        FILE_CONTENT_TYPE.put(".xi" , "audio/x-xi");
        FILE_CONTENT_TYPE.put(".xla" , "application/vnd.ms-excel");
        FILE_CONTENT_TYPE.put(".xlc" , "application/vnd.ms-excel");
        FILE_CONTENT_TYPE.put(".xld" , "application/vnd.ms-excel");
        FILE_CONTENT_TYPE.put(".xlf" , "application/x-xliff");
        FILE_CONTENT_TYPE.put(".xliff" , "application/x-xliff");
        FILE_CONTENT_TYPE.put(".xll" , "application/vnd.ms-excel");
        FILE_CONTENT_TYPE.put(".xlm" , "application/vnd.ms-excel");
        FILE_CONTENT_TYPE.put(".xls" , "application/vnd.ms-excel");
        FILE_CONTENT_TYPE.put(".xlsm" , "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        FILE_CONTENT_TYPE.put(".xlsx" , "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        FILE_CONTENT_TYPE.put(".xlt" , "application/vnd.ms-excel");
        FILE_CONTENT_TYPE.put(".xlw" , "application/vnd.ms-excel");
        FILE_CONTENT_TYPE.put(".xm" , "audio/x-xm");
        FILE_CONTENT_TYPE.put(".xmf" , "audio/x-xmf");
        FILE_CONTENT_TYPE.put(".xmi" , "text/x-xmi");
//        FILE_CONTENT_TYPE.put(".xml" , "application/xml");
        FILE_CONTENT_TYPE.put(".xpm" , "image/x-xpixmap");
        FILE_CONTENT_TYPE.put(".xps" , "application/vnd.ms-xpsdocument");
        FILE_CONTENT_TYPE.put(".xsl" , "application/xml");
        FILE_CONTENT_TYPE.put(".xslfo" , "text/x-xslfo");
        FILE_CONTENT_TYPE.put(".xslt" , "application/xml");
        FILE_CONTENT_TYPE.put(".xspf" , "application/xspf+xml");
        FILE_CONTENT_TYPE.put(".xul" , "application/vnd.mozilla.xul+xml");
        FILE_CONTENT_TYPE.put(".xwd" , "image/x-xwindowdump");
        FILE_CONTENT_TYPE.put(".xyz" , "chemical/x-pdb");
        FILE_CONTENT_TYPE.put(".xz" , "application/x-xz");
        FILE_CONTENT_TYPE.put(".w2p" , "application/w2p");
        FILE_CONTENT_TYPE.put(".z" , "application/x-compress");
        FILE_CONTENT_TYPE.put(".zabw" , "application/x-abiword");
        FILE_CONTENT_TYPE.put(".zip" , "application/zip");
        FILE_CONTENT_TYPE.put("form-data" , "multipart/form-data");
        FILE_CONTENT_TYPE.put("mixed" , "multipart/mixed");
    }
}
