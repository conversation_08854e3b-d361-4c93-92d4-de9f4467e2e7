# Tomcat
server:
  port: 8080
  ssl:
    key-store: classpath:server.jks
    key-password: Jsc@123
    key-store-type: JKS
    enabled: true
management:
  endpoints:
    enabled-by-default: false #关闭监控
    web:
      exposure:
        include: '*'
# spring配置
spring:
  redis:
    host: onecity-os-redis
#    host: *************
#    port: 30425
    port: 6379
    password: DONT(QkOmrw81EnDa0CDf9y1hywwU2sWxykwRkIHa8ZXhBvSUmcPtXUJerFvBv/77k4AxY40uLtxHEcjv86VWFXHNoPxPcGIgG6n10aMBOlsrWLwWZAjlOWr3N4JrG04kk+fPG+57s7x6Zu6algBVF8FWVJfIxkMWMIhU0IZGZ5njtok=)
  main:
    allow-circular-references: true
  cloud:
    gateway:
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 认证中心
        - id: onecity-os-auth
          uri: lb://onecity-os-auth
          predicates:
            - Path=/service-auth/**
          filters:
            # 验证码处理
            - CacheRequestFilter
            - ValidateCodeFilter
            - StripPrefix=1
        # 代码生成
        - id: onecity-os-gen
          uri: lb://onecity-os-gen
          predicates:
            - Path=/service-gen/**
          filters:
            - StripPrefix=1
        # 定时任务
        - id: onecity-os-job
          uri: lb://onecity-os-job
          predicates:
            - Path=/service-job/**
          filters:
            - StripPrefix=1
        # 系统模块
        - id: onecity-os-system
          uri: lb://onecity-os-system
          predicates:
            - Path=/service-system/**
          filters:
            - StripPrefix=1
        # 文件服务
        - id: onecity-os-file
          uri: lb://onecity-os-file
          predicates:
            - Path=/service-file/**
          filters:
            - StripPrefix=1
        # 门户服务
        - id: onecity-os-gov
          uri: lb://onecity-os-gov
          predicates:
            - Path=/service-gov/**
          filters:
            - StripPrefix=1
        # 流程引擎
        - id: onecity-os-activiti
          uri: lb://onecity-os-activiti
          predicates:
            - Path=/service-activiti/**
          filters:
            - StripPrefix=1
        # 大屏设计
        - id: onecity-os-onescreen
          uri: lb://onecity-os-onescreen
          predicates:
            - Path=/service-onescreen/**
          filters:
            - StripPrefix=1
        # 消息中心
        - id: onecity-os-message
          uri: lb://onecity-os-message
          predicates:
            - Path=/service-message/**
          filters:
            - StripPrefix=1
      # 通知公告
        - id: onecity-os-notice
          uri: lb://onecity-os-notice
          predicates:
          - Path=/service-information/**
          filters:
          - StripPrefix=1
      # 填报
        - id: onecity-os-backend-module-system
          uri: lb://onecity-os-backend-module-system
          predicates:
          - Path=/cockpit-system/**
          filters:
          - StripPrefix=1
      # 指标
        - id: onecity-os-cockpit-indicator
          uri: lb://onecity-os-cockpit-indicator
          predicates:
          - Path=/cockpit-indicator/**
          filters:
          - StripPrefix=1
      # 督办
        - id: onecity-os-supervise
          uri: lb://onecity-os-supervise
          predicates:
          - Path=/system-supervise/**
          filters:
          - StripPrefix=1
      # 圈阅批示
        - id: onecity-os-disposal
          uri: lb://onecity-os-disposal
          predicates:
          - Path=/system-disposal/**
          filters:
          - StripPrefix=1
      # 问卷调查
        - id: onecity-os-questionairesurvey
          uri: lb://onecity-os-questionairesurvey
          predicates:
          - Path=/service-questionairesurvey/**
          filters:
          - StripPrefix=1
      # 后台管理
        - id: onecity-os-management
          uri: lb://onecity-os-management
          predicates:
          - Path=/service-management/**
          filters:
          - StripPrefix=1
      # 领导交办
        - id: onecity-os-assigned
          uri: lb://onecity-os-assigned
          predicates:
          - Path=/system-assigned/**
          filters:
          - StripPrefix=1

# 返回数据加密开关
response:
  encryptFlag: 1

# 安全配置
security:
  # 验证码
  captcha:
    enabled: true
    type: math
  # 防止XSS攻击
  xss:
    enabled: true
    excludeUrls:
      - /system/notice
  # 不校验白名单
  ignore:
    whites:
      - /service-auth/logout
      - /service-auth/login
      - /service-auth/oneCityLogin
      - /service-auth/register
      - /*/v2/api-docs
      - /csrf
      - /gov/**
      - /service-activiti/**
      - /service-onescreen/**
      - /service-information/**
      - /system-supervise/**
      - /cockpit-system/**
      - /cockpit-indicator/**
      - /service-message/**
      - /api-file/**
      - /system-portal/**
      - /home-portal/**
      - /app-portal/**
      - /system-calculate/**
      - /service-notice/**
      - /system-disposal/**
      - /service-questionairesurvey/**
      - /service-assigned/**

  check:
    sessionPaths:
      - /api-file/**
  response:
    whites:
      - /service-notice/notice/downloadMatterFile
      - /app-notice/app/notice/downloadMatterFile
      - /service-inform/reportCheck/downloadInstruction