package com.onecity.os.assigned.constant;/**
 * Description
 *
 * <AUTHOR> 2021/11/9
 */

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Title UtilConstants
 * @Description 通用常量
 * @date 2021/11/9 11:03
 */

public interface UtilConstants {

    /**
     * 通用字符
     */
    interface Symbol {
        String SIGH = "!";
        String AT = "@";
        String WELL = "#";
        String DOLLAR = "$";
        String RMB = "￥";
        String SPACE = " ";
        String LB = System.getProperty("line.separator");
        String PERCENTAGE = "%";
        String AND = "&";
        String STAR = "*";
        String MIDDLE_LINE = "-";
        String LOWER_LINE = "_";
        String EQUAL = "=";
        String PLUS = "+";
        String COLON = ":";
        String SEMICOLON = ";";
        String COMMA = ",";
        String POINT = ".";
        String SLASH = "/";
        String VERTICAL_BAR = "|";
        String DOUBLE_SLASH = "//";
        String BACKSLASH = "\\";
        String QUESTION = "?";
        String LEFT_BIG_BRACE = "{";
        String RIGHT_BIG_BRACE = "}";
        String LEFT_MIDDLE_BRACE = "[";
        String RIGHT_MIDDLE_BRACE = "]";
        String BACKQUOTE = "`";
        String RU_CURRENCY_SYMBOL = "₽";
        String UFEFF = "\uFEFF";
        String YEAR = "年";
        String MONTH = "月";
        String DAY = "日";
    }

    /**
     * 文件格式
     */
    interface FileType {
        //文件格式
        List<String> XLS = Arrays.asList("xls","xlsx");
        //PDF
        List<String> PDF = Collections.singletonList("pdf");
    }

    /**
     * 状态统计
     */
    interface State {
        //草稿
        String DRAFT = "DRAFT";
        //待处理
        String PENDING = "PENDING";
        //发布
        String PUBLISH = "PUBLISH";
        //编辑
        String EDIT = "EDIT";
        //催办
        String URGE = "URGE";
        //转发
        String FORWARD = "FORWARD";
        //反馈
        String FEEDBACK = "FEEDBACK";
        //撤回
        String REVOKE = "REVOKE";
        //完结
        String COMPLETE = "COMPLETE";
        //删除
        String DELETED = "DELETED";
    }

    /**
     * 数据来源
     */
    interface Source {
        //PC
        String PC = "PC";
        //APP
        String APP = "APP";
    }

    /**
     * 通用标记
     */
    interface StateFlag {
        //PC
        String TRUE = "1";
        //APP
        String FALSE = "0";
    }

    /**
     * 消息级别 1-红色，2-黄色，3-绿色
     */
    interface MsgFlag {
        //红色
        String RD = "1";
        //黄色
        String YE = "2";
        //绿色
        String GN = "3";
    }

    /**
     * 通用标记
     */
    interface ActionType {
        //新建
        String NEW = "NEW";
        //转发
        String FORWARD = "FORWARD";
        //反馈
        String FEEDBACK = "FEEDBACK";
    }

    /**
     * 通用标记
     */
    interface UserStatus {
        //创建人
        String CREATOR = "CREATOR";
        //接受人
        String RECEIVER = "RECEIVER";
    }
}

