package com.onecity.os.assigned.controller;

import com.onecity.os.assigned.modules.assigned.entity.LeaderAssigned;
import com.onecity.os.assigned.modules.assigned.vo.ActionHistoryVo;
import com.onecity.os.assigned.modules.assigned.vo.ActionVo;
import com.onecity.os.assigned.modules.assigned.vo.OperateVo;
import com.onecity.os.assigned.service.AssignedService;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 领导交办api入口
 *
 * @Author: zack
 * @Date: 2024/3/6 16:42
 */
@Slf4j
@RestController
@RequestMapping("/assigned")
@Api(tags = "领导交办api入口")
public class AssignedController extends BaseController {

    @Resource
    private AssignedService assignedService;

    /**
     * 领导交办信息新建
     *
     * @param assigned
     * @return
     */
    @PostMapping("/saveData")
    @ApiOperation("新建")
    public BaseResult<LeaderAssigned> saveData(@RequestBody @Valid LeaderAssigned assigned) {
        return assignedService.saveData(assigned);
    }

    /**
     * 领导交办信息发起人查询接口
     *
     * @param userId
     * @return
     */
    @GetMapping("/list/creator")
    @ApiOperation("领导交办信息发起人查询接口")
    public TableDataInfo queryPageListByCreator(@RequestParam(name = "userId") String userId) {
        startPage();
        List<LeaderAssigned> leaderAssignedList = assignedService.queryByCreator(userId);
        return getDataTable(leaderAssignedList);
    }

    /**
     * 领导交办信息发起人查询接口
     *
     * @param userId
     * @return
     */
    @GetMapping("/list/receiver")
    @ApiOperation("领导交办信息接受人查询接口")
    public TableDataInfo queryPageListByReceiver(@RequestParam(name = "userId") String userId) {
        startPage();
        List<ActionVo> actionList = assignedService.queryByReceiver(userId);
        return getDataTable(actionList);
    }


    /**
     * 领导交办信息接受人未处理数据查询
     *
     * @param userId
     * @return
     */
    @GetMapping("/queryReceiverTotal")
    @ApiOperation("新建/编辑")
    public BaseResult<Integer> queryReceiverTotal(@RequestParam(name = "userId") String userId) {
        return assignedService.queryReceiverTotal(userId);
    }


    /**
     * 领导交办信息详情查询
     *
     * @param
     * @return
     */
    @GetMapping("/queryAssignedDetail")
    @ApiOperation("领导交办信息详情查询")
    public BaseResult<LeaderAssigned> queryAssignedDetail(@RequestParam(name = "assignedId") String assignedId,
                                                          @RequestParam(name = "actionId") String actionId,
                                                          @RequestParam(name = "userStatus") String userStatus) {
        LeaderAssigned assigned = assignedService.queryAssignedDetail(assignedId, actionId, userStatus);
        return BaseResult.ok(assigned);
    }

    /**
     * 根据领导交办id查询接受人操作记录
     *
     * @param id
     * @return
     */
    @GetMapping("/getActionListById")
    @ApiOperation("根据领导交办id查询接受人操作记录")
    public BaseResult<List<ActionHistoryVo>> getActionListById(@RequestParam(name = "id") String id) {
        List<ActionHistoryVo> actionVos = assignedService.getActionListById(id);
        return BaseResult.ok(actionVos);
    }

    /**
     * 反馈
     *
     * @param operateVo
     * @return
     */
    @PostMapping("/feedback")
    @ApiOperation("领导交办反馈")
    public BaseResult<String> feedback(@RequestBody @Valid OperateVo operateVo) {
        return assignedService.actionFeedback(operateVo.getActionId(), operateVo.getUserId(), operateVo.getContent());
    }

    /**
     * 转发
     *
     * @param operateVo
     * @return
     */
    @PostMapping("/forward")
    @ApiOperation("领导交办转发")
    public BaseResult<String> forward(@RequestBody @Valid OperateVo operateVo) {
        return assignedService.actionForward(operateVo);
    }


    /**
     * PC端查询
     * @param time
     * @param creatorName
     * @return
     */
    @GetMapping("/pc/queryByParam")
    @ApiOperation("PC端查询")
    public TableDataInfo queryByParam(@RequestParam(name = "time") String time,
                                      @RequestParam(name = "creatorName") String creatorName) {
        startPage();
        List<LeaderAssigned> assignedVos = assignedService.queryByParam(time, creatorName);
        return getDataTable(assignedVos);
    }
}
