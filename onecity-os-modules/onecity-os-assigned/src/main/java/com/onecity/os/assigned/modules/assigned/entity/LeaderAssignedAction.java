package com.onecity.os.assigned.modules.assigned.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 领导交办接收人及操作表
 *
 * @Author: zack
 * @Date: 2024/3/6 16:06
 */
@Data
@Table(name = "leader_assigned_action")
public class LeaderAssignedAction implements Serializable {

    /**
     * 主键自增
     **/
    @Id
    @GeneratedValue
    private String id;

    /**
     * 领导交办id
     */
    @Column(name = "assigned_id")
    @ApiModelProperty(value = "领导交办id")
    private String assignedId;

    /**
     * 接收人id
     */
    @Column(name = "receiver")
    @ApiModelProperty(value = "接收人id")
    private String receiver;

    /**
     * 接收名称
     */
    @Column(name = "receiver_name")
    @ApiModelProperty(value = "接收人名称")
    private String receiverName;

    /**
     * 创建人id
     */
    @Column(name = "creator")
    @ApiModelProperty(value = "创建人id")
    @NotNull(message = "创建人信息不可为空")
    private String creator;

    /**
     * 创建人名称
     */
    @Column(name = "creator_name")
    @ApiModelProperty(value = "创建人名称")
    @NotNull(message = "创建人名称不可为空")
    private String creatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", example = "1990-01-01 00:00:00")
    private java.util.Date createTime;

    /**
     * 修改人id
     */
    @Column(name = "updater")
    @ApiModelProperty(value = "修改人id")
    private String updater;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间", example = "1990-01-01 00:00:00")
    private java.util.Date updateTime;

    /**
     * 操作类型
     */
    @Column(name = "action_type")
    @ApiModelProperty(value = "操作类型", notes = "NEW = 新建； FORWARD = 转发； FEEDBACK = 反馈;", example = "PENDING")
    private String actionType;

    /**
     * 转发来源，接受人行id
     */
    @Column(name = "forward_from")
    @ApiModelProperty(value = " 转发来源，接受人行id")
    private String forwardFrom;

    /**
     * 转发给某人(名称)
     */
    @Column(name = "forward_to")
    @ApiModelProperty(value = "转发给某人(名称),多个人,拼接，用于前端展示")
    private String forwardTo;

    /**
     * 操作内容
     */
    @Column(name = "action_content")
    @ApiModelProperty(value = "操作内容：反馈内容/转发备注")
    private String actionContent;

    /**
     * 是否删除 0未删除 1已删除
     */
    @Column(name = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private int isDelete;

    /**
     * 备用字段
     */
    @Column(name = "backup")
    private String backup;

}
