package com.onecity.os.assigned.modules.assigned.feign;

import com.onecity.os.assigned.modules.assigned.dto.RemindInfoDto;
import com.onecity.os.assigned.modules.assigned.feign.fallback.MessageFeignFallbackFactory;
import com.onecity.os.common.core.domain.BaseResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2022/5/30 15:56
 */
@FeignClient(name = "onecity-os-message", fallbackFactory = MessageFeignFallbackFactory.class, decode404 = true)
public interface MessageFeignService {

    /**
     * add message
     * @param remindInfo
     * @return
     */
    @PostMapping("/remind/addMessage")
    BaseResult addMsg(@RequestBody List<RemindInfoDto> remindInfo);

    /**
     * delete message
     * @param serviceId
     * @param serviceType
     * @param appUserId
     * @param userName
     * @return
     */
    @GetMapping("/remind/deleteMessageByServiceAndUserId")
    BaseResult deleteMessageByServiceAndUserId(@RequestParam(name = "serviceId") String serviceId,
                                               @RequestParam(name = "serviceType") String serviceType,
                                               @RequestParam(name = "appUserId") String appUserId,
                                               @RequestParam(name = "userName") String userName);
}
