package com.onecity.os.assigned.modules.assigned.feign.fallback;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.assigned.modules.assigned.dto.RemindInfoDto;
import com.onecity.os.assigned.modules.assigned.feign.MessageFeignService;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2022/5/31 10:04
 */
@Slf4j
@Component
public class MessageFeignFallbackFactory implements FallbackFactory<MessageFeignService> {
    @Override
    public MessageFeignService create(Throwable cause) {
        return new MessageFeignService() {
            @Override
            public BaseResult addMsg(List<RemindInfoDto> remindInfo) {
                log.info("addMsg  : {} ", JSONObject.toJSONString(remindInfo));
                log.error("通知公告获取异常！");
                return BaseResult.fail("通知公告获取异常");
            }
            @Override
            public BaseResult deleteMessageByServiceAndUserId(String serviceId, String serviceType, String appUserId, String userName) {
                log.info("deleteMessageByServiceAndUserId : {} {} {}", JSONObject.toJSONString(serviceId), JSONObject.toJSONString(serviceType), JSONObject.toJSONString(appUserId));
                log.error("删除消息异常！");
                return BaseResult.fail("删除消息异常");
            }
        };
    }
}
