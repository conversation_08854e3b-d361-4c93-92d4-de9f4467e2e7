package com.onecity.os.assigned.modules.assigned.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Author: zack
 * @Date: 2024/3/8 11:24
 */
@Data
public class ActionHistoryVo {

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    private String userName;

    /**
     * 用户操作类型
     */
    @ApiModelProperty(value = "用户操作类型")
    private String type;

    /**
     * 用户操作备注等内容
     */
    @ApiModelProperty(value = "用户操作备注等内容")
    private String content;

    /**
     * 转发目标人名称
     */
    @ApiModelProperty(value = "转发目标人名称")
    private String forwardTo;

    /**
     * 操作时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "操作时间", example = "1990-01-01 00:00:00")
    private java.util.Date operateTime;
}
