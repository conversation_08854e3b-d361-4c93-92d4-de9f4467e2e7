package com.onecity.os.assigned.modules.assigned.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.onecity.os.assigned.modules.assigned.entity.LeaderAssignedAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.List;

/**
 * 领导交办表
 *
 * @Author: zack
 * @Date: 2024/3/6 15:52
 */
@Data
public class ActionVo implements Serializable {

    /**
     * 领导交办id
     */
    @ApiModelProperty(value = "领导交办id")
    private String assignedId;

    /**
     * 接受人行数据id
     */
    @ApiModelProperty(value = "接受人行数据id")
    private String actionId;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private String creator;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
    private String creatorName;

    /**
     * 修改人id
     */
    @ApiModelProperty(value = "修改人id")
    private String updater;

    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称")
    private String updaterName;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", example = "1990-01-01 00:00:00")
    private java.util.Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间", example = "1990-01-01 00:00:00")
    private java.util.Date updateTime;

    /**
     * 圈批图片url
     */
    @ApiModelProperty(value = "圈批图片")
    private String assignedPic;

    /**
     * 交办内容
     */
    @ApiModelProperty(value = "交办内容")
    private String contentTxt;

    /**
     * 交办图片url
     */
    @ApiModelProperty(value = "交办内容")
    private String contentPic;


    /**
     * 接收人id
     */
    @ApiModelProperty(value = "接收人id")
    private String receiver;

    /**
     * 接收名称
     */
    @ApiModelProperty(value = "接收人名称")
    private String receiverName;

    /**
     * 接受人状态状态
     */
    @ApiModelProperty(value = "接受人状态状态")
    private String actionType;

    /**
     * 接受人状态状态
     */
    @ApiModelProperty(value = "接受人状态状态")
    private String assignedStatus;

    /**
     * 标题（圈批发起人+发起的交办批示）
     */
    private String title;

    /**
     * 接受人信息
     */
    @ApiModelProperty(value = "接受人信息（创建，编辑时该字段为必传字段）")
    private List<LeaderAssignedAction> receiverList;
}
