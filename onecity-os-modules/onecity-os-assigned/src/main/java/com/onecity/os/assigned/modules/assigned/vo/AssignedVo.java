package com.onecity.os.assigned.modules.assigned.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.onecity.os.assigned.modules.assigned.entity.LeaderAssignedAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: zack
 * @Date: 2024/3/8 10:11
 */
@Data
public class AssignedVo {

    /**
     * 主键自增
     **/
    @Id
    @GeneratedValue
    private String id;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @NotNull(message = "创建人信息不可为空")
    private String creator;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
    private String creatorName;

    /**
     * 修改人id
     */
    @ApiModelProperty(value = "修改人id")
    private String updater;

    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称")
    private String updaterName;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", example = "1990-01-01 00:00:00")
    private java.util.Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间", example = "1990-01-01 00:00:00")
    private java.util.Date updateTime;

    /**
     * 圈批图片url
     */
    @ApiModelProperty(value = "圈批图片")
    private String assignedPic;

    /**
     * 交办内容
     */
    @ApiModelProperty(value = "交办内容")
    private String contentTxt;

    /**
     * 交办图片url
     */
    @ApiModelProperty(value = "交办内容")
    private String contentPic;


    /**
     * 接受人信息
     */
    @Transient
    @ApiModelProperty(value = "接受人信息（创建，编辑时该字段为必传字段）")
    private List<LeaderAssignedAction> receiverList;

    /**
     * 前端获取到的当前时间
     */
    @Transient
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "前端获取到的当前时间", example = "1990-01-01 00:00:00")
    private java.util.Date localTime;

    /**
     * 交办状态（状态中接收人都没有反馈则状态为处理中，有一个反馈则改为已反馈，接收人进行转发则不算反馈）
     */
    private String assignedStatus;

    /**
     * 标题（圈批发起人+发起的交办批示）
     */
    private String title;
}
