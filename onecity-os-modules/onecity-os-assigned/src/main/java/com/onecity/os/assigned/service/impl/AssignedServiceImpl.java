package com.onecity.os.assigned.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.assigned.constant.UtilConstants;
import com.onecity.os.assigned.modules.assigned.dto.RemindInfoDto;
import com.onecity.os.assigned.modules.assigned.entity.LeaderAssigned;
import com.onecity.os.assigned.modules.assigned.entity.LeaderAssignedAction;
import com.onecity.os.assigned.modules.assigned.feign.MessageFeignService;
import com.onecity.os.assigned.modules.assigned.mapper.AssignedActionMapper;
import com.onecity.os.assigned.modules.assigned.mapper.AssignedMapper;
import com.onecity.os.assigned.modules.assigned.vo.ActionHistoryVo;
import com.onecity.os.assigned.modules.assigned.vo.ActionVo;
import com.onecity.os.assigned.modules.assigned.vo.OperateVo;
import com.onecity.os.assigned.service.AssignedService;
import com.onecity.os.assigned.utils.DateUtils;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.system.api.domain.SysUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: zack
 * @Date: 2024/3/6 16:53
 */
@Slf4j
@Service("assignedService")
public class AssignedServiceImpl implements AssignedService {

    @Value("${app.baseMessageUrl}")
    private String baseMessageUrl;

    @Value("${app.assignedMessageUrl}")
    private String assignedMessageUrl;

    @Resource
    private AssignedMapper assignedMapper;

    @Resource
    private AssignedActionMapper assignedActionMapper;

    @Resource
    private MessageFeignService messageFeignService;

    @Override
    public BaseResult<LeaderAssigned> saveData(LeaderAssigned request) {
        //当前时间统一采用前端传输时间，服务器时间暂时无法对应，后续如有疑问再做调整
        Date nowDate = request.getLocalTime();
        request.setId(UUID.randomUUID().toString().replaceAll("-", ""));
        //当前用户信息
        SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
        request.setCreator(String.valueOf(sysUser.getUserId()));
        request.setCreatorName(sysUser.getNickName());
        request.setUpdater(request.getCreator());
        request.setUpdaterName(request.getCreatorName());
        request.setCreateTime(nowDate);
        request.setUpdateTime(nowDate);
        request.setIsDelete(0);
        assignedMapper.insertAssigned(request);
        //接受人行保存
        if (!CollectionUtils.isEmpty(request.getReceiverList())) {
            request.getReceiverList().forEach(receiver -> {
                receiver.setId(UUID.randomUUID().toString().replaceAll("-", ""));
                receiver.setAssignedId(request.getId());
                receiver.setCreator(request.getCreator());
                receiver.setCreatorName(request.getCreatorName());
                receiver.setCreateTime(nowDate);
                receiver.setActionType(UtilConstants.ActionType.NEW);
                receiver.setIsDelete(0);
                receiver.setUpdateTime(nowDate);
                receiver.setUpdater(request.getCreator());
                assignedActionMapper.insertAction(receiver);
                //接受人收到的提示消息
                try {
                    //【领导交办 您收到一条新的领导交办信息，请及时处理。
                    Boolean msgResult = sendMsg(receiver);
                    log.info("addMsg success : {} ", JSONObject.toJSONString(msgResult));
                } catch (Exception e) {
                    log.info(" addMsg error : {}", e.getMessage());
                }
            });
        }
        return BaseResult.ok();
    }

    @Override
    public List<LeaderAssigned> queryByCreator(String userId) {
        List<LeaderAssigned> assignedList = assignedMapper.queryByCreator(userId, null, null);
        //查询接受人行
        assignedList.forEach(assigned -> {
            List<LeaderAssignedAction> actions = assignedActionMapper.queryByAssignedId(assigned.getId());
            //判断状态，交办状态（状态中接收人都没有反馈则状态为处理中，有一个反馈则改为已反馈，接收人进行转发则不算反馈）
            //判断是否存在"反馈"接受人数据
            LeaderAssignedAction feedback = actions.stream()
                    .filter(ac -> ac.getActionType().equals(UtilConstants.ActionType.FEEDBACK))
                    .findFirst().orElse(null);
            if (null != feedback) {
                assigned.setAssignedStatus(UtilConstants.ActionType.FEEDBACK);
            } else {
                assigned.setAssignedStatus(UtilConstants.ActionType.NEW);
            }
            //标题（圈批发起人+发起的交办批示）
            assigned.setTitle(assigned.getCreatorName() + "发起的交办批示");
        });
        return assignedList;
    }

    @Override
    public List<ActionVo> queryByReceiver(String userId) {
        List<ActionVo> actionVos = assignedMapper.queryByReceiver(userId);
        actionVos.forEach(action -> {
            //标题（圈批发起人+发起的交办批示）
            action.setTitle(action.getCreatorName() + "发起的交办批示");
            action.setAssignedStatus(action.getActionType());
        });
        return actionVos;
    }

    @Override
    public BaseResult<Integer> queryReceiverTotal(String receiver) {
        Integer count = assignedActionMapper.queryReceiverTotal(receiver);
        return BaseResult.ok(count);
    }

    @Override
    public LeaderAssigned queryAssignedDetail(String assignedId, String actionId, String userStatus) {
        LeaderAssigned assigned = assignedMapper.queryById(assignedId);
        log.info("test for queryAssignedDetail assigned : {}", JSONObject.toJSONString(assigned));
        //查询接受人行
        List<LeaderAssignedAction> actions = assignedActionMapper.queryByAssignedId(assigned.getId());
        //接受人数据
        assigned.setReceiverList(actions);
        //创建人查询
        if (userStatus.equals(UtilConstants.UserStatus.CREATOR)) {
            //判断状态，交办状态（状态中接收人都没有反馈则状态为处理中，有一个反馈则改为已反馈，接收人进行转发则不算反馈）
            //判断是否存在"反馈"接受人数据
            LeaderAssignedAction feedback = actions.stream()
                    .filter(ac -> ac.getActionType().equals(UtilConstants.ActionType.FEEDBACK))
                    .findFirst().orElse(null);
            if (null != feedback) {
                assigned.setAssignedStatus(UtilConstants.ActionType.FEEDBACK);
            } else {
                assigned.setAssignedStatus(UtilConstants.ActionType.NEW);
            }
        }
        //接收人查询
        if (userStatus.equals(UtilConstants.UserStatus.RECEIVER)) {
            //匹配接收人行数据，将接受人行状态返回
            actions.stream().filter(ac -> ac.getId().equals(actionId)).findFirst()
                    .ifPresent(receiverAction -> assigned.setAssignedStatus(receiverAction.getActionType()));
        }

        //标题（圈批发起人+发起的交办批示）
        assigned.setTitle(assigned.getCreatorName() + "发起的交办批示");
        return assigned;
    }

    @Override
    public List<ActionHistoryVo> getActionListById(String id) {
        List<LeaderAssignedAction> actions = assignedActionMapper.queryByAssignedId(id);
        List<ActionHistoryVo> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(actions)) {
            LeaderAssigned assigned = assignedMapper.queryById(id);
            //获取交办头信息
            ActionHistoryVo firHistoryVo = new ActionHistoryVo();
            firHistoryVo.setUserName(assigned.getCreatorName());
            firHistoryVo.setType("CREATOR");
            firHistoryVo.setContent("下发");
            firHistoryVo.setOperateTime(assigned.getCreateTime());
            result.add(firHistoryVo);
        }
        //接受人信息，排除NEW类型的数据，创建历史操作数据
        actions.stream().filter(action -> !action.getActionType().equals(UtilConstants.ActionType.NEW))
                .collect(Collectors.toList()).forEach(checkAction -> {
                    ActionHistoryVo historyVo = new ActionHistoryVo();
                    historyVo.setUserName(checkAction.getReceiverName());
                    historyVo.setType(checkAction.getActionType());
                    historyVo.setContent(checkAction.getActionContent());
                    historyVo.setOperateTime(checkAction.getUpdateTime());
                    historyVo.setForwardTo(checkAction.getForwardTo());
                    result.add(historyVo);
                });
        //操作历史记录根据创建时间排序(倒叙)
        return result.stream().sorted(Comparator.comparing(ActionHistoryVo::getOperateTime, Comparator.reverseOrder())).collect(Collectors.toList());
    }

    @Override
    public BaseResult<String> actionFeedback(String actionId, String userId, String content) {
        LeaderAssignedAction action = assignedActionMapper.queryById(actionId);
        action.setActionType(UtilConstants.ActionType.FEEDBACK);
        action.setUpdater(userId);
        action.setUpdateTime(new Date());
        action.setActionContent(content);
        assignedActionMapper.updateAction(action);
        messageFeignService.deleteMessageByServiceAndUserId(action.getId(), "ldjb", String.valueOf(SecurityUtils.getLoginUser().getUserid()), SecurityUtils.getLoginUser().getUsername());
        return BaseResult.ok("反馈成功");
    }

    @Override
    public BaseResult<String> actionForward(OperateVo operateVo) {
        //更新旧数据
        LeaderAssignedAction action = assignedActionMapper.queryById(operateVo.getActionId());
        action.setActionType(UtilConstants.ActionType.FORWARD);
        action.setUpdater(operateVo.getUserId());
        action.setUpdateTime(new Date());
        action.setActionContent(operateVo.getContent());
        StringBuilder forwardName = new StringBuilder();
        //创建新数据
        operateVo.getReceiverList().forEach(receiver -> {
            receiver.setId(UUID.randomUUID().toString().replaceAll("-", ""));
            receiver.setAssignedId(action.getAssignedId());
            receiver.setCreator(action.getCreator());
            receiver.setCreatorName(action.getCreatorName());
            receiver.setCreateTime(new Date());
            receiver.setUpdateTime(new Date());
            receiver.setUpdater(operateVo.getUserId());
            receiver.setActionType(UtilConstants.ActionType.NEW);
            receiver.setForwardFrom(operateVo.getActionId());
            receiver.setIsDelete(0);
            forwardName.append(receiver.getReceiverName()).append(UtilConstants.Symbol.COMMA);
            assignedActionMapper.insertAction(receiver);
            //接受人收到的提示消息
            try {
                //【领导交办 您收到一条新的领导交办信息，请及时处理。
                Boolean msgResult = sendMsg(receiver);
                log.info("addMsg success : {} ", JSONObject.toJSONString(msgResult));
            } catch (Exception e) {
                log.info(" addMsg error : {}", e.getMessage());
            }
        });
        //去除最后一个"，"
        String content = forwardName.substring(0, forwardName.length() - 1);
        action.setForwardTo(content);
        assignedActionMapper.updateAction(action);
        messageFeignService.deleteMessageByServiceAndUserId(action.getId(), "ldjb", String.valueOf(SecurityUtils.getLoginUser().getUserid()), SecurityUtils.getLoginUser().getUsername());
        return BaseResult.ok("转发成功");
    }

    @Override
    public List<LeaderAssigned> queryByParam(String time, String creatorName) {
        //处理用户输入非法表情包报错
        try {
            List<LeaderAssigned> assignedList = assignedMapper.queryByCreator(null, creatorName, time);
            //接受人信息
            assignedList.forEach(assigned -> {
                List<LeaderAssignedAction> actions = assignedActionMapper.queryByAssignedId(assigned.getId());
                assigned.setReceiverList(actions);
                //处置流程
                List<ActionHistoryVo> actionHistoryVos = getActionListById(assigned.getId());
                assigned.setActionList(actionHistoryVos);
            });
            return assignedList;
        } catch (Exception e) {
            return new ArrayList<>();
        }

    }

    /**
     * 发送提醒消息
     *
     * @param action
     * @return
     */
    private boolean sendMsg(LeaderAssignedAction action) {
        RemindInfoDto remindInfoDto = new RemindInfoDto();
        //remindInfoDto.setRemindTitle("【领导交办】");
        remindInfoDto.setRemindTitle("领导交办 交办待处理");
        remindInfoDto.setIsRead((byte) 0);
        remindInfoDto.setRemindedType((byte) 2);
        remindInfoDto.setIsDelete((byte) 0);
        remindInfoDto.setCreater(action.getCreatorName());
        remindInfoDto.setCreateTime(DateUtils.dateTimeIntact());
        remindInfoDto.setRemindContent("您收到一条新的交办任务，请及时处理。");
        remindInfoDto.setLevel(UtilConstants.MsgFlag.YE);
        remindInfoDto.setPcUserId(null);
        remindInfoDto.setAppUserId(action.getReceiver());
        remindInfoDto.setSourceType(UtilConstants.Source.APP);
        remindInfoDto.setMessageUrl(baseMessageUrl + assignedMessageUrl);
        remindInfoDto.setAppMsgType("2");
        remindInfoDto.setServiceId(action.getId());
        remindInfoDto.setServiceType("ldjb");
        messageFeignService.addMsg(Collections.singletonList(remindInfoDto));
        return true;
    }

}
