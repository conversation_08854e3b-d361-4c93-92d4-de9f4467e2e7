package com.onecity.os.calculate.controller;

import com.onecity.os.calculate.constant.CommonConstant;
import com.onecity.os.calculate.modules.calculate.entity.IndicatorCalInfo;
import com.onecity.os.calculate.modules.calculate.vo.AppCalResultVo;
import com.onecity.os.calculate.modules.calculate.vo.IndicatorCalResultVo;
import com.onecity.os.calculate.modules.calculate.vo.IndicatorCalVo;
import com.onecity.os.calculate.service.AppCalService;
import com.onecity.os.calculate.service.CalculateService;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 指标信计算api入口
 *
 * @Author: zack
 * @Date: 2022/6/7 17:36
 */
@Slf4j
@RestController
@RequestMapping("/calculate")
@Api(tags = "指标信息api入口")
public class CalculateController extends BaseController {

    @Resource
    private CalculateService calculateService;

    @Resource
    private AppCalService appCalService;

    /**
     * 获取指标计算展示信息，如未获取到则新建
     *
     * @return 成功message
     */
    @GetMapping("/queryCalInfo")
    @ApiOperation("获取指标计算展示信息，如未获取到则新建")
    public BaseResult<IndicatorCalInfo> queryCalInfo(@RequestParam(name = "sourceId", required = true) String sourceId,
                                                     @RequestParam(name = "sourceName", required = true) String sourceName,
                                                     @RequestParam(name = "indicatorId", required = true) String indicatorId,
                                                     @RequestParam(name = "indicatorName", required = true) String indicatorName) {
        return calculateService.queryCalInfo(sourceId, sourceName, indicatorId, indicatorName);
    }

    /**
     * 根据sourceId获取指标检验结果
     *
     * @return 成功message
     */
    @GetMapping("/queryInfoBySourceId")
    @ApiOperation("根据sourceId获取指标检验结果-返回需要校验未通过的<指标id,指标名称>")
    public BaseResult<Map<String, String>> queryCalInfoBySourceId(@RequestParam(name = "sourceId") String sourceId) {
        return calculateService.queryCalInfoBySourceId(sourceId);
    }

    /**
     * 指标计算修改
     *
     * @return 成功message
     */
    @PostMapping("/saveCalInfo")
    @ApiOperation("指标计算修改-sourceId,indicatorId必输，其余字段根据修改内容传输,未修改字段不必传输")
    public BaseResult<String> saveCalInfo(@RequestBody @Valid IndicatorCalInfo indicatorCalInfo) {

        return calculateService.saveCalInfo(indicatorCalInfo);
    }

    /**
     * 指标计算修改-批量
     *
     * @return 成功message
     */
    @PostMapping("/batch/saveCalInfo")
    @ApiOperation("指标计算修改-sourceId,indicatorId必输，其余字段根据修改内容传输,未修改字段不必传输")
    public BaseResult<String> saveBatchCalInfo(@RequestBody @Valid List<IndicatorCalInfo> indicatorCalInfos) {
        for (IndicatorCalInfo indicatorCalInfo : indicatorCalInfos) {
            BaseResult<String> result = calculateService.saveCalInfo(indicatorCalInfo);
            if (!CommonConstant.SC_OK_200.equals(result.getCode())) {
                return result;
            }
        }
        return BaseResult.ok();
    }

    /**
     * 指标计算逻辑
     *
     * @return 成功message
     */
    @PostMapping("/indicatorCal")
    @ApiOperation("指标计算")
    public BaseResult<IndicatorCalResultVo> indicatorCal(@RequestBody @Valid IndicatorCalVo indicatorCalVo) {
        return calculateService.indicatorCal(indicatorCalVo);
    }

    /**
     * APP端指标计算逻辑
     *
     * @return 成功message
     */
    @PostMapping("/app/indicatorCal")
    @ApiOperation("指标计算")
    public BaseResult<AppCalResultVo> appCal(@RequestBody @Valid IndicatorCalVo indicatorCalVo) {
        return appCalService.appIndicatorCal(indicatorCalVo);
    }

}
