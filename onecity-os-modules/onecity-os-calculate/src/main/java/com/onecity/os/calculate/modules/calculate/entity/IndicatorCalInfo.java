package com.onecity.os.calculate.modules.calculate.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

/**
 * @Author: zack
 * @Date: 2022/6/9 12:00
 */
@Data
@Table(name = "indicator_cal_info")
public class IndicatorCalInfo {

    /**
     * 主键自增
     **/
    @Id
    @GeneratedValue
    private String id;


    /**
     * 板块ID
     **/
    @Column(name = "source_id")
    @ApiModelProperty(value = "板块ID")
    @NotNull(message = "板块ID不可为空")
    private String sourceId;

    /**
     * 板块ID
     **/
    @Column(name = "source_name")
    @ApiModelProperty(value = "板块名称")
    private String sourceName;

    /**
     * 指标ID
     **/
    @Column(name = "indicator_id")
    @ApiModelProperty(value = "指标ID")
    @NotNull(message = "指标ID不可为空")
    private String indicatorId;

    /**
     * 指标名称
     **/
    @Column(name = "indicator_name")
    @ApiModelProperty(value = "指标名称")
    private String indicatorName;

    /**
     * 平均值展示标记
     **/
    @Column(name = "avg_flag")
    @ApiModelProperty(value = "平均值展示标记,APP展示传1,APP不展示传0")
    private String avgFlag;

    /**
     * 增长值展示标记
     **/
    @Column(name = "growth_flag")
    @ApiModelProperty(value = "增长值展示标记,APP展示传1,APP不展示传0")
    private String growthFlag;

    /**
     * 增长比例展示标记
     **/
    @Column(name = "growth_rate_flag")
    @ApiModelProperty(value = "增长百分比展示标记,APP展示传1,APP不展示传0")
    private String growthRateFlag;

    /**
     * 往期同比展示标记 Year-on-year
     **/
    @Column(name = "yoy_rise_flag")
    @ApiModelProperty(value = "往期同比展示标记,APP展示传1,APP不展示传0")
    private String yoyRiseFlag;

    /**
     * 保存校验标记
     **/
    @Column(name = "save_check_flag")
    @ApiModelProperty(value = "保存校验标记,校验通过传1,校验不通过传0")
    private String saveCheckFlag;

    /**
     * APP展示标记
     **/
    @Column(name = "app_check_flag")
    @ApiModelProperty(value = "APP展示标记,校验通过传1,校验不通过传0")
    private String appCheckFlag;
}
