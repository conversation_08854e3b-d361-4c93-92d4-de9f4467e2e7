package com.onecity.os.calculate.service;

import com.onecity.os.calculate.modules.calculate.entity.IndicatorCalInfo;
import com.onecity.os.calculate.modules.calculate.vo.IndicatorCalResultVo;
import com.onecity.os.calculate.modules.calculate.vo.IndicatorCalVo;
import com.onecity.os.common.core.domain.BaseResult;

import java.util.Map;

/**
 * @Author: zack
 * @Date: 2022/6/9 15:58
 */
public interface CalculateService {

    /**
     * 获取指标计算展示信息，如未获取到则新建
     *
     * @param sourceId
     * @param sourceName
     * @param indicatorId
     * @param indicatorName
     * @return
     */
    BaseResult<IndicatorCalInfo> queryCalInfo(String sourceId, String sourceName, String indicatorId, String indicatorName);

    /**
     * 根据sourceId获取指标检验结果
     *
     * @param sourceId
     * @return
     */
    BaseResult<Map<String, String>> queryCalInfoBySourceId(String sourceId);

    /**
     * 保存计算结果-用户指标计算页面修改展示标记
     * @param indicatorCalInfo
     * @return
     */
    BaseResult<String> saveCalInfo(IndicatorCalInfo indicatorCalInfo);

    /**
     * 指标计算逻辑
     * @param indicatorCalVo
     * @return
     */
    BaseResult<IndicatorCalResultVo> indicatorCal(IndicatorCalVo indicatorCalVo);
}
