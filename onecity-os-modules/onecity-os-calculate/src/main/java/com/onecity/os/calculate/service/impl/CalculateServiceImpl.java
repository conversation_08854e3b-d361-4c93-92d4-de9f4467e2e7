package com.onecity.os.calculate.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.calculate.constant.UtilConstants;
import com.onecity.os.calculate.modules.calculate.entity.IndicatorCalInfo;
import com.onecity.os.calculate.modules.calculate.mapper.CalculateMapper;
import com.onecity.os.calculate.modules.calculate.vo.IndicatorCalResultVo;
import com.onecity.os.calculate.modules.calculate.vo.IndicatorCalVo;
import com.onecity.os.calculate.modules.calculate.vo.IndicatorDate;
import com.onecity.os.calculate.service.CalculateService;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.text.UUID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: zack
 * @Date: 2022/6/9 15:59
 */
@Slf4j
@Service("calculateService")
public class CalculateServiceImpl implements CalculateService {

    Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");

    @Resource
    private CalculateMapper calculateMapper;

    @Override
    public BaseResult<IndicatorCalInfo> queryCalInfo(String sourceId, String sourceName, String indicatorId, String indicatorName) {
        //获取当前指标计算信息
        IndicatorCalInfo info = calculateMapper.queryCalInfo(sourceId, indicatorId);
        if (null != info) {
            return BaseResult.ok(info);
        } else {
            //创建当前指标信息
            IndicatorCalInfo newInfo = new IndicatorCalInfo();
            newInfo.setId(UUID.randomUUID().toString().trim());
            newInfo.setSourceId(sourceId);
            newInfo.setSourceName(sourceName);
            newInfo.setIndicatorId(indicatorId);
            newInfo.setIndicatorName(indicatorName);
            newInfo.setAvgFlag(UtilConstants.StateFlag.FALSE);
            newInfo.setGrowthFlag(UtilConstants.StateFlag.FALSE);
            newInfo.setGrowthRateFlag(UtilConstants.StateFlag.FALSE);
            newInfo.setYoyRiseFlag(UtilConstants.StateFlag.FALSE);
            newInfo.setSaveCheckFlag(UtilConstants.StateFlag.TRUE);
            newInfo.setAppCheckFlag(UtilConstants.StateFlag.TRUE);
            calculateMapper.insertCalInfo(newInfo);
            IndicatorCalInfo indicatorCalInfo = calculateMapper.queryCalInfo(sourceId, indicatorId);
            return BaseResult.ok(indicatorCalInfo);
        }
    }

    @Override
    public BaseResult<Map<String, String>> queryCalInfoBySourceId(String sourceId) {
        List<IndicatorCalInfo> infos = calculateMapper.queryCalInfoBySourceId(sourceId);
        if (CollectionUtils.isEmpty(infos)) {
            return BaseResult.ok();
        } else {
            Map<String, String> result = new LinkedHashMap<>();
            infos.forEach(info -> result.put(info.getIndicatorId(), info.getIndicatorName()));
            return BaseResult.ok(result);
        }
    }

    @Override
    public BaseResult<String> saveCalInfo(IndicatorCalInfo indicatorCalInfo) {
        log.info("saveCalInfo 1.1.1. indicatorCalInfo : {} ", JSONObject.toJSONString(indicatorCalInfo));
        calculateMapper.updateCalInfo(indicatorCalInfo);
        return BaseResult.ok("更新完成");
    }

    @Override
    public BaseResult<IndicatorCalResultVo> indicatorCal(IndicatorCalVo indicatorCalVo) {
        log.info("indicatorCal 1.0.0 : indicatorCalVo {} ", JSONObject.toJSONString(indicatorCalVo));
        IndicatorCalResultVo result = new IndicatorCalResultVo();
        List<IndicatorDate> calData = new ArrayList<>();
        List<IndicatorDate> resultEntry = new ArrayList<>();
        //不同周期逻辑处理
        switch (indicatorCalVo.getUpdateCycle()) {
            case UtilConstants.UpdateCycle.ANNUAL_UPDATE:
                //年度更新 2018-2022
                String updateDate = indicatorCalVo.getUpdateDate();
                int startDate = Integer.parseInt(updateDate.substring(0, 4));
                int endDate = Integer.parseInt(updateDate.substring(5, 9));
                //数据范围
                while (startDate <= endDate) {
                    List<IndicatorDate> yIndicatorDates = calculateMapper.querySubIndicatorDate(indicatorCalVo, Integer.toString(startDate));
                    if (CollectionUtils.isEmpty(yIndicatorDates)) {
                        log.info("indicatorCal 1.1.1.1 : data error : 指标 {} 缺少 {} 数据", JSONObject.toJSONString(indicatorCalVo.getIndicatorId()), JSONObject.toJSONString(startDate));
                        startDate++;
                        continue;
                    }
                    if (!CollectionUtils.isEmpty(yIndicatorDates) && yIndicatorDates.size() != 1) {
                        return BaseResult.fail("数据期重复无法进入计算");
//                        log.info("indicatorCal 1.1.1.2 : data error : {} ", JSONObject.toJSONString(yIndicatorDates));
//                        startDate++;
//                        continue;
                    }
                    yIndicatorDates.get(0).setCalData(Integer.toString(startDate));
                    calData.add(yIndicatorDates.get(0));
                    startDate++;
                }
                if (CollectionUtils.isEmpty(calData)) {
                    log.info("indicatorCal 1.1.1.3 : data calData : {} ", JSONObject.toJSONString(calData));
                    return BaseResult.ok();
                }
                //计算
                BigDecimal ySum = BigDecimal.ZERO;
                for (IndicatorDate date : calData) {
                    if (!StringUtils.isEmpty(date.getItemValue()) && pattern.matcher(date.getItemValue()).matches()) {
                        ySum = ySum.add(BigDecimal.valueOf(Double.parseDouble(date.getItemValue())));
                    }
                    //计算增长值，增长百分比
                    int presentData = Integer.parseInt(date.getCalData());
                    //找到计算比较数据
                    IndicatorDate compareData = calData.stream()
                            .filter(cal -> !StringUtils.isEmpty(cal.getCalData()) && Integer.valueOf(cal.getCalData()).equals(presentData - 1))
                            .findFirst().orElse(null);
                    //增长值,增长百分比，计算
                    if (null != compareData && null != compareData.getItemValue()) {
                        //数值校验
                        if (!pattern.matcher(date.getItemValue()).matches() || !pattern.matcher(compareData.getItemValue()).matches()) {
                            return BaseResult.fail("本期数据/往期数据，存在非数字数据，无法计算");
                        }
                        //计算
                        BigDecimal growthValue = BigDecimal.valueOf(Double.parseDouble(date.getItemValue()))
                                .subtract(BigDecimal.valueOf(Double.parseDouble(compareData.getItemValue())));
                        //增长值
                        date.setGrowthValue(String.valueOf(growthValue));
                        //增长百分比
                        BigDecimal growthRate = growthValue.divide(BigDecimal.valueOf(Double.parseDouble(compareData.getItemValue())), 2, RoundingMode.HALF_UP);
                        date.setGrowthRate(changType(Double.parseDouble(String.valueOf(growthRate))));
                    } else {
                        log.info("indicatorCal 1.1.2. : 增长值计算有误 : {} ", JSONObject.toJSONString(compareData));
                    }
                }
                //平均值
                BigDecimal yAvg = ySum.divide(BigDecimal.valueOf(calData.size()), 2, RoundingMode.HALF_UP);
                result.setAvg(String.valueOf(yAvg));
                result.setIndicatorDate(calData);
                break;
            case UtilConstants.UpdateCycle.HALF_ANNUAL_UPDATE:
                //半年更新-年份纬度，数据分为上半年，下半年，只应该有两条数据
                BigDecimal hySum = BigDecimal.ZERO;
                List<IndicatorDate> hyIndicatorDates = calculateMapper.querySubIndicatorDate(indicatorCalVo, indicatorCalVo.getUpdateDate());
                if (CollectionUtils.isEmpty(hyIndicatorDates)) {
                    log.info("indicatorCal 1.2.0. : hyIndicatorDates is empty。");
                    return BaseResult.ok();
                } else {
                    //判断数据期是否存在存在重复数据
                    long count = hyIndicatorDates.stream().map(IndicatorDate::getUpdateDate)
                            .collect(Collectors.toList()).stream().distinct().count();
                    //判断去重后的数据是否小于之前数据
                    boolean isRepeat = count < hyIndicatorDates.size();
                    if (isRepeat) {
                        log.info("indicatorCal 1.2.1. : data error : {} ", JSONObject.toJSONString(hyIndicatorDates));
                        return BaseResult.fail("数据期重复无法进入计算");
                    }
                    hyIndicatorDates.forEach(data -> data.setCalData(data.getUpdateDate()));
                    calData.addAll(hyIndicatorDates);
                }

                //计算
                //计算增长值，增长百分比 - 半年周期只有下半年才计算
                String hyOne = indicatorCalVo.getUpdateDate() + UtilConstants.Symbol.MIDDLE_LINE + UtilConstants.UpdateCycle.HALF_ANNUAL_ONE;
                String hyTwo = indicatorCalVo.getUpdateDate() + UtilConstants.Symbol.MIDDLE_LINE + UtilConstants.UpdateCycle.HALF_ANNUAL_TWO;
                //找到计算比较数据
                IndicatorDate hyOneData = calData.stream()
                        .filter(cal -> !StringUtils.isEmpty(cal.getCalData()) && hyOne.equals(cal.getCalData()))
                        .findFirst().orElse(null);
                IndicatorDate hyTwoData = calData.stream()
                        .filter(cal -> !StringUtils.isEmpty(cal.getCalData()) && hyTwo.equals(cal.getCalData()))
                        .findFirst().orElse(null);

                //增长值,增长百分比，计算
                if (null != hyTwoData && null != hyOneData && !StringUtils.isEmpty(hyOneData.getItemValue())
                        && !StringUtils.isEmpty(hyTwoData.getItemValue())) {
                    //数值校验
                    if (!pattern.matcher(hyTwoData.getItemValue()).matches() || !pattern.matcher(hyOneData.getItemValue()).matches()) {
                        return BaseResult.fail("本期数据/往期数据，存在非数字数据，无法计算");
                    }
                    BigDecimal growthValue = BigDecimal.valueOf(Double.parseDouble(hyTwoData.getItemValue()))
                            .subtract(BigDecimal.valueOf(Double.parseDouble(hyOneData.getItemValue())));
                    //增长值
                    hyTwoData.setGrowthValue(String.valueOf(growthValue));
                    //增长百分比
                    BigDecimal growthRate = growthValue.divide(BigDecimal.valueOf(Double.parseDouble(hyOneData.getItemValue())), 2, RoundingMode.HALF_UP);
                    hyTwoData.setGrowthRate(changType(Double.parseDouble(String.valueOf(growthRate))));
                    //总计
                    hySum = hySum.add(BigDecimal.valueOf(Double.parseDouble(hyOneData.getItemValue())))
                            .add(BigDecimal.valueOf(Double.parseDouble(hyTwoData.getItemValue())));
                } else {
                    log.info("indicatorCal 1.2.2. : 增长值计算有误 hyOneData : {} ; hyTwoData : {}", JSONObject.toJSONString(hyOneData),
                            JSONObject.toJSONString(hyTwoData));
                }
                //平均值
                BigDecimal hyAvg = hySum.divide(BigDecimal.valueOf(calData.size()), 2, RoundingMode.HALF_UP);
                log.info("1.2.1.5. hySum : {} ", hyAvg);
                result.setAvg(String.valueOf(hyAvg));

                //往期同比
                //查找往期同比数据
                //往期上半年 hyOneData - oldHYOneData
                String oldHYOne = Integer.parseInt(indicatorCalVo.getUpdateDate()) - 1 + UtilConstants.Symbol.MIDDLE_LINE + UtilConstants.UpdateCycle.HALF_ANNUAL_ONE;
                List<IndicatorDate> oldHYOneDatas = calculateMapper.queryIndicatorDate(indicatorCalVo, oldHYOne);
                if (CollectionUtils.isEmpty(oldHYOneDatas) || oldHYOneDatas.size() != 1) {
                    log.info("indicatorCal 1.2.3. : data error : 未找到/找到多个【{}】往期同比数据 :【{}】", JSONObject.toJSONString(hyOne), JSONObject.toJSONString(oldHYOne));
                } else {
                    IndicatorDate oldHYOneData = oldHYOneDatas.get(0);
                    if (null != hyOneData && StringUtils.isNotEmpty(oldHYOneData.getItemValue())) {
                        //数值校验
                        if (!pattern.matcher(oldHYOneData.getItemValue()).matches()) {
                            return BaseResult.fail("本期数据/往期数据，存在非数字数据，无法计算");
                        }
                        //计算同比
                        BigDecimal hyOneYoyRise = BigDecimal.valueOf(Double.parseDouble(hyOneData.getItemValue()))
                                .subtract(BigDecimal.valueOf(Double.parseDouble(oldHYOneData.getItemValue())))
                                .divide(BigDecimal.valueOf(Double.parseDouble(oldHYOneData.getItemValue())), 2, RoundingMode.HALF_UP);
                        hyOneData.setYoyRise(changType(Double.parseDouble(String.valueOf(hyOneYoyRise))));
                    }

                    //往期下半年 hyTwoData - oldHYTwoData
                    String oldHYTwo = Integer.parseInt(indicatorCalVo.getUpdateDate()) - 1 + UtilConstants.Symbol.MIDDLE_LINE + UtilConstants.UpdateCycle.HALF_ANNUAL_TWO;
                    List<IndicatorDate> oldHYTwoDatas = calculateMapper.queryIndicatorDate(indicatorCalVo, oldHYTwo);
                    if (CollectionUtils.isEmpty(oldHYTwoDatas) || oldHYTwoDatas.size() != 1) {
                        log.info("indicatorCal 1.2.4. : data error : 未找到/找到多个【{}】往期同比数据 :【{}】", JSONObject.toJSONString(hyTwo), JSONObject.toJSONString(oldHYTwo));
                    }
                    IndicatorDate oldHYTwoData = oldHYTwoDatas.get(0);
                    if (null != hyTwoData && StringUtils.isNotEmpty(oldHYTwoData.getItemValue())) {
                        //数值校验
                        if (!pattern.matcher(oldHYTwoData.getItemValue()).matches()) {
                            return BaseResult.fail("本期数据/往期数据，存在非数字数据，无法计算");
                        }
                        //计算同比
                        BigDecimal hyTwoYoyRise = BigDecimal.valueOf(Double.parseDouble(hyTwoData.getItemValue()))
                                .subtract(BigDecimal.valueOf(Double.parseDouble(oldHYTwoData.getItemValue())))
                                .divide(BigDecimal.valueOf(Double.parseDouble(oldHYTwoData.getItemValue())), 2, RoundingMode.HALF_UP);
                        hyTwoData.setYoyRise(changType(Double.parseDouble(String.valueOf(hyTwoYoyRise))));
                    }
                }
                resultEntry.add(hyOneData);
                resultEntry.add(hyTwoData);
                result.setIndicatorDate(resultEntry);
                break;
            case UtilConstants.UpdateCycle.QUARTERLY_UPDATE:
                //季度更新-年份纬度,数据分为四个季度
                List<IndicatorDate> qIndicatorDates = calculateMapper.querySubIndicatorDate(indicatorCalVo, indicatorCalVo.getUpdateDate());
                if (CollectionUtils.isEmpty(qIndicatorDates)) {
                    log.info("indicatorCal 1.3.0.  : hyIndicatorDates is empty。 ");
                    return BaseResult.ok();
                } else {
                    //判断数据期是否存在存在重复数据
                    long count = qIndicatorDates.stream().map(IndicatorDate::getUpdateDate)
                            .collect(Collectors.toList()).stream().distinct().count();
                    //判断去重后的数据是否小于之前数据
                    boolean isRepeat = count < qIndicatorDates.size();
                    if (isRepeat) {
                        log.info("indicatorCal 1.3.1. : data error : {} ", JSONObject.toJSONString(qIndicatorDates));
                        return BaseResult.fail("数据期重复无法进入计算");
                    }
                    qIndicatorDates.forEach(data -> data.setCalData(data.getUpdateDate()));
                    calData.addAll(qIndicatorDates);
                }
                //计算
                //拼写统计 数据期
                String qOne = indicatorCalVo.getUpdateDate() + UtilConstants.Symbol.MIDDLE_LINE + UtilConstants.UpdateCycle.QUARTERLY_ONE;
                String qTwo = indicatorCalVo.getUpdateDate() + UtilConstants.Symbol.MIDDLE_LINE + UtilConstants.UpdateCycle.QUARTERLY_TWO;
                String qThird = indicatorCalVo.getUpdateDate() + UtilConstants.Symbol.MIDDLE_LINE + UtilConstants.UpdateCycle.QUARTERLY_THIRD;
                String qFour = indicatorCalVo.getUpdateDate() + UtilConstants.Symbol.MIDDLE_LINE + UtilConstants.UpdateCycle.QUARTERLY_FOUR;

                //总计
                BigDecimal qSum = BigDecimal.ZERO;

                IndicatorDate qOneData = qIndicatorDates.stream().filter(qDate -> qDate.getCalData().equals(qOne)).findFirst().orElse(null);
                IndicatorDate qTwoData = qIndicatorDates.stream().filter(qDate -> qDate.getCalData().equals(qTwo)).findFirst().orElse(null);
                IndicatorDate qThirdData = qIndicatorDates.stream().filter(qDate -> qDate.getCalData().equals(qThird)).findFirst().orElse(null);
                IndicatorDate qFourData = qIndicatorDates.stream().filter(qDate -> qDate.getCalData().equals(qFour)).findFirst().orElse(null);
                //计算增长值，增长百分比
                //增长值,增长百分比，往期同比，计算
                //1季度
                //往期同比
                if (null != qOneData && null != qOneData.getItemValue() && pattern.matcher(qOneData.getItemValue()).matches()) {
                    qSum = qSum.add(BigDecimal.valueOf(Double.parseDouble(qOneData.getItemValue())));
                    //往期数据
                    String oldQOne = Integer.parseInt(indicatorCalVo.getUpdateDate()) - 1 + UtilConstants.Symbol.MIDDLE_LINE + UtilConstants.UpdateCycle.QUARTERLY_ONE;
                    List<IndicatorDate> oldQOneDatas = calculateMapper.queryIndicatorDate(indicatorCalVo, oldQOne);
                    if (!CollectionUtils.isEmpty(oldQOneDatas) && oldQOneDatas.size() == 1) {
                        IndicatorDate oldQOneData = oldQOneDatas.get(0);
                        if (StringUtils.isNotEmpty(oldQOneData.getItemValue())) {
                            //数值校验
                            if (!pattern.matcher(qOneData.getItemValue()).matches() || !pattern.matcher(oldQOneData.getItemValue()).matches()) {
                                return BaseResult.fail("本期数据/往期数据，存在非数字数据，无法计算");
                            }
                            //计算同比
                            BigDecimal qOneYoyRise = BigDecimal.valueOf(Double.parseDouble(qOneData.getItemValue()))
                                    .subtract(BigDecimal.valueOf(Double.parseDouble(oldQOneData.getItemValue())))
                                    .divide(BigDecimal.valueOf(Double.parseDouble(oldQOneData.getItemValue())), 2, RoundingMode.HALF_UP);
                            qOneData.setYoyRise(changType(Double.parseDouble(String.valueOf(qOneYoyRise))));
                        } else {
                            log.info("indicatorCal 1.3.1.1. : oldQOneData 有误 oldQOneData : {}", JSONObject.toJSONString(oldQOneData));
                        }
                    } else {
                        log.info("indicatorCal 1.3.1.2 : data error : 未找到/找到多个【{}】往期同比数据 :【{}】", JSONObject.toJSONString(qOne), JSONObject.toJSONString(oldQOne));
                    }
                }
                //2季度
                if (null != qTwoData && null != qTwoData.getItemValue() && pattern.matcher(qTwoData.getItemValue()).matches()) {
                    qSum = qSum.add(BigDecimal.valueOf(Double.parseDouble(qTwoData.getItemValue())));
                    if (null != qOneData && null != qOneData.getItemValue()) {
                        //数值校验
                        if (!pattern.matcher(qOneData.getItemValue()).matches() || !pattern.matcher(qTwoData.getItemValue()).matches()) {
                            return BaseResult.fail("本期数据/往期数据，存在非数字数据，无法计算");
                        }
                        BigDecimal growthValue = BigDecimal.valueOf(Double.parseDouble(qTwoData.getItemValue()))
                                .subtract(BigDecimal.valueOf(Double.parseDouble(qOneData.getItemValue())));
                        //增长值
                        qTwoData.setGrowthValue(String.valueOf(growthValue));
                        //增长百分比
                        BigDecimal growthRate = growthValue.divide(BigDecimal.valueOf(Double.parseDouble(qOneData.getItemValue())), 2, RoundingMode.HALF_UP);
                        qTwoData.setGrowthRate(changType(Double.parseDouble(String.valueOf(growthRate))));
                    }
                } else {
                    log.info("indicatorCal 1.3.2. : 增长值计算有误 qTwoData : {} ; qOneData : {}", JSONObject.toJSONString(qTwoData),
                            JSONObject.toJSONString(qOneData));
                }
                //2季度往期同比
                String oldQTwo = Integer.parseInt(indicatorCalVo.getUpdateDate()) - 1 + UtilConstants.Symbol.MIDDLE_LINE + UtilConstants.UpdateCycle.QUARTERLY_TWO;
                List<IndicatorDate> oldQTwoDatas = calculateMapper.queryIndicatorDate(indicatorCalVo, oldQTwo);
                if (CollectionUtils.isEmpty(oldQTwoDatas) || oldQTwoDatas.size() != 1) {
                    log.info("indicatorCal 1.3.2.2 : data error : 未找到/找到多个【{}】往期同比数据 :【{}】", JSONObject.toJSONString(qTwo), JSONObject.toJSONString(oldQTwo));
                } else {
                    IndicatorDate oldQTwoData = oldQTwoDatas.get(0);
                    if (null != qTwoData && StringUtils.isNotEmpty(oldQTwoData.getItemValue())) {
                        //数值校验
                        if (!pattern.matcher(oldQTwoData.getItemValue()).matches()) {
                            return BaseResult.fail("本期数据/往期数据，存在非数字数据，无法计算");
                        }
                        //计算同比
                        BigDecimal qTwoYoyRise = BigDecimal.valueOf(Double.parseDouble(qTwoData.getItemValue()))
                                .subtract(BigDecimal.valueOf(Double.parseDouble(oldQTwoData.getItemValue())))
                                .divide(BigDecimal.valueOf(Double.parseDouble(oldQTwoData.getItemValue())), 2, RoundingMode.HALF_UP);
                        qTwoData.setYoyRise(changType(Double.parseDouble(String.valueOf(qTwoYoyRise))));
                    }
                }
                //3季度
                if (null != qThirdData && null != qThirdData.getItemValue() && pattern.matcher(qThirdData.getItemValue()).matches()) {
                    qSum = qSum.add(BigDecimal.valueOf(Double.parseDouble(qThirdData.getItemValue())));
                    if (null != qTwoData && null != qTwoData.getItemValue()) {
                        //数值校验
                        if (!pattern.matcher(qThirdData.getItemValue()).matches() || !pattern.matcher(qTwoData.getItemValue()).matches()) {
                            return BaseResult.fail("本期数据/往期数据，存在非数字数据，无法计算");
                        }
                        BigDecimal growthValue = BigDecimal.valueOf(Double.parseDouble(qThirdData.getItemValue()))
                                .subtract(BigDecimal.valueOf(Double.parseDouble(qTwoData.getItemValue())));
                        //增长值
                        qThirdData.setGrowthValue(String.valueOf(growthValue));
                        //增长百分比
                        BigDecimal growthRate = growthValue.divide(BigDecimal.valueOf(Double.parseDouble(qTwoData.getItemValue())), 2, RoundingMode.HALF_UP);
                        qThirdData.setGrowthRate(changType(Double.parseDouble(String.valueOf(growthRate))));
                    }
                } else {
                    log.info("indicatorCal 1.3.2. : 增长值计算有误  qThirdData : {} ; qTwoData : {}", JSONObject.toJSONString(qThirdData),
                            JSONObject.toJSONString(qTwoData));
                }
                //3季度往期同比
                String oldQThird = Integer.parseInt(indicatorCalVo.getUpdateDate()) - 1 + UtilConstants.Symbol.MIDDLE_LINE + UtilConstants.UpdateCycle.QUARTERLY_THIRD;
                List<IndicatorDate> oldQThirdDatas = calculateMapper.queryIndicatorDate(indicatorCalVo, oldQThird);
                if (CollectionUtils.isEmpty(oldQThirdDatas) || oldQThirdDatas.size() != 1) {
                    log.info("indicatorCal 1.3.3.2 : data error : 未找到/找到多个【{}】往期同比数据 :【{}】", JSONObject.toJSONString(qThird), JSONObject.toJSONString(oldQThird));
                } else {
                    IndicatorDate oldQThirdData = oldQThirdDatas.get(0);
                    if (null != qThirdData && StringUtils.isNotEmpty(oldQThirdData.getItemValue())) {
                        //数值校验
                        if (!pattern.matcher(oldQThirdData.getItemValue()).matches()) {
                            return BaseResult.fail("本期数据/往期数据，存在非数字数据，无法计算");
                        }
                        //计算同比
                        BigDecimal qThirdYoyRise = BigDecimal.valueOf(Double.parseDouble(qThirdData.getItemValue()))
                                .subtract(BigDecimal.valueOf(Double.parseDouble(oldQThirdData.getItemValue())))
                                .divide(BigDecimal.valueOf(Double.parseDouble(oldQThirdData.getItemValue())), 2, RoundingMode.HALF_UP);
                        qThirdData.setYoyRise(changType(Double.parseDouble(String.valueOf(qThirdYoyRise))));
                    }
                }
                //4季度
                if (null != qFourData && null != qFourData.getItemValue() && pattern.matcher(qFourData.getItemValue()).matches()) {
                    qSum = qSum.add(BigDecimal.valueOf(Double.parseDouble(qFourData.getItemValue())));
                    if (null != qThirdData && null != qThirdData.getItemValue()) {
                        //数值校验
                        if (!pattern.matcher(qThirdData.getItemValue()).matches() || !pattern.matcher(qFourData.getItemValue()).matches()) {
                            return BaseResult.fail("本期数据/往期数据，存在非数字数据，无法计算");
                        }
                        BigDecimal growthValue = BigDecimal.valueOf(Double.parseDouble(qFourData.getItemValue()))
                                .subtract(BigDecimal.valueOf(Double.parseDouble(qThirdData.getItemValue())));
                        //增长值
                        qFourData.setGrowthValue(String.valueOf(growthValue));
                        //增长百分比
                        BigDecimal growthRate = growthValue.divide(BigDecimal.valueOf(Double.parseDouble(qThirdData.getItemValue())), 2, RoundingMode.HALF_UP);
                        qFourData.setGrowthRate(changType(Double.parseDouble(String.valueOf(growthRate))));
                    }
                } else {
                    log.info("indicatorCal 1.4.2. : 增长值计算有误  qFourData : {} ; qThirdData : {}", JSONObject.toJSONString(qFourData),
                            JSONObject.toJSONString(qThirdData));
                }
                //4季度往期同比
                String oldQFour = Integer.parseInt(indicatorCalVo.getUpdateDate()) - 1 + UtilConstants.Symbol.MIDDLE_LINE + UtilConstants.UpdateCycle.QUARTERLY_FOUR;
                List<IndicatorDate> oldQFourDatas = calculateMapper.queryIndicatorDate(indicatorCalVo, oldQFour);
                if (CollectionUtils.isEmpty(oldQFourDatas) || oldQFourDatas.size() != 1) {
                    log.info("indicatorCal 1.3.4.2 : data error : 未找到/找到多个【{}】往期同比数据 :【{}】", JSONObject.toJSONString(qFour), JSONObject.toJSONString(oldQFour));
                } else {
                    IndicatorDate oldQFourData = oldQFourDatas.get(0);
                    if (null != qFourData && StringUtils.isNotEmpty(oldQFourData.getItemValue())) {
                        //数值校验
                        if (!pattern.matcher(qFourData.getItemValue()).matches() || !pattern.matcher(oldQFourData.getItemValue()).matches()) {
                            return BaseResult.fail("本期数据/往期数据，存在非数字数据，无法计算");
                        }
                        //计算同比
                        BigDecimal qFourYoyRise = BigDecimal.valueOf(Double.parseDouble(qFourData.getItemValue()))
                                .subtract(BigDecimal.valueOf(Double.parseDouble(oldQFourData.getItemValue())))
                                .divide(BigDecimal.valueOf(Double.parseDouble(oldQFourData.getItemValue())), 2, RoundingMode.HALF_UP);
                        qFourData.setYoyRise(changType(Double.parseDouble(String.valueOf(qFourYoyRise))));
                    }
                }
                BigDecimal qAvg = qSum.divide(BigDecimal.valueOf(qIndicatorDates.size()), 2, RoundingMode.HALF_UP);
                result.setAvg(String.valueOf(qAvg));
                resultEntry.add(qOneData);
                resultEntry.add(qTwoData);
                resultEntry.add(qThirdData);
                resultEntry.add(qFourData);
                result.setIndicatorDate(resultEntry);
                break;
            case UtilConstants.UpdateCycle.MONTHLY_UPDATE:
                //月度更新-年份纬度,数据分为12个月 2022-03
                List<IndicatorDate> mIndicatorDates = calculateMapper.querySubIndicatorDate(indicatorCalVo, indicatorCalVo.getUpdateDate());
                if (CollectionUtils.isEmpty(mIndicatorDates)) {
                    log.info("indicatorCal 1.4.0. : mIndicatorDates is empty。");
                    return BaseResult.ok();
                } else {
                    //判断数据期是否存在存在重复数据
                    long count = mIndicatorDates.stream().map(IndicatorDate::getUpdateDate)
                            .collect(Collectors.toList()).stream().distinct().count();
                    //判断去重后的数据是否小于之前数据
                    boolean isRepeat = count < mIndicatorDates.size();
                    if (isRepeat) {
                        log.info("indicatorCal 1.4.1. : data error : {} ", JSONObject.toJSONString(mIndicatorDates));
                        return BaseResult.fail("数据期重复无法进入计算");
                    }
                    mIndicatorDates.forEach(data -> data.setCalData(data.getUpdateDate()));
                    calData.addAll(mIndicatorDates);
                }
                //拼接 计算月度更新列表
                List<String> calUpdateDate = spliceMUpdateDate(indicatorCalVo.getUpdateDate());
                //总计
                BigDecimal mSum = BigDecimal.ZERO;
                for (String mUpdateDate : calUpdateDate) {
                    log.info("indicatorCal 1.4.2. mUpdateDate : {} ", JSONObject.toJSONString(mUpdateDate));
                    //按顺序找到当前计算数据
                    IndicatorDate mIndicatorDate = mIndicatorDates.stream().filter(mDate -> mDate.getUpdateDate().equals(mUpdateDate))
                            .findFirst().orElse(null);
                    if (null != mIndicatorDate && StringUtils.isNotEmpty(mIndicatorDate.getItemValue())) {
                        //数值校验
                        if (!pattern.matcher(mIndicatorDate.getItemValue()).matches()) {
                            return BaseResult.fail("本期数据/往期数据，存在非数字数据，无法计算");
                        }
                        //总计
                        mSum = mSum.add(BigDecimal.valueOf(Double.parseDouble(mIndicatorDate.getItemValue())));
                        //增长值,增长百分比，计算 (1月份不计算)
                        if (!mIndicatorDate.getUpdateDate().equals(indicatorCalVo.getUpdateDate() + UtilConstants.Symbol.MIDDLE_LINE + "01")) {
                            //找到计算比较数据
                            String compareUpdateDate = calMUpdateDate(mUpdateDate);
                            IndicatorDate compareData = mIndicatorDates.stream().filter(mDate -> mDate.getUpdateDate().equals(compareUpdateDate))
                                    .findFirst().orElse(null);
                            if (null != compareData && StringUtils.isNotEmpty(compareData.getItemValue())) {
                                if (!pattern.matcher(compareData.getItemValue()).matches()) {
                                    return BaseResult.fail("本期数据/往期数据，存在非数字数据，无法计算");
                                }
                                BigDecimal growthValue = BigDecimal.valueOf(Double.parseDouble(mIndicatorDate.getItemValue()))
                                        .subtract(BigDecimal.valueOf(Double.parseDouble(compareData.getItemValue())));
                                //增长值
                                mIndicatorDate.setGrowthValue(String.valueOf(growthValue));
                                //增长百分比
                                BigDecimal growthRate = growthValue.divide(BigDecimal.valueOf(Double.parseDouble(compareData.getItemValue())), 2, RoundingMode.HALF_UP);
                                mIndicatorDate.setGrowthRate(changType(Double.parseDouble(String.valueOf(growthRate))));
                            } else {
                                log.info("indicatorCal 1.4.2.2. : 增长值计算有误 : {}, 数据 ： {} ", compareUpdateDate, JSONObject.toJSONString(compareData));
                            }
                        }
                        //往期同比
                        String oldMUpdateDate = calYUpdateDate(mUpdateDate);
                        List<IndicatorDate> oldMIndicatorDate = calculateMapper.queryIndicatorDate(indicatorCalVo, oldMUpdateDate);
                        if (CollectionUtils.isEmpty(oldMIndicatorDate) || oldMIndicatorDate.size() != 1) {
                            log.info("indicatorCal 1.4.23 : data error : 未找到/找到多个【{}】往期同比数据 :【{}】", JSONObject.toJSONString(mUpdateDate), JSONObject.toJSONString(oldMUpdateDate));
                        } else {
                            IndicatorDate oldDate = oldMIndicatorDate.get(0);
                            if (StringUtils.isNotEmpty(oldDate.getItemValue())) {
                                if (!pattern.matcher(oldDate.getItemValue()).matches()) {
                                    return BaseResult.fail("本期数据/往期数据，存在非数字数据，无法计算");
                                }
                                //计算同比
                                BigDecimal mYoyRise = BigDecimal.valueOf(Double.parseDouble(mIndicatorDate.getItemValue()))
                                        .subtract(BigDecimal.valueOf(Double.parseDouble(oldDate.getItemValue())))
                                        .divide(BigDecimal.valueOf(Double.parseDouble(oldDate.getItemValue())), 2, RoundingMode.HALF_UP);
                                mIndicatorDate.setYoyRise(changType(Double.parseDouble(String.valueOf(mYoyRise))));
                            }
                        }
                        resultEntry.add(mIndicatorDate);
                    } else {
                        log.info("indicatorCal 1.4.3. : data error : 未找到 {} ，的数据 ", JSONObject.toJSONString(mUpdateDate));
                    }
                }
                //平均值
                BigDecimal mAvg = mSum.divide(BigDecimal.valueOf(calData.size()), 2, RoundingMode.HALF_UP);
                result.setAvg(String.valueOf(mAvg));
                result.setIndicatorDate(resultEntry);
                break;
            case UtilConstants.UpdateCycle.WEEKLY_UPDATE:
                log.info("indicatorCal 1.5.1. : 周更新逻辑未处理 ");
                //周更新
                break;
            default:
                return BaseResult.fail("更新周期有误");
        }
        return BaseResult.ok(result);
    }

    /**
     * 月度统一顺序，同时方便计算对比
     *
     * @param updateDate
     * @return
     */
    private List<String> spliceMUpdateDate(String updateDate) {
        List<String> updateDateList = new ArrayList<>();
        updateDateList.add(updateDate + UtilConstants.Symbol.MIDDLE_LINE + "01");
        updateDateList.add(updateDate + UtilConstants.Symbol.MIDDLE_LINE + "02");
        updateDateList.add(updateDate + UtilConstants.Symbol.MIDDLE_LINE + "03");
        updateDateList.add(updateDate + UtilConstants.Symbol.MIDDLE_LINE + "04");
        updateDateList.add(updateDate + UtilConstants.Symbol.MIDDLE_LINE + "05");
        updateDateList.add(updateDate + UtilConstants.Symbol.MIDDLE_LINE + "06");
        updateDateList.add(updateDate + UtilConstants.Symbol.MIDDLE_LINE + "07");
        updateDateList.add(updateDate + UtilConstants.Symbol.MIDDLE_LINE + "08");
        updateDateList.add(updateDate + UtilConstants.Symbol.MIDDLE_LINE + "09");
        updateDateList.add(updateDate + UtilConstants.Symbol.MIDDLE_LINE + "10");
        updateDateList.add(updateDate + UtilConstants.Symbol.MIDDLE_LINE + "11");
        updateDateList.add(updateDate + UtilConstants.Symbol.MIDDLE_LINE + "12");
        return updateDateList;
    }

    /**
     * 月份计算，向前数月，并补0
     *
     * @param dateMonth
     * @return
     */
    private String calMUpdateDate(String dateMonth) {
        int month = Integer.parseInt(dateMonth.substring(dateMonth.length() - 2));
        int year = Integer.parseInt(dateMonth.substring(0, 4));
        dateMonth = year + "-" + String.format("%02d", month - 1);
        return dateMonth;
    }

    /**
     * 月份计算，向前数年，
     *
     * @param dateMonth
     * @return
     */
    private String calYUpdateDate(String dateMonth) {
        int month = Integer.parseInt(dateMonth.substring(dateMonth.length() - 2));
        int year = Integer.parseInt(dateMonth.substring(0, 4));
        dateMonth = year - 1 + "-" + String.format("%02d", month);
        return dateMonth;
    }

    /**
     * 转百分数
     *
     * @param num
     * @return
     */
    private String changType(double num) {
        DecimalFormat decimalFormat = new DecimalFormat("0%");
        return decimalFormat.format(num);
    }
}
