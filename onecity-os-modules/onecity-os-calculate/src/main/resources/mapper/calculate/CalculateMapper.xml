<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.calculate.modules.calculate.mapper.CalculateMapper">

    <select id="queryCalInfo"
            resultType="com.onecity.os.calculate.modules.calculate.entity.IndicatorCalInfo">
        SELECT id               AS id,
               source_id        AS sourceId,
               source_name      AS sourceName,
               indicator_id     AS indicatorId,
               indicator_name   AS indicatorName,
               avg_flag         AS avgFlag,
               growth_flag      AS growthFlag,
               growth_rate_flag AS growthRateFlag,
               yoy_rise_flag    AS yoyRiseFlag,
               save_check_flag  AS saveCheckFlag,
               app_check_flag   AS appCheckFlag
        FROM indicator_cal_info AS info
        WHERE info.source_id = #{sourceId}
          AND info.indicator_id = #{indicatorId}
    </select>

    <insert id="insertCalInfo"
            parameterType="com.onecity.os.calculate.modules.calculate.entity.IndicatorCalInfo"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO indicator_cal_info(id, source_id, source_name, indicator_id, indicator_name, avg_flag, growth_flag,
                                       growth_rate_flag, yoy_rise_flag, save_check_flag, app_check_flag)
        VALUES (#{info.id}, #{info.sourceId}, #{info.sourceName}, #{info.indicatorId}, #{info.indicatorName},
                #{info.avgFlag}, #{info.growthFlag}, #{info.growthRateFlag}, #{info.yoyRiseFlag},
                #{info.saveCheckFlag}, #{info.appCheckFlag})
    </insert>

    <select id="queryCalInfoBySourceId"
            resultType="com.onecity.os.calculate.modules.calculate.entity.IndicatorCalInfo">
        SELECT id               AS id,
               source_id        AS sourceId,
               source_name      AS sourceName,
               indicator_id     AS indicatorId,
               indicator_name   AS indicatorName,
               avg_flag         AS avgFlag,
               growth_flag      AS growthFlag,
               growth_rate_flag AS growthRateFlag,
               yoy_rise_flag    AS yoyRiseFlag,
               save_check_flag  AS saveCheckFlag,
               app_check_flag   AS appCheckFlag
        FROM indicator_cal_info AS info
        WHERE info.source_id = #{sourceId}
          AND info.save_check_flag = '0'
    </select>

    <update id="updateCalInfo"
            parameterType="com.onecity.os.calculate.modules.calculate.entity.IndicatorCalInfo">
        UPDATE indicator_cal_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="info.avgFlag != null  and info.avgFlag != ''">
                avg_flag = #{info.avgFlag},
            </if>
            <if test="info.growthFlag != null  and info.growthFlag != ''">
                growth_flag = #{info.growthFlag},
            </if>
            <if test="info.growthRateFlag != null  and info.growthRateFlag != ''">
                growth_rate_flag = #{info.growthRateFlag},
            </if>
            <if test="info.yoyRiseFlag != null  and info.yoyRiseFlag != ''">
                yoy_rise_flag = #{info.yoyRiseFlag},
            </if>
            <if test="info.saveCheckFlag != null  and info.saveCheckFlag != ''">
                save_check_flag = #{info.saveCheckFlag},
            </if>
            <if test="info.appCheckFlag != null  and info.appCheckFlag != ''">
                app_check_flag = #{info.appCheckFlag},
            </if>
        </trim>
        where 1 = 1
        <if test="info.id != null  and info.id != ''">
            AND id = #{info.id}
        </if>
        AND indicator_id = #{info.indicatorId}
        AND source_id = #{info.sourceId}
    </update>

    <select id="querySubIndicatorDate"
            resultType="com.onecity.os.calculate.modules.calculate.vo.IndicatorDate">
        SELECT ind.source_id      AS sourceId,
               ind.source_name    AS sourceName,
               cal.indicator_id   AS indicatorId,
               ind.indicator_name AS indicatorName,
               ind.update_cycle   AS updateCycle,
               cal.update_date    AS updateDate,
               cal.item_name      AS itemName,
               cal.item_value     AS itemValue,
               cal.item_unit      AS itemUnit
        FROM general_indicator_data_tianbao AS cal
                 LEFT JOIN general_indicator_tianbao AS ind ON cal.indicator_id = ind.id
        WHERE ind.source_id = #{info.sourceId}
          AND cal.indicator_id = #{info.indicatorId}
          AND ind.update_cycle = #{info.updateCycle}
          AND cal.is_delete = 0
          AND LEFT (cal.update_date, 4) = #{updateDate}
    </select>

    <select id="queryIndicatorDate"
            resultType="com.onecity.os.calculate.modules.calculate.vo.IndicatorDate">
        SELECT ind.source_id      AS sourceId,
               ind.source_name    AS sourceName,
               cal.indicator_id   AS indicatorId,
               ind.indicator_name AS indicatorName,
               ind.update_cycle   AS updateCycle,
               cal.update_date    AS updateDate,
               cal.item_name      AS itemName,
               cal.item_value     AS itemValue,
               cal.item_unit      AS itemUnit
        FROM general_indicator_data_tianbao AS cal
                 LEFT JOIN general_indicator_tianbao AS ind ON cal.indicator_id = ind.id
        WHERE ind.source_id = #{info.sourceId}
          AND cal.indicator_id = #{info.indicatorId}
          AND ind.update_cycle = #{info.updateCycle}
          AND cal.is_delete = 0
          AND cal.update_date = #{updateDate}
    </select>

    <select id="queryAppSubIndicatorDate"
            resultType="com.onecity.os.calculate.modules.calculate.vo.IndicatorDate">
        SELECT cal.id             AS dataId,
               ind.source_id      AS sourceId,
               ind.source_name    AS sourceName,
               cal.indicator_id   AS indicatorId,
               ind.indicator_name AS indicatorName,
               ind.update_cycle   AS updateCycle,
               cal.update_date    AS updateDate,
               cal.item_name      AS itemName,
               cal.item_value     AS itemValue,
               cal.item_unit      AS itemUnit
        FROM general_indicator_data AS cal
                 LEFT JOIN general_indicator AS ind ON cal.indicator_id = ind.id
        WHERE ind.source_id = #{info.sourceId}
          AND cal.indicator_id = #{info.indicatorId}
          AND ind.update_cycle = #{info.updateCycle}
          AND cal.is_delete = 0
          AND LEFT (cal.update_date, 4) = #{updateDate}
    </select>

    <select id="queryAppIndicatorDate"
            resultType="com.onecity.os.calculate.modules.calculate.vo.IndicatorDate">
        SELECT cal.id             AS dataId,
               ind.source_id      AS sourceId,
               ind.source_name    AS sourceName,
               cal.indicator_id   AS indicatorId,
               ind.indicator_name AS indicatorName,
               ind.update_cycle   AS updateCycle,
               cal.update_date    AS updateDate,
               cal.item_name      AS itemName,
               cal.item_value     AS itemValue,
               cal.item_unit      AS itemUnit
        FROM general_indicator_data AS cal
                 LEFT JOIN general_indicator AS ind ON cal.indicator_id = ind.id
        WHERE ind.source_id = #{info.sourceId}
          AND cal.indicator_id = #{info.indicatorId}
          AND ind.update_cycle = #{info.updateCycle}
          AND cal.is_delete = 0
          AND cal.update_date = #{updateDate}
    </select>
</mapper>