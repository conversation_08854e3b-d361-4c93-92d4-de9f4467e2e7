package com.onecity.os.disposal.aop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.onecity.os.common.core.domain.BaseResult;
import io.swagger.annotations.ApiOperation;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Date;

/**
 * @Author: yuk
 * @Description: 打印日志切面
 * @Date: 2020-01-14 11:02
 * @Version: 1.0.0
 */
@Component
@Aspect
@Order(10)
public class ControllerAspect {
    Logger logger = LoggerFactory.getLogger(ControllerAspect.class);

    @Pointcut("execution(* com.onecity.os.disposal.controller.*.*(..))")
    public void cutController(){}

    @Before("cutController()")
    public void before(){
    }

    /**
     * <AUTHOR>
     * @Description 在所有接口查询后打印查询结果数据
     * @Date 2020-01-14
     * @Param
     * @return 查询结果，接口返回
     **/
    @Around("cutController()")
    public BaseResult afterReturn(ProceedingJoinPoint point) throws Throwable {
        Date startDate = new Date();
        Object[] args = point.getArgs();
        String paramStr = JSON.toJSONString(args[0]);
        Object object = point.proceed();
        if(object == null){
            return BaseResult.ok(new JSONArray());
        }
        BaseResult result = (BaseResult) object;
        //获取注解
        Signature signature = point.getSignature();
        MethodSignature msg=(MethodSignature) signature;
        Object target = point.getTarget();
        Method method = target.getClass().getMethod(msg.getName(), msg.getParameterTypes());
        //获取接口描述
        String logDesc = method.getAnnotation(ApiOperation.class).value();
        Date endDate = new Date();

        logger.info("调用 ：：： " + logDesc  + "接口 ：：： 参数为-----" + paramStr + "\r\n结果为-----"
                + JSON.toJSONString(result) + "\r\n***********耗时：" + (endDate.getTime() - startDate.getTime()) + "ms");
        return result;
    }
}
