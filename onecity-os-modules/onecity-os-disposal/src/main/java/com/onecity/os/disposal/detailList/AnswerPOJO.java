package com.onecity.os.disposal.detailList;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Author: yuk
 * @menu 批示流转信息映射
 * @Description: 批示流转信息映射
 * @Date: 2022/6/10 14:23
 * @Version: 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name="disposal_flow")
public class AnswerPOJO {
    @Id
    private String id;
    /**
     * 批示id
     */
    @Column(name="disposal_id")
    private String disposalId;
    /**
     * 流程id
     */
    @Column(name="process_id")
    private String processId;
    /**
     * 步骤id
     */
    @Column(name="step_id")
    private String stepId;
    /**
     * 角色id
     */
    @Column(name="role_id")
    private String roleId;

    /**
     * 角色名称
     */
    @Column(name="role_name")
    private String roleName;

    /**
     * 状态
     */
    @Column(name="status")
    private Integer status;

    /**
     * 操作时间
     */
    @Column(name="operate_time")
    private String operateTime;

    /**
     * 操作人id
     */
    @Column(name="operate_user_id")
    private String operateUserId;

    /**
     * 操作人姓名
     */
    @Column(name="operate_user_name")
    private String operateUserName;

    /**
     * 意见
     */
    @Column(name="context")
    private String context;

    /**
     * 操作类型
     */
    @Column(name="type")
    private Integer type;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDisposalId() {
        return disposalId;
    }

    public void setDisposalId(String disposalId) {
        this.disposalId = disposalId;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public String getStepId() {
        return stepId;
    }

    public void setStepId(String stepId) {
        this.stepId = stepId;
    }

    public String getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(String operateTime) {
        this.operateTime = operateTime;
    }

    public String getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(String operateUserId) {
        this.operateUserId = operateUserId;
    }

    public String getOperateUserName() {
        return operateUserName;
    }

    public void setOperateUserName(String operateUserName) {
        this.operateUserName = operateUserName;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}