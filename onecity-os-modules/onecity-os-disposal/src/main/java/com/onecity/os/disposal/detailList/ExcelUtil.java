package com.onecity.os.disposal.detailList;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: yuk
 * @menu
 * @Description:
 * @Date: 2022/7/27 16:22
 * @Version: 1.0.0
 */
public class ExcelUtil {

    static{
        Map<String,String> map = new  HashMap<String,String>();
        map.put("工资薪金","money");
    }

    public static void main(String[] args) {
        JSONArray resultArray = new JSONArray();

        Map<Integer,String> numToZimu = new HashMap<>();
        for(int n = 0; n < 26; n ++ ){
            String letter = "";
            letter = ((char) (n % 26 + (int) 'A')) + letter;
            numToZimu.put(n,letter);
        }
        for(int n = 26; n < 52; n ++ ){
            String letter = "";
            letter = ((char) ((n - 26) % 26 + (int) 'A')) + letter;
            numToZimu.put(n,"A" + letter);
        }
        for(int n = 52; n < 78; n ++ ){
            String letter = "";
            letter = ((char) ((n - 52) % 26 + (int) 'A')) + letter;
            numToZimu.put(n,"B" + letter);
        }
        File file = new File("E:/test1.xlsx");
        XSSFWorkbook hssfWorkbook = null;
        try {
            hssfWorkbook = new XSSFWorkbook(file);
        } catch (IOException | InvalidFormatException e) {
            e.printStackTrace();
        }
        JSONArray shuoming = new JSONArray();
        //获取所有的sheet
        XSSFSheet hssfSheet = hssfWorkbook.getSheet("Sheet1");
        int rowNum = hssfSheet.getLastRowNum();
        for(int i = 0 ; i <= rowNum; i ++){
            XSSFRow row =  hssfSheet.getRow(i);
            if(StringUtils.isBlank(row.getCell(0).getStringCellValue())){
                break;
            }
            if(row.getCell(0).getStringCellValue().contains(".")){
                JSONObject jsonObject = new JSONObject();
                String[] strs = row.getCell(0).getStringCellValue().split("\\.");
                String name = strs[strs.length-1];
                String desc = row.getCell(4).getStringCellValue();
                jsonObject.put("name",name);
                jsonObject.put("desc",desc);
                shuoming.add(jsonObject);
            }
        }
        String jsonStr = "[{\n" +
                "\"id\":\"9000000799080705\",\n" +
                "\"basicInfo\":{\"name\":\"20220714-1级扩展\",\"basicCode\":\"61050016900101K01\",\"parentId\":\"4000000099783772\",\"implementCode\":\"610401161050016900101K01\",\"eventSpeciesId\":\"05\",\"eventSpeciesName\":\"行政给付\",\"level\":\"3\",\"adHerarchyId\":\"1\",\"adHerarchyName\":\"省级\",\"settingBasis\":null,\"edition\":null,\"basicId\":\"66\",\"businessCode\":null,\"expandCode\":null,\"implementMainpart\":\"1\",\"implementDept\":\"1\",\"mainpartCode\":\"11620000013897638U\",\"settleTime\":\"1\",\"settleType\":\"1\",\"settleExplain\":\"索大法\",\"undertakeTime\":\"1\",\"undertakeType\":\"2\",\"undertakeExplain\":\"萨嘎\",\"processType\":\"1\",\"serviceObject\":\"1\",\"mainpartNatral\":\"015\",\"localpartNatral\":null,\"mainpartLegal\":null,\"localpartLegal\":null,\"handleType\":\",1\",\"charge\":\"1\",\"mobileLogin\":\"0\",\"mobilePath\":null,\"pcLogin\":\"0\",\"pcPath\":null,\"handleProcess\":null,\"processExplain\":\"撒发达发\",\"powerSource\":\"1\",\"specialApp\":\"案说法\",\"acceptanceCondition\":\"大\",\"consultingPhone\":\"1388888888\",\"consultingAddress\":\"大厦\",\"consultingWeb\":\"www.baidu.com\",\"complaintPhone\":\"***********\",\"complaintAddress\":\"大厦\",\"complaintWeb\":\"www.baidu.com\",\"handleAddress\":\"ds\",\"handleTime\":\"8:00\",\"startDate\":1657814400000,\"cancelDate\":1659196800000,\"extendId\":\"65\",\"garrisonRoom\":\"0\",\"serviceChannel\":\"5\",\"handleRange\":\"1\",\"unitDept\":\"dsafa \",\"countLimit\":\"20\",\"resultName\":\"大\",\"resultType\":\"20\",\"resultSample\":null,\"appointment\":\"0\",\"selfHelp\":\"0\",\"webPay\":\"0\",\"payType\":null,\"express\":\"0\",\"expressChannel\":null,\"webImplement\":\"0\",\"implementDeep\":null,\"webAddress\":\"www.baidu.com\",\"sceneTimes\":\"2\",\"extendExplain\":\"美元与\",\"townName\":\"索大法\",\"townCode\":\"123456111111111111\",\"communityName\":\"昂贵的撒\",\"communityCode\":\"123456111111111111\",\"licenseId\":null,\"eventType\":null,\"allowCondition\":null,\"conditionBasis\":null,\"programBasis\":null,\"intermediary\":null,\"chargesNatrue\":null,\"intermediaryName\":null,\"intermediaryBasis\":null,\"intermediaryDept\":null,\"told\":null,\"survey\":null,\"evidence\":null,\"deal\":null,\"test\":null,\"appraisal\":null,\"review\":null,\"publicity\":null,\"certificatesName\":null,\"certificatesTime\":null,\"timeType\":null,\"regulationsBasis\":null,\"continueRequirement\":null,\"certificatesRegion\":null,\"regionBasis\":null,\"regionExplain\":null,\"numLimit\":null,\"publishMode\":null,\"otherMode\":null,\"publishCycle\":null,\"cycleType\":null,\"permitMode\":null,\"otherPermit\":null,\"permitBasis\":null,\"yearInspection\":null,\"inspectionBasis\":null,\"inspectionCycle\":null,\"otherInspection\":null,\"inspectionMaterials\":null,\"inspectionCharge\":null,\"inspectionProve\":null,\"yearReport\":null,\"reportBasis\":null,\"reportCycle\":null,\"otherReport\":null,\"reportMaterials\":null,\n" +
                "\"materialInfoList\":[{\"materialId\":\"85\",\"implementCode\":\"610401161050016900101K01\",\"materialName\":\"材料1\",\"necessity\":\"1\",\"mode\":null,\"channel\":\"10\",\"channelExplain\":\"下发\",\"enclosureBlank\":null,\"enclosureDemo\":null,\"pageCount\":\"1\",\"pageRule\":\"1\",\"fillingRemarks\":\"需要\",\"acceptCriteria\":\"需要\",\"basis\":\"需要\",\"remark\":null,\"key\":\"85\"}],\n" +
                "\"answerInfoList\":[{\"answerId\":\"146\",\"implementCode\":\"610401161050016900101K01\",\"question\":\"如何办理\",\"answer\":\"不告诉你！\",\"key\":\"146\"}],\n" +
                "\"chargeInfoList\":[{\"chargeId\":\"77\",\"implementCode\":\"610401161050016900101K01\",\"chargeProject\":\"材料1\",\"chargeStandard\":\"50元\",\"chargeBasis\":\"工本费\",\"reduction\":\"1\",\"reductionBasis\":\"财政补贴\",\"remark\":\"补贴不用缴费\",\"key\":\"77\"}]\n" +
                "}\n" +
                "}]";
        JSONArray temp = JSONArray.parseArray(jsonStr);
        JSONObject jsonObject = temp.getJSONObject(0);

        File file2 = new File("E:/test2.xlsx");
        XSSFWorkbook hssfWorkbook2 = null;
        try {
            hssfWorkbook2 = new XSSFWorkbook(file2);
        } catch (IOException | InvalidFormatException e) {
            e.printStackTrace();
        }
        JSONArray qingdan = new JSONArray();
        //获取所有的sheet
        XSSFSheet hssfSheet2 = hssfWorkbook2.getSheet("基础信息");
        XSSFRow row2 =  hssfSheet2.getRow(0);
        int cellNum = row2.getLastCellNum();
        for(int i = 0 ; i < cellNum; i ++){
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("desc",row2.getCell(i).getStringCellValue());
            jsonObject1.put("zimu",numToZimu.get(i));
            jsonObject1.put("num",i);
            qingdan.add(jsonObject1);
        }
        for(Map.Entry entry : jsonObject.getJSONObject("basicInfo").entrySet()){
            String param =  (String) entry.getKey();
            boolean f = false;
            for(int i = 0 ; i < shuoming.size() ; i ++){
                f = false;
                JSONObject object = shuoming.getJSONObject(i);
                if(param.equals(object.getString("name"))){
                    String desc = convertName(object.getString("desc"));
                    boolean flag = false;
                    for(int j = 0 ; j < qingdan.size() ; j ++){
                        flag = false;
                        JSONObject object1 = qingdan.getJSONObject(j);
                        String desc1 = object1.getString("desc").replaceAll("：","-");
                        if(desc.equals(desc1)){
                            JSONObject resultObject = new JSONObject();
                            resultObject.put("name",param);
                            resultObject.put("desc",desc);
                            resultObject.put("lie",object1.get("zimu"));
                            resultObject.put("num",object1.get("num"));
                            resultArray.add(resultObject);
                            flag = true;
                            break;
                        }
                    }
                    if(!flag){
                        JSONObject resultObject = new JSONObject();
                        resultObject.put("name",param);
                        resultObject.put("desc",desc);
                        resultArray.add(resultObject);
                    }
                    f = true;
                    break;
                }
            }
            if(!f){
                JSONObject resultObject = new JSONObject();
                resultObject.put("name",param);
                resultArray.add(resultObject);
            }
        }
        JSONObject resultObject = new JSONObject();
        resultObject.put("name","basicCode");
        resultObject.put("desc","基本编码");
        resultObject.put("lie","F");
        resultObject.put("num",5);
        resultArray.add(resultObject);

        JSONArray dataArray = new JSONArray();
        int rows = hssfSheet2.getLastRowNum();
        for(int i = 1 ; i <= rows;i++){
            XSSFRow xssfRow = hssfSheet2.getRow(i);
            JSONObject data = new JSONObject();
            for(int j = 0; j < resultArray.size(); j ++){
                JSONObject temp1 = resultArray.getJSONObject(j);
                if(temp1.containsKey("num")){
                    int cellNums = temp1.getInteger("num");
                    XSSFCell xssfCell = xssfRow.getCell(cellNums);
                    Object value = null;
                    if(xssfCell != null){
                        try{
                            value = xssfCell.getStringCellValue();
                        }catch (IllegalStateException e){
                            value = xssfCell.getNumericCellValue();
                        }
                    }
                    data.put(temp1.getString("name"),value);
                }else {
                    data.put(temp1.getString("name"),"");
                }

            }
            data.put("link",xssfRow.getCell(3).getStringCellValue());
            dataArray.add(data);
        }
        XSSFSheet hssfSheet3 = hssfWorkbook2.getSheet("申请材料");
        JSONArray jsonArray = new JSONArray();
        int rows1 = hssfSheet3.getLastRowNum();
        String name = "道路运输从业资格证换发、补发、变更";
        for(int i = 1 ; i <= rows1;i++){
            XSSFRow xssfRow = hssfSheet3.getRow(i);

            if(StringUtils.isNotBlank(xssfRow.getCell(1).getStringCellValue()) && !name.equals(xssfRow.getCell(1).getStringCellValue())){
                for(int j = 0 ; j < dataArray.size(); j ++){
                    if(name.equals(dataArray.getJSONObject(j).getString("link"))){
                        for(int m=0;m < jsonArray.size(); m++){
                            jsonArray.getJSONObject(m).put("implementCode",dataArray.getJSONObject(j).getString("implementCode"));
                        }
                        dataArray.getJSONObject(j).put("materialInfoList",jsonArray);
                        break;
                    }
                }
                jsonArray = new JSONArray();
                name = xssfRow.getCell(1).getStringCellValue();
            }
            JSONObject jsonObject1 = new JSONObject();

            jsonObject1.put("materialName",xssfRow.getCell(2)!=null?xssfRow.getCell(2).getStringCellValue():null);
            jsonObject1.put("channel",xssfRow.getCell(3)!=null?xssfRow.getCell(3).getStringCellValue():null);
            jsonObject1.put("type",xssfRow.getCell(4)!=null?xssfRow.getCell(4).getStringCellValue():null);
            jsonObject1.put("mode",xssfRow.getCell(5)!=null?xssfRow.getCell(5).getStringCellValue():null);
            jsonObject1.put("pageCount",xssfRow.getCell(6)!=null?xssfRow.getCell(6).getNumericCellValue():null);
            jsonObject1.put("necessity",xssfRow.getCell(7)!=null?xssfRow.getCell(7).getStringCellValue():null);
            jsonObject1.put("enclosureBlank",xssfRow.getCell(8)!=null?xssfRow.getCell(8).getStringCellValue():null);
            jsonObject1.put("enclosureDemo",xssfRow.getCell(9)!=null?xssfRow.getCell(9).getStringCellValue():null);
            jsonObject1.put("pageRule",xssfRow.getCell(10)!=null?xssfRow.getCell(10).getStringCellValue():null);
            jsonObject1.put("fillingRemarks",xssfRow.getCell(11)!=null?xssfRow.getCell(11).getStringCellValue():null);
            jsonObject1.put("acceptCriteria",xssfRow.getCell(12)!=null?xssfRow.getCell(12).getStringCellValue():null);
            jsonObject1.put("basis",xssfRow.getCell(13)!=null?xssfRow.getCell(13).getStringCellValue():null);
            jsonObject1.put("remark",xssfRow.getCell(14)!=null?xssfRow.getCell(14).getStringCellValue():null);
            jsonArray.add(jsonObject1);

        }

        XSSFSheet hssfSheet4 = hssfWorkbook2.getSheet("常见问题");
        JSONArray jsonArray1 = new JSONArray();
        int rows2 = hssfSheet4.getLastRowNum();
        name = "道路运输从业资格证换发、补发、变更";
        for(int i = 1 ; i <= rows2;i++){
            XSSFRow xssfRow = hssfSheet4.getRow(i);

            if(StringUtils.isNotBlank(xssfRow.getCell(1).getStringCellValue()) && !name.equals(xssfRow.getCell(1).getStringCellValue())){
                for(int j = 0 ; j < dataArray.size(); j ++){
                    if(name.equals(dataArray.getJSONObject(j).getString("link"))){
                        for(int m=0;m < jsonArray1.size(); m++){
                            jsonArray1.getJSONObject(m).put("implementCode",dataArray.getJSONObject(j).getString("implementCode"));
                        }
                        dataArray.getJSONObject(j).put("answerInfoList",jsonArray1);
                        break;
                    }
                }
                jsonArray1 = new JSONArray();
                name = xssfRow.getCell(1).getStringCellValue();
            }
            JSONObject jsonObject1 = new JSONObject();

            jsonObject1.put("answer",xssfRow.getCell(3)!=null?xssfRow.getCell(3).getStringCellValue():null);
            jsonObject1.put("question",xssfRow.getCell(2)!=null?xssfRow.getCell(2).getStringCellValue():null);
            jsonArray1.add(jsonObject1);

        }
        XSSFWorkbook result = null;
//        File file1 = new File("E:/result.txt");
        try {
            result = new XSSFWorkbook();
            XSSFSheet resultSheet = result.createSheet("比对结果");
            for(int i = 0 ; i < resultArray.size(); i ++ ){
                JSONObject jsonObject1 = resultArray.getJSONObject(i);
                XSSFRow xssfRow = resultSheet.createRow(i);
                XSSFCell cell = xssfRow.createCell(0);
                cell.setCellValue(jsonObject1.getString("name"));
                XSSFCell cell1 = xssfRow.createCell(1);
                cell1.setCellValue(jsonObject1.getString("desc"));
                XSSFCell cell2 = xssfRow.createCell(2);
                cell2.setCellValue(jsonObject1.getString("lie"));

//                String str = "`"+ jsonObject1.getString("name")+"` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '" + jsonObject1.getString("desc") +"'" + ",";
//                str = str + "\r\n";
//                Writer writer = new FileWriter(file1,true);
//                writer.write(str);
//                writer.flush();
            }
//            result.write(new FileOutputStream(new File("E:/result.xlsx")));


        } catch (Exception e) {
            e.printStackTrace();
        }

        System.out.println(JSONObject.toJSONString(dataArray.toJSONString(),SerializerFeature.WriteMapNullValue));
    }

    private static String convertName(String name) {
        name = name.replace("有无中介服务","中介服务");
        name = name.replace("业务办理项编码","业务编码");
        name = name.replace("乡镇街道名称","区域名称");
        name = name.replace("是否进驻大厅","是否进驻政务大厅");
        name = name.replace("行政许可事项类型","事项类型");
        name = name.replace("自然人事项主体分类","面向自然人事项主题分类");
        name = name.replace("实施机构","实施主体");
        name = name.replace("承诺办结时限","承诺时限");
        name = name.replace("法人事项主体分类","面向法人事项主题分类");
        name = name.replace("到办事现场次数","到现场次数");
        name = name.replace("办理流程","办理流程图");
        name = name.replace("法定办结时限","法定时限");
        name = name.replace("网上办理深度","网办深度");
        return name;
    }
}