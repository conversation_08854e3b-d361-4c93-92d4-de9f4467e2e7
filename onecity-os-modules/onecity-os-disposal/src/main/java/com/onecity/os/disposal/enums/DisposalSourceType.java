
package com.onecity.os.disposal.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 批示来源类型
 */
public enum DisposalSourceType {

    DISPOSAL_SOURCE_CREAT(0, "创建"),
    DISPOSAL_SOURCE_REPORT(2, "呈报");


    DisposalSourceType(int type, String desc){
        this.type = type;
        this.desc = desc;
    }

    private final int type;
    private final String desc;

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(int type) {
        for(DisposalSourceType disposalSourceType : DisposalSourceType.values()){
            if(disposalSourceType.getType() == type){
                return disposalSourceType.getDesc();
            }
        }
        return "";
    }

    public static List<Map<String, Object>> getMap() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (DisposalSourceType disposalSourceType : DisposalSourceType.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", disposalSourceType.getType());
            map.put("desc", disposalSourceType.getDesc());
            list.add(map);
        }
        return list;
    }


}
