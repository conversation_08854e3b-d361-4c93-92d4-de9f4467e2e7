
package com.onecity.os.disposal.enums;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 批示状态
 */
public enum DisposalStatus {

    DISPOSAL_STATUS_DEFAULT(0, "未开始"),
    DISPOSAL_STATUS_FLOW(1, "流转中"),
    DISPOSAL_STATUS_REPORT(2, "已呈报"),
    DISPOSAL_STATUS_END(3, "已结束");


    DisposalStatus(int status, String desc){
        this.status = status;
        this.desc = desc;
    }

    private final int status;
    private final String desc;

    public int getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(int status) {
        for(DisposalStatus disposalStatus : DisposalStatus.values()){
            if(disposalStatus.getStatus() == status){
                return disposalStatus.getDesc();
            }
        }
        return "";
    }

    public static List<Map<String, Object>> getMap() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (DisposalStatus disposalStatus : DisposalStatus.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", disposalStatus.getStatus());
            map.put("desc", disposalStatus.getDesc());
            list.add(map);
        }
        return list;
    }


}
