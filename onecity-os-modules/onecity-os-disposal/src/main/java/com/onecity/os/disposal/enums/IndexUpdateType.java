
package com.onecity.os.disposal.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 批示来源类型
 */
public enum IndexUpdateType {

    INDEX_UPDATE_TYPE_NAME(0, "名称"),
    INDEX_UPDATE_TYPE_DATE(1, "数据");


    IndexUpdateType(int type, String desc){
        this.type = type;
        this.desc = desc;
    }

    private final int type;
    private final String desc;

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(int type) {
        for(IndexUpdateType indexUpdateType : IndexUpdateType.values()){
            if(indexUpdateType.getType() == type){
                return indexUpdateType.getDesc();
            }
        }
        return "";
    }

    public static List<Map<String, Object>> getMap() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (IndexUpdateType indexUpdateType : IndexUpdateType.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", indexUpdateType.getType());
            map.put("desc", indexUpdateType.getDesc());
            list.add(map);
        }
        return list;
    }


}
