/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.onecity.os.disposal.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 流程操作类型
 */
public enum ProcessOperateType {

    PROCESS_OPERATE_TYPE_DISPOSAL(0, "批示"),
    PROCESS_OPERATE_TYPE_DO(1, "办理"),
    PROCESS_OPERATE_TYPE_READ(2, "审阅"),
    PROCESS_OPERATE_TYPE_COMPLETE(3, "完成");


    ProcessOperateType(int status, String desc){
        this.type = status;
        this.desc = desc;
    }

    private final Integer type;
    private final String desc;

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(int status) {
        for(ProcessOperateType processOperateType : ProcessOperateType.values()){
            if(processOperateType.getType() == status){
                return processOperateType.getDesc();
            }
        }
        return "";
    }

    public static List<Map<String, Object>> getMap() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProcessOperateType processOperateType : ProcessOperateType.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", processOperateType.getType());
            map.put("desc", processOperateType.getDesc());
            list.add(map);
        }
        return list;
    }

    public static ProcessOperateType getByType(Integer operateType) {
        for(ProcessOperateType type : values()){
            if (type.getType() == operateType) {
                //获取指定的枚举
                return type;
            }
        }
        return null;
    }
}
