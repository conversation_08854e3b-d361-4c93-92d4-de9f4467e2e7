
package com.onecity.os.disposal.enums;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 批示来源类型
 */
public enum StepFlowStatus {

    STEP_FLOW_STATUS_WAIT(0, "待执行"),
    STEP_FLOW_STATUS_COMPLETE(1, "已执行"),
    STEP_FLOW_STATUS_ROLLBACK(2, "已撤回");


    StepFlowStatus(int status, String desc){
        this.status = status;
        this.desc = desc;
    }

    private final int status;
    private final String desc;

    public int getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(int status) {
        for(StepFlowStatus stepFlowStatus : StepFlowStatus.values()){
            if(stepFlowStatus.getStatus() == status){
                return stepFlowStatus.getDesc();
            }
        }
        return "";
    }

    public static List<Map<String, Object>> getMap() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (StepFlowStatus stepFlowStatus : StepFlowStatus.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("status", stepFlowStatus.getStatus());
            map.put("desc", stepFlowStatus.getDesc());
            list.add(map);
        }
        return list;
    }


}
