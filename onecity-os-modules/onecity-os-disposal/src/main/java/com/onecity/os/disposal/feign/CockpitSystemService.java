package com.onecity.os.disposal.feign;

import com.onecity.os.common.core.constant.ServiceNameConstants;
import com.onecity.os.common.core.domain.BaseResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 *
 */
@FeignClient(name = ServiceNameConstants.SUPERVISE_MODULE_SYSTEM, fallbackFactory = CockpitSystemServiceFallbackFactory.class, decode404 = true)
public interface CockpitSystemService {
    /**
     * feign调用：发送批示通知
     * @param
     * @return Result
     */
    @PostMapping(value = "/dingdingapi/sendDisposalNotice")
    ResultInfo sendDisposalNotice(@RequestBody DisposalNoticeVO disposalNoticeVO);

    /**
     * feign调用：根据用户查询批示权限
     * @param
     * @return Result
     */
    @GetMapping(value = "/configManage/getPermissionsByUserId")
    BaseResult getPermissionsByUserId(@RequestParam(value = "roleList") List<String> roleList, @RequestParam(value = "indicatorId") String indicatorId);
}
