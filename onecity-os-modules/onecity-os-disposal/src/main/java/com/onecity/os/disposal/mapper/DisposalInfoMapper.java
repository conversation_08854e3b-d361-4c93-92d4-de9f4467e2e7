package com.onecity.os.disposal.mapper;

import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.disposal.model.dto.DisposalListDTO;
import com.onecity.os.disposal.model.po.DisposalFlowPOJO;
import com.onecity.os.disposal.model.po.DisposalInfoPOJO;
import com.onecity.os.disposal.model.po.DisposalProcessLinkPOJO;
import com.onecity.os.disposal.model.po.ProcessStepInfoPOJO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DisposalInfoMapper extends BaseMapper<DisposalInfoPOJO> {

    List<DisposalInfoPOJO> selectDisposalWaitByRoleId(@Param("roleIdList") List<String> roleIdList);
    //查询我发起的批示
    List<DisposalInfoPOJO> selectDisposalApplyByUserId(@Param("userId") String userId);
    //查询我接收的批示
    List<DisposalInfoPOJO> selectDisposalReceiveByRoleId(@Param("roleIdList") List<String> roleIdList);

    List<DisposalInfoPOJO> selectDisposalCompleteByRoleId(@Param("roleIdList") List<String> roleIdList);

    List<DisposalInfoPOJO> selectDisposalCompleteByUserId(@Param("roleIdList") List<String> roleIdList,@Param("userId") String userId);

    List<ProcessStepInfoPOJO> selectDisposalReadByRoleId(@Param("disposalId") String disposalId, @Param("roleIdList") List<String> roleIdList);

    List<DisposalFlowPOJO> selectDisposalReportByRoleId(@Param("disposalId")String disposalId, @Param("roleIdList") List<String> roleIdList);

    Integer insertNewByReportDisposal(@Param("newId")String newId,@Param("disposalId")String disposalId, @Param("createTime")String createTime,
                                   @Param("createUserId")String createUserId,@Param("createUserName")String createUserName);

    Integer insertDisposalProcessLink(@Param("newId")String newId,@Param("disposalId")String disposalId,@Param("oldDisposalId")String oldDisposalId,
                                   @Param("processId")String reportProcessId,@Param("sourceType")int sourceType,
                                      @Param("originDisposalId")String originDisposalId);

    Integer insertDisposalProcessLinkFirst(@Param("newId")String newId,@Param("disposalId")String disposalId,
                                        @Param("processId")String reportProcessId,@Param("sourceType")int sourceType,
                                           @Param("sort")int sort,@Param("originDisposalId")String originDisposalId);

    Integer updateStatusById(@Param("disposalIdList")List<String> disposalIdList, @Param("status")int status);


    Integer updateStepFlowEnd(@Param("disposalId")String disposalId, @Param("status")int status);

    List<DisposalInfoPOJO> selectDisposalListByIndex(@Param("indexId")String indexId,@Param("userId")String userId,@Param("lastUpdateTime")String lastUpdateTime);

    DisposalProcessLinkPOJO selectLastLinkByDisposalId(@Param("disposalId")String disposalId);

    List<DisposalInfoPOJO> getLastReportDisposal(@Param("disposalIdList")List<String> tempIdList);

    Integer getSourceTypeByDisposalId(@Param("disposalId")String disposalId);

    List<DisposalListDTO> getProcessStepListAll(@Param("sourceName")String sourceName, @Param("userName")String userName);
}
