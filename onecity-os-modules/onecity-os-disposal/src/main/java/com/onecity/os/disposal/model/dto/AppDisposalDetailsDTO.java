package com.onecity.os.disposal.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.onecity.os.disposal.model.entity.StepFlowInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.List;

/**
 * @Author: yuk
 * @Version: 1.0.0
 */
@Api(value = "批示详情返回数据实体")
@Data
public class AppDisposalDetailsDTO {
    /**
     * 待办数量
     */
    private int unprocessed;
    /**
     * 列表
     */
    private List<DisposalDetailsDTO> result;
}