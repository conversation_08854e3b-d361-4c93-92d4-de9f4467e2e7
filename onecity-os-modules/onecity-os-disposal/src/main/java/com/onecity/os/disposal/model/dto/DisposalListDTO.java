package com.onecity.os.disposal.model.dto;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 */
@Api(value = "批示列表返回数据实体")
@Data
public class DisposalListDTO {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 批示模块
     */
    @ApiModelProperty(value = "批示模块")
    private String sourceName;

    /**
     * 批示领导
     */
    @ApiModelProperty(value = "批示领导")
    private String createUserName;

    /**
     * 批示时间
     */
    @ApiModelProperty(value = "批示时间")
    private String createTime;

    /**
     * 批示内容
     */
    @ApiModelProperty(value = "批示内容")
    private String content;

    /**
     * 是否已完成
     */
    @ApiModelProperty(value = "是否已完成，0：未完成，1：已完成, 2：已撤回")
    private Integer status;
    @ApiModelProperty(value = "状态描述")
    private String statusStr;

}