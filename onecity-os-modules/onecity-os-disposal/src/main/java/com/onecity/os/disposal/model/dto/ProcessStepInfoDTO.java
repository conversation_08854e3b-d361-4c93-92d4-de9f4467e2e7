package com.onecity.os.disposal.model.dto;

import com.onecity.os.system.api.domain.SysRole;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: yuk
 * @menu 流程步骤返回数据
 * @Description: 流程步骤返回数据
 * @Date: 2022/6/10 15:11
 * @Version: 1.0.0
 */
@Api(value = "流程步骤返回数据")
@Data
public class ProcessStepInfoDTO {
    /**
     * 流程id
     */
    @ApiModelProperty(value = "流程id")
    private String processId;
    /**
     * 步骤id，新增传空
     */
    @ApiModelProperty(value = "步骤id，新增传空")
    private String id;
    /**
     * 角色id，逗号隔开
     */
    @ApiModelProperty(value = "角色id，逗号隔开")
    private String roleIds;
    /**
     * 角色名称，逗号隔开
     */
    @ApiModelProperty(value = "角色名称，逗号隔开")
    private String roleNames;
    /**
     * 角色列表
     */
    @ApiModelProperty(value = "角色列表")
    private List<SysRole> roleList;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private Integer operateType;
    /**
     * 操作名称
     */
    @ApiModelProperty(value = "操作名称")
    private String operateName;
    /**
     * 角色id，逗号隔开
     */
    @ApiModelProperty(value = "转发流程id，逗号隔开")
    private String turnProcessId;
    /**
     * 角色名称，逗号隔开
     */
    @ApiModelProperty(value = "转发流程名称，逗号隔开")
    private String turnProcessName;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private int sort;

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(String roleIds) {
        this.roleIds = roleIds;
    }

    public String getRoleNames() {
        return roleNames;
    }

    public void setRoleNames(String roleNames) {
        this.roleNames = roleNames;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public String getOperateName() {
        return operateName;
    }

    public void setOperateName(String operateName) {
        this.operateName = operateName;
    }

    public String getTurnProcessId() {
        return turnProcessId;
    }

    public void setTurnProcessId(String turnProcessId) {
        this.turnProcessId = turnProcessId;
    }

    public String getTurnProcessName() {
        return turnProcessName;
    }

    public void setTurnProcessName(String turnProcessName) {
        this.turnProcessName = turnProcessName;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }
}