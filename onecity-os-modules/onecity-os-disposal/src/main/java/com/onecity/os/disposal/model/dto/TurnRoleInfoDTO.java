package com.onecity.os.disposal.model.dto;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Author: yuk
 * @menu 转发到的角色信息返回数据实体
 * @Description: 转发到的角色信息返回数据实体
 * @Date: 2022/6/14 11:01
 * @Version: 1.0.0
 */
public class TurnRoleInfoDTO {
    /**
     * 角色id
     */
    @ApiModelProperty(value = "角色id")
    private String roleId;
    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称")
    private String roleName;

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }
}