package com.onecity.os.disposal.model.entity;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: yuk
 * @menu 操作步骤信息
 * @Description: 操作步骤信息
 * @Date: 2022/6/13 16:46
 * @Version: 1.0.0
 */
@Api(value = "批示流转返回数据实体")
@Data
public class StepFlowInfo {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private String operateTime;

    /**
     * 操作人id
     */
    @ApiModelProperty(value = "操作人id")
    private String operateUserId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operateUserName;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private int operateType;
    /**
     * 操作名称
     */
    @ApiModelProperty(value = "操作名称")
    private String operateName;
    /**
     * 操作状态
     */
    @ApiModelProperty(value = "操作状态")
    private Integer status;
    /**
     * 意见
     */
    @ApiModelProperty(value = "意见")
    private String context;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(String operateTime) {
        this.operateTime = operateTime;
    }

    public String getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(String operateUserId) {
        this.operateUserId = operateUserId;
    }

    public String getOperateUserName() {
        return operateUserName;
    }

    public void setOperateUserName(String operateUserName) {
        this.operateUserName = operateUserName;
    }

    public int getOperateType() {
        return operateType;
    }

    public void setOperateType(int operateType) {
        this.operateType = operateType;
    }

    public String getOperateName() {
        return operateName;
    }

    public void setOperateName(String operateName) {
        this.operateName = operateName;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }
}