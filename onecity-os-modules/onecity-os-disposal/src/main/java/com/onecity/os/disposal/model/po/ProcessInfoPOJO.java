package com.onecity.os.disposal.model.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Author: yuk
 * @menu 批示信息表映射
 * @Description: 批示信息表映射
 * @Date: 2022/6/10 14:23
 * @Version: 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name="process_info")
public class ProcessInfoPOJO {
    @Id
    private String id;
    /**
     * 编号
     */
    @Column(name="code")
    private String code;
    /**
     * 名称
     */
    @Column(name="name")
    private String name;
    /**
     * 是否删除：0：未删除，1：已删除
     */
    @Column(name="is_delete")
    private String isDelete;

    /**
     * 发起时间
     */
    @Column(name="create_time")
    private String createTime;

    /**
     * 发起人id
     */
    @Column(name="create_user_id")
    private String createUserId;

    /**
     * 发起人姓名
     */
    @Column(name="create_user_name")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name="update_time")
    private String updateTime;

    /**
     * 更新人id
     */
    @Column(name="update_user_id")
    private String updateUserId;

    /**
     * 更新人姓名
     */
    @Column(name="update_user_name")
    private String updateUserName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(String isDelete) {
        this.isDelete = isDelete;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }
}