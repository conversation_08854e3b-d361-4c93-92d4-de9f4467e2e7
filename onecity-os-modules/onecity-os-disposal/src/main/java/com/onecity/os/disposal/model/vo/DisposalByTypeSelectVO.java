package com.onecity.os.disposal.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: yuk
 * @menu 批示列表查询入参
 * @Description: 批示列表查询入参
 * @Date: 2022/6/10 14:54
 * @Version: 1.0.0
 */
@Api(value = "批示列表查询入参")
@Data
public class DisposalByTypeSelectVO {
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;
    /**
     * 角色id
     */
//    @JsonProperty("READ_ONLY")
    @ApiModelProperty(value = "角色id")
    private String roleIds;
    /**
     * 类型，0：待办，1：已办
     */
    @ApiModelProperty(value = "类型，0：待办，1：已办")
    private String type;
    /**
     * 页数
     */
    @ApiModelProperty(value = "页数")
    private int pageNum;
    /**
     * 每页数据量
     */
    @ApiModelProperty(value = "每页数据量")
    private int pageSize;

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}