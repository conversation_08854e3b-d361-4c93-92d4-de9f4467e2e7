package com.onecity.os.disposal.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: yuk
 * @menu 批示详情查询入参
 * @Description: 批示详情查询入参
 * @Date: 2022/6/13 14:54
 * @Version: 1.0.0
 */
@Api(value = "批示详情查询入参")
@Data
public class DisposalDetailsSelectVO {
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;
    /**
     * 角色id
     */
//    @JsonProperty("READ_ONLY")
    @ApiModelProperty(value = "角色id")
    private String roleIds;
    /**
     * 批示id
     */
    @ApiModelProperty(value = "批示id")
    private String disposalId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDisposalId() {
        return disposalId;
    }

    public void setDisposalId(String disposalId) {
        this.disposalId = disposalId;
    }

    public String getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(String roleIds) {
        this.roleIds = roleIds;
    }
}