package com.onecity.os.disposal.model.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 */
@Api(value = "批示列表查询入参")
@Data
public class DisposalListSelectVO {

    /**
     * 批示模块
     */
    @ApiModelProperty(value = "批示模块")
    private String sourceName;

    /**
     * 流程id
     */
    @ApiModelProperty(value = "批示领导")
    private String userName;
    /**
     * 页数
     */
    @ApiModelProperty(value = "页数")
    private Integer pageNum;
    /**
     * 每页数据量
     */
    @ApiModelProperty(value = "每页数据量")
    private Integer pageSize;
}