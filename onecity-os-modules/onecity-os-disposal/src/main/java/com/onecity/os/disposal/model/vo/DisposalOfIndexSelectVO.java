package com.onecity.os.disposal.model.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: yuk
 * @menu 查询指标批示历史入参
 * @Description: 查询指标批示历史入参
 * @Date: 2022/6/14 10:54
 * @Version: 1.0.0
 */
@Api(value = "查询指标批示历史入参")
@Data
public class DisposalOfIndexSelectVO {
    /**
     * 指标id
     */
    @ApiModelProperty(value = "指标id")
    private String indexId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 页数
     */
    @ApiModelProperty(value = "页数")
    private Integer pageNum;
    /**
     * 每页数据量
     */
    @ApiModelProperty(value = "每页数据量")
    private Integer pageSize;

    public String getIndexId() {
        return indexId;
    }

    public void setIndexId(String indexId) {
        this.indexId = indexId;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}