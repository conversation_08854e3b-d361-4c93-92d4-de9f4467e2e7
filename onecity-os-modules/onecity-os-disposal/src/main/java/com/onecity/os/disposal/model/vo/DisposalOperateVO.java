package com.onecity.os.disposal.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: yuk
 * @menu 批示操作入参
 * @Description: 批示操作入参
 * @Date: 2022/6/14 14:54
 * @Version: 1.0.0
 */
@Api(value = "批示操作入参")
@Data
public class DisposalOperateVO {
    /**
     * 批示结果信息id
     */
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;
    /**
     * 角色id
     */
//    @JsonProperty("READ_ONLY")
    @ApiModelProperty(value = "角色id")
    private String roleId;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户姓名")
    private String userRealName;
    /**
     * 批示id
     */
    @ApiModelProperty(value = "批示id")
    private String disposalId;

    /**
     * 流程id
     */
    @ApiModelProperty(value = "流程id")
    private String processId;

    /**
     * 步骤id
     */
    @ApiModelProperty(value = "步骤id")
    private String stepId;

    /**
     * 转发角色id
     */
//    @JsonProperty("READ_ONLY")
    @ApiModelProperty(value = "转发角色id")
    private String turnRoleId;

    /**
     * 转发角色名称
     */
//    @JsonProperty("READ_ONLY")
    @ApiModelProperty(value = "转发角色名称")
    private String turnRoleName;

    /**
     * 呈报流程id
     */
    @ApiModelProperty(value = "呈报流程id")
    private String reportProcessId;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    private String context;


    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDisposalId() {
        return disposalId;
    }

    public void setDisposalId(String disposalId) {
        this.disposalId = disposalId;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public String getStepId() {
        return stepId;
    }

    public void setStepId(String stepId) {
        this.stepId = stepId;
    }

    public String getTurnRoleId() {
        return turnRoleId;
    }

    public void setTurnRoleId(String turnRoleId) {
        this.turnRoleId = turnRoleId;
    }

    public String getTurnRoleName() {
        return turnRoleName;
    }

    public void setTurnRoleName(String turnRoleName) {
        this.turnRoleName = turnRoleName;
    }

    public String getReportProcessId() {
        return reportProcessId;
    }

    public void setReportProcessId(String reportProcessId) {
        this.reportProcessId = reportProcessId;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public String getUserRealName() {
        return userRealName;
    }

    public void setUserRealName(String userRealName) {
        this.userRealName = userRealName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }
}