package com.onecity.os.disposal.model.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Author: yuk
 * @menu 指标更新信息
 * @Description: 指标更新信息
 * @Date: 2022/7/12 9:30
 * @Version: 1.0.0
 */
@Api(value = "指标更新信息入参")
@Data
public class IndexUpdateRecordVO {
    /**
     * 指标id
     */
    @ApiModelProperty(value = "指标id")
    private String indexId;
    /**
     * 更新类型
     */
    @ApiModelProperty(value = "更新类型")
    private Integer updateType;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    public String getIndexId() {
        return indexId;
    }

    public void setIndexId(String indexId) {
        this.indexId = indexId;
    }

    public Integer getUpdateType() {
        return updateType;
    }

    public void setUpdateType(Integer updateType) {
        this.updateType = updateType;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}