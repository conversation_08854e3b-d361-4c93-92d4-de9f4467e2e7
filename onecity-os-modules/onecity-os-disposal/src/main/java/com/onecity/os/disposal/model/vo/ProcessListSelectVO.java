package com.onecity.os.disposal.model.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: yuk
 * @menu 流程列表查询入参
 * @Description: 流程列表查询入参
 * @Date: 2022/6/10 14:54
 * @Version: 1.0.0
 */
@Api(value = "流程列表查询入参")
@Data
public class ProcessListSelectVO {
    /**
     * 流程名称
     */
    @ApiModelProperty(value = "流程名称")
    private String processName;
    /**
     * 页数
     */
    @ApiModelProperty(value = "页数")
    private Integer pageNum;
    /**
     * 每页数据量
     */
    @ApiModelProperty(value = "每页数据量")
    private Integer pageSize;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }
}