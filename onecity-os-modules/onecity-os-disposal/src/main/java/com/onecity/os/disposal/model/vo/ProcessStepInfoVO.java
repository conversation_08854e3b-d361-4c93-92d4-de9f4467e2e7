package com.onecity.os.disposal.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: yuk
 * @menu 流程步骤新增/编辑入参
 * @Description: 流程步骤新增/编辑入参
 * @Date: 2022/6/10 15:11
 * @Version: 1.0.0
 */
@Api(value = "流程步骤新增/编辑入参")
@Data
public class ProcessStepInfoVO {
    /**
     * 步骤id，新增传空
     */
    @ApiModelProperty(value = "步骤id，新增传空")
    private String id;
    /**
     * 流程id
     */
    @ApiModelProperty(value = "流程id")
    private String processId;

    /**
     * 角色id，逗号隔开
     */
//    @JsonProperty("READ_ONLY")
    @ApiModelProperty(value = "角色id，逗号隔开")
    private String roleIds;
    /**
     * 角色名称，逗号隔开
     */
//    @JsonProperty("READ_ONLY")
    @ApiModelProperty(value = "角色名称，逗号隔开")
    private String roleNames;
    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private int operateType;
    /**
     * 操作名称
     */
    @ApiModelProperty(value = "操作名称")
    private String operateName;
    /**
     * 角色id，逗号隔开
     */
    @ApiModelProperty(value = "转发流程id，逗号隔开")
    private String turnProcessIds;
    /**
     * 角色名称，逗号隔开
     */
    @ApiModelProperty(value = "转发流程名称，逗号隔开")
    private String turnProcessNames;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private int sort;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "用户名")
    private String userName;


    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public String getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(String roleIds) {
        this.roleIds = roleIds;
    }

    public String getRoleNames() {
        return roleNames;
    }

    public void setRoleNames(String roleNames) {
        this.roleNames = roleNames;
    }

    public int getOperateType() {
        return operateType;
    }

    public void setOperateType(int operateType) {
        this.operateType = operateType;
    }

    public String getOperateName() {
        return operateName;
    }

    public void setOperateName(String operateName) {
        this.operateName = operateName;
    }

    public String getTurnProcessIds() {
        return turnProcessIds;
    }

    public void setTurnProcessIds(String turnProcessIds) {
        this.turnProcessIds = turnProcessIds;
    }

    public String getTurnProcessNames() {
        return turnProcessNames;
    }

    public void setTurnProcessNames(String turnProcessNames) {
        this.turnProcessNames = turnProcessNames;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}