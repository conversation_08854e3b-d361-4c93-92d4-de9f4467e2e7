package com.onecity.os.disposal.model.vo;

import com.onecity.os.disposal.model.entity.SortInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: yuk
 * @menu 流程步骤新增/编辑入参
 * @Description: 流程步骤新增/编辑入参
 * @Date: 2022/6/10 15:11
 * @Version: 1.0.0
 */
@Api(value = "流程步骤新增/编辑入参")
@Data
public class ProcessStepSortVO {
    /**
     * 流程id
     */
    @ApiModelProperty(value = "流程id")
    private String processId;
    /**
     * 步骤id，排序对应的实体数组
     */
    @ApiModelProperty(value = "步骤id，排序对应的实体数组")
    private List<SortInfo> sortList;

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public List<SortInfo> getSortList() {
        return sortList;
    }

    public void setSortList(List<SortInfo> sortList) {
        this.sortList = sortList;
    }
}