package com.onecity.os.disposal.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.DateUtils;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.disposal.enums.*;
import com.onecity.os.disposal.feign.*;
import com.onecity.os.disposal.mapper.*;
import com.onecity.os.disposal.model.dto.*;
import com.onecity.os.disposal.model.entity.RoleInfo;
import com.onecity.os.disposal.model.entity.StepFlowInfo;
import com.onecity.os.disposal.model.po.*;
import com.onecity.os.disposal.model.vo.*;
import com.onecity.os.disposal.service.DisposalOperateService;
import com.onecity.os.disposal.utils.SnowflakeUtil;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: yuk
 * @menu 批示操作服务实现类
 * @Description: 批示操作服务实现类
 * @Date: 2022/6/14 11:04
 * @Version: 1.0.0
 */
@Service
@Slf4j
public class DisposalOperateServiceImpl implements DisposalOperateService {
    @Autowired
    DisposalOperateMapper disposalOperateMapper;

    @Autowired
    ProcessStepMapper processStepMapper;

    @Autowired
    DisposalFlowMapper disposalFlowMapper;

    @Autowired
    DisposalInfoMapper disposalInfoMapper;

    @Autowired
    ProcessInfoMapper processInfoMapper;

    @Autowired
    CockpitSystemService cockpitSystemService;

    @Autowired
    IndexUpdateRecordMapper indexUpdateRecordMapper;
    @Resource
    private MessageFeignService messageFeignService;

    @Value("${app.baseMessageUrl}")
    private String baseMessageUrl;

    @Override
    public AppDisposalDetailsDTO getDisposalOfUserByType(DisposalByTypeSelectVO processByTypeSelectVO) {
        AppDisposalDetailsDTO result = new AppDisposalDetailsDTO();
        List<DisposalDetailsDTO> resultList = new ArrayList<>();
        //现根据用户id，查询角色id
        String roleIds = StringUtils.isEmpty(processByTypeSelectVO.getRoleIds())?
                getRoleInfoByUserId(processByTypeSelectVO.getUserId()).getRoleId():processByTypeSelectVO.getRoleIds();
        if(StringUtils.isEmpty(roleIds)){
            return result;
        }
        if(DisposalType.DISPOSAL_TYPE_WAIT.getType().equals(processByTypeSelectVO.getType())){
            //我发起的
            //创建的批示且发起人是当前用户的
            List<DisposalInfoPOJO> applyList = disposalInfoMapper.selectDisposalApplyByUserId(processByTypeSelectVO.getUserId());
            convertList(applyList,resultList,DisposalType.DISPOSAL_TYPE_WAIT.getType());
        }else if(DisposalType.DISPOSAL_TYPE_COMPLETE.getType().equals(processByTypeSelectVO.getType())) {
            //我接收的
            //呈报的批示且用户是批示 + 其他操作类型需要用户操作的
            List<DisposalInfoPOJO> receiveList = disposalInfoMapper.selectDisposalReceiveByRoleId(Arrays.asList(roleIds.split(",")));
            int unprocessed = 0;
            //未处理的放前面
            //当前用户的数据放在后面
            List<DisposalInfoPOJO> unprocessedList = new ArrayList<>();
            //判断操作状态
            for(int i = 0 ; i < receiveList.size() ; i ++){
                DisposalInfoPOJO pojo = receiveList.get(i);
                //查询当前批示，当前操作人有无未操作记录
                List<DisposalFlowPOJO> notList = disposalFlowMapper.selectFlowByDisposalIdAndRoleIds(pojo.getId(),Arrays.asList(roleIds.split(",")));
                if(pojo.getStatus() != null && pojo.getStatus() == 3){
                    pojo.setStatus(1);
                }else {
                    if(notList.size() > 0 ){
                        pojo.setStatus(0);
                        unprocessed++;
                        unprocessedList.add(receiveList.remove(i));
                        i--;
                    }else {
                        //查看是否有审阅记录
                        List<DisposalFlowPOJO> notSyList = disposalFlowMapper.selectSYFlowByDisposalIdAndRoleIds(pojo.getId(),Arrays.asList(roleIds.split(",")));
                        List<DisposalFlowPOJO> syList = disposalFlowMapper.selectSYFlowByDisposalId(pojo.getId());
                        if(syList.size() == 0 && notSyList.size()>0){
                            pojo.setStatus(0);
                            unprocessed++;
                            unprocessedList.add(receiveList.remove(i));
                            i--;
                        }else {
                            pojo.setStatus(1);
                        }
                    }
                }
            }
            result.setUnprocessed(unprocessed);
            receiveList.addAll(0,unprocessedList);
            convertList(receiveList,resultList,DisposalType.DISPOSAL_TYPE_COMPLETE.getType());
        }
        result.setResult(resultList);
        return result;
    }

    private void convertList(List<DisposalInfoPOJO> sourceList,List<DisposalDetailsDTO> resultList,String type){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for(DisposalInfoPOJO disposalInfo : sourceList){
            DisposalDetailsDTO detailsDTO = new DisposalDetailsDTO();
            detailsDTO.setId(disposalInfo.getId());
            detailsDTO.setTitle(disposalInfo.getTitle());
            detailsDTO.setContent(disposalInfo.getContent());
            try {
                detailsDTO.setCreateTime(simpleDateFormat.format(simpleDateFormat.parse(disposalInfo.getCreateTime())));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            //状态翻译
            if(DisposalType.DISPOSAL_TYPE_WAIT.getType().equals(type)){
                //我发起的
                if(disposalInfo.getStatus() != null && disposalInfo.getStatus() <= 2){
                    detailsDTO.setStatusStr("流转中");
                }else {
                    detailsDTO.setStatusStr("已完结");
                }
            } else {
                if(disposalInfo.getStatus()==0){
                    detailsDTO.setStatusStr("未处理");
                }else {
                    detailsDTO.setStatusStr("已处理");
                }
            }
            resultList.add(detailsDTO);
        }
    }
    @Override
    public DisposalDetailsDTO getDisposalDetails(DisposalDetailsSelectVO processDetailsSelectVO) {
        DisposalDetailsDTO result = new DisposalDetailsDTO();
        String disposalId = processDetailsSelectVO.getDisposalId();
        //根据用户查询角色信息
        String roleIds = StringUtils.isEmpty(processDetailsSelectVO.getRoleIds())?
                getRoleInfoByUserId(processDetailsSelectVO.getUserId()).getRoleId():processDetailsSelectVO.getRoleIds();
        if(StringUtils.isEmpty(roleIds)){
            return result;
        }
        //查询指标批示信息
        DisposalInfoPOJO pojo = disposalInfoMapper.selectByPrimaryKey(disposalId);
        if(pojo == null){
            return result;
        }
        BeanUtils.copyProperties(pojo,result);
        if(result.getStatus() != null && result.getStatus() <= 2){
            //未完成
            result.setStatus(0);
        }else {
            //已完成
            result.setStatus(1);
        }
        //判断是否有审阅
        hasRead(result,disposalId,roleIds);
        //判断是否有呈报
        hasReport(result,disposalId,roleIds);
        //查询，组装流转信息
        //先查询当前批示及后续批示的id列表
        List<String> disposalIdList = disposalFlowMapper.selectDisposalIdOfLink(processDetailsSelectVO.getDisposalId());
        log.debug("根据批示id:" + processDetailsSelectVO.getDisposalId() + "，查询批示链条所有批示id为：：" + JSON.toJSONString(disposalIdList));
        if(disposalIdList!=null && disposalIdList.size() > 0){
            //查询这些批示的流转信息
            List<StepFlowInfo> stepFlowInfos = disposalFlowMapper.selectStepFlowsByDisposalId(disposalIdList);
            result.setFlowList(stepFlowInfos);
        }
        return result;
    }

    /**
     * 是否有呈报选项
     * @param result
     * @param disposalId
     * @param roleIds
     */
    private void hasReport(DisposalDetailsDTO result,String disposalId,String roleIds) {
        //查询角色的待办步骤
        List<DisposalFlowPOJO> tempList2 = disposalInfoMapper.selectDisposalReportByRoleId(disposalId,Arrays.asList(roleIds.split(",")));
        boolean flag = false;
        for(int i = 0; i < tempList2.size();i ++){
            DisposalFlowPOJO pojo1 = tempList2.get(i);
            String processId = pojo1.getProcessId();
            String stepId = pojo1.getStepId();
            //查询当前步骤信息
            ProcessStepInfoPOJO processStepInfoPOJO = processStepMapper.selectCurrentStepInfo(processId,stepId);
            if(i == 0){
                result.setCurrentStepId(stepId);
                result.setCurrentProcessId(processId);
                result.setOperateType(processStepInfoPOJO.getOperateType());
                result.setOperateName(processStepInfoPOJO.getOperateName());
            }
            //是否存在呈报流程
            if(StringUtils.isNotEmpty(processStepInfoPOJO.getTurnProcessIds())){
                result.setReportProcessId(pojo1.getProcessId());
                result.setReportStepId(pojo1.getStepId());
                flag = true;
                break;
            }
        }
        result.setReport(flag);
    }

    /**
     * 是否有审阅选项
     * @param result
     * @param disposalId
     * @param roleIds
     */
    private void hasRead(DisposalDetailsDTO result,String disposalId,String roleIds) {
        List<ProcessStepInfoPOJO> tempList1 = disposalInfoMapper.selectDisposalReadByRoleId(disposalId,Arrays.asList(roleIds.split(",")));
        //批示当前的步骤信息
        List<Integer> sortList = disposalFlowMapper.selectCurrentStepSort(disposalId);
        //如果已经执行到审阅，则有审阅权限
        if(tempList1!=null && tempList1.size()>0){
            boolean flag = false;
            for(Integer sort : sortList){
                for(ProcessStepInfoPOJO pojo1 : tempList1){
                    if(sort > pojo1.getSort()){
                        flag = true;
                        break;
                    }
                }
                if(flag){
                    break;
                }
            }
            if(flag){
                result.setCheck(true);
                result.setCheckProcessId(tempList1.get(0).getProcessId());
                result.setCheckStepId(tempList1.get(0).getId());
            }
        }else {
            result.setCheck(false);
        }
    }

    @Override
    public List<TurnRoleInfoDTO> getTurnRoles(DisposalNextSelectVO disposalNextSelectVO) {
        List<TurnRoleInfoDTO> resultList = new ArrayList<>();
        //查询当前步骤信息
        ProcessStepInfoPOJO pojo;
        if(StringUtils.isEmpty(disposalNextSelectVO.getStepId())){
            pojo = processStepMapper.selectNextStepByProcessIdNotRead(disposalNextSelectVO.getProcessId());
        }else {
            pojo = processStepMapper.selectNextStepNotRead(disposalNextSelectVO.getProcessId(),disposalNextSelectVO.getStepId());
        }
        if(pojo == null){
            return resultList;
        }
        //组装角色信息
        List<String> roleIds = Arrays.asList(pojo.getRoleIds().split(","));
        List<String> roleNames = Arrays.asList(pojo.getRoleNames().split(","));
        for(int i = 0 ; i < roleIds.size(); i ++){
            TurnRoleInfoDTO dto = new TurnRoleInfoDTO();
            dto.setRoleId(roleIds.get(i));
            if(roleNames.size() > i){
                dto.setRoleName(roleNames.get(i));
            }
            resultList.add(dto);
        }
        return resultList;
    }

    @Override
    public List<ProcessInfoDTO> getReportProcesses(DisposalNextSelectVO disposalNextSelectVO) {
        List<ProcessInfoDTO> resultList = new ArrayList<>();
        //查询当前步骤信息
        ProcessStepInfoPOJO pojo = processStepMapper.selectCurrentStepInfo(disposalNextSelectVO.getProcessId(),disposalNextSelectVO.getStepId());
        if(pojo == null){
            return resultList;
        }
        //组装流程信息
        if(pojo.getTurnProcessIds().length() > 0){
            List<String> processIds = Arrays.asList(pojo.getTurnProcessIds().split(","));
            List<String> processNames = Arrays.asList(pojo.getTurnProcessNames().split(","));
            for(int i = 0 ; i < processIds.size(); i ++){
                ProcessInfoDTO dto = new ProcessInfoDTO();
                dto.setId(processIds.get(i));
                if(processNames.size() > i){
                    dto.setName(processNames.get(i));
                }
                resultList.add(dto);
            }
        }

        return resultList;
    }

    @Override
    public List<ProcessInfoDTO> getDisposalProcesses(DisposalNextSelectVO disposalNextSelectVO) {
        RoleInfo roleInfo = getRoleInfoByUserId(disposalNextSelectVO.getUserId());
        String roleIds = StringUtils.isEmpty(disposalNextSelectVO.getRoleIds())?roleInfo.getRoleId():disposalNextSelectVO.getRoleIds();
        if(StringUtils.isEmpty(roleIds)){
            return new ArrayList<>();
        }
        return processInfoMapper.selectProcessListByRoleId(Arrays.asList(roleIds.split(",")));
    }

    @Override
    public PageInfo getDisposalListByIndex(DisposalOfIndexSelectVO disposalOfIndexSelectVO) {
        List<DisposalDetailsDTO> resultList = new ArrayList<>();
        //查询最近一次修改指标名称的时间
        IndexUpdateRecordPOJO lastUpdate = indexUpdateRecordMapper.selectLastUpdateByType(disposalOfIndexSelectVO.getIndexId(), IndexUpdateType.INDEX_UPDATE_TYPE_NAME.getType());
        //查询最近一次指标名称修改后的指标批示列表
        String updateTime = lastUpdate== null?null:lastUpdate.getUpdateTime();
        List<DisposalInfoPOJO> tempList = disposalInfoMapper.selectDisposalListByIndex(disposalOfIndexSelectVO.getIndexId(),disposalOfIndexSelectVO.getUserId(),updateTime);
        //如果有呈报，只获取最新的呈报的批示数据(从关联表中查询)
        List<String> tempIdList = new ArrayList<>();
        for(DisposalInfoPOJO pojo : tempList){
            tempIdList.add(pojo.getId());
        }
        PageInfo pageInfo = new PageInfo();
        if(tempIdList.size() > 0){
            tempList = disposalInfoMapper.getLastReportDisposal(tempIdList);
            for(DisposalInfoPOJO pojo : tempList){
                DisposalDetailsDTO dto = new DisposalDetailsDTO();
                BeanUtils.copyProperties(pojo,dto);
                resultList.add(dto);
            }
        }
        pageInfo.setList(resultList);
        return pageInfo;
    }

    @Override
    public DisposalStatusDTO isRoleCanDisposal(RoleIdVO roleIdVO) {
        DisposalStatusDTO disposalStatus = new DisposalStatusDTO();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return disposalStatus;
        }
        List<Long> roleIdList = Arrays.asList(loginUser.getSysUser().getRoleIds());
        List<String> roleIdStringList = roleIdList.stream().map(Object::toString).collect(Collectors.toList());
        disposalStatus = hasPermission(disposalStatus,roleIdStringList,loginUser.getUserid().toString(),roleIdVO.getIndexId());
        disposalStatus = isDisposaling(disposalStatus,loginUser.getUserid().toString(),roleIdVO.getIndexId());
        return disposalStatus;
    }

    /**
     * 用户是否有某指标的批示权限
     * @param disposalStatus 批示状态返回值
     * @param roleList 角色列表
     * @param userId 用户id
     * @param indexId 指标id
     * @return
     */
    private DisposalStatusDTO hasPermission(DisposalStatusDTO disposalStatus,List<String> roleList,String userId,String indexId){
        log.info("从填报服务查询用户是否有批示权限，roleList=" + JSONObject.toJSONString(roleList.toString()) + "---and indexId=" + JSONObject.toJSONString(indexId) );
        BaseResult result = cockpitSystemService.getPermissionsByUserId(roleList,indexId);
        log.info("从填报服务查询用户是否有批示权限，返回结果=" + JSON.toJSONString(result));
        Boolean hasPer = (Boolean)result.getData();
        if(!hasPer){
            disposalStatus.setCanDisposal(false);
        }else {
            //是否有批示权限
            Integer i = 0;
            if(roleList.size() > 0){
                i = processStepMapper.selectStepHasRoleIds(roleList);
            }
            if(i != null){
                disposalStatus.setCanDisposal(i > 0 ? true:false);
            }
        }
        return disposalStatus;
    }
    /**
     * 批示是否正在进行中
     * @param disposalStatus 批示状态返回值
     * @param userId 用户id
     * @param indexId 指标id
     * @return
     */
    private DisposalStatusDTO isDisposaling(DisposalStatusDTO disposalStatus,String userId,String indexId){
        //批示是否正在进行中
        List<DisposalFlowPOJO> temp = disposalFlowMapper.selectDisposalFlowByIndexAndRoleId(userId,indexId);
        List<IndexUpdateRecordPOJO> list = indexUpdateRecordMapper.selectLastUpdateByIndexId(indexId);
        //如果没有批示过，显示批示（false）
        if(temp.size() == 0){
            disposalStatus.setHasDisposal(false);
        }else {
            //如果批示过但没有更新过，显示已批示（true）
            if(list.size() == 0){
                disposalStatus.setHasDisposal(true);
            }else {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                try {
                    Date disposalDate = simpleDateFormat.parse(temp.get(0).getOperateTime());
                    Date updateDate = simpleDateFormat.parse(list.get(0).getUpdateTime());
                    //如果批示时间晚于更新时间，显示已批示
                    if(disposalDate.getTime() > updateDate.getTime()){
                        disposalStatus.setHasDisposal(true);
                    }else{
                        //如果批示时间早于更新时间，显示批示
                        disposalStatus.setHasDisposal(false);
                    }
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }
        }
        return disposalStatus;
    }

    @Override
    public Boolean isDisposalFlowing(ProcessIdVO disposalIdVO) {
        List<StepFlowInfo> list = disposalFlowMapper.selectFlowsByProcessId(disposalIdVO.getProcessId());
        return list.size()>0?true:false;
    }


    @Override
    public PageInfo getProcessStepListAll(DisposalListSelectVO disposalListSelectVO) {
        List<DisposalListDTO> result = disposalInfoMapper.getProcessStepListAll(disposalListSelectVO.getSourceName(),disposalListSelectVO.getUserName());
        PageInfo pageInfo = new PageInfo(result);
        //状态翻译
        for(DisposalListDTO disposalListDTO : result){
            if(disposalListDTO.getStatus() != null && disposalListDTO.getStatus() <= 2){
                //未完成
                disposalListDTO.setStatusStr("流转中");
            }else {
                //已完成
                disposalListDTO.setStatusStr("已完结");
            }
        }
        pageInfo.setList(result);
        return pageInfo;
    }

    @Override
    public DisposalDetailsDTO getDisposalDetailsPc(String disposalId) {
        DisposalDetailsDTO result = new DisposalDetailsDTO();
        //查询指标批示信息
        DisposalInfoPOJO pojo = disposalInfoMapper.selectByPrimaryKey(disposalId);
        if(pojo == null){
            return result;
        }
        BeanUtils.copyProperties(pojo,result);
        if(result.getStatus() != null && result.getStatus() <= 2){
            result.setStatusStr("流转中");
        }else {
            result.setStatusStr("已完结");
        }
        //查询，组装流转信息
        //先查询当前批示及后续批示的id列表
        List<String> disposalIdList = disposalFlowMapper.selectDisposalIdOfLink(disposalId);
        log.debug("根据批示id:" + disposalId + "，查询批示链条所有批示id为：：" + JSON.toJSONString(disposalIdList));
        if(disposalIdList!=null && disposalIdList.size() > 0){
            //查询这些批示的流转信息
            List<StepFlowInfo> stepFlowInfos = disposalFlowMapper.selectStepFlowsByDisposalId(disposalIdList);
            result.setFlowList(stepFlowInfos);
        }
        return result;
    }

    @Override
    @Transactional
    public Boolean readDisposal(DisposalOperateVO disposalOperateVO) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = simpleDateFormat.format(new Date());
        //记录操作信息
        DisposalFlowPOJO disposalFlowPOJO = new DisposalFlowPOJO();
        BeanUtils.copyProperties(disposalOperateVO,disposalFlowPOJO);
        String id = SnowflakeUtil.snowflake().nextIdStr();
        disposalFlowPOJO.setId(id);
        disposalFlowPOJO.setStatus(StepFlowStatus.STEP_FLOW_STATUS_COMPLETE.getStatus());
        disposalFlowPOJO.setOperateTime(now);
        disposalFlowPOJO.setOperateUserName(disposalOperateVO.getUserRealName());
        disposalFlowPOJO.setOperateUserId(disposalOperateVO.getUserId());
        disposalFlowPOJO.setType(ProcessOperateType.PROCESS_OPERATE_TYPE_READ.getType());
        disposalFlowMapper.insert(disposalFlowPOJO);
        //删除当前流程步骤的待办
        messageFeignService.deleteMessageByService(disposalOperateVO.getDisposalId()+"_"+disposalOperateVO.getProcessId()+"_"+disposalOperateVO.getStepId(),"zbps");
        //给发起人发消息
        DisposalInfoPOJO disposalInfoPOJO = disposalInfoMapper.selectByPrimaryKey(disposalOperateVO.getDisposalId());
        this.addMsg(SecurityUtils.getUsername(),"指标批示 批示反馈","您下发的批示任务有新的反馈，请及时查阅。",disposalInfoPOJO.getCreateUserId(),"1",disposalInfoPOJO.getId());
        return true;
    }

    @Override
    public Boolean reportDisposal(DisposalOperateVO disposalOperateVO,String host) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = simpleDateFormat.format(new Date());
        //记录操作信息
        recordStepFlowInfo(disposalOperateVO,4);
        //结束当前批示流程
        completeDisposal(disposalOperateVO.getDisposalId(), DisposalStatus.DISPOSAL_STATUS_REPORT.getStatus());
        //新开一个批示
        String id = SnowflakeUtil.snowflake().nextIdStr();
        disposalInfoMapper.insertNewByReportDisposal(id,disposalOperateVO.getDisposalId(),now,disposalOperateVO.getUserId(),disposalOperateVO.getUserRealName());
        //插入关系表
        String id1 = SnowflakeUtil.snowflake().nextIdStr();
        DisposalProcessLinkPOJO disposalProcessLink = disposalInfoMapper.selectLastLinkByDisposalId(disposalOperateVO.getDisposalId());
        if(disposalProcessLink!= null){
            disposalInfoMapper.insertDisposalProcessLink(id1,id,disposalOperateVO.getDisposalId(),disposalOperateVO.getReportProcessId(), DisposalSourceType.DISPOSAL_SOURCE_REPORT.getType(),disposalProcessLink.getOriginDisposalId());
        }else{
            disposalInfoMapper.insertDisposalProcessLinkFirst(id1,id,disposalOperateVO.getReportProcessId(), DisposalSourceType.DISPOSAL_SOURCE_REPORT.getType(),1,id);
        }
        //新增一个待办流转信息
        ProcessStepInfoPOJO processStepInfo = processStepMapper.selectDisposalStepByProcessId(disposalOperateVO.getReportProcessId());
        DisposalInfoPOJO disposalInfoPOJO = disposalInfoMapper.selectByPrimaryKey(disposalOperateVO.getDisposalId());
        createDisposalFlow(host,disposalInfoPOJO.getTitle(),id,disposalOperateVO.getReportProcessId(),processStepInfo.getId(),processStepInfo.getRoleIds(),
                processStepInfo.getRoleNames(),disposalOperateVO.getUserId(),disposalOperateVO.getUserRealName(),
                disposalOperateVO.getContext(),StepFlowStatus.STEP_FLOW_STATUS_WAIT.getStatus());

        //删除当前流程步骤的待办
        messageFeignService.deleteMessageByService(disposalOperateVO.getDisposalId()+"_"+disposalOperateVO.getProcessId()+"_"+disposalOperateVO.getStepId(),"zbps");
        //给发起人发消息
        this.addMsg(SecurityUtils.getUsername(),"指标批示 批示反馈","您下发的批示任务有新的反馈，请及时查阅。",disposalInfoPOJO.getCreateUserId(),"1",disposalInfoPOJO.getId());
        return true;
    }

    @Override
    public Boolean turnDisposal(DisposalOperateVO disposalOperateVO,String host) {
        //记录操作信息
        recordStepFlowInfo(disposalOperateVO,ProcessOperateType.PROCESS_OPERATE_TYPE_DO.getType());
        DisposalInfoPOJO disposalInfoPOJO = disposalInfoMapper.selectByPrimaryKey(disposalOperateVO.getDisposalId());
        //新增下一步流转信息
        createNextFlow(host,disposalInfoPOJO.getTitle(),disposalOperateVO.getDisposalId(),disposalOperateVO.getProcessId(),
                disposalOperateVO.getStepId(),disposalOperateVO.getTurnRoleId(),disposalOperateVO.getTurnRoleName(),null);
        //是否结束当前流程
        if(isProcessComplete(disposalOperateVO.getProcessId(),disposalOperateVO.getStepId())){
            completeDisposal(disposalOperateVO.getDisposalId());
            this.deleteSyMsg(disposalOperateVO.getDisposalId(),disposalOperateVO.getDisposalId());
        }
        //删除当前流程步骤的待办
        messageFeignService.deleteMessageByService(disposalOperateVO.getDisposalId()+"_"+disposalOperateVO.getProcessId()+"_"+disposalOperateVO.getStepId(),"zbps");
        //给发起人发消息
        this.addMsg(SecurityUtils.getUsername(),"指标批示 批示反馈","您下发的批示任务有新的反馈，请及时查阅。",disposalInfoPOJO.getCreateUserId(),"1",disposalInfoPOJO.getId());
        return true;
    }
    private void deleteSyMsg(String disposalId,String processId){
        //查询本流程审阅步骤id
        List<String> syIds = processStepMapper.getSyStepIdByProcessId(processId);
        for(String stepId : syIds){
            //删除当前流程步骤的待办
            messageFeignService.deleteMessageByService(disposalId+"_"+processId+"_"+stepId,"zbps");
        }
    }

    private void addMsg(String creatorName, String title, String content, String userId, String appMsgType,String reportId) {
        RemindInfoDto remind = new RemindInfoDto();
        remind.setRemindTitle(title);
        remind.setIsRead((byte) 0);
        remind.setRemindedType((byte) 2);
        remind.setIsDelete((byte) 0);
        remind.setCreater(creatorName);
        remind.setCreateTime(DateUtils.dateTimeIntact());
        remind.setRemindContent(content);
        remind.setLevel("3");
        remind.setPcUserId(null);
        remind.setAppUserId(userId);
        remind.setServiceId(reportId);
        remind.setServiceType("zbps");
        remind.setSourceType("APP");
        remind.setAppMsgType(appMsgType);
        remind.setMessageUrl(baseMessageUrl + "ldps");
        log.info("发送app消息ids:----"+remind.getAppUserId());
        BaseResult result = messageFeignService.addMsg(Collections.singletonList(remind));
        log.info("发送app消息result:----"+result.getCode());
        log.info("发送app消息result:----"+result.getMsg());
    }

    @Override
    @Transactional(rollbackFor={RuntimeException.class, Exception.class})
    public Boolean indexDisposal(IndexDisposalVO indexDisposalVO, String host) throws Exception{
        DisposalInfoPOJO pojo = new DisposalInfoPOJO();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = simpleDateFormat.format(new Date());
        //根据用户查询角色信息
        RoleInfo roleInfo = getRoleInfoByUserId(indexDisposalVO.getUserId());
        String roleName = StringUtils.isEmpty(indexDisposalVO.getCurrentRoleNames())?roleInfo.getRoleName():indexDisposalVO.getCurrentRoleNames();
        String roleIds = StringUtils.isEmpty(indexDisposalVO.getCurrentRoleIds())?roleInfo.getRoleId():indexDisposalVO.getCurrentRoleIds();
        //是否已插入待办批示信息
        List<DisposalFlowPOJO> disposalFlowList = disposalFlowMapper.selectDisposalFlowByIndex(indexDisposalVO.getIndexId(),indexDisposalVO.getProcessId());
        if(disposalFlowList.size() > 0){
            //更新状态为已办
            for(DisposalFlowPOJO disposalFlow:disposalFlowList){
                disposalFlowMapper.updateFlowInfoByDisposal(disposalFlow.getDisposalId(),disposalFlow.getProcessId(),disposalFlow.getStepId(),
                        indexDisposalVO.getUserId(),indexDisposalVO.getUserRealName(),indexDisposalVO.getContext(),now,
                        StepFlowStatus.STEP_FLOW_STATUS_COMPLETE.getStatus(),ProcessOperateType.PROCESS_OPERATE_TYPE_DISPOSAL.getType());

                DisposalInfoPOJO disposalInfoPOJO = disposalInfoMapper.selectByPrimaryKey(disposalFlow.getDisposalId());
                //修改批示信息中
                //disposalInfoPOJO.setContent(indexDisposalVO.getContext());
                //disposalInfoMapper.updateByPrimaryKey(disposalInfoPOJO);
                createNextFlow(host,disposalInfoPOJO.getTitle(),disposalFlow.getDisposalId(),disposalFlow.getProcessId(),disposalFlow.getStepId(),
                        indexDisposalVO.getRoleId(),indexDisposalVO.getRoleName(),null);
                //是否结束当前流程
                if(isProcessComplete(disposalFlow.getProcessId(),disposalFlow.getStepId())){
                    completeDisposal(disposalFlow.getDisposalId());
                }
            }
        }else {
            //新增批示信息
            String disposalId = SnowflakeUtil.snowflake().nextIdStr();
            pojo.setId(disposalId);
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append(indexDisposalVO.getUserRealName()).append("关于").append(indexDisposalVO.getIndexName()).append("的批示");
            pojo.setTitle(stringBuffer.toString());
            pojo.setSourceName(indexDisposalVO.getSourceName());
            pojo.setIndexId(indexDisposalVO.getIndexId());
            pojo.setIndexName(indexDisposalVO.getIndexName());
            pojo.setContent(indexDisposalVO.getContext());
            pojo.setImageUrl(indexDisposalVO.getImageUrl());
            pojo.setStatus(DisposalStatus.DISPOSAL_STATUS_FLOW.getStatus());
            pojo.setCreateTime(now);
            pojo.setCreateUserId(indexDisposalVO.getUserId());
            pojo.setCreateUserName(indexDisposalVO.getUserRealName());
            disposalInfoMapper.insert(pojo);
            //插入批示和流程关系表
            String linkId = SnowflakeUtil.snowflake().nextIdStr();
            disposalInfoMapper.insertDisposalProcessLinkFirst(linkId,disposalId,indexDisposalVO.getProcessId(),DisposalSourceType.DISPOSAL_SOURCE_CREAT.getType(),1,disposalId);
            //新增第一步流转信息（已完成状态）
            ProcessStepInfoPOJO processStepInfo = getDisposalStepId(indexDisposalVO.getProcessId());
            if(processStepInfo == null || processStepInfo.getId()==null){
                throw new Exception("流程步骤不存在");
            }
            createDisposalFlow(null,null,disposalId,indexDisposalVO.getProcessId(),processStepInfo.getId(),roleIds,roleName,indexDisposalVO.getUserId(),indexDisposalVO.getUserRealName(),indexDisposalVO.getContext(),StepFlowStatus.STEP_FLOW_STATUS_COMPLETE.getStatus());
            //新增下一步流转信息
            createNextFlow(host,stringBuffer.toString(),disposalId,indexDisposalVO.getProcessId(),processStepInfo.getId(),indexDisposalVO.getRoleId(),indexDisposalVO.getRoleName(),null);
            //是否结束当前流程
            if(isProcessComplete(indexDisposalVO.getProcessId(),processStepInfo.getId())){
                completeDisposal(disposalId);
            }
        }
        return true;
    }

    @Override
    public String canRollBackDisposal(DisposalOperateVO disposalOperateVO) {
        //查询当前步骤信息
//        DisposalFlowPOJO disposalFlowPOJO = disposalFlowMapper.selectRoleLastFlow(disposalOperateVO.getId(),Arrays.asList(disposalOperateVO.getRoleId().split(",")));
        DisposalFlowPOJO disposalFlowPOJO = disposalFlowMapper.selectUserLastFlow(disposalOperateVO.getId(),disposalOperateVO.getUserId());
        if(disposalFlowPOJO == null){
            return "当前流程不存在";
        }
        if(disposalFlowPOJO.getType()==ProcessOperateType.PROCESS_OPERATE_TYPE_COMPLETE.getType()){
            return "当前批示已结束";
        }
        //是否是呈报
        if(disposalFlowPOJO.getType()!=null && disposalFlowPOJO.getType() == 4){
            //查询呈报后的新批示列表
            List<DisposalProcessLinkPOJO> list = disposalFlowMapper.selectReportedDisposalList(disposalOperateVO.getId());
            int j = 0;
            //查找列表中，与当前批示id相等的数据
            for(int i = 0 ; i < list.size(); i ++){
                DisposalProcessLinkPOJO pojo = list.get(i);
                if(pojo.getDisposalId().equals(disposalOperateVO.getDisposalId())){
                    j = i + 1;
                }
            }
            List<String> disposalIdList = new ArrayList<>();
            //如果有下一步的呈报批示
            if(list.size() > j){
                for(int i = j ; i < list.size(); i ++ ){
                    disposalIdList.add(list.get(i).getDisposalId());
                }
                DisposalProcessLinkPOJO pojo = list.get(j);
                //查询新批示的流程流转信息
                List<DisposalFlowPOJO> tempList = disposalFlowMapper.selectNextDisposalStep(pojo.getDisposalId(),pojo.getProcessId());
                boolean flag = false;
                //是否有已完成的流程
                if(tempList != null && tempList.size() > 0){
                    for(DisposalFlowPOJO pojo1:tempList){
                        if(pojo1.getStatus().equals(StepFlowStatus.STEP_FLOW_STATUS_COMPLETE.getStatus())){
                            flag = true;
                            break;
                        }
                    }
                }
                //如果有已完成的流程，不允许撤回
                if(flag){
                    return "已有后续流程，不允许撤回";
                }
            }
        }else {
            //非呈报情况
            DisposalInfoPOJO disposalInfoPOJO = disposalInfoMapper.selectByPrimaryKey(disposalOperateVO.getDisposalId());
            if(disposalInfoPOJO == null || disposalInfoPOJO.getStatus() != DisposalStatus.DISPOSAL_STATUS_FLOW.getStatus()){
                return "当前批示已结束";
            }
            //这条记录后面的操作记录
            List<DisposalFlowPOJO> tempList1 = disposalFlowMapper.selectNextFlowInfo(disposalFlowPOJO.getId());
            if(tempList1 != null && tempList1.size() > 0){
                for(DisposalFlowPOJO pojo : tempList1){
                    //如果有已完成的，不允许撤回
                    if(pojo.getStatus() == StepFlowStatus.STEP_FLOW_STATUS_COMPLETE.getStatus()){
                        return "已有后续流程，不允许撤回";
                    }
                }
            }
        }
        return "true";
    }

    @Override
    @Transactional
    public Boolean rollBackDisposal(DisposalOperateVO disposalOperateVO) {
        //查询当前步骤信息
        //TODO 撤回是否能提供撤回的flowId，步骤id
        DisposalFlowPOJO disposalFlowPOJO = disposalFlowMapper.selectUserLastFlow(disposalOperateVO.getId(),disposalOperateVO.getUserId());
        if(disposalFlowPOJO == null || disposalFlowPOJO.getType()==ProcessOperateType.PROCESS_OPERATE_TYPE_COMPLETE.getType()){
            return false;
        }
        //是否是呈报
        if(disposalFlowPOJO.getType()!=null && disposalFlowPOJO.getType() == 4){
            //查询呈报后的新批示列表
            List<DisposalProcessLinkPOJO> list = disposalFlowMapper.selectReportedDisposalList(disposalOperateVO.getId());
            int j = 0;
            //查找列表中，与当前批示id相等的数据
            for(int i = 0 ; i < list.size(); i ++){
                DisposalProcessLinkPOJO pojo = list.get(i);
                if(pojo.getDisposalId().equals(disposalOperateVO.getDisposalId())){
                    j = i + 1;
                }
            }
            List<String> disposalIdList = new ArrayList<>();
            //如果有下一步的呈报批示
            if(list.size() > j){
                for(int i = j ; i < list.size(); i ++ ){
                    disposalIdList.add(list.get(i).getDisposalId());
                }
                DisposalProcessLinkPOJO pojo = list.get(j);
                //查询新批示的流程流转信息
                List<DisposalFlowPOJO> tempList = disposalFlowMapper.selectNextDisposalStep(pojo.getDisposalId(),pojo.getProcessId());
                boolean flag = false;
                //是否有已完成的流程
                if(tempList != null && tempList.size() > 0){
                    for(DisposalFlowPOJO pojo1:tempList){
                        if(pojo1.getStatus().equals(StepFlowStatus.STEP_FLOW_STATUS_COMPLETE.getStatus())){
                            flag = true;
                            break;
                        }
                    }
                }
                //如果有已完成的流程，不允许撤回
                if(flag){
                    return false;
                }
            }
            //查询呈报后的流程id
            String nextId = disposalFlowMapper.selectDisposalLinkNext(disposalOperateVO.getId());
            List<String> nextIdList = new ArrayList<>();
            nextIdList.add(nextId);

            if(disposalIdList.size() > 0 ){
                //删除新批示
                disposalFlowMapper.deleteNextDisposalAll(disposalIdList);
                //删除新批示与流程关系
                disposalFlowMapper.deleteNextDisposalLinkAll(disposalIdList);
                //删除新批示步骤
                disposalFlowMapper.deleteNextDisposalStepAll(disposalIdList);
            }
            //还原未执行记录
            disposalFlowMapper.rollBackStep(disposalFlowPOJO.getId());
            //createrollBackStep(disposalFlowPOJO,null);
            //修改原批示的状态为流转中
            List<String> idList = new ArrayList<>();
            idList.add(disposalOperateVO.getId());
            disposalInfoMapper.updateStatusById(idList,DisposalStatus.DISPOSAL_STATUS_FLOW.getStatus());
            //删除当前步骤后的步骤
//            List<DisposalFlowPOJO> flowInfoList = disposalFlowMapper.selectNextFlowInfo(disposalFlowPOJO.getId());
//            for(DisposalFlowPOJO pojo : flowInfoList){
//                disposalFlowMapper.deleteByPrimaryKey(pojo.getId());
//            }
            disposalFlowMapper.deleteNextDisposalStepAll(nextIdList);
        }else {
            //非呈报情况
            DisposalInfoPOJO disposalInfoPOJO = disposalInfoMapper.selectByPrimaryKey(disposalOperateVO.getDisposalId());
            if(disposalInfoPOJO == null || disposalInfoPOJO.getStatus() != DisposalStatus.DISPOSAL_STATUS_FLOW.getStatus()){
                return false;
            }
            //这条记录后面的操作记录
            List<DisposalFlowPOJO> tempList1 = disposalFlowMapper.selectNextFlowInfo(disposalFlowPOJO.getId());
            if(tempList1 != null && tempList1.size() > 0){
                for(DisposalFlowPOJO pojo : tempList1){
                    //如果有已完成的，不允许撤回
                    if(pojo.getStatus() == StepFlowStatus.STEP_FLOW_STATUS_COMPLETE.getStatus()){
                        return false;
                    }
                }
            }
            //撤回操作
            switch (ProcessOperateType.getByType(disposalFlowPOJO.getType())){
                case PROCESS_OPERATE_TYPE_READ:
                    //如果是审阅，删除flow表记录
                    disposalFlowMapper.deleteByPrimaryKey(disposalFlowPOJO.getId());
                    //记录撤回操作信息
                    //createRollBackFlow(disposalFlowPOJO);
                    break;
                case PROCESS_OPERATE_TYPE_DO:
                    //还原未完成flow表记录
                    disposalFlowMapper.rollBackStep(disposalFlowPOJO.getId());
                    //删除下一条的待办记录
                    List<DisposalFlowPOJO> flowInfoList = disposalFlowMapper.selectNextFlowInfo(disposalFlowPOJO.getId());
                    for(DisposalFlowPOJO pojo : flowInfoList){
                        disposalFlowMapper.deleteByPrimaryKey(pojo.getId());
                    }
                    break;
                case PROCESS_OPERATE_TYPE_DISPOSAL:
                    List<String> disposalIds = new ArrayList<>();
                    disposalIds.add(disposalOperateVO.getDisposalId());
                    if(disposalIds.size() > 0){
                        //需要判断是否是呈报的，
                        Integer sourceType = disposalInfoMapper.getSourceTypeByDisposalId(disposalInfoPOJO.getId());
                        if(String.valueOf(DisposalSourceType.DISPOSAL_SOURCE_CREAT.getType()).equals(String.valueOf(sourceType))){
                            //记录撤回操作信息
                            //createRollBackFlow(disposalFlowPOJO);
                            //撤回批示
                            disposalFlowMapper.deleteNextDisposalAll(disposalIds);
                            disposalFlowMapper.deleteNextDisposalLinkAll(disposalIds);
                            disposalFlowMapper.deleteNextDisposalStepAll(disposalIds);
                        }else {
                            //记录撤回操作信息
                            //createRollBackFlow(disposalFlowPOJO);
                            //如果是呈报的，删除下一步待办，将当前批示新建为待办
                            disposalFlowMapper.rollBackReportDispoal(disposalFlowPOJO.getId());
                            //createrollBackStep(disposalFlowPOJO,disposalFlowPOJO.getType());
                            //删除下一条的待办记录
                            flowInfoList = disposalFlowMapper.selectNextFlowInfo(disposalFlowPOJO.getId());
                            for(DisposalFlowPOJO pojo : flowInfoList){
                                disposalFlowMapper.deleteByPrimaryKey(pojo.getId());
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
        }
        return true;
    }

    /**
     * 新增撤回操作记录
     * @param disposalFlowPOJO
     */
    private void createRollBackFlow(DisposalFlowPOJO disposalFlowPOJO){
        String id = SnowflakeUtil.snowflake().nextIdStr();
        disposalFlowPOJO.setId(id);
        disposalFlowPOJO.setStatus(StepFlowStatus.STEP_FLOW_STATUS_ROLLBACK.getStatus());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = simpleDateFormat.format(new Date());
        disposalFlowPOJO.setOperateTime(now);
        disposalFlowMapper.insert(disposalFlowPOJO);
    }

    /**
     * 新增撤回的待办记录
     * @param disposalFlowPOJO
     */
    private void createrollBackStep(DisposalFlowPOJO disposalFlowPOJO,Integer type){
        String id = SnowflakeUtil.snowflake().nextIdStr();
        disposalFlowPOJO.setId(id);
        disposalFlowPOJO.setStatus(StepFlowStatus.STEP_FLOW_STATUS_WAIT.getStatus());
        disposalFlowPOJO.setType(type);
        disposalFlowPOJO.setContext(null);
        disposalFlowPOJO.setOperateUserId(null);
        disposalFlowPOJO.setOperateUserName(null);
        disposalFlowPOJO.setOperateTime(null);
        disposalFlowMapper.insert(disposalFlowPOJO);
    }


    /**
     * 验证是否完成
     */
    private Boolean isProcessComplete(String processId,String stepId){
        ProcessStepInfoPOJO pojo;
        if(StringUtils.isEmpty(stepId)){
            pojo = processStepMapper.selectNextStepByProcessIdNotRead(processId);
        }else {
            pojo = processStepMapper.selectNextStepNotRead(processId,stepId);
        }
        if(pojo!=null && pojo.getOperateType()!=null && ProcessOperateType.PROCESS_OPERATE_TYPE_COMPLETE.getType().equals(pojo.getOperateType())){
            return true;
        }else {
            return false;
        }
    }
    /**
     * 处理完成逻辑
     */
    private Boolean completeDisposal(String disposalId){
        updateDisposalStatus(disposalId,DisposalStatus.DISPOSAL_STATUS_END.getStatus());
        disposalInfoMapper.updateStepFlowEnd(disposalId,StepFlowStatus.STEP_FLOW_STATUS_COMPLETE.getStatus());
        return true;
    }
    /**
     * 处理完成逻辑
     */
    private Boolean completeDisposal(String disposalId,Integer status){
        updateDisposalStatus(disposalId,status);
        disposalInfoMapper.updateStepFlowEnd(disposalId,StepFlowStatus.STEP_FLOW_STATUS_COMPLETE.getStatus());
        return true;
    }

    /**
     * 更新批示状态
     */
    private Boolean updateDisposalStatus(String disposalId,int status){
        List<String> disposalIdList = disposalFlowMapper.selectDisposalIdOfLinkBefore(disposalId);
        disposalInfoMapper.updateStatusById(disposalIdList,status);
        return true;
    }

    /**
     * 记录步骤流转信息
     */
    private void recordStepFlowInfo(DisposalOperateVO disposalOperateVO,Integer type){
        recordStepFlowInfo(disposalOperateVO.getDisposalId(),disposalOperateVO.getProcessId(),disposalOperateVO.getStepId(),
                disposalOperateVO.getUserId(),disposalOperateVO.getUserRealName(),disposalOperateVO.getContext(),type);
    }

    /**
     * 记录步骤流转信息
     */
    private void recordStepFlowInfo(String disposalId,String processId,String stepId,String userId,String userRealName,String context,Integer type){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = simpleDateFormat.format(new Date());
        int status = StepFlowStatus.STEP_FLOW_STATUS_COMPLETE.getStatus();
        disposalFlowMapper.updateFlowInfoByDisposal(disposalId,processId,stepId,userId,userRealName,context,now,status,type);
    }

    /**
     * 查询第一步的步骤id
     */
    private ProcessStepInfoPOJO getDisposalStepId(String processId){
        List<ProcessStepInfoPOJO> list = processStepMapper.selectStepInfoAll(processId);

        if(list.size() > 0){
            ProcessStepInfoPOJO pojo = list.get(0);
            if(pojo.getOperateType() == ProcessOperateType.PROCESS_OPERATE_TYPE_DISPOSAL.getType()){
                return pojo;
            }
        }
        return null;
    }

    /**
     * 创建批示流转信息
     */
    private void createDisposalFlow(String host,String title,String disposalId,String processId,String stepId,String roleId,
                                    String roleName,String userId, String userName,String context,Integer status){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = simpleDateFormat.format(new Date());
        DisposalFlowPOJO pojo = new DisposalFlowPOJO();
        String id = SnowflakeUtil.snowflake().nextIdStr();
        pojo.setId(id);
        pojo.setDisposalId(disposalId);
        pojo.setProcessId(processId);
        pojo.setStepId(stepId);
        pojo.setRoleId(roleId);
        pojo.setRoleName(roleName);
        pojo.setOperateTime(now);
        pojo.setOperateUserId(userId);
        pojo.setOperateUserName(userName);
        pojo.setContext(context);
        pojo.setStatus(status);
        pojo.setType(ProcessOperateType.PROCESS_OPERATE_TYPE_DISPOSAL.getType());
        disposalFlowMapper.insert(pojo);
        if(host != null){
            sendDingNotice(roleId,"您收到一条新的指标批示任务，请及时处理。",host,disposalId+"_"+processId+"_"+stepId,"2");
        }
    }

    /**
     * 创建下一步步骤流转信息
     */
    private void createNextFlow(String host,String title,String disposalId,String processId,String stepId,String roleId,String roleName,Integer type){
        createNextFlow(host,title,disposalId,processId,stepId,roleId,roleName,StepFlowStatus.STEP_FLOW_STATUS_WAIT.getStatus(),type);
    }

    /**
     * 创建下一步步骤流转信息
     */
    private void createNextFlow(String host,String title,String disposalId,String processId,String stepId,String roleId,String roleName,int status,Integer type){
        DisposalFlowPOJO pojo = new DisposalFlowPOJO();
        ProcessStepInfoPOJO processStepInfo = new ProcessStepInfoPOJO();
        processStepInfo.setOperateType(5);
        processStepInfo.setProcessId(processId);
        processStepInfo.setId(stepId);
        //跳过操作类型是审阅的
        int i = 0;
        while(processStepInfo.getOperateType() >= ProcessOperateType.PROCESS_OPERATE_TYPE_READ.getType() ){
            //审阅步骤，需要发消息
            if(i > 0){
                sendDingNotice(processStepInfo.getRoleIds(),"您收到一条新的指标批示任务，请及时处理。",host,disposalId+"_"+processStepInfo.getProcessId()+"_"+processStepInfo.getId(),"2");
                //新增一个待办流转信息
                String id = SnowflakeUtil.snowflake().nextIdStr();
                pojo.setId(id);
                pojo.setDisposalId(disposalId);
                pojo.setProcessId(processId);
                pojo.setStepId(processStepInfo.getId());
                pojo.setRoleId(processStepInfo.getRoleIds());
                pojo.setRoleName(processStepInfo.getRoleNames());
                pojo.setStatus(status);
                pojo.setType(ProcessOperateType.PROCESS_OPERATE_TYPE_READ.getType());
                disposalFlowMapper.insert(pojo);
            }
            processStepInfo = selectNextStepInfo(processStepInfo.getProcessId(),processStepInfo.getId());
            //操作类型是完成的直接返回
            if(processStepInfo == null || ProcessOperateType.PROCESS_OPERATE_TYPE_COMPLETE.getType().equals(processStepInfo.getOperateType())){
                return;
            }
            i ++ ;
        }
        if(StringUtils.isEmpty(roleId)){
            roleId = processStepInfo.getRoleIds().split(",")[0];
            roleName = processStepInfo.getRoleNames().split(",")[0];
        }
        String id = SnowflakeUtil.snowflake().nextIdStr();
        pojo.setId(id);
        pojo.setDisposalId(disposalId);
        pojo.setProcessId(processId);
        pojo.setStepId(processStepInfo.getId());
        pojo.setRoleId(roleId);
        pojo.setRoleName(roleName);
        pojo.setStatus(status);
        pojo.setType(type);
        disposalFlowMapper.insert(pojo);

        sendDingNotice(roleId,"您收到一条新的指标批示任务，请及时处理。",host,disposalId+"_"+processStepInfo.getProcessId()+"_"+processStepInfo.getId(),"2");

    }

    //发送钉钉消息
    private void sendDingNotice(String roleId,String title,String host,String serviceId,String appMsgType) {
        DisposalNoticeVO disposalNoticeVO = new DisposalNoticeVO();
        StringBuilder content = new StringBuilder();
        content.append(title);
        disposalNoticeVO.setRoleIds(roleId);
        disposalNoticeVO.setMessage(content.toString());
        disposalNoticeVO.setHost(host);
        disposalNoticeVO.setAppMsgType(appMsgType);
        disposalNoticeVO.setServiceId(serviceId);
        log.info("调用钉钉端，发送通知：：" + JSON.toJSONString(disposalNoticeVO));
        cockpitSystemService.sendDisposalNotice(disposalNoticeVO);
    }

    /**
     * 查询流程下一步信息
     */
    private ProcessStepInfoPOJO selectNextStepInfo(String processId, String stepId){
        if(StringUtils.isEmpty(stepId)){
            return processStepMapper.selectNextStepByProcessId(processId);
        }
        return processStepMapper.selectNextStep(processId,stepId);
    }
    /**
     * 根据用户id查询角色信息
     */
    private RoleInfo getRoleInfoByUserId(String userId){
        RoleInfo roleInfo = new RoleInfo();
        roleInfo.setRoleId("1");
        roleInfo.setRoleName("一级权限");
        return roleInfo;
    }
}