server:
  port: 8080
# spring配置
spring:
  servlet:
    multipart:
      # 根据实际需求作调整
      max-file-size: 10MB
      # 默认最大请求大小为10M，总上传的数据大小
      max-request-size: 15MB
  main:
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  redis:
    host: onecity-os-redis
#    host: *************
    port: 6379
#    port: 30425
    password: DONT(QkOmrw81EnDa0CDf9y1hywwU2sWxykwRkIHa8ZXhBvSUmcPtXUJerFvBv/77k4AxY40uLtxHEcjv86VWFXHNoPxPcGIgG6n10aMBOlsrWLwWZAjlOWr3N4JrG04kk+fPG+57s7x6Zu6algBVF8FWVJfIxkMWMIhU0IZGZ5njtok=)
  datasource:
    druid:
      initialSize: 5
      minIdle: 5
      maxActive: 20
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      filters: stat,slf4j
      connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      druid:
        initialSize: 5
        minIdle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
          # 主库数据源
          master:
            driver-class-name: com.mysql.cj.jdbc.Driver
#            url: jdbc:mysql://*************:31286/service-disposal?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
#            url: jdbc:mysql://*************:31403/service-disposal?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
            url: ****************************************************************************************************************************************************************
            username: root
            password: DONT(QkOmrw81EnDa0CDf9y1hywwU2sWxykwRkIHa8ZXhBvSUmcPtXUJerFvBv/77k4AxY40uLtxHEcjv86VWFXHNoPxPcGIgG6n10aMBOlsrWLwWZAjlOWr3N4JrG04kk+fPG+57s7x6Zu6algBVF8FWVJfIxkMWMIhU0IZGZ5njtok=)
          # 从库数据源
          # slave:
            # username:
            # password:
            # url:
            # driver-class-name:
      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭
# seata配置
seata:
  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启
  enabled: false
  # Seata 应用编号，默认为 ${spring.application.name}
  application-id: ${spring.application.name}
  # Seata 事务组编号，用于 TC 集群名
  tx-service-group: ${spring.application.name}-group
  # 关闭自动代理
  enable-auto-data-source-proxy: false
  # 服务配置项
  service:
    # 虚拟组和分组的映射
    vgroup-mapping:
      ruoyi-system-group: default
  config:
    type: nacos
    nacos:
#      serverAddr: *************:30309
      serverAddr: onecity-os-nacos:8848
      group: SEATA_GROUP
      namespace:
  registry:
    type: nacos
    nacos:
      application: seata-server
#      server-addr: *************:30309
      server-addr: onecity-os-nacos:8848
      namespace:

# mybatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.onecity.os.disposal
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath:mapper/**/*.xml
#Mybatis输出sql日志
logging:
  level:
    com.onecity.os.disposal.mapper: debug
# swagger配置
swagger:
  title: notice模块接口文档
  license: Powered By ruoyi
  licenseUrl: https://ruoyi.vip

# fdfs配置
fdfs:
  soTimeout: 1500 #socket连接超时时长
  connectTimeout: 600 #连接tracker服务器超时时长
  thumbImage: #缩略图生成参数，可选
   width: 150
   height: 150
  web-server-url: onecity-os-fastdfs-storage:22122
#  web-server-url: *************:31968
  trackerList: #TrackerList参数,支持多个，我这里只有一个，如果有多个在下方加- x.x.x.x:port
  - onecity-os-fastdfs-tracker:22122
#  - *************:31566
springfox:
  documentation:
    # 总开关（同时设置auto-startup=false，否则/v3/api-docs等接口仍能继续访问）
    enabled: true
    auto-startup: true
    swagger-ui:
      enabled: true
app:
  baseMessageUrl: http://************:32753/app-portal/app/#/