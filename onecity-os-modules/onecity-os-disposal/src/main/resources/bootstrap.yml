# Spring
spring: 
  application:
    # 应用名称
    name: onecity-os-disposal
  profiles:
    # 环境配置
    active: dev
  jmx:
    enabled: false
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: onecity-os-nacos:8848
#        server-addr: 192.168.108.1:30309
      config:
        # 配置中心地址
        server-addr: onecity-os-nacos:8848
#        server-addr: 192.168.108.1:30309
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}