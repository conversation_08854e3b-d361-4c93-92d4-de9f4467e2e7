<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.disposal.mapper.DisposalInfoMapper">

    <select id="selectDisposalWaitByRoleId" resultType="com.onecity.os.disposal.model.po.DisposalInfoPOJO">
        select distinct A.id as id, A.title,A.createTime,A.imageUrl,A.content,A.createUserId,
        group_concat(A.operateType) as operateType,group_concat(A.operateName) as operateName from (
        select di.id as id,di.disposal_title as title,di.create_time as createTime,di.image_url as imageUrl,
        di.disposal_content as content,di.create_user_id as createUserId,psi.operate_type as operateType,psi.operate_name as operateName
        from disposal_info di left join disposal_process_link dpl on di.id=dpl.disposal_id
        left join process_step_info psi on psi.process_id = dpl.process_id
        where di.status = 1 and (psi.operate_type = 0 or psi.operate_type = 2)
        <foreach collection="roleIdList" item="roleId" separator="+" open="and (" close=") > 0">
            find_in_set(#{roleId}, psi.role_ids)
        </foreach>
        union all
        select di.id,di.disposal_title as title,di.create_time as createTime,di.image_url as imageUrl,
        di.disposal_content as content,di.create_user_id as createUserId,psi.operate_type as operateType,psi.operate_name as operateName
        from disposal_info di left join disposal_flow dw on di.id=dw.disposal_id
        left join process_step_info psi on psi.id = dw.step_id where dw.status=0 and dw.role_id in
        <foreach collection="roleIdList" item="roleId" separator="," open="(" close=")">
            #{roleId}
        </foreach>) A group by A.id , A.title,A.createTime,A.imageUrl,A.content order by A.createTime desc
    </select>

    <select id="selectDisposalApplyByUserId" resultType="com.onecity.os.disposal.model.po.DisposalInfoPOJO">
        select di.id as id,di.disposal_title as title,di.create_time as createTime,
        di.disposal_content as content, di.status from disposal_info di left join disposal_process_link dpl on di.id=dpl.disposal_id
        where dpl.source_type = 0 and di.create_user_id = #{userId}
        order by di.create_time desc
    </select>

    <select id="selectDisposalReceiveByRoleId" resultType="com.onecity.os.disposal.model.po.DisposalInfoPOJO">
        select distinct A.id as id, A.title,A.createTime,A.imageUrl,A.content,A.createUserId,A.status,
        group_concat(A.operateType) as operateType,group_concat(A.operateName) as operateName from (
        select di.id,di.disposal_title as title,di.create_time as createTime,di.image_url as imageUrl,di.status as status,
        di.disposal_content as content,di.create_user_id as createUserId,psi.operate_type as operateType,psi.operate_name as operateName
        from disposal_info di left join disposal_process_link dpl on di.id=dpl.disposal_id
        left join process_step_info psi on psi.process_id = dpl.process_id
        where dpl.source_type = 2 and psi.operate_type = 0
        <foreach collection="roleIdList" item="roleId" separator="+" open="and (" close=") > 0">
            find_in_set(#{roleId}, psi.role_ids)
        </foreach>
        union all
        select di.id,di.disposal_title as title,di.create_time as createTime,di.image_url as imageUrl,di.status as status,
        di.disposal_content as content,di.create_user_id as createUserId,psi.operate_type as operateType,psi.operate_name as operateName
        from disposal_info di left join disposal_flow dw on di.id=dw.disposal_id
        left join process_step_info psi on psi.id = dw.step_id
        where (dw.type != 0 or dw.type is null)
        <foreach collection="roleIdList" item="roleId" separator="+" open="and (" close=") > 0">
            find_in_set(#{roleId}, dw.role_id)
        </foreach>
        ) A
        group by A.id , A.title,A.createTime,A.imageUrl,A.content order by A.createTime desc
    </select>



    <select id="selectDisposalCompleteByRoleId" resultType="com.onecity.os.disposal.model.po.DisposalInfoPOJO">
        select distinct A.id as id, A.title,A.createTime,A.imageUrl,A.content,A.createUserId,
        group_concat(A.operateType) as operateType,group_concat(A.operateName) as operateName from (
        select di.id,di.disposal_title as title,di.create_time as createTime,di.image_url as imageUrl,
        di.disposal_content as content,di.create_user_id as createUserId,psi.operate_type as operateType,psi.operate_name as operateName
        from disposal_info di left join disposal_process_link dpl on di.id=dpl.disposal_id
        left join process_step_info psi on psi.process_id = dpl.process_id
        where di.status &gt; 1 and (psi.operate_type = 0 or psi.operate_type = 2)
        <foreach collection="roleIdList" item="roleId" separator="+" open="and (" close=") > 0">
            find_in_set(#{roleId}, psi.role_ids)
        </foreach>
        union all
        select di.id,di.disposal_title as title,di.create_time as createTime,di.image_url as imageUrl,
        di.disposal_content as content,di.create_user_id as createUserId,psi.operate_type as operateType,psi.operate_name as operateName
        from disposal_info di left join disposal_flow dw on di.id=dw.disposal_id
        left join process_step_info psi on psi.id = dw.step_id
        where dw.status=1 and (psi.operate_type=1 or psi.operate_type=3) and dw.role_id in
        <foreach collection="roleIdList" item="roleId" separator="," open="(" close=")">
            #{roleId}
        </foreach>) A group by A.id , A.title,A.createTime,A.imageUrl,A.content order by A.createTime desc
    </select>

    <select id="selectDisposalCompleteByUserId" resultType="com.onecity.os.disposal.model.po.DisposalInfoPOJO">
        select distinct A.id as id, A.title,A.createTime,A.imageUrl,A.content,A.createUserId,
        group_concat(A.operateType) as operateType,group_concat(A.operateName) as operateName from (
        select di.id,di.disposal_title as title,di.create_time as createTime,di.image_url as imageUrl,
        di.disposal_content as content,di.create_user_id as createUserId,psi.operate_type as operateType,psi.operate_name as operateName
        from disposal_info di left join disposal_process_link dpl on di.id=dpl.disposal_id
        left join process_step_info psi on psi.process_id = dpl.process_id
        where di.status &gt; 1 and di.status != 4 and (psi.operate_type = 0 or psi.operate_type = 2)
        <foreach collection="roleIdList" item="roleId" separator="+" open="and (" close=") > 0">
            find_in_set(#{roleId}, psi.role_ids)
        </foreach>
        union all
        select di.id,di.disposal_title as title,di.create_time as createTime,di.image_url as imageUrl,
        di.disposal_content as content,di.create_user_id as createUserId,psi.operate_type as operateType,psi.operate_name as operateName
        from disposal_info di left join disposal_flow dw on di.id=dw.disposal_id
        left join process_step_info psi on psi.id = dw.step_id
        where dw.status=1 and (psi.operate_type=1 or psi.operate_type=3) and dw.operate_user_id = #{userId}) A
        group by A.id , A.title,A.createTime,A.imageUrl,A.content order by A.createTime desc
    </select>

    <select id="selectDisposalReadByRoleId" resultType="com.onecity.os.disposal.model.po.ProcessStepInfoPOJO">
        select psi.id as id,psi.process_id as processId,psi.sort as sort from disposal_info di left join disposal_process_link dpl on di.id=dpl.disposal_id
        left join process_step_info psi on psi.process_id = dpl.process_id
        where di.id=#{disposalId} and di.status = 1 and psi.operate_type = 2
        <foreach collection="roleIdList" item="roleId" separator="+" open="and (" close=") > 0">
            find_in_set(#{roleId}, psi.role_ids)
        </foreach> order by psi.sort
    </select>

    <select id="selectDisposalReportByRoleId" resultType="com.onecity.os.disposal.model.po.DisposalFlowPOJO">
        select id,disposal_id as disposalId,process_id as processId,step_id as stepId,role_id as roleId,
        role_name as roleName,status,operate_time as operateTime,operate_user_id as operateUserId,
        operate_user_name as operateUserName,context as context,type
        from disposal_flow dw where disposal_id = #{disposalId} and status=0 and (dw.type != 2 or dw.type is null)
        <foreach collection="roleIdList" item="roleId" separator="+" open="and (" close=") > 0">
            find_in_set(#{roleId}, dw.role_id)
        </foreach>
    </select>

    <insert id="insertNewByReportDisposal">
        insert into disposal_info (id,source_name,index_id,index_name,disposal_title,disposal_content,image_url,status,create_user_id,create_user_name,create_time)
        select #{newId} as id,source_name,index_id,index_name,disposal_title, disposal_content,image_url,1 as status,
         #{createUserId} as create_user_id,#{createUserName} as create_user_name,#{createTime} as create_time
         from disposal_info where id=#{disposalId}
    </insert>

    <insert id="insertDisposalProcessLink">
        insert into disposal_process_link (id,disposal_id,process_id,source_type,origin_disposal_id,sort)
        select #{newId} as id ,#{disposalId} as disposal_id , #{processId} as process_id,
        #{sourceType} as source_type, #{originDisposalId} as origin_disposal_id,
        case when max(sort) is NULL THEN 1 else max(sort) + 1 end as sort from disposal_process_link
        where disposal_id = #{oldDisposalId}
    </insert>

    <insert id="insertDisposalProcessLinkFirst">
        insert into disposal_process_link (id,disposal_id,process_id,source_type,sort,origin_disposal_id)
        values(#{newId},#{disposalId},#{processId},#{sourceType},#{sort},#{originDisposalId})
    </insert>

    <select id="selectLastLinkByDisposalId" resultType="com.onecity.os.disposal.model.po.DisposalProcessLinkPOJO">
        select id,disposal_id as disposalId,process_id as processId,source_type as sourceType,
        sort as sort,origin_disposal_id as originDisposalId from disposal_process_link
        where disposal_id = #{disposalId} order by sort desc limit 1
    </select>

    <update id="updateStatusById">
        <if test="disposalIdList!=null and disposalIdList.size() > 0">
            update disposal_info set status=#{status} where id in
            <foreach collection="disposalIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="updateStepFlowEnd">
        update disposal_flow set status=#{status} where disposal_id=#{disposalId} and (`type` != 2 or `type` is null)
    </update>

    <select id="selectDisposalListByIndex" resultType="com.onecity.os.disposal.model.po.DisposalInfoPOJO">
        select id,disposal_title as title,disposal_content as content,create_time as createTime,
        case status when 1 then 0
        when 2 then 1
        when 3 then 1
        end status from
        disposal_info where index_id=#{indexId} and create_user_id = #{userId}
        <if test="lastUpdateTime!=null and lastUpdateTime!=''">
            and create_time &gt; #{lastUpdateTime}
        </if>
    </select>

    <select id="getLastReportDisposal" resultType="com.onecity.os.disposal.model.po.DisposalInfoPOJO">
        select id,disposal_title as title,disposal_content as content,create_time as createTime,
        case status when 1 then 0
        when 2 then 1
        when 3 then 1
        end status from
        disposal_info where id in (
        select disposal_id from disposal_process_link dpl,
        (select max(sort) sort,origin_disposal_id from disposal_process_link where disposal_id in
        <foreach collection="disposalIdList" open="(" close=")" separator="," item="id">
            #{id}
        </foreach> group by origin_disposal_id ) A where dpl.sort = A.sort and dpl.origin_disposal_id = A.origin_disposal_id
        )
    </select>

    <select id="getSourceTypeByDisposalId" resultType="java.lang.Integer">
        select source_type from disposal_process_link where disposal_id = #{disposalId}
    </select>

    <select id="getProcessStepListAll" resultType="com.onecity.os.disposal.model.dto.DisposalListDTO">
        SELECT di.id ,di.source_name as sourceName,di.create_user_name as createUserName,
        di.create_time as createTime,di.disposal_content as content,di.`status`
        FROM disposal_process_link dpl
        LEFT JOIN disposal_info di ON dpl.disposal_id = di.id
        WHERE dpl.source_type = 0
        <if test="sourceName!=null and sourceName!=''">
            and INSTR(source_name,#{sourceName})
        </if>
        <if test="userName!=null and userName!=''">
            and INSTR(create_user_name,#{userName})
        </if>
        order by di.create_time desc
    </select>
</mapper>