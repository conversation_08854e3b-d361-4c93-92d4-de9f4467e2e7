<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.disposal.mapper.IndexUpdateRecordMapper">

    <select id="selectLastUpdateByIndexId" resultType="com.onecity.os.disposal.model.po.IndexUpdateRecordPOJO">
        select id,index_id as indexId,update_type as updateType,update_time as updateTime from index_update_record
        where index_id = #{indexId} order by update_time desc limit 1
    </select>

    <select id="selectLastUpdateByType" resultType="com.onecity.os.disposal.model.po.IndexUpdateRecordPOJO">
        select id,index_id as indexId,update_type as updateType,update_time as updateTime from index_update_record
        where index_id = #{indexId} and update_type=#{type} order by update_time desc limit 1
    </select>

    <insert id="insertIndexUpdateRecords" >
        insert into index_update_record (id,index_id,update_type,update_time) value
        <foreach collection="recordList" item="record" open="(" close=")" separator="),(" >
            #{record.id},#{record.indexId},#{record.updateType},#{record.updateTime}
        </foreach>
    </insert>
</mapper>