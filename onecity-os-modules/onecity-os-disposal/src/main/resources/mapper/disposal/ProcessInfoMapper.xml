<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.disposal.mapper.ProcessInfoMapper">

    <select id="selectProcessByNameNotId" resultType="com.onecity.os.disposal.model.po.ProcessInfoPOJO">
        select id from process_info where name=#{processName} and is_delete=0
    </select>

    <select id="selectProcessListByName" resultType="com.onecity.os.disposal.model.po.ProcessInfoPOJO">
        select id,code,name from process_info where  is_delete=0
        <if test="processName!=null and processName.length > 0">
            and INSTR(name,#{processName})
        </if>
        order by create_time desc
    </select>

    <select id="selectProcessListByRoleId" resultType="com.onecity.os.disposal.model.po.ProcessInfoPOJO">
        select * from process_info where is_delete=0 and id in
        (select process_id from process_step_info where operate_type = 0
            <foreach collection="roleIdList" item="roleId" separator="+" open="and (" close=") > 0">
                find_in_set(#{roleId}, role_ids)
            </foreach>
        )
        order by create_time desc
    </select>

    <update id="updateNameById">
        update process_info set name=#{name},update_time=#{updateTime},update_user_name=#{updateUserName},
        update_user_id=#{updateUserId} where id=#{id}
    </update>

    <update id="deleteByIdList">
        <if test="idList!=null and idList.size() > 0">
            update process_info set is_delete=1 where id in
            <foreach collection="idList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </update>

    <delete id="deleteStepTurnProcessLink">
        delete from step_process_link where turn_process_id in
        <foreach collection="idList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </delete>
</mapper>