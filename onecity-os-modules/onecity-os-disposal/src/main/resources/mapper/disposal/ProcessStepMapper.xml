<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.disposal.mapper.ProcessStepMapper">

    <update id="modifyProcessStepSort">
        update process_step_info set sort = #{sort} where process_id = #{processId} and id=#{stepId}
    </update>

    <select id="selectNextStep" resultType="com.onecity.os.disposal.model.po.ProcessStepInfoPOJO">
        select psi.id,psi.process_id as processId,psi.role_ids as roleIds,psi.role_names as roleNames
        ,psi.operate_type as operateType,psi.operate_name as operateName,psi.sort,psi.create_time as createTime,psi.create_user_id as createUserId,
        psi.create_user_name as createUserName,psi.update_time as updateTime,psi.update_user_id as updateUserId,
        GROUP_CONCAT(pi.id ORDER BY spl.sort) as turnProcessIds,GROUP_CONCAT(pi.name ORDER BY spl.sort) as turnProcessNames,
        psi.update_user_name as updateUserName from process_step_info psi left join step_process_link spl on psi.id=spl.step_id
        left join process_info pi on pi.id=spl.turn_process_id
        where psi.process_id=#{processId} and psi.sort = (select sort + 1 from process_step_info where process_id=#{processId} and id = #{stepId}) GROUP BY psi.id
    </select>

    <select id="selectNextStepNotRead" resultType="com.onecity.os.disposal.model.po.ProcessStepInfoPOJO">
        select psi.id,psi.process_id as processId,psi.role_ids as roleIds,psi.role_names as roleNames
        ,psi.operate_type as operateType,psi.operate_name as operateName,psi.sort,psi.create_time as createTime,psi.create_user_id as createUserId,
        psi.create_user_name as createUserName,psi.update_time as updateTime,psi.update_user_id as updateUserId,
        GROUP_CONCAT(pi.id ORDER BY spl.sort) as turnProcessIds,GROUP_CONCAT(pi.name ORDER BY spl.sort) as turnProcessNames,
        psi. update_user_name as updateUserName from process_step_info psi left join step_process_link spl on psi.id=spl.step_id
        left join process_info pi on pi.id=spl.turn_process_id
        where psi.operate_type !=2 and psi.process_id=#{processId}
        and psi.sort &gt; (select sort from process_step_info where process_id=#{processId} and id = #{stepId}) GROUP BY psi.id order by psi.sort limit 1
    </select>

    <select id="selectNextStepByProcessId" resultType="com.onecity.os.disposal.model.po.ProcessStepInfoPOJO">
        select psi.id,psi.process_id as processId,psi.role_ids as roleIds,psi.role_names as roleNames
        ,psi.operate_type as operateType,psi.operate_name as operateName,psi.sort,psi.create_time as createTime,psi.create_user_id as createUserId,
        psi.create_user_name as createUserName,psi.update_time as updateTime,psi.update_user_id as updateUserId,
        GROUP_CONCAT(pi.id ORDER BY spl.sort) as turnProcessIds,GROUP_CONCAT(pi.name ORDER BY spl.sort) as turnProcessNames,
        psi.update_user_name as updateUserName from process_step_info psi left join step_process_link spl on psi.id=spl.step_id
        left join process_info pi on pi.id=spl.turn_process_id
        where psi.process_id=#{processId} and psi.sort = (select min(sort) + 1 from process_step_info where process_id=#{processId}) GROUP BY psi.id
    </select>

    <select id="selectNextStepByProcessIdNotRead" resultType="com.onecity.os.disposal.model.po.ProcessStepInfoPOJO">
        select psi.id,psi.process_id as processId,psi.role_ids as roleIds,psi.role_names as roleNames
        ,psi.operate_type as operateType,psi.operate_name as operateName,psi.sort,psi.create_time as createTime,psi.create_user_id as createUserId,
        psi.create_user_name as createUserName,psi.update_time as updateTime,psi.update_user_id as updateUserId,
        GROUP_CONCAT(pi.id ORDER BY spl.sort) as turnProcessIds,GROUP_CONCAT(pi.name ORDER BY spl.sort) as turnProcessNames,
        psi. update_user_name as updateUserName from process_step_info psi left join step_process_link spl on psi.id=spl.step_id
        left join process_info pi on pi.id=spl.turn_process_id
        where psi.operate_type !=2 and psi.process_id=#{processId}
        and psi.sort &gt; (select min(sort) from process_step_info where process_id=#{processId}) GROUP BY psi.id order by psi.sort limit 1
    </select>

    <select id="selectDisposalStepByProcessId" resultType="com.onecity.os.disposal.model.po.ProcessStepInfoPOJO">
        select psi.id,psi.process_id as processId,psi.role_ids as roleIds,psi.role_names as roleNames
        ,psi.operate_type as operateType,psi.operate_name as operateName,psi.sort,psi.create_time as createTime,psi.create_user_id as createUserId,
        psi.create_user_name as createUserName,psi.update_time as updateTime,psi.update_user_id as updateUserId,
        GROUP_CONCAT(pi.id ORDER BY spl.sort) as turnProcessIds,GROUP_CONCAT(pi.name ORDER BY spl.sort) as turnProcessNames,
        psi. update_user_name as updateUserName from process_step_info psi left join step_process_link spl on psi.id=spl.step_id
        left join process_info pi on pi.id=spl.turn_process_id
        where psi.process_id=#{processId} and psi.sort = (select min(sort) from process_step_info where process_id=#{processId})
    </select>

    <select id="selectCurrentStepInfo" resultType="com.onecity.os.disposal.model.po.ProcessStepInfoPOJO">
        select psi.id,psi.process_id as processId,psi.role_ids as roleIds,psi.role_names as roleNames
        ,psi.operate_type as operateType,psi.operate_name as operateName,psi.sort,psi.create_time as createTime,psi.create_user_id as createUserId,
        psi.create_user_name as createUserName,psi.update_time as updateTime,psi.update_user_id as updateUserId,
        GROUP_CONCAT(pi.id ORDER BY spl.sort) as turnProcessIds,GROUP_CONCAT(pi.name ORDER BY spl.sort) as turnProcessNames,
        psi. update_user_name as updateUserName from process_step_info psi left join step_process_link spl on psi.id=spl.step_id
        left join process_info pi on pi.id=spl.turn_process_id
        where psi.process_id=#{processId} and psi.id = #{stepId}
    </select>

    <select id="selectStepInfoAll" resultType="com.onecity.os.disposal.model.po.ProcessStepInfoPOJO">
        select psi.id,psi.process_id as processId,psi.role_ids as roleIds,psi.role_names as roleNames
        ,psi.operate_type as operateType,psi.operate_name as operateName,psi.sort,psi.create_time as createTime,psi.create_user_id as createUserId,
        psi.create_user_name as createUserName,psi.update_time as updateTime,psi.update_user_id as updateUserId,
        GROUP_CONCAT(pi.id ORDER BY spl.sort) as turnProcessIds,GROUP_CONCAT(pi.name ORDER BY spl.sort) as turnProcessNames,
        psi. update_user_name as updateUserName from process_step_info psi left join step_process_link spl on psi.id=spl.step_id
        left join process_info pi on pi.id=spl.turn_process_id
        where psi.process_id=#{processId} GROUP BY psi.id order by psi.sort
    </select>

    <update id="updateStepInfoById">
        update process_step_info set id=id,operate_name=#{operateName}
        <if test="roleIds!=null and roleIds !=''">
            ,role_ids=#{roleIds}
        </if>
        <if test="roleNames!=null and roleNames !=''">
            ,role_names=#{roleNames}
        </if>
        <if test="operateType!=null and operateType !=''">
            ,operate_type=#{operateType}
        </if>
        <if test="sort!=null and sort !=''">
            ,sort=#{sort}
        </if>
        <if test="updateTime!=null and updateTime !=''">
            ,update_time=#{updateTime}
        </if>
        <if test="updateUserId!=null and updateUserId !=''">
            ,update_user_id=#{updateUserId}
        </if>
        <if test="updateUserName!=null and updateUserName !=''">
            ,update_user_name=#{updateUserName}
        </if>
        where process_id=#{processId} and id=#{id}
    </update>

    <insert id="insertNewStep" >
        insert into process_step_info (id,process_id,role_ids,role_names,operate_type,operate_name,sort,create_time,create_user_id,create_user_name)
        select #{id} as id ,#{processId},#{roleIds} as role_ids,#{roleNames} as role_names,#{operateType} as operate_type,
        #{operateName} as  operate_name,
        case when max(sort) is NULL THEN 1 else max(sort) + 1 end as sort,#{createTime} as create_time,#{createUserId} as create_user_id,
        #{createUserName} as create_user_name from process_step_info where process_id=#{processId}
    </insert>
    
    <select id="selectStepHasRoleIds" resultType="java.lang.Integer">
        select
        <foreach collection="roleIdList" item="roleId" separator="+" open="sum(" close=")">
            find_in_set(#{roleId}, psi.role_ids)
        </foreach>
        from process_step_info psi left join process_info pi on pi.id=psi.process_id
        where pi.is_delete = 0 and psi.sort=1 and psi.operate_type = 0
    </select>
    <insert id="insertTurnProcessLink" >
        insert into step_process_link (process_id,step_id,turn_process_id,sort) value
        <foreach collection="processIdList" open="(" close=")" separator="),(" item="turnId" index="i">
            #{processId},#{stepId},#{turnId},#{i}+1
        </foreach>
    </insert>
    <delete id="deleteTurnProcessLinkByStepIds">
        delete from step_process_link where step_id in
        <foreach collection="stepIdList" open="(" close=")" separator="," item="stepId">
            #{stepId}
        </foreach>
    </delete>

    <select id="getSyStepIdByProcessId" resultType="java.lang.String">
        select id from process_step_info where process_id=#{processId} and operate_type = 2
    </select>
</mapper>