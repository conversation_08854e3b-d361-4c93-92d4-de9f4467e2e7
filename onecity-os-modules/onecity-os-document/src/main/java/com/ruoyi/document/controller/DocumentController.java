package com.ruoyi.document.controller;

import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.ruoyi.document.domain.Document;
import com.ruoyi.document.domain.DocumentHandle;
import com.ruoyi.document.domain.vo.DocumentDetail;
import com.ruoyi.document.domain.vo.DocumentParam;
import com.ruoyi.document.domain.vo.DocumentVo;
import com.ruoyi.document.service.IDocumentService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/document")
public class DocumentController extends BaseController
{

    @Autowired
    private IDocumentService documentService;

    /**
     * 查询我发起的公文列表
     */
    @GetMapping("/publishList")
    @ApiOperation(value = "查询我发起的公文列表")
    public TableDataInfo publishList(DocumentParam documentParam)
    {
        startPage();
        List<Document> list = documentService.publishList(documentParam);
        return getDataTable(list);
    }

//    /**
//     * 查询我待处理的公文列表
//     */
//    @GetMapping("/pendingList")
//    @ApiOperation(value = "查询我的待办公文列表")
//    public TableDataInfo pendingList(DocumentParam documentParam)
//    {
//        startPage();
//        List<Document> list = documentService.pendingList(documentParam);
//        return getDataTable(list);
//    }
//
//    /**
//     * 查询我已处理的公文列表
//     */
//    @GetMapping("/handleList")
//    @ApiOperation(value = "查询我已处理的公文列表")
//    public TableDataInfo handleList(DocumentParam documentParam)
//    {
//        startPage();
//        List<Document> list = documentService.handleList(documentParam);
//        return getDataTable(list);
//    }

    /**
     *发布（保存）
     */
    @PostMapping("/add")
    @ApiOperation(value = "发布（保存）公文")
    @Log(title="公文-新建公文",businessType = BusinessType.INSERT)
    public AjaxResult addSave(@RequestBody DocumentVo documentVo)
    {
        if(documentService.insertDocument(documentVo) == 1){
            return AjaxResult.success("新增公文成功！");
        }
        return AjaxResult.error("新增失败！");
    }

    /**
     * 处理公文
     */
    @PostMapping("/handle")
    @ApiOperation(value = "处理公文")
    public AjaxResult handle(@RequestBody DocumentHandle documentHandle)
    {
        if(documentService.handle(documentHandle) == 1){
            return AjaxResult.success("处理公文成功！");
        }
        return AjaxResult.error("处理公文失败！");
    }

    /**
     * 删除公文
     */
    @GetMapping( "/remove")
    @ApiOperation(value = "删除公文")
    @Log(title="公文-删除公文",businessType = BusinessType.DELETE)
    public AjaxResult remove(@RequestParam(name="id",required=true) String id)
    {
        if(documentService.deleteDocumentById(id) == 1){
            return AjaxResult.success("删除公文成功！");
        }
        return AjaxResult.error("删除公文失败！");
    }

    /**
     * 发布公文
     */
    @GetMapping( "/publish")
    @ApiOperation(value = "发布公文")
    @Log(title="公文-发布公文",businessType = BusinessType.OTHER)
    public AjaxResult publish(@RequestParam(name="id",required=true) String id)
    {
        if(documentService.publish(id) == 1){
            return AjaxResult.success("发布公文成功！");
        }
        return AjaxResult.error("发布公文失败！");
    }

    /**
     * 撤回公文
     */
    @GetMapping( "/recall")
    @ApiOperation(value = "撤回公文")
    public AjaxResult recall(@RequestParam(name="id",required=true) String id)
    {
        if(documentService.recall(id) == 1){
            return AjaxResult.success("撤回公文成功！");
        }
        return AjaxResult.error("撤回公文失败！");
    }

    /**
     * 查审批意见列表
     */
    @GetMapping( "/handleUserList")
    @ApiOperation(value = "查审批意见列表")
    public AjaxResult handleUserList(@RequestParam(name="id",required=true) String id)
    {
        return AjaxResult.success(documentService.selectDocumentHandleList(id));
    }

    /**
     * 获取公文详情
     */
    @GetMapping("/getDocumentDetail")
    @ApiOperation(value = "取公文详情")
    public AjaxResult getDocumentDetail(@RequestParam(name="id",required=true) String id)
    {
        DocumentDetail documentDetail = documentService.getDocumentDetail(id);
        return AjaxResult.success(documentDetail);
    }

    /**
     *编辑公文
     */
    @PostMapping("/edit")
    @ApiOperation(value = "编辑公文")
    @Log(title="公文-修改公文",businessType = BusinessType.UPDATE)
    public AjaxResult edit(@RequestBody DocumentVo documentVo)
    {
        if(documentService.editDocument(documentVo) == 1){
            return AjaxResult.success("编辑公文成功！");
        }
        return AjaxResult.error("编辑失败！");
    }
}
