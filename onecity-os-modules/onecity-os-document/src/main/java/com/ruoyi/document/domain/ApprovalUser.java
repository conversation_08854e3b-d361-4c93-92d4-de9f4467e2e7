package com.ruoyi.document.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * 审批人员对象 approval_user
 * 
 * <AUTHOR>
 * @date 2022-06-16
 */
@Data
@ApiModel("审批人员对象")
public class ApprovalUser
{
    private static final long serialVersionUID = 1L;

    /** 审批流程id */
    @ApiModelProperty(value = "审批流程id（新增时不必传入）")
    private String processId;

    /** 审批人姓名 */
    @ApiModelProperty(value = "审批人姓名")
    private String approvalUser;

    /** 审批人用户名 */
    @ApiModelProperty(value = "审批人用户名")
    private String approvalUserName;
    /** 审批人用户名 */
    @ApiModelProperty(value = "审批人用户来源 0 pc端 1app端")
    private String approvalUserSource;
}
