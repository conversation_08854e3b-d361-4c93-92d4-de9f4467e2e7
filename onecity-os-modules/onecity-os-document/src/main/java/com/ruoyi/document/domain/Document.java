package com.ruoyi.document.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 公文主对象 document
 * 
 * <AUTHOR>
 * @date 2022-06-08
 */
@Data
@ApiModel("公文对象")
public class Document
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty(value = "主键")
    private String id;
    /** 模板id */
    @ApiModelProperty(value = "模板id")
    private String templateId;

    /** 审批顺序 */
    @ApiModelProperty(value = "审批顺序")
    private Integer approvalOrder;

    /** 标题 */
    @ApiModelProperty(value = "标题")
    private String docTitle;

    /** 文号 */
    @ApiModelProperty(value = "文号")
    private String docNumber;

    /** 状态 0-待发布 1-流转中 2-已办结 */
    @ApiModelProperty(value = "状态 0-待发布 1-流转中 2-已办结")
    private Integer status;
    @ApiModelProperty(value = "公告状态文字")
    private String statusStr;

    /** 删除状态 0 未删除 1 已删除 */
    @ApiModelProperty(value = "删除状态 0 未删除 1 已删除")
    private Integer isDelete;

    /** 发布时间 */
    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+08")
    private Date publishTime;

    /** 发起人用户名 */
    @ApiModelProperty(value = "发起人用户名")
    private String createUserName;

    /** 发起人姓名 */
    @ApiModelProperty(value = "发起人姓名")
    private String createName;

    /** 更新人姓名 */
    @ApiModelProperty(value = "更新人姓名")
    private String updateName;

    /**
     * 更新时间
     */
    @ApiModelProperty(hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+08")
    private Date updateTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+08")
    private Date createTime;

    @ApiModelProperty(value = "正文内容")
    private String content;
    @ApiModelProperty(value = "表单数据id")
    private String dataId;
}
