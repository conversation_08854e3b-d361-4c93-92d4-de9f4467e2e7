package com.ruoyi.document.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 模板对象 template
 * 
 * <AUTHOR>
 * @date 2022-06-16
 */
@Data
@ApiModel("模板对象")
public class Template
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty(value = "主键")
    private String id;

    /** 公文模块名称 */
    @ApiModelProperty(value = "公文模块名称")
    private String templateTitle;

    /** 状态 0-有效，1无效 */
    @ApiModelProperty(value = "状态 0-有效，1无效")
    private Integer status;
    @ApiModelProperty(value = "状态文字")
    private String statusStr;

    /** 删除状态 0 未删除 1 已删除 */
    @ApiModelProperty(value = "删除状态 0 未删除 1 已删除")
    private Integer isDelete;

    /** 正文模板id */
    @ApiModelProperty(value = "正文模板id ")
    private String textId;
    @ApiModelProperty(value = "表单id ")
    private String formId;

    /**
     * 创建时间
     */
    @ApiModelProperty(hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+08")
    private Date createTime;
}
