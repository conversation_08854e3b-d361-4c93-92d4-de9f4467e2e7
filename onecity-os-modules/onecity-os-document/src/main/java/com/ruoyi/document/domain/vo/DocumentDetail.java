package com.ruoyi.document.domain.vo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.document.domain.DocumentFiles;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 公文
 *
 * <AUTHOR>
 * @date 2022-05-20
 */
@Data
@ApiModel("公文详情返回")
public class DocumentDetail
{
    /** 主键 */
    @ApiModelProperty(value = "主键")
    private String id;
    /** 模板id */
    @ApiModelProperty(value = "模板id")
    private String templateId;
    /** 正文模板id */
    @ApiModelProperty(value = "正文模板id ")
    private String textId;
    @ApiModelProperty(value = "表单id ")
    private String formId;
    /** 标题 */
    @ApiModelProperty(value = "标题")
    private String docTitle;
    /** 文号 */
    @ApiModelProperty(value = "文号")
    private String docNumber;
    @ApiModelProperty(value = "正文内容")
    private String content;
    @ApiModelProperty(value = "表单数据id")
    private String dataId;
    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+08")
    private Date publishTime;
    /** 审批顺序 */
    @ApiModelProperty(value = "审批顺序")
    private Integer approvalOrder;
    @ApiModelProperty(value = "附件列表")
    List<DocumentFiles> filesList;
}
