package com.ruoyi.document.domain.vo;
import com.ruoyi.document.domain.DocumentFiles;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 公文
 *
 * <AUTHOR>
 * @date 2022-05-20
 */
@Data
@ApiModel("公文新增参")
public class DocumentVo
{
    /** 主键 */
    @ApiModelProperty(value = "主键")
    private String id;
    /** 模板id */
    @ApiModelProperty(value = "模板id")
    private String templateId;

    /** 标题 */
    @ApiModelProperty(value = "标题")
    private String docTitle;

    /** 文号 */
    @ApiModelProperty(value = "文号")
    private String docNumber;

    @ApiModelProperty(value = "状态 保存草稿传入0 提交传入 1")
    private Integer status;

    /** 发起人用户名 */
    @ApiModelProperty(value = "发起人用户名")
    private String createUserName;

    /** 发起人姓名 */
    @ApiModelProperty(value = "发起人姓名")
    private String createName;
    @ApiModelProperty(value = "正文内容")
    private String content;
    @ApiModelProperty(value = "表单数据id")
    private String dataId;

    @ApiModelProperty(value = "附件列表")
    List<DocumentFiles> filesList;
}
