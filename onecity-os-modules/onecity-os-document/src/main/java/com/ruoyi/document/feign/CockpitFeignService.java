package com.ruoyi.document.feign;

import com.onecity.os.common.core.constant.ServiceNameConstants;
import com.onecity.os.common.core.domain.BaseResult;
import com.ruoyi.document.domain.vo.NoticeMessageVO;
import com.ruoyi.document.feign.fallback.CockpitFeignServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 *
 */
@FeignClient(name = ServiceNameConstants.SUPERVISE_MODULE_SYSTEM, fallbackFactory = CockpitFeignServiceFallbackFactory.class, decode404 = true)
public interface CockpitFeignService {
    /**
     * feign调用：
     * @param
     * @return Result
     */
    @PostMapping(value = "/dingdingapi/sendNoticeNote")
    BaseResult sendNoticeNote(@RequestBody NoticeMessageVO noticeMessageVO);
}
