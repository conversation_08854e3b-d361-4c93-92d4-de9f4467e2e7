package com.ruoyi.document.mapper;

import java.util.List;
import com.ruoyi.document.domain.ApprovalUser;
import org.apache.ibatis.annotations.Mapper;

/**
 * 审批人员Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-06-16
 */
@Mapper
public interface ApprovalUserMapper 
{
    /**
     * 查询审批人员
     * 
     * @param processId 审批人员主键
     * @return 审批人员
     */
    public ApprovalUser selectApprovalUserByProcessId(String processId);

    /**
     * 查询审批人员列表
     * 
     * @param approvalUser 审批人员
     * @return 审批人员集合
     */
    public List<ApprovalUser> selectApprovalUserList(ApprovalUser approvalUser);

    /**
     * 新增审批人员
     * 
     * @param approvalUser 审批人员
     * @return 结果
     */
    public int insertApprovalUser(ApprovalUser approvalUser);

    /**
     * 修改审批人员
     * 
     * @param approvalUser 审批人员
     * @return 结果
     */
    public int updateApprovalUser(ApprovalUser approvalUser);

    /**
     * 删除审批人员
     * 
     * @param processId 审批人员主键
     * @return 结果
     */
    public int deleteApprovalUserByProcessId(String processId);

    /**
     * 批量删除审批人员
     * 
     * @param processIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteApprovalUserByProcessIds(String[] processIds);
}
