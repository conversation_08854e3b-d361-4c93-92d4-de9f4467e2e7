package com.ruoyi.document.mapper;

import java.util.List;
import com.ruoyi.document.domain.DocumentHandle;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 办理结果Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-06-08
 */
@Mapper
public interface DocumentHandleMapper 
{
    /**
     * 查询办理结果列表
     * 
     * @param id 办理结果
     * @return 办理结果集合
     */
    public List<DocumentHandle> selectDocumentHandleList(String id);

    /**
     * 新增办理结果
     * 
     * @param documentHandle 办理结果
     * @return 结果
     */
    public int insertDocumentHandle(DocumentHandle documentHandle);

    /**
     * 根据公文id查询当前阶段已处理人数
     * @param id
     * @return
     */
    Integer getHandledNumByDocumentId(@Param("id") String id,@Param("approvalOrder") Integer approvalOrder);

    /**
     * 根据公文id删除审核列表
     * @param id
     * @return
     */
    Integer deleteHandleByDocumentId(@Param("id") String id);

    /**
     * @param
     * @return
     */
    Integer getHandledNumByUserId(@Param("documentId") String documentId,@Param("userId") String userId,@Param("approvalOrder") Integer approvalOrder);
}
