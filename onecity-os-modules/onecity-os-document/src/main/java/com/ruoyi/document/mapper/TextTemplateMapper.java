package com.ruoyi.document.mapper;

import java.util.List;
import com.ruoyi.document.domain.TextTemplate;
import com.ruoyi.document.domain.vo.TemplateParam;
import org.apache.ibatis.annotations.Mapper;

/**
 * 正文模板Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-06-16
 */
@Mapper
public interface TextTemplateMapper 
{
    /**
     * 查询正文模板
     * 
     * @param id 正文模板主键
     * @return 正文模板
     */
    public TextTemplate selectTextTemplateById(String id);

    /**
     * 查询正文模板列表
     * 
     * @param templateParam 正文模板
     * @return 正文模板集合
     */
    public List<TextTemplate> selectTextTemplateList(TemplateParam templateParam);

    /**
     * 新增正文模板
     * 
     * @param textTemplate 正文模板
     * @return 结果
     */
    public int insertTextTemplate(TextTemplate textTemplate);

    /**
     * 修改正文模板
     * 
     * @param textTemplate 正文模板
     * @return 结果
     */
    public int updateTextTemplate(TextTemplate textTemplate);

    /**
     * 删除正文模板
     * 
     * @param id 正文模板主键
     * @return 结果
     */
    public int deleteTextTemplateById(String id);

    /**
     * 批量删除正文模板
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTextTemplateByIds(String[] ids);
}
