package com.ruoyi.document.service;

import java.util.List;
import com.ruoyi.document.domain.Document;
import com.ruoyi.document.domain.DocumentHandle;
import com.ruoyi.document.domain.vo.AppDocumentParam;
import com.ruoyi.document.domain.vo.DocumentDetail;
import com.ruoyi.document.domain.vo.DocumentParam;
import com.ruoyi.document.domain.vo.DocumentVo;

/**
 * 公文主Service接口
 * 
 * <AUTHOR>
 * @date 2022-06-08
 */
public interface IDocumentService 
{
    /**
     * 查询我发起的公文主列表
     *
     * @param documentParam 公文主
     * @return 公文主集合
     */
    public List<Document> publishList(DocumentParam documentParam);

    /**
     * 查询我办理的公文主列表
     *
     * @param appDocumentParam 公文主
     * @return 公文主集合
     */
    public List<Document> handleList(AppDocumentParam appDocumentParam);

    /**
     * 查询待我办理的公文主列表
     *
     * @param appDocumentParam 公文主
     * @return 公文主集合
     */
    public List<Document> pendingList(AppDocumentParam appDocumentParam);


    /**
     * 新增公文主
     * 
     * @param documentVo 公文主
     * @return 结果
     */
    public int insertDocument(DocumentVo documentVo);

    /**
     * 修改公文
     * @param documentVo
     * @return
     */
    int editDocument(DocumentVo documentVo);

    /**
     * 处理公文
     * @param documentHandle
     * @return
     */
    int handle(DocumentHandle documentHandle);

    /**
     * 修改公文主
     * 
     * @param document 公文主
     * @return 结果
     */
    public int updateDocument(Document document);


    /**
     * 删除公文主信息
     * 
     * @param id 公文主主键
     * @return 结果
     */
     int deleteDocumentById(String id);

    /**
     * 发布公文
     *
     * @param id 公文主主键
     * @return 结果
     */
    int publish(String id);

    /**
     * 撤回公文
     *
     * @param id 公文主主键
     * @return 结果
     */
    int recall(String id);

    List<DocumentHandle> selectDocumentHandleList(String id);

    /**
     * 获取公文详情
     * @param id
     * @return
     */
    DocumentDetail getDocumentDetail(String id);

    /**
     * @param
     * @return
     */
    Integer getHandledNumByUserId(String documentId,String userId,Integer approvalOrder);
}
