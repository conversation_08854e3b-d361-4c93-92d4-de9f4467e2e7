package com.ruoyi.document.service.impl;

import java.util.List;

import com.onecity.os.common.core.utils.DateUtils;
import com.ruoyi.document.consts.DocumentConstant;
import com.ruoyi.document.domain.DocumentFiles;
import com.ruoyi.document.domain.DocumentHandle;
import com.ruoyi.document.domain.vo.*;
import com.ruoyi.document.feign.CockpitFeignService;
import com.ruoyi.document.mapper.ApprovalProcessMapper;
import com.ruoyi.document.mapper.DocumentFilesMapper;
import com.ruoyi.document.mapper.DocumentHandleMapper;
import com.ruoyi.document.utils.IdWorker;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.document.mapper.DocumentMapper;
import com.ruoyi.document.domain.Document;
import com.ruoyi.document.service.IDocumentService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 公文主Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-06-08
 */
@Service
public class DocumentServiceImpl implements IDocumentService 
{
    @Autowired
    private DocumentMapper documentMapper;
    @Autowired
    private DocumentHandleMapper documentHandleMapper;
    @Autowired
    private ApprovalProcessMapper approvalProcessMapper;
    @Autowired
    private DocumentFilesMapper documentFilesMapper;
    @Autowired
    private IdWorker idWorker;
    @Resource
    private CockpitFeignService cockpitFeignService;


    @Override
    public List<Document> publishList(DocumentParam documentParam) {
        List<Document> list = documentMapper.publishList(documentParam);
        /*翻译状态，方便前端展示*/
        this.tranStatus(list);
        return list;
    }
    private void tranStatus(List<Document> list){
        for(Document document : list){
            if(DocumentConstant.STATUS_WAITE == document.getStatus()){
                document.setStatusStr("待发布");
            }
            if(DocumentConstant.STATUS_DEALING == document.getStatus()){
                document.setStatusStr("流转中");
            }
            if(DocumentConstant.STATUS_FINISH == document.getStatus()){
                document.setStatusStr("已完结");
            }
            if(DocumentConstant.STATUS_CANCELED == document.getStatus()){
                document.setStatusStr("已撤回");
            }
        }
    }

    @Override
    public List<Document> handleList(AppDocumentParam appDocumentParam) {
        List<Document> list = documentMapper.handleList(appDocumentParam);
        /*翻译状态，方便前端展示*/
        this.tranStatus(list);
        return list;
    }

    @Override
    public List<Document> pendingList(AppDocumentParam appDocumentParam) {
        List<Document> list = documentMapper.pendingList(appDocumentParam);
        /*翻译状态，方便前端展示*/
        this.tranStatus(list);
        return list;
    }

    /**
     * 新增公文主
     *
     * @param documentVo 公文主
     * @return 结果
     */
    @Override
    public int insertDocument(DocumentVo documentVo)
    {
        Document document = new Document();
        BeanUtils.copyProperties(documentVo,document);
        String id = String.valueOf(idWorker.nextId());
        document.setId(id);
        document.setIsDelete(DocumentConstant.DELETE_NO);
        if(DocumentConstant.STATUS_DEALING == document.getStatus()){
            document.setPublishTime(DateUtils.getNowDate());
            document.setApprovalOrder(1);
            //发送钉钉消息
            List<String> users = approvalProcessMapper.getApprovalUsers(documentVo.getTemplateId(),1);
            this.sendMessage(document.getDocTitle(),users);
        }
        /**添加附件*/
        if(null != documentVo.getFilesList() && documentVo.getFilesList().size() > 0){
            addFile(id,documentVo.getFilesList());
        }
        return documentMapper.insertDocument(document);
    }
    private void addFile(String documentId,List<DocumentFiles> fileList){
        for(DocumentFiles file : fileList){
            file.setDocumentId(documentId);
        }
        documentFilesMapper.insertFilesList(fileList);
    }

    private void sendMessage(String docTitle,List<String> userIds){
        StringBuilder content = new StringBuilder();
        content.append("【公文待办】").append(docTitle);
        NoticeMessageVO noticeMessageVO = new NoticeMessageVO();
        noticeMessageVO.setMessage(content.toString());
        noticeMessageVO.setUserIds(userIds);
        noticeMessageVO.setSource("公文");
        cockpitFeignService.sendNoticeNote(noticeMessageVO);
    }

    @Override
    public int editDocument(DocumentVo documentVo) {
        Document document = new Document();
        BeanUtils.copyProperties(documentVo,document);
        document.setIsDelete(DocumentConstant.DELETE_NO);
        if(DocumentConstant.STATUS_DEALING == document.getStatus()){
            document.setPublishTime(DateUtils.getNowDate());
            document.setApprovalOrder(1);
        }
        /**删除之前的附件*/
        documentFilesMapper.deleteFilesById(documentVo.getId());
        /**添加附件*/
        if(null != documentVo.getFilesList() && documentVo.getFilesList().size() > 0){
            addFile(documentVo.getId(),documentVo.getFilesList());
        }
        return documentMapper.updateDocument(document);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int handle(DocumentHandle documentHandle) {
        //查询公文的模板id
        String templateId = documentMapper.getTemplateIdById(documentHandle.getDocumentId());
        //查询当前公文所处处理阶段
        Integer approvalOrder = documentMapper.getApprovalOrderById(documentHandle.getDocumentId());
        //查询当前阶段已处理的人数
        Integer handledNum = documentHandleMapper.getHandledNumByDocumentId(documentHandle.getDocumentId(),approvalOrder);
        //查询该公文总共需要的处理阶段数
        Integer approvalNum =  approvalProcessMapper.getApprovalNumById(templateId);
        //查询当前阶段需要审批人数
        Integer needApprovalUserNum = approvalProcessMapper.getNeedApprovalNum(templateId,approvalOrder);
        //判断当前处理是否是当前流程最后一人
        if((needApprovalUserNum - handledNum) == 1){
            //当前阶段已完成，再判断当前流程是否是最后一流程
            if(approvalNum == approvalOrder){
                //审批流程已走完，更新公文状态
                Document document = new Document();
                document.setId(documentHandle.getDocumentId());
                document.setStatus(DocumentConstant.STATUS_FINISH);
                documentMapper.updateDocument(document);
            }else {
                //当前阶段已完成，审批流程未完成，更新公文流程阶段
                Document document = new Document();
                document.setId(documentHandle.getDocumentId());
                document.setApprovalOrder(approvalOrder+1);
                documentMapper.updateDocument(document);
                //发送钉钉消息
                List<String> users = approvalProcessMapper.getApprovalUsers(templateId,approvalOrder+1);
                DocumentDetail documentDetail = documentMapper.getDocumentDetail(documentHandle.getDocumentId());
                this.sendMessage(documentDetail.getDocTitle(),users);
            }
        }
        //保存处理记录
        documentHandle.setHandleTime(DateUtils.getNowDate());
        documentHandle.setApprovalOrder(approvalOrder);
        return documentHandleMapper.insertDocumentHandle(documentHandle);
    }

    /**
     * 修改公文主
     * 
     * @param document 公文主
     * @return 结果
     */
    @Override
    public int updateDocument(Document document)
    {
        document.setUpdateTime(DateUtils.getNowDate());
        return documentMapper.updateDocument(document);
    }

    /**
     * 删除公文主信息
     * 
     * @param id 公文主主键
     * @return 结果
     */
    @Override
    public int deleteDocumentById(String id)
    {
        Document document = new Document();
        document.setId(id);
        document.setIsDelete(DocumentConstant.DELETE_YES);
        return documentMapper.updateDocument(document);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int publish(String id) {
        //修改公文状态
        Document document = new Document();
        document.setId(id);
        document.setStatus(DocumentConstant.STATUS_DEALING);
        document.setPublishTime(DateUtils.getNowDate());
        document.setApprovalOrder(1);
        //发送钉钉消息
        DocumentDetail documentDetail = documentMapper.getDocumentDetail(id);
        List<String> users = approvalProcessMapper.getApprovalUsers(documentDetail.getTemplateId(),1);
        this.sendMessage(documentDetail.getDocTitle(),users);
        return documentMapper.updateDocument(document);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int recall(String id) {
        Document document = new Document();
        document.setId(id);
        document.setStatus(DocumentConstant.STATUS_WAITE);
        document.setPublishTime(null);
        documentHandleMapper.deleteHandleByDocumentId(id);
        return documentMapper.updateDocumentReCall(document);
    }

    @Override
    public List<DocumentHandle> selectDocumentHandleList(String id) {
        return documentHandleMapper.selectDocumentHandleList(id);
    }

    @Override
    public DocumentDetail getDocumentDetail(String id) {
        DocumentDetail documentDetail = documentMapper.getDocumentDetail(id);
        /**查询附件列表*/
        List<DocumentFiles> fileList = documentFilesMapper.getFilesListById(id);
        documentDetail.setFilesList(fileList);
        return documentDetail;
    }

    @Override
    public Integer getHandledNumByUserId(String documentId,String userId, Integer approvalOrder) {
        return documentHandleMapper.getHandledNumByUserId(documentId,userId,approvalOrder);
    }
}
