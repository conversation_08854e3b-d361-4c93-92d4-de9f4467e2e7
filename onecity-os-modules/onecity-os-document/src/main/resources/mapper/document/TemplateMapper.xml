<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.document.mapper.TemplateMapper">
    
    <resultMap type="Template" id="TemplateResult">
        <result property="id"    column="id"    />
        <result property="templateTitle"    column="template_title"    />
        <result property="status"    column="status"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createTime"    column="create_time"    />
        <result property="textId"    column="text_id"    />
        <result property="formId"    column="form_id"    />
    </resultMap>

    <sql id="selectTemplateVo">
        select id, template_title, status, is_delete, create_time, text_id, form_id from template
    </sql>

    <select id="selectTemplateList" parameterType="TemplateParam" resultMap="TemplateResult">
        <include refid="selectTemplateVo"/>
        <where>
            is_delete = 0
            <if test="title != null  and title != ''"> and template_title like concat('%', #{title}, '%')</if>
        </where>
        ORDER BY status,create_time desc
    </select>

    <select id="selectUsedTemplateList"  resultMap="TemplateResult">
        <include refid="selectTemplateVo"/>
        where is_delete = 0 and status = 0
        ORDER BY create_time desc
    </select>
    
    <select id="selectTemplateById" parameterType="String" resultMap="TemplateResult">
        <include refid="selectTemplateVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTemplate" parameterType="Template">
        insert into template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="templateTitle != null">template_title,</if>
            <if test="status != null">status,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createTime != null">create_time,</if>
            <if test="textId != null">text_id,</if>
            <if test="formId != null">form_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="templateTitle != null">#{templateTitle},</if>
            <if test="status != null">#{status},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="textId != null">#{textId},</if>
            <if test="formId != null">#{formId},</if>
         </trim>
    </insert>

    <update id="updateTemplate" parameterType="Template">
        update template
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateTitle != null">template_title = #{templateTitle},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="textId != null">text_id = #{textId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTemplateById" parameterType="String">
        delete from template where id = #{id}
    </delete>

    <delete id="deleteTemplateByIds" parameterType="String">
        delete from template where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>