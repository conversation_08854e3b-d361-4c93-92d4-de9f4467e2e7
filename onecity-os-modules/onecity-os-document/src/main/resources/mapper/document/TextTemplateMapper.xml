<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.document.mapper.TextTemplateMapper">
    
    <resultMap type="TextTemplate" id="TextTemplateResult">
        <result property="id"    column="id"    />
        <result property="textTitle"    column="text_title"    />
        <result property="content"    column="content"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectTextTemplateVo">
        select id, text_title, content, create_time from text_template
    </sql>

    <select id="selectTextTemplateList" parameterType="TemplateParam" resultMap="TextTemplateResult">
        <include refid="selectTextTemplateVo"/>
        <where>
            is_delete = 0
            <if test="title != null and title != ''"> and text_title like concat('%', #{title}, '%')</if>
        </where>
        ORDER BY create_time desc
    </select>
    
    <select id="selectTextTemplateById" parameterType="String" resultMap="TextTemplateResult">
        <include refid="selectTextTemplateVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTextTemplate" parameterType="TextTemplate">
        insert into text_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="textTitle != null">text_title,</if>
            <if test="content != null">content,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="textTitle != null">#{textTitle},</if>
            <if test="content != null">#{content},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateTextTemplate" parameterType="TextTemplate">
        update text_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="textTitle != null">text_title = #{textTitle},</if>
            <if test="content != null">content = #{content},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTextTemplateById" parameterType="String">
        delete from text_template where id = #{id}
    </delete>

    <delete id="deleteTextTemplateByIds" parameterType="String">
        delete from text_template where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>