package com.ruoyi.file.controller;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.web.domain.AjaxResult;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.file.FileUtils;
import com.ruoyi.file.service.ISysFileService;
import com.onecity.os.system.api.domain.SysFile;

import org.apache.pdfbox.pdmodel.PDDocument;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Locale;

/**
 * 文件请求处理
 * 
 * <AUTHOR>
 */
@RestController
public class SysFileController
{
    private static final Logger log = LoggerFactory.getLogger(SysFileController.class);
    /**
     * 全部文件(普通文件,图片,)后缀 支持的类型
     */
    private static final String[] FILE_SUFFIX_SUPPORT = {".xlsx", ".xls", ".doc", ".docx", ".txt",
            ".jpg", ".jpeg", ".png", ".pdf"};
    @Autowired
    private ISysFileService sysFileService;

    /**
     * 文件上传请求
     */
    @PostMapping("upload")
    public BaseResult<SysFile> upload(MultipartFile file)
    {
        //获取文件大小
        double fileSize = (double) file.getSize() / 1048576;
        // 校验文件名字
        String originalFilename = file.getOriginalFilename();
        String suffix = originalFilename.substring(originalFilename.lastIndexOf('.'));
        String fileName = originalFilename.substring(0,originalFilename.lastIndexOf('.'));
        log.info("上传文件大小为=" + fileSize + "M");
        //校验文件是否被修改后缀
        String fileSuffixName = suffix.toLowerCase(Locale.ROOT);
        log.info("文件后缀为："+ JSONObject.toJSONString(fileSuffixName));
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            byte[] byteArray = new byte[4];
            inputStream.read(byteArray, 0, 4);
            StringBuilder hex = new StringBuilder();
            for (byte b : byteArray) {
                hex.append(String.format("%02X", b));
            }
            //获取文件的魔数png魔数-89504e470d0a1a0a jpg魔数-
            switch (fileSuffixName){
                case ".png":
                    if(!"89504E47".equals(hex.toString())){
                        return BaseResult.fail("文件后缀名被修改");
                    }
                    break;
                case ".pdf":
                    if(!"25504446".equals(hex.toString())){
                        return BaseResult.fail("文件后缀名被修改");
                    }
                    try {
                        // 判断文件xss攻击
                        PDDocument doc = PDDocument.load(inputStream);
                        String CosName = doc.getDocument().getTrailer().toString();
                        if (CosName.contains("COSName{JavaScript}") || CosName.contains("COSName{JS}")) {
                            return BaseResult.fail("文件包含攻击脚本");
                        }
                    } catch (Exception e) {
                        log.error("PDF效验异常：" + e.getMessage());
                        return BaseResult.fail("文件包含攻击脚本");
                    }
                    break;
                case ".jpg":
                    log.info("进入jpg比对");
                    if(!"FFD8FFE0".equals(hex.toString())){
                        return BaseResult.fail("文件后缀名被修改");
                    }
                    break;
                default:
                    return BaseResult.fail("文件格式不支持,请更换后重试!");
            }
        }catch (IOException e){
            log.error("打开文件报错："+e.getMessage());
            return BaseResult.fail("文件内容出错,请更换后重试!");
        }finally {
            IOUtils.closeQuietly(inputStream);
        }

        if(fileSize > 20 ){
            return BaseResult.fail("上传文件最大为20M!");
        }
        if(!originalFilename.contains(".")){
            return BaseResult.fail("文件不能没有后缀!");
        }
        if(!Arrays.asList(FILE_SUFFIX_SUPPORT).contains(suffix.toLowerCase(Locale.ROOT))){
            return BaseResult.fail("文件格式不支持,请更换后重试!");
        }
        if(fileName.length() > 200){
            return BaseResult.fail("文件名长度不允许超过200，请更换后重试！");
        }
        try
        {
            // 上传并返回访问地址
            String url = sysFileService.uploadFile(file);
            SysFile sysFile = new SysFile();
            sysFile.setName(FileUtils.getName(url));
            sysFile.setUrl(url);
            return BaseResult.ok(sysFile);
        }
        catch (Exception e)
        {
            log.error("上传文件失败", e);
            return BaseResult.fail(e.getMessage());
        }
    }
}