package com.ruoyi.file.service;

import org.apache.commons.io.FilenameUtils;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import com.github.tobato.fastdfs.domain.fdfs.StorePath;
import com.github.tobato.fastdfs.service.FastFileStorageClient;

import java.io.IOException;
import java.io.InputStream;

/**
 * FastDFS 文件存储
 * 
 * <AUTHOR>
 */
@Service
public class FastDfsSysFileServiceImpl implements ISysFileService
{
    /**
     * 域名或本机访问地址
     */
    @Value("${fdfs.domain}")
    public String domain;

    @Autowired
    private FastFileStorageClient storageClient;

    /**
     * FastDfs文件上传接口
     * 
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    @Override
    public String uploadFile(MultipartFile file) throws Exception
    {
        StorePath storePath = null;
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            storePath = storageClient.uploadFile(inputStream, file.getSize(),
                    FilenameUtils.getExtension(file.getOriginalFilename()), null);
        }catch (IOException e){
            e.printStackTrace();
        }finally {
            IOUtils.closeQuietly(inputStream);
        }
        return domain + "/" + storePath.getFullPath();
    }
}
