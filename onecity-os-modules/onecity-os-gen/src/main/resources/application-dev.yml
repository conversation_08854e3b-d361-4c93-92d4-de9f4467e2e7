server:
  port: 8080
# spring配置
spring:
  redis:
    host: onecity-os-redis
    port: 6379
    password: DONT(QkOmrw81EnDa0CDf9y1hywwU2sWxykwRkIHa8ZXhBvSUmcPtXUJerFvBv/77k4AxY40uLtxHEcjv86VWFXHNoPxPcGIgG6n10aMBOlsrWLwWZAjlOWr3N4JrG04kk+fPG+57s7x6Zu6algBVF8FWVJfIxkMWMIhU0IZGZ5njtok=)
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************
    username: root
    password: DONT(QkOmrw81EnDa0CDf9y1hywwU2sWxykwRkIHa8ZXhBvSUmcPtXUJerFvBv/77k4AxY40uLtxHEcjv86VWFXHNoPxPcGIgG6n10aMBOlsrWLwWZAjlOWr3N4JrG04kk+fPG+57s7x6Zu6algBVF8FWVJfIxkMWMIhU0IZGZ5njtok=)

# mybatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.ruoyi.gen.domain
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath:mapper/**/*.xml

# swagger配置
swagger:
  title: 代码生成接口文档
  license: Powered By ruoyi
  licenseUrl: https://ruoyi.vip

# 代码生成
gen:
  # 作者
  author: ruoyi
  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool
  packageName: com.ruoyi.system
  # 自动去除表前缀，默认是false
  autoRemovePre: false
  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）
  tablePrefix: sys_
