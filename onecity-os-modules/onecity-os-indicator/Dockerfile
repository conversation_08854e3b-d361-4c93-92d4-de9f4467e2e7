#FROM image.onecode.cmict.cloud/library/java:8
FROM image.onecode.cmict.cloud/library/openjdk:8-jre
VOLUME /tmp
COPY target/onecity-os-indicator.jar /onecity-os-indicator-dev.jar
RUN mkdir -p /opt/logs/onecity-os-indicator/
CMD ["java","-Djava.boss.egd=file:/dev/./urandom","-Duser.timezone=GMT+08","-server","-Xms1024m","-Xmx1024m","-jar","/onecity-os-indicator-dev.jar","--spring.profiles.active=dev"]
