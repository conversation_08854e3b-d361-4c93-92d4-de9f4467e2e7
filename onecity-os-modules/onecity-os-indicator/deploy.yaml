apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: $DEPLOY_NAMESPACE
  name: $APP_NAME
  labels:
    app: $APP_NAME
spec:
  replicas: 1
  selector:
    matchLabels:
      app: $APP_NAME
  template:
    metadata:
      labels:
        app: $APP_NAME
    spec:
      containers:
      - name: $APP_NAME
        image: $REGISTRY/$HARBOR_PROJECT_NAME/$IMAGE_NAME
        ports:
        - name: http-8080
          containerPort: 8080
          protocol: TCP
        imagePullPolicy: Always
      dnsPolicy: ClusterFirst
      nodeSelector:
        internet: '1'
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  namespace: $DEPLOY_NAMESPACE
  name: $APP_NAME
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
  selector:
    app: $APP_NAME
