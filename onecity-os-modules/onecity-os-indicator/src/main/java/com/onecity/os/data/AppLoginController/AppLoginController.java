package com.onecity.os.data.AppLoginController;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.constant.Constants;
import com.onecity.os.common.core.constant.SecurityConstants;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.exception.ServiceException;
import com.onecity.os.common.core.utils.RSA.RSACoder;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.data.AppLoginController.vo.ThirdUserInfoVo;
import com.onecity.os.system.api.RemoteUserService;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.service.TokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import cn.hutool.crypto.SmUtil;

/**
 * <AUTHOR>
 * @Date
 */
@Slf4j
@RestController
@RequestMapping("/appLogin")
public class AppLoginController {
    @Value("${app.thirdPlandUrl}")
    private String baseUrl;
    private static final String appId = "108";
    private static final String grantType = "authorization_code";
    @Value("${app.privateKey}")
    private String PRIVATE_KEY;
    @Value("${app.sm2privateKey}")
    private String sm2privateKey;
    @Value("${app.sm2publickey}")
    private String sm2publickey;


    @Autowired
    private RemoteUserService remoteUserService;
    @Autowired
    private TokenService tokenService;
    @GetMapping("/getTokenByCode")
    public BaseResult download(@RequestParam(value = "code") String code) {
        //1.现根据code获取token
        String token = "";
        SM2 sm2 = SmUtil.sm2(sm2privateKey,sm2publickey);
        try {
            //获取appSecret
            String appSecret = RSACoder.encryptByPrivateKey(code,PRIVATE_KEY);
            String url = baseUrl + "/system/subsystem/code/verify?code=" + code + "&appId=" + appId + "&appSecret=" + appSecret + "&grantType=" + grantType;
            log.info("调用平台接口获取token请求url："+JSONObject.toJSONString(url));
            String result = HttpRequest.get(url).header("Content-Type", "application/json").timeout(20000).execute().body();
            String header = HttpRequest.get(url).header("Content-Type", "application/json").timeout(20000).execute().header("encFlag");
            log.info("调用平台接口获取token返回结果：" + result);
            if("1".equals(header)) {
                String resultStr = StrUtil.utf8Str(sm2.decrypt(result, KeyType.PrivateKey));
                result = resultStr;
            }
            com.alibaba.fastjson.JSONObject jsonObjects = JSON.parseObject(result);
            if ("0".equals(jsonObjects.getString("code"))) {
                JSONObject dataObject = (JSONObject) jsonObjects.get("data");
                token = dataObject.getString("token");
            } else {
                return BaseResult.fail("获取第三方token失败");
            }
        } catch (Exception e) {
            log.error("请求平台接口获取token失败"+e.getMessage());
            return BaseResult.fail("请求平台接口失败，请重试！");
        }
        //2.根据token获取用户信息
        String tokenUrl = baseUrl + "/token/verify";
        String tokenResult = HttpRequest.get(tokenUrl).header("Content-Type", "application/json").header("token",token).timeout(20000).execute().body();
        String header = HttpRequest.get(tokenUrl).header("Content-Type", "application/json").header("token",token).timeout(20000).execute().header("encFlag");
        log.info("调用平台接口获取用户信息返回结果：" + tokenResult);
        if("1".equals(header)) {
            String resultStr = StrUtil.utf8Str(sm2.decrypt(tokenResult, KeyType.PrivateKey));
            tokenResult = resultStr;
        }
        JSONObject userObjet = JSON.parseObject(tokenResult);
        if("0".equals(userObjet.getString("code"))){
            String data = userObjet.getString("data");
            ThirdUserInfoVo userInfoVo = JSON.parseObject(data,ThirdUserInfoVo.class);
            //生成我们自己的token
            // 查询用户信息
            BaseResult<LoginUser> userResult = remoteUserService.getUserInfo(userInfoVo.getLoginName(), SecurityConstants.INNER);
            if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData()))
            {
                return BaseResult.fail(4004,"无法登录，请联系管理员");
            }
            LoginUser userInfo = userResult.getData();
            if ("1".equals(userInfo.getSysUser().getStatus()) || ObjectUtils.isEmpty(userInfo.getRoles())){
                return BaseResult.fail(4001,"暂未授权，请联系管理员");
            }
            return BaseResult.ok(tokenService.createToken(userInfo));
        }else {
            return BaseResult.fail("获取第三方用户信息失败");
        }
    }
}
