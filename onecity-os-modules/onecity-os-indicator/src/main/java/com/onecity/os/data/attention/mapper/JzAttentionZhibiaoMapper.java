package com.onecity.os.data.attention.mapper;


import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.data.attention.model.po.JzAttentionZhibiao;
import com.onecity.os.data.attention.model.vo.AttentionIndicatorInfoVo;
import com.onecity.os.data.attention.model.vo.JzAttentionZhibiaoVo;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 晋中关注指标表(JzAttentionZhibiao)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-08-17 10:05:59
 */
@Mapper
public interface JzAttentionZhibiaoMapper extends BaseMapper<JzAttentionZhibiao> {

	@Insert("insert into jz_attention_zhibiao (indicator_id,indicator_name,update_date,link,status," +
			" user_id,user_name,create_time,creater,update_time,updater,source_simple_name) " +
			" values (#{zhibiao.indicatorId},#{zhibiao.indicatorName},#{zhibiao.updateDate}," +
			" #{zhibiao.link},#{zhibiao.status},#{zhibiao.userId}," +
			" #{zhibiao.userName},#{zhibiao.createTime},#{zhibiao.creater}," +
			" #{zhibiao.updateTime},#{zhibiao.updater},#{zhibiao.sourceSimpleName})")
	int saveZhibiao(@Param("zhibiao") JzAttentionZhibiao zhibiao);

	@Select("select a.id as id, a.indicator_id as indicatorId,a.indicator_name as indicatorName," +
			"a.update_date as updateDate,a.link as link,a.status as status,a.user_id as userId," +
			"a.user_name as userName,a.create_time as createTime,a.creater as creater," +
			"a.update_time as updateTime,a.updater as updater," +
			"a.source_simple_name as sourceSimpleName,b.source_name as sourceName " +
			"from jz_attention_zhibiao a left join source_manage b on a.source_simple_name=b.source_simple_name where a.user_id = #{userid} and a.status = 1 order by a.source_simple_name")
	List<JzAttentionZhibiao> selectAttentionList(@Param("userid") String userid);

	@Update("update jz_attention_zhibiao set status = #{vo.status}, link = #{vo.link}," +
			" update_time = #{vo.updateTime}, updater = #{vo.updater}  where " +
			" indicator_id = #{vo.indicatorId} and user_id = #{vo.userId}")
	void updateStatus(@Param("vo") JzAttentionZhibiaoVo vo);

	@Update("update jz_attention_zhibiao set status = 0 where id = #{id} ")
	void deleteStatus(Integer id);

	@Select("select * from jz_attention_zhibiao where user_id = #{userid} and indicator_id = #{indicatorId}")
	JzAttentionZhibiao getJzAttentionZhibiao(@Param("userid") String userid, @Param("indicatorId") String indicatorId);

	@Select("select * from jz_attention_zhibiao where id = #{id}")
	JzAttentionZhibiao selectJzAttentionZhibiao(Integer id);

//	@Select("SELECT a.id AS indicatorId,a.indicator_name,a.source_name,a.indicator_exhibit_type,a.update_date AS updateTime," +
//			"date_format(a.update_time,'截至%Y年%m月') AS endTime,b.id,b.`link`,b.user_id,b.`status` " +
//			"FROM general_indicator_tianbao a LEFT JOIN jz_attention_zhibiao b ON a.id=b.indicator_id " +
//			"WHERE b.`status`=1 AND b.user_id=#{userId} AND a.source_id=#{tj} AND a.is_delete=0")
//	List<AttentionIndicatorInfoVo> getAttIndicatorInfoListByUserId(@Param("userId") String userId, @Param("tj") String tj);
//
	@Select("select e.id,e.indicator_name,e.source_name,e.indicator_exhibit_type,e.update_date," +
			"e.is_show,e.name_show_flag,e.url_ids,e.url_name,e.url_type,e.sort_type,e.is_legend,e.is_screen,f.indicator_name as parent_name,e.update_cycle,e.data_update_mode,e.data_config_id,e.endTime,e.attentionId,e.source_simple_name,e.`link`,e.user_id,e.`status`,e.update_time,e.source_manage_name as source_manage_name from (SELECT c.id,c.indicator_name,c.source_name,c.indicator_exhibit_type,c.update_date," +
			"c.is_show,c.name_show_flag,c.url_ids,c.url_name,c.url_type,c.sort_type,c.is_legend,c.is_screen,c.parent_id,c.update_cycle,c.data_update_mode,c.data_config_id,c.endTime,c.attentionId,c.source_simple_name,c.`link`,c.user_id,c.`status`,c.update_time,d.source_name as source_manage_name from (SELECT a.id,a.indicator_name,a.source_name,a.indicator_exhibit_type,a.update_date," +
			"a.is_show,a.name_show_flag,a.url_ids,a.url_name,a.url_type,a.sort_type,a.is_legend,a.is_screen,a.parent_id,a.update_cycle,a.data_update_mode,a.data_config_id,date_format(a.update_time,'截至%Y年%m月') AS endTime,b.id as attentionId,b.source_simple_name,b.`link`,b.user_id,b.`status`,b.update_time " +
			"FROM general_indicator a LEFT JOIN jz_attention_zhibiao b ON a.id=b.indicator_id " +
			"WHERE b.`status`=1 AND b.user_id=#{userId} AND a.is_delete=0 and a.is_show=1) c left join source_manage d on c.source_simple_name=d.source_simple_name where d.is_delete=0 and d.is_start=1)e left join general_indicator f on e.parent_id=f.id where f.is_delete=0 and f.is_show=1 ORDER BY e.source_simple_name,e.update_time desc ")
	List<AttentionIndicatorInfoVo> getAttIndicatorInfoListByUserId(@Param("userId") String userId);


	@Select("select c.source_simple_name from (SELECT " +
			"a.source_simple_name as source_simple_name, " +
			"b.source_name as source_name " +
			"FROM " +
			"jz_attention_zhibiao a " +
			"LEFT JOIN source_manage b on a.source_simple_name=b.source_simple_name " +
			"WHERE " +
			"a.user_id = #{userId} " +
			"AND a.STATUS = 1 AND b.is_start = 1 " +
			"GROUP BY " +
			"source_simple_name " +
			"ORDER BY " +
			"CONVERT (source_name USING gbk) ASC) c")
	List<String> getAttSourceSimpleNameByUserId(@Param("userId") String userId);
}
















