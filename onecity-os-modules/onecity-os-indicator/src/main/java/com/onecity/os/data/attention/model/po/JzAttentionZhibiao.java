package com.onecity.os.data.attention.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 晋中关注指标表(JzAttentionZhibiao)表实体类
 *
 * <AUTHOR>
 * @since 2020-08-17 09:51:30
 */
@Data
@Table(name = "jz_attention_zhibiao")
@ApiModel
public class JzAttentionZhibiao implements Serializable {
    private static final long serialVersionUID = 247965716940140107L;
    //主键
    @Id
    private Integer id;
    //指标id
    @ApiModelProperty(value = "指标id", required = true)
    private String indicatorId;
    //指标名称
    private String indicatorName;
    //更新日期
    private String updateDate;
    //跳转链接
    private String link;
    //0：未关注； 1：已关注；
    private Integer status;
    //关注人id
    @ApiModelProperty(value = "关注人id", required = true)
    private String userId;
    //关注人
    private String userName;
    //创建时间
    private Date createTime;
    //创建人
    private String creater;
    //更新时间
    private Date updateTime;
    //更新人
    private String updater;
    //板块编码
    private String sourceSimpleName;

    private String sourceName;

}