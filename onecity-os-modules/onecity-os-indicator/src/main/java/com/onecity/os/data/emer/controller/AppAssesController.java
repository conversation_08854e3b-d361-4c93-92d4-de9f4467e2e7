package com.onecity.os.data.emer.controller;



import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.data.emer.model.vo.asses.AppAssesParam;
import com.onecity.os.data.emer.model.vo.plan.PlanContentParam;
import com.onecity.os.data.emer.service.AppAssesService;
import com.onecity.os.data.emer.service.AppHeadquarterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/appAsses")
public class AppAssesController {

    @Autowired
    AppAssesService appAssesService;

    /**
     *  根据name获取评估报告
     */
    @PostMapping("/getAssesReportByName")
    @ResponseBody
    public BaseResult getAssesReportByName(@RequestBody @Valid AppAssesParam appAssesParam) {

        return BaseResult.ok(appAssesService.getAssesReportByName(appAssesParam.getReportName()));
    }

//    /**
//     *  获取所有评估报告
//     */
//    @PostMapping("/getAllAssesReport")
//    @ResponseBody
//    public ResultInfo<T> getAllAssesReport() {
//
//        return ResultInfo.ok(appAssesService.getAssesReportByName());
//    }


}
