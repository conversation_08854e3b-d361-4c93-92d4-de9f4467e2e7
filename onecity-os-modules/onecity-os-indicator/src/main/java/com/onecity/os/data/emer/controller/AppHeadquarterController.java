package com.onecity.os.data.emer.controller;


import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.data.emer.model.entity.headquarter.AllHeadquartersBean;
import com.onecity.os.data.emer.model.entity.headquarter.HeadquarterUserBean;
import com.onecity.os.data.emer.model.vo.headquarter.HeadquarterUserParam;
import com.onecity.os.data.emer.service.AppHeadquarterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


@Slf4j
@RestController
@RequestMapping("/appHeadquarters")
@Api(tags = "应急指挥相关接口")
public class AppHeadquarterController {

    @Autowired
    AppHeadquarterService appHeadquarterService;


    /**
     * 获取所有部门
     */
    @GetMapping("/getAllHeadquarters")
    @ResponseBody
    @ApiOperation(value = "获取所有部门列表")
    public BaseResult getHeadquarters() {

        return BaseResult.ok(appHeadquarterService.getAllHeadquarters());
    }

    /**
     * 根据部门Id查找用户信息
     *
     * @param headquarterUserParam
     * @return
     */

    @PostMapping("/getHeadquarterUserById")
    @ResponseBody
    @ApiOperation(value = "根据部门Id查找用户信息")
    public BaseResult getHeadquarterUserById(@RequestBody @Valid HeadquarterUserParam headquarterUserParam) {


        return BaseResult.ok(appHeadquarterService.getHeadquarterUserById(headquarterUserParam));
    }


}
