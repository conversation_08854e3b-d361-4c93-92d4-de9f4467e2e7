package com.onecity.os.data.emer.controller;



import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.data.emer.model.vo.asses.AppAssesParam;
import com.onecity.os.data.emer.model.vo.institute.InstituteParam;
import com.onecity.os.data.emer.service.AppAssesService;
import com.onecity.os.data.emer.service.AppInstituteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/AppInstitute")
@Api(tags = "区县纵横-区县简介/基本情况/机构职责相关接口")
public class AppInstituteController {

    @Autowired
    AppInstituteService appInstituteService;

    /**
     *  根据id获取厅局及区县信息
     */
    @PostMapping("/getInstituteById")
    @ResponseBody
    @ApiOperation(value = "根据id获取厅局及区县信息")
    public BaseResult getInstituteById(@RequestBody @Valid InstituteParam instituteParam) {

        return BaseResult.ok(appInstituteService.getInstituteById(instituteParam.getId()));
    }



}
