package com.onecity.os.data.emer.controller;



import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.data.emer.model.entity.plan.PlanType;
import com.onecity.os.data.emer.model.vo.plan.PlanContentParam;
import com.onecity.os.data.emer.service.AppPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/appPlan")
@Api(tags = "应急指挥-应急预案相关接口")
public class AppPlanController {

    @Autowired
    AppPlanService appPlanService;

//    /**
//     *  获取所有预案类型
//     */
//    @GetMapping("/getPlanType")
//    @ResponseBody
//    public ResultInfo<PlanType> getPlanType() {
//
//        return ResultInfo.ok(appPlanService.getPlanType());
//    }

    /**
     * 根据类型,获取预案信息
     */
    @PostMapping("/getPlanContent")
    @ResponseBody
    @ApiOperation(value = "根据类型,获取预案信息")
    public BaseResult getPlanContent(@RequestBody @Valid PlanContentParam planContentParam) {

        return BaseResult.ok(appPlanService.getPlanContent(planContentParam.getType()));
    }

    /**
     * 根据预案id,获取预案内容
     */
    @PostMapping("/getPlanContentById")
    @ResponseBody
    @ApiOperation(value = "根据预案id,获取预案内容")
    public BaseResult getPlanContentById(@RequestBody @Valid PlanContentParam planContentParam) {

        return BaseResult.ok(appPlanService.getPlanContentById(planContentParam.getId()));
    }


}
