package com.onecity.os.data.emer.mapper;

import com.onecity.os.data.emer.model.entity.headquarter.DepartmentBean;
import com.onecity.os.data.emer.model.entity.headquarter.HeadquarterUserBean;
import com.onecity.os.data.emer.model.entity.headquarter.HeadquarterUserBeanOuter;
import com.onecity.os.data.emer.model.entity.headquarter.UserBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


import java.util.List;

@Mapper
public interface AppHeadquarterMapper {

    List<DepartmentBean> getAllHeadquarters(@Param("type") Integer type);

    Integer getPopulation(@Param("type") Integer type);

    List<HeadquarterUserBean> getHeadquarterUserById(@Param("id") Long id);

    List<String> getStation(@Param("id") Long id, @Param("type") Integer type);

    /**
     * 根据id,查找指挥部部分信息
     *
     * @param id
     * @return
     */
    HeadquarterUserBeanOuter getEmerDepartById(@Param("id") Long id);

    /**
     * 根据指挥部id,岗位查找下面的人员
     *
     * @param id
     * @param station
     * @return
     */
    List<UserBean> getEmerUsersByDepartId(@Param("id") Long id, @Param("station") String station);


}







