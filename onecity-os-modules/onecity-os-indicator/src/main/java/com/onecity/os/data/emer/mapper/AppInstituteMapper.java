package com.onecity.os.data.emer.mapper;

import com.onecity.os.data.emer.model.entity.asses.AssesReportBean;
import com.onecity.os.data.emer.model.entity.institute.InstituteBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AppInstituteMapper {

    InstituteBean getInstituteById(@Param("id") Integer id);

}
