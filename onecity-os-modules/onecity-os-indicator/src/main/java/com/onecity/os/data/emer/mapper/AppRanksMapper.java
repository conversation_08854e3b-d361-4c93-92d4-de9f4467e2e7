package com.onecity.os.data.emer.mapper;


import com.onecity.os.data.emer.model.entity.ranks.AllRanksContent;
import com.onecity.os.data.emer.model.entity.ranks.DistrictAndPopulation;
import com.onecity.os.data.emer.model.entity.ranks.RanksContent;
import lombok.Data;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AppRanksMapper {

    List<String> getRanksType();

    List<DistrictAndPopulation> getDistrictAndPopulation(@Param("type") int type);

    List<RanksContent> getRanksContent(@Param("district") String district, @Param("type") int type);

    List<AllRanksContent> getAllRanksContentById(@Param("id") String id);


}
