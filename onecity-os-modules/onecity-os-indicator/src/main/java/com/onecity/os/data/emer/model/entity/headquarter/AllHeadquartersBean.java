package com.onecity.os.data.emer.model.entity.headquarter;

import java.util.List;

public class AllHeadquartersBean {

    private Integer population;
    private int type;
    private String typeName;



    private List<DepartmentBean> Department;

    public List<DepartmentBean> getDepartment() {
        return Department;
    }

    public void setDepartment(List<DepartmentBean> department) {
        Department = department;
    }

    public Integer getPopulation() {
        return population;
    }

    public void setPopulation(Integer population) {
        this.population = population;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }


}
