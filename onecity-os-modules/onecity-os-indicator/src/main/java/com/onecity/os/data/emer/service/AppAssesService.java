package com.onecity.os.data.emer.service;

import com.onecity.os.data.emer.mapper.AppAssesMapper;
import com.onecity.os.data.emer.mapper.AppHeadquarterMapper;
import com.onecity.os.data.emer.model.entity.asses.AssesReportBean;
import com.onecity.os.data.emer.model.entity.plan.PlanContenct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AppAssesService {

    @Autowired
    AppAssesMapper appAssesMapper;

    public List<AssesReportBean> getAssesReportByName(String reportName){
        List<AssesReportBean> assesReport = appAssesMapper.getAssesReportByName(reportName);

        return  assesReport;
    }
}
