package com.onecity.os.data.emer.service;

import com.onecity.os.data.emer.mapper.AppHeadquarterMapper;
import com.onecity.os.data.emer.model.entity.headquarter.*;
import com.onecity.os.data.emer.model.vo.headquarter.HeadquarterUserParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class AppHeadquarterService {

    @Resource
    private AppHeadquarterMapper appHeadquarterMapper;

    private final Integer gHeadquarterType = 0;
    private final Integer sHeadquarterType = 1;

    public Object getAllHeadquarters() {
        List<Object> ObjectsList = new ArrayList<>();

        Integer gHeadquarterPopulation = appHeadquarterMapper.getPopulation(gHeadquarterType);
        List<DepartmentBean> gAllHeadquarters = appHeadquarterMapper.getAllHeadquarters(gHeadquarterType);
        AllHeadquartersBean gAllHeadquartersBean = new AllHeadquartersBean();
        gAllHeadquartersBean.setPopulation(gHeadquarterPopulation);
        gAllHeadquartersBean.setType(gHeadquarterType);
        gAllHeadquartersBean.setDepartment(gAllHeadquarters);
        gAllHeadquartersBean.setTypeName("总指挥部");
        ObjectsList.add(gAllHeadquartersBean);

        List<DepartmentBean> sAllHeadquarters = appHeadquarterMapper.getAllHeadquarters(sHeadquarterType);
        Integer sHeadquarterPopulation = appHeadquarterMapper.getPopulation(sHeadquarterType);
        AllHeadquartersBean sAllHeadquartersBean = new AllHeadquartersBean();
        sAllHeadquartersBean.setType(sHeadquarterType);
        sAllHeadquartersBean.setPopulation(sHeadquarterPopulation);
        sAllHeadquartersBean.setDepartment(sAllHeadquarters);
        sAllHeadquartersBean.setTypeName("专项应急指挥部");
        ObjectsList.add(sAllHeadquartersBean);
        return ObjectsList;

    }

    public Object getHeadquarterUserById(HeadquarterUserParam headquarterUserParam) {
        HeadquarterUserBeanOuter headquarterUserBeanOuter = new HeadquarterUserBeanOuter();
        // 根据id,查找指挥部部分信息
        headquarterUserBeanOuter = appHeadquarterMapper.getEmerDepartById(headquarterUserParam.getId());
        assert null != headquarterUserBeanOuter;
        // 查询当前部门有的岗位名称
        List<String> stationList = appHeadquarterMapper.getStation(headquarterUserParam.getId(), headquarterUserBeanOuter.getType());
        if (CollectionUtils.isNotEmpty(stationList)) {
            // 创建人员岗位列表
            List<HeadquarterBearn> beanList = new ArrayList<>();
            // 遍历岗位
            for (String station : stationList) {
                HeadquarterBearn headquarterBearn = new HeadquarterBearn();
                headquarterBearn.setStation(station);
                // 创建该岗位下的人员列表
                List<UserBean> users = new ArrayList<>();
                // 根据指挥部id,岗位名称,查找下面的人员
                users = appHeadquarterMapper.getEmerUsersByDepartId(headquarterUserParam.getId(), station);
                headquarterBearn.setUserBean(users);
                beanList.add(headquarterBearn);
            }
            headquarterUserBeanOuter.setHeadquarterBearns(beanList);
        }
        return headquarterUserBeanOuter;
    }


}
