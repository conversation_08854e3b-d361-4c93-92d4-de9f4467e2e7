package com.onecity.os.data.emer.service;


import com.onecity.os.data.emer.mapper.AppPlanMapper;
import com.onecity.os.data.emer.mapper.AppRanksMapper;
import com.onecity.os.data.emer.model.entity.plan.PlanContenct;
import com.onecity.os.data.emer.model.entity.ranks.AllRanksContent;
import com.onecity.os.data.emer.model.entity.ranks.DistrictAndPopulation;
import com.onecity.os.data.emer.model.entity.ranks.RanksContent;
import com.onecity.os.data.emer.model.vo.ranks.AppRanksParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AppRanksService {

    @Autowired
    AppRanksMapper appRanksMapper;

    public List<String> getRanksType(){
        return appRanksMapper.getRanksType();
    }



    public List<DistrictAndPopulation> getDistrictAndPopulation(int type){

        List<DistrictAndPopulation> districtAndPopulation = appRanksMapper.getDistrictAndPopulation(type);
        if (districtAndPopulation == null && districtAndPopulation.size() == 0) {
            return districtAndPopulation;
        }
        for (DistrictAndPopulation dp : districtAndPopulation){
            List<RanksContent> ranksContent = appRanksMapper.getRanksContent(dp.getDistrict(), type);
//            if (ranksContent == null && ranksContent.size() == 0) {
//                continue;
//            }
            dp.setRanksContent(ranksContent);

        }
        return  districtAndPopulation;
    }
    public List<AllRanksContent> getAllRanksContentById(AppRanksParam appRanksParam){
        return appRanksMapper.getAllRanksContentById(appRanksParam.getId());
    }




}
