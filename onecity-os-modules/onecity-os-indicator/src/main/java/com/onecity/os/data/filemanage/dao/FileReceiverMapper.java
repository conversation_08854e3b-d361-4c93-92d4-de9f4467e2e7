package com.onecity.os.data.filemanage.dao;



import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.data.filemanage.entity.po.FileReceiver;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;


import java.util.List;

@Mapper
public interface FileReceiverMapper extends BaseMapper<FileReceiver> {

    @Select("SELECT DISTINCT receiver from filereceiver where fileid in (SELECT id from filemanange WHERE adminid=#{id})")
    List<String> getRecervers(@Param("id") Integer id);

    @Select("update filereceiver set isread=1  where fileid=#{id} and receiverid=#{userid}")
    void updateRead(String id, String userid);
    @Delete("delete  from filereceiver where fileid in (SELECT id from filemanange WHERE adminid=#{id})")
    void deleteByAdminid(String id);
}