//package com.onecity.os.data.handler;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
//
///**
// * token拦截的路径
// *
// * <AUTHOR>
// * @date 2021/2/4 上午11:33
// */
////@EnableWebMvc
//@Configuration
//public class WebAppConfiguration implements WebMvcConfigurer {
//
//    /**
//     * 不注册这个在过滤器中 ValidateInterceptor注入的类将报空
//     */
//    @Bean
//    public ValidateInterceptor validateInterceptor() {
//        return new ValidateInterceptor();
//    }
//
//    @Override
//    public void addInterceptors(InterceptorRegistry registry) {
//        registry.addInterceptor(validateInterceptor())
//                .addPathPatterns("/**")
//                // 通知公告和要情专报
//                .excludePathPatterns("/fileupdate/**")
//                .excludePathPatterns("/specialreport/**")
//                .excludePathPatterns("/swagger-resources/**")
//                .excludePathPatterns("/swagger-ui.html/**")
//                .excludePathPatterns("/v2/api-docs/**")
//                .excludePathPatterns("/v2/api-docs-ext/**")
//                .excludePathPatterns("/doc.html/**");
//    }
//
//
//
//}
//
//
//
//
//
//
//
//
//
//
//
