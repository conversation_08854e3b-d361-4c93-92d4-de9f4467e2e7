package com.onecity.os.data.indicator.constants;/**
 * Description
 *
 * <AUTHOR> 2021/11/9
 */

import java.util.*;

/**
 * <AUTHOR>
 * @Title UtilConstants
 * @Description 通用常量
 * @date 2021/11/9 11:03
 */

public interface UtilConstants {

    /**
     * 通用字符
     */
    interface Symbol {
        String SIGH = "!";
        String AT = "@";
        String WELL = "#";
        String DOLLAR = "$";
        String RMB = "￥";
        String SPACE = " ";
        String LB = System.getProperty("line.separator");
        String PERCENTAGE = "%";
        String AND = "&";
        String STAR = "*";
        String MIDDLE_LINE = "-";
        String LOWER_LINE = "_";
        String EQUAL = "=";
        String PLUS = "+";
        String COLON = ":";
        String SEMICOLON = ";";
        String COMMA = ",";
        String POINT = ".";
        String SLASH = "/";
        String VERTICAL_BAR = "|";
        String DOUBLE_SLASH = "//";
        String BACKSLASH = "\\";
        String QUESTION = "?";
        String LEFT_BIG_BRACE = "{";
        String RIGHT_BIG_BRACE = "}";
        String LEFT_MIDDLE_BRACE = "[";
        String RIGHT_MIDDLE_BRACE = "]";
        String BACKQUOTE = "`";
        String RU_CURRENCY_SYMBOL = "₽";
        String UFEFF = "\uFEFF";
        String YEAR = "年";
        String MONTH = "月";
        String DAY = "日";
    }

    /**
     * 文件格式
     */
    interface FileType {
        //文件格式
        List<String> XLS = Arrays.asList("xls", "xlsx");
        //PDF
        List<String> PDF = Collections.singletonList("pdf");
    }

    /**
     * 提醒类型
     */
    interface RemindType {
        //月度提醒
        String MON = "MON_REMIND";
        //逾期差3天提醒
        String OVER3 = "OVERDUE_3_REMIND";
        //逾期差1天提醒
        String OVER1 = "OVERDUE_1_REMIND";
    }

    /**
     * 数据来源
     */
    interface Source {
        //PC
        String PC = "PC";
        //APP
        String APP = "APP";
    }

    /**
     * 通用标记
     */
    interface StateFlag {
        //PC
        String TRUE = "1";
        //APP
        String FALSE = "0";
    }

    /**
     * 指标头操作
     */
    interface Operate {
        //新增
        String ADD = "ADD";
        //删除
        String DEL = "DEL";
        //调整
        String UPDATE = "UPDATE";
        //保持
        String KEEP = "KEEP";
    }

    /**
     * 指标数据标头默认结构
     *
     * @return
     */
    static List<String> getAllColumn() {
        List<String> allColumnNames = new ArrayList<>();
        allColumnNames.add("item_value");
        allColumnNames.add("item_value1");
        allColumnNames.add("item_value2");
        allColumnNames.add("item_value3");
        allColumnNames.add("item_value4");
        allColumnNames.add("item_value5");
        allColumnNames.add("item_value6");
        return allColumnNames;
    }

    /**
     * item_value对照转换，
     * 前端传值 itemValue，转成item_value 数据库/前端使用
     *
     * @param source
     * @return
     */
     static String getItemMap(String source) {
        Map<String, String> itemMap = new HashMap<>(14);
        itemMap.put("item_value", "itemValue");
        itemMap.put("item_value1", "itemValue1");
        itemMap.put("item_value2", "itemValue2");
        itemMap.put("item_value3", "itemValue3");
        itemMap.put("item_value4", "itemValue4");
        itemMap.put("item_value5", "itemValue5");
        itemMap.put("item_value6", "itemValue6");
        itemMap.put("itemValue", "item_value");
        itemMap.put("itemValue1", "item_value1");
        itemMap.put("itemValue2", "item_value2");
        itemMap.put("itemValue3", "item_value3");
        itemMap.put("itemValue4", "item_value4");
        itemMap.put("itemValue5", "item_value5");
        itemMap.put("itemValue6", "item_value6");
        return itemMap.get(source);
    }
}

