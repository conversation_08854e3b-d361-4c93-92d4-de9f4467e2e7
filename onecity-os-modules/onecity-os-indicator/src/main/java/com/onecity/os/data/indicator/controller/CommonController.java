package com.onecity.os.data.indicator.controller;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.file.FastDFSClient;
import com.onecity.os.data.indicator.model.vo.SourceManageReqVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2020/5/21 9:17
 */
@Controller
public class CommonController {
    @Autowired
    private FastDFSClient fastDFSClient;

//    @ResponseBody
////    @PostMapping("/file/upload")
//    public BaseResult upload(@RequestBody MultipartFile file) throws IOException {
//        String fileUrl = fastDFSClient.uploadFile(file);
//        if(StringUtils.isBlank(fileUrl)){
//            return BaseResult.fail("文件上传失败");
//        }
//        return BaseResult.ok(fileUrl);
//    }


    @ResponseBody
    @PostMapping("/file/download")
    public BaseResult download(@RequestBody SourceManageReqVO req) {
        String iconUrl = req.getIconUrl();
        if(StringUtils.isBlank(iconUrl)){
            return BaseResult.fail("图片地址为空");
        }
        String base64Str = fastDFSClient.downloadForBase64(iconUrl);
        return BaseResult.ok(base64Str);
    }

}
