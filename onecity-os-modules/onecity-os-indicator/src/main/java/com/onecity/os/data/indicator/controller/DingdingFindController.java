package com.onecity.os.data.indicator.controller;

import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.data.indicator.app.AuthHelper;
import com.onecity.os.data.indicator.app.DingEVN;
import com.onecity.os.data.indicator.model.vo.dingmaillist.DingdingFindResVo;
import com.onecity.os.data.indicator.service.DingFindService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 钉钉通讯录管理
 */
@Slf4j
@RestController
@RequestMapping("/dingFindMail")
public class DingdingFindController extends BaseController {

    @Autowired
    DingFindService dingFindService;

    @PostMapping("/showDepartment")
    @ApiOperation(value = "展示部门分页")
    public TableDataInfo showDepartment(@RequestParam(value = "pageNo",required = false) Integer pageNo, @RequestParam(value = "pageSize" ,required = false) Integer pageSize) {
        String accessToken = null;
        try {
            accessToken = AuthHelper.getAccessToken(DingEVN.AppKey, DingEVN.AppSecret);
        } catch (Exception e) {
            e.printStackTrace();
        }

        startPage();
        List<DingdingFindResVo> dingDingFindResVos = dingFindService.showDepartment(pageNo,pageSize,accessToken);
        return getDataTable(dingDingFindResVos);
    }
}
