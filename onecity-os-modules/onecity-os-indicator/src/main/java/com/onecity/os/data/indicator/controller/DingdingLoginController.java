package com.onecity.os.data.indicator.controller;


import com.alibaba.fastjson.JSONObject;


import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.data.filemanage.entity.vo.FileUpload;
import com.onecity.os.data.filemanage.service.FileManageService;
import com.onecity.os.data.handler.PassToken;
import com.onecity.os.data.indicator.app.AppManageUtil;
import com.onecity.os.data.indicator.app.AuthHelper;
import com.onecity.os.data.indicator.app.DingEVN;
import com.onecity.os.data.indicator.app.UserService;
import com.onecity.os.data.indicator.model.po.dingmaillist.User;
import com.onecity.os.data.indicator.ruoyiLogFeign.RemoteLogAppService;
import com.onecity.os.data.indicator.ruoyiLogFeign.SysLogininfor;
import com.onecity.os.data.indicator.service.DingMailService;
import com.onecity.os.data.indicator.service.LogService;
import com.onecity.os.data.indicator.util.BlankUtil;
import com.onecity.os.data.notice.service.SendTianBaoRemindService;
import com.onecity.os.data.indicator.util.IPUtils;
import com.onecity.os.data.indicator.util.SpringContextUtils;
import com.onecity.os.data.noticeimportant.entity.vo.DingFileUpload;
import com.onecity.os.data.noticeimportant.entity.vo.DingLoginVo;
import com.onecity.os.data.noticeimportant.entity.vo.DingOperateDto;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;

/**
 * @Description: 钉钉H5 应用 免登录验证
 * <AUTHOR>
 * @Date 2020/2/14
 * @Version V1.0
 **/
@Slf4j
@RestController
@RequestMapping("/dingding")
public class DingdingLoginController {

    @Autowired
    private FileManageService fileManageService;
    @Autowired
    private AppManageUtil appManageUtil;
    @Autowired
    private LogService logService;
    @Resource
    private DingMailService dingMailService;
    @Autowired
    private SendTianBaoRemindService sendTianBaoRemindService;
    // private GridOperatorService gridOperatorService;

    @Resource
    private RemoteLogAppService remoteLogAppService;


    /*
     * 根据免登录码登录
     */
    @GetMapping("/noLogin")
    public BaseResult noLogin(HttpServletRequest request, HttpServletResponse response) {

//        //1.获取code
//        String code = request.getParameter("code");
//        System.out.println("code:"+code);
//        Object result="";
//        try {
//            //2.通过CODE换取身份userid
//            String accessToken = AuthHelper.getAccessToken(DingEVN.CORP_ID, DingEVN.CORP_SECRET);
//            UserService us = new UserService();
//            String userId=us.getUserInfo(accessToken, code).getString("userid");
//          //   log.info("userid:"+userId);
//            //3.通过userid换取用户信息
//            JSONObject jsonObject=us.getUser(accessToken, userId);
//            String usercode=jsonObject.getString("jobnumber").trim();
//            String  username=jsonObject.getString("name").trim();
//            //验证用户信息是否存在
//
//            if(usercode.startsWith("wgy")){
//                log.info("gridOperator:"+usercode+username);
//                GridOperator gridOperator=gridOperatorService.getwgyinfo(usercode,username);
//                log.info("gridOperator:"+gridOperator);
//                if(!BlankUtil.isBlank(gridOperator)){
//
//                    return  ResultInfo.ok(gridOperator);
//                }
//            }else if(usercode.startsWith("gly")){
//                GridManager gridManager= gridOperatorService.getglyinfo(usercode,username);
//                log.info("gridManager:"+gridManager);
//                if(!BlankUtil.isBlank(gridManager)){
//
//                    return  ResultInfo.ok(gridManager);
//                }
//            }else{
//                   return  ResultInfo.ok("没有权限，请联系管理员!");
//            }
//            //result= JSON.toJSON(jsonObject);
//          //  log.info("userinfo:"+result);
//        } catch (Exception e) {
//            // TODO Auto-generated catch block
//            e.printStackTrace();
//
//        }

        return  BaseResult.ok("没有权限，请联系管理员!!!");
    }

    @PassToken
    @GetMapping("/getSign")
    public BaseResult getSign(HttpServletRequest request, HttpServletResponse response) {
        return  BaseResult.ok(AuthHelper.getConfig(request));
    }

    @PassToken
    @GetMapping("/getUser")
    public BaseResult getUser(HttpServletRequest request, HttpServletResponse response) {
        //1.获取code
        String code = request.getParameter("code");
        System.out.println("code:"+code);
        try {
            //2.通过CODE换取身份userid
            String accessToken = AuthHelper.getAccessToken(DingEVN.AppKey, DingEVN.AppSecret);
            UserService us = new UserService();
            String userId=us.getUserInfo(accessToken, code).getString("userid");
            //   log.info("userid:"+userId);
            //3.通过userid换取用户信息
            JSONObject jsonObject=us.getUser(accessToken, userId);
            User user = dingMailService.selectUserByUserId(userId);
            if(user!=null){
                String roleIds = user.getRoles();
                if(!StringUtils.isNotBlank(roleIds)){
                    jsonObject.put("roles", dingMailService.selectAllRolesByIds(Arrays.asList(roleIds.split(","))));
                }
            }
            return  BaseResult.ok(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
            return  BaseResult.ok("获取用户信息异常！");
        }

    }

    @PostMapping("/upload")
    @ApiOperation(value = "钉盘文件上传")
    public BaseResult uploadDingFile(@RequestBody FileUpload fileUploadList, HttpServletRequest request, HttpServletResponse response){
        //  log.info("filesize:"+baseFileList.getFileList().size());
        if(fileUploadList.getFileList().size()==0){
            return BaseResult.fail("上传文件不能为空！");
        }
        if(fileUploadList.getReceiverList().size()==0){
            return BaseResult.fail("接收人不能为空！");
        }
        if(BlankUtil.isBlank(fileUploadList.getCreaterid())){
            return BaseResult.fail("创建人信息不能为空！");
        }
        String accessToken=request.getParameter("access_token");
        String userid=request.getParameter("user_id");
        String code=request.getParameter("code");
        try {
            String s = fileManageService.saveDingFile(fileUploadList, accessToken, DingEVN.AgentId, userid, code);
            //钉盘文件上传
            return BaseResult.ok(s);
        } catch (IOException e) {
            e.printStackTrace();
            return BaseResult.fail("文件上传失败!");
        }
    }

    @GetMapping("/grant")
    @ApiOperation(value = "授权用户访问钉盘文件")
    @PassToken
    public BaseResult grantCustomSpace(HttpServletRequest request, HttpServletResponse response){
        //1.获取code
        String code = request.getParameter("code");
        log.info("获取code:"+JSONObject.toJSONString(code));
        String accessToken = "";
        String userid = "";
        try {
            //2.通过CODE换取身份userid
            accessToken = AuthHelper.getAccessToken(DingEVN.AppKey, DingEVN.AppSecret);
            UserService us = new UserService();
            userid=us.getUserInfo(accessToken, code).getString("userid");
            log.info("免登陆码为 --> {}, accessToken --> {}, userId --> {}",JSONObject.toJSONString(code), JSONObject.toJSONString(accessToken), JSONObject.toJSONString(userid));
        } catch (Exception e) {
            log.error("获取用户信息失败! --> {}", e);
            return BaseResult.fail("获取用户信息失败!");
        }
        String type = request.getParameter("type");
        String fields = request.getParameter("fileids");
        String spaceId = "";
        try {
            //获取企业自定义钉盘空间
            //请求成功返回 空间id spaceid
            JSONObject customSpace = appManageUtil.getCustomSpace(accessToken, DingEVN.AgentId);
            if(null!=customSpace && null!=customSpace.getString("spaceid")){
                log.info("获取企业自定义空间spaceId为 --> {}", customSpace.getString("spaceid"));
                spaceId = customSpace.getString("spaceid");
            }
        } catch (Exception e) {
            log.error("获取企业自定义空间spaceId失败 ! --> {}", e);
            return BaseResult.fail("获取企业自定义空间失败!");
        }
        try {
            //授权用户访问企业自定义空间
            JSONObject jsonObject = appManageUtil.grantCustomSpace(accessToken, DingEVN.AgentId, type, userid, fields);
            log.info("grantCustomSpace --> "+jsonObject.toJSONString());
            log.info("授权用户访问企业自定义空间成功! --> {}",spaceId);
            return BaseResult.ok(spaceId);
        } catch (Exception e) {
            log.error("授权用户访问企业自定义空间失败 ! --> {}", e);
            return BaseResult.fail("授权用户访问企业自定义空间失败 ! ");
        }
    }

    @PostMapping("/savedingfile")
    @ApiOperation( value = "保存钉盘存储的文件信息")
    public BaseResult saveDingFile(@RequestBody DingFileUpload dingFileUpload) throws IOException{
        //保存钉盘存储的文件信息
        if(dingFileUpload.getReceiverList().size()==0){
            return BaseResult.fail("接收人不能为空！");
        }
        if(BlankUtil.isBlank(dingFileUpload.getCreaterid())){
            return BaseResult.fail("创建人信息不能为空！");
        }
        String msg= fileManageService.saveDingFiles(dingFileUpload);
        return BaseResult.ok(msg);
    }

    @ApiOperation(value = "钉钉登陆信息记录接口")
    @RequestMapping(value = "/loginMsg", method = RequestMethod.POST)
    public BaseResult login(@RequestBody DingLoginVo request) {
        log.info(" loginMsg request : {}", JSONObject.toJSONString(request));
        SysLogininfor sysLogininfor = new SysLogininfor();
//        sysLogininfor.setUserName(userId);
//        sysLogininfor.setRealName(username);
        LoginUser user = SecurityUtils.getLoginUser();
        if(null != user){
            log.info("当前登录用户：{}", user);
            sysLogininfor.setPhone(user.getPhone());
            sysLogininfor.setUserName(user.getUsername());
            sysLogininfor.setRealName(user.getSysUser().getNickName());
            sysLogininfor.setMsg("用户名: " + user.getUsername() + ",登录成功！");
        }
        sysLogininfor.setSource("dingding");
        sysLogininfor.setStatus("1");
        sysLogininfor.setAccessTime(new Date());
        sysLogininfor.setAppFlag(1);
        remoteLogAppService.saveLogininfor(sysLogininfor);
        return BaseResult.ok();
    }

    @ApiOperation(value = "钉钉操作信息记录接口")
    @RequestMapping(value = "/dingOperateMsg", method = RequestMethod.POST)
    public BaseResult DingOperateMsg(@RequestBody DingOperateDto request) {
        log.info(" dingOperateMsg request : {}", JSONObject.toJSONString(request));
        //content
        String content = request.getContent();
        SysLogininfor sysLogininfor = new SysLogininfor();
//        sysLogininfor.setUserName(userId);
//        sysLogininfor.setRealName(username);
        LoginUser user = SecurityUtils.getLoginUser();
        if(null != user){
            log.info("当前登录用户：{}", user);
            sysLogininfor.setPhone(user.getPhone());
            sysLogininfor.setUserName(user.getUsername());
            sysLogininfor.setRealName(user.getSysUser().getNickName());
        }
        sysLogininfor.setMsg(content + "版块");
        sysLogininfor.setSource("dingding");
        sysLogininfor.setStatus("1");
        sysLogininfor.setAccessTime(new Date());
        sysLogininfor.setAppFlag(2);
        log.info("开始调用日志服务");
        remoteLogAppService.saveLogininfor(sysLogininfor);
        log.info("调用日志服务完成");
        return BaseResult.ok();
    }

    @ApiOperation(value = "APP用户消息发送")
    @PassToken
    @PostMapping("/testSendRemindMsg")
    public BaseResult testSendRemindMsg() {
        log.info("test sendRemindMsg");
        sendTianBaoRemindService.overDueTianBaoRemindToUsers();
        return BaseResult.ok();
    }

    @ApiOperation(value = "APP用户消息发送")
    @PassToken
    @PostMapping("/testSendAnalysisMsg")
    public BaseResult testSendAnalysisMsg() {
        log.info("test SendAnalysisMsg");
        sendTianBaoRemindService.statisticalAnalysisRemindToUsers();
        return BaseResult.ok();
    }

}
