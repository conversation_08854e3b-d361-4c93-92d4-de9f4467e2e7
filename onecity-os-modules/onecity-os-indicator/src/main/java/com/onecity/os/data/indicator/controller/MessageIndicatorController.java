package com.onecity.os.data.indicator.controller;


import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.data.indicator.check.IndicatorCommon;
import com.onecity.os.data.indicator.check.IndicatorDetail;
import com.onecity.os.data.indicator.check.IndicatorList;
import com.onecity.os.data.indicator.check.MessageIndicator;

import com.onecity.os.data.indicator.model.vo.MessageIndicatorDetailResVO;
import com.onecity.os.data.indicator.model.vo.MessageIndicatorReqVO;
import com.onecity.os.data.indicator.model.vo.MessageIndicatorResVO;
import com.onecity.os.data.indicator.model.vo.MessageIndicatorTabResVO;
import com.onecity.os.data.indicator.service.MessageIndicatorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/5/21 16:42
 */
@Controller
@RequestMapping("/message")
@Slf4j
@Api(tags = "指标信息相关接口")
public class MessageIndicatorController {

    @Autowired
    private MessageIndicatorService messageIndicatorService;

    /**
     * 根据ICON的id查询信息类指标分类
     *
     * @return
     */
    @ResponseBody
    @PostMapping("/getTabList")
    @ApiOperation(("根据ICON的id查询指标分类"))
    public BaseResult getTabList(@Validated(IndicatorCommon.class) @RequestBody MessageIndicatorReqVO req) {
        List<MessageIndicatorTabResVO> list = messageIndicatorService.listTab(req);
        return BaseResult.ok(list);
    }

    /**
     * 查询某个分类下的指标列表
     *
     * @return
     */
    @ResponseBody
    @PostMapping("/getMessageIndicatorList")
    @ApiOperation(("查询某个分类下的指标列表"))
    public BaseResult getMessageIndicatorList(@Validated(IndicatorList.class) @RequestBody MessageIndicatorReqVO req) {
        List<MessageIndicatorResVO> messageIndicatorResVOS = messageIndicatorService.listMessageIndicator(req);
        return BaseResult.ok(messageIndicatorResVOS);
    }

    /**
     * 查询某个信息类详情
     *
     * @return
     */
    @ResponseBody
    @PostMapping("/getMessageIndicatorDetail")
    @ApiOperation(("查询某个指标的详情"))
    public BaseResult getMessageIndicatorDetail(@Validated(IndicatorDetail.class) @RequestBody MessageIndicatorReqVO req) {
        MessageIndicatorDetailResVO messageIndicatorDetail = messageIndicatorService.getMessageIndicatorDetail(req);
        return BaseResult.ok(messageIndicatorDetail,true);
    }

    /**
     * 查询历史指标信息指标列表
     *
     * @return
     */
    @ResponseBody
    @PostMapping("/getHistoryMessageIndicatorList")
    @ApiOperation(("查询历史指标信息指标列表"))
    public BaseResult getHistoryMessageIndicatorList(@Validated(MessageIndicator.class) @RequestBody MessageIndicatorReqVO req) {
        List<MessageIndicatorResVO> indicatorDetailResVOS = messageIndicatorService.listHistoryMessageIndicator(req);
        return BaseResult.ok(indicatorDetailResVOS);
    }
}
