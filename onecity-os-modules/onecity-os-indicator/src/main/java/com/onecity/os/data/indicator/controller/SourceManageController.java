package com.onecity.os.data.indicator.controller;



import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.data.handler.PassToken;
import com.onecity.os.data.indicator.model.dto.GetMenuListByUserIdAndTypeDto;
import com.onecity.os.data.indicator.model.dto.GetMenuListDto;
import com.onecity.os.data.indicator.model.vo.SourceManageResVO;
import com.onecity.os.data.indicator.service.SourceManageService;

import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-05-21 10:11:56
 */
@Slf4j
@RestController
@RequestMapping("sourceManage")
@Api(tags = "厅局相关接口")
public class SourceManageController {

    @Resource
    private SourceManageService sourceManageService;

    /**
     * 获取厅局列表
     *
     * @return
     */
    @ResponseBody
    @GetMapping("/getList")
    @ApiOperation(value = "获取厅局列表")
    public BaseResult getList() {
        List<SourceManageResVO> sourceManageResVOS = sourceManageService.listSource();
        return BaseResult.ok(sourceManageResVOS);
    }

    /**
     * 根据类型,获取厅局信息
     *
     * @return
     */
    @ResponseBody
    @GetMapping("/getListByType")
    @ApiOperation(value = "根据类型,获取厅局信息")
    @ApiImplicitParam(name = "type", value = "类型", required = true)
    public BaseResult getListByCode(@RequestParam(name = "type", required = true) String type) {
        List<SourceManageResVO> sourceManageResVOS = sourceManageService.getListByCode(type);
        return BaseResult.ok(sourceManageResVOS);
    }

    /**
     * 根据登录的用户id,获取菜单列表
     *
     * @param userId
     * @return
     */
    @PassToken
    @GetMapping("/getMenuListByUserId")
    @ApiOperation(value = "根据登录的用户id,获取菜单列表")
    @ApiImplicitParam(name = "userId", value = "登录的用户id", required = true)
    public BaseResult getMenuListByUserId(@RequestParam(name = "userId", required = true) String userId) {
        log.info("根据登录用户id,获取菜单列表,用户的userId: " + JSONObject.toJSONString(userId));
        GetMenuListByUserIdAndTypeDto dto = sourceManageService.getMenuListByUserId(userId);
        if (ObjectUtils.isEmpty(dto.getSourceList())) {
            return BaseResult.fail(4001,"暂未授权，请联系管理员");
        }
        return BaseResult.ok(dto);
    }

    /**
     * 根据登录的用户角色,获取菜单列表
     *
     * @param roleIds
     * @return
     */
    @PassToken
    @GetMapping("/getMenuListByRoleIds")
    @ApiOperation(value = "根据登录的用户角色,获取菜单列表")
    @ApiImplicitParam(name = "userId", value = "登录的用户id", required = true)
    public BaseResult getMenuListByRoleIds(@RequestParam(name = "roleIds") Long[] roleIds) {
        log.info("根据登录用户id,获取菜单列表,用户的roleIds: " + JSONObject.toJSONString(roleIds));
        GetMenuListByUserIdAndTypeDto dto = new GetMenuListByUserIdAndTypeDto();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail(4000,"未获取到当前用户，请联系管理员");
        }
        dto = sourceManageService.getMenuListByRoleId(loginUser.getSysUser().getRoleIds());
        if (ObjectUtils.isEmpty(dto.getSourceList())) {
            return BaseResult.fail(4001,"暂未授权，请联系管理员");
        }
        return BaseResult.ok(dto);
    }

    /**
     * 根据登录用户id和类型,获取菜单列表
     *
     * @param type   tjzt-市直机关; qxzh-区县纵横
     * @return
     */
    @GetMapping("/getMenuListByUserIdAndType")
    @ApiOperation(value = "根据登录用户id和类型,获取菜单列表")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "userId", value = "登录的用户id", required = true),
            @ApiImplicitParam(name = "type", value = "类型", required = true)})
    public BaseResult getMenuListByUserIdAndType(@RequestParam(name = "type", required = true) String type,
                                                 @RequestParam(name = "userId", required = true) String userId) {

        List<GetMenuListDto> sourceManageResVOS = new ArrayList<>();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail(4000,"未获取到当前用户，请联系管理员");
        }
        sourceManageResVOS = sourceManageService.getMenuListByUserIdAndType(loginUser.getUserid().toString(), type);
        if(ObjectUtils.isEmpty(sourceManageResVOS)){
            BaseResult.fail(4001,"暂未授权，请联系管理员");
        }
        return BaseResult.ok(sourceManageResVOS);
    }
    /**
     * 根据登录用户id获取菜单列表
     *
     * @return
     */
    @GetMapping("/getAllMenuListByUserId")
    @ApiOperation(value = "根据登录用户id和类型,获取菜单列表")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "userId", value = "登录的用户id", required = true)})
    public BaseResult getAllMenuListByUserId(@RequestParam(name = "userId", required = true) String userId) {
        List<GetMenuListDto> sourceManageResVOS = new ArrayList<>();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail(4000,"未获取到当前用户，请联系管理员");
        }
        sourceManageResVOS = sourceManageService.getAllMenuListByUserId(loginUser.getUserid().toString());
        if(ObjectUtils.isEmpty(sourceManageResVOS)){
            BaseResult.fail(4001,"暂未授权，请联系管理员");
        }
        return BaseResult.ok(sourceManageResVOS);
    }


    /**
     * 根据登录用户角色和类型,获取菜单列表
     *
     * @param roleIds
     * @param type   tjzt-市直机关; qxzh-区县纵横
     * @return
     */
    @GetMapping("/getMenuListByRoleIdAndType")
    @ApiOperation(value = "根据登录用户id和类型,获取菜单列表")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "userId", value = "登录的用户id", required = true),
                    @ApiImplicitParam(name = "type", value = "类型", required = true)})
    public BaseResult getMenuListByRoleIdAndType(@RequestParam(name = "roleIds") Long[] roleIds,
                                                 @RequestParam(name = "type", required = true) String type) {
        List<GetMenuListDto> sourceManageResVOS = sourceManageService.getMenuListByRoleIdAndType(roleIds, type);
        if(ObjectUtils.isEmpty(sourceManageResVOS)){
            BaseResult.fail(4001,"暂未授权，请联系管理员");
        }
        return BaseResult.ok(sourceManageResVOS);
    }

    /**
     * 根据登录用户角色获取菜单列表
     *
     * @param roleIds
     * @return
     */
    @GetMapping("/getAllMenuListByRoleId")
    @ApiOperation(value = "根据登录用户id和类型,获取菜单列表")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "userId", value = "登录的用户id", required = true)})
    public BaseResult getAllMenuListByRoleId(@RequestParam(name = "roleIds") Long[] roleIds) {
        List<GetMenuListDto> sourceManageResVOS = sourceManageService.getAllMenuListByRoleId(roleIds);
        if(ObjectUtils.isEmpty(sourceManageResVOS)){
            BaseResult.fail(4001,"暂未授权，请联系管理员");
        }
        return BaseResult.ok(sourceManageResVOS);
    }
}