package com.onecity.os.data.indicator.mapper;


import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.data.indicator.model.po.DataConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DataConfigMapper extends BaseMapper<DataConfig> {


    /**
     * 根据id获取数据配置详情
     * @param id
     * @return
     */
    DataConfig getDataConfigById(@Param("id") String id);

    /**
     *
     */
    List<DataConfig> getDataConfigByIndicatorId(@Param("id") String id);
}
