package com.onecity.os.data.indicator.mapper;


import com.onecity.os.data.indicator.model.po.MessageIndicatorDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2020-05-21 10:11:56
 */
@Mapper
public interface MessageIndicatorDetailMapper {
    /**
     * 根据主键查询
     * @param id
     * @return
     */
    MessageIndicatorDetail selectById(@Param("id")Long id);

    /**
     * 根据指标id查询指标数量
     * @param indicatorId
     * @return
     */
    int countByIndicatorId(@Param("indicatorId")String indicatorId);

    /**
     * 根据指标id查询历史列表
     * @param indicatorId
     * @return
     */
    List<MessageIndicatorDetail> selectByIndicatorId(@Param("indicatorId")String indicatorId);

}