package com.onecity.os.data.indicator.mapper;

import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.data.indicator.model.dto.GetMenuListDto;
import com.onecity.os.data.indicator.model.dto.SourceTableDTO;
import com.onecity.os.data.indicator.model.po.SourceManage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (SourceManage)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-05-21 10:11:56
 */
@Mapper
public interface SourceManageMapper extends BaseMapper<SourceManage> {

    void createIndicatorTable(SourceTableDTO req);

    /**
     * 根据登录用户id,获取菜单列表
     *
     * @param userId
     * @return
     */
    List<GetMenuListDto> getMenuListByUserId(@Param("userId") String userId);

    List<GetMenuListDto> getMenuListByRoleIds(@Param("roleIds") Long[] roleIds);

    List<String> getSourceSimpleNameByRoleIds(@Param("roleIds") List<Long> roleIds);

    List<SourceManage> selectSourceManageList();

    List<SourceManage> selectSourceManageListByType(@Param("type") String type);
    SourceManage getSourceManageBySourceSimpleName(@Param("sourceSimpleName") String sourceSimpleName);

    /**
     * 根据登录用户id和类型,获取菜单列表
     *
     * @param userId
     * @param type
     * @return
     */
    List<GetMenuListDto> getMenuListByUserIdAndType(@Param("userId") String userId, @Param("type") String type);
    List<GetMenuListDto> getAllMenuListByUserId(@Param("userId") String userId, @Param("type") String type);

    List<GetMenuListDto> getMenuListByRoleIdAndType(@Param("roleIds") Long[] roleIds, @Param("type") String type);

    List<GetMenuListDto> getAllMenuListByRoleId(@Param("roleIds") Long[] roleIds,@Param("type") String type);
}