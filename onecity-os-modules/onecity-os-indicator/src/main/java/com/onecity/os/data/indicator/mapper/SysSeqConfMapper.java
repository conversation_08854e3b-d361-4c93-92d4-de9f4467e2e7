package com.onecity.os.data.indicator.mapper;


import com.onecity.os.data.indicator.model.po.SysSeqConf;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SysSeqConfMapper {

    int deleteByPrimaryKey(String seqId);

    int insert(SysSeqConf record);

    int insertSelective(SysSeqConf record);

    SysSeqConf selectByPrimaryKey(String seqId);

    int updateByPrimaryKeySelective(SysSeqConf record);

    int updateByPrimaryKey(SysSeqConf record);
}