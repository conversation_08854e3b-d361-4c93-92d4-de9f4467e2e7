package com.onecity.os.data.indicator.mapper.dingmaillist;



import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.data.indicator.model.po.dingmaillist.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DingMailRoleMapper extends BaseMapper<Role> {

    Role selectRoleByRoleid(String roleid);

    Integer getRoleLevelUp(@Param("roleids") List<String> roleids);

    List<String> selectRoleByLevel(@Param("levelUp")Integer levelUp, @Param("levelDown")Integer levelDown);

    void saveRole(Role role);

    void truncateData();

}
