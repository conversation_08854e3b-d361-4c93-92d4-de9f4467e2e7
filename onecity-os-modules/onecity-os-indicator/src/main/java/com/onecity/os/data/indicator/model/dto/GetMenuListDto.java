package com.onecity.os.data.indicator.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * 获取菜单列表出参
 *
 * <AUTHOR>
 * @date 2021/1/7 上午10:52
 */
@Data
public class GetMenuListDto {
    private Long id;
    //来源名称
    private String sourceName;
    //来源简称
    private String sourceSimpleName;
    //图标地址
    private String iconUrl;
    //类型
    private String type;

    //是否是指标板块：0 否 1 是
    private Integer isIndicators;

    /**
     * 排序
     */
//    @JsonIgnore
    private Integer sequence;

    /**
     * 关联指标id或者外部链接
     */
    private String urlIds;
    /**
     * 链接名称
     */
    private String urlName;
    /**
     * 链接类型0-无链接1-内部2-外部
     */
    private String urlType;

    /**
     * 指标板块一级分组是否隐藏0-隐藏1-展示
     */
    private String firstGroupFlag;

    /**
     * 指标板块二级分组是否隐藏0-隐藏1-展示
     */
    private String secondGroupFlag;
    /**
     * 指标板块信息简报是否展示0-隐藏1-展示
     */
    private String appReportInfoFlag;

    List<GetMenuListDto> childMenu;
}
