package com.onecity.os.data.indicator.model.po;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "data_config")
public class DataConfig {
    /**
     * 数据配置id
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private String id;

    /**
     * 数据集id
     */
    @Column(name = "data_set_id")
    private Long dataSetId;

    /**
     * 数据集名称
     */
    @Column(name = "data_set_name")
    private String dataSetName;

    /**
     * 数值单位
     */
    @Column(name = "data_unit")
    private String dataUnit;

    /**
     * 数据值
     */
    @Column(name = "data_value")
    private String dataValue;

    /**
     * 数据值名称
     */
    @Column(name = "data_value_name")
    private String dataValueName;

    /**
     * X轴自动或者指标项
     */
    @Column(name = "data_key")
    private String dataKey;

    /**
     * X轴自动或者指标项名称
     */
    @Column(name = "data_key_name")
    private String dataKeyName;

    /**
     * 是否删除1-已删除；0-未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 是否是主值，1代表是主值
     */
    @Column(name = "is_master_value")
    private Integer isMasterValue;

    /**
     * 指标id，
     */
    @Column(name = "indicator_id")
    private String indicatorId;

    /**
     * 副值单位
     */
    @Column(name = "secondary_unit")
    private String secondaryUnit;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @Column(name = "creater")
    private String creater;

    /**
     * 更新人
     */
    @Column(name = "updater")
    private String updater;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getDataSetId() {
        return dataSetId;
    }

    public void setDataSetId(Long dataSetId) {
        this.dataSetId = dataSetId;
    }

    public String getDataSetName() {
        return dataSetName;
    }

    public void setDataSetName(String dataSetName) {
        this.dataSetName = dataSetName;
    }

    public String getDataUnit() {
        return dataUnit;
    }

    public void setDataUnit(String dataUnit) {
        this.dataUnit = dataUnit;
    }

    public String getDataValue() {
        return dataValue;
    }

    public void setDataValue(String dataValue) {
        this.dataValue = dataValue;
    }

    public String getDataKey() {
        return dataKey;
    }

    public void setDataKey(String dataKey) {
        this.dataKey = dataKey;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreater() {
        return creater;
    }

    public void setCreater(String creater) {
        this.creater = creater;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }
}