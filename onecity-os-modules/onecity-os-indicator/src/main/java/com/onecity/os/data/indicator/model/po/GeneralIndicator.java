package com.onecity.os.data.indicator.model.po;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;

import lombok.Data;


import javax.persistence.Column;
import javax.persistence.Id;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2020-05-12 16:41:34
 */
@Data
public class GeneralIndicator {
    //指标ID
    @Id
    private String id;
    //指标名称
    private String indicatorName;
    //指标展现类型
    private String indicatorExhibitType;
    //父指标ID,如果为一级指标, 该字段为空
    private String parentId;
    //父指标名称,如果为一级指标, 该字段为空
    private String parentName;

    private String iconUrl;
    private String sourceId;
    private String sourceName;
    //排序
    private Integer sequence;
    //指标类型，0：指标，1：tab类型
    private Integer indicatorType;
    //更新日期文本类型
    private String updateDate;
    //更新周期
    private String updateCycle;
    //面向领导
    private String leader;
    //是否删除0:否1:是
    private Integer delete;
    //创建时间
    private LocalDateTime createTime;
    //创建人
    private String creater;
    //更新时间
    private LocalDateTime updateTime;
    //更新人
    private String updater;
    // 分组类型 0指标 1网有
    private Integer groupType;
    // 分组网页
    private String groupUrl;

    // 是否展示0不展示1展示
    private Integer isShow;

    // 是否展示筛选框0不展示1展示
    private Integer isScreen;

    /**
     * 是否展示筛选框 0-不展示1展示
     */
    private Integer isLegend;

    /**
     * 数据更新方式1-手动填报2数据对接
     */
    private Integer dataUpdateMode;

    /**
     * 数据配置id
     */
    private String dataConfigId;

    /**
     * 关联指标id
     */
    private String urlIds;
    /**
     * 链接名称
     */
    private String urlName;
    /**
     * 链接类型
     */
    private String urlType;
    /**
     * 指标下的指标排列方式
     */
    private String sortType;
    /**
     * 指标名称展示标识0-不展示1-展示
     */
    private String nameShowFlag;
    /**
     * 释义链接
     */
    private String paraUrl;

}