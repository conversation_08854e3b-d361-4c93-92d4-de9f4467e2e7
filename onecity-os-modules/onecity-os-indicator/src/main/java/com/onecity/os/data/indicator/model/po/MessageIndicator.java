package com.onecity.os.data.indicator.model.po;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2020-05-12 16:41:34
 */
@Data
public class MessageIndicator {
    //指标ID
    private String indicatorId;
    //指标名称
    private String indicatorName;
    //排序
    private Integer sequence;

    //指标来源厅局ID
    private String sourceId;
    //指标来源厅局
    private String sourceName;
    //类别名称
    private String categoryName;
    //更新日期文本类型
    private String updateDate;
    //更新周期
    private String updateCycle;

    private Integer type;

    private String typeName;
    //是否删除0:否1:是
    private Integer delete;
    //创建时间
    private LocalDateTime createTime;
    //创建人
    private String creater;
    //更新时间
//    private LocalDateTime updateTime;
    private String updateTime;
    //更新人
    private String updater;

    //置顶更新时间
    private String topUpdateTime;


}