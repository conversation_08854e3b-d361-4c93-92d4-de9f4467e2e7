package com.onecity.os.data.indicator.model.po.dingmaillist;


import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 部门
 * <AUTHOR>
 * @Date 2020/3/19
 * @Version V1.0
 **/
@Data
@Table(name = "dingmail_department")
@ApiModel("部门表")
public class Department {
    /**
     * 主键id
     */
    @Id
    private Long id;
    /**
     * 钉钉部门id
     */
    private Long departmentid;
    /**
     * 部门名称。长度限制为1~64个字符。不允许包含字符‘-’‘，’以及‘,’。
     */
    private String name;
    /**
     * 父部门id,根部门id为1
     */
    private Long parentid;

}
