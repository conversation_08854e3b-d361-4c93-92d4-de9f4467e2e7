package com.onecity.os.data.indicator.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 实体（厅局和一级指标）
 * <AUTHOR>
 */
@Data
public class FirstGeneralIndicatorVO implements Serializable {

    private static final long serialVersionUID = 4983346127581497177L;

    private String id;

    private String indicatorName;
    //排序
    private Integer sequence;

    //二级指标列表
    private List<TwoGeneralIndicatorVO> twoGeneralIndicatorList;
}
