package com.onecity.os.data.indicator.model.vo;

import com.onecity.os.data.indicator.model.po.GeneralIndicatorDataTitle;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.List;

/**
 * 解读区数据
 * <AUTHOR>
 */
@Data
public class GeneralIndicatorDetailVO implements Serializable {

    private static final long serialVersionUID = 853020813359534L;

    private String id;

    private String indicatorName;

    /**
     * 指标展现类型
     **/
    private String indicatorExhibitType;

    /**
     * 指标更新周期
     */
    private String updateCycle;

    /**
     * 是否展示筛选框
     */
    private Integer isScreen;

    /**
     * 是否展示筛选框 0-不展示1展示
     */
    private Integer isLegend;

    /**
     * 指标数据对应数据年份
     */
    private String yearDate;

    /**
     * 数据更新方式1-手动填报2数据对接
     */
    private Integer dataUpdateMode;

    /**
     * 数据配置id
     */
    private String dataConfigId;

    /**
     * 关联指标id
     */
    private String urlIds;
    /**
     * 链接名称
     */
    private String urlName;
    /**
     * 链接类型
     */
    private String urlType;
    /**
     * 指标下的指标排列方式
     */
    private String sortType;
    /**
     * 指标名称展示标识0-不展示1-展示
     */
    private String nameShowFlag;
    /**
     * 释义链接
     */
    private String paraUrl;

    /**
     * 数据集id
     */
    private Long dataSetId;

    /**
     * 数据集名称
     */
    private String dataSetName;

    /**
     * 数值单位
     */
    private String dataUnit;

    /**
     * 数据值
     */
    private String dataValue;

    /**
     * X轴自动或者指标项
     */
    private String dataKey;

    /**
     * 指标数据
     */
    private List<GeneralIndicatorDataVO> list;

    /**
     * 指标数据
     */
    private List<GeneralIndicatorDetailVO> childList;

    /**
     * 指标头数据
     */
    private List<GeneralIndicatorDataTitle> tableHeaderList;

}
