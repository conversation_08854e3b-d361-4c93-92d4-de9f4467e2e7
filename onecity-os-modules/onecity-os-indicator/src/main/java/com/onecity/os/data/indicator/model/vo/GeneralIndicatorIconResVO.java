package com.onecity.os.data.indicator.model.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 图标实体（厅局和一级指标）
 * <AUTHOR>
 */
@Data
public class GeneralIndicatorIconResVO implements Serializable {

    private static final long serialVersionUID = 4983346127581497177L;

    private String id;

    private String indicatorName;

    /**
     * 图标地址
     */
    private String iconUrl;
    /**
     * 分组网页
     */
    private String groupUrl;

}
