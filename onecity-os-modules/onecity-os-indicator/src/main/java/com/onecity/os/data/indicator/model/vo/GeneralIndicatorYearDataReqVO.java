package com.onecity.os.data.indicator.model.vo;


import com.onecity.os.data.indicator.check.IndicatorCommon;
import com.onecity.os.data.indicator.check.IndicatorOther;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 前端请求实体
 *
 * <AUTHOR>
 * @since 2020-02-28 18:07:30
 */
@Data
@ApiModel
public class GeneralIndicatorYearDataReqVO implements Serializable {


    private static final long serialVersionUID = -3765545701758433621L;

    /**
     * 指标id
     */
    @NotEmpty(message = "不能为空", groups = IndicatorOther.class)
    @ApiModelProperty(value = "指标id")
    private String id;
    /**
     * 来源--拼接表名废除
     */
    private String sourceSimpleName;

    /**
     * 展示年份
     */
    @NotEmpty(message = "不能为空")
    @ApiModelProperty(value = "指标更新周期")
    private String updateDate;

    /**
     * 请求年份
     */
    @ApiModelProperty(value = "指标年份选择")
    private String yearDate;

    /**
     * 展示年份
     */
    @ApiModelProperty(value = "展示年份")
    private String showDate;

    @ApiModelProperty(value = "指标类型")
    private Integer indicatorType;

    @NotEmpty(message = "数据来源id不能为空", groups = {IndicatorCommon.class})
    @ApiModelProperty(value = "厅局简称/id")
    private String sourceId;
}