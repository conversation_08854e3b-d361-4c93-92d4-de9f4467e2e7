package com.onecity.os.data.indicator.service;

import com.onecity.os.data.indicator.model.po.DataConfig;

import java.util.List;

public interface DataConfigService {



    /**
     * 根据id获取数据配置
     * @param id
     * @return
     */
    DataConfig getDataConfigById(String id);

    /**
     * 根据指标id获取数据配置
     * @param indicatorId
     * @return
     */
    List<DataConfig> getDataConfigByIndicatorId(String indicatorId);


}
