package com.onecity.os.data.indicator.service;


import com.onecity.os.data.indicator.model.po.GeneralIndicatorData;
import com.onecity.os.data.indicator.model.vo.GeneralIndicatorSearchDataVo;
import com.onecity.os.data.indicator.model.vo.GeneralIndicatorYearDataReqVO;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2020-03-02 15:28:17
 */
public interface GeneralIndicatorDataService {

    List<GeneralIndicatorData> listByIndicatorId(String sourceSimpleName,String indicatorId);

    //根据最大数据期年份以及指标id获取展示的不删除的非年度指标数据
    List<GeneralIndicatorData> listIndicatorDatas(GeneralIndicatorData params);

    //根据最大数据期年份以及指标id获取展示的不删除的年度指标数据
    List<GeneralIndicatorData> listIndicatorYearDatas(GeneralIndicatorData params);

    List<GeneralIndicatorData> list(String sourceSimpleName,GeneralIndicatorData params);

    List<String> listShowDate(String sourceSimpleName,String indicatorId);

    List<GeneralIndicatorData> getDataList(GeneralIndicatorYearDataReqVO req);

    //根据指标名称与角色列表模糊查询指标及指标数据
    List<GeneralIndicatorSearchDataVo> searchIndicator(String indicatorName, Long userId);

    String getMaxUpdateDateYear(String indicatorId);

}