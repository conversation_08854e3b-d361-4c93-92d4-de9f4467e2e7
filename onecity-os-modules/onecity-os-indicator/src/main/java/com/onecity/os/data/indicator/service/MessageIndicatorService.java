package com.onecity.os.data.indicator.service;

import com.onecity.os.data.indicator.model.vo.MessageIndicatorDetailResVO;
import com.onecity.os.data.indicator.model.vo.MessageIndicatorReqVO;
import com.onecity.os.data.indicator.model.vo.MessageIndicatorResVO;
import com.onecity.os.data.indicator.model.vo.MessageIndicatorTabResVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/5/12 17:04
 */
public interface MessageIndicatorService {

    List<MessageIndicatorTabResVO> listTab(MessageIndicatorReqVO req);

    List<MessageIndicatorResVO> listMessageIndicator(MessageIndicatorReqVO req);

    MessageIndicatorDetailResVO getMessageIndicatorDetail(MessageIndicatorReqVO req);

    List<MessageIndicatorResVO> listHistoryMessageIndicator(MessageIndicatorReqVO req);
}
