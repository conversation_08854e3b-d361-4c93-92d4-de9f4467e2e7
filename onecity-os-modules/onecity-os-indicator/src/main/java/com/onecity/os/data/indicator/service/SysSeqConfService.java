package com.onecity.os.data.indicator.service;

import com.onecity.os.data.indicator.model.po.SysSeqConf;

/**
 * <AUTHOR>
 * @Date 2020/5/21 10:38
 */
public interface SysSeqConfService {
    String getStringSequence(String seqId);

    Integer getIntSequence(String seqId);

    String getStringRuleSequence(String seqId,Integer length,String prefix);

    String seqCurrentNext(SysSeqConf sysSeqConf);
}
