package com.onecity.os.data.indicator.service.impl;

import com.onecity.os.data.indicator.model.po.DataConfig;
import com.onecity.os.data.indicator.mapper.DataConfigMapper;
import com.onecity.os.data.indicator.service.DataConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class DataConfigServiceImpl implements DataConfigService {

    @Resource
    private DataConfigMapper dataConfigMapper;



    @Override
    public DataConfig getDataConfigById(String id) {
        return dataConfigMapper.getDataConfigById(id);
    }

    @Override
    public List<DataConfig> getDataConfigByIndicatorId(String indicatorId) {
        return dataConfigMapper.getDataConfigByIndicatorId(indicatorId);
    }


}
