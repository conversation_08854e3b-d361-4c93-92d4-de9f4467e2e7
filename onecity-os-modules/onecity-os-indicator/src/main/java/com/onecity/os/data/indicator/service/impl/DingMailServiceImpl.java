package com.onecity.os.data.indicator.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.onecity.os.data.indicator.app.AppManageUtil;
import com.onecity.os.data.indicator.app.DingEVN;
import com.onecity.os.data.indicator.mapper.dingmaillist.DingMailDepartmentMapper;
import com.onecity.os.data.indicator.mapper.dingmaillist.DingMailRoleMapper;
import com.onecity.os.data.indicator.mapper.dingmaillist.DingMailUserMapper;
import com.onecity.os.data.indicator.model.bean.PageResult;
import com.onecity.os.data.indicator.model.dto.DingdingRoleDTO;
import com.onecity.os.data.indicator.model.dto.GetPhoneInfosByParamsDto;
import com.onecity.os.data.indicator.model.dto.GetPhoneUserInfoByParamsDto;
import com.onecity.os.data.indicator.model.po.dingmaillist.Department;
import com.onecity.os.data.indicator.model.po.dingmaillist.Role;
import com.onecity.os.data.indicator.model.po.dingmaillist.User;
import com.onecity.os.data.indicator.model.vo.dingmaillist.*;
import com.onecity.os.data.indicator.service.DingMailService;
import com.onecity.os.data.indicator.util.BeanHelper;
import com.dingtalk.api.response.OapiDepartmentListResponse;
import com.dingtalk.api.response.OapiRoleListResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@Transactional
public class DingMailServiceImpl implements DingMailService {

    @Autowired
    AppManageUtil appManageUtil;
    @Resource
    DingMailDepartmentMapper dingMailDepartmentMapper;
    @Autowired
    DingMailRoleMapper dingMailRoleMapper;
    @Autowired
    DingMailUserMapper dingMailUserMapper;

    @Override
    public void updateDingDepartmentInfo(String accessToken, String departmentId, Boolean fetchchild) {
        //调用钉钉部门查询接口
        List<OapiDepartmentListResponse.Department> departmentInfos = appManageUtil.getDepartmentInfos(accessToken, departmentId, fetchchild);
        if (CollectionUtils.isNotEmpty(departmentInfos)) {
            dingMailDepartmentMapper.truncateData();
            for (OapiDepartmentListResponse.Department departmentInfo : departmentInfos) {
                Department dep = new Department();
                dep.setDepartmentid(departmentInfo.getId());
                dep.setName(departmentInfo.getName());
                dep.setParentid(departmentInfo.getParentid());
                //将部门信息保存到数据库
                try {
                    dingMailDepartmentMapper.saveDepartment(dep);
                    log.info("部门信息保存成功 --> {}", JSONObject.toJSONString(dep));
                } catch (Exception e) {
                    log.error("部门信息保存失败 --> {}, error -->{}", JSONObject.toJSONString(dep), e.getMessage());
                }
            }
        }
    }

    @Override
    public void updateDingUserInfo(String accessToken, String departmentId, String userId, Boolean fetchchild) {
        try {
            //查询所有部门id
            List<OapiDepartmentListResponse.Department> departmentInfos = appManageUtil.getDepartmentInfos(accessToken, departmentId, fetchchild);
            List<String> departmentids = departmentInfos.stream().map(department -> String.valueOf(department.getId())).collect(Collectors.toList());
            departmentids.add("1");
            List<String> userIds = new ArrayList<>();
            for (String depId : departmentids) {
                List<String> eleIds = appManageUtil.getUserIds(accessToken, depId);
                if (CollectionUtils.isNotEmpty(eleIds)) {
                    userIds.addAll(eleIds);
                }
            }
            userIds = userIds.stream().distinct().collect(Collectors.toList());
            dingMailUserMapper.truncateData();
            for (String userid : userIds) {
                User userInfo = appManageUtil.getUserInfo(accessToken, userid);
                log.info("用户信息保存成功 userinfo --> {}", JSONObject.toJSONString(userInfo));
                //保存用户角色关系信息
                String role = userInfo.getRoles();
                if (null != role && StringUtils.isNotBlank(role)) {
                    String[] roles = role.split(",");
                    //根据roleid查询level
                    Integer roleLevelUp = dingMailRoleMapper.getRoleLevelUp(new ArrayList<>(Arrays.asList(roles)));
                    userInfo.setRolelevel(roleLevelUp);
                }
                //用户在部门中的排序信息
                String orderindepts = userInfo.getOrderindepts();
                Map<Long, Long> map = (Map<Long, Long>) JSONObject.parse(orderindepts);
                for (Map.Entry<Long, Long> entry : map.entrySet()) {
                    User userEle = new User();
                    userEle.setUserid(userInfo.getUserid());
                    userEle.setName(userInfo.getName());
                    userEle.setMobile(userInfo.getMobile());
                    userEle.setPosition(userInfo.getPosition());
                    userEle.setRoles(userInfo.getRoles());
                    userEle.setRolelevel(userInfo.getRolelevel());
                    userEle.setOrderindepts(entry.getValue() + "");
                    userEle.setDepartments(entry.getKey() + "");
                    dingMailUserMapper.saveUser(userEle);
                }
                log.info("用户信息保存成功 userinfo --> {}", JSONObject.toJSONString(userInfo));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void updateDingRoleInfo(String accessToken, String roleId) {
        Map<String, Integer> roleLevelMap = new HashMap<>();
        for (int i = 0; i < DingEVN.DingRoleLevelNames.length; i++) {
            roleLevelMap.put(DingEVN.DingRoleLevelNames[i], Integer.valueOf(DingEVN.DingRoleLevels[i]));
        }
        //调用钉钉角色信息查询接口
        OapiRoleListResponse.OpenRoleGroup roleInfos = appManageUtil.getRoleInfos(accessToken);
        if (null != roleInfos) {
            dingMailRoleMapper.truncateData();
            String groupName = roleInfos.getName();
            Long groupId = roleInfos.getGroupId();
            List<OapiRoleListResponse.OpenRole> roles = roleInfos.getRoles();
            for (OapiRoleListResponse.OpenRole role : roles) {
                Role roleTemp = new Role();
                roleTemp.setGroupname(groupName);
                roleTemp.setGroupid(groupId + "");
                roleTemp.setRoleid(role.getId() + "");
                roleTemp.setRolename(role.getName());
                //等级设置 level
                roleTemp.setLevel(roleLevelMap.get(role.getName()));
                //将角色信息保存到数据库
                dingMailRoleMapper.saveRole(roleTemp);
                log.info("角色信息保存成功 roleInfo --> {}", role);
            }
        }
    }

    @Override
    public Object getDingMailInfos(String accessToken, String userid) {
        try {
            //1、根据用户userid 查出 user 信息
            User userInfo = dingMailUserMapper.selectUserByUserId(userid);
            log.info("用户基本信息为 user --> {}", JSONObject.toJSONString(userInfo));

            //2、获取角色信息
            String roles = userInfo.getRoles();
            Role roleInfo = dingMailRoleMapper.selectRoleByRoleid(roles);
            log.info("用户角色信息为 role --> {}", JSONObject.toJSONString(roleInfo));
            //根据角色等级查询可以获取的角色范围
            Integer levelUp = roleInfo.getLevel() - DingEVN.DingRoleLevelUp;
            Integer levelDown = roleInfo.getLevel() + DingEVN.DingRoleLevelDown;
            List<String> roleids = dingMailRoleMapper.selectRoleByLevel(levelUp, levelDown);

            //3、获取一级部门信息
            List<OapiDepartmentListResponse.Department> departmentInfos = appManageUtil.getDepartmentInfos(accessToken, null, false);
//            List<String> departmentids = departmentInfos.stream().map(department -> String.valueOf(department.getId())).collect(Collectors.toList());
            List<DingMailInfo> dingMailInfoList = new ArrayList<>();
            //4、根据角色等级level 来查询 此userid 可以看到的通讯录范围， 根据部门id逐层获取
            for (OapiDepartmentListResponse.Department departmentEle : departmentInfos) {
                DingMailInfo dingMailInfo = new DingMailInfo();
                dingMailInfo.setId(departmentEle.getId());
                dingMailInfo.setName(departmentEle.getName());

                //查询外层符合角色等级的通讯录用户
                List<User> firstUsers = dingMailUserMapper.selectUserByRoleAndDep(departmentEle.getId() + "", roleids);
                if (null != firstUsers && CollectionUtils.isNotEmpty(firstUsers)) {
                    List<DingMailChildren> dingMailChildrenFirst = new ArrayList<>();
                    for (User firstUser : firstUsers) {
                        DingMailChildren dingMailChildren = new DingMailChildren();
                        dingMailChildren.setUid(firstUser.getId());
                        dingMailChildren.setNumber(firstUser.getMobile());
                        dingMailChildren.setAmember(firstUser.getName());
                        dingMailChildren.setJob(firstUser.getPosition());
                        dingMailChildrenFirst.add(dingMailChildren);
                    }
                    dingMailInfo.setChildren(dingMailChildrenFirst);
                }
                //查询下级部门通讯录
                List<OapiDepartmentListResponse.Department> secondDepartmentInfos = appManageUtil.getDepartmentInfos(accessToken, departmentEle.getId() + "", false);
                if (null != secondDepartmentInfos && CollectionUtils.isNotEmpty(secondDepartmentInfos)) {
                    List<DingMailFather> dingMailFathers = new ArrayList<>();
                    for (OapiDepartmentListResponse.Department secondDepartmentInfo : secondDepartmentInfos) {
                        List<DingMailChildren> dingMailChildrenSecond = new ArrayList<>();
                        //查询内层符合角色等级的通讯录用户
                        List<User> secondUsers = dingMailUserMapper.selectUserByRoleAndDep(secondDepartmentInfo.getId() + "", roleids);
                        if (CollectionUtils.isNotEmpty(secondUsers)) {
                            DingMailFather dingMailFather = new DingMailFather();
                            dingMailFather.setUnames(secondDepartmentInfo.getName());
                            for (User secondUser : secondUsers) {
                                DingMailChildren dingMailChildren2 = new DingMailChildren();
                                dingMailChildren2.setUid(secondUser.getId());
                                dingMailChildren2.setNumber(secondUser.getMobile());
                                dingMailChildren2.setAmember(secondUser.getName());
                                dingMailChildren2.setJob(secondUser.getPosition());
                                dingMailChildrenSecond.add(dingMailChildren2);
                            }
                            dingMailFather.setChildren(dingMailChildrenSecond);
                            dingMailFathers.add(dingMailFather);
                        }
                    }
                    if (null != dingMailFathers && CollectionUtils.isNotEmpty(dingMailFathers))
                        dingMailInfo.setFather(dingMailFathers);
                }
                if (null != dingMailInfo && (null != dingMailInfo.getChildren() || null != dingMailInfo.getFather()))
                    dingMailInfoList.add(dingMailInfo);
            }
            return JSONObject.toJSON(dingMailInfoList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

//    @Override
//    public Object getDingMailInfos(String accessToken, String userid) {
//        try {
//            // 创建出参
//            List<DingMailInfo> dingMailInfoList = new ArrayList<>();
//            //1、根据用户userid 查出 user 信息
//            User userInfo = dingMailUserMapper.selectUserByUserId(userid);
//            log.info("用户基本信息为 user --> {}", JSONObject.toJSONString(userInfo));
//            //2、获取角色信息
//            String roles = userInfo.getRoles();
//            Role roleInfo = dingMailRoleMapper.selectRoleByRoleid(roles);
//            log.info("用户角色信息为 role --> {}", JSONObject.toJSONString(roleInfo));
//            //根据角色等级查询可以获取的角色范围
//            Integer levelUp = roleInfo.getLevel() - DingEVN.DingRoleLevelUp;
//            Integer levelDown = roleInfo.getLevel() + DingEVN.DingRoleLevelDown;
//            //3、获取一级部门信息
//            List<OapiDepartmentListResponse.Department> departmentInfos = dingDingManageUtil.getDepartmentInfos(accessToken, null, false);
//
//            DingMailInfo dingMailInfo = new DingMailInfo();
//            if (CollectionUtil.isNotEmpty(departmentInfos)) {
//                // 获取部门id集合
//                List<String> departmentIds = departmentInfos.stream().map(department -> String.valueOf(department.getId())).collect(Collectors.toList());
//                List<String> departmentNames = departmentInfos.stream().map(department -> String.valueOf(department.getName())).collect(Collectors.toList());
//                // 4.跟据一级部门ids和角色等级,查询出外层符合角色等级的通讯录用户
//                List<User> firstUsers = dingMailUserMapper.selectUserByDepIdsAndRoleLevel(departmentIds, levelUp, levelDown);
//                // 如果一级用户为空,直接赋值部门id和部门名称
//                if (CollectionUtil.isEmpty(firstUsers)) {
//                    for (int i = 0; i < departmentIds.size(); i++) {
//                        dingMailInfo.setId(Long.valueOf(departmentIds.get(i)));
//                        dingMailInfo.setName(departmentNames.get(i));
//                        // 查询下级部门通讯录
//                        getSecondDepartmentInfos(accessToken, dingMailInfo, departmentIds.get(i), levelUp, levelDown);
//                    }
//                } else {
//                    for (int i = 0; i < departmentIds.size(); i++) {
//                        dingMailInfo.setId(Long.valueOf(departmentIds.get(i)));
//                        dingMailInfo.setName(departmentNames.get(i));
//                        // 遍历一级部门的用户,并和相应的一级部门信息相匹配
//                        List<DingMailChildren> dingMailChildrenFirst = new ArrayList<>();
//                        int firstUsersSize = firstUsers.size();
//                        for (int j = 0; j < firstUsersSize; j++) {
//                            if (firstUsers.get(j).getDepartments().equals(departmentIds.get(i))) {
//                                DingMailChildren dingMailChildren = new DingMailChildren();
//                                dingMailChildren.setUid(firstUsers.get(j).getId());
//                                dingMailChildren.setNumber(firstUsers.get(j).getMobile());
//                                dingMailChildren.setAmember(firstUsers.get(j).getName());
//                                dingMailChildren.setJob(firstUsers.get(j).getPosition());
//                                dingMailChildrenFirst.add(dingMailChildren);
//                                // 移除符合条件的用户,继续遍历剩下的用户
//                                firstUsers.remove(firstUsers.get(j));
//                                firstUsersSize -= 1;
//                                j -= 1;
//                            }
//                        }
//                        dingMailInfo.setChildren(dingMailChildrenFirst);
//                        // 查询下级部门通讯录
//                        getSecondDepartmentInfos(accessToken, dingMailInfo, departmentIds.get(i), levelUp, levelDown);
//                    }
//                }
//            }
//            if (null != dingMailInfo.getChildren() || null != dingMailInfo.getFather())
//                dingMailInfoList.add(dingMailInfo);
//            return JSONObject.toJSON(dingMailInfoList);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
//
//    /**
//     * 查询下级部门通讯录
//     *
//     * @param accessToken
//     * @param dingMailInfo
//     * @param departmentId
//     * @param levelUp
//     * @param levelDown
//     */
//    private void getSecondDepartmentInfos(String accessToken, DingMailInfo dingMailInfo, String departmentId, Integer levelUp, Integer levelDown) {
//        // 查询下级部门通讯录
//        List<OapiDepartmentListResponse.Department> secondDepartmentInfos = dingDingManageUtil.getDepartmentInfos(accessToken, departmentId + "", false);
//        if (CollectionUtil.isNotEmpty(secondDepartmentInfos)) {
//            // 获取二级部门id集合
//            List<String> secondDepartmentIds = secondDepartmentInfos.stream().map(department -> String.valueOf(department.getId())).collect(Collectors.toList());
//            List<String> secondDepartmentNames = secondDepartmentInfos.stream().map(department -> String.valueOf(department.getName())).collect(Collectors.toList());
//            // 4.跟据二级部门ids和角色等级,查询出符合角色等级的通讯录用户
//            List<User> secondUsers = dingMailUserMapper.selectUserByDepIdsAndRoleLevel(secondDepartmentIds, levelUp, levelDown);
//
//            List<DingMailFather> dingMailFathers = new ArrayList<>();
//            // 遍历用户信息
//            if (CollectionUtil.isNotEmpty(secondUsers)) {
//                for (int i = 0; i < secondDepartmentIds.size(); i++) {
//                    List<DingMailChildren> dingMailChildrenSecond = new ArrayList<>();
//                    DingMailFather dingMailFather = new DingMailFather();
//                    dingMailFather.setUnames(secondDepartmentNames.get(i));
//                    int secondUsersSize = secondUsers.size();
//                    for (int j = 0; j < secondUsersSize; j++) {
//                        if (secondUsers.get(j).getDepartments().equals(secondDepartmentIds.get(i))) {
//                            DingMailChildren dingMailChildren2 = new DingMailChildren();
//                            dingMailChildren2.setUid(secondUsers.get(j).getId());
//                            dingMailChildren2.setNumber(secondUsers.get(j).getMobile());
//                            dingMailChildren2.setAmember(secondUsers.get(j).getName());
//                            dingMailChildren2.setJob(secondUsers.get(j).getPosition());
//                            dingMailChildrenSecond.add(dingMailChildren2);
//                            // 移除符合条件的用户,继续遍历剩下的用户
//                            secondUsers.remove(secondUsers.get(j));
//                            secondUsersSize -= 1;
//                            j -= 1;
//                        }
//                    }
//                    dingMailFather.setChildren(dingMailChildrenSecond);
//                    dingMailFathers.add(dingMailFather);
//                }
//            }
//            if (CollectionUtils.isNotEmpty(dingMailFathers))
//                dingMailInfo.setFather(dingMailFathers);
//        }
//    }


    @Override
    public List<OapiDepartmentListResponse.Department> getPhoneFirstDepartments(String accessToken, String userid) {
        try {
            // 获取一级部门信息
            return appManageUtil.getDepartmentInfos(accessToken, null, false);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public DingMailInfo getPhoneSecondDepartmentInfos(String accessToken, String userid, Long id) {
        try {
            // 创建返回对象
            DingMailInfo dingMailInfo = new DingMailInfo();
            // 根据用户userid 查出 user 信息
            User userInfo = dingMailUserMapper.selectUserByUserId(userid);
            log.info("用户基本信息为 user --> {}", JSONObject.toJSONString(userInfo));
            // 获取角色信息
            String roles = userInfo.getRoles();
            Role roleInfo = dingMailRoleMapper.selectRoleByRoleid(roles);
            log.info("用户角色信息为 role --> {}", JSONObject.toJSONString(roleInfo));
            // 根据角色等级查询可以获取的角色范围
            Integer levelUp = roleInfo.getLevel() - DingEVN.DingRoleLevelUp;
            Integer levelDown = roleInfo.getLevel() + DingEVN.DingRoleLevelDown;
            // 根据角色等级level 来查询 此userid 可以看到的通讯录范围
            List<String> roleids = dingMailRoleMapper.selectRoleByLevel(levelUp, levelDown);
            // 根据部门id,角色ids,查询外层符合角色等级的通讯录用户
            List<User> firstUsers = dingMailUserMapper.selectUserByRoleAndDep(id + "", roleids);
            if (CollectionUtils.isNotEmpty(firstUsers)) {
                List<DingMailChildren> dingMailChildrenFirst = new ArrayList<>();
                for (User firstUser : firstUsers) {
                    DingMailChildren dingMailChildren = new DingMailChildren();
                    dingMailChildren.setUid(firstUser.getId());
                    dingMailChildren.setNumber(firstUser.getMobile());
                    dingMailChildren.setAmember(firstUser.getName());
                    dingMailChildren.setJob(firstUser.getPosition());
                    dingMailChildrenFirst.add(dingMailChildren);
                }
                dingMailInfo.setChildren(dingMailChildrenFirst);
            }
            // 查询下级部门通讯录
            List<OapiDepartmentListResponse.Department> secondDepartmentInfos = appManageUtil.getDepartmentInfos(accessToken, id + "", false);
            if (CollectionUtils.isNotEmpty(secondDepartmentInfos)) {
                List<DingMailFather> dingMailFathers = new ArrayList<>();
                for (OapiDepartmentListResponse.Department secondDepartmentInfo : secondDepartmentInfos) {
                    List<DingMailChildren> dingMailChildrenSecond = new ArrayList<>();
                    //查询内层符合角色等级的通讯录用户
                    List<User> secondUsers = dingMailUserMapper.selectUserByRoleAndDep(secondDepartmentInfo.getId() + "", roleids);
                    if (CollectionUtils.isNotEmpty(secondUsers)) {
                        DingMailFather dingMailFather = new DingMailFather();
                        dingMailFather.setUnames(secondDepartmentInfo.getName());
                        for (User secondUser : secondUsers) {
                            DingMailChildren dingMailChildren2 = new DingMailChildren();
                            dingMailChildren2.setUid(secondUser.getId());
                            dingMailChildren2.setNumber(secondUser.getMobile());
                            dingMailChildren2.setAmember(secondUser.getName());
                            dingMailChildren2.setJob(secondUser.getPosition());
                            dingMailChildrenSecond.add(dingMailChildren2);
                        }
                        dingMailFather.setChildren(dingMailChildrenSecond);
                        dingMailFathers.add(dingMailFather);
                    }
                }
                if (CollectionUtils.isNotEmpty(dingMailFathers)) {
                    dingMailInfo.setFather(dingMailFathers);
                }
            }
            return dingMailInfo;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public PageResult getPhoneInfosPageList(String accessToken, String userid, Integer page, Integer size) {
        // 根据用户userid 查出 user 信息
        User userInfo = dingMailUserMapper.selectUserByUserId(userid);
        log.info("用户基本信息为 user --> {}", JSONObject.toJSONString(userInfo));
        // 获取角色等级level
        Integer roleLevel = userInfo.getRolelevel();
        // 根据角色等级查询可以获取的角色范围
        Integer levelUp = 0;
        Integer levelDown = 0;
        if(roleLevel != null){
             levelUp = roleLevel - DingEVN.DingRoleLevelUp;
             levelDown = roleLevel + DingEVN.DingRoleLevelDown;
        }
        // 根据角色等级level 来查询 此userid 可以看到的通讯录范围
        List<String> roleids = dingMailRoleMapper.selectRoleByLevel(levelUp, levelDown);
        // 查找所有部门信息
//        QueryWrapper<Department> queryWrapper = new QueryWrapper();
//        queryWrapper.eq("parentid", "1");
//        IPage<Department> iPage = new Page<>(page, size);
        PageHelper.startPage(page,size);
        List<Department> departmentIPage = dingMailDepartmentMapper.selectDepartByparentId();
        Long total =  new PageInfo(departmentIPage).getTotal();
        int pages = new PageInfo(departmentIPage).getPages();
        List<GetPhoneInfosByParamsDto> departmentList = BeanHelper.copyWithCollection(departmentIPage, GetPhoneInfosByParamsDto.class);
        // 查询部门下面的人员数量
        if (CollectionUtils.isNotEmpty(departmentList)) {
            List<Long> ids = null;
            for (GetPhoneInfosByParamsDto department : departmentList) {
                ids = new ArrayList<>();
                ids.add(department.getDepartmentid());
                // 根据部门id,查找该部门下的子部门
                getChildDepartmentsByParentId(department.getDepartmentid(), ids);
                // 根据部门id和角色id,查找该部门下面的人员数量
                //Integer userNum = dingMailUserMapper.getUserNumByDepartmentIdsAndRoleIds(ids, roleids);
                List<User> checkUser = new ArrayList<>();
                ids.forEach(depart-> {
                    //找到roleids匹配数据
                    List<User> users = dingMailUserMapper.getUserByDepart(depart);
                    users.forEach(user -> {
                        String userRoles = user.getRoles();
                        if (StringUtils.isNotEmpty(userRoles)){
                            String[] userRole = userRoles.split(",");
                            Arrays.stream(userRole).filter(roleids::contains).findFirst().ifPresent(checkRole -> checkUser.add(user));
                        }
                    });
                });
                department.setName(department.getName() + "（" + checkUser.size() + "人）");
            }
        }
//        return new PageResult<>(departmentList, departmentIPage.getTotal(), departmentIPage.getPages());
        return new PageResult(departmentList, total, Long.valueOf(pages));
    }

    /**
     * 根据父部门id,查找子部门id
     *
     * @param id  部门id
     * @param ids 所有部门id
     */
    private void getChildDepartmentsByParentId(Long id, List<Long> ids) {
        List<Long> departmentIds = dingMailDepartmentMapper.getDepartmentIdByParentId(id);
        if (CollectionUtils.isNotEmpty(departmentIds)) {
            ids.addAll(departmentIds);
            for (Long id1 : departmentIds) {
                getChildDepartmentsByParentId(id1, ids);
            }
        }
    }

    @Override
    public DingMailInfo getFirstDepartUserInfosById(String userid, Long id) {
        try {
            // 创建返回对象
            DingMailInfo dingMailInfo = new DingMailInfo();
            // 根据用户userid 查出 user 信息
            User userInfo = dingMailUserMapper.selectUserByUserId(userid);
            log.info("用户基本信息为 user --> {}", JSONObject.toJSONString(userInfo));
            // 获取角色等级level
            Integer roleLevel = userInfo.getRolelevel();
            // 根据角色等级查询可以获取的角色范围
            Integer levelUp = 0;
            Integer levelDown = 0;
            if(roleLevel != null){
                levelUp = roleLevel - DingEVN.DingRoleLevelUp;
                levelDown = roleLevel + DingEVN.DingRoleLevelDown;
            }
            // 根据角色等级level 来查询 此userid 可以看到的通讯录范围
            List<String> roleids = dingMailRoleMapper.selectRoleByLevel(levelUp, levelDown);
            // 根据部门id,角色ids,查询外层符合角色等级的通讯录用户
//            List<User> firstUsers = dingMailUserMapper.selectUserByRoleAndDep(id + "", roleids);
            List<User> firstUsers = new ArrayList<>();
            List<User> users = dingMailUserMapper.getUserByDepart(id);
            users.forEach(user -> {
                String userRoles = user.getRoles();
                if (StringUtils.isNotEmpty(userRoles)){
                    String[] userRole = userRoles.split(",");
                    Arrays.stream(userRole).filter(roleids::contains).findFirst().ifPresent(checkRole -> firstUsers.add(user));
                }
            });
            if (CollectionUtils.isNotEmpty(firstUsers)) {
                List<DingMailChildren> dingMailChildrenFirst = new ArrayList<>();
                for (User firstUser : firstUsers) {
                    DingMailChildren dingMailChildren = new DingMailChildren();
                    dingMailChildren.setUid(firstUser.getId());
                    dingMailChildren.setNumber(firstUser.getMobile());
                    dingMailChildren.setAmember(firstUser.getName());
                    dingMailChildren.setJob(firstUser.getPosition());
                    dingMailChildrenFirst.add(dingMailChildren);
                }
                dingMailInfo.setChildren(dingMailChildrenFirst);
            }
            // 查询下级部门通讯录
            getChildDeparInfosByParentId(id, roleids, dingMailInfo);
            return dingMailInfo;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 查询下级部门通讯录
     *
     * @param id
     * @param roleids
     * @param dingMailInfo
     */
    private void getChildDeparInfosByParentId(Long id, List<String> roleids, DingMailInfo dingMailInfo) {
        List<Department> departmentIds = dingMailDepartmentMapper.getDepartmentInfoByParentId(id);
        if (CollectionUtils.isNotEmpty(departmentIds)) {
            List<DingMailFather> dingMailFathers = new ArrayList<>();
            List<Long> ids;
            for (Department secondDepartmentInfo : departmentIds) {
                ids = new ArrayList<>();
                ids.add(secondDepartmentInfo.getDepartmentid());
                // 根据部门id,查找该部门下的全部子部门
                getChildDepartmentsByParentId(secondDepartmentInfo.getDepartmentid(), ids);
                // 根据部门id,角色ids,查询外层符合角色等级的通讯录用户
                List<User> firstUsers = new ArrayList<>();
                List<User> users = dingMailUserMapper.getUserByDepart(secondDepartmentInfo.getDepartmentid());
                users.forEach(user -> {
                    String userRoles = user.getRoles();
                    if (StringUtils.isNotEmpty(userRoles)){
                        String[] userRole = userRoles.split(",");
                        Arrays.stream(userRole).filter(roleids::contains).findFirst().ifPresent(checkRole -> firstUsers.add(user));
                    }
                });
                DingMailFather dingMailFather = new DingMailFather();
                dingMailFather.setDepartmentid(secondDepartmentInfo.getDepartmentid());
                dingMailFather.setUnames(secondDepartmentInfo.getName() + "（" + firstUsers.size() + "人）");
                // 此版本部门暂时不展示人员
//                List<DingMailChildren> dingMailChildrenSecond = new ArrayList<>();
//                if (CollectionUtils.isNotEmpty(secondUsers)) {
//                    for (User secondUser : secondUsers) {
//                        DingMailChildren dingMailChildren2 = new DingMailChildren();
//                        dingMailChildren2.setUid(secondUser.getId());
//                        dingMailChildren2.setNumber(secondUser.getMobile());
//                        dingMailChildren2.setAmember(secondUser.getName());
//                        dingMailChildren2.setJob(secondUser.getPosition());
//                        dingMailChildrenSecond.add(dingMailChildren2);
//                    }
//                    dingMailFather.setChildren(dingMailChildrenSecond);
//                }
                dingMailFathers.add(dingMailFather);
            }
            if (CollectionUtils.isNotEmpty(dingMailFathers)) {
                dingMailInfo.setFather(dingMailFathers);
            }
        }
    }

    @Override
    public PageResult<GetPhoneUserInfoByParamsDto> getPhoneUserInfoByParams(String userid, String params, Integer page,
                                                                            Integer size) {
        // 创建返回对象
        List<GetPhoneUserInfoByParamsDto> dtos = new ArrayList<>();
        // 根据用户userid 查出 user 信息
        User userInfo = dingMailUserMapper.selectUserByUserId(userid);
        log.info("用户基本信息为 user --> {}", JSONObject.toJSONString(userInfo));
        // 获取角色等级level
        Integer roleLevel = userInfo.getRolelevel();
        // 根据角色等级查询可以获取的角色范围
        Integer levelUp = 0;
        Integer levelDown = 0;
        if(roleLevel != null){
            levelUp = roleLevel - DingEVN.DingRoleLevelUp;
            levelDown = roleLevel + DingEVN.DingRoleLevelDown;
        }
        // 根据角色等级level 来查询 此userid 可以看到的通讯录范围
        List<String> roleids = dingMailRoleMapper.selectRoleByLevel(levelUp, levelDown);
        // 如果查询的是人员名称,进行人员的查询
        GetPhoneUserInfoByParamsDto dto1 = getPhoneUserInfoByUserNameParams(params, levelUp, levelDown,dtos, page, size);
        // 如果查询的是部门名称,进行部门的查询
        GetPhoneUserInfoByParamsDto dto2 = getPhoneUserInfoByDepartNameParams(params, roleids, dtos, page, size);
        // 这个totalPage是无效的,与实际核对不上,前端不使用这个
        double totalPage = (double) (dto1.getTotal() + dto2.getTotal()) / size;
        // 设置总页数
        long totalPageLong = (long) Math.ceil(totalPage);
        // 截取返回的数据
        return new PageResult<>(dtos, dto1.getTotal() + dto2.getTotal(), totalPageLong);
    }

    /**
     * 如果查询的是部门名称,进行部门的查询
     *
     * @param params
     * @param roleids
     * @param dtos
     * @param page
     * @param size
     */
    private GetPhoneUserInfoByParamsDto getPhoneUserInfoByDepartNameParams(String params, List<String> roleids, List<GetPhoneUserInfoByParamsDto> dtos,
                                                                           Integer page, Integer size) {
        GetPhoneUserInfoByParamsDto dto2 = new GetPhoneUserInfoByParamsDto();
        // 根据搜索内容,查询相关部门,先查一级部门
//        QueryWrapper<Department> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("parentid", 1);
//        queryWrapper.like("name", params);
//        IPage<Department> iPage = new Page<>(page, size);
        PageHelper.startPage(page,size);
        List<Department> departIPage = dingMailDepartmentMapper.selectDepartByparentIdAndName(params);
        List<GetPhoneUserInfoByParamsDto> departmentList = BeanHelper.copyWithCollection(departIPage, GetPhoneUserInfoByParamsDto.class);
        // 设置总数
        Long total = Long.valueOf(0);
        Long totalPage = Long.valueOf(0);
        total += (new PageInfo(departIPage)).getTotal();
        totalPage += (new PageInfo(departIPage)).getPages();
        if (CollectionUtils.isNotEmpty(departmentList)) {
            // 部门ids
            List<Long> ids = new ArrayList<>();
            // 创建部门参数
            GetPhoneUserInfoByParamsDto dto;
            for (GetPhoneUserInfoByParamsDto department : departmentList) {
                dto = new GetPhoneUserInfoByParamsDto();
                ids = new ArrayList<>();
                ids.add(department.getDepartmentid());
                // 根据部门id,查找该部门下的子部门
                getChildDepartmentsByParentId(department.getDepartmentid(), ids);
                // 根据部门id,角色ids,查询外层符合角色等级的通讯录用户
                List<User> firstUsers = new ArrayList<>();
                ids.forEach(depart-> {
                    //找到roleids匹配数据
                    List<User> users = dingMailUserMapper.getUserByDepart(depart);
                    users.forEach(user -> {
                        String userRoles = user.getRoles();
                        if (StringUtils.isNotEmpty(userRoles)){
                            String[] userRole = userRoles.split(",");
                            Arrays.stream(userRole).filter(roleids::contains).findFirst().ifPresent(checkRole -> firstUsers.add(user));
                        }
                    });
                });
                dto.setId(department.getDepartmentid());
                dto.setAmember(department.getName() + "（" + firstUsers.size() + "人）");
                dto.setType(1);
                dtos.add(dto);
            }
        }
        // 再查找二级及以下部门
//        QueryWrapper<Department> queryWrapper2 = new QueryWrapper<>();
//        queryWrapper2.like("name", params);
//        queryWrapper2.ne("parentid", 1);
//        IPage<Department> iPage2 = new Page<>(page, size);
        PageHelper.startPage(page,size);
        List<Department> departIPage2 = dingMailDepartmentMapper.selectDepartByparentIdAndName(params);
        List<GetPhoneUserInfoByParamsDto> departmentList2 = BeanHelper.copyWithCollection(departIPage2, GetPhoneUserInfoByParamsDto.class);
        // 设置总数
        total += (new PageInfo(departIPage2)).getTotal();
        totalPage += (new PageInfo(departIPage2)).getPages();
        dto2.setTotal(total);
        dto2.setTotalPage(totalPage);
        if (CollectionUtils.isNotEmpty(departmentList2)) {
            // 部门ids
            List<Long> ids2 = new ArrayList<>();
            for (GetPhoneUserInfoByParamsDto depart2 : departmentList2) {
                ids2 = new ArrayList<>();
                ids2.add(depart2.getDepartmentid());
                // 创建部门参数
                GetPhoneUserInfoByParamsDto dto = new GetPhoneUserInfoByParamsDto();
                dto.setId(depart2.getDepartmentid());
                String departName = "";
                departName = depart2.getName();
                // 根据部门id,查找所在所有部门和上级部门,并拼接部门
                departName = getAllDepartByDepartId(depart2.getParentid(), departName);
                dto.setDepartAndJob(departName);
                // 根据部门id,查找该部门下的子部门id
                getChildDepartmentsByParentId(depart2.getDepartmentid(), ids2);
                // 根据部门id和角色id,查找该部门下面的人员数量
                List<User> firstUsers = new ArrayList<>();
                ids2.forEach(depart-> {
                    //找到roleids匹配数据
                    List<User> users = dingMailUserMapper.getUserByDepart(depart);
                    users.forEach(user -> {
                        String userRoles = user.getRoles();
                        if (StringUtils.isNotEmpty(userRoles)){
                            String[] userRole = userRoles.split(",");
                            Arrays.stream(userRole).filter(roleids::contains).findFirst().ifPresent(checkRole -> firstUsers.add(user));
                        }
                    });
                });
                dto.setId(depart2.getDepartmentid());
                dto.setAmember(depart2.getName() + "（" + firstUsers.size() + "人）");
                dto.setType(1);
                dtos.add(dto);
            }
        }
        return dto2;
    }

    /**
     * 如果查询的是人员名称,进行人员的查询
     *
     * @param params
     * @param
     * @param dtos
     * @param page
     * @param size
     */
    private GetPhoneUserInfoByParamsDto getPhoneUserInfoByUserNameParams(String params, Integer levelUp,Integer levelDown,
                                                                         List<GetPhoneUserInfoByParamsDto> dtos,
                                                                         Integer page, Integer size) {
        GetPhoneUserInfoByParamsDto dto1 = new GetPhoneUserInfoByParamsDto();
        // 根据搜索内容,角色ids,查询外层符合角色等级的通讯录用户
//        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
//        queryWrapper.like("name", params);
//        queryWrapper.between("rolelevel",levelUp,levelDown);
//        IPage<User> iPage = new Page<>(page, size);
        PageHelper.startPage(page,size);
        List<User> userInfoIPage = dingMailUserMapper.selectUserByNameAndLevel(params,levelUp,levelDown);
        List<GetPhoneUserInfoByParamsDto> dtoList = BeanHelper.copyWithCollection(userInfoIPage, GetPhoneUserInfoByParamsDto.class);
        // 设置总数
        Long total = Long.valueOf(0);
        Long totalPage = Long.valueOf(0);
        total += (new PageInfo(userInfoIPage)).getTotal();
        totalPage += (new PageInfo(userInfoIPage)).getPages();
        dto1.setTotal(total);
        dto1.setTotalPage(totalPage);
        // 再根据人员所在一级部门,拼接部门+职位
        if (CollectionUtils.isNotEmpty(dtoList)) {
            // 创建出参对象
            GetPhoneUserInfoByParamsDto dto;
            for (GetPhoneUserInfoByParamsDto user1 : dtoList) {
                dto = new GetPhoneUserInfoByParamsDto();
                dto.setId(user1.getId());
                dto.setAmember(user1.getName());
                dto.setNumber(user1.getMobile());
                String departName = "";
                if (StringUtils.isNotBlank(user1.getPosition())) {
                    departName = "/" + user1.getPosition();
                }
                // 查找人员所在部门(最底层的部门)
//                QueryWrapper<Department> queryWrapper2 = new QueryWrapper<>();
//                queryWrapper2.eq("departmentid", user1.getDepartments());
                Department department = dingMailDepartmentMapper.selectDepartByDepartId(user1.getDepartments());
                // 查找上级部门
                if (ObjectUtil.isNotNull(department)) {
                    departName = department.getName() + departName;
                    // 根据部门id,查找所在所有部门和上级部门,并拼接部门
                    departName = getAllDepartByDepartId(department.getParentid(), departName);
                    dto.setDepartAndJob(departName);
                }
                dto.setType(0);
                dtos.add(dto);
            }
        }
        return dto1;
    }

    /**
     * 根据部门id,查找所在所有部门和上级部门,并拼接部门
     *
     * @param departId
     * @param departName
     */
    private String getAllDepartByDepartId(Long departId, String departName) {
//        QueryWrapper<Department> queryWrapper2 = new QueryWrapper<>();
//        queryWrapper2.eq("departmentid", departId);
        Department department = dingMailDepartmentMapper.selectDepartByDepartId(String.valueOf(departId));
        if (ObjectUtil.isNotNull(department)) {
            departName = department.getName() + "/" + departName;
            // 继续查找
            getAllDepartByDepartId(department.getParentid(), departName);
        }
        return departName;
    }

    @Override
    public List<GetPhoneUserInfoByParamsDto> getSecondPhoneUserByParams(String userid, String params, Long departmentid) {
        // 创建返回对象
        List<GetPhoneUserInfoByParamsDto> dtos = new ArrayList<>();
        // 根据用户userid 查出 user 信息
        User userInfo = dingMailUserMapper.selectUserByUserId(userid);
        log.info("用户基本信息为 user --> {}", JSONObject.toJSONString(userInfo));
        // 获取角色等级level
        Integer roleLevel = userInfo.getRolelevel();
        // 根据角色等级查询可以获取的角色范围
        Integer levelUp = roleLevel - DingEVN.DingRoleLevelUp;
        Integer levelDown = roleLevel + DingEVN.DingRoleLevelDown;
        // 根据角色等级level 来查询 此userid 可以看到的通讯录范围
        List<String> roleids = dingMailRoleMapper.selectRoleByLevel(levelUp, levelDown);
        // 用来存储上级部门id和下级部门id
        List<Long> allDepartIds = new ArrayList<>();
        allDepartIds.add(departmentid);
        // 根据上级部门id,搜索内容
//        QueryWrapper<Department> queryWrapper = new QueryWrapper<>();
//        queryWrapper.like("name", params);
//        queryWrapper.eq("parentid", departmentid);
        List<Department> departList = dingMailDepartmentMapper.selectDepartByNameAndParentId(params,departmentid);
        if (CollectionUtils.isNotEmpty(departList)) {
            // 部门ids
            List<Long> ids = new ArrayList<>();
            // 创建部门参数
            GetPhoneUserInfoByParamsDto dto;
            for (Department department : departList) {
                dto = new GetPhoneUserInfoByParamsDto();
                ids = new ArrayList<>();
                ids.add(department.getDepartmentid());
                allDepartIds.add(department.getDepartmentid());
                // 根据部门id,查找该部门下的子部门
                getChildDepartmentsByParentId(department.getDepartmentid(), ids);
                // 根据部门id和角色id,查找该部门下面的人员数量
                List<User> firstUsers = new ArrayList<>();
                ids.forEach(depart-> {
                    //找到roleids匹配数据
                    List<User> users = dingMailUserMapper.getUserByDepart(depart);
                    users.forEach(user -> {
                        String userRoles = user.getRoles();
                        if (StringUtils.isNotEmpty(userRoles)){
                            String[] userRole = userRoles.split(",");
                            Arrays.stream(userRole).filter(roleids::contains).findFirst().ifPresent(checkRole -> firstUsers.add(user));
                        }
                    });
                });
                dto.setId(department.getDepartmentid());
                dto.setAmember(department.getName() + "（" + firstUsers.size() + "人）");
                dtos.add(dto);
            }
        }
        // 查询人员
//        QueryWrapper<User> queryWrapper2 = new QueryWrapper<>();
//        queryWrapper2.between("rolelevel",levelUp,levelDown);
//        queryWrapper2.in("departments", allDepartIds);
//        queryWrapper2.like("name", params);
        List<User> userList = dingMailUserMapper.selectUserByDepIdsAndRoleLevelAndName(levelUp,levelDown,allDepartIds,params);
        // 再根据人员所在部门,拼接部门+职位
        if (CollectionUtils.isNotEmpty(userList)) {
            // 创建出参对象
            GetPhoneUserInfoByParamsDto dto;
            for (User user1 : userList) {
                dto = new GetPhoneUserInfoByParamsDto();
                dto.setId(user1.getId());
                dto.setAmember(user1.getName());
                dto.setNumber(user1.getMobile());
                String departName = "";
                if (StringUtils.isNotBlank(user1.getPosition())) {
                    departName = "/" + user1.getPosition();
                }
                // 查找人员所在部门,查找部门信息,判断是一级还是二级,再做拼接
//                QueryWrapper<Department> queryWrapper3 = new QueryWrapper<>();
//                queryWrapper3.eq("departmentid", user1.getDepartments());
                Department department = dingMailDepartmentMapper.selectDepartByDepartId(user1.getDepartments());
                // 如果是二级部门,查找上级部门,在拼接
                if (1 == department.getParentid()) {
                    if (ObjectUtil.isNotNull(department)) {
                        departName = department.getName() + departName;
                        // 根据部门id,查找所在所有部门和上级部门,并拼接部门
                        departName = getAllDepartByDepartId(department.getParentid(), departName);
                        dto.setDepartAndJob(departName);
                    }
                } else {
                    // 如果是一级部门,直接拼接
                    dto.setDepartAndJob(department.getName() + departName);
                }
                dtos.add(dto);
            }
        }
        Collections.sort(dtos, new Comparator<GetPhoneUserInfoByParamsDto>() {
            @Override
            public int compare(GetPhoneUserInfoByParamsDto h1, GetPhoneUserInfoByParamsDto h2) {
                return Math.toIntExact(h1.getId() - h2.getId());
            }
        });
        return dtos;
    }
    @Override
    public User selectUserByUserId(String userId) {
        return dingMailUserMapper.selectUserByUserId(userId);
    }

    @Override
    public List<DingdingRoleDTO> selectAllRolesByIds(List<String> roleIdList) {
        return dingMailUserMapper.selectAllRolesByIds(roleIdList);
    }


}


















