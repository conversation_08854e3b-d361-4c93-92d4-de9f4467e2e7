package com.onecity.os.data.indicator.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.constant.Constant;
import com.onecity.os.common.core.constant.Constants;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.data.indicator.mapper.GeneralIndicatorMapper;
import com.onecity.os.data.indicator.mapper.SourceManageMapper;
import com.onecity.os.data.indicator.model.po.DataConfig;
import com.onecity.os.data.indicator.model.po.GeneralIndicator;
import com.onecity.os.data.indicator.model.po.GeneralIndicatorDataTitle;
import com.onecity.os.data.indicator.model.vo.GeneralIndicatorDataVO;
import com.onecity.os.data.indicator.model.vo.GeneralIndicatorSearchDataVo;
import com.onecity.os.data.indicator.model.vo.GeneralIndicatorYearDataReqVO;
import com.onecity.os.data.indicator.model.vo.ItemValues;
import com.onecity.os.data.indicator.ruoyiLogFeign.ReportFeignService;
import com.onecity.os.data.indicator.ruoyiLogFeign.ResponseBean;
import com.onecity.os.data.indicator.service.DataConfigService;
import com.onecity.os.data.indicator.service.GeneralIndicatorDataService;
import com.onecity.os.data.indicator.mapper.GeneralIndicatorDataMapper;
import com.onecity.os.data.indicator.model.po.GeneralIndicatorData;
import com.onecity.os.data.indicator.service.IndicatorTitleService;
import com.onecity.os.data.indicator.util.BeanHelper;
import com.onecity.os.data.systemstyle.domain.SystemStyle;
import com.onecity.os.data.systemstyle.mapper.SystemStyleMapper;
import com.onecity.os.system.api.RemoteUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.*;


/**
 *
 * <AUTHOR>
 * @since 2020-03-02 15:28:17
 */
@Slf4j
@Service
public class GeneralIndicatorDataServiceImpl implements GeneralIndicatorDataService {
	@Autowired(required = false)
	private GeneralIndicatorDataMapper indicatorDataMapper;

	@Autowired(required = false)
	private GeneralIndicatorMapper indicatorMapper;

	@Autowired(required = false)
	private SourceManageMapper sourceManageMapper;

	@Autowired
	private RemoteUserService remoteUserService;

	@Autowired
	private ReportFeignService reportFeignService;

	@Autowired
	private DataConfigService dataConfigService;

	@Resource
	private IndicatorTitleService indicatorTitleService;

	@Resource
	private SystemStyleMapper systemStyleMapper;

	/**
	 * 根据指标id查询指标数据
	 * @param indicatorId
	 * @return
	 */
	@Override
	public List<GeneralIndicatorData> listByIndicatorId(String sourceSimpleName,String indicatorId) {
		if(StringUtils.isBlank(indicatorId)){
			return null;
		}
		GeneralIndicatorData params = new GeneralIndicatorData();
		params.setDelete(Constant.DELETE_NO);
		params.setIndicatorId(indicatorId);
		params.setCurrentFlag(Constant.CURRENT_DATA_YES);
//		String tableName = CommonUtil.getTableName(sourceSimpleName, Constant.GENERAL_INDICATOR_DATA_TALBE_SUFFIX);
		List<GeneralIndicatorData> list = indicatorDataMapper.list(params);
		return list;
	}

	//根据最大数据期年份以及指标id获取展示的不删除的非年度更新指标数据
	@Override
	public List<GeneralIndicatorData> listIndicatorDatas(GeneralIndicatorData params) {
		return indicatorDataMapper.listIndicatorDatas(params);
	}

	//根据最大数据期年份以及指标id获取展示的不删除的年度更新指标数据
	@Override
	public List<GeneralIndicatorData> listIndicatorYearDatas(GeneralIndicatorData params) {
		return indicatorDataMapper.listIndicatorYearDatas(params);
	}

	@Override
	public List<GeneralIndicatorData> list(String sourceSimpleName, GeneralIndicatorData params) {
		params.setDelete(Constant.DELETE_NO);
//		String tableName = CommonUtil.getTableName(sourceSimpleName, Constant.GENERAL_INDICATOR_DATA_TALBE_SUFFIX);
		List<GeneralIndicatorData> list = indicatorDataMapper.list(params);
		return list;
	}

	/**
	 * 根据指标id查询历史年份集合
	 * @param indicatorId
	 * @return
	 */
	@Override
	public List<String> listShowDate(String sourceSimpleName,String indicatorId) {
		GeneralIndicatorData params = new GeneralIndicatorData();
		params.setDelete(Constant.DELETE_NO);
		params.setIndicatorId(indicatorId);
//		String tableName = CommonUtil.getTableName(sourceSimpleName, Constant.GENERAL_INDICATOR_DATA_TALBE_SUFFIX);
		List<String> result = indicatorDataMapper.listShowDate(params);
		return result;
	}

	/**
	 * 根据前端传的年份查询指标数据
	 * @param req
	 * @return
	 */
	@Override
	public List<GeneralIndicatorData> getDataList(GeneralIndicatorYearDataReqVO req) {
		//确定查询范围
		String startYear = null;
		String endYear = null;
		List<GeneralIndicatorData> generalIndicatorDataList = new ArrayList<>();
		if (req.getYearDate().length()>4){
			startYear = req.getYearDate().split("-")[0];
			endYear = req.getYearDate().split("-")[1];
		}else if(req.getYearDate().length()==4){
			startYear =req.getYearDate();
		}
		generalIndicatorDataList = indicatorDataMapper.getYearDataList(req.getId(), startYear, endYear);
		return generalIndicatorDataList;
	}

	//获取指标数据中最大的数据期年份
	@Override
	public String getMaxUpdateDateYear(String indicatorId) {
		return indicatorDataMapper.getMaxUpdateDateYear(indicatorId);
	}

	@Override
	public List<GeneralIndicatorSearchDataVo> searchIndicator(String indicatorName, Long userId){
		//获取角色ids
		BaseResult<List<Long>> roleIdsResult = remoteUserService.getRoleIdsByUserId(userId);
		if(Constants.FAIL==roleIdsResult.getCode()){
			return new ArrayList<GeneralIndicatorSearchDataVo>();
		}else if(ObjectUtils.isEmpty(roleIdsResult.getData())){
			return new ArrayList<GeneralIndicatorSearchDataVo>();
		}
		List<Long> roleIDList = roleIdsResult.getData();
		//获取角色拥有权限的指标板块编码
		List<String> sourceSimpleNameList = sourceManageMapper.getSourceSimpleNameByRoleIds(roleIDList);
		//根据指标名称与板块编码模糊查询指标
		List<GeneralIndicatorSearchDataVo> searchDataList = indicatorMapper.searchIndicatorBySourceSimpleNameAndName(sourceSimpleNameList,indicatorName);
		SystemStyle systemStyle = systemStyleMapper.selectSystemStyleById();
		//根据查询获取的指标查询相应的指标数据
		for(GeneralIndicatorSearchDataVo indicatorSearchData:searchDataList){
			indicatorSearchData.setAppSourceNameFlag(systemStyle.getAppSourceNameFlag());
			indicatorSearchData.setAppUpdateCycleFlag(systemStyle.getAppUpdateCycleFlag());
			//区分指标数据对接类型1为手动填报，2为数据对接
			if (indicatorSearchData.getDataUpdateMode()==1) {
				//查询指标头信息
				List<GeneralIndicatorDataTitle> titles = indicatorTitleService.queryById(indicatorSearchData.getId());
				indicatorSearchData.setTableHeaderList(titles);
				String maxUpdateYear = indicatorDataMapper.getMaxUpdateDateYear(indicatorSearchData.getId());
				//组装查询条件
				GeneralIndicatorData generalIndicatorData = new GeneralIndicatorData();
				if (ObjectUtil.isNotNull(indicatorSearchData.getIsScreen()) && 1 == indicatorSearchData.getIsScreen()) {
					generalIndicatorData.setUpdateDate(maxUpdateYear);
				}
				generalIndicatorData.setCurrentFlag(1);
				generalIndicatorData.setDelete(0);
				generalIndicatorData.setIndicatorId(indicatorSearchData.getId());
				List<GeneralIndicatorData> generalIndicatorDatas = new ArrayList<>();
				//获取指标更新周期，区分年度更新与非年度更新
				if ("年度更新".equals(indicatorSearchData.getUpdateCycle())) {
					generalIndicatorDatas = listIndicatorYearDatas(generalIndicatorData);
					if (ObjectUtil.isNotNull(maxUpdateYear)) {
						String minUpdateDateYear = String.valueOf(Integer.valueOf(maxUpdateYear) - 4);
						indicatorSearchData.setYearDate(minUpdateDateYear + "-" + maxUpdateYear);
					}
				} else {
					generalIndicatorDatas = listIndicatorDatas(generalIndicatorData);
					if (ObjectUtil.isNotNull(maxUpdateYear)) {
						indicatorSearchData.setYearDate(maxUpdateYear);
					}
				}
				if (generalIndicatorDatas != null && generalIndicatorDatas.size() > 0) {
					List<GeneralIndicatorDataVO> generalIndicatorDataVOS = BeanHelper.copyWithCollection(generalIndicatorDatas, GeneralIndicatorDataVO.class);
					indicatorSearchData.setIndicatorDataList(generalIndicatorDataVOS);
				}

			}else if (indicatorSearchData.getDataUpdateMode()==2){
//				if(!ObjectUtil.isEmpty(indicatorSearchData.getDataConfigId())){
				//数据配置id不是空的时，数据集等均不是空的，前端在数据配置弹窗中做了数据配置相关全部的必填项
				List<DataConfig> dataConfigList = dataConfigService.getDataConfigByIndicatorId(indicatorSearchData.getId());
				if (!ObjectUtil.isEmpty(dataConfigList)&&ObjectUtils.isNotEmpty(dataConfigList.get(0).getDataSetId())){
					List<GeneralIndicatorDataVO> dataUpdateMode2 = new ArrayList<>();
					try {
						//同一个指标配置的数据集是相同的，故只需要取第一个数据配置的数据集id即可
						Long dataSetId = dataConfigList.get(0).getDataSetId();
						//请求数据集接口获取数据
						ResponseBean responseBean = reportFeignService.detailById(dataSetId);
						log.info("获取到的数据集数据为data："+responseBean.getData().toString());
						//获取数据非空时将其处理为前端需要的格式
						LinkedHashMap dataDto = (LinkedHashMap)responseBean.getData();
						List<Map> dataArray = (List<Map>)dataDto.get("data");
						if (dataArray.size() > 0) {
							for (Map object : dataArray) {
								GeneralIndicatorDataVO generalIndicatorDataVO = new GeneralIndicatorDataVO();
								List<ItemValues> itemValuesList = new LinkedList<>();
								for(DataConfig dc : dataConfigList) {
									ItemValues itemValues = new ItemValues();
									//设置获取数据对接指标数据的数据key
									itemValues.setDataValue(dc.getDataValue());
									//设置值是主值还是副值
									itemValues.setIsMaster(dc.getIsMasterValue());
									//设置值的名称
									itemValues.setItemValueName(dc.getDataValueName());
									//设置值的具体数据
									if(null != object.get(dc.getDataKey())) {
										itemValues.setItemValueValue(object.get(dc.getDataValue()).toString());
									}
									//设置x轴的值
									if(null != object.get(dc.getDataValue())) {
										generalIndicatorDataVO.setItemName(object.get(dc.getDataKey()).toString());
									}
									//设置x轴的名称即x轴具体代表什么意思
									generalIndicatorDataVO.setDataKeyName(dc.getDataKeyName());
									//设置主值单位
									generalIndicatorDataVO.setItemUnit(dc.getDataUnit());
									//设置副值单位
									generalIndicatorDataVO.setSecondaryUnit(dc.getSecondaryUnit());
									itemValuesList.add(itemValues);
								}
								generalIndicatorDataVO.setItemValuesList(itemValuesList);
								dataUpdateMode2.add(generalIndicatorDataVO);
							}
						}
						indicatorSearchData.setIndicatorDataList(dataUpdateMode2);
					}catch (Exception e){
						log.error("请求数据集接口获取数据异常！"+e.getMessage());
					}
				}
//				}
			}
			//组装查询到的指标的父级指标或者分组
			setParentIndicatorOrGroup(indicatorSearchData);
		}
		List<GeneralIndicatorSearchDataVo> serachDataList1 = new ArrayList<>();
		for(GeneralIndicatorSearchDataVo generalIndicatorSearchDataVo:searchDataList){
			GeneralIndicatorSearchDataVo generalIndicatorSearchDataVo1 = BeanHelper.copyProperties(generalIndicatorSearchDataVo,GeneralIndicatorSearchDataVo.class);
			serachDataList1.add(generalIndicatorSearchDataVo1);
		}
		//去除脏数据，父指标已被删除的指标
		for(GeneralIndicatorSearchDataVo generalIndicatorSearchDataVo:serachDataList1){
			deleteDirtyData(generalIndicatorSearchDataVo,searchDataList);
		}
		//增加搜索结果的一二级分组
		for(GeneralIndicatorSearchDataVo generalIndicatorSearchDataVo : searchDataList){
			setFirstAndSecondGroup(generalIndicatorSearchDataVo);
		}
		return searchDataList;
	}

	public void setFirstAndSecondGroup(GeneralIndicatorSearchDataVo indicatorSearchDataVo){
		List<GeneralIndicator> indicators = new ArrayList<>();
		if(!"0".equals(indicatorSearchDataVo.getParentId())){
			GeneralIndicator generalIndicator = indicatorMapper.getInfoByIndicatorId(indicatorSearchDataVo.getParentId());
			indicators.add(generalIndicator);
			indicators = getParentIndicator(indicatorSearchDataVo.getParentId(),indicators);
		}
		for(GeneralIndicator generalIndicator1:indicators) {
			log.info("搜索查看:"+JSONObject.toJSONString(generalIndicator1));
			if (generalIndicator1.getIndicatorType() == 1 && "0".equals(generalIndicator1.getParentId())) {
				indicatorSearchDataVo.setFirstGroup(generalIndicator1.getId());
			} else if (generalIndicator1.getIndicatorType() == 1 && !"0".equals(generalIndicator1.getParentId())) {
				indicatorSearchDataVo.setSecondGroup(generalIndicator1.getId());
			}
		}
	}
	public List<GeneralIndicator> getParentIndicator(String id,List<GeneralIndicator> generalIndicatorList){
		GeneralIndicator generalIndicator = indicatorMapper.getInfoByIndicatorId(id);
		generalIndicatorList.add(generalIndicator);
		if(!"0".equals(generalIndicator.getParentId())){
			generalIndicatorList = getParentIndicator(generalIndicator.getParentId(),generalIndicatorList);
		}
		return generalIndicatorList;
	}

	public void setParentIndicatorOrGroup(GeneralIndicatorSearchDataVo indicatorSearchDataVo){
		if(!"0".equals(indicatorSearchDataVo.getParentId())){
			GeneralIndicator generalIndicator = indicatorMapper.getInfoByIndicatorId(indicatorSearchDataVo.getParentId());
			if(ObjectUtils.isNotEmpty(generalIndicator)) {
				GeneralIndicatorSearchDataVo generalIndicatorSearchDataVo = BeanHelper.copyProperties(generalIndicator, GeneralIndicatorSearchDataVo.class);
				indicatorSearchDataVo.setParentIndicator(generalIndicatorSearchDataVo);
				setParentIndicatorOrGroup(generalIndicatorSearchDataVo);
			}
		}
	}

	public void deleteDirtyData(GeneralIndicatorSearchDataVo generalIndicatorSearchDataVo,List<GeneralIndicatorSearchDataVo> searchDataVoList){
		Boolean deleteFlag = false;
		//删除不是一级指标并且父级指标为null的指标
		if(ObjectUtils.isEmpty(generalIndicatorSearchDataVo.getParentIndicator())&&!"0".equals(generalIndicatorSearchDataVo.getParentId())){
			deleteFlag=true;
		}
		//删除父级指标不一致的指标
		if(ObjectUtils.isNotEmpty(generalIndicatorSearchDataVo.getParentIndicator())
				&&!generalIndicatorSearchDataVo.getParentId().equals(generalIndicatorSearchDataVo.getParentIndicator().getId())){
			deleteFlag=true;
		}
		if(generalIndicatorSearchDataVo.getDelete()==1||generalIndicatorSearchDataVo.getIsShow()==0){
			deleteFlag=true;
		}
		//删除父级指标不为空，但是父级指标不展示或者父级的父级不展示或者删除的指标
		if(ObjectUtils.isNotEmpty(generalIndicatorSearchDataVo.getParentIndicator())){
			if(0==generalIndicatorSearchDataVo.getParentIndicator().getIsShow()){
				deleteFlag=true;
			}else {
				deleteFlag = setDeleteFlag(generalIndicatorSearchDataVo.getParentIndicator(), deleteFlag);
			}
		}
		log.info("APP搜索结果指标名称："+ JSONObject.toJSONString(generalIndicatorSearchDataVo.getIndicatorName()));
		log.info("脏数据标识："+JSONObject.toJSONString(deleteFlag));
		if(deleteFlag){
			searchDataVoList.remove(generalIndicatorSearchDataVo);
		}
	}

	public Boolean setDeleteFlag(GeneralIndicatorSearchDataVo generalIndicatorSearchDataVo,Boolean deleteFlag){
		if(generalIndicatorSearchDataVo.getIsShow()==0||generalIndicatorSearchDataVo.getDelete()==1){
			deleteFlag = true;
		}
		if(!"0".equals(generalIndicatorSearchDataVo.getParentId())) {
			if (ObjectUtils.isNotEmpty(generalIndicatorSearchDataVo.getParentIndicator())) {
				deleteFlag = setDeleteFlag(generalIndicatorSearchDataVo.getParentIndicator(), deleteFlag);
			} else {
				deleteFlag = true;
			}
		}
		return deleteFlag;
	}
}