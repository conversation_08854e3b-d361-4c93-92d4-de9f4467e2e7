package com.onecity.os.data.indicator.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.onecity.os.common.core.constant.Constant;
import com.onecity.os.data.indicator.mapper.GeneralIndicatorMapper;
import com.onecity.os.data.indicator.mapper.SourceManageMapper;
import com.onecity.os.data.indicator.model.po.*;
import com.onecity.os.data.indicator.model.vo.*;
import com.onecity.os.data.indicator.ruoyiLogFeign.ReportFeignService;
import com.onecity.os.data.indicator.ruoyiLogFeign.ResponseBean;
import com.onecity.os.data.indicator.service.DataConfigService;
import com.onecity.os.data.indicator.service.GeneralIndicatorDataService;
import com.onecity.os.data.indicator.service.GeneralIndicatorService;
import com.onecity.os.data.indicator.service.IndicatorTitleService;
import com.onecity.os.data.indicator.util.BeanHelper;
import com.onecity.os.data.indicator.util.DateUtil;
import com.onecity.os.data.systemstyle.domain.SystemStyle;
import com.onecity.os.data.systemstyle.mapper.SystemStyleMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;


/**
 * <AUTHOR>
 * @since 2020-03-02 15:26:33
 */
@Service
@Slf4j
public class GeneralIndicatorServiceImpl implements GeneralIndicatorService {


    @Autowired(required = false)
    private GeneralIndicatorMapper indicatorMapper;
    @Autowired
    private GeneralIndicatorDataService generalIndicatorDataService;

    @Resource
    private DataConfigService dataConfigService;

    @Resource
    private ReportFeignService reportFeignService;

    @Resource
    private IndicatorTitleService indicatorTitleService;

    @Resource
    private SystemStyleMapper systemStyleMapper;

    @Resource
    private SourceManageMapper sourceManageMapper;

    /**
     * 查询厅局ICON列表
     *
     * @param req
     * @return
     */
    @Override
    public List<GeneralIndicatorIconResVO> listIcon(GeneralIndicatorReqVO req) {
//        String sourceSimpleName = req.getSourceSimpleName();
//        String tableName = sourceSimpleName + Constant.GENERAL_INDICATOR_TALBE_SUFFIX;
        GeneralIndicator param = new GeneralIndicator();
        param.setParentId(Constant.PARENT_ID_ONE_LEVEL);
        param.setIsShow(Constant.SHOW_YES);
        param.setDelete(Constant.DELETE_NO);
        param.setSourceId(req.getSourceId());
        List<GeneralIndicator> generalIndicators = indicatorMapper.listIcon(param);
        List<GeneralIndicatorIconResVO> generalIndicatorResVOS = BeanHelper.copyWithCollection(generalIndicators, GeneralIndicatorIconResVO.class);
        return generalIndicatorResVOS;
    }

    /**
     * 查询ICON下的tab列表
     *
     * @param req
     * @return
     */
    @Override
    public List<GeneralIndicatorIconResVO> listTab(GeneralIndicatorReqVO req) {
        String id = req.getId();
//        String sourceSimpleName = req.getSourceSimpleName();
//        String tableName = sourceSimpleName + Constant.GENERAL_INDICATOR_TALBE_SUFFIX;
        GeneralIndicator param = new GeneralIndicator();
        param.setParentId(id);
        param.setDelete(Constant.DELETE_NO);
        param.setIsShow(Constant.SHOW_YES);
        param.setIndicatorType(Constant.TAB_INDICATOR);
        param.setSourceId(req.getSourceId());
        List<GeneralIndicator> generalIndicators = indicatorMapper.listTab(param);
        List<GeneralIndicatorIconResVO> generalIndicatorResVOS = BeanHelper.copyWithCollection(generalIndicators, GeneralIndicatorIconResVO.class);
        return generalIndicatorResVOS;
    }

    @Override
    public GeneralIndicatorResVo listIndicator(GeneralIndicatorReqVO req) {
        GeneralIndicatorResVo result = new GeneralIndicatorResVo();
        SystemStyle systemStyle = systemStyleMapper.selectSystemStyleById();
        result.setAppSourceNameFlag(systemStyle.getAppSourceNameFlag());
        result.setAppUpdateCycleFlag(systemStyle.getAppUpdateCycleFlag());
        String id = req.getId();
        //查询核心区数据
        List<GeneralIndicatorCoreVo> coreList = this.getCoreList(req);
        //查询解决区数据
        req.setId(id);
        List<GeneralIndicatorDetailVO> detailList = this.getDetailList(req);
        result.setCoreList(coreList);
        result.setDetailList(detailList);
        return result;
    }

    @Override
    public GeneralIndicatorVos getFirstIndicatorList(String sourceSimpleName,String first,String second,Integer pageNo, Integer pageSize) {
        GeneralIndicatorVos generalIndicatorVos = new GeneralIndicatorVos();
        SourceManage sourceManage = sourceManageMapper.getSourceManageBySourceSimpleName(sourceSimpleName);
        if(ObjectUtils.isEmpty(sourceManage)){
            return generalIndicatorVos;
        }
        generalIndicatorVos.setFirstGroupFlag(sourceManage.getFirstGroupFlag());
        generalIndicatorVos.setSecondGroupFlag(sourceManage.getSecondGroupFlag());
        generalIndicatorVos.setAppReportInfoFlag(sourceManage.getAppReportInfoFlag());
        SystemStyle systemStyle = systemStyleMapper.selectSystemStyleById();
        generalIndicatorVos.setAppSourceNameFlag(systemStyle.getAppSourceNameFlag());
        generalIndicatorVos.setAppUpdateCycleFlag(systemStyle.getAppUpdateCycleFlag());
        List<FirstGeneralIndicatorVO> result = new ArrayList<>();
        //先查询所有的指标分组
        GeneralIndicator param = new GeneralIndicator();
        param.setIsShow(Constant.SHOW_YES);
        param.setDelete(Constant.DELETE_NO);
        param.setIndicatorType(Constant.TAB_INDICATOR);
        param.setSourceId(sourceSimpleName);
        List<GeneralIndicator> generalIndicators = indicatorMapper.listIcon(param);
        //筛选出一级分组
        for (GeneralIndicator generalIndicator : generalIndicators) {
            FirstGeneralIndicatorVO firstGeneralIndicatorVO = new FirstGeneralIndicatorVO();
            if (generalIndicator.getParentId().equals(Constant.PARENT_ID_ONE_LEVEL)) {
                firstGeneralIndicatorVO = BeanHelper.copyProperties(generalIndicator, FirstGeneralIndicatorVO.class);
                result.add(firstGeneralIndicatorVO);
            }
        }
        //筛选一级分组下二级分组
        for (FirstGeneralIndicatorVO firstGeneralIndicator : result) {
            List<TwoGeneralIndicatorVO> twoGeneralIndicatorList = new ArrayList<>();
            for (GeneralIndicator generalIndicator : generalIndicators) {
                TwoGeneralIndicatorVO twoGeneralIndicator = new TwoGeneralIndicatorVO();
                if (generalIndicator.getParentId().equals(firstGeneralIndicator.getId())) {
                    twoGeneralIndicator = BeanHelper.copyProperties(generalIndicator, TwoGeneralIndicatorVO.class);
                    twoGeneralIndicatorList.add(twoGeneralIndicator);
                }
            }
            firstGeneralIndicator.setTwoGeneralIndicatorList(twoGeneralIndicatorList);
        }
        if(ObjectUtils.isEmpty(second)) {
            //查询排序在第一个一级分组下的第一个二级分组下的（指标及分组分页返回）以及对应数据
            if (CollectionUtils.isNotEmpty(result) && CollectionUtils.isNotEmpty(result.get(0).getTwoGeneralIndicatorList())) {
                TwoGeneralIndicatorVO indicatorVO = result.get(0).getTwoGeneralIndicatorList().get(0);
                //调用获取三级指标及数据的service
//                PageHelper.startPage(pageNo, pageSize);
                GeneralIndicatorReqVO req = new GeneralIndicatorReqVO();
                req.setSourceId(sourceSimpleName);
                req.setId(indicatorVO.getId());
                req.setPageNum(pageNo);
                req.setPageSize(pageSize);
                PageInfo<GeneralIndicatorCoreVo> coreList = this.listIndicatorByTabId(req);
                generalIndicatorVos.setIndicatorCoreVoPageInfo(coreList);
//                if (CollectionUtils.isNotEmpty(coreList)) {
//                    PageInfo dataDtoPage = new PageInfo(coreList);
//                    dataDtoPage.setList(coreList);
//                    generalIndicatorVos.setIndicatorCoreVoPageInfo(dataDtoPage);
//                }
            }
        }else {
            //调用获取三级指标及数据的service
//            PageHelper.startPage(pageNo, pageSize);
            GeneralIndicatorReqVO req = new GeneralIndicatorReqVO();
            req.setSourceId(sourceSimpleName);
            req.setId(second);
            req.setPageNum(pageNo);
            req.setPageSize(pageSize);
            PageInfo<GeneralIndicatorCoreVo> coreList = this.listIndicatorByTabId(req);
            generalIndicatorVos.setIndicatorCoreVoPageInfo(coreList);
//            if (CollectionUtils.isNotEmpty(coreList)) {
//                PageInfo dataDtoPage = new PageInfo(coreList);
//                dataDtoPage.setList(coreList);
//                generalIndicatorVos.setIndicatorCoreVoPageInfo(dataDtoPage);
//            }
        }
        generalIndicatorVos.setFirstGeneralIndicatorVOList(result);
        return generalIndicatorVos;
    }

    /**
     * 根据二级tabid分页查询指标及数据
     *
     * @param req
     * @return
     */
    @Override
    public PageInfo<GeneralIndicatorCoreVo> listIndicatorByTabId(GeneralIndicatorReqVO req) {
        //查询核心区数据
        return Optional.ofNullable(getCoreListPage(req)).orElse(new PageInfo<>());
    }

    @Override
    public GeneralIndicatorCoreVo listIndicatorDataById(String id) {
        GeneralIndicator generalIndicator = indicatorMapper.getIndicatorInfoByIndicatorId(id);
        if(ObjectUtils.isEmpty(generalIndicator)){
            return new GeneralIndicatorCoreVo();
        }
        GeneralIndicatorCoreVo generalIndicatorCoreVo = BeanHelper.copyProperties(generalIndicator,GeneralIndicatorCoreVo.class);
        if(generalIndicator.getDataUpdateMode()==1) {
            //获取最大的数据期
            String maxUpdateDateYear = generalIndicatorDataService.getMaxUpdateDateYear(id);
//        String sourceSimpleName = req.getSourceSimpleName();
            //组装查询条件
            GeneralIndicatorData generalIndicatorData = new GeneralIndicatorData();
            if (ObjectUtil.isNotNull(generalIndicator.getIsScreen())&&1 == generalIndicator.getIsScreen()) {
                generalIndicatorData.setUpdateDate(maxUpdateDateYear);
            }
            generalIndicatorData.setCurrentFlag(1);
            generalIndicatorData.setDelete(0);
            generalIndicatorData.setIndicatorId(id);
            List<GeneralIndicatorData> generalIndicatorDatas = new ArrayList<>();
            //获取指标更新周期，区分年度更新与非年度更新
            if ("年度更新".equals(generalIndicator.getUpdateCycle())) {
                generalIndicatorDatas = generalIndicatorDataService.listIndicatorYearDatas(generalIndicatorData);
                if (ObjectUtil.isNotNull(maxUpdateDateYear)) {
                    String minUpdateDateYear = String.valueOf(Integer.valueOf(maxUpdateDateYear) - 4);
                    generalIndicatorCoreVo.setYearDate(minUpdateDateYear + "-" + maxUpdateDateYear);
                }
            } else {
                generalIndicatorDatas = generalIndicatorDataService.listIndicatorDatas(generalIndicatorData);
                if (ObjectUtil.isNotNull(maxUpdateDateYear)) {
                    generalIndicatorCoreVo.setYearDate(maxUpdateDateYear);
                }
            }
            if (generalIndicatorDatas != null && generalIndicatorDatas.size() > 0) {
                List<GeneralIndicatorDataVO> generalIndicatorDataVOS = BeanHelper.copyWithCollection(generalIndicatorDatas, GeneralIndicatorDataVO.class);
                String indicatorName = generalIndicatorDatas.get(0).getIndicatorName();
                if (StringUtils.isNotBlank(indicatorName)) {
                    generalIndicatorCoreVo.setIndicatorName(indicatorName);
                }
                generalIndicatorCoreVo.setList(generalIndicatorDataVOS);
            }
        }else if(generalIndicator.getDataUpdateMode()==2){
            //如果是数据对接，则请求数据集接口获取数据
            //数据配置id不是空的时，数据集等均不是空的，前端在数据配置弹窗中做了数据配置相关全部的必填项
            List<DataConfig> dataConfigList = dataConfigService.getDataConfigByIndicatorId(generalIndicator.getId());
            log.info("获取数据集数据之前");
            if (ObjectUtils.isNotEmpty(dataConfigList)&&dataConfigList.size()>0){
                log.info("获取数据集数据之前进入判断条件");
                List<GeneralIndicatorDataVO> dataUpdateMode2 = new ArrayList<>();
                try {
                    //同一个指标配置的数据集是相同的，故只需要取第一个数据配置的数据集id即可
                    Long dataSetId = dataConfigList.get(0).getDataSetId();
                    //请求数据集接口获取数据
                    ResponseBean responseBean = reportFeignService.detailById(dataSetId);
                    log.info("获取到的数据集数据为data："+responseBean.getData().toString());
                    //获取数据非空时将其处理为前端需要的格式
                    if (!ObjectUtil.isEmpty(responseBean.getData())) {
                        LinkedHashMap dataDto = (LinkedHashMap)responseBean.getData();
                        List<Map> dataArray = (List<Map>)dataDto.get("data");
                        if (dataArray.size() > 0) {
                            for (Map object : dataArray) {
                                GeneralIndicatorDataVO generalIndicatorDataVO = new GeneralIndicatorDataVO();
                                List<ItemValues> itemValuesList = new LinkedList<>();
                                for(DataConfig dc : dataConfigList) {
                                    ItemValues itemValues = new ItemValues();
                                    //设置获取数据对接指标数据的数据key
                                    itemValues.setDataValue(dc.getDataValue());
                                    //设置值是主值还是副值
                                    itemValues.setIsMaster(dc.getIsMasterValue());
                                    //设置值的名称
                                    itemValues.setItemValueName(dc.getDataValueName());
                                    //设置值的具体数据
                                    if(null != object.get(dc.getDataKey())) {
                                        itemValues.setItemValueValue(object.get(dc.getDataValue()).toString());
                                    }
                                    //设置x轴的值
                                    if(null != object.get(dc.getDataValue())) {
                                        generalIndicatorDataVO.setItemName(object.get(dc.getDataKey()).toString());
                                    }
                                    //设置x轴的名称即x轴具体代表什么意思
                                    generalIndicatorDataVO.setDataKeyName(dc.getDataKeyName());
                                    //设置主值单位
                                    generalIndicatorDataVO.setItemUnit(dc.getDataUnit());
                                    //设置副值单位
                                    generalIndicatorDataVO.setSecondaryUnit(dc.getSecondaryUnit());
                                    itemValuesList.add(itemValues);
                                }
                                generalIndicatorDataVO.setItemValuesList(itemValuesList);
                                dataUpdateMode2.add(generalIndicatorDataVO);
                            }
                        }
                    }
                    generalIndicatorCoreVo.setList(dataUpdateMode2);
                } catch (Exception e) {
                    log.error("请求数据集接口获取数据异常！" + e.getMessage());
                }
            }
        }
        //在指标信息上添加指标头信息
        List<GeneralIndicatorDataTitle> titles = indicatorTitleService.queryById(generalIndicator.getId());
        generalIndicatorCoreVo.setTableHeaderList(titles);
        return generalIndicatorCoreVo;
    }

    /**
     * 获取核心数据列表
     *
     * @param req
     * @return
     */
    private PageInfo<GeneralIndicatorCoreVo> getCoreListPage(GeneralIndicatorReqVO req) {
        String id = req.getId();
//        String sourceSimpleName = req.getSourceSimpleName();
//        String tableName = sourceSimpleName + Constant.GENERAL_INDICATOR_TALBE_SUFFIX;
        GeneralIndicator param = new GeneralIndicator();
        param.setParentId(id);
        param.setDelete(Constant.DELETE_NO);
        param.setIsShow(Constant.SHOW_YES);
//        param.setIndicatorType(Constant.CORE_DATA);
        //分页查询2级分组下的指标及分组
//        param.setIndicatorType(Constant.GENERAL_INDICATOR);
        PageHelper.startPage(req.getPageNum(),req.getPageSize());
        param.setSourceId(req.getSourceId());
        List<GeneralIndicator> generalIndicators = indicatorMapper.listCoreIndicatorByParentId(param);
        PageInfo<GeneralIndicator> generalIndicatorPageInfo = new PageInfo<>(generalIndicators);
        List<GeneralIndicatorCoreVo> generalIndicatorCoreVos = new ArrayList<>();
        if (generalIndicators != null && generalIndicators.size() > 0) {
            generalIndicatorCoreVos = new ArrayList<>(generalIndicators.size());
            for (GeneralIndicator generalIndicator : generalIndicators) {
                GeneralIndicatorCoreVo generalIndicatorCoreVo = this.setCoreIndicator(generalIndicator, req);
                //在指标信息上添加指标头信息
                List<GeneralIndicatorDataTitle> titles = indicatorTitleService.queryById(generalIndicator.getId());
                generalIndicatorCoreVo.setTableHeaderList(titles);
                generalIndicatorCoreVos.add(generalIndicatorCoreVo);
            }
        }

        PageInfo<GeneralIndicatorCoreVo> generalIndicatorCoreVoPageInfo = new PageInfo<>(generalIndicatorCoreVos);
        generalIndicatorCoreVoPageInfo.setTotal(generalIndicatorPageInfo.getTotal());
        generalIndicatorCoreVoPageInfo.setPageNum(generalIndicatorPageInfo.getPageNum());
        generalIndicatorCoreVoPageInfo.setPageSize(generalIndicatorPageInfo.getPageSize());
        generalIndicatorCoreVoPageInfo.setPages(generalIndicatorPageInfo.getPages());
        generalIndicatorCoreVoPageInfo.setList(generalIndicatorCoreVos);
        return generalIndicatorCoreVoPageInfo;
    }

    /**
     * 获取核心数据列表
     *
     * @param req
     * @return
     */
    private List<GeneralIndicatorCoreVo> getCoreList(GeneralIndicatorReqVO req) {
        String id = req.getId();
//        String sourceSimpleName = req.getSourceSimpleName();
//        String tableName = sourceSimpleName + Constant.GENERAL_INDICATOR_TALBE_SUFFIX;
        GeneralIndicator param = new GeneralIndicator();
        param.setParentId(id);
        param.setDelete(Constant.DELETE_NO);
        param.setIsShow(Constant.SHOW_YES);
//        param.setIndicatorType(Constant.CORE_DATA);
        param.setIndicatorType(Constant.GENERAL_INDICATOR);
        param.setSourceId(req.getSourceId());
        List<GeneralIndicator> generalIndicators = indicatorMapper.listCoreIndicatorByParentId(param);
        List<GeneralIndicatorCoreVo> generalIndicatorCoreVos = null;
        if (generalIndicators != null && generalIndicators.size() > 0) {
            generalIndicatorCoreVos = new ArrayList<>(generalIndicators.size());
            for (GeneralIndicator generalIndicator : generalIndicators) {
                GeneralIndicatorCoreVo generalIndicatorCoreVo = this.setCoreIndicator(generalIndicator, req);
                //在指标信息上添加指标头信息
                List<GeneralIndicatorDataTitle> titles = indicatorTitleService.queryById(generalIndicator.getId());
                generalIndicatorCoreVo.setTableHeaderList(titles);
                generalIndicatorCoreVos.add(generalIndicatorCoreVo);
            }
        }

        return generalIndicatorCoreVos;
    }

    /**
     * 设置核心指标
     *
     * @param generalIndicator
     * @param req
     */
    private GeneralIndicatorCoreVo setCoreIndicator(GeneralIndicator generalIndicator, GeneralIndicatorReqVO req) {
        //设置基本信息
        GeneralIndicatorCoreVo result = this.setBaseInfo(generalIndicator, req);
        //设置基本指标数据集合
        this.setBaseDataList(generalIndicator, req, result);
        //设置历史年份集合
        this.setShowDateList(generalIndicator, req, result);
        //设置下级数据
        req.setId(generalIndicator.getId());
        List<GeneralIndicatorCoreVo> coreList = this.getCoreList(req);
        result.setChildList(coreList);
        return result;
    }

    /**
     * 设置指标基本信息
     *
     * @param generalIndicator
     */
    private GeneralIndicatorCoreVo setBaseInfo(GeneralIndicator generalIndicator, GeneralIndicatorReqVO req) {
        GeneralIndicatorCoreVo result = BeanHelper.copyProperties(generalIndicator, GeneralIndicatorCoreVo.class);
        //因多维指标展示数据对接中指标id记录在数据对接表中，故不需要在指标中设置数据配置相关了
//        if(result.getDataUpdateMode()==2&&!ObjectUtil.isEmpty(result.getDataConfigId())){
//            DataConfig dataConfig = dataConfigService.getDataConfigById(result.getDataConfigId());
//            result.setDataKey(dataConfig.getDataKey());
//            result.setDataValue(dataConfig.getDataValue());
//            result.setDataSetId(dataConfig.getDataSetId());
//            result.setDataSetName(dataConfig.getDataSetName());
//            result.setDataUnit(dataConfig.getDataUnit());
//        }
        String showDate = req.getShowDate();
        if (StringUtils.isNotBlank(showDate)) {
            result.setShowDate(showDate);
        } else {
            LocalDateTime createTime = generalIndicator.getCreateTime();
            if(createTime!=null){
                String year = DateUtil.getString(createTime, "yyyy");
                result.setShowDate(year);
            }
        }
        return result;
    }

    /**
     * 设置展示指标数据
     *
     * @param generalIndicator
     * @param result
     */
    private void setBaseDataList(GeneralIndicator generalIndicator, GeneralIndicatorReqVO req, GeneralIndicatorCoreVo result) {
        if(generalIndicator.getDataUpdateMode()==1) {
            String id = generalIndicator.getId();
            //todo 获取最大的数据期，并将数据期进行返回给前端，非年度更新的以最新有数据的年份进行指标数据获取，年度更新的以最大年份往前数五年为默认区间进行指标数据获取
            //获取最大的数据期
            String maxUpdateDateYear = generalIndicatorDataService.getMaxUpdateDateYear(id);
//        String sourceSimpleName = req.getSourceSimpleName();
            //组装查询条件
            GeneralIndicatorData generalIndicatorData = new GeneralIndicatorData();
            if (ObjectUtil.isNotNull(generalIndicator.getIsScreen())&&1 == generalIndicator.getIsScreen()) {
                generalIndicatorData.setUpdateDate(maxUpdateDateYear);
            }
            generalIndicatorData.setCurrentFlag(1);
            generalIndicatorData.setDelete(0);
            generalIndicatorData.setIndicatorId(id);
            List<GeneralIndicatorData> generalIndicatorDatas = new ArrayList<>();
            //获取指标更新周期，区分年度更新与非年度更新
            if ("年度更新".equals(generalIndicator.getUpdateCycle())) {
                generalIndicatorDatas = generalIndicatorDataService.listIndicatorYearDatas(generalIndicatorData);
                if (ObjectUtil.isNotNull(maxUpdateDateYear)) {
                    String minUpdateDateYear = String.valueOf(Integer.valueOf(maxUpdateDateYear) - 4);
                    result.setYearDate(minUpdateDateYear + "-" + maxUpdateDateYear);
                }
            } else {
                generalIndicatorDatas = generalIndicatorDataService.listIndicatorDatas(generalIndicatorData);
                if (ObjectUtil.isNotNull(maxUpdateDateYear)) {
                    result.setYearDate(maxUpdateDateYear);
                }
            }
//        GeneralIndicatorData generalIndicatorData = new GeneralIndicatorData();
//        String showDate = req.getShowDate();
//        if (StringUtils.isBlank(showDate)) {
//            generalIndicatorData.setCurrentFlag(Constant.CURRENT_DATA_YES);
//        }
//        generalIndicatorData.setUpdateDate(req.getShowDate());
//        generalIndicatorData.setIndicatorId(id);
//        List<GeneralIndicatorData> generalIndicatorDatas = generalIndicatorDataService.list(sourceSimpleName, generalIndicatorData);
            if (generalIndicatorDatas != null && generalIndicatorDatas.size() > 0) {
                List<GeneralIndicatorDataVO> generalIndicatorDataVOS = BeanHelper.copyWithCollection(generalIndicatorDatas, GeneralIndicatorDataVO.class);
                String indicatorName = generalIndicatorDatas.get(0).getIndicatorName();
                if (StringUtils.isNotBlank(indicatorName)) {
                    result.setIndicatorName(indicatorName);
                }
//            result.setUpdateDate(generalIndicatorDatas.get(0).getUpdateDate());
                result.setList(generalIndicatorDataVOS);
            }
        }else if(generalIndicator.getDataUpdateMode()==2){
            //如果是数据对接，则请求数据集接口获取数据
//            if(!ObjectUtil.isEmpty(generalIndicator.getDataConfigId())) {
                //数据配置id不是空的时，数据集等均不是空的，前端在数据配置弹窗中做了数据配置相关全部的必填项
            List<DataConfig> dataConfigList = dataConfigService.getDataConfigByIndicatorId(generalIndicator.getId());
            log.info("获取数据集数据之前");
            if (ObjectUtils.isNotEmpty(dataConfigList)&&dataConfigList.size()>0){
                log.info("获取数据集数据之前进入判断条件");
                List<GeneralIndicatorDataVO> dataUpdateMode2 = new ArrayList<>();
                try {
                    //同一个指标配置的数据集是相同的，故只需要取第一个数据配置的数据集id即可
                    Long dataSetId = dataConfigList.get(0).getDataSetId();
                    //请求数据集接口获取数据
                    ResponseBean responseBean = reportFeignService.detailById(dataSetId);
                    log.info("获取到的数据集数据为data："+responseBean.getData().toString());
                    //获取数据非空时将其处理为前端需要的格式
                    if (!ObjectUtil.isEmpty(responseBean.getData())) {
                        LinkedHashMap dataDto = (LinkedHashMap)responseBean.getData();
                        List<Map> dataArray = (List<Map>)dataDto.get("data");
                        if (dataArray.size() > 0) {
                            for (Map object : dataArray) {
                                GeneralIndicatorDataVO generalIndicatorDataVO = new GeneralIndicatorDataVO();
                                List<ItemValues> itemValuesList = new LinkedList<>();
                                for(DataConfig dc : dataConfigList) {
                                    ItemValues itemValues = new ItemValues();
                                    //设置获取数据对接指标数据的数据key
                                    itemValues.setDataValue(dc.getDataValue());
                                    //设置值是主值还是副值
                                    itemValues.setIsMaster(dc.getIsMasterValue());
                                    //设置值的名称
                                    itemValues.setItemValueName(dc.getDataValueName());
                                    //设置值的具体数据
                                    if(null != object.get(dc.getDataKey())) {
                                        itemValues.setItemValueValue(object.get(dc.getDataValue()).toString());
                                    }
                                    //设置x轴的值
                                    if(null != object.get(dc.getDataValue())) {
                                        generalIndicatorDataVO.setItemName(object.get(dc.getDataKey()).toString());
                                    }
                                    //设置x轴的名称即x轴具体代表什么意思
                                    generalIndicatorDataVO.setDataKeyName(dc.getDataKeyName());
                                    //设置主值单位
                                    generalIndicatorDataVO.setItemUnit(dc.getDataUnit());
                                    //设置副值单位
                                    generalIndicatorDataVO.setSecondaryUnit(dc.getSecondaryUnit());
                                    itemValuesList.add(itemValues);
                                }
                                generalIndicatorDataVO.setItemValuesList(itemValuesList);
//                                generalIndicatorDataVO.setItemUnit(result.getDataUnit());
                                dataUpdateMode2.add(generalIndicatorDataVO);
                            }
                        }
                    }
                    result.setList(dataUpdateMode2);
                } catch (Exception e) {
                    log.error("请求数据集接口获取数据异常！" + e.getMessage());
                }
            }
//            }
        }
    }

    /**
     * 设置历史年份集合
     *
     * @param generalIndicator
     * @param result
     */
    private void setShowDateList(GeneralIndicator generalIndicator, GeneralIndicatorReqVO req, GeneralIndicatorCoreVo result) {
        String id = generalIndicator.getId();
        String sourceSimpleName = req.getSourceSimpleName();
        List<String> showDateList = generalIndicatorDataService.listShowDate(sourceSimpleName, id);
        if (showDateList != null && showDateList.size() > 0) {
            result.setShowDateList(showDateList);
        }

    }


    /**
     * 获取解读数据列表
     *
     * @param req
     * @return
     */
    private List<GeneralIndicatorDetailVO> getDetailList(GeneralIndicatorReqVO req) {
        String id = req.getId();
//        String sourceSimpleName = req.getSourceSimpleName();
//        String tableName = sourceSimpleName + Constant.GENERAL_INDICATOR_TALBE_SUFFIX;
        GeneralIndicator param = new GeneralIndicator();
        param.setSourceId(req.getSourceId());
        param.setParentId(id);
        param.setDelete(Constant.DELETE_NO);
        param.setIsShow(Constant.SHOW_YES);
        if(req.getIndicatorType()!=null){
            param.setIndicatorType(req.getIndicatorType());
        }else{
            param.setIndicatorType(Constant.TAB_INDICATOR);
        }
//        param.setIndicatorType(Constant.DETAIL_DATA);
        List<GeneralIndicator> generalIndicators = indicatorMapper.listCoreIndicatorByParentId(param);
        List<GeneralIndicatorDetailVO> generalIndicatorDetailVos = null;
        if (generalIndicators != null && generalIndicators.size() > 0) {
            generalIndicatorDetailVos = new ArrayList<>(generalIndicators.size());
            for (GeneralIndicator generalIndicator : generalIndicators) {
                GeneralIndicatorDetailVO generalIndicatorDetailVO = this.setDetailIndicator(generalIndicator, req);
                //在指标信息上添加指标头信息
                List<GeneralIndicatorDataTitle> titles = indicatorTitleService.queryById(generalIndicator.getId());
                generalIndicatorDetailVO.setTableHeaderList(titles);
                generalIndicatorDetailVos.add(generalIndicatorDetailVO);
            }
        }

        return generalIndicatorDetailVos;
    }

    private GeneralIndicatorDetailVO setDetailIndicator(GeneralIndicator generalIndicator, GeneralIndicatorReqVO req) {
        //设置基本信息
        GeneralIndicatorDetailVO result = this.setBaseInfoInDetail(generalIndicator);
        //设置展示指标数据
        this.setBaseDataListInDetail(generalIndicator, req, result);
        //设置下级数据
        req.setId(generalIndicator.getId());
        //TODO indicator_type表示0：指标1：tab
        req.setIndicatorType(Constant.GENERAL_INDICATOR);
        List<GeneralIndicatorDetailVO> childList = this.getDetailList(req);
        result.setChildList(childList);
        return result;
    }

    private GeneralIndicatorDetailVO setBaseInfoInDetail(GeneralIndicator generalIndicator) {
        GeneralIndicatorDetailVO result = BeanHelper.copyProperties(generalIndicator, GeneralIndicatorDetailVO.class);
        //因多维指标展示数据对接中指标id记录在数据对接表中，故不需要在指标中设置数据配置相关了
//        if(result.getDataUpdateMode()==2&&!ObjectUtil.isEmpty(result.getDataConfigId())){
//            DataConfig dataConfig = dataConfigService.getDataConfigById(result.getDataConfigId());
//            result.setDataKey(dataConfig.getDataKey());
//            result.setDataValue(dataConfig.getDataValue());
//            result.setDataSetId(dataConfig.getDataSetId());
//            result.setDataSetName(dataConfig.getDataSetName());
//            result.setDataUnit(dataConfig.getDataUnit());
//        }
        return result;
    }

    /**
     * 设置展示指标数据--解读区
     *
     * @param generalIndicator
     * @param result
     */
    private void setBaseDataListInDetail(GeneralIndicator generalIndicator, GeneralIndicatorReqVO req, GeneralIndicatorDetailVO result) {
        if (generalIndicator.getDataUpdateMode()==1) {
            String id = generalIndicator.getId();
            //todo 获取最大的数据期，并将数据期进行返回给前端，非年度更新的以最新有数据的年份进行指标数据获取，年度更新的以最大年份往前数五年为默认区间进行指标数据获取
            //获取最大的数据期
            String maxUpdateDateYear = generalIndicatorDataService.getMaxUpdateDateYear(id);
//        String sourceSimpleName = req.getSourceSimpleName();
            //组装查询条件
            GeneralIndicatorData generalIndicatorData = new GeneralIndicatorData();
            if (ObjectUtil.isNotNull(generalIndicator.getIsScreen())&&1 == generalIndicator.getIsScreen()) {
                generalIndicatorData.setUpdateDate(maxUpdateDateYear);
            }
            generalIndicatorData.setCurrentFlag(1);
            generalIndicatorData.setDelete(0);
            generalIndicatorData.setIndicatorId(id);
            List<GeneralIndicatorData> generalIndicatorDatas = new ArrayList<>();
            //获取指标更新周期，区分年度更新与非年度更新
            if ("年度更新".equals(generalIndicator.getUpdateCycle())) {
                generalIndicatorDatas = generalIndicatorDataService.listIndicatorYearDatas(generalIndicatorData);
                if (ObjectUtil.isNotNull(maxUpdateDateYear)) {
                    String minUpdateDateYear = String.valueOf(Integer.valueOf(maxUpdateDateYear) - 4);
                    result.setYearDate(minUpdateDateYear + "-" + maxUpdateDateYear);
                }
            } else {
                generalIndicatorDatas = generalIndicatorDataService.listIndicatorDatas(generalIndicatorData);
                if (ObjectUtil.isNotNull(maxUpdateDateYear)) {
                    result.setYearDate(maxUpdateDateYear);
                }
            }
//        String sourceSimpleName = req.getSourceSimpleName();
//        GeneralIndicatorData generalIndicatorData = new GeneralIndicatorData();
//        String showDate = req.getShowDate();
//        if (StringUtils.isBlank(showDate)) {
//            generalIndicatorData.setCurrentFlag(Constant.CURRENT_DATA_YES);
//        }
//        generalIndicatorData.setUpdateDate(req.getShowDate());
//        generalIndicatorData.setIndicatorId(id);
//        List<GeneralIndicatorData> generalIndicatorDatas = generalIndicatorDataService.list(sourceSimpleName, generalIndicatorData);
            if (generalIndicatorDatas != null && generalIndicatorDatas.size() > 0) {
                List<GeneralIndicatorDataVO> generalIndicatorDataVOS = BeanHelper.copyWithCollection(generalIndicatorDatas, GeneralIndicatorDataVO.class);
                String indicatorName = generalIndicatorDatas.get(0).getIndicatorName();
                if (StringUtils.isNotBlank(indicatorName)) {
                    result.setIndicatorName(indicatorName);
                }
                result.setList(generalIndicatorDataVOS);
            }
        }else if (generalIndicator.getDataUpdateMode()==2){
//            if(!ObjectUtil.isEmpty(generalIndicator.getDataConfigId())){
            //数据配置id不是空的时，数据集等均不是空的，前端在数据配置弹窗中做了数据配置相关全部的必填项
            List<DataConfig> dataConfigList = dataConfigService.getDataConfigByIndicatorId(generalIndicator.getId());
            log.info("获取数据集数据之前");
            if (ObjectUtils.isNotEmpty(dataConfigList)&&dataConfigList.size()>0){
                log.info("获取数据集数据之前进入判断条件");
                List<GeneralIndicatorDataVO> dataUpdateMode2 = new ArrayList<>();
                try {
                    //同一个指标配置的数据集是相同的，故只需要取第一个数据配置的数据集id即可
                    Long dataSetId = dataConfigList.get(0).getDataSetId();
                    //请求数据集接口获取数据
                    ResponseBean responseBean = reportFeignService.detailById(dataSetId);
                    log.info("获取到的数据集数据为data："+responseBean.getData().toString());
                    //获取数据非空时将其处理为前端需要的格式
                    LinkedHashMap dataDto = (LinkedHashMap)responseBean.getData();
                    List<Map> dataArray = (List<Map>)dataDto.get("data");
                    if (dataArray.size() > 0) {
                        for (Map object : dataArray) {
                            GeneralIndicatorDataVO generalIndicatorDataVO = new GeneralIndicatorDataVO();
                            List<ItemValues> itemValuesList = new LinkedList<>();
                            for(DataConfig dc : dataConfigList) {
                                ItemValues itemValues = new ItemValues();
                                //设置获取数据对接指标数据的数据key
                                itemValues.setDataValue(dc.getDataValue());
                                //设置值是主值还是副值
                                itemValues.setIsMaster(dc.getIsMasterValue());
                                //设置值的名称
                                itemValues.setItemValueName(dc.getDataValueName());
                                //设置值的具体数据
                                if(null != object.get(dc.getDataKey())) {
                                    itemValues.setItemValueValue(object.get(dc.getDataValue()).toString());
                                }
                                //设置x轴的值
                                if(null != object.get(dc.getDataValue())) {
                                    generalIndicatorDataVO.setItemName(object.get(dc.getDataKey()).toString());
                                }
                                //设置x轴的名称即x轴具体代表什么意思
                                generalIndicatorDataVO.setDataKeyName(dc.getDataKeyName());
                                //设置主值单位
                                generalIndicatorDataVO.setItemUnit(dc.getDataUnit());
                                //设置副值单位
                                generalIndicatorDataVO.setSecondaryUnit(dc.getSecondaryUnit());
                                itemValuesList.add(itemValues);
                            }
                            generalIndicatorDataVO.setItemValuesList(itemValuesList);
//                                generalIndicatorDataVO.setItemUnit(result.getDataUnit());
                            dataUpdateMode2.add(generalIndicatorDataVO);
                        }
                    }
                    result.setList(dataUpdateMode2);
                }catch (Exception e){
                    log.error("请求数据集接口获取数据异常！"+e.getMessage());
                }
            }
//            }
        }
    }
}