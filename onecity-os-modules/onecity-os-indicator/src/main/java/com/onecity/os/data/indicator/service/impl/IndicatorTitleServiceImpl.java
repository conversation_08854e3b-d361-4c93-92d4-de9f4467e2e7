package com.onecity.os.data.indicator.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.data.indicator.constants.UtilConstants;
import com.onecity.os.data.indicator.mapper.IndicatorTitleMapper;
import com.onecity.os.data.indicator.model.po.GeneralIndicatorDataTitle;
import com.onecity.os.data.indicator.service.IndicatorTitleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 指标数据标头
 *
 * @Author: zack
 * @Date: 2024/1/22 16:15
 */
@Slf4j
@Service("indicatorTitleService")
public class IndicatorTitleServiceImpl implements IndicatorTitleService {

    @Resource
    private IndicatorTitleMapper indicatorTitleMapper;

    @Override
    public List<GeneralIndicatorDataTitle> queryById(String indicatorId) {
        List<GeneralIndicatorDataTitle> titles = indicatorTitleMapper.queryById(indicatorId);
        //1.获取现有字段名称 eg:item_value item_value1 item_value2
        List<String> columnNames = titles.stream().map(GeneralIndicatorDataTitle::getColumnName).collect(Collectors.toList());
        //2.对比,获取完整字段名
        List<String> allColumnNames = UtilConstants.getAllColumn();
        //3.筛选出可用字段名
        List<String> checkColumnNames = new ArrayList<>();
        for (String column : allColumnNames) {
            if (!columnNames.contains(column)) {
                checkColumnNames.add(UtilConstants.getItemMap(column));
            }
        }
        //4..将可用字段传给接口
        for (GeneralIndicatorDataTitle title : titles) {
            title.setCheckColumn(checkColumnNames);
            title.setColumnName(UtilConstants.getItemMap(title.getColumnName()));
        }
        return titles;
    }

}
