package com.onecity.os.data.indicator.service.impl;

import com.onecity.os.data.indicator.mapper.MessageIndicatorDetailMapper;
import com.onecity.os.data.indicator.model.po.MessageIndicatorDetail;
import com.onecity.os.data.indicator.service.MessageIndicatorDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2020/5/22 16:36
 */
@Service
public class MessageIndicatorDetailServiceImpl implements MessageIndicatorDetailService {
    @Autowired
    private MessageIndicatorDetailMapper messageIndicatorDetailMapper;
    /**
     * 根据主键Id查询指标详情信息
     * @param id
     * @return
     */
    @Override
    public MessageIndicatorDetail getMessageIndicatorDetailById(Long id) {
        MessageIndicatorDetail messageIndicatorDetail = messageIndicatorDetailMapper.selectById(id);
        return messageIndicatorDetail;
    }

    @Override
    public int countByIndicatorId(String indicatorId) {
        int count = messageIndicatorDetailMapper.countByIndicatorId(indicatorId);
        return count;
    }

    /**
     * 查询历史指标列表
     * @param indicatorId
     * @return
     */
    @Override
    public List<MessageIndicatorDetail> listHistoryMessageIndicator(String indicatorId) {
        List<MessageIndicatorDetail> messageIndicatorDetails = messageIndicatorDetailMapper.selectByIndicatorId(indicatorId);
        return messageIndicatorDetails;
    }

    /**
     * 获取当前指标数据
     * @param indicatorId
     * @return
     */
    @Override
    public MessageIndicatorDetail getCurrentMessageIndicatorDetail(String indicatorId) {
        List<MessageIndicatorDetail> messageIndicatorDetails = messageIndicatorDetailMapper.selectByIndicatorId(indicatorId);
        MessageIndicatorDetail result = new MessageIndicatorDetail();
        if(messageIndicatorDetails!=null&&messageIndicatorDetails.size()>0){
            result = messageIndicatorDetails.get(0);
        }
        return result;
    }
}
