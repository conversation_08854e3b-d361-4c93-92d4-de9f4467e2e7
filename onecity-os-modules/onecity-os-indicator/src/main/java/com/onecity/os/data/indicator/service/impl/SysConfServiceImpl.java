package com.onecity.os.data.indicator.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.data.indicator.exception.BusinessException;
import com.onecity.os.data.indicator.mapper.SysSeqConfMapper;
import com.onecity.os.data.indicator.model.po.SysSeqConf;
import com.onecity.os.data.indicator.service.SysSeqConfService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2020/5/21 10:38
 */
@Service
public class SysConfServiceImpl implements SysSeqConfService {
    private Logger log = LoggerFactory.getLogger(SysConfServiceImpl.class);
    @Autowired
    private SysSeqConfMapper sysSeqConfMapper;

    /**修改序列返回下一个值（String）
     * @param seqId
     * @return
     */
    @Override
    public String getStringSequence(String seqId){
        log.info("Start - getStringSequence");
        String seq = null;
        log.info("Parameter - seqId:"+seqId
        );
        if(seqId!=null&&!"".equals(seqId)){
            SysSeqConf sysSeqConf = sysSeqConfMapper.selectByPrimaryKey(seqId);
            if(sysSeqConf!=null){
                seq = this.seqCurrentNext(sysSeqConf);
            }else{
                log.error("通过seqId："+seqId+"查找主键序列失败，返回值为空！");
                throw new BusinessException("系统异常，请联系管理员！");
            }
        }else{
            log.error("查找序列失败，key值为空！");
            throw new BusinessException("系统异常，请联系管理员！");
        }
        log.info("End - getStringSequence");
        return seq;
    }

    /** 获取下一位int型序列
     * @param seqId
     * @return
     */
    @Override
    public Integer getIntSequence(String seqId){
        log.info("Start - getIntSequence");
        String seq = null;
        log.info("Parameter - seqId:"+seqId);
        SysSeqConf sysSeqConf = sysSeqConfMapper.selectByPrimaryKey(seqId);
        seq = this.seqCurrentNext(sysSeqConf);
        Integer intseq = null;
        if(seq!=null||!"".equals(seq)){
            intseq = Integer.parseInt(seq);
        }else{
            throw new BusinessException("系统异常，请联系管理员！");
        }
        log.info("End - getIntSequence");
        return intseq;
    }

    /** 获取规则序列值
     * @param seqId 序列名称
     * @param length 序列总长度
     * @param prefix 序列前缀
     * @return
     */
    @Override
    public String getStringRuleSequence(String seqId,Integer length,String prefix){
        log.info("Start - getStringRuleSequence");
        String seq = null;
        log.info("Parameter - seqId:"+seqId);
        log.info("Parameter - length:"+length);
        //如果未设定长度默认为15
        if(length==null||length==0){
            length=15;
        }
        log.info("Parameter - prefix:"+prefix);
        if(seqId!=null&&"".equals(seqId)){
            seq = this.getStringSequence(seqId);
            if(seq!=null&&!"".equals(prefix)){
                int prefixLength = prefix.length();
                int seqLength = seq.length();
                int size = prefixLength+seqLength;
                if(length>=size){
                    for(int i =length -size;i>0;i--){
                        seq = "0"+seq;
                    }
                    seq = prefix+seq;
                }else{
                    log.error("序列值超出了给定长度！");
                    throw  new BusinessException("系统异常，请联系管理员！");
                }
            }else{
                log.info("获取序列失败！");
            }

        }else{
            log.info("获取主键失败！，传入名称为空！");
        }
        log.info("End - getStringRuleSequence");
        return seq;
    }
    /**修改序列值加1
     * @param sysSeqConf
     * @return
     */
    @Override
    public String seqCurrentNext(SysSeqConf sysSeqConf){
        log.info("Start-seqCurrentNext");
        //声明返回值
        String seq = null;
        if(sysSeqConf!=null){
            long seqCurrent = sysSeqConf.getSeqCurrent();
            long seqMaximum = sysSeqConf.getSeqMaximum();
            if(seqCurrent!=0){
                //获取下一位
                seqCurrent +=1;
                //判断序列值是否超过最大值
                if(seqCurrent>seqMaximum){
                    log.error("序列值已经超出最大值！");
                    throw new BusinessException("系统异常，请联系管理员！");
                }else{
                    sysSeqConf.setSeqCurrent(seqCurrent);
                    //修改数据库
                    int i = sysSeqConfMapper.updateByPrimaryKey(sysSeqConf);
                    //判断修改是否成功
                    if(i==1){
                        //转成字符串
                        seq = Long.toString(seqCurrent);
                        log.info("获取序列成功，seqId："+ JSONObject.toJSONString(sysSeqConf.getSeqId())+"seqCurrent："+JSONObject.toJSONString(seqCurrent));
                    }else{
                        log.info("主键序列修改失败seqId："+JSONObject.toJSONString(sysSeqConf.getSeqId()));
                        throw new BusinessException("系统异常，请联系管理员！");
                    }
                }
            }else{
                String seqId = sysSeqConf.getSeqId();
                log.info("seqId:"+JSONObject.toJSONString(seqId)+"-序列当前值为空");
                throw new BusinessException("系统异常，请联系管理员！");
            }
        }else{
            log.error("调用seqCurrentNext方法传入对对象为空！");
            throw new BusinessException("系统异常，请联系管理员！");
        }
        log.info("End-seqCurrentNext");
        return seq;
    }
}
