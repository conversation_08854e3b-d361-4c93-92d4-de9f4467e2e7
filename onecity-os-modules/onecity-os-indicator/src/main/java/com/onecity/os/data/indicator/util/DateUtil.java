package com.onecity.os.data.indicator.util;

import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2020/5/25 10:11
 */
public class DateUtil {

    public static String getString(LocalDate date, String pattern){
        String result = "";
        if(StringUtils.isBlank(pattern)){
            result = getString(date);
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        result = date.format(dateTimeFormatter);
        return result;
    }

    public static String getString(LocalDateTime dateTime, String pattern){
        String result = "";
        if(StringUtils.isBlank(pattern)){
            result = getString(dateTime);
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        result = dateTime.format(dateTimeFormatter);
        return result;
    }

    public static String getString(LocalDate date){
        if(date==null){
            date = LocalDate.now();
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy.MM.dd");
        String format = date.format(dateTimeFormatter);
        return format;
    }

    public static String getString(LocalDateTime dateTime){
        if(dateTime==null){
            dateTime = LocalDateTime.now();
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm:SS");
        String format = dateTime.format(dateTimeFormatter);
        return format;
    }
}
