package com.onecity.os.data.notice.entity.po;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/3/21
 * @Version V1.0
 **/


@Data
@Table(name = "specialfileadmin")
@ApiModel(value = "要情专报信息管理")
public class SpecialReportFileAdmin {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private String theme;
    private String createrid;
    private String creater;
    private String createtime;
    private String department;
    private Integer iswrite;
    /**
     * 是否删除 0:否1:是
     */
    private  Integer isDelete;
    //工作通知任务id
    private String taskid;
}
