package com.onecity.os.data.notice.entity.vo;

import lombok.Data;
import javax.persistence.*;
import java.util.Date;

/**
 * @Author: zack
 * @Date: 2022/4/15 10:16
 */
@Data
@Table(name = "notice_manage")
public class NoticeManage {
    /**
     * 主键id
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Long id;

    /**
     * ip地址
     */
    @Transient
    private String host;

    /**
     * 标题
     */
    private String title;

    /**
     * 发布来源
     */
    @Column(name = "public_source")
    private String publicSource;

    /**
     * 发布日期
     */
    @Column(name = "public_date")
    private String publicDate;

    /**
     * 文件名
     */
    @Column(name = "file_name")
    private String fileName;

    /**
     * 文件路径
     */
    @Column(name = "file_path")
    private String filePath;

    /**
     * 状态(0-草稿;1-已发布;2已撤回)
     */
    private Byte status;

    /**
     * 是否已读 0-否;1-是
     */
    @Transient
    private Byte isRead;

    /**
     * 阅读次数
     */
    @Column(name = "read_count")
    private Integer readCount;

    /**
     * 是否删除 0-否;1-是
     */
    @Column(name = "is_delete")
    private Byte isDelete;

    /**
     * 创建人
     */
    private String creater;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 发布的内容
     */
    private String content;

}
