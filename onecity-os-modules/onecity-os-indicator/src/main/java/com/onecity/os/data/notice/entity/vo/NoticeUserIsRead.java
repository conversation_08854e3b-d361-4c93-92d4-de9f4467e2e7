package com.onecity.os.data.notice.entity.vo;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * @Author: zack
 * @Date: 2022/4/15 14:29
 */
@Data
@Table(name = "notice_user_is_read")
public class NoticeUserIsRead {
    /**
     * 主键id
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Long id;

    /**
     * 钉钉用户id
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 通知公告主键id
     */
    @Column(name = "notice_id")
    private Long noticeId;

    /**
     * 创建人
     */
    private String creater;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
