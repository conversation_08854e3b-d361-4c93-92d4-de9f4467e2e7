package com.onecity.os.data.notice.mapper;

import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.data.notice.entity.RemindInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
@Mapper
public interface RemindInfoMapper extends BaseMapper<RemindInfo> {

    /**
     * 查询推送消息
     * @return
     */
    List<RemindInfo> getOverDueRemindForMsg();

    /**
     * 查询推送消息
     * @return
     */
    List<RemindInfo> getAnalysisRemindForMsg();


    List<RemindInfo> selectList(@Param("startTime")String startTime,@Param("endTime")String endTime,
                                @Param("remindedType")int remindedType,@Param("remindedCount")int remindedCount);
}