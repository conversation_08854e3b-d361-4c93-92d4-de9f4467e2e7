package com.onecity.os.data.notice.mapper;


import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.data.notice.entity.po.SpecialReportFileAdmin;
import org.apache.ibatis.annotations.*;


import java.util.List;

@Mapper
public interface SpecialFileAdminMapper extends BaseMapper<SpecialReportFileAdmin> {

    @Insert("insert into specialfileadmin (theme,createrid,department,creater,createtime) values (#{theme}, #{createrid},#{department},#{creater}, #{createtime})")
    @Options(useGeneratedKeys=true, keyProperty="id", keyColumn="id")
    void save(SpecialReportFileAdmin dingFileAdmin);

    @Select("SELECT * from specialfileadmin a where createrid=#{createrid} and is_delete = 0 ORDER BY a.createtime desc,a.iswrite desc")
    List<SpecialReportFileAdmin> getFileAdmin(@Param("createrid") String createrid);

    @Select("SELECT * from specialfileadmin where id = #{id} and is_delete = 0")
    SpecialReportFileAdmin getFileAdminById(Integer id);

    @Update("update specialfileadmin set is_delete = 1  where id = #{id}")
    void deleteRecorde(Integer id);

    @Update("update specialfileadmin set task_id = #{taskId}  where id = #{id}")
    void updateTaskId(@Param("id") Integer id,@Param("taskId") String taskId);
}