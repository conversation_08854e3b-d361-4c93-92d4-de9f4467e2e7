package com.onecity.os.data.notice.mapper;


import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.data.notice.entity.po.SpecialReportFileAdmin;
import com.onecity.os.data.notice.entity.po.SpecialReportFileManage;
import com.onecity.os.data.notice.entity.vo.NoticeManage;
import com.onecity.os.data.notice.entity.vo.NoticeUserIsRead;
import com.onecity.os.data.noticeimportant.entity.vo.DingFileAdminVo;
import com.onecity.os.data.noticeimportant.entity.vo.DingFileManageVo;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface SpecialFileManageMapper extends BaseMapper<SpecialReportFileManage> {


    List<DingFileManageVo> selectListByUserid(@Param("userid") String userid);
    List<DingFileManageVo> selectQueryListByRecevierId(@Param("userid") String userid, String creater, String startdate, String enddate, String filename);

    List<SpecialReportFileAdmin> selectQueryListByCreaterId(@Param("userid") String userid, String receiver, String startdate, String enddate, String filename);

    List<SpecialReportFileManage> selectListByAdminid(@Param("adminid") Integer adminid);

    SpecialReportFileManage selectById(@Param("id") String id);

    void save(SpecialReportFileManage fileManange);


    /**
     * 根据用户id,获取发送的文件列表
     *
     * @param userid
     * @param receiver
     * @param startdate
     * @param enddate
     * @param filename
     * @return
     */
    List<DingFileAdminVo> getSendListByUserId(@Param("userid") String userid, @Param("receiver") String receiver,
                                              @Param("startdate") String startdate, @Param("enddate") String enddate,
                                              @Param("filename") String filename);

    /**
     * 查询通知公告
     * @return
     */
    List<NoticeManage> getNoticePageList();

    List<NoticeUserIsRead> getNoticeUserMsg(@Param("userid") String userid, @Param("noticeId") Long noticeId);

    @Select("SELECT * from specialfilemanage where createrid=#{userid}")
    List<SpecialReportFileManage> getSendList(@Param("userid") String userid);

    @Delete("DELETE from specialfilemanage where adminid=#{adminid}")
    int deleteByAdmin(@Param("adminid") String adminid);
}