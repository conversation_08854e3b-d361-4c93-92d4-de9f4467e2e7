package com.onecity.os.data.notice.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.data.indicator.model.bean.CommentInfo;
import com.onecity.os.data.notice.entity.po.SpecialComment;
import com.onecity.os.data.notice.mapper.SpecialCommentMapper;
import com.onecity.os.data.noticeimportant.service.CommentAbstractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@Transactional
public class SpecialCommentServiceImpl extends CommentAbstractService<SpecialComment> {
    @Autowired
    SpecialCommentMapper specialCommentMapper;

    public void sendComment(CommentInfo commentInfo) {
        Date now = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SpecialComment dingComment = new SpecialComment();
        dingComment.setAdminid(commentInfo.getAdminid());
        dingComment.setReceiver(commentInfo.getReceiver());
        dingComment.setReceiverid(commentInfo.getReceiverid());
        dingComment.setCommenttitle(commentInfo.getCommenttitle());
        dingComment.setCommentcontent(commentInfo.getCommentcontent());
        dingComment.setWritetime(format.format(now));
        dingComment.setFilemanageid(commentInfo.getFileid());
        //1、保存审批记录
        specialCommentMapper.save(dingComment);
        log.info("审批记录保存成功! -->{}", JSONObject.toJSONString(dingComment));
        //2、修改发送记录中 iswrite 为 1， 已批示
        specialCommentMapper.updateFileadminIsWrite(commentInfo.getAdminid());
        log.info("修改发送记录中 iswrite 成功!");
        //3、修改文件记录中 iswrite 为 1， 已批示
        specialCommentMapper.updateFilemanageIsWrite(commentInfo.getFileid());
        log.info("修改文件记录中 iswrite 成功!");
        //4、修改接收记录中 isread iswrite 均为1，已阅,已批示
        specialCommentMapper.updateIsWriteAndRead(commentInfo.getFileid(), commentInfo.getReceiverid());
        log.info("修改接收记录中 isread iswrite 均为1  成功!");
    }

    public SpecialComment checkCommentByReceiver(Integer filemanageid, String receiverid) {
        SpecialComment dingComment = null;
        try {
            dingComment = specialCommentMapper.selectByReceiverid(filemanageid, receiverid);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dingComment;
    }

    public List<SpecialComment> checkCommentBySender(Integer adminid) {
        List<SpecialComment> dingComments = null;
        try {
            dingComments = specialCommentMapper.selectBySender(adminid);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dingComments;
    }

    @Override
    public List<SpecialComment> checkCommentByFile(Integer filemanageid) {
        List<SpecialComment> dingComments = null;
        try {
            dingComments = specialCommentMapper.selectByFile(filemanageid);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dingComments;
    }
}
