package com.onecity.os.data.notice.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.data.indicator.app.AppManageUtil;
import com.onecity.os.data.indicator.model.vo.dingmaillist.DingFileVo;
import com.onecity.os.data.indicator.util.Base64Util;
import com.onecity.os.data.notice.entity.po.SpecialReportFileAdmin;
import com.onecity.os.data.notice.entity.po.SpecialReportFileManage;
import com.onecity.os.data.notice.entity.po.SpecialReportFileReceiver;
import com.onecity.os.data.notice.entity.vo.NoticeManage;
import com.onecity.os.data.notice.entity.vo.NoticeUserIsRead;
import com.onecity.os.data.notice.mapper.SpecialFileAdminMapper;
import com.onecity.os.data.notice.mapper.SpecialFileManageMapper;
import com.onecity.os.data.notice.mapper.SpecialFileReceiverMapper;
import com.onecity.os.data.notice.service.SpecialFileManageService;
import com.onecity.os.data.noticeimportant.entity.vo.DingFileAdminVo;
import com.onecity.os.data.noticeimportant.entity.vo.DingFileManageVo;
import com.onecity.os.data.noticeimportant.entity.vo.DingFileUpload;
import com.onecity.os.data.noticeimportant.entity.vo.DingFileUploadUpdate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class SpecialFileManageServiceImpl implements SpecialFileManageService {

    @Autowired
    private SpecialFileManageMapper dingFileManageMapper;
    @Autowired
    private SpecialFileReceiverMapper dingFileReceiverMapper;
    @Autowired
    private SpecialFileAdminMapper dingFileAdminMapper;
    @Autowired
    private AppManageUtil appManageUtil;

    @Override
    public List<DingFileManageVo> getList(String userid) {
        return dingFileManageMapper.selectListByUserid(userid);
    }

    @Override
    public List<DingFileManageVo> getQueryListByRecevierId(String userid, String creater, String startdate, String enddate, String filename) {
        return dingFileManageMapper.selectQueryListByRecevierId(userid, creater, startdate, enddate, filename);
    }

    @Override
    public List<SpecialReportFileAdmin> getQueryListByCreaterId(String userid, String receiver, String startdate, String enddate, String filename) {
        return dingFileManageMapper.selectQueryListByCreaterId(userid, receiver, startdate, enddate, filename);
    }

    @Override
    public List<String> getReceiverId(Integer id) {
        return dingFileReceiverMapper.getReceiverId(id);
    }

    @Override
    public SpecialReportFileManage getById(String id) {
        return dingFileManageMapper.selectById(id);
    }

    @Override
    public void deleteById(String id) {
        dingFileManageMapper.deleteByIds(id);
    }

    @Override
    public List<SpecialReportFileManage> getSendList(String userid) {
        return dingFileManageMapper.getSendList(userid);
    }

    @Override
    public Integer saveDingFiles(DingFileUpload fileUploadList){
        //保存基本消息
        SpecialReportFileAdmin fileAdmin=new SpecialReportFileAdmin();
        Calendar cal = Calendar.getInstance();
        Date now = cal.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        fileAdmin.setCreater(fileUploadList.getCreater());
        if(StringUtils.isNotBlank(fileUploadList.getDepartment())) {
            fileAdmin.setDepartment(fileUploadList.getDepartment());
        }
        fileAdmin.setCreaterid(fileUploadList.getCreaterid());
        fileAdmin.setCreatetime(format.format(now));
        fileAdmin.setTheme("无");
        dingFileAdminMapper.save(fileAdmin);
        //保存附件/接收人信息
        for(DingFileVo baseFile: fileUploadList.getFileList()){
            SpecialReportFileManage dingfileManage =new SpecialReportFileManage();
            dingfileManage.setAdminid(fileAdmin.getId());
            dingfileManage.setSpaceId(baseFile.getSpaceId());
            dingfileManage.setFileId(baseFile.getFileId());
            dingfileManage.setFileName(baseFile.getFileName());
            dingfileManage.setFileType(baseFile.getFileType());
            dingfileManage.setFileSize(baseFile.getFileSize());
            dingFileManageMapper.save(dingfileManage);
            for (DingFileUpload.ReceiverListBean receiver: fileUploadList.getReceiverList()){
                SpecialReportFileReceiver fileReceiver=new SpecialReportFileReceiver();
                fileReceiver.setFileid(dingfileManage.getId());
                fileReceiver.setReceiverid(receiver.getReceiverid());
                fileReceiver.setReceiver(receiver.getReceiver());
                dingFileReceiverMapper.insert(fileReceiver);
            }
        }
        return fileAdmin.getId();
    }

    @Override
    public List<SpecialReportFileAdmin> getFileAdmin(String userid) {
//        QueryWrapper<SpecialReportFileAdmin> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("createrid",userid);
        return dingFileAdminMapper.getFileAdmin(userid);
    }

    @Override
    public List<SpecialReportFileManage> getFileManange(Integer id) {
        return dingFileManageMapper.selectListByAdminid(id);
    }

    @Override
    public List<String> getFileReceiver(Integer id) {
        return dingFileReceiverMapper.getRecervers(id);
    }

    @Override
    public void updateRead(String id, String userid) {
        dingFileReceiverMapper.updateRead(id,userid);
    }

    @Override
    public SpecialReportFileAdmin getFileAdminById(Integer id) {
        return dingFileAdminMapper.getFileAdminById(id);
    }

    @Override
    public void deleteFileAllInfo(String id) {
        dingFileReceiverMapper.deleteByAdminid(id);
        dingFileManageMapper.deleteByAdmin(id);
        dingFileAdminMapper.deleteByIds(id);
    }

    @Override
    public String sendDingFilesToUser(DingFileUploadUpdate fileUploadList, String accessToken, String userid, String agent_id, String code) throws IOException {
        //保存基本消息
        SpecialReportFileAdmin fileAdmin=new SpecialReportFileAdmin();
        Calendar cal = Calendar.getInstance();
        Date now = cal.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        fileAdmin.setCreater(fileUploadList.getCreater());
        if(StringUtils.isNotBlank(fileUploadList.getDepartment())) {
            fileAdmin.setDepartment(fileUploadList.getDepartment());
        }
        fileAdmin.setCreaterid(fileUploadList.getCreaterid());
        fileAdmin.setCreatetime(format.format(now));
        fileAdmin.setTheme("无");
        dingFileAdminMapper.save(fileAdmin);
        //保存附件/接收人信息
        for (DingFileUploadUpdate.FileListBean baseFile: fileUploadList.getFileList()){
            MultipartFile multipartFile = Base64Util.base64ToMultipart(baseFile.getContent());
            String originalFilename = baseFile.getName();
            log.info("originalFilename -->{}",JSONObject.toJSONString(originalFilename));
            // 文件扩展名
            String ext = originalFilename.substring(originalFilename.lastIndexOf(".")).trim();
            if (multipartFile.isEmpty()) {
                return "请选择图片！";
            }
            //避免文件名重复使用uuid来避免,产生一个随机的uuid字符
            String realFileName= UUID.randomUUID().toString();
            String url = "";
            try {
                JSONObject jsonObject = appManageUtil.uploadDingFile(accessToken, agent_id, multipartFile);
                url = jsonObject.getString("media_id");
                List<String> receiverIds = fileUploadList.getReceiverList().stream().map(DingFileUploadUpdate.ReceiverListBean::getReceiverid).collect(Collectors.toList());
                DingFileVo dingFileVo = appManageUtil.sendDingFileToUser(userid, code, accessToken, agent_id, receiverIds, url, originalFilename);
                SpecialReportFileManage dingfileManage =new SpecialReportFileManage();
                dingfileManage.setAdminid(fileAdmin.getId());
                dingfileManage.setSpaceId(dingFileVo.getSpaceId());
                dingfileManage.setFileId(dingFileVo.getFileId());
                dingfileManage.setFileName(dingFileVo.getFileName());
                dingfileManage.setFileType(dingFileVo.getFileType());
                dingfileManage.setFileSize(dingFileVo.getFileSize());
                dingFileManageMapper.save(dingfileManage);
                for (DingFileUploadUpdate.ReceiverListBean receiver : fileUploadList.getReceiverList()) {
                    SpecialReportFileReceiver fileReceiver=new SpecialReportFileReceiver();
                    fileReceiver.setFileid(dingfileManage.getId());
                    fileReceiver.setReceiverid(receiver.getReceiverid());
                    fileReceiver.setReceiver(receiver.getReceiver());
                    dingFileReceiverMapper.insert(fileReceiver);
                }
            } catch (Exception e) {
                log.error("ding upload error ! --> {} ", e);
                return "钉钉文件上传失败!";
            }
        }
        return fileAdmin.getId()+"";
    }

    @Override
    public void deleteRecorde(Integer id) {
        dingFileAdminMapper.deleteRecorde(id);
    }

    @Override
    public void updateTaskId(Integer id, String taskId) {
        dingFileAdminMapper.updateTaskId(id, taskId);
    }

    @Override
    public List<String> getReceiverFileid(Integer fileid) {
        return dingFileReceiverMapper.getReceiverFileid(fileid);
    }

    @Override
    public List<DingFileAdminVo> getSendListByUserId(String userid, String receiver, String startdate, String enddate, String filename) {
        return dingFileManageMapper.getSendListByUserId(userid, receiver, startdate, enddate, filename);
    }

    @Override
    public List<NoticeManage> getNoticePageList(String userId) {
        List<NoticeManage> pageList = dingFileManageMapper.getNoticePageList();
        //判断该用户是否已读,分页数据，数量不大，所以采用环处理
        pageList.forEach(notice -> {
            List<NoticeUserIsRead> userMsg = dingFileManageMapper.getNoticeUserMsg(userId, notice.getId());
            if (CollectionUtils.isEmpty(userMsg)) {
                notice.setIsRead((byte) 0);
            } else {
                notice.setIsRead((byte) 1);
            }
        });
        return pageList;
    }

}
