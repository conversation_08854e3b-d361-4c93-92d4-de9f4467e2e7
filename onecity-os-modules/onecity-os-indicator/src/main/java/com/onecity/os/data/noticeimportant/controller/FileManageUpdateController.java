package com.onecity.os.data.noticeimportant.controller;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.data.handler.PassToken;
import com.onecity.os.data.indicator.app.AuthHelper;
import com.onecity.os.data.indicator.app.AppManageUtil;
import com.onecity.os.data.indicator.app.DingEVN;
import com.onecity.os.data.indicator.model.bean.CommentInfo;
import com.onecity.os.data.indicator.model.bean.DingWorkMsgInfo;
import com.onecity.os.data.indicator.util.BlankUtil;
import com.onecity.os.data.noticeimportant.entity.ImportantReportManage;
import com.onecity.os.data.noticeimportant.entity.po.DingComment;
import com.onecity.os.data.noticeimportant.entity.po.DingFileAdmin;
import com.onecity.os.data.noticeimportant.entity.po.DingFileManage;
import com.onecity.os.data.noticeimportant.entity.vo.*;
import com.onecity.os.data.noticeimportant.service.DingFileManageService;
import com.onecity.os.data.noticeimportant.service.impl.DingCommentServiceImpl;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 要情专报接口 版本2
 * 20200320
 * 通知公告接口 更改为 要情专报接口
 */
@Slf4j
@RestController
@Api(tags = "要情专报接口")
@RequestMapping("/fileupdate")
public class FileManageUpdateController extends BaseController {

    @Autowired
    private DingFileManageService dingFileManageService;
    @Autowired
    private DingCommentServiceImpl commentService;
    @Autowired
    private AppManageUtil appManageUtil;

    /**
     * sendflag=1 表示发送消息通知
     */
    @PostMapping("/upload")
    @ApiOperation(value = "文件上传")
    public BaseResult upload(@RequestBody DingFileUpload dingFileUploadList, String sendflag) {
        DateFormat df3 = DateFormat.getDateInstance(DateFormat.MEDIUM, Locale.CHINA);
        DateFormat df7 = DateFormat.getTimeInstance(DateFormat.MEDIUM, Locale.CHINA);
        Date now = new Date();
        String date3 = df3.format(now);
        String time3 = df7.format(now);
        //  log.info("filesize:"+baseFileList.getFileList().size());
        if (dingFileUploadList.getFileList().size() == 0) {
            return BaseResult.fail("上传文件不能为空！");
        }
        if (dingFileUploadList.getReceiverList().size() == 0) {
            return BaseResult.fail("接收人不能为空！");
        }
        if (BlankUtil.isBlank(dingFileUploadList.getCreaterid())) {
            return BaseResult.fail("创建人信息不能为空！");
        }
        //保存钉盘的文件信息
        Integer msg = dingFileManageService.saveDingFiles(dingFileUploadList);
        //发送消息通知
        if (StringUtils.isNotBlank(sendflag) && sendflag.equals("1")) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //获取accessToken
            String accessToken = null;
            try {
                accessToken = AuthHelper.getAccessToken(DingEVN.AppKey, DingEVN.AppSecret);
                log.info("获取accessToken成功, --> {}", accessToken);
            } catch (Exception e) {
                log.error("获取accessToken失败! --> {} ", e);
            }
            String filenames = String.join(",", dingFileUploadList.getFileList().stream().map(dingFileVo -> dingFileVo.getFileName()).collect(Collectors.toList()));
            //根据adminid查出receiverid
            List<String> ids = dingFileManageService.getReceiverId(msg);
            String receiverids = String.join(",", ids);
            //组装DingWorkMsgInfo
            DingWorkMsgInfo dingWorkMsgInfo = new DingWorkMsgInfo();
            dingWorkMsgInfo.setUsername(dingFileUploadList.getCreater());
            dingWorkMsgInfo.setTime(date3 + " " + time3);
            //现在前端creater中已经包括单位名称
//            dingWorkMsgInfo.setDepartments(dingFileUploadList.getDepartment());
            dingWorkMsgInfo.setFilenames(filenames);
            dingWorkMsgInfo.setModule("要情专报");
            dingWorkMsgInfo.setMessageUrl(dingFileUploadList.getMessageUrl());
            Long taskId = appManageUtil.sendWorkMsgUpdate(accessToken, DingEVN.AgentId, receiverids, null, dingWorkMsgInfo);
//            dingDingManageUtil.sendWorkMsg(accessToken, DingEVN.AgentId, receiverids,null, filenames);

            if (taskId != null) {
                log.info("任务id---->{}", taskId);
                dingFileManageService.updateTaskId(msg, taskId.toString());
            }
        }
        log.info("文件保存成功! 消息通知发送成功!");
        return BaseResult.ok("保存成功");
    }

    @PostMapping("/uploadsingle")
    @ApiOperation(value = "将上传的文件发送给钉钉用户")
    @Deprecated
    public BaseResult uploadSingleAndToUser(@RequestBody DingFileUploadUpdate dingFileUploadList) {
        //  log.info("filesize:"+baseFileList.getFileList().size());
        if (dingFileUploadList.getFileList().size() == 0) {
            return BaseResult.fail("上传文件不能为空！");
        }
        if (dingFileUploadList.getReceiverList().size() == 0) {
            return BaseResult.fail("接收人不能为空！");
        }
        if (BlankUtil.isBlank(dingFileUploadList.getCreaterid())) {
            return BaseResult.fail("创建人信息不能为空！");
        }
        //1.获取code
        String code = dingFileUploadList.getCode();
        String userid = dingFileUploadList.getCreaterid();
        String accessToken = "";
        try {
            accessToken = AuthHelper.getAccessToken(DingEVN.AppKey, DingEVN.AppSecret);
            log.info("免登陆码为 --> {}, accessToken --> {}, userId --> {}", JSONObject.toJSONString(code), JSONObject.toJSONString(accessToken), JSONObject.toJSONString(userid));
        } catch (Exception e) {
            log.error("获取用户信息失败! --> {}", e);
            return BaseResult.fail("获取用户信息失败!");
        }
        try {
            String s = dingFileManageService.sendDingFilesToUser(dingFileUploadList, accessToken, userid, DingEVN.AgentId, code);
            //钉盘文件上传
            return BaseResult.ok(s);
        } catch (IOException e) {
            e.printStackTrace();
            return BaseResult.fail("文件上传失败!");
        }
    }

    @GetMapping("/download")
    @ApiOperation(value = "文件预览调用接口")
    public BaseResult downLoad(String id, String userid) throws Exception {
        if (BlankUtil.isBlank(id)) {
            return BaseResult.fail("id为空!");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        DingFileManage fileManange = dingFileManageService.getById(id);
        if (fileManange == null) {
            return BaseResult.fail("没有此文件!");
        }
        //标记已读
        dingFileManageService.updateRead(id, loginUser.getUserid().toString());
        return BaseResult.ok();
    }

    @PassToken
    @GetMapping("/receivelist")
    @ApiOperation(value = "收到的文件列表")
    public BaseResult filelist(String userid) throws Exception {
        List<DingFileManageVo> fileManageVos = new ArrayList<>();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isNotEmpty(loginUser)) {
            fileManageVos = dingFileManageService.getList(loginUser.getUserid().toString());
        }
        return BaseResult.ok(fileManageVos);
    }

    @GetMapping("/sendlist")
    @ApiOperation(value = "发送的文件列表")
    public BaseResult sendlist(String userid) throws Exception {
        if (BlankUtil.isBlank(userid)) {
            return BaseResult.fail("userid参数为空!");
        }
        List<DingFileAdminVo> fileAdminVos = new ArrayList<>();
        //查询文件消息主表
//        List<DingFileAdmin> fileAdmins = new ArrayList<>();
//        List<DingFileManage> baseFileList = null;
//        List<String> receiverList = null;
//        fileAdmins = dingFileManageService.getFileAdmin(userid);
//        for (DingFileAdmin fileAdmin : fileAdmins) {
//            DingFileAdminVo fileAdminVo = new DingFileAdminVo();
//            fileAdminVo.setId(fileAdmin.getId());
//            fileAdminVo.setCreatetime(fileAdmin.getCreatetime());
//            fileAdminVo.setIswrite(null != fileAdmin.getIswrite() ? fileAdmin.getIswrite() : 0);
//            //查询文件附件表
//            baseFileList = new ArrayList<>();
//            baseFileList = dingFileManageService.getFileManange(fileAdmin.getId());
//            //fileManangeVo.setFileList(new ArrayList<>());
//            fileAdminVo.setFileList(baseFileList);
//            //查询接收人表
//            receiverList = new ArrayList<>();
//            receiverList = dingFileManageService.getFileReceiver(fileAdmin.getId());
//            fileAdminVo.setReceiverList(receiverList);
//            fileAdminVos.add(fileAdminVo);
//        }
        fileAdminVos = dingFileManageService.getSendListByUserId(userid, null, null, null, null);
        return BaseResult.ok(fileAdminVos);
//        log.info("查询发送的文件列表 查询条件为 userid->{}",userid);
//        List<DingFileManageVo> queryListByCreaterId = dingFileManageService.getQueryListByCreaterId(userid, null,null,null,null);
//        return BaseResult.ok(queryListByCreaterId);
    }

    @GetMapping("/deletefile")
    @ApiOperation(value = "删除")
    public BaseResult deletefile(String id) throws Exception {
        if (BlankUtil.isBlank(id)) {
            return BaseResult.fail("id为空!");
        }

        //查询消息
        DingFileAdmin fileAdmin = dingFileManageService.getFileAdminById(Integer.valueOf(id));
        if (BlankUtil.isBlank(fileAdmin)) {
            return BaseResult.fail("无此信息!");
        }
        List<DingFileManage> fileMananges = dingFileManageService.getFileManange(Integer.valueOf(id));
        if (CollectionUtils.isEmpty(fileMananges)) {
            return BaseResult.fail("没有此文件!");
        }
        dingFileManageService.deleteFileAllInfo(id);
        return BaseResult.ok("操作成功!");
    }

    @GetMapping("/receivelistUpdate")
    @ApiOperation(value = "根据发送人/时间段/文件名等条件查询收到的文件列表")
    public BaseResult receivelistUpdate(String userid, String creater, String startdate, String enddate, String filename) throws Exception {
        if (BlankUtil.isBlank(userid)) {
            return BaseResult.fail("userid参数为空!");
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMddHHmmss");
        if (StringUtils.isNotBlank(startdate)) {
            startdate = startdate + "000000";
            startdate = sdf.format(sdf2.parse(startdate));
        }
        if (StringUtils.isNotBlank(enddate)) {
//            enddate = enddate+"235959";
            enddate = enddate + "000000";
            enddate = sdf.format(sdf2.parse(enddate));
        }
        log.info("查询收到的文件列表 查询条件为 userid->{}, creater->{}, startdate->{}, enddate->{}, filename->{}", JSONObject.toJSONString(userid), JSONObject.toJSONString(creater), JSONObject.toJSONString(startdate), JSONObject.toJSONString(enddate), JSONObject.toJSONString(filename));
        try {
            List<DingFileManageVo> fileManageVos = dingFileManageService.getQueryListByRecevierId(userid, creater, startdate, enddate, filename);
            return BaseResult.ok(fileManageVos);
        } catch (Exception e) {
            log.error("要情专报_根据发送人/时间段/文件名等条件查询收到的文件列表报错:" + e.getMessage());
            return BaseResult.fail("您的操作数有误,请重新操作!");
        }
    }

    @GetMapping("/sendlistUpdate")
    @ApiOperation(value = "根据收件人/时间段/文件名等查询发送的文件列表")
    public BaseResult sendlistUpdate(String userid, String receiver, String startdate, String enddate, String filename) throws Exception {
        if (BlankUtil.isBlank(userid)) {
            return BaseResult.fail("userid参数为空!");
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMddHHmmss");
        if (StringUtils.isNotBlank(startdate)) {
            startdate = startdate + "000000";
            startdate = sdf.format(sdf2.parse(startdate));
        }
        if (StringUtils.isNotBlank(enddate)) {
//            enddate = enddate+"235959";
            enddate = enddate + "000000";
            enddate = sdf.format(sdf2.parse(enddate));
        }
        log.info("查询发送的文件列表 查询条件为 userid->{}, receiver->{}, startdate->{}, enddate->{}, filename->{}", JSONObject.toJSONString(userid), JSONObject.toJSONString(receiver), JSONObject.toJSONString(startdate), JSONObject.toJSONString(enddate), JSONObject.toJSONString(filename));
        List<DingFileAdminVo> fileAdminVos = new ArrayList<>();
        //查询文件消息主表
//        List<DingFileAdmin> fileAdmins = new ArrayList<>();
        try {
//            DingFileAdminVo fileAdminVo = new DingFileAdminVo();
//            List<DingFileManage> baseFileList = null;
//            List<String> receiverList = null;
//            fileAdmins = dingFileManageService.getQueryListByCreaterId(userid, receiver, startdate, enddate, filename);
//            for (DingFileAdmin fileAdmin : fileAdmins) {
//                fileAdminVo.setId(fileAdmin.getId());
//                fileAdminVo.setCreatetime(fileAdmin.getCreatetime());
//                fileAdminVo.setIswrite(fileAdmin.getIswrite());
//                //查询文件附件表
//                baseFileList = new ArrayList<>();
//                baseFileList = dingFileManageService.getFileManange(fileAdmin.getId());
//                //fileManangeVo.setFileList(new ArrayList<>());
//                fileAdminVo.setFileList(baseFileList);
//                //查询接收人表
//                receiverList = new ArrayList<>();
//                receiverList = dingFileManageService.getFileReceiver(fileAdmin.getId());
//                fileAdminVo.setReceiverList(receiverList);
//                fileAdminVos.add(fileAdminVo);
//            }
            fileAdminVos = dingFileManageService.getSendListByUserId(userid, receiver, startdate, enddate, filename);
            return BaseResult.ok(fileAdminVos);
        } catch (Exception e) {
            log.error("要情专报_根据发送人/时间段/文件名等条件查询发送的文件列表报错:" + e.getMessage());
            return BaseResult.fail("您的操作数有误,请重新操作!");
        }
    }


    @PostMapping("/sendcomment")
    @ApiOperation(value = "发送审批内容")
    public BaseResult sendComment(@RequestBody CommentInfo commentInfo) {
        if (BlankUtil.isBlank(commentInfo)) {
            return BaseResult.fail("commentInfo 参数为空!");
        }
        try {
            if (commentInfo.getCommentcontent().length() > 100) {
                return BaseResult.fail("批示意见过长，输入范围在100以内");
            }
            commentService.sendComment(commentInfo);
            return BaseResult.ok();
        } catch (Exception e) {
            e.printStackTrace();
            log.info("发送审批内容失败!--> {}", e);
            return BaseResult.fail("审批失败!");
        }
    }

    @GetMapping("/sendercheckcomment")
    @ApiOperation(value = "已发送查看批示列表,并展示内容")
    public BaseResult checkCommentBySender(Integer adminid) {
        if (null == adminid) {
            return BaseResult.fail("adminid 参数为空!");
        }
        try {
            List<DingComment> dingComments = commentService.checkCommentBySender(adminid);
            if (CollectionUtils.isNotEmpty(dingComments)) {
                return BaseResult.ok(dingComments);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResult.ok("尚未批示!");
    }

    @GetMapping("/filecheckcomment")
    @ApiOperation(value = "指定文件查看批示列表,并展示内容")
    public BaseResult checkCommentByFile(Integer filemanageid) {
        if (null == filemanageid) {
            return BaseResult.fail("filemanageid 参数为空!");
        }
        try {
            List<DingComment> dingComments = commentService.checkCommentByFile(filemanageid);
            if (CollectionUtils.isNotEmpty(dingComments)) {
                return BaseResult.ok(dingComments);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResult.ok("尚未批示!");
    }

    @GetMapping("/checkcomment")
    @ApiOperation(value = "查看已批示内容")
    public BaseResult checkCommentByReceiver(Integer filemanageid, String receiverid) {
        if (null == filemanageid || StringUtils.isBlank(receiverid)) {
            return BaseResult.fail("参数为空!");
        }
        try {
            DingComment dingComment = commentService.checkCommentByReceiver(filemanageid, receiverid);
            return BaseResult.ok(dingComment);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResult.ok("尚未批示!");
    }

    @GetMapping("/deleterecorde")
    @ApiOperation(value = "删除已发送记录")
    public BaseResult deleteRecorde(String id) {
        if (BlankUtil.isBlank(id)) {
            return BaseResult.fail("id为空!");
        }
        try {
            Integer ids = Integer.valueOf(id);
            //查询消息
            DingFileAdmin fileAdmin = dingFileManageService.getFileAdminById(ids);
            if (BlankUtil.isBlank(fileAdmin)) {
                return BaseResult.fail("无此信息!");
            }
            dingFileManageService.deleteRecorde(ids);
            return BaseResult.ok("操作成功!");
        } catch (Exception e) {
            return BaseResult.fail("操作失败!");
        }
    }

    @GetMapping("/getImportantReportPageList")
    @ApiOperation(value = "要情专报移动端查询接口")
    public TableDataInfo getReportList(@RequestParam(name = "title", required = false) String title,
                                       @RequestParam(name = "isAudit", required = false) Integer isAudit,
                                       @RequestParam(name = "pageNum", defaultValue = "1") Integer page,
                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer size,
                                       @RequestParam(name = "userId", required = true) String userId) {
        startPage();
        List<ImportantReportManage> pageList = new ArrayList<>();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isNotEmpty(loginUser)) {
            pageList = dingFileManageService.getReportList(title, isAudit, loginUser.getUserid().toString());
        }
        return getDataTable(pageList,true);
    }

    @GetMapping("/getImportantReportInfoById")
    @ApiOperation(value = "要情专报详情")
    public BaseResult getImportantReportInfoById(@RequestParam(name = "id", required = true) String id) {
        return BaseResult.ok(dingFileManageService.getReport(id),true);
    }

    @PostMapping("/auditImportantReportById")
    @ApiOperation(value = "要情专报批示接口")
    public BaseResult auditImportant(@RequestBody AuditImportantVo auditImportantVo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)) {
            BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        auditImportantVo.setUserId(loginUser.getUserid().toString());
        auditImportantVo.setUserName(loginUser.getSysUser().getNickName());
        return dingFileManageService.auditImportant(auditImportantVo);
    }



    @GetMapping("/getAuditImportantReportListById")
    @ApiOperation(value = "获取要情专报下的批示信息")
    public BaseResult getAuditImportantReportListById(@RequestParam(name = "id", required = true) String id) {
        return BaseResult.ok(dingFileManageService.getAuditImportantReportListById(id));
    }
}
