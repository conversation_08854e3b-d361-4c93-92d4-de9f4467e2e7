package com.onecity.os.data.noticeimportant.dao;

import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.data.noticeimportant.entity.po.DingComment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


import java.util.List;

@Mapper
public interface DingCommentMapper extends BaseMapper<DingComment> {

    void save(DingComment dingComment);

    void updateFileadminIsWrite(Integer adminid);

    void updateFilemanageIsWrite(Integer filemanageid);

    void updateIsWriteAndRead(Integer fileid, String receiverid);

    DingComment selectByReceiverid(@Param("filemanageid") Integer filemanageid, @Param("receiverid") String DingCommentreceiverid);

    List<DingComment> selectBySender(Integer adminid);

    List<DingComment> selectByFile(Integer filemanageid);
}
