package com.onecity.os.data.noticeimportant.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "important_report_manage")
public class ImportantReportManage {
    /**
     * 主键id
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Long id;

    /**
     * ip地址
     */
    @Transient
    private String host;

    /**
     * 标题
     */
    private String title;

    /**
     * 发布来源
     */
    @Column(name = "public_source")
    private String publicSource;

    /**
     * 发送时间
     */
    @Column(name = "send_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 文件名
     */
    @Column(name = "file_name")
    private String fileName;

    /**
     * 文件路径
     */
    @Column(name = "file_path")
    private String filePath;

    /**
     * 状态(0-草稿;1-已发布;2已撤回)
     */
    private Byte status;

    /**
     * 是否已读 0-否;1-是
     */
    @Column(name = "is_read")
    private Byte isRead;

    /**
     * 阅读次数
     */
    @Column(name = "read_count")
    private Integer readCount;

    /**
     * 是否删除 0-否;1-是
     */
    @Column(name = "is_delete")
    private Byte isDelete;

    /**
     * 创建人
     */
    private String creater;
    /**
     * 创建人userid
     */
    @Column(name = "create_id")
    private String createId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 接收人ids
     */
    @Column(name = "receive_user_id")
    private String receiveUserId;

    /**
     * 批示人的userIds
     */
    @Column(name = "audit_user_ids")
    private String auditUserIds;

    /**
     * 专报内容
     */
    private String content;

    @Transient
    private String sendUser;
}