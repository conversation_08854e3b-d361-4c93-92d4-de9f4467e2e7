package com.onecity.os.data.noticeimportant.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.data.indicator.app.AppManageUtil;
import com.onecity.os.data.indicator.model.vo.dingmaillist.DingFileVo;
import com.onecity.os.data.indicator.util.Base64Util;
import com.onecity.os.data.noticeimportant.dao.*;
import com.onecity.os.data.noticeimportant.entity.ImportantReportAudit;
import com.onecity.os.data.noticeimportant.entity.ImportantReportManage;
import com.onecity.os.data.noticeimportant.entity.ImportantReportManageDTO;
import com.onecity.os.data.noticeimportant.entity.po.DingFileAdmin;
import com.onecity.os.data.noticeimportant.entity.po.DingFileManage;
import com.onecity.os.data.noticeimportant.entity.po.DingFileReceiver;
import com.onecity.os.data.noticeimportant.entity.vo.*;
import com.onecity.os.data.noticeimportant.feign.MessageFeignService;
import com.onecity.os.data.noticeimportant.feign.RemindInfoDto;
import com.onecity.os.data.noticeimportant.service.DingFileManageService;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class DingFileManageServiceImpl implements DingFileManageService {

    @Autowired
    private DingFileManageMapper dingFileManageMapper;
    @Autowired
    private DingFileReceiverMapper dingFileReceiverMapper;
    @Autowired
    private DingFileAdminMapper dingFileAdminMapper;
    @Autowired
    private AppManageUtil appManageUtil;
    @Resource
    private ImportantReportManageMapper importantReportManageMapper;
    @Resource
    private ImportantReportAuditMapper importantReportAuditMapper;

    @Resource
    private MessageFeignService messageFeignService;

    @Override
    public List<DingFileManageVo> getList(String userid) {
        return dingFileManageMapper.selectListByUserid(userid);
    }

    @Override
    public List<DingFileManageVo> getQueryListByRecevierId(String userid, String creater, String startdate, String enddate, String filename) {
        return dingFileManageMapper.selectQueryListByRecevierId(userid, creater, startdate, enddate, filename);
    }

    @Override
    public List<DingFileAdmin> getQueryListByCreaterId(String userid, String receiver, String startdate, String enddate, String filename) {
        return dingFileManageMapper.selectQueryListByCreaterId(userid, receiver, startdate, enddate, filename);
    }

    @Override
    public List<String> getReceiverId(Integer id) {
        return dingFileReceiverMapper.getReceiverId(id);
    }

    @Override
    public DingFileManage getById(String id) {
        return dingFileManageMapper.selectById(id);
    }

    @Override
    public void deleteById(String id) {
        dingFileManageMapper.deleteByIds(id);
    }

    @Override
    public List<DingFileAdmin> getSendList(String userid) {
        DingFileAdmin dingFileAdmin = new DingFileAdmin();
        dingFileAdmin.setCreaterid(userid);
        return dingFileAdminMapper.select(dingFileAdmin);
    }

    @Override
    public Integer saveDingFiles(DingFileUpload fileUploadList){
//        //保存基本消息
//        DingFileAdmin fileAdmin=new DingFileAdmin();
//        Calendar cal = Calendar.getInstance();
//        Date now = cal.getTime();
//        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        fileAdmin.setCreater(fileUploadList.getCreater());
//        if(StringUtils.isNotBlank(fileUploadList.getDepartment())) fileAdmin.setDepartment(fileUploadList.getDepartment());
//        fileAdmin.setCreaterid(fileUploadList.getCreaterid());
//        fileAdmin.setCreatetime(format.format(now));
//        fileAdmin.setTheme("无");
//        dingFileAdminMapper.save(fileAdmin);
        //保存附件/接收人信息
        DingFileAdmin fileAdmin = null;
        for(DingFileVo baseFile: fileUploadList.getFileList()){
            //保存基本消息
            fileAdmin=new DingFileAdmin();
            Calendar cal = Calendar.getInstance();
            Date now = cal.getTime();
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            fileAdmin.setCreater(fileUploadList.getCreater());
            if(StringUtils.isNotBlank(fileUploadList.getDepartment())) {
                fileAdmin.setDepartment(fileUploadList.getDepartment());
            }
            fileAdmin.setCreaterid(fileUploadList.getCreaterid());
            fileAdmin.setCreatetime(format.format(now));
            fileAdmin.setTheme("无");
            dingFileAdminMapper.save(fileAdmin);
            DingFileManage dingfileManage =new DingFileManage();
            dingfileManage.setAdminid(fileAdmin.getId());
            dingfileManage.setSpaceId(baseFile.getSpaceId());
            dingfileManage.setFileId(baseFile.getFileId());
            dingfileManage.setFileName(baseFile.getFileName());
            dingfileManage.setFileType(baseFile.getFileType());
            dingfileManage.setFileSize(baseFile.getFileSize());
            dingFileManageMapper.save(dingfileManage);
            for (DingFileUpload.ReceiverListBean receiver: fileUploadList.getReceiverList()){
                DingFileReceiver fileReceiver=new DingFileReceiver();
                fileReceiver.setFileid(dingfileManage.getId());
                fileReceiver.setReceiverid(receiver.getReceiverid());
                fileReceiver.setReceiver(receiver.getReceiver());
                dingFileReceiverMapper.insert(fileReceiver);
            }
        }
        return fileAdmin.getId();
    }

    @Override
    public List<DingFileAdmin> getFileAdmin(String userid) {
        return dingFileAdminMapper.getFileAdmin(userid);
//        QueryWrapper<DingFileAdmin> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("createrid",userid);
//
//        return dingFileAdminMapper.selectList(queryWrapper);
    }

    @Override
    public List<DingFileManage> getFileManange(Integer id) {
        return dingFileManageMapper.selectListByAdminid(id);
    }

    @Override
    public List<String> getFileReceiver(Integer id) {
        return dingFileReceiverMapper.getRecervers(id);
    }

    @Override
    public void updateRead(String id, String userid) {
        dingFileReceiverMapper.updateRead(id,userid);
    }

    @Override
    public DingFileAdmin getFileAdminById(Integer id) {
        return dingFileAdminMapper.getFileAdminById(id);
    }

    @Override
    public void deleteFileAllInfo(String id) {
        dingFileReceiverMapper.deleteByAdminid(id);
        dingFileManageMapper.deleteByAdmin(id);
        dingFileAdminMapper.deleteByIds(id);
    }

    @Override
    public String sendDingFilesToUser(DingFileUploadUpdate fileUploadList, String accessToken, String userid, String agent_id, String code) throws IOException {
        //保存基本消息
        DingFileAdmin fileAdmin=new DingFileAdmin();
        Calendar cal = Calendar.getInstance();
        Date now = cal.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        fileAdmin.setCreater(fileUploadList.getCreater());
        if(StringUtils.isNotBlank(fileUploadList.getDepartment())) {
            fileAdmin.setDepartment(fileUploadList.getDepartment());
        }
        fileAdmin.setCreaterid(fileUploadList.getCreaterid());
        fileAdmin.setCreatetime(format.format(now));
        fileAdmin.setTheme("无");
        dingFileAdminMapper.save(fileAdmin);
        //保存附件/接收人信息
        for (DingFileUploadUpdate.FileListBean baseFile: fileUploadList.getFileList()){
            MultipartFile multipartFile = Base64Util.base64ToMultipart(baseFile.getContent());
            String originalFilename = baseFile.getName();
            log.info("originalFilename -->{}",JSONObject.toJSONString(originalFilename));
            // 文件扩展名
            String ext = originalFilename.substring(originalFilename.lastIndexOf(".")).trim();
            if (multipartFile.isEmpty()) {
                return "请选择图片！";
            }
            //避免文件名重复使用uuid来避免,产生一个随机的uuid字符
            String realFileName= UUID.randomUUID().toString();
            String url = "";
            try {
                JSONObject jsonObject = appManageUtil.uploadDingFile(accessToken, agent_id, multipartFile);
                url = jsonObject.getString("media_id");
                List<String> receiverIds = fileUploadList.getReceiverList().stream().map(DingFileUploadUpdate.ReceiverListBean::getReceiverid).collect(Collectors.toList());
                DingFileVo dingFileVo = appManageUtil.sendDingFileToUser(userid, code, accessToken, agent_id, receiverIds, url, originalFilename);
                DingFileManage dingfileManage =new DingFileManage();
                dingfileManage.setAdminid(fileAdmin.getId());
                dingfileManage.setSpaceId(dingFileVo.getSpaceId());
                dingfileManage.setFileId(dingFileVo.getFileId());
                dingfileManage.setFileName(dingFileVo.getFileName());
                dingfileManage.setFileType(dingFileVo.getFileType());
                dingfileManage.setFileSize(dingFileVo.getFileSize());
                dingFileManageMapper.save(dingfileManage);
                for (DingFileUploadUpdate.ReceiverListBean receiver : fileUploadList.getReceiverList()) {
                    DingFileReceiver fileReceiver=new DingFileReceiver();
                    fileReceiver.setFileid(dingfileManage.getId());
                    fileReceiver.setReceiverid(receiver.getReceiverid());
                    fileReceiver.setReceiver(receiver.getReceiver());
                    dingFileReceiverMapper.insert(fileReceiver);
                }
            } catch (Exception e) {
                log.error("ding upload error ! --> {} ", e);
                return "钉钉文件上传失败!";
            }
        }
        return fileAdmin.getId()+"";
    }

    /**
    * 逻辑删除已发送数据
    * @param id
    * @return void
    */
    @Override
    public void deleteRecorde(Integer id) {
        dingFileAdminMapper.deleteRecorde(id);
    }

    @Override
    public void updateTaskId(Integer id, String taskId) {
        dingFileAdminMapper.updateTaskId(id, taskId);
    }

    @Override
    public List<DingFileAdminVo> getSendListByUserId(String userid, String receiver, String startdate, String enddate, String filename) {
        return dingFileManageMapper.getSendListByUserId(userid, receiver, startdate, enddate, filename);
    }

    @Override
    public List<ImportantReportManage> getReportList(String title, Integer isAudit, String userId) {
        List<ImportantReportManage> pageList = new ArrayList<>();
        if (1 == isAudit) {
            pageList = dingFileManageMapper.getReportList(title, isAudit, userId);
        } else {
            pageList = dingFileManageMapper.getReportList(title, null, userId);
        }
        List<ImportantReportManageDTO> list = new ArrayList<>();
        if(pageList != null && pageList.size() > 0){
            //判断该用户是否已读,分页数据，数量不大，所以采用循环处理
            pageList.forEach(msg -> {
                List<ImportantReportAudit> audits = dingFileManageMapper.getAuditMsg(null, msg.getId());
                if (CollectionUtils.isEmpty(audits)) {
                    msg.setIsRead((byte) 0);
                } else {
                    msg.setIsRead((byte) 1);
                }
                ImportantReportManageDTO dto = new ImportantReportManageDTO();
                BeanUtils.copyProperties(msg,dto);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                if(msg!=null && msg.getSendTime()!=null){
                    dto.setSendTime(simpleDateFormat.format(msg.getSendTime()));
                }
                list.add(dto);
            });
        }
        return pageList;
    }

    @Override
    public ImportantReportManage getReport(String id) {
        //统计阅读次数
        ImportantReportManage parem = new ImportantReportManage();
        parem.setId(Long.parseLong(id));
        ImportantReportManage reportManage = importantReportManageMapper.selectOne(parem);
        reportManage.setReadCount(reportManage.getReadCount() + 1);
        importantReportManageMapper.updateByPrimaryKeySelective(reportManage);
        return reportManage;
    }

    @Override
    public BaseResult auditImportant(AuditImportantVo auditImportantVo) {
        ImportantReportAudit audit = new ImportantReportAudit();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isNotEmpty(loginUser)){
            audit.setAuditUserId(loginUser.getUserid().toString());
            audit.setCreater(loginUser.getSysUser().getNickName());
        }
        audit.setImportantReportId(auditImportantVo.getId());
        log.info("要情专报批示：：：" + JSONObject.toJSONString(auditImportantVo.getAuditView()) + " time =" + JSONObject.toJSONString(new SimpleDateFormat("yyyy-MM-dd HH:mm:Ss").format(new Date())));
        audit.setAuditTime(new Date());
        audit.setAuditView(auditImportantVo.getAuditView());
        audit.setCreateTime(new Date());
        importantReportAuditMapper.insert(audit);
        //调整要情专报头数据，记录批示人id
        ImportantReportManage reportMsg = importantReportManageMapper.selectByPrimaryKey(auditImportantVo.getId());
        reportMsg.setIsRead((byte) 1);
        if (StringUtils.isNotEmpty(reportMsg.getAuditUserIds())) {
            reportMsg.setAuditUserIds(reportMsg.getAuditUserIds() + "," + auditImportantVo.getUserId());
        } else {
            reportMsg.setAuditUserIds(auditImportantVo.getUserId());
        }
        importantReportManageMapper.updateByPrimaryKeySelective(reportMsg);

        sendMessage(auditImportantVo,reportMsg);
        // 删除消息
        messageFeignService.deleteMessageByServiceAndUserId(auditImportantVo.getId(), "yqzb", String.valueOf(SecurityUtils.getLoginUser().getUserid()), SecurityUtils.getLoginUser().getUsername());

        return BaseResult.ok();
    }

    private void sendMessage(AuditImportantVo auditImportantVo,ImportantReportManage reportMsg) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        RemindInfoDto remindInfoDto = new RemindInfoDto();
        remindInfoDto.setCreater(auditImportantVo.getUserId());
        remindInfoDto.setCreateTime(simpleDateFormat.format(new Date()));
        remindInfoDto.setIsDelete(Byte.parseByte("0"));
        remindInfoDto.setIsRead(Byte.parseByte("0"));
        remindInfoDto.setLevel("2");
        remindInfoDto.setPcUserId(reportMsg.getCreateId());
        remindInfoDto.setRemindContent("您发起的"+ reportMsg.getTitle() +"已批示，请及时查收。");
        remindInfoDto.setRemindedCount(Byte.parseByte("1"));
        remindInfoDto.setRemindedType(Byte.parseByte("0"));
        remindInfoDto.setRemindTitle("【要情专报】批示消息");
        remindInfoDto.setSourceId(null);
        remindInfoDto.setSourceType("PC");
        remindInfoDto.setUpdater(auditImportantVo.getUserName());
        remindInfoDto.setUpdateTime(simpleDateFormat.format(new Date()));
        List<RemindInfoDto> remindInfoDtoList = new ArrayList<>();
        remindInfoDtoList.add(remindInfoDto);
        log.info("要请专报批示后发送消息*************" + JSON.toJSONString(remindInfoDtoList));
        messageFeignService.addMsg(remindInfoDtoList);
    }

    @Override
    public List<ImportantReportAudit> getAuditImportantReportListById(String importantId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<ImportantReportAudit> audits = importantReportAuditMapper.getList(importantId,loginUser.getUserid().toString());
        audits.forEach(audit -> audit.setAuditUser(audit.getCreater()));
        return audits;
    }
}
