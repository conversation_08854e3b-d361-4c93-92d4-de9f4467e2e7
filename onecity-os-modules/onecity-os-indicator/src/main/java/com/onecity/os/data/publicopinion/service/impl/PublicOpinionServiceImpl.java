package com.onecity.os.data.publicopinion.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.data.publicopinion.entity.PublicOpinion;
import com.onecity.os.data.publicopinion.mapper.PublicOpinionMapper;
import com.onecity.os.data.publicopinion.service.PublicOpinionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/16 上午9:25
 */
@Slf4j
@Service
public class PublicOpinionServiceImpl implements PublicOpinionService {

    @Resource
    private PublicOpinionMapper publicOpinionMapper;

    /**
     * 获取token的url地址
     */
    @Value("${orderInfo.token.url}")
    private String tokenUrl;

    /**
     * 获取token的用户名
     */
    @Value("${orderInfo.token.userName}")
    private String tokenUserName;

    /**
     * 获取token的用户密码
     */
    @Value("${orderInfo.token.passWord}")
    private String tokenPassWord;

    /**
     * 获取办结结果满意度的url
     */
    @Value("${orderInfo.satisfaction.url}")
    private String satisfactionUrl;

    /**
     * 获取工单占比的url
     */
    @Value("${orderInfo.proportionOfOrders.url}")
    private String proportionOfOrdersUrl;

    /**
     * 工单办理情况、办结情况、逾期情况的url
     */
    @Value("${orderInfo.workOrderProcessing.url}")
    private String workOrderProcessingUrl;

    /**
     * 各类工单的办理量和受理量的url
     */
    @Value("${orderInfo.settlementAmount.url}")
    private String settlementAmountUrl;

    /**
     * 工单状况、热点问题、工单监察的url
     */
    @Value("${orderInfo.workOrderStatusRoot.url}")
    private String workOrderStatusRootUrl;

    /**
     * 综合办理趋势的url
     */
    @Value("${orderInfo.handlingTrends.url}")
    private String handlingTrendsUrl;

    /**
     * 办结率排名TOP5、满意度TOP10、按时办结单位TOP10、逾期办结单位TOP10的url
     */
    @Value("${orderInfo.undertakingSituationAnalysis.url}")
    private String undertakingSituationAnalysisUrl;

    /**
     * 承办单位数量接口
     */
    @Value("${orderInfo.numberContractors.url}")
    private String numberContractorsUrl;

    @Override
    public List<PublicOpinion> getPublicOpinionMenu() {
        // 查找顶层菜单
        PublicOpinion publicOpinion = new PublicOpinion();
        publicOpinion.setLevel(1);
        publicOpinion.setParentId(0L);
        publicOpinion.setIsDelete(0);
        List<PublicOpinion> publicOpinionParent = new ArrayList<>();
        publicOpinionParent = publicOpinionMapper.select(publicOpinion);
        return publicOpinionParent;
    }

    @Override
    public List<PublicOpinion> getPublicOpinionMenuByParentId(String id) {
        List<PublicOpinion> childs = new ArrayList<>();
        PublicOpinion publicOpinion = new PublicOpinion();
        publicOpinion.setParentId(Long.valueOf(id));
        publicOpinion.setIsDelete(0);
        childs = publicOpinionMapper.select(publicOpinion);
        if (childs != null && childs.size() > 0) {
            for (PublicOpinion child : childs) {
                getChildPublicOpinion(child);
            }
        }
        return childs;
    }

    /**
     * 获取社情舆情子集菜单
     *
     * @param publicOpinion
     */
    private void getChildPublicOpinion(PublicOpinion publicOpinion) {
        PublicOpinion publicOpinions = new PublicOpinion();
        publicOpinions.setParentId(publicOpinion.getId());
        publicOpinions.setIsDelete(0);
        List<PublicOpinion> childs = publicOpinionMapper.select(publicOpinions);
        if (childs != null && childs.size() > 0) {
            publicOpinion.setChildPublicOpinions(childs);
            for (PublicOpinion child : childs) {
                getChildPublicOpinion(child);
            }
        }
    }

    /**
     * 调用三方接口获取社情舆情登录的token
     *
     * @return
     * @throws Exception
     */
    private String getOrderInfoToken() throws Exception {
        HttpClient client = HttpClients.createDefault();
        // 调用三方接口
        HttpPost post = new HttpPost(tokenUrl);
        post.addHeader("Content-Type", "application/json;charset=UTF-8");
        // 设置传输超时时间
        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000).build();
        post.setConfig(requestConfig);
        JSONObject date = new JSONObject();
        date.put("userName", tokenUserName);
        date.put("passWord", tokenPassWord);
        log.info("社情舆情---调用第三方登录接口,发送的请求体:" + date);
        StringEntity s = new StringEntity(date.toString(), ContentType.create("text/json", "UTF-8"));
        post.setEntity(s);
        log.info("社情舆情---调用第三方登录接口,发送的请求:" + post);
        try {
            HttpResponse res = client.execute(post);
            String jsData = EntityUtils.toString(res.getEntity());
            String result1 = JSONObject.parseObject(jsData).getString("code");
            // 调取结果失败,返回null
            if (!"1".equals(result1)) {
                log.error("社情舆情---调用第三方登录接口,获取token失败---" + jsData);
                return null;
            }
            return JSONObject.parseObject(jsData).getString("token");
        } catch (Exception e) {
            log.error("社情舆情---调用第三方登录接口,获取token失败");
            throw new Exception("社情舆情---调用第三方登录接口,获取token失败");
        }
    }

    /**
     * 调用三方接口获取社情舆情数据
     *
     * @param date
     * @param token
     * @param url
     * @return
     */
    private JSONObject getOutsideData(String date, String token, String url) throws Exception {
        HttpClient client = HttpClients.createDefault();
        // 调用三方接口
        HttpGet get = new HttpGet(url + "/" + date);
        get.setHeader("Authorization", "Bearer " + token);
        get.addHeader("Content-Type", "application/json;charset=UTF-8");
        // 设置请求超时时间
        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000).build();
        get.setConfig(requestConfig);
        log.info("社情舆情---调用第三方接口,发送的请求:" + get);
        try {
            HttpResponse res = client.execute(get);
            String jsData = EntityUtils.toString(res.getEntity());
            String result1 = JSONObject.parseObject(jsData).getJSONObject("status").getString("code");
            // 调取结果失败,返回null
            if (!"1".equals(result1)) {
                log.error("社情舆情---调用第三方接口失败:" + JSONObject.toJSONString(url) + "---" + JSONObject.toJSONString(jsData));
                return null;
            }
            JSONObject dataJson = JSONObject.parseObject(jsData).getJSONObject("status").getJSONObject("paras");
            if (ObjectUtils.isEmpty(dataJson)) {
                log.error("社情舆情---调用第三方接口:" + JSONObject.toJSONString(url) + "获取到的数据为空---" + JSONObject.toJSONString(jsData));
                return null;
            }
            return dataJson;
        } catch (Exception e) {
            throw new Exception("社情舆情---调用第三方接口失败:" + url);
        }
    }


    @Override
    public JSONObject getSatisfaction(String date) throws Exception {
        // 获取token
        String token = getOrderInfoToken();
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        // 调用三方接口获取办结结果满意度
        return getOutsideData(date, token, satisfactionUrl);
    }


    @Override
    public JSONObject getProportionOfOrders(String date) throws Exception {
        // 获取token
        String token = getOrderInfoToken();
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        // 调用三方接口获取工单占比
        return getOutsideData(date, token, proportionOfOrdersUrl);
    }

    @Override
    public JSONObject getWorkOrderProcessing(String date) throws Exception {
        // 获取token
        String token = getOrderInfoToken();
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        // 调用三方接口获取工单办理情况、办结情况、逾期情况
        return getOutsideData(date, token, workOrderProcessingUrl);
    }

    @Override
    public JSONObject getSettlementAmount(String date) throws Exception {
        // 获取token
        String token = getOrderInfoToken();
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        // 调用三方接口获取各类工单的办理量和受理量
        return getOutsideData(date, token, settlementAmountUrl);
    }

    @Override
    public JSONObject getWorkOrderStatusRoot(String date) throws Exception {
        // 获取token
        String token = getOrderInfoToken();
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        // 调用三方接口获取工单状况、热点问题、工单监察
        return getOutsideData(date, token, workOrderStatusRootUrl);
    }

    @Override
    public JSONObject getHandlingTrends(String date) throws Exception {
        // 获取token
        String token = getOrderInfoToken();
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        // 调用三方接口获取综合办理趋势
        return getOutsideData(date, token, handlingTrendsUrl);
    }

    @Override
    public JSONObject getUndertakingSituationAnalysis(String date) throws Exception {
        // 获取token
        String token = getOrderInfoToken();
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        // 调用三方接口获取办结率排名TOP5、满意度TOP10、按时办结单位TOP10、逾期办结单位TOP10
        return getOutsideData(date, token, undertakingSituationAnalysisUrl);
    }

    @Override
    public JSONObject getNumberContractors() throws Exception {
        // 获取token
        String token = getOrderInfoToken();
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        // 调用三方接口获取承办单位数量接口
        return getOutsideData("", token, numberContractorsUrl);
    }
}

















