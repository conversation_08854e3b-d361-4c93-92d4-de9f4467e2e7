package com.onecity.os.data.schedule;

import com.onecity.os.data.notice.service.SendTianBaoRemindService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;

/**
 * 定时任务
 *
 * <AUTHOR>
 * @date 2021/1/12 下午4:03
 */
@Slf4j
@Component
public class RemindSchedule {

    @Autowired
    private SendTianBaoRemindService sendTianBaoRemindService;

    /**
     * 解决 @Scheduled 同一时间执行多个任务
     *
     * @return
     */
    @Bean
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setPoolSize(50);
        return taskScheduler;
    }

    /**
     * CHBN填报提醒，逾期3天，逾期1天提醒 调整为每日按上午9点推送
     */
//    @Scheduled(cron = "0 0 9 * * ?")
    @XxlJob(value = "overDueTianBaoRemindToUsers")
    public void overDueTianBaoRemindToUsers() {
        log.info("开始--逾期给用户发送填报更新提醒通知");
        sendTianBaoRemindService.overDueTianBaoRemindToUsers();
        log.info("结束--逾期给用户发送填报更新提醒通知");
    }

    /**
     * 填报更新提醒:每日
     */
//    @Scheduled(cron = "30 0 8 0/1 * ?")
    public void dailyRemindToUsers() {
        log.info("开始--日更新数据给用户发送填报更新提醒通知");
        sendTianBaoRemindService.dailyRemindToUsers();
        log.info("结束--日更新数据给用户发送填报更新提醒通知");
    }

    /**
     * CHBN填报提醒，统计分析 调整为每日按上午9点推送（实际只有月初和月末有消息）
     */
//    @Scheduled(cron = "0 0 9 * * ?")
    @XxlJob(value = "statisticalAnalysisRemindToUsers")
    public void statisticalAnalysisRemindToUsers() {
        log.info("开始--填报更新提醒:统计分析提醒");
        sendTianBaoRemindService.statisticalAnalysisRemindToUsers();
        log.info("结束--填报更新提醒:统计分析提醒");
    }

}




