package com.onecity.os.data.shengtai.mapper;

import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.data.shengtai.model.JzkqjcZhibiaoData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2020-04-09 11:00
 */
@Mapper
public interface JzKqjcZhibiaoDataMapper extends BaseMapper<JzkqjcZhibiaoData> {


    @Select("select * from jz_kqjc_zhibiao_data where indicator_id = #{indicatorId}  order by sequence")
    List<JzkqjcZhibiaoData> listByIndicatorId(String indicatorId);


}
