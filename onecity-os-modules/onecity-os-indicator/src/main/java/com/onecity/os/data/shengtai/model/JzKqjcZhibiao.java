package com.onecity.os.data.shengtai.model;


import lombok.Data;


import javax.persistence.Id;
import java.util.Date;


/**
 * 空气监测 指标描述表
 * <AUTHOR>
 * @date 2002-04-09 10:52
 */
@Data
public class JzKqjcZhibiao{

	/**主键**/
	@Id
	private String id;
	/**指标ID**/
	private String indicatorId;
	/**指标名称**/
	private String indicatorName;
	/**指标展现类型**/
	private String indicatorExhibitType;
	/**指标级别(一级二级)**/
	private String indicatorLevel;
	/**父指标ID,如果为一级指标, 该字段为空**/
	private String parentIndicatorId;
	/**父指标名称,如果为一级指标, 该字段为空**/
	private String parentIndicatorName;
	/**是否有下级指标**/
	private String hasNextLevelIndicator;
	/**主题Id  目前未使用**/
	private String subjectId;
	/**主题名称  目前未使用**/
	private String subjectName;
	/**栏目Id  目前未使用**/
	private String tabId;
	/**栏目名称  目前未使用**/
	private String tabName;
	/**栏目类型**/
	private String type;
	/**排序**/
	private String sequence;
	/**跳转链接**/
	private String link;
	/**指标所处位置**/
	private String source;
	/**是否关注**/
	private String attention;
	/**是否督办**/
	private String oversee;
	/**类别码**/
	private String categoryId;
	/**类别名称**/
	private String categoryName;
	/**创建时间**/
	private Date createTime;
	/**创建人**/
	private String creater;
	/**更新时间**/
	private Date updateTime;
	/**更新人**/
	private String updater;

}
