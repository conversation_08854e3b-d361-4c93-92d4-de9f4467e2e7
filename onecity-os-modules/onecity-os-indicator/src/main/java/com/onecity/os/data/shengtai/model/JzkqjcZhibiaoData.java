package com.onecity.os.data.shengtai.model;


import lombok.Data;

import javax.persistence.Id;


/**
 * 空气监测指标数据表
 * <AUTHOR>
 * @date 2020-04-09 10:53
 */
@Data
public class JzkqjcZhibiaoData {

	/**主键自增**/
	@Id
	private String id;
	/**指标Id**/
	private String indicatorId;
	/**排序**/
	private String sequence;
	/**指标项目**/
	private String itemName;
	/**指标项目值**/
	private String itemValue;
	private String itemUnit;
	/**用来表示指标数据是增加还是减少，前端显示不同的颜色**/
	private String identify;

	private Integer style;
	/**
	 * 是否折行显示0：否1：是
	 */
	private Integer isFold;

}
