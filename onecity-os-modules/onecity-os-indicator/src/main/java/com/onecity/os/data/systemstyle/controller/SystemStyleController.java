package com.onecity.os.data.systemstyle.controller;

import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.data.systemstyle.domain.SystemStyle;
import com.onecity.os.data.systemstyle.service.ISystemStyleService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 系统样式Controller
 * 
 * <AUTHOR>
 * @date 2023-03-21
 */
@RestController
@RequestMapping("/style")
public class SystemStyleController extends BaseController
{

    @Autowired
    private ISystemStyleService systemStyleService;


    /**
     * 查询系统样式列表
     */
    @GetMapping("/getStyle")
    @ApiOperation(value = "获取系统样式配置")
    public AjaxResult list()
    {
        SystemStyle result = systemStyleService.selectSystemStyleById();
        return AjaxResult.success(result);
    }
}
