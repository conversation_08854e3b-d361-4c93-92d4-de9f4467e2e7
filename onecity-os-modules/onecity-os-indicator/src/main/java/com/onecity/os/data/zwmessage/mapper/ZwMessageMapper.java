package com.onecity.os.data.zwmessage.mapper;


import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.data.zwmessage.model.po.ZwMessage;
import com.onecity.os.data.zwmessage.model.vo.ZwDelMsgVo;
import com.onecity.os.data.zwmessage.model.vo.ZwMessageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ZwMessageMapper extends BaseMapper<ZwMessage> {

    List<ZwMessageVo> selectZwList(@Param("userid") String userid, @Param("title") String title,
                                   @Param("startdate") String startdate, @Param("enddate") String enddate,
                                   @Param("type") String type);

    void save(ZwMessage zwMessage);

    List<ZwMessageVo> selectMessageByCreateid(@Param("userid") String userid);


    List<ZwMessageVo> selectMessageByType(@Param("type") String type);

    List<ZwMessageVo> selectMessageByTypeAndUserid(@Param("type") String type, @Param("userid") String userid);


    List<ZwDelMsgVo> selectMessageById(@Param("msgid") String msgid);

    ZwMessage getFileAdminById(@Param("id") Integer id);

    /**
     * 逻辑删除已发送数据
     */
    void deleteRecorde(@Param("id") Integer id);

    ZwMessage getFileAdminByTitle(@Param("title") String title, @Param("type") Integer type);

    ZwMessage getFileAdminByIssue(@Param("issue") String issue, @Param("type") Integer type);

    ZwMessage getFileAdminId(@Param("id") Integer id);
}