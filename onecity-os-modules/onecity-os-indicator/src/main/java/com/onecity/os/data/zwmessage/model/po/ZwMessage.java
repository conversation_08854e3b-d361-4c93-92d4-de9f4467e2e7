package com.onecity.os.data.zwmessage.model.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "zw_message")
@ApiModel("政务信息上报期刊表")
public class ZwMessage {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Integer id;
    private String title;
    private String issue;
    private Integer imgflag;
    private Integer type;
    private Integer fileid;
    private Integer imgid;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createtime;
    private String createid;
    private String createname;
    /**
     * 是否删除 0:否1:是
     */
    private Integer isDelete;
}
