#端口号
server:
  port: 8080
#server:port=8088
spring:
  application:
    name: onecity-os-ruoyi-indicator
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  redis:
    host: onecity-os-redis
    port: 6379
    #    host: *************
    #    port: 30425
    password: DONT(QkOmrw81EnDa0CDf9y1hywwU2sWxykwRkIHa8ZXhBvSUmcPtXUJerFvBv/77k4AxY40uLtxHEcjv86VWFXHNoPxPcGIgG6n10aMBOlsrWLwWZAjlOWr3N4JrG04kk+fPG+57s7x6Zu6algBVF8FWVJfIxkMWMIhU0IZGZ5njtok=)
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
#  autoconfigure:
#    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure,com.anji.plus.gaea.GaeaAutoConfiguration
  datasource:
    druid:
      initialSize: 5
      minIdle: 5
      maxActive: 20
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      filters: stat,slf4j
      connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      druid:
        initialSize: 5
        minIdle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
#      primary: master
      datasource:
        master:
          url: *********************************************************************************************************************************************
          username: root
          password: DONT(QkOmrw81EnDa0CDf9y1hywwU2sWxykwRkIHa8ZXhBvSUmcPtXUJerFvBv/77k4AxY40uLtxHEcjv86VWFXHNoPxPcGIgG6n10aMBOlsrWLwWZAjlOWr3N4JrG04kk+fPG+57s7x6Zu6algBVF8FWVJfIxkMWMIhU0IZGZ5njtok=)
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.mysql.jdbc.Driver
  cloud:
    nacos:
      discovery:
        server-addr: onecity-os-nacos:8848

seata:
  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启
  enabled: false
  # Seata 应用编号，默认为 ${spring.application.name}
  application-id: ${spring.application.name}
  # Seata 事务组编号，用于 TC 集群名
  tx-service-group: ${spring.application.name}-group
  # 关闭自动代理
  enable-auto-data-source-proxy: false
  # 服务配置项
  service:
    # 虚拟组和分组的映射
    vgroup-mapping:
      ruoyi-system-group: default
  config:
    type: nacos
    nacos:
      serverAddr: onecity-os-nacos:8848
      #        serverAddr: *************:30309
      group: SEATA_GROUP
      namespace:
  registry:
    type: nacos
    nacos:
      application: seata-server
      #        server-addr: *************:30309
      server-addr: onecity-os-nacos:8848
      namespace:

#myBatis-plus
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.onecity.os.data.*
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
##fastDFS 服务器配置
#socket连接超时时长 ms
#fdfs:so-timeout= 1500
fdfs:
  so-timeout: 15000
#连接tracker服务器超时时长 ms
#fdfs:connect-timeout=600
  connect-timeout: 6000
#tracker跟踪服务器，主要做调度工作,可配置多个,如果有多个在下方加- x.x.x.x:port
#fdfs:tracker-list=10.100.8.4:22122
  tracker-list: onecity-os-fastdfs-tracker:22122
#文件下载访问地址
#fdfs:web-server-url=http://10.160.255.139/
  web-server-url: http://onecity-os-fastdfs-tracker:8080/

indicator:
  template:
    file:
      path: /home/<USER>/leader_cockpit_product/dev/template/

#钉钉 企业应用接入秘钥相关
dingding:
  #  开发版
#  CorpId: ding8a5bd4a1b56aa0baee0f45d8e4f7c288
#  AppKey: dingplldmz0gskm4tfru
#  AppSecret: b1NDlurw-1x4t5PK4ulZdUrNQYzg5pa_jpL9HS1f5JJhmrxbnCQe8uYnaMtgTk5c
#  AgentId: 825067087
#  DingPanAgentId: 449580876
#  SignedUrl: http://111.63.48.25:61334/app/#/home
#  DingRoleGroupName: 驾驶舱
  #演示版
#  CorpId: dingdebdf9aead7b1adcacaaa37764f94726
#  AgentId: 1651527232
#  AppKey: dingx7c3abazl08nbtcn
#  AppSecret: 9VyGZ4OW5Muy2_zJv0nr90N6GIIsriKCApEfo34ZFBSyE_mVdzYr4xVochsvcaPi
#  DingPanAgentId: 449580876
#  SignedUrl: http://111.63.48.25:31518/app/#/home
#  DingRoleGroupName: 驾驶舱演示版
#  钉钉测试版
  CorpId: ding2f6e657a3893596724f2f5cc6abecb85
  AppKey: ding0kwcm5cz3xsne64h
  AppSecret: d_YNk8D6FbIYmqKqOSXZ1oMIgu6ZlV6ucRUqDJhztX2PL9h3yTgj8xmiUhqPAYdf
  AgentId: 1651556269
  DingPanAgentId: 449580876
  SignedUrl: http://111.63.48.25:32753/app-portal/app/
  DingRoleGroupName: 驾驶舱测试版
  DingRoleLevel: 1,2,3,4,5
  DingRoleLevelName: 一级职位,二级职位,三级职位,四级职位,五级职位
  #钉钉向上查看通讯录范围, 应为通讯录为五级,所有 5 就是向上看到所有的
  DingRoleLevelUp: 5
  #钉钉向下查看通讯录范围
  DingRoleLevelDown: 2

#社情舆情调用外部接口的请求信息
orderInfo:
  token:
    url: http://60.220.224.225:8998/api/GetToken/GetInformation
    userName: Leader
    passWord: Leaderadmin
  satisfaction:
    url: http://60.220.224.225:8998/api/Get_Order_Info/Satisfaction
  proportionOfOrders:
    url: http://60.220.224.225:8998/api/Get_Order_Info/Proportionoforders
  workOrderProcessing:
    url: http://60.220.224.225:8998/api/Get_Order_Info/WorkOrderProcessing
  settlementAmount:
    url: http://60.220.224.225:8998/api/Get_Order_Info/Settlementamount
  workOrderStatusRoot:
    url: http://60.220.224.225:8998/api/Get_Order_Info/WorkOrderStatusRoot
  handlingTrends:
    url: http://60.220.224.225:8998/api/Get_Order_Info/HandlingTrends
  undertakingSituationAnalysis:
    url: http://60.220.224.225:8998/api/Get_Order_Info/UndertakingSituationAnalysis
  numberContractors:
    url: http://60.220.224.225:8998/api/Get_Order_Info/NumberContractors

app:
  #发送APP通知消息，messageUrl
  picUrl: http://39.100.105.105:8080/group1/M00/00/00/rBriW18nfzWAfeNbAAAMBM970cM469.png
  baseMessageUrl: http://111.63.48.25:32753/app-portal/app/#/
  superviseMessageUrl: sxdb
  indicatorMessageUrl: myInterest
  disposalMessageUrl: ldps
  noticeMessageUrl: notice
  #一网统管平台请求地址
  thirdPlandUrl:  https://wqevent.onecity.cmccsi.cn/dev/api
  sm2privateKey: 1910B1A23C3BBBDC31EDD14994F07ED93607F4191FD0AF4BE4B9CAA5221D50A2
  sm2publickey: 04A918EC921A71C1C8B215D52ED3BA3EBA62AB232AB890CB3772927F95ABC2BED314D440A8824D0338491BB65DACA12E0F2732EEFDBE6F9001FAAE91E868B3BAAD
  #一网统管平台私钥
  privateKey: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKQ8qZKNnO2HoLaY
other:
  videoUrl: http://39.98.154.144:8060/robot/liveStream?deviceId=34020000001310000002&playType=1&subDeviceId=34020000001310000002&type=2
  tianqi: http://211.142.59.187:10132/api-jsc
  email: <EMAIL>
#关闭分页查询优化，超过最大页数后不返回数据
pagehelper:
  reasonable: false
