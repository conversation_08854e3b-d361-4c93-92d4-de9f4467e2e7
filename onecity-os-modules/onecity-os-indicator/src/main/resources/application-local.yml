#端口号
server:
  port: 8080
  servlet:
    context-path: service-cockpit-indicator
#server:port=8088
spring:
  application:
    name: onecity-os-cockpit-indicator
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: *******************************************************************************************************************************************
          username: root
          password: DONT(QkOmrw81EnDa0CDf9y1hywwU2sWxykwRkIHa8ZXhBvSUmcPtXUJerFvBv/77k4AxY40uLtxHEcjv86VWFXHNoPxPcGIgG6n10aMBOlsrWLwWZAjlOWr3N4JrG04kk+fPG+57s7x6Zu6algBVF8FWVJfIxkMWMIhU0IZGZ5njtok=)
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.mysql.jdbc.Driver
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 192.168.108.1:30309
      config:
        # 配置中心地址
        server-addr: 192.168.108.1:30309
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

#myBatis-plus
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:mapper/**Mapper:xml
  type-aliases-package: cokpit.indicator.model.po

##fastDFS 服务器配置
#socket连接超时时长 ms
#fdfs:so-timeout= 1500
fdfs:
  so-timeout: 15000
#连接tracker服务器超时时长 ms
#fdfs:connect-timeout=600
  connect-timeout: 6000
#tracker跟踪服务器，主要做调度工作,可配置多个,如果有多个在下方加- x.x.x.x:port
#fdfs:tracker-list=10.100.8.4:22122
  tracker-list: 192.168.108.1:31566
#文件下载访问地址
#fdfs:web-server-url=http://10.160.255.139/
  web-server-url: http://192.168.108.1:31566/

indicator:
  template:
    file:
      path: C://模板

#钉钉 企业应用接入秘钥相关
dingding:
  CorpId: ding8a5bd4a1b56aa0baee0f45d8e4f7c288
  AppKey: dingplldmz0gskm4tfru
  AppSecret: b1NDlurw-1x4t5PK4ulZdUrNQYzg5pa_jpL9HS1f5JJhmrxbnCQe8uYnaMtgTk5c
  AgentId: 825067087
  DingPanAgentId: 824849938
  SignedUrl: http://111.63.48.25:61334/app/#/home
  DingRoleGroupName: 数字政府领导驾驶舱
  DingRoleLevel: 1,2,3,4,5
  DingRoleLevelName: 一级职位,二级职位,三级职位,四级职位,五级职位
  #钉钉向上查看通讯录范围, 应为通讯录为五级,所有 5 就是向上看到所有的
  DingRoleLevelUp: 5
  #钉钉向下查看通讯录范围
  DingRoleLevelDown: 2

#社情舆情调用外部接口的请求信息
orderInfo:
  token:
    url: http://60.220.224.225:8998/api/GetToken/GetInformation
    userName=Leader
    passWord=Leaderadmin
  satisfaction:
    url: http://60.220.224.225:8998/api/Get_Order_Info/Satisfaction
  proportionOfOrders:
    url: http://60.220.224.225:8998/api/Get_Order_Info/Proportionoforders
  workOrderProcessing:
    url: http://60.220.224.225:8998/api/Get_Order_Info/WorkOrderProcessing
  settlementAmount:
    url: http://60.220.224.225:8998/api/Get_Order_Info/Settlementamount
  workOrderStatusRoot:
    url: http://60.220.224.225:8998/api/Get_Order_Info/WorkOrderStatusRoot
  handlingTrends:
    url: http://60.220.224.225:8998/api/Get_Order_Info/HandlingTrends
  undertakingSituationAnalysis:
    url: http://60.220.224.225:8998/api/Get_Order_Info/UndertakingSituationAnalysis
  numberContractors:
    url: http://60.220.224.225:8998/api/Get_Order_Info/NumberContractors

app:
  #发送APP通知消息，messageUrl
  picUrl: http://39.100.105.105:8080/group1/M00/00/00/rBriW18nfzWAfeNbAAAMBM970cM469.png
  baseMessageUrl: http://111.63.48.25:32753/app-portal/app/#/
  superviseMessageUrl: sxdb
  indicatorMessageUrl: myInterest
  disposalMessageUrl: ldps
  noticeMessageUrl: notice


