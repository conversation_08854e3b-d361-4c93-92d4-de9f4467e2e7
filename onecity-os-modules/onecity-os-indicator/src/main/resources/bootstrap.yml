spring:
  application:
    # 应用名称
    name: onecity-os-ruoyi-indicator
  profiles:
    active: dev
  jmx:
    enabled: false
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: onecity-os-nacos:8848
#        server-addr: 192.168.112.12:30309
      config:
        # 配置中心地址
        server-addr: onecity-os-nacos:8848
#        server-addr: 192.168.112.12:30309
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
swagger:
  production: false
  basic:
    enable: true
    username: cockpit
    password: cockpit1234

#XXL-job配置
jsc:
  xxljob:
    ##是否启用xxljob
    enabled: true
    ### 调度中心部署跟地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
    adminAddresses: http://onecity-os-backend-job:8080/xxl-job-admin
#    adminAddresses: http://**************:30310/xxl-job-admin
    ### 执行器通讯TOKEN [选填]：非空时启用；
    accessToken: jiashicang20179aSr@Mg%df25
    ### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
    appname: ${spring.application.name}
    ### 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。
    address:
    ### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
    ip:
    ### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
    port: 0
    ### 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
    logPath: ./logs/xxl-job/jobhandler
    ### 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
    logRetentionDays: 30