<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true">
    <include resource="org/springframework/boot/logging/logback/base.xml"/>
    <contextName>cockpit</contextName>
    <property name="logHome" value="./log" scope="context"/>
<!--    <property name="LOG_HOME" value="/Users/<USER>/chinamobile/projects/logs" />-->
<!--    <property name="logHome" value="C:/logs/yd" scope="context"/>-->
    <!-- 文件切割大小 -->
    <property name="maxFileSize" value="100MB" />
    <!-- 文档保留天数 -->
    <property name="maxHistory" value="60" />
    <!-- 文档保留总大小 -->
    <property name="totalSizeCap" value="10GB" />
    <jmxConfigurator />
    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--系统服务日志-->
        <file>${logHome}/logFile.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logHome}/logFile.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 单个日志文件最多 100MB, 60天的日志周期，最大不能超过10GB -->
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <charset>utf-8</charset>
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level  %logger{36}: %msg%n</Pattern>
        </encoder>
    </appender>
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logHome}/logErrorFile.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logHome}/logErrorFile.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <charset>utf-8</charset>
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level  %logger{36}: %msg%n</Pattern>
        </encoder>
    </appender>
    <logger name="com.jzctc.cokpit.indicator" level="INFO"/>
    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

    <root level="INFO">
        <appender-ref ref="INFO_FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </root>
</configuration>
