<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.emer.mapper.AppAssesMapper">

    <resultMap type="com.onecity.os.data.emer.model.entity.asses.AssesReportBean" id="AssesContentMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="reportName" column="reportName" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="createCompany" column="create_company" jdbcType="VARCHAR"/>
        <result property="filePath" column="file_path" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>


    <select id="getAssesReportByName" resultMap="AssesContentMap">
        select id,`name` as reportName,`type`,create_company,file_path,create_time from emer_report  where is_delete = 0
        <if test="reportName != null">
            and INSTR(`name`,#{reportName})
        </if>
        order by sequence asc,update_time desc
    </select>








</mapper>