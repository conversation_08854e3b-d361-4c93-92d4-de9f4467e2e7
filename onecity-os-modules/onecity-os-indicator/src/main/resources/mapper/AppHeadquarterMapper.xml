<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.emer.mapper.AppHeadquarterMapper">

    <select id="getAllHeadquarters" resultType="com.onecity.os.data.emer.model.entity.headquarter.DepartmentBean">
    select id,`name` from emer_department where `type` = #{type} and is_delete = 0 order by sequence,create_time desc
    </select>

    <select id="getPopulation" resultType="Integer">
    select COUNT(`name`) as population,`type`  from emer_department  where `type` = #{type} and is_delete = 0
    </select>


    <select id="getHeadquarterUserById"
            resultType="com.onecity.os.data.emer.model.entity.headquarter.HeadquarterUserBean">
        select  t2.`name`,t2.station,t2.duty,t1.firduty,t1.secduty
        from emer_department as t1 left join emer_user as t2
        on t1.id = t2.depart_id where t1.id = #{id} and t1.is_delete = 0 and t2.is_delete=0
    </select>

    <select id="getStation" resultType="String">
        SELECT
        t2.station
        FROM
        emer_department AS t1
        LEFT JOIN emer_user AS t2 ON t1.id = t2.depart_id
        WHERE
        t1.id = #{id}
        and t1.is_delete = 0 and t2.is_delete=0 GROUP BY t2.station
        <if test="null != type and 0 == type">
            ORDER BY
            CASE
            WHEN t2.station = '总指挥' THEN
            1
            WHEN t2.station = '常务副总指挥' THEN
            2
            WHEN t2.station = '副总指挥' THEN
            3
            WHEN t2.station = '成员' THEN
            4
            END ASC
        </if>
        <if test="null != type and 1 == type">
            ORDER BY
            CASE
            WHEN t2.station = '指挥长' THEN
            1
            WHEN t2.station = '副指挥长' THEN
            2
            WHEN t2.station = '成员' THEN
            3
            END ASC
        </if>
    </select>

    <select id="getEmerDepartById" resultType="com.onecity.os.data.emer.model.entity.headquarter.HeadquarterUserBeanOuter">
        SELECT * FROM emer_department WHERE id = #{id} AND is_delete = 0 LIMIT 1
    </select>

    <select id="getEmerUsersByDepartId" resultType="com.onecity.os.data.emer.model.entity.headquarter.UserBean">
         SELECT * FROM emer_user WHERE depart_id = #{id} AND is_delete = 0 AND station=#{station}
    </select>


</mapper>

















