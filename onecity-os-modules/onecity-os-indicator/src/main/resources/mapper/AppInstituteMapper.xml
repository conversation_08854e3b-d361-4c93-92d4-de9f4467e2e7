<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.emer.mapper.AppInstituteMapper">

    <!--<resultMap type="com.onecity.os.data.emer.model.entity.asses.AssesReportBean" id="AssesContentMap">-->
    <!--<result property="id" column="id" jdbcType="BIGINT"/>-->
    <!--<result property="reportName" column="reportName" jdbcType="VARCHAR"/>-->
    <!--<result property="type" column="type" jdbcType="VARCHAR"/>-->
    <!--<result property="create_company" column="createCompany" jdbcType="VARCHAR"/>-->
    <!--<result property="file_path" column="filePath" jdbcType="VARCHAR"/>-->
    <!--<result property="create_time" column="createTime" jdbcType="TIMESTAMP"/>-->
    <!--</resultMap>-->


    <select id="getInstituteById" resultType="com.onecity.os.data.emer.model.entity.institute.InstituteBean">
   select  t1.area_introduce,t1.base_state,t1.institute_duty from institute_introduce_manage as t1 left join source_manage as t2
    on t1.source_manage_id = t2.id
    where t1.source_manage_id = #{id} and t1.is_delete = 0 and t2.is_delete = 0 order by t1.create_time desc LIMIT 1
    </select>


</mapper>