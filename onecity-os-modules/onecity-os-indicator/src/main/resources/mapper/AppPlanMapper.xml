<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.emer.mapper.AppPlanMapper">
    <resultMap type="com.onecity.os.data.emer.model.entity.plan.PlanContenct" id="planContenctMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="sequence" column="sequence" jdbcType="INTEGER"/>
        <result property="state" column="state" jdbcType="INTEGER"/>
        <result property="createCompany" column="create_company" jdbcType="VARCHAR"/>
        <result property="effectDate" column="effect_date" jdbcType="TIMESTAMP"/>
        <result property="creater" column="creater" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
<!--        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>-->
    </resultMap>

<!--    <select id="getPlanType" resultType="com.onecity.os.data.emer.model.entity.plan.PlanType">-->
<!--      select type from emer_plan where  is_delete = 0 GROUP BY type order by sequence-->
<!--    </select>-->

    <select id="getPlanContent" resultMap="planContenctMap" >
        select id,
        `name`,sequence,state,create_company,effect_date,creater,create_time,updater,update_time
        from emer_plan where type = #{type}
        and is_delete = 0 order by sequence asc,create_time desc
    </select>

    <select id="getPlanContentById" resultType="String">
      select file_path from emer_plan where id = #{id}
    and is_delete = 0
    </select>











</mapper>