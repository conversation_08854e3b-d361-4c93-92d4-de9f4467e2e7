<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.emer.mapper.AppRanksMapper">
    <!--<resultMap type="com.onecity.os.data.emer.model.entity.plan.PlanContenct" id="planContenctMap">-->
        <!--<result property="id" column="id" jdbcType="BIGINT"/>-->
        <!--<result property="name" column="name" jdbcType="VARCHAR"/>-->
        <!--<result property="sequence" column="sequence" jdbcType="INTEGER"/>-->
        <!--<result property="state" column="state" jdbcType="INTEGER"/>-->
        <!--<result property="create_company" column="createCompany" jdbcType="VARCHAR"/>-->
        <!--<result property="effect_date" column="effectDate" jdbcType="TIMESTAMP"/>-->
        <!--<result property="creater" column="creater" jdbcType="VARCHAR"/>-->
        <!--<result property="create_time" column="createTime" jdbcType="TIMESTAMP"/>-->
        <!--<result property="updater" column="updater" jdbcType="VARCHAR"/>-->
        <!--<result property="update_time" column="updateTime" jdbcType="TIMESTAMP"/>-->
        <!--<result property="is_delete" column="isDelete" jdbcType="INTEGER"/>-->
    <!--</resultMap>-->

    <select id="getRanksType" resultType="String">
      select type from emer_ranks where is_delete = 0  group by type
    </select>


    <select id="getDistrictAndPopulation" resultType="com.onecity.os.data.emer.model.entity.ranks.DistrictAndPopulation">
      select district,count(district) as population from emer_ranks where type = #{type} and is_delete = 0 group by district
    </select>


    <select id="getRanksContent" resultType="com.onecity.os.data.emer.model.entity.ranks.RanksContent">
      select id,`name`,contacts,contacts_tel,nums from emer_ranks where district = #{district} and `type` = #{type} and is_delete = 0
      order by creater_time desc
    </select>


    <select id="getAllRanksContentById" resultType="com.onecity.os.data.emer.model.entity.ranks.AllRanksContent">
        select id,`name`,contacts,contacts_tel,nums,unit,station,quality,rescueclass,equipment from emer_ranks where id = #{id} and is_delete = 0
    </select>





</mapper>