<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.indicator.mapper.dingmaillist.DingMailDepartmentMapper">

    <sql id="Base_Column_List">
        id,departmentid,name,parentid
    </sql>

    <insert id="saveDepartment" parameterType="com.onecity.os.data.indicator.model.po.dingmaillist.Department" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO dingmail_department ( id, departmentid, name, parentid)
        VALUES
            (#{id}, #{departmentid},#{name}, #{parentid})
    </insert>

    <delete id="truncateData">
        truncate table dingmail_department
    </delete>

    <select id="getDepartmentIdByParentId" resultType="Long">
        SELECT departmentid FROM dingmail_department WHERE parentid=#{id} AND parentid != 1
    </select>

    <select id="selectDepartByDepartId" resultType="com.onecity.os.data.indicator.model.po.dingmaillist.Department">
        select <include refid="Base_Column_List"/> from dingmail_department
        where departmentid=#{departmentid} limit 1
    </select>

    <select id="selectDepartByNameAndParentId" resultType="com.onecity.os.data.indicator.model.po.dingmaillist.Department">
        select <include refid="Base_Column_List"/> from dingmail_department
        where departmentid = #{departmentid}
        <if test="name != null  and name != ''">
            and INSTR(`name`, #{name})
        </if>
    </select>

    <select id="selectDepartByparentIdAndName" resultType="com.onecity.os.data.indicator.model.po.dingmaillist.Department">
        select <include refid="Base_Column_List"/> from dingmail_department
        where parentid = 1
        <if test="name != null  and name != ''">
            and INSTR(`name`, #{name})
        </if>
    </select>

    <select id="selectDepartByparentId" resultType="com.onecity.os.data.indicator.model.po.dingmaillist.Department">
        select <include refid="Base_Column_List"/> from dingmail_department where parentid = 1
    </select>

    <select id="getDepartmentInfoByParentId" resultType="com.onecity.os.data.indicator.model.po.dingmaillist.Department">
        select <include refid="Base_Column_List"/> from dingmail_department where parentid=#{id} AND parentid != 1
    </select>

</mapper>