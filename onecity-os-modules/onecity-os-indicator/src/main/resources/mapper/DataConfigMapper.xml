<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.onecity.os.data.indicator.mapper.DataConfigMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.data.indicator.model.po.DataConfig">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="data_set_id" property="dataSetId" jdbcType="BIGINT"/>
        <result column="data_set_name" property="dataSetName" jdbcType="VARCHAR"/>
        <result column="data_unit" property="dataUnit" jdbcType="VARCHAR"/>
        <result column="data_value" property="dataValue" jdbcType="VARCHAR"/>
        <result column="data_key" property="dataKey" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creater" property="creater" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="is_master_value" property="isMasterValue" jdbcType="TINYINT"/>
        <result column="indicator_id" property="indicatorId" jdbcType="VARCHAR"/>
        <result column="secondary_unit" property="secondaryUnit" jdbcType="VARCHAR"/>
        <result column="data_value_name" property="dataValueName" jdbcType="VARCHAR"/>
        <result column="data_key_name" property="dataKeyName" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="getDataConfigById" resultType="com.onecity.os.data.indicator.model.po.DataConfig">
        select * from data_config
        where id = #{id}
        and is_delete = 0
    </select>

    <select id="getDataConfigByIndicatorId" resultType="com.onecity.os.data.indicator.model.po.DataConfig">
        select * from data_config
        where indicator_id = #{id}
        and is_delete = 0
    </select>

</mapper>













