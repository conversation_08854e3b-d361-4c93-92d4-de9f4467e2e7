<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.noticeimportant.dao.DingCommentMapper">

    <select id="selectByReceiverid" resultType="com.onecity.os.data.noticeimportant.entity.po.DingComment">
        select * from dingcomment where filemanageid=#{filemanageid} and receiverid=#{receiverid}
    </select>

    <select id="selectBySender" resultType="com.onecity.os.data.noticeimportant.entity.po.DingComment">
        select * from dingcomment where adminid=#{adminid} order by writetime desc
    </select>

    <select id="selectByFile" resultType="com.onecity.os.data.noticeimportant.entity.po.DingComment">
        select * from dingcomment where filemanageid=#{filemanageid} order by writetime desc
    </select>

    <insert id="save" parameterType="com.onecity.os.data.noticeimportant.entity.po.DingComment" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO dingcomment ( id, adminid, filemanageid, receiver, receiverid, commenttitle, commentcontent, writetime )
        VALUES
            (#{id}, #{adminid},#{filemanageid},#{receiver}, #{receiverid}, #{commenttitle}, #{commentcontent}, #{writetime})
    </insert>


    <update id="updateFileadminIsWrite"  >
        update dingfileadmin set iswrite=1  where id=#{adminid}
    </update>

    <update id="updateFilemanageIsWrite"  >
        update dingfilemanage set iswrite=1  where id=#{filemanageid}
    </update>

    <update id="updateIsWriteAndRead">
        update  dingfilereceiver set iswrite=1,isread=1 where receiverid=#{receiverid} and fileid=#{fileid}
    </update>
</mapper>