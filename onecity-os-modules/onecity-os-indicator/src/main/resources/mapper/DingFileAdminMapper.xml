<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.noticeimportant.dao.DingFileAdminMapper">

    <select id="getFileAdmin" resultType="com.onecity.os.data.noticeimportant.entity.po.DingFileAdmin">
      SELECT * from dingfileadmin a where createrid=#{createrid} AND is_delete = 0 ORDER BY a.createtime desc,a.iswrite desc
  </select>

    <insert id="save" parameterType="com.onecity.os.data.noticeimportant.entity.po.DingFileAdmin"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    INSERT INTO dingfileadmin ( id, theme, createrid, department, creater, createtime)
    VALUES
	    (#{id}, #{theme},#{createrid}, #{department}, #{creater}, #{createtime})
  </insert>

    <select id="getFileAdminById" resultType="com.onecity.os.data.noticeimportant.entity.po.DingFileAdmin">
    SELECT * from dingfileadmin where id = #{id} AND is_delete = 0
  </select>

    <update id="deleteRecorde">
    update dingfileadmin set is_delete = 1  where id = #{id}
  </update>


</mapper>
















