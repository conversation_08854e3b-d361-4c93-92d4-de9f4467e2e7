<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.noticeimportant.dao.DingFileManageMapper">

    <resultMap id="DingFileAdminVoMap" type="com.onecity.os.data.noticeimportant.entity.vo.DingFileAdminVo">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="createtime" property="createtime" jdbcType="VARCHAR"/>
        <result column="iswrite" property="iswrite" jdbcType="INTEGER"/>
        <collection property="fileList" resultMap="fileListMap"/>
        <collection property="receiverList" resultMap="receiverListMap"/>
    </resultMap>
    <resultMap id="fileListMap" type="com.onecity.os.data.noticeimportant.entity.po.DingFileManage">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="adminid" property="adminid" jdbcType="INTEGER"/>
        <result column="spaceId" property="spaceId" jdbcType="VARCHAR"/>
        <result column="fileId" property="fileId" jdbcType="VARCHAR"/>
        <result column="fileName" property="fileName" jdbcType="VARCHAR"/>
        <result column="fileType" property="fileType" jdbcType="VARCHAR"/>
        <result column="fileSize" property="fileSize" jdbcType="INTEGER"/>
        <result column="iswrite" property="iswrite" jdbcType="INTEGER"/>
    </resultMap>
    <resultMap id="receiverListMap" type="String">
        <result column="receiver" property="receiver" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectListByUserid" resultType="com.onecity.os.data.noticeimportant.entity.vo.DingFileManageVo">
    SELECT
      a.id,
      a.adminid,
      a.fileName,
      a.fileId,
      a.fileSize,
      a.fileType,
      a.spaceId,
      c.department,
      c.creater,
      c.createtime,
      b.isread,
      b.iswrite
    FROM
	  dingfilemanage a
      LEFT JOIN dingfileadmin c ON a.adminid = c.id
      LEFT JOIN dingfilereceiver b ON a.id = b.fileid
    WHERE
	    b.receiverid = #{userid}  ORDER BY c.createtime desc,b.isread desc,b.iswrite desc
  </select>

    <select id="selectQueryListByRecevierId"
            resultType="com.onecity.os.data.noticeimportant.entity.vo.DingFileManageVo">
        SELECT
        a.id,
        a.adminid,
        a.fileName,
        a.fileId,
        a.fileSize,
        a.fileType,
        a.spaceId,
        c.department,
        c.creater,
        c.createtime,
        b.isread,
        b.iswrite
        FROM
        dingfilemanage a
        LEFT JOIN dingfileadmin c ON a.adminid = c.id
        LEFT JOIN dingfilereceiver b ON a.id = b.fileid
        WHERE
        b.receiverid = #{userid}
        <if test="creater != null and creater != ''">and INSTR(c.creater,#{creater})</if>
        <if test="startdate != null and startdate != ''">and c.createtime >= #{startdate}</if>
        <if test="enddate != null and enddate != ''">and c.createtime &lt;= #{enddate}</if>
        <if test="filename != null and filename != ''">and INSTR(a.filename,#{filename})</if>
        AND c.is_delete = 0
        ORDER BY c.createtime desc,b.isread desc,b.iswrite desc
    </select>

    <select id="selectQueryListByCreaterId" resultType="com.onecity.os.data.noticeimportant.entity.po.DingFileAdmin">
        SELECT
        DISTINCT c.*
        FROM dingfileadmin c
        LEFT JOIN dingfilemanage a ON a.adminid = c.id
        LEFT JOIN dingfilereceiver b ON a.id = b.fileId
        WHERE
        c.createrid = #{userid}
        <if test="receiver != null and receiver != ''">and INSTR(b.receiver,#{receiver})</if>
        <if test="startdate != null and startdate != ''">and c.createtime >= #{startdate}</if>
        <if test="enddate != null and enddate != ''">and c.createtime &lt;= #{enddate}</if>
        <if test="filename != null and filename != ''">and INSTR(a.filename,#{filename})</if>
        ORDER BY c.createtime desc,c.iswrite
    </select>

    <select id="selectListByAdminid" resultType="com.onecity.os.data.noticeimportant.entity.po.DingFileManage">
    SELECT
	    *
    FROM
	    dingfilemanage
    WHERE
	    adminid = #{adminid}
  </select>

    <select id="selectById" resultType="com.onecity.os.data.noticeimportant.entity.po.DingFileManage">
    SELECT
        *
    FROM
        dingfilemanage
    WHERE
        id = #{id}
  </select>

    <insert id="save" parameterType="com.onecity.os.data.noticeimportant.entity.po.DingFileManage"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    INSERT INTO dingfilemanage ( id, adminid, spaceId, fileId, fileName, fileType, fileSize )
    VALUES
	    (#{id}, #{adminid},#{spaceId}, #{fileId}, #{fileName}, #{fileType}, #{fileSize})
  </insert>

    <select id="getSendListByUserId" resultMap="DingFileAdminVoMap">
        SELECT
        DISTINCT c.*,
        a.*,
        b.receiver AS receiver
        FROM dingfileadmin c
        LEFT JOIN dingfilemanage a ON a.adminid = c.id
        LEFT JOIN dingfilereceiver b ON a.id = b.fileId
        WHERE
        c.createrid = #{userid}
        <if test="receiver != null and receiver != ''">and INSTR(b.receiver,#{receiver})</if>
        <if test="startdate != null and startdate != ''">and c.createtime >= #{startdate}</if>
        <if test="enddate != null and enddate != ''">and c.createtime &lt;= #{enddate}</if>
        <if test="filename != null and filename != ''">and INSTR(a.filename,#{filename})</if>
        ORDER BY c.createtime desc,c.iswrite
    </select>

    <select id="getReportList" resultType="com.onecity.os.data.noticeimportant.entity.ImportantReportManage">
        SELECT m.id,
        m.content,
        m.title,
        m.send_time AS sendTime,
        su.realname AS sendUser,
        m.public_source as publicSource
        FROM important_report_manage AS m
        left join (select important_report_id,group_concat(audit_user_id) as audit_user_id from important_report_audit group by important_report_id) A
        on m.id = A.important_report_id
        left join sys_user su on m.creater=su.username
        WHERE FIND_IN_SET(#{userId}, m.receive_user_id)
        <if test="title != null and title != ''">
            AND INSTR(m.title,#{title})
        </if>
        <if test="isAudit != null and isAudit != ''">
            AND A.audit_user_id is not null AND FIND_IN_SET(#{userId}, A.audit_user_id) > 0
        </if>
        <if test="isAudit == null">
            AND (A.audit_user_id is null OR FIND_IN_SET(#{userId}, A.audit_user_id) = 0)
        </if>
        AND m.is_delete = 0 and m.status = 1
        ORDER BY m.update_time DESC
    </select>

    <select id="getAuditMsg" resultType="com.onecity.os.data.noticeimportant.entity.ImportantReportAudit">
        SELECT audit.id,
        audit.important_report_id,
        audit.audit_user_id,
        audit.audit_time,
        audit.audit_view,
        audit.creater,
        audit.create_time
        FROM important_report_audit audit
        WHERE 1 = 1
        <if test="auditUserId != null and auditUserId != ''">
            AND audit.audit_user_id = #{auditUserId}
        </if>
        AND audit.important_report_id = #{importantReportId}
    </select>

    <select id="selectUserIdByUserName" resultType="java.lang.String">
        select id from sys_user where username=#{userName} limit 1
    </select>
</mapper>