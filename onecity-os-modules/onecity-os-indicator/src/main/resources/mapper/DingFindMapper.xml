<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.indicator.mapper.dingmaillist.DingFindMapper">

    <select id="getListPage" parameterType="com.onecity.os.data.indicator.model.vo.dingmaillist.DingdingFindReqVo" resultType="com.onecity.os.data.indicator.model.vo.dingmaillist.DingdingFindResVo">
        select t1.name,count(t2.departments) as population from dingmail_department as t1
        left join  dingmail_user as t2 on t1.departmentid= t2.departments
        group by t1.name,t2.departments
    </select>



</mapper>