<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.indicator.mapper.dingmaillist.DingMailRoleMapper">

    <select id="selectRoleByRoleid" resultType="com.onecity.os.data.indicator.model.po.dingmaillist.Role">
        select  * from dingmail_role where roleid=#{roleid}
    </select>

    <select id="getRoleLevelUp" resultType="java.lang.Integer">
        select min(a.level) from dingmail_role a
        <where>
            a.roleid IN
            <foreach item="roleids" index="index" collection="roleids" open="(" separator="," close=")"> #{roleids}</foreach>
        </where>
    </select>

    <select id="selectRoleByLevel" resultType="java.lang.String">
        select a.roleid from dingmail_role a where a.level &gt;= #{levelUp} and a.level &lt;= #{levelDown}
    </select>

    <insert id="saveRole" parameterType="com.onecity.os.data.indicator.model.po.dingmaillist.Role" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO dingmail_role ( id, roleid, rolename, level, groupname, groupid)
        VALUES
            (#{id}, #{roleid},#{rolename}, #{level}, #{groupname}, #{groupid})
    </insert>

    <delete id="truncateData">
        truncate table dingmail_role
    </delete>

</mapper>