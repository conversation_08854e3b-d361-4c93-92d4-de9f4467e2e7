<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.indicator.mapper.dingmaillist.DingMailUserMapper">

    <select id="selectUserByUserId" resultType="com.onecity.os.data.indicator.model.po.dingmaillist.User">
        select  * from dingmail_user where userid=#{userid} limit 1
    </select>

    <select id="selectUserByRoleAndDep" resultType="com.onecity.os.data.indicator.model.po.dingmaillist.User">
        select * from dingmail_user a
        <!--屏蔽角色查询 -->
        <where>
            a.roles IN
            <foreach item="roleids" index="index" collection="roleids" open="(" separator="," close=")">#{roleids}
            </foreach>
        </where>
        and a.departments=#{departmentid}

<!--        where a.departments=#{departmentid}-->

        order by a.orderindepts desc
    </select>

    <insert id="saveUser" parameterType="com.onecity.os.data.indicator.model.po.dingmaillist.User"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO dingmail_user ( id, userid, name, mobile, position, departments, roles, rolelevel, orderindepts)
        VALUES
            (#{id}, #{userid},#{name}, #{mobile}, #{position}, #{departments}, #{roles}, #{rolelevel}, #{orderindepts})
    </insert>

    <delete id="truncateData">
        truncate table dingmail_user
    </delete>

    <select id="selectUserByDepIdsAndRoleLevel" resultType="com.onecity.os.data.indicator.model.po.dingmaillist.User">
        SELECT
        *
        FROM
        dingmail_user a
        LEFT JOIN dingmail_role b ON b.roleid = a.roles
        WHERE
        b.`level` &gt;= #{levelUp}
        AND b.level &lt;= #{levelDown}
        AND a.departments IN
        <foreach item="departmentId" collection="departmentIds" open="(" separator="," close=")">
            #{departmentId}
        </foreach>
        order by a.orderindepts desc
    </select>

    <select id="getUserNumByDepartmentIdsAndRoleIds" resultType="Integer">
        SELECT COUNT( id ) FROM dingmail_user
        WHERE
        departments IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="null != roleids and 0 != roleids.size()">
            AND roles IN
            <foreach collection="roleids" item="roleid" open="(" separator="," close=")">
                #{roleid}
            </foreach>
        </if>
    </select>

    <select id="getUserByDepart" resultType="com.onecity.os.data.indicator.model.po.dingmaillist.User">
        SELECT
        *
        FROM dingmail_user u
                 LEFT JOIN dingmail_department dp ON dp.departmentid = u.departments
        where
            FIND_IN_SET(#{departId}, u.departments)
    </select>

    <select id="selectUserByNameAndLevel" resultType="com.onecity.os.data.indicator.model.po.dingmaillist.User">
        select * from dingmail_user
        where rolelevel >= #{levelUp} and rolelevel &lt;= #{levelDown}
        <if test="name != null  and name != ''">
            and INSTR(name, #{name})
        </if>
    </select>

    <select id="selectUserByDepIdsAndRoleLevelAndName" resultType="com.onecity.os.data.indicator.model.po.dingmaillist.User">
        select * from dingmail_user
        where rolelevel >= #{levelUp} and rolelevel &lt;= #{levelDown}
        <if test="name != null  and name != ''">
            and INSTR(`name`, #{name})
        </if>
        and departments in
         <foreach collection="allDepartIds" open="(" separator="," close=")" item="departments">
            #{departments}
         </foreach>
    </select>

    <select id="selectAllRolesByIds" resultType="com.onecity.os.data.indicator.model.dto.DingdingRoleDTO">
        select id,roleid as roleId,rolename as roleName from dingmail_role where 1=1 and roleid in
        <foreach collection="roleIdList" open="(" separator="," close=")" item="roleId">
            #{roleId}
        </foreach>
        order by level
    </select>
</mapper>