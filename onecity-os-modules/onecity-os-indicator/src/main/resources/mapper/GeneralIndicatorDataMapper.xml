<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.indicator.mapper.GeneralIndicatorDataMapper">

    <resultMap type="com.onecity.os.data.indicator.model.po.GeneralIndicatorData" id="BaseResultMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="indicatorId" column="indicator_id" jdbcType="VARCHAR"/>
        <result property="indicatorName" column="indicator_name" jdbcType="VARCHAR"/>
        <result property="itemName" column="item_name" jdbcType="VARCHAR"/>
        <result property="itemValue" column="item_value" jdbcType="VARCHAR"/>
        <result property="itemUnit" column="item_unit" jdbcType="VARCHAR"/>
        <result property="identify" column="identify" jdbcType="VARCHAR"/>
        <result property="style" column="style" jdbcType="INTEGER"/>
        <result property="fold" column="is_fold" jdbcType="INTEGER"/>
        <result property="sequence" column="sequence" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creater" column="creater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="VARCHAR"/>
        <result property="currentFlag" column="current_flag" jdbcType="INTEGER"/>
        <result property="delete" column="is_delete" jdbcType="INTEGER"/>
        <result property="urlIds" column="url_ids" jdbcType="VARCHAR"/>
        <result property="urlName" column="url_name" jdbcType="VARCHAR"/>
        <result property="urlType" column="url_type" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,indicator_id, indicator_name, item_name, item_value, item_value1, item_value2, item_value3, item_value4,
        item_value5, item_value6,item_unit, item_unit_2nd,
        identify,style,is_fold,sequence,create_time,creater,update_time,updater,
        update_date,is_delete,current_flag,url_ids,url_type
    </sql>
    <sql id="where">
        <where>
            <if test="indicatorId != null and indicatorId != '' ">
                indicator_id = #{indicatorId}
            </if>
            <if test="delete != null">
                AND is_delete = #{delete}
            </if>
            <if test="currentFlag != null">
                AND current_flag = #{currentFlag}
            </if>
            <if test="updateDate != null">
                AND update_date = #{updateDate}
            </if>
        </where>
    </sql>
    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from general_indicator_data
        <include refid="where"/>
        order by sequence
    </select>

    <select id="listIndicatorDatas" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from general_indicator_data
        where
        <if test="indicatorId != null and indicatorId != '' ">
            indicator_id = #{indicatorId}
        </if>
        <if test="delete != null">
            AND is_delete = #{delete}
        </if>
        <if test="currentFlag != null">
            AND current_flag = #{currentFlag}
        </if>
        <if test="updateDate != null">
            AND subString(update_date,1,4) = #{updateDate}
        </if>
        order by sequence
    </select>

    <select id="listIndicatorYearDatas" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from general_indicator_data
        where
        <if test="indicatorId != null and indicatorId != '' ">
            indicator_id = #{indicatorId}
        </if>
        <if test="delete != null">
            AND is_delete = #{delete}
        </if>
        <if test="currentFlag != null">
            AND current_flag = #{currentFlag}
        </if>
        <if test="updateDate != null">
            AND subString(update_date,1,4) >= #{updateDate}-4 AND subString(update_date,1,4) &lt;= #{updateDate}
        </if>
        order by sequence
    </select>

    <select id="listShowDate" resultType="string">
        select update_date
        from general_indicator_data
        <include refid="where"/>
        group by update_date
    </select>

    <select id="getZhibiaoDataListByIndicatorId" resultType="com.onecity.os.data.attention.model.vo.AttentionZhiBiaoDataVo">
        SELECT
        item_name,item_value,item_unit,identify
        FROM general_indicator_data
        WHERE
        indicator_id=#{indicatorId}
        AND is_delete=0
        ORDER BY sequence
    </select>

    <select id="getYearDataList" resultType="com.onecity.os.data.indicator.model.po.GeneralIndicatorData">
        select * from general_indicator_data
        where indicator_id=#{indicatorId}
        AND is_delete=0
        AND current_flag=1
        <if test="startYear != null and endYear != null">
            AND subString(update_date,1,4) >= #{startYear} AND subString(update_date,1,4) &lt;= #{endYear}
        </if>
        <if test="startYear != null and endYear == null">
            AND subString(update_date,1,4) = #{startYear}
        </if>
        ORDER BY sequence
    </select>

    <select id="getMaxUpdateDateYear" resultType="java.lang.String">
        select MAX(subString(update_date,1,4)) maxUpdateDateYear from general_indicator_data
        where indicator_id=#{indicatorId}
        AND is_delete=0
        AND current_flag=1
    </select>

</mapper>