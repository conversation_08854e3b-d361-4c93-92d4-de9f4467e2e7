<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.indicator.mapper.GeneralIndicatorMapper">

    <resultMap type="com.onecity.os.data.indicator.model.po.GeneralIndicator" id="BaseResultMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="indicatorName" column="indicator_name" jdbcType="VARCHAR"/>
        <result property="indicatorExhibitType" column="indicator_exhibit_type" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="parentName" column="parent_name" jdbcType="VARCHAR"/>
        <result property="iconUrl" column="icon_url" jdbcType="VARCHAR"/>
        <result property="sourceId" column="source_id" jdbcType="VARCHAR"/>
        <result property="sourceName" column="source_name" jdbcType="VARCHAR"/>
        <result property="sequence" column="sequence" jdbcType="INTEGER"/>
        <result property="indicatorType" column="indicator_type" jdbcType="INTEGER"/>
        <result property="updateDate" column="update_date" jdbcType="VARCHAR"/>
        <result property="updateCycle" column="update_cycle" jdbcType="VARCHAR"/>
        <result property="leader" column="leader" jdbcType="VARCHAR"/>
        <result property="delete" column="is_delete" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creater" column="creater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="groupType" column="group_type" jdbcType="INTEGER"/>
        <result property="groupUrl" column="group_url" jdbcType="VARCHAR"/>
        <result property="isShow" column="is_show" jdbcType="INTEGER"/>
        <result property="isScreen" column="is_screen" jdbcType="INTEGER"/>
        <result property="isLegend" column="is_legend" jdbcType="INTEGER"/>
        <result property="dataUpdateMode" column="data_update_mode" jdbcType="INTEGER"/>
        <result property="dataConfigId" column="data_config_id" jdbcType="VARCHAR"/>
        <result property="urlIds" column="url_ids" jdbcType="VARCHAR"/>
        <result property="urlName" column="url_name" jdbcType="VARCHAR"/>
        <result property="urlType" column="url_type" jdbcType="VARCHAR"/>
        <result property="sortType" column="sort_type" jdbcType="VARCHAR"/>
        <result property="paraUrl" column="para_url" jdbcType="VARCHAR"/>
        <result property="nameShowFlag" column="name_show_flag" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, indicator_name, indicator_exhibit_type,  parent_id,
        parent_name, icon_url,source_id,source_name,sequence,indicator_type,update_date,
        update_cycle,leader,is_delete,create_time,creater,update_time,updater,
        group_type,group_url,is_show,is_screen,is_legend,data_update_mode,
        data_config_id,url_ids,url_type,url_name,
        sort_type,para_url,name_show_flag
    </sql>

    <sql id="Base_Column_List1">
        a.id as id,
        a.indicator_name as indicatorName,
        a.indicator_exhibit_type as indicatorExhibitType,
        a.parent_id as parentId,
        a.parent_name as parentName,
        a.icon_url as iconUrl,
        a.source_id as sourceId,
        a.source_name as sourceName,
        a.sequence as sequence,
        a.indicator_type as indicatorType,
        a.update_date as updateDate,
        a.update_cycle as updateCycle,
        a.leader as leader,
        a.is_delete as `delete`,
        a.create_time as createTime,
        a.creater as creater,
        a.update_time as updateTime,
        a.updater as updater,
        a.group_type as groupType,
        a.group_url as groupUrl,
        a.is_show as isShow,
        a.is_screen as isScreen,
        a.is_legend as isLegend,
        a.data_update_mode as dataUpdateMode,
        a.data_config_id as dataConfigId,
        b.source_name as sourceManageName,
        b.is_indicators as isIndicators,
        a.name_show_flag as nameShowFlag,
        a.url_ids as urlIds,
        a.url_name as urlName,
        a.url_type as urlType
    </sql>

    <sql id="Base_Column_List2">
        id as id,
        indicator_name as indicatorName,
        indicator_exhibit_type as indicatorExhibitType,
        parent_id as parentId,
        parent_name as parentName,
        icon_url as iconUrl,
        source_id as sourceId,
        source_name as sourceName,
        sequence as sequence,
        indicator_type as indicatorType,
        update_date as updateDate,
        update_cycle as updateCycle,
        leader as leader,
        is_delete as `delete`,
        create_time as createTime,
        creater as creater,
        update_time as updateTime,
        updater as updater,
        group_type as groupType,
        group_url as groupUrl,
        is_show as isShow,
        is_screen as isScreen,
        is_legend as isLegend,
        data_update_mode as dataUpdateMode,
        data_config_id as dataConfigId
    </sql>

    <sql id="where">
        <where>
            <if test="parentId != null and parentId != '' ">
                parent_id = #{parentId}
            </if>
            <if test="sourceId != null and sourceId != ''">
                AND source_id = #{sourceId}
            </if>
            <if test="delete != null">
                AND is_delete = #{delete}
            </if>
            <if test="isShow != null">
                AND is_show = #{isShow}
            </if>
            <if test="indicatorType != null">
                AND indicator_type = #{indicatorType}
            </if>
        </where>
    </sql>
    <select id="listIcon" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from general_indicator
        <include refid="where" />
        order by sequence
    </select>
    <select id="listTab" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from general_indicator
        <include refid="where" />
        order by sequence
    </select>
    <select id="listCoreIndicatorByParentId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from general_indicator
        <include refid="where" />
        order by sequence
    </select>

    <select id="getIndicatorListByParentId" resultType="com.onecity.os.data.indicator.model.po.GeneralIndicator">
        select * from general_indicator where parent_id = #{indicatorId} and is_delete=0 order by sequence
    </select>

    <select id="getInfoByIndicatorId" resultType="com.onecity.os.data.indicator.model.po.GeneralIndicator">
        select <include refid="Base_Column_List2" /> from general_indicator where id = #{indicatorId} and is_delete=0
    </select>

    <select id="getIndicatorInfoByIndicatorId" resultType="com.onecity.os.data.indicator.model.po.GeneralIndicator">
        select <include refid="Base_Column_List" />
        from general_indicator where id=#{indicatorId} and is_delete=0 and is_show=1
    </select>

    <select id="searchIndicatorBySourceSimpleNameAndName" resultType="com.onecity.os.data.indicator.model.vo.GeneralIndicatorSearchDataVo">
        select <include refid="Base_Column_List1" />
        from general_indicator a left join source_manage b on a.source_id = b.source_simple_name
        where
        a.source_id in
        <foreach collection="sourceSimpleNameList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and INSTR(a.indicator_name,#{indicatorName})
        and a.is_delete=0 and a.is_show=1
        order by b.source_name
    </select>
</mapper>