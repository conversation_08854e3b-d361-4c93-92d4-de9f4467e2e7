<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.noticeimportant.dao.ImportantReportAuditMapper">

    <select id="getList" resultType="com.onecity.os.data.noticeimportant.entity.ImportantReportAudit">
        SELECT * from important_report_audit where important_report_id = #{importantId} and audit_user_id = #{userId}
        order by audit_time DESC
    </select>
</mapper>