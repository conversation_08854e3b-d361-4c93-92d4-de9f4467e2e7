<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.indicator.mapper.IndicatorTitleMapper">

    <select id="queryById"
            resultType="com.onecity.os.data.indicator.model.po.GeneralIndicatorDataTitle">
        SELECT t.id,
               t.indicator_id    AS indicatorId,
               t.indicator_name  AS indicatorName,
               t.item_name       AS itemName,
               t.is_delete       AS isDelete,
               t.create_time     AS createTime,
               t.creator         AS creator,
               t.update_time     AS updateTime,
               t.updater         AS updater,
               t.item_value_name AS itemValueName,
               t.item_name_name  AS itemNameName,
               t.main_value      AS mainValue,
               t.column_name     AS columnName,
               t.sequence        AS sequence
        FROM general_indicator_data_title AS t
        WHERE 1 = 1
          AND t.is_delete = 0
          AND t.indicator_id = #{indicatorId}
        ORDER BY t.sequence
    </select>
</mapper>





















