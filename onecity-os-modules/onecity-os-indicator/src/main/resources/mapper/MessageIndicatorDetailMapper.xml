<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.indicator.mapper.MessageIndicatorDetailMapper">

    <resultMap type="com.onecity.os.data.indicator.model.po.MessageIndicatorDetail" id="BaseResultMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="indicatorId" column="indicator_id" jdbcType="VARCHAR"/>
        <result property="indicatorName" column="indicator_name" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="LONGVARCHAR"/>
        <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creater" column="creater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, indicator_id, indicator_name, content,file_url,
        update_date,is_delete,create_time,creater,update_time,updater
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from message_indicator_detail
        where id = #{id} and is_delete=0;
    </select>
    <select id="selectByIndicatorId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from message_indicator_detail
        where indicator_id = #{indicatorId} and is_delete=0
        order by update_date desc
    </select>
    <select id="countByIndicatorId" resultType="int">
        select count(*)
        from message_indicator_detail
        where indicator_id = #{indicatorId} and is_delete=0;
    </select>
</mapper>