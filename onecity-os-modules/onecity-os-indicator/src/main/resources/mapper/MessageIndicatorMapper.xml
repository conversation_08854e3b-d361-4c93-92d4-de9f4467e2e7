<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.indicator.mapper.MessageIndicatorMapper">

    <resultMap type="com.onecity.os.data.indicator.model.po.MessageIndicator" id="BaseResultMap">
        <result property="indicatorId" column="indicator_id" jdbcType="VARCHAR"/>
        <result property="indicatorName" column="indicator_name" jdbcType="VARCHAR"/>
        <result property="sourceName" column="source_name" jdbcType="VARCHAR"/>
        <result property="sourceId" column="source_id" jdbcType="VARCHAR"/>
        <result property="sequence" column="sequence" jdbcType="INTEGER"/>
        <result property="categoryName" column="category_name" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="VARCHAR"/>
        <result property="updateCycle" column="update_cycle" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="typeName" column="type_name" jdbcType="VARCHAR"/>
        <result property="delete" column="is_delete" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creater" column="creater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="topUpdateTime" column="top_update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id indicator_id, indicator_name, source_id,source_name,sequence,category_name,update_date,
        update_cycle,type,is_delete,create_time,creater,update_time,updater,top_update_time
    </sql>
    <sql id="where">
        <where>
            <if test="param.sourceId != null and param.sourceId != '' ">
                source_id = #{param.sourceId}
            </if>
            <if test="param.type != null">
                AND type = #{param.type}
            </if>
            <if test="param.delete != null">
                AND is_delete = #{param.delete}
            </if>
            ORDER BY sequence DESC,top_update_time DESC,update_time DESC
        </where>
    </sql>
    <select id="list" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from message_indicator
        <include refid="where" />
    </select>
    <select id="listTab" resultMap="BaseResultMap">
        select a.type,b.item_text type_name from message_indicator a
        left join sys_dict_item b on a.type=b.item_value
        left join sys_dict c on b.dict_id=c.id
        where c.dict_code='message_indicator_type'
        and source_id=#{param.sourceId}
        group by type order by b.sort_order;
    </select>

    <select id="listTab1" resultMap="BaseResultMap">
        select * from message_indicator
        where source_id=#{param.sourceId}
        and is_delete = #{param.delete}
        group by type ;
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from message_indicator
        where id=#{id} and is_delete=0
    </select>
</mapper>