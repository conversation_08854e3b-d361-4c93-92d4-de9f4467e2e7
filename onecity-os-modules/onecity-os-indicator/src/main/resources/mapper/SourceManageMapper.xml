<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.indicator.mapper.SourceManageMapper">

    <resultMap type="com.onecity.os.data.indicator.model.po.SourceManage" id="BaseResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="sourceId" column="source_id" jdbcType="VARCHAR"/>
        <result property="sourceName" column="source_name" jdbcType="VARCHAR"/>
        <result property="iconUrl" column="icon_url" jdbcType="VARCHAR"/>
        <result property="sourceSimpleName" column="source_simple_name" jdbcType="VARCHAR"/>
        <result property="sequence" column="sequence" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creater" column="creater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="isIndicators" column="is_indicators" jdbcType="INTEGER"/>
        <result property="urlIds" column="url_ids" jdbcType="VARCHAR"/>
        <result property="urlName" column="url_name" jdbcType="VARCHAR"/>
        <result property="urlType" column="url_type" jdbcType="VARCHAR"/>
        <result property="firstGroupFlag" column="first_group_flag" jdbcType="VARCHAR"/>
        <result property="secondGroupFlag" column="second_group_flag" jdbcType="VARCHAR"/>
        <result property="appReportInfoFlag" column="app_report_info_flag" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, source_id as sourceId, source_name as sourceName, icon_url as iconUrl,source_simple_name as sourceSimpleName,
        sequence,create_time as createTime,creater,update_time as updateTime,updater,is_delete as isDelete,`type` as `type`,is_indicators as isIndicators,
        url_ids as urlIds,url_name as urlName,url_type as urlType,first_group_flag as firstGroupFlag,second_group_flag as secondGroupFlag,
        app_report_info_flag as appReportInfoFlag
    </sql>
    <update id="createIndicatorTable" parameterType="com.onecity.os.data.indicator.model.dto.SourceTableDTO">
        CREATE TABLE ${generalIndicatorTableName} (
          `id` varchar(255) NOT NULL COMMENT '指标ID',
          `indicator_name` varchar(255) DEFAULT NULL COMMENT '指标名称',
          `indicator_exhibit_type` varchar(255) DEFAULT NULL COMMENT '指标展现类型',
          `parent_id` varchar(255) DEFAULT NULL COMMENT '父指标ID,如果为一级指标, 该字段为空',
          `parent_name` varchar(255) DEFAULT NULL COMMENT '父指标名称,如果为一级指标, 该字段为空',
          `icon_url` varchar(255) DEFAULT NULL COMMENT '图标地址',
          `source_id` varchar(16) DEFAULT NULL COMMENT '数据来源id',
          `source_name` varchar(255) DEFAULT NULL COMMENT '数据来源',
          `sequence` int(11) DEFAULT NULL COMMENT '排序',
          `indicator_type` int(11) DEFAULT '0' COMMENT '指标类型，0：指标，1：tab类型',
          `update_date` varchar(100) DEFAULT NULL COMMENT '更新日期文本类型',
          `update_cycle` varchar(45) DEFAULT NULL COMMENT '更新周期',
          `is_delete` int(1) DEFAULT '0' COMMENT '是否删除0:否1:是',
          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
          `creater` varchar(255) DEFAULT NULL COMMENT '创建人',
          `update_time` datetime DEFAULT NULL COMMENT '更新时间',
          `updater` varchar(255) DEFAULT NULL COMMENT '更新人',
          PRIMARY KEY (`id`)
        );
        CREATE TABLE ${generalIndicatorDataTableName} (
          `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键自增',
          `indicator_id` varchar(255) DEFAULT NULL COMMENT '指标Id',
          `indicator_name` varchar(255) DEFAULT NULL COMMENT '指标名称',
          `item_name` varchar(255) DEFAULT NULL COMMENT '指标项目',
          `item_value` varchar(1000) DEFAULT NULL COMMENT '指标项目值',
          `item_unit` varchar(10) DEFAULT NULL COMMENT '单位',
          `identify` varchar(255) DEFAULT NULL COMMENT '用来表示指标数据是增加还是减少，前端显示不同的颜色',
          `style` int(11) DEFAULT '0' COMMENT '指标文字展示方式，0：平铺；1：加横线',
          `is_fold` int(11) DEFAULT '0' COMMENT '是否折行0：否1：是',
          `sequence` int(11) DEFAULT NULL COMMENT '排序',
          `update_date` varchar(255) DEFAULT NULL COMMENT '更新日期',
          `current_flag` int(11) DEFAULT '0' COMMENT '是否当前展示0：是1：否',
          `is_delete` int(1) DEFAULT '0' COMMENT '是否删除0:否1:是',
          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
          `creater` varchar(255) DEFAULT NULL COMMENT '创建人',
          `update_time` datetime DEFAULT NULL COMMENT '更新时间',
          `updater` varchar(255) DEFAULT NULL COMMENT '更新人',
          PRIMARY KEY (`id`)
        );
        CREATE TABLE ${messageIndicatorTableName} (
          `id` varchar(255) NOT NULL COMMENT '指标ID',
          `indicator_name` varchar(255) DEFAULT NULL COMMENT '指标名称',
          `sequence` int(11) DEFAULT NULL COMMENT '排序',
          `parent_id` varchar(255) DEFAULT NULL COMMENT '父级指标id',
          `category_name` varchar(255) DEFAULT NULL COMMENT '类别名称',
          `source_id` varchar(16) DEFAULT NULL COMMENT '数据来源id',
          `source_name` varchar(255) DEFAULT NULL COMMENT '数据来源',
          `update_date` varchar(100) DEFAULT NULL COMMENT '更新日期文本类型',
          `update_cycle` varchar(45) DEFAULT NULL COMMENT '更新周期',
          `is_delete` int(1) DEFAULT '0' COMMENT '是否删除0:否1:是',
          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
          `creater` varchar(255) DEFAULT NULL COMMENT '创建人',
          `update_time` datetime DEFAULT NULL COMMENT '更新时间',
          `updater` varchar(255) DEFAULT NULL COMMENT '更新人',
          PRIMARY KEY (`id`)
        );
        CREATE TABLE ${messageIndicatorDetailTableName} (
          `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键自增',
          `indicator_id` varchar(255) DEFAULT NULL COMMENT '指标Id',
          `indicator_name` varchar(255) DEFAULT NULL COMMENT '指标名称',
          `content` text COMMENT '内容详情',
          `update_date` varchar(32) DEFAULT NULL COMMENT '更新日期',
          `file_url` varchar(255) DEFAULT NULL COMMENT '文件地址',
          `is_delete` int(1) DEFAULT '0' COMMENT '是否删除0:否1:是',
          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
          `creater` varchar(255) DEFAULT NULL COMMENT '创建人',
          `update_time` datetime DEFAULT NULL COMMENT '更新时间',
          `updater` varchar(255) DEFAULT NULL COMMENT '更新人',
          PRIMARY KEY (`id`)
        );
    </update>

    <select id="selectSourceManageList" resultType="com.onecity.os.data.indicator.model.po.SourceManage">
        select <include refid="Base_Column_List"/> from source_manage
        where is_delete = 0 and is_start = 1 AND b.is_indicators != 4
        order by sequence
    </select>

    <select id="selectSourceManageListByType" resultType="com.onecity.os.data.indicator.model.po.SourceManage">
        select <include refid="Base_Column_List"/> from source_manage
        where is_delete = 0 and is_start = 1 and `type` = #{type} AND b.is_indicators != 4
        order by sequence
    </select>

    <select id="getSourceManageBySourceSimpleName" resultType="com.onecity.os.data.indicator.model.po.SourceManage">
        select <include refid="Base_Column_List"/> from source_manage
        where is_delete = 0 and is_start = 1 and source_simple_name = #{sourceSimpleName}
        limit 1
    </select>


    <select id="getSourceSimpleNameByRoleIds" resultType="java.lang.String">
        SELECT
        distinct(b.source_simple_name) AS sourceSimpleName
        FROM
        role_source a
        LEFT JOIN source_manage b ON b.id = a.source_id
        WHERE
        a.role_id in
        <foreach collection="roleIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND b.is_delete = 0
        AND b.is_start = 1
        AND b.is_indicators in (1,5)
    </select>

    <select id="getMenuListByRoleIds" resultType="com.onecity.os.data.indicator.model.dto.GetMenuListDto">
        SELECT
             distinct(a.source_id) AS id,
            b.source_name AS sourceName,
            b.source_simple_name AS sourceSimpleName,
            b.icon_url AS iconUrl,
            b.type AS type,
            b.sequence AS sequence,
            b.is_indicators AS isIndicators,
            b.url_ids as urlIds,
            b.url_name as urlName,
            b.url_type as urlType,
            b.first_group_flag as firstGroupFlag,
            b.second_group_flag as secondGroupFlag,
            b.app_report_info_flag as appReportInfoFlag
        FROM
            role_source a
            LEFT JOIN source_manage b ON b.id = a.source_id
        WHERE
            a.role_id in
            <foreach collection="roleIds" item="id" open="(" separator="," close=")">
                    #{id}
            </foreach>
            AND b.is_delete = 0
            AND b.is_start = 1
            AND b.is_indicators != 4
        ORDER BY
            b.sequence
    </select>

    <select id="getMenuListByUserId" resultType="com.onecity.os.data.indicator.model.dto.GetMenuListDto">
        SELECT
            a.source_id AS id,
            b.source_name AS sourceName,
            b.source_simple_name AS sourceSimpleName,
            b.icon_url AS iconUrl,
            b.type AS type,
            b.sequence AS sequence,
            b.is_indicators AS isIndicators
        FROM
            dingmail_user_menu a
            LEFT JOIN source_manage b ON b.id = a.source_id
        WHERE
            a.user_id = #{userId}
            AND b.is_delete = 0
            AND b.is_start = 1
            AND b.is_indicators != 4
        ORDER BY
            b.sequence
    </select>

    <select id="getMenuListByUserIdAndType" resultType="com.onecity.os.data.indicator.model.dto.GetMenuListDto">
        SELECT
            a.source_id AS id,
            b.source_name AS sourceName,
            b.source_simple_name AS sourceSimpleName,
            b.icon_url AS iconUrl,
            b.type AS type,
            b.is_indicators AS isIndicators
        FROM
            dingmail_user_menu a
            LEFT JOIN source_manage b ON b.id = a.source_id
        WHERE
            a.user_id = #{userId}
            AND b.is_delete = 0
            AND b.is_start = 1
            AND b.type = #{type}
            AND b.is_indicators != 4
        ORDER BY
            b.sequence
    </select>
    <select id="getAllMenuListByUserId" resultType="com.onecity.os.data.indicator.model.dto.GetMenuListDto">
        SELECT
            a.source_id AS id,
            b.source_name AS sourceName,
            b.source_simple_name AS sourceSimpleName,
            b.icon_url AS iconUrl,
            b.type AS type,
            b.is_indicators AS isIndicators
        FROM
            dingmail_user_menu a
            LEFT JOIN source_manage b ON b.id = a.source_id
        WHERE
            a.user_id = #{userId}
            AND b.is_delete = 0
            AND b.is_start = 1
            AND b.type != #{type}
            AND b.is_indicators != 4
        ORDER BY
            b.sequence
    </select>

    <select id="getMenuListByRoleIdAndType" resultType="com.onecity.os.data.indicator.model.dto.GetMenuListDto">
        SELECT
            distinct(a.source_id) AS id,
            b.source_name AS sourceName,
            b.source_simple_name AS sourceSimpleName,
            b.icon_url AS iconUrl,
            b.type AS type,
            b.is_indicators AS isIndicators,
            b.url_ids as urlIds,
            b.url_name as urlName,
            b.url_type as urlType,
            b.first_group_flag as firstGroupFlag,
            b.second_group_flag as secondGroupFlag,
            b.app_report_info_flag as appReportInfoFlag
        FROM
            role_source a
            LEFT JOIN source_manage b ON b.id = a.source_id
        WHERE
            a.role_id in
            <foreach collection="roleIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            AND b.is_delete = 0
            AND b.is_start = 1
            AND b.type = #{type}
            AND b.is_indicators != 4
        ORDER BY
            b.sequence
    </select>

    <select id="getAllMenuListByRoleId" resultType="com.onecity.os.data.indicator.model.dto.GetMenuListDto">
        SELECT
        distinct(a.source_id) AS id,
        b.source_name AS sourceName,
        b.source_simple_name AS sourceSimpleName,
        b.icon_url AS iconUrl,
        b.type AS type,
        b.is_indicators AS isIndicators,
        b.url_ids as urlIds,
        b.url_name as urlName,
        b.url_type as urlType,
        b.first_group_flag as firstGroupFlag,
        b.second_group_flag as secondGroupFlag,
        b.app_report_info_flag as appReportInfoFlag
        FROM
        role_source a
        LEFT JOIN source_manage b ON b.id = a.source_id
        WHERE
        a.role_id in
        <foreach collection="roleIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND b.is_delete = 0
        AND b.is_start = 1
        AND b.type != #{type}
        AND b.is_indicators != 4
        ORDER BY
        b.sequence
    </select>

</mapper>