<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.notice.mapper.SpecialCommentMapper">

    <select id="selectByReceiverid" resultType="com.onecity.os.data.notice.entity.po.SpecialComment">
        select * from specialcomment where filemanageid=#{filemanageid} and receiverid=#{receiverid}
    </select>

    <select id="selectBySender" resultType="com.onecity.os.data.notice.entity.po.SpecialComment">
        select * from specialcomment where adminid=#{adminid} order by writetime desc
    </select>

    <select id="selectByFile" resultType="com.onecity.os.data.notice.entity.po.SpecialComment">
        select * from specialcomment where filemanageid=#{filemanageid} order by writetime desc
    </select>

    <insert id="save" parameterType="com.onecity.os.data.notice.entity.po.SpecialComment" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO specialcomment ( id, adminid, filemanageid, receiver, receiverid, commenttitle, commentcontent, writetime )
        VALUES
            (#{id}, #{adminid},#{filemanageid},#{receiver}, #{receiverid}, #{commenttitle}, #{commentcontent}, #{writetime})
    </insert>

    <update id="updateFileadminIsWrite"  >
        update specialfileadmin set iswrite=1  where id=#{adminid}
    </update>

    <update id="updateFilemanageIsWrite"  >
        update specialfilemanage set iswrite=1  where id=#{filemanageid}
    </update>

    <update id="updateIsWriteAndRead">
        update  specialfilereceiver set iswrite=1,isread=1 where receiverid=#{receiverid} and fileid=#{fileid}
    </update>
</mapper>