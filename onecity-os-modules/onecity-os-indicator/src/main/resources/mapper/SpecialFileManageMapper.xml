<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.notice.mapper.SpecialFileManageMapper">

    <resultMap id="DingFileAdminVoMap" type="com.onecity.os.data.noticeimportant.entity.vo.DingFileAdminVo">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="createtime" property="createtime" jdbcType="VARCHAR"/>
        <result column="iswrite" property="iswrite" jdbcType="INTEGER"/>
        <collection property="fileList" resultMap="fileListMap"/>
        <collection property="receiverList" resultMap="receiverListMap"/>
    </resultMap>
    <resultMap id="fileListMap" type="com.onecity.os.data.noticeimportant.entity.po.DingFileManage">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="adminid" property="adminid" jdbcType="INTEGER"/>
        <result column="spaceId" property="spaceId" jdbcType="VARCHAR"/>
        <result column="fileId" property="fileId" jdbcType="VARCHAR"/>
        <result column="fileName" property="fileName" jdbcType="VARCHAR"/>
        <result column="fileType" property="fileType" jdbcType="VARCHAR"/>
        <result column="fileSize" property="fileSize" jdbcType="INTEGER"/>
        <result column="iswrite" property="iswrite" jdbcType="INTEGER"/>
    </resultMap>
    <resultMap id="receiverListMap" type="String">
        <result column="receiver" property="receiver" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectListByUserid" resultType="com.onecity.os.data.noticeimportant.entity.vo.DingFileManageVo">
    SELECT
      a.id,
      a.adminid,
      a.fileName,
      a.fileId,
      a.fileSize,
      a.fileType,
      a.spaceId,
      c.department,
      c.creater,
      c.createtime,
      b.isread,
      b.iswrite
    FROM
	  specialfilemanage a
      LEFT JOIN specialfileadmin c ON a.adminid = c.id
      LEFT JOIN specialfilereceiver b ON a.id = b.fileid
    WHERE
	    b.receiverid = #{userid}  ORDER BY c.createtime desc,b.isread desc,b.iswrite desc
  </select>

    <select id="selectQueryListByRecevierId"
            resultType="com.onecity.os.data.noticeimportant.entity.vo.DingFileManageVo">
        SELECT
        a.id,
        a.adminid,
        a.fileName,
        a.fileId,
        a.fileSize,
        a.fileType,
        a.spaceId,
        c.department,
        c.creater,
        c.createtime,
        b.isread,
        b.iswrite
        FROM
        specialfilemanage a
        LEFT JOIN specialfileadmin c ON a.adminid = c.id
        LEFT JOIN specialfilereceiver b ON a.id = b.fileid
        WHERE
        b.receiverid = #{userid}
        <if test="creater != null and creater != ''">and INSTR(c.creater,#{creater})</if>
        <if test="startdate != null and startdate != ''">and c.createtime >= #{startdate}</if>
        <if test="enddate != null and enddate != ''">and c.createtime &lt;= #{enddate}</if>
        <if test="filename != null and filename != ''">and INSTR(a.filename,#{filename})</if>
        ORDER BY c.createtime desc,b.isread desc,b.iswrite desc
    </select>

    <select id="selectQueryListByCreaterId" resultType="com.onecity.os.data.notice.entity.po.SpecialReportFileAdmin">
        SELECT
        DISTINCT c.*
        FROM specialfileadmin c
        LEFT JOIN specialfilemanage a ON a.adminid = c.id
        LEFT JOIN specialfilereceiver b ON a.id = b.fileId
        WHERE
        c.createrid = #{userid}
        <if test="receiver != null and receiver != ''">and INSTR(b.receiver,#{receiver})</if>
        <if test="startdate != null and startdate != ''">and c.createtime >= #{startdate}</if>
        <if test="enddate != null and enddate != ''">and c.createtime &lt;= #{enddate}</if>
        <if test="filename != null and filename != ''">and INSTR(a.filename,#{filename})</if>
        AND c.is_delete = 0
        ORDER BY c.createtime desc,c.iswrite desc
    </select>

    <select id="selectListByAdminid" resultType="com.onecity.os.data.notice.entity.po.SpecialReportFileManage">
    SELECT
	    *
    FROM
	    specialfilemanage
    WHERE
	    adminid = #{adminid}
  </select>

    <select id="selectById" resultType="com.onecity.os.data.notice.entity.po.SpecialReportFileManage">
    SELECT
        *
    FROM
        specialfilemanage
    WHERE
        id = #{id}
  </select>

    <insert id="save" parameterType="com.onecity.os.data.notice.entity.po.SpecialReportFileManage"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    INSERT INTO specialfilemanage ( id, adminid, spaceId, fileId, fileName, fileType, fileSize )
    VALUES
	    (#{id}, #{adminid},#{spaceId}, #{fileId}, #{fileName}, #{fileType}, #{fileSize})
  </insert>

    <select id="getSendListByUserId" resultMap="DingFileAdminVoMap">
        SELECT
        DISTINCT c.*,
        a.*,
        b.receiver AS receiver
        FROM specialfileadmin c
        LEFT JOIN specialfilemanage a ON a.adminid = c.id
        LEFT JOIN specialfilereceiver b ON a.id = b.fileId
        WHERE
        c.createrid = #{userid}
        <if test="receiver != null and receiver != ''">and INSTR(b.receiver,#{receiver})</if>
        <if test="startdate != null and startdate != ''">and c.createtime >= #{startdate}</if>
        <if test="enddate != null and enddate != ''">and c.createtime &lt;= #{enddate}</if>
        <if test="filename != null and filename != ''">and INSTR(a.filename,#{filename})</if>
        ORDER BY c.createtime desc,c.iswrite
    </select>

    <select id="getNoticePageList" parameterType="com.onecity.os.data.notice.entity.vo.NoticeManage"
            resultType="com.onecity.os.data.notice.entity.vo.NoticeManage">
        SELECT m.id,
               m.title,
               m.public_source,
               m.public_date,
               m.create_time,
               m.update_time,
               m.`status`,
               m.content,
               m.file_name,
               m.file_path,
               m.read_count
        FROM notice_manage m
        WHERE m.is_delete = 0
        ORDER BY m.create_time DESC, m.public_date DESC
    </select>

    <select id="getNoticeUserMsg" resultType="com.onecity.os.data.notice.entity.vo.NoticeUserIsRead">
        SELECT msg.id,
               msg.user_id,
               msg.notice_id,
               msg.creater,
               msg.create_time,
               msg.updater,
               msg.update_time
        FROM notice_user_is_read msg
        WHERE 1 = 1
          AND msg.notice_id = #{noticeId}
          AND msg.user_id = #{userid}
    </select>

</mapper>