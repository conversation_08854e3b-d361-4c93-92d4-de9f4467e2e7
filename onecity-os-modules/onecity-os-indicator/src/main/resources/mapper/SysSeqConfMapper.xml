<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.indicator.mapper.SysSeqConfMapper">

    <resultMap type="com.onecity.os.data.indicator.model.po.SysSeqConf" id="BaseResultMap">
        <id column="SEQ_ID" property="seqId" jdbcType="VARCHAR"/>
        <result column="SEQ_CURRENT" property="seqCurrent" jdbcType="BIGINT"/>
        <result column="SEQ_MAXIMUM" property="seqMaximum" jdbcType="BIGINT"/>
    </resultMap>
    <sql id="Base_Column_List">
       SEQ_ID, SEQ_CURRENT, SEQ_MAXIMUM
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from sys_seq_conf
        where SEQ_ID = #{seqId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sys_seq_conf
    where SEQ_ID = #{seqId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="com.onecity.os.data.indicator.model.po.SysSeqConf">
    insert into sys_seq_conf (SEQ_ID, SEQ_CURRENT, SEQ_MAXIMUM
      )
    values (#{seqId,jdbcType=VARCHAR}, #{seqCurrent,jdbcType=BIGINT}, #{seqMaximum,jdbcType=BIGINT}
      )
  </insert>
    <insert id="insertSelective" parameterType="com.onecity.os.data.indicator.model.po.SysSeqConf">
        insert into sys_seq_conf
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="seqId != null">
                SEQ_ID,
            </if>
            <if test="seqCurrent != null">
                SEQ_CURRENT,
            </if>
            <if test="seqMaximum != null">
                SEQ_MAXIMUM,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="seqId != null">
                #{seqId,jdbcType=VARCHAR},
            </if>
            <if test="seqCurrent != null">
                #{seqCurrent,jdbcType=BIGINT},
            </if>
            <if test="seqMaximum != null">
                #{seqMaximum,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.onecity.os.data.indicator.model.po.SysSeqConf">
        update sys_seq_conf
        <set>
            <if test="seqCurrent != null">
                SEQ_CURRENT = #{seqCurrent,jdbcType=BIGINT},
            </if>
            <if test="seqMaximum != null">
                SEQ_MAXIMUM = #{seqMaximum,jdbcType=BIGINT},
            </if>
        </set>
        where SEQ_ID = #{seqId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.onecity.os.data.indicator.model.po.SysSeqConf">
    update sys_seq_conf
    set SEQ_CURRENT = #{seqCurrent,jdbcType=BIGINT},
      SEQ_MAXIMUM = #{seqMaximum,jdbcType=BIGINT}
    where SEQ_ID = #{seqId,jdbcType=VARCHAR}
  </update>
</mapper>