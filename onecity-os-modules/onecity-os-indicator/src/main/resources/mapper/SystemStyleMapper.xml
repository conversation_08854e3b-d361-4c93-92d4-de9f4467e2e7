<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.systemstyle.mapper.SystemStyleMapper">
    
    <resultMap type="com.onecity.os.data.systemstyle.domain.SystemStyle" id="SystemStyleResult">
        <result property="id"    column="id"    />
        <result property="style"    column="style"    />
        <result property="pcName"    column="pc_name"    />
        <result property="appName"    column="app_name"    />
        <result property="appSourceNameFlag"    column="app_source_name_flag"    />
        <result property="appUpdateCycleFlag"    column="app_update_cycle_flag"    />
        <result property="appTopFlag"    column="app_top_flag"    />
        <result property="appFirstPageFlag"    column="app_first_page_flag"    />
        <result property="appBackFlag"    column="app_back_flag"    />
        <result property="appWaterFlag"    column="app_water_flag"    />
    </resultMap>

    <sql id="selectSystemStyleVo">
        select id, style, pc_name, app_name, app_source_name_flag, app_update_cycle_flag,
          app_top_flag,app_first_page_flag,app_back_flag,app_water_flag  from system_style
    </sql>

    <select id="selectSystemStyleById"  resultMap="SystemStyleResult">
        <include refid="selectSystemStyleVo"/>
        LIMIT 1
    </select>

</mapper>