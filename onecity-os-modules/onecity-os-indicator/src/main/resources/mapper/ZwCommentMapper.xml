<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.zwmessage.mapper.ZwCommentMapper">

    <select id="selectByReceiverid" resultType="com.onecity.os.data.zwmessage.model.po.ZwComment">
        select * from zw_comment where filemanageid=#{filemanageid} and receiverid=#{receiverid}
    </select>

    <select id="selectBySender" resultType="com.onecity.os.data.zwmessage.model.vo.ZwCommentVo">
        select a.*,b.title from
        zw_comment a
        LEFT JOIN zw_message b ON a.adminid = b.id
        where adminid=#{adminid} order by writetime desc
    </select>

    <select id="selectByFile" resultType="com.onecity.os.data.zwmessage.model.po.ZwComment">
        select * from zw_comment where filemanageid=#{filemanageid} order by writetime desc
    </select>

    <insert id="save" parameterType="com.onecity.os.data.zwmessage.model.po.ZwComment" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO zw_comment ( id, adminid, filemanageid, receiver, receiverid, commenttitle, commentcontent, writetime, iswrite )
        VALUES
            (#{id}, #{adminid},#{filemanageid},#{receiver}, #{receiverid}, #{commenttitle}, #{commentcontent}, #{writetime}, #{iswrite})
    </insert>

    <update id="updateFileadminIsWrite"  >
        update zw_message set iswrite=1  where id=#{adminid}
    </update>

    <delete id="deleteCommentByAdminid">
        DELETE FROM zw_comment where zw_comment.adminid=#{adminid}
    </delete>
</mapper>