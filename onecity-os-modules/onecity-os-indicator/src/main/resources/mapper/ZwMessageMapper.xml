<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.zwmessage.mapper.ZwMessageMapper">

    <select id="selectZwList" resultType="com.onecity.os.data.zwmessage.model.vo.ZwMessageVo">
        SELECT
        a.id,
        a.title,
        a.issue,
        a.type,
        a.createtime,
        a.createname,
        a.createid,
        a.fileid filemanageid,
        IFNULL(d.iswrite, 0) iswrite,
        b.imgurl,
        c.fileId,
        c.spaceId
        FROM
        zw_message a
        LEFT JOIN zw_imageinfo b ON a.imgid = b.id
        LEFT JOIN zw_fileinfo c ON a.fileid = c.id
        LEFT JOIN zw_comment d ON a.id=d.adminid
        <if test="userid !=null and userid != ''">and d.receiverid=#{userid}</if>
        <where>
            1=1
            <if test="title != null and title != ''">and INSTR(a.title,#{title})</if>
            <if test="startdate != null and startdate != ''">and a.createtime &gt;= #{startdate}</if>
            <if test="enddate != null and enddate != ''">and a.createtime &lt;= #{enddate}</if>
            <if test="type != null and type != ''">and a.type = #{type}</if>
        </where>
        ORDER BY
        a.createtime desc
    </select>

    <select id="selectMessageByCreateid" resultType="com.onecity.os.data.zwmessage.model.vo.ZwMessageVo">
        SELECT
        a.id,
        a.title,
        a.issue,
        a.type,
        a.createtime,
        a.createname,
        a.createid,
        a.fileid filemanageid,
        a.iswrite,
        b.imgurl,
        c.fileId,
        c.spaceId
        FROM
        zw_message a
        LEFT JOIN zw_imageinfo b ON a.imgid = b.id
        LEFT JOIN zw_fileinfo c ON a.fileid = c.id
        WHERE
        1=1
        <if test="userid != null and userid != ''">and a.createid = #{userid}</if>
        AND is_delete = 0
        ORDER BY a.createtime desc
    </select>
    <select id="selectMessageByType" resultType="com.onecity.os.data.zwmessage.model.vo.ZwMessageVo">
        SELECT
        a.id,
        a.title,
        a.issue,
        a.createtime,
        a.createid,
        a.createname,
        a.fileid filemanageid,
        b.imgurl,
        c.fileId,
        c.spaceId,
        c.fileName,
        c.fileSize,
        c.fileType,
        IFNULL(d.iswrite, 0) iswrite
        FROM
        zw_message a
        LEFT JOIN zw_imageinfo b ON a.imgid = b.id
        LEFT JOIN zw_fileinfo c ON a.fileid = c.id
        LEFT JOIN zw_comment d ON a.id=d.adminid
        WHERE
        1=1
        <if test="type != null and type != ''">and a.type = #{type}</if>
        ORDER BY a.createtime desc
    </select>
    <select id="selectMessageByTypeAndUserid" resultType="com.onecity.os.data.zwmessage.model.vo.ZwMessageVo">
        SELECT
        a.id,
        a.title,
        a.issue,
        a.createtime,
        a.createid,
        a.createname,
        a.fileid filemanageid,
        b.imgurl,
        c.fileId,
        c.spaceId,
        c.fileName,
        c.fileSize,
        c.fileType,
        IFNULL(d.iswrite, 0) iswrite
        FROM
        zw_message a
        LEFT JOIN zw_imageinfo b ON a.imgid = b.id
        LEFT JOIN zw_fileinfo c ON a.fileid = c.id
        LEFT JOIN zw_comment d ON a.id=d.adminid
        <if test="userid !=null and userid != ''">and d.receiverid=#{userid}</if>
        WHERE
        1=1
        <if test="type != null and type != ''">and a.type = #{type}</if>
        ORDER BY a.createtime desc
    </select>
    <select id="selectMessageById" resultType="com.onecity.os.data.zwmessage.model.vo.ZwDelMsgVo">
        SELECT
        a.id,
        b.imgurl,
        a.imgid,
        a.fileid
        FROM
        zw_message a
        LEFT JOIN zw_imageinfo b ON a.imgid = b.id
        LEFT JOIN zw_fileinfo c ON a.fileid = c.id
        WHERE
        1=1
        <if test="msgid != null and msgid != ''">and a.id = #{msgid}</if>
        ORDER BY a.createtime desc
    </select>


    <insert id="save">
    INSERT INTO zw_message ( title, issue, imgflag, type, imgid, fileid, createtime, createid, createname )
VALUES
	(#{title}, #{issue}, #{imgflag}, #{type},#{imgid}, #{fileid}, #{createtime}, #{createid}, #{createname})
  </insert>

    <select id="getFileAdminById" resultType="com.onecity.os.data.zwmessage.model.po.ZwMessage">
    SELECT * from zw_message where id = #{id} and is_delete = 0
  </select>

    <update id="deleteRecorde">
    UPDATE zw_message set is_delete = 1  where id = #{id}
  </update>

    <select id="getFileAdminByTitle" resultType="com.onecity.os.data.zwmessage.model.po.ZwMessage">
        SELECT * FROM zw_message a
        WHERE
        1=1
        <if test="title != null and title != ''">and a.title = #{title}</if>
        <if test="type != null and type != ''">and a.type = #{type}</if>
    </select>

    <select id="getFileAdminByIssue" resultType="com.onecity.os.data.zwmessage.model.po.ZwMessage">
        SELECT * FROM zw_message a
        WHERE
        1=1
        <if test="issue != null and issue != ''">and a.issue = #{issue}</if>
        <if test="type != null and type != ''">and a.type = #{type}</if>
    </select>

    <select id="getFileAdminId" resultType="com.onecity.os.data.zwmessage.model.po.ZwMessage">
    SELECT * FROM zw_message WHERE id = #{id}
  </select>

</mapper>