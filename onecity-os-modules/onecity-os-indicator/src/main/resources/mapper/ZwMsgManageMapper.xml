<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.data.zwmessage.mapper.ZwMsgManageMapper">


    <select id="getListByType" resultType="com.onecity.os.data.zwmessage.model.po.ZwMsgManage">
    SELECT * from zw_msg_manage where type = #{type} and is_delete = 0
    order by create_time DESC
  </select>
</mapper>