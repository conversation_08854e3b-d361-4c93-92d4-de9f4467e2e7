# 基础镜像
#DEV
#FROM local.harbor.com/library/openjdk:8-jre-bullseye
#onescreenv2-dev
FROM image.onecode.cmict.cloud/library/openjdk:8-jre
# 挂载目录
VOLUME /tmp
# 复制jar文件到路径
COPY target/onecity-os-inform.jar /onecity-os-inform.jar
# 创建目录
RUN mkdir -p /opt/logs/onecity-os-inform/
CMD ["java","-Djava.boss.egd=file:/dev/./urandom","-Duser.timezone=GMT+08","-server","-Xms1024m","-Xmx1024m","-jar","/onecity-os-inform.jar"]
