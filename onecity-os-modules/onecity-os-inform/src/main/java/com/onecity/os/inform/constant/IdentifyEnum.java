package com.onecity.os.inform.constant;

/**
 *
 */
public enum IdentifyEnum {

    TEXT("text", "文本"),
    POSITIVE("positive", "正数"),
    NEGATIVE("negative", "负数"),
    TITLE("title", "标题"),;
    private final String code;
    private final String message;

    IdentifyEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getValue() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static com.onecity.os.inform.constant.IdentifyEnum getByMessage(String message){
        com.onecity.os.inform.constant.IdentifyEnum[] arr =  com.onecity.os.inform.constant.IdentifyEnum.values();
        for (int i = 0; i < arr.length; i++) {
            if (arr[i].message.equals(message)){
                return arr[i];
            }
        }
        return null;
    }
}
