package com.onecity.os.inform.constant;

/**
 *指标展示方式
 * <AUTHOR>
 * @date 2020/06/11
 */
public enum IndicatorExhibitTypeEnum {

    BARCHART("barchart", "柱状图"),
    LINECHART("linechart", "趋势图"),
    WORD("word", "数字"),
    PIECHART("piechart", "饼状图");
    private final String code;
    private final String message;

    IndicatorExhibitTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getValue() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static com.onecity.os.inform.constant.IndicatorExhibitTypeEnum getByMessage(String message){
        com.onecity.os.inform.constant.IndicatorExhibitTypeEnum[] arr =  com.onecity.os.inform.constant.IndicatorExhibitTypeEnum.values();
        for (int i = 0; i < arr.length; i++) {
            if (arr[i].message.equals(message)){
                return arr[i];
            }
        }
        return WORD;
    }
}
