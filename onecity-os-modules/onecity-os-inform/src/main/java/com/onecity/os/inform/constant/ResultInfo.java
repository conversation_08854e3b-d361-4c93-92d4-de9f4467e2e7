package com.onecity.os.inform.constant;

import lombok.Data;


/***
 * 返回值
 * @param <T>
 */
@Data
public class ResultInfo<T> {

    private String resp_code;
    private String resp_msg;
    private T datas;

    public ResultInfo(T datas) {
        this.datas = datas;
    }

    public ResultInfo(ResultInfoEnum infoEnum, T datas) {
        this(infoEnum);
        this.datas = datas;
    }
    public ResultInfo(String resp_code, String resp_msg){
        this.resp_code = resp_code;
        this.resp_msg = resp_msg;
    }

    public ResultInfo(ResultInfoEnum infoEnum) {
    	this.resp_code =infoEnum.getCode();
        this.resp_msg = infoEnum.getMsg();
	}

	public static<T> ResultInfo ok(T datas) {
      return new ResultInfo(ResultInfoEnum.OK,datas);
    }
    public static ResultInfo ok() {
        return new ResultInfo(ResultInfoEnum.OK);
    }
    public static ResultInfo error(ResultInfoEnum infoEnum) {
        return new ResultInfo(infoEnum.getCode(),infoEnum.getMsg());
    }
    public static ResultInfo error(String msg) {
        return new ResultInfo(ResultInfoEnum.ERROR.getCode(),msg);
    }
}
