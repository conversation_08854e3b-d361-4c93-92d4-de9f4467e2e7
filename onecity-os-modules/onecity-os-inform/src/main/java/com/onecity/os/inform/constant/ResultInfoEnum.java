package com.onecity.os.inform.constant;

/**
 * 参数校验1开头；返回结果2开头；操作3开头，其他校验4开头
 */
public enum ResultInfoEnum {
    // **********通知信息定义****************
    OK("0", "OK"),
    ERROR("1", "ERROR"),

    // **********常规错误定义 N ****************
    PARAM_IS_NULL("10001", "参数不能空"),
    SOURCE_IS_NULL("10002", "厅局来源不能空"),
    FILE_NAME_IS_NULL("10003", "文件名称不能空"),
    SOURCE_SIMPLE_NAME_ERROR("10004", "厅局编码只能是纯字母"),
    SOURCE_SIMPLE_NAME_NULL("10005", "厅局编码不能为空"),
    SOURCE_SIMPLE_NAME_LENGTH_ERROR("10006", "厅局编码长度不能超过10位"),
    ZHIBIAO_IS_NULL("20001", "指标为空"),
    SOURCE_MANAGE_NULL("20002", "无此厅局信息"),
    FILE_UPLOAD_FAIL("30001", "文件上传失败"),
    CREATE_SOURCE_FAIL("30002", "创建厅局失败"),
    UPDATE_SOURCE_FAIL("30003", "更新数据失败"),
    DELETE_SOURCE_FAIL("30004", "删除厅局失败"),
    STOP_SOURCE_FAIL("30005", "停用厅局失败"),
    START_SOURCE_FAIL("30006", "启动厅局失败"),
    INVALID_FILE_TYPE("40001", "无效的文件类型");

    private String code;
    private String msg;


    ResultInfoEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
