package com.onecity.os.inform.constant;/**
 * Description
 *
 * <AUTHOR> 2021/11/9
 */

import java.util.*;

/**
 * <AUTHOR>
 * @Title UtilConstants
 * @Description 通用常量
 * @date 2021/11/9 11:03
 */

public interface UtilConstants {

    /**
     * 通用字符
     */
    interface Symbol {
        String SIGH = "!";
        String AT = "@";
        String WELL = "#";
        String DOLLAR = "$";
        String RMB = "￥";
        String SPACE = " ";
        String LB = System.getProperty("line.separator");
        String PERCENTAGE = "%";
        String AND = "&";
        String STAR = "*";
        String MIDDLE_LINE = "-";
        String LOWER_LINE = "_";
        String EQUAL = "=";
        String PLUS = "+";
        String COLON = ":";
        String SEMICOLON = ";";
        String COMMA = ",";
        String POINT = ".";
        String SLASH = "/";
        String VERTICAL_BAR = "|";
        String DOUBLE_SLASH = "//";
        String BACKSLASH = "\\";
        String QUESTION = "?";
        String LEFT_BIG_BRACE = "{";
        String RIGHT_BIG_BRACE = "}";
        String LEFT_MIDDLE_BRACE = "[";
        String RIGHT_MIDDLE_BRACE = "]";
        String BACKQUOTE = "`";
        String RU_CURRENCY_SYMBOL = "₽";
        String UFEFF = "\uFEFF";
        String YEAR = "年";
        String MONTH = "月";
        String DAY = "日";
    }

    /**
     * 文件格式
     */
    interface FileType {
        //文件格式
        List<String> XLS = Arrays.asList("xls", "xlsx");
        //PDF
        List<String> PDF = Collections.singletonList("pdf");
    }

    /**
     * 状态统计
     */
    interface State {
        //未提交
        String DRAFT = "DRAFT";
        //已发布
        String PUBLISH = "PUBLISH";
        //审核中
        String AUDIT = "AUDIT";
        //已审核 AUDITED （用于审核流程上记录 A角色审核完，B角色未审核状态）
        String AUDITED = "AUDITED";
        //编辑
        String EDIT = "EDIT";
        //驳回
        String OVERRULE = "OVERRULE";
        //撤回
        String REVOKE = "REVOKE";
        //提交
        String SUBMIT = "SUBMIT";
        //删除
        String DELETED = "DELETED";
        //启用
        String ENABLE = "ENABLE";
        //续推待审核
        String PUSH_AUDIT = "PUSH_AUDIT";
        //续推已审核 PUSH_AUDITED （用于审核流程上记录 A角色审核完，B角色未审核状态）
        String PUSH_AUDITED = "PUSH_AUDITED";
        //续推已发布
        String PUSH_PUBLISH = "PUSH_PUBLISH";
        //续推已驳回
        String PUSH_OVERRULE = "PUSH_OVERRULE";
        //已归档
        String ARCHIVED = "ARCHIVED";
    }

    /**
     * 数据来源
     */
    interface Source {
        //PC
        String PC = "PC";
        //APP
        String APP = "APP";
    }

    /**
     * 通用标记
     */
    interface StateFlag {
        //PC
        String TRUE = "1";
        //APP
        String FALSE = "0";
    }

    /**
     * 更新周期
     */
    interface UpdateCycle {
        String DAILY_UPDATE = "日更新";
        String WEEKLY_UPDATE = "周更新";
        String HALF_MONTHLY_UPDATE = "半月更新";
        String MONTHLY_UPDATE = "月度更新";
        String QUARTERLY_UPDATE = "季度更新";
        String HALF_ANNUAL_UPDATE = "半年更新";
        String ANNUAL_UPDATE = "年度更新";

        String HALF_ANNUAL_ONE = "上半年";
        String HALF_ANNUAL_TWO = "下半年";
        String QUARTERLY_ONE = "第一季度";
        String QUARTERLY_TWO = "第二季度";
        String QUARTERLY_THIRD = "第三季度";
        String QUARTERLY_FOUR = "第四季度";
    }

    /**
     * 数值常量
     */
    interface Num {
        int ONE = 1;
        int TWO = 2;
        int Four = 4;
        int TWELVE = 12;
    }

    /**
     * 指标头操作
     */
    interface Operate {
        //新增
        String ADD = "ADD";
        //删除
        String DEL = "DEL";
        //调整
        String UPDATE = "UPDATE";
        //保持
        String KEEP = "KEEP";
    }

    /**
     * 指标数据标头默认结构
     *
     * @return
     */
    static List<String> getAllColumn() {
        List<String> allColumnNames = new ArrayList<>();
        allColumnNames.add("item_value");
        allColumnNames.add("item_value1");
        allColumnNames.add("item_value2");
        allColumnNames.add("item_value3");
        allColumnNames.add("item_value4");
        allColumnNames.add("item_value5");
        allColumnNames.add("item_value6");
        return allColumnNames;
    }

    /**
     * item_value对照转换，
     * 前端传值 itemValue，转成item_value 数据库/前端使用
     *
     * @param source
     * @return
     */
    static String getItemMap(String source) {
        Map<String, String> itemMap = new HashMap<>(14);
        itemMap.put("item_value", "itemValue");
        itemMap.put("item_value1", "itemValue1");
        itemMap.put("item_value2", "itemValue2");
        itemMap.put("item_value3", "itemValue3");
        itemMap.put("item_value4", "itemValue4");
        itemMap.put("item_value5", "itemValue5");
        itemMap.put("item_value6", "itemValue6");
        itemMap.put("itemValue", "item_value");
        itemMap.put("itemValue1", "item_value1");
        itemMap.put("itemValue2", "item_value2");
        itemMap.put("itemValue3", "item_value3");
        itemMap.put("itemValue4", "item_value4");
        itemMap.put("itemValue5", "item_value5");
        itemMap.put("itemValue6", "item_value6");
        return itemMap.get(source);
    }

    /**
     * 类型
     */
    interface IndType {
        //LINE
        String LINE = "line";
        //BAR
        String BAR = "bar";
        //horizontalbar
        String HORIZONTALBAR = "horizontalbar";
        //pie
        String PIE = "pie";
        //radar
        String RADAR = "radar";
        //table
        String TABLE = "table";
        //word
        String WORD = "word";
        //2columngrid2列方格图
        String TWOCOLUMNGRID = "2columngrid";
        //rectword矩形文本图
        String RECTWORD = "rectword";
        //multgrid多维方格图
        String MULTFRID = "multgrid";
    }

    /**
     * 类型
     */
    interface ColumnName {
        //item0
        String ITEM0 = "item_value";
        //item1
        String ITEM1 = "item_value1";
        //item2
        String ITEM2 = "item_value2";
        //item3
        String ITEM3 = "item_value3";
        //item4
        String ITEM4 = "item_value4";
        //item5
        String ITEM5 = "item_value5";
        //item6
        String ITEM6 = "item_value6";
    }
}

