package com.onecity.os.inform.controller;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.inform.feign.SystemFeignService;
import com.onecity.os.inform.modules.inform.entity.InformInfo;
import com.onecity.os.inform.modules.inform.entity.RecipientInfo;
import com.onecity.os.inform.modules.inform.vo.DailyDataVo;
import com.onecity.os.inform.modules.inform.vo.InformVo;
import com.onecity.os.inform.modules.inform.vo.StatisticsDataVo;
import com.onecity.os.inform.service.InformInstructService;
import com.onecity.os.inform.service.InformService;
import com.onecity.os.system.api.RemoteUserService;
import com.onecity.os.system.api.domain.SysUser;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 指标信计算api入口
 *
 * @Author: zack
 * @Date: 2022/6/7 17:36
 */
@Slf4j
@RestController
@RequestMapping("/inform")
@Api(tags = "呈报信息api入口")
public class InformController extends BaseController {

    @Resource
    private InformService informService;
    @Resource
    private InformInstructService informInstructService;

    @Resource
    private SystemFeignService systemFeignService;

    @Resource
    private RemoteUserService remoteUserService;

    /**
     * 新增/编辑，数据集接口
     *
     * @return 成功message
     */
    @PostMapping("/save")
    @ApiOperation("新建/编辑, id为空则新建")
    @Log(title = "阅批呈报-呈报添加或修改",businessType = BusinessType.OTHER)
    public BaseResult<InformInfo> saveData(@RequestBody @Valid InformInfo informInfo) {
        return informService.saveData(informInfo);
    }

    /**
     * 呈报操作接口
     *
     * @return 成功message
     */
    @PostMapping("/informOperation")
    @ApiOperation("呈报操作接口 (撤回(REVOKE)/提交(SUBMIT))")
    @Log(title = "阅批呈报-呈报提交或撤回或删除",businessType = BusinessType.OTHER)
    public BaseResult<String> informOperation(@RequestParam(name = "informId") String informId,
                                              @RequestParam(name = "operation") String operation,
                                              @RequestParam(name = "userId") String userId,
                                              @RequestParam(name = "userName") String userName) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)) {
            BaseResult.fail("为获取到当前用户，请联系管理员");
        }
        return informService.informOperation(informId, operation, loginUser.getUserid().toString(), loginUser.getSysUser().getNickName());
    }

    /**
     * 呈报操作删除接口
     *
     * @return 成功message
     */
    @PostMapping("/deleteInform")
    @ApiOperation("呈报操作接口 删除(DELETED)")
    @Log(title = "阅批呈报-呈报删除",businessType = BusinessType.DELETE)
    public BaseResult<String> deleteInform(@RequestParam(name = "informId") String informId,
                                              @RequestParam(name = "operation") String operation,
                                              @RequestParam(name = "userId") String userId,
                                              @RequestParam(name = "userName") String userName) {
        return informService.informOperation(informId, operation, userId, userName);
    }

    @GetMapping("/list")
    @ApiOperation("呈报信息列表查询接口")
    public TableDataInfo queryPageListByParam(InformVo informVo) {
        //用户信息调整为自动获取
        SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
        informVo.setUserId(String.valueOf(sysUser.getUserId()));
        startPage();
        List<InformInfo> informInfos = informService.queryPageListByParam(informVo);
        return getDataTable(informInfos, true);
    }

    @GetMapping("/listByRole")
    @ApiOperation("呈报信息列表查询接口")
    public TableDataInfo queryPageListByRole(InformVo informVo) {
        //用户信息调整为自动获取
        SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
        log.info("queryPageListByRole 1.1.1. sysUser : {}", JSONObject.toJSONString(sysUser));
        informVo.setUserId(String.valueOf(sysUser.getUserId()));
        log.info("queryPageListByRole 1.1.2. UserId : {}", JSONObject.toJSONString(informVo.getUserId()));
        //获取当前用户角色id
        List<String> roleIds = Arrays.stream(sysUser.getRoleIds()).map(Objects::toString).collect(Collectors.toList());
        informVo.setRoleIds(roleIds);
        startPage();
        log.info("queryPageListByRole 1.1.3. informVo : {}", JSONObject.toJSONString(informVo));
        List<InformInfo> informInfos = informService.queryPageListByParam(informVo);
        return getDataTable(informInfos, true);
    }

    @GetMapping("/details")
    @ApiOperation("呈报信息详情接口")
    public BaseResult<InformInfo> queryDetails(@RequestParam(name = "id") String id) {
        return informService.queryDetails(id);
    }

    @PostMapping("/push")
    @ApiOperation("续推接口")
    @Log(title = "阅批呈报-续推", businessType = BusinessType.OTHER)
    public BaseResult push(@RequestBody InformInfo informInfo) {
        return informService.push(informInfo.getId(), informInfo.getRecipient());
    }
    /**
     * 查询系统统计数据
     * 此方法通过调用系统Feign服务获取统计数据，并补充通知和指令的数量信息
     *
     * @return 包含统计数据的BaseResult对象
     */
    @ApiOperation("查询系统统计数据接口")
    @GetMapping("/getStatisticsData")
    public BaseResult<StatisticsDataVo> queryAllInformCount() {
        // 调用系统Feign服务获取统计数据
        BaseResult<StatisticsDataVo> statisticsData = systemFeignService.getStatisticsData();
        // 检查响应码，如果不是200，则返回失败结果
        int code = statisticsData.getCode();
        if (code != 200) {
            return BaseResult.fail("查询繁忙，请稍后再试");
        }
        // 获取并处理统计数据
        StatisticsDataVo statisticsDataVo = statisticsData.getData();
        Date today = getTodayMidnight();
        // 查询并设置通知数量
        int informCount = Optional.ofNullable(informService.queryAllInformCount(today)).orElse(0L).intValue();
        statisticsDataVo.setInformCount(informCount);
        // 查询并设置指令数量
        int instructionCount = Optional.ofNullable(informInstructService.queryAllInformCount(today)).orElse(0L).intValue();
        statisticsDataVo.setInstructionCount(instructionCount);
        // 返回成功结果
        return BaseResult.ok(statisticsDataVo);
    }

    /**
     * 获取当天的 00:00:00 时间
     * @return 当天的 00:00:00 时间
     */
    private static Date getTodayMidnight() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }


    /**
     * 查询每日呈报数量
     * 本方法通过接收开始日期和结束日期，查询在这段时间内每天的呈报数量
     * 主要用于统计和展示每日呈报发送量的趋势
     * @param dayStart 开始日期，格式为YYYY-MM-DD，用于限定查询的起始时间
     * @param dayEnd 结束日期，格式为YYYY-MM-DD，用于限定查询的结束时间
     * @return 返回一个BaseResult对象，其中包含了一个List<DailyDataVo>对象，
     *         列表中的每个元素代表了一天的呈报数量数据
     */
    @GetMapping("/queryDailyInformCount")
    public BaseResult<List<DailyDataVo>> queryDailyInformCount(@RequestParam(name = "dayStart") String dayStart,
                                                               @RequestParam(name = "dayEnd") String dayEnd) {
        return BaseResult.ok(informService.queryDailyInformCount(dayStart, dayEnd));
    }



    /**
     * 查询每日批示数量
     * 本方法通过接收开始日期和结束日期，查询在这段时间内每天的批示数量
     * 使用了@GetMapping注解，表示这是一个处理GET请求的方法
     * @param dayStart 开始日期，格式为字符串，用于界定查询的起始时间
     * @param dayEnd   结束日期，格式为字符串，用于界定查询的结束时间
     * @return 返回一个BaseResult对象，其中包含了一个List，List中存放了每日数据的DailyDataVo对象
     */
    @GetMapping("/queryDailyInstructionsCount")
    public BaseResult<List<DailyDataVo>> queryDailyInstructionsCount(@RequestParam(name = "dayStart") String dayStart,
                                                                     @RequestParam(name = "dayEnd") String dayEnd) {
        return BaseResult.ok(informInstructService.queryDailyInstructionsCount(dayStart, dayEnd));
    }


    @GetMapping("/recipients")
    @ApiOperation("呈报信息接收人查询")
    public BaseResult<List<RecipientInfo>> queryRecipients(@RequestParam(name = "id") String id) {
        return informService.queryRecipients(id);
    }

    @PostMapping("/clean")
    @ApiOperation("呈报信息强制删除")
    @Log(title = "阅批呈报-呈报信息强制删除",businessType = BusinessType.DELETE)
    public BaseResult<String> clean(@RequestParam(name = "informId") String informId) {
        return informService.clean(informId);
    }
}
