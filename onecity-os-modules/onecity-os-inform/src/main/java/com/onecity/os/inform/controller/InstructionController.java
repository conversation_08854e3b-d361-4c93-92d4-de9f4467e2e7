package com.onecity.os.inform.controller;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.inform.modules.inform.dto.InformDto;
import com.onecity.os.inform.modules.inform.dto.InstructionDto;
import com.onecity.os.inform.modules.inform.entity.InformInfo;
import com.onecity.os.inform.modules.inform.entity.RecipientInfo;
import com.onecity.os.inform.modules.inform.vo.InformVo;
import com.onecity.os.inform.modules.inform.vo.InstructionsVo;
import com.onecity.os.inform.modules.inform.vo.TransferParam;
import com.onecity.os.inform.modules.inform.vo.TransferVo;
import com.onecity.os.inform.service.InformService;
import com.onecity.os.inform.service.InstructionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 批示信息相关接口
 */
@Slf4j
@RestController
@RequestMapping("/instruction")
@Api(tags = "批示信息相关接口")
public class InstructionController extends BaseController {

    @Resource
    private InstructionService instructionService;



    @GetMapping("/informList")
    @ApiOperation("呈报信息列表查询接口")
    public TableDataInfo informList(@RequestParam(name = "title") String title,
                                              @RequestParam(name = "type") String type,
                                              @RequestParam(name = "time",required = false) String time,
                                              @RequestParam(name = "pageNum") Integer pageNum,
                                              @RequestParam(name = "pageSize") Integer pageSize) {
        startPage();
        List<InformDto> informDtos = instructionService.informList(title,type,time);
        return getDataTable(informDtos);
    }

    @GetMapping("/instructionList")
    @ApiOperation("呈报批示列表")
    public TableDataInfo instructionList(@RequestParam(name = "informId") String informId,
                                    @RequestParam(name = "pageNum") Integer pageNum,
                                    @RequestParam(name = "pageSize") Integer pageSize) {
        startPage();
        List<InstructionDto> instructionDtos = instructionService.instructionList(informId);
        return getDataTable(instructionDtos);
    }

    @GetMapping("/informArchive")
    @ApiOperation("批示信息--呈报归档")
    @Log(title = "阅批呈报-呈报归档",businessType = BusinessType.OTHER)
    public BaseResult informArchive(@RequestParam(name = "informId") String informId) {
        return instructionService.informArchive(informId);
    }

    @GetMapping("/instructionDetails")
    @ApiOperation("批示信息--查看批示详情")
    public BaseResult<InstructionsVo> instructionDetails(@RequestParam(name = "instructionId") String instructionId) {
        return instructionService.instructionDetails(instructionId);
    }

    @GetMapping("/instructionArchive")
    @ApiOperation("批示信息--批示归档")
    @Log(title = "阅批呈报-批示归档",businessType = BusinessType.OTHER)
    public BaseResult instructionArchive(@RequestParam(name = "instructionId") String instructionId) {
        return instructionService.instructionArchive(instructionId);
    }

    @PostMapping("/transfer")
    @ApiOperation("批示信息--批示转办")
    @Log(title = "阅批呈报-批示转办",businessType = BusinessType.OTHER)
    public BaseResult transfer(@RequestBody TransferParam transferParam) {
        return instructionService.transfer(transferParam);
    }

    @GetMapping("/transferList")
    @ApiOperation("批示信息--呈报转办列表")
    public TableDataInfo transferList(@RequestParam(name = "informId") String informId,
                                         @RequestParam(name = "pageNum") Integer pageNum,
                                         @RequestParam(name = "pageSize") Integer pageSize) {
        startPage();
        List<TransferVo> transferVos = instructionService.transferList(informId);
        return getDataTable(transferVos);
    }

    @GetMapping("/transferDetails")
    @ApiOperation("批示信息--转办详情")
    public BaseResult<TransferVo> transferDetails(@RequestParam(name = "transferId") String transferId) {
        return instructionService.transferDetails(transferId);
    }
}
