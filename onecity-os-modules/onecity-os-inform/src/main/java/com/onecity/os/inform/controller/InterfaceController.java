package com.onecity.os.inform.controller;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.inform.modules.inform.vo.FeedUpParam;
import com.onecity.os.inform.modules.inform.vo.InterfaceUserParam;
import com.onecity.os.inform.modules.inform.vo.InterfaceUserVo;
import com.onecity.os.inform.modules.inform.vo.TransferVo;
import com.onecity.os.inform.service.InstructionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 批示信息相关接口
 */
@Slf4j
@RestController
@RequestMapping("/interface")
@Api(tags = "接口人配置相关接口")
public class InterfaceController extends BaseController {

    @Resource
    private InstructionService instructionService;



    @GetMapping("/list")
    @ApiOperation("人员配置--接口人列表")
    public TableDataInfo interfaceUserList(@RequestParam(name = "nickName") String nickName,
                                              @RequestParam(name = "deptName") String deptName,
                                              @RequestParam(name = "interfaceName") String interfaceName,
                                              @RequestParam(name = "pageNum") Integer pageNum,
                                              @RequestParam(name = "pageSize") Integer pageSize) {
        startPage();
        List<InterfaceUserVo> interfaceUserVos = instructionService.interfaceUserList(nickName,deptName,interfaceName);
        return getDataTable(interfaceUserVos);
    }

    @PostMapping("/add")
    @ApiOperation("人员配置--新建配置")
    @Log(title = "阅批呈报-添加人员配置",businessType = BusinessType.UPDATE)
    public BaseResult add(@RequestBody InterfaceUserParam interfaceUserParam) {
        return instructionService.addInterfaceUser(interfaceUserParam);
    }

    @PostMapping("/edit")
    @ApiOperation("人员配置--修改配置")
    @Log(title = "阅批呈报-修改人员配置",businessType = BusinessType.UPDATE)
    public BaseResult edit(@RequestBody InterfaceUserVo interfaceUserVo) {
        return instructionService.editInterfaceUser(interfaceUserVo);
    }

    @GetMapping("/delete")
    @ApiOperation("人员配置--删除配置")
    @Log(title = "阅批呈报-删除人员配置",businessType = BusinessType.DELETE)
    public BaseResult delete(@RequestParam(name = "userId") Long userId) {
        return instructionService.deleteInterfaceUser(userId);
    }
}
