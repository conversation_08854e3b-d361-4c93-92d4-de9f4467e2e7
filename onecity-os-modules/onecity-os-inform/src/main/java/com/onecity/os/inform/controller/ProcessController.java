package com.onecity.os.inform.controller;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.inform.modules.inform.entity.ProcessInfo;
import com.onecity.os.inform.modules.inform.entity.ProcessStepInfo;
import com.onecity.os.inform.modules.inform.vo.ProcessStepVo;
import com.onecity.os.inform.modules.inform.vo.RoleVo;
import com.onecity.os.inform.service.ProcessInfoService;
import com.onecity.os.inform.service.ProcessStepInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * processController
 *
 * <AUTHOR>
 * @since 2024/8/9 13:37
 */
@Slf4j
@RestController
@RequestMapping("/process")
@Api(tags = "审批流程管理api入口")
public class ProcessController extends BaseController {

    @Resource
    private ProcessInfoService processInfoService;

    @Resource
    private ProcessStepInfoService processStepInfoService;

    /**
     * 呈报操作接口
     *
     * @return 成功message
     */
    @PostMapping("/save")
    @ApiOperation("审批流程管理-新建|编辑流程接口")
    @Log(title = "新建|编辑流程", businessType = BusinessType.OTHER)
    public BaseResult save(@RequestParam(name = "id", required = false) String processId,
                                   @RequestParam(name = "processName") String processName,
                                   @RequestParam(name = "content") String content) {
        return processInfoService.save(processId, processName, content);
    }

    @GetMapping("/list")
    @ApiOperation("流程列表接口")
    public TableDataInfo list() {
        startPage();
        List<ProcessInfo> processInfos = processInfoService.list();
        return getDataTable(processInfos);
    }

    @PostMapping("/del")
    @ApiOperation("流程删除接口")
    @Log(title = "审批流程管理-删除流程", businessType = BusinessType.DELETE)
    public BaseResult del(@RequestParam(name = "id") String id) {
        return processInfoService.del(id);
    }

    @GetMapping("/step/list")
    @ApiOperation("流程步骤接口")
    public BaseResult<List<ProcessStepInfo>> stepList(@RequestParam(name = "processId") String processId) {
        return processStepInfoService.list(processId);
    }

    @PostMapping("/step/save")
    @ApiOperation("流程步骤保存接口")
    @Log(title = "审批流程管理-新建|编辑步骤", businessType = BusinessType.OTHER)
    public BaseResult stepSave(@RequestBody ProcessStepVo processStepVo) {
        return processStepInfoService.save(processStepVo.getId(), processStepVo.getProcessId(),
                                            processStepVo.getStepName(), processStepVo.getRoleIds());
    }

    @PostMapping("/step/del")
    @ApiOperation("流程步骤删除接口")
    @Log(title = "审批流程管理-删除步骤", businessType = BusinessType.DELETE)
    public BaseResult stepDel(@RequestParam(name = "id") String id) {
        return processStepInfoService.del(id);
    }

    @PostMapping("/step/move")
    @ApiOperation("流程步骤移动接口")
    @Log(title = "审批流程管理-移动步骤", businessType = BusinessType.UPDATE)
    public BaseResult stepMove(@RequestParam(name = "id") String id,
                               @RequestParam(name = "type") Integer type) {
        return processStepInfoService.move(id, type);
    }

}
