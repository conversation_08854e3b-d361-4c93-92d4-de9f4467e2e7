package com.onecity.os.inform.controller;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.inform.modules.inform.vo.*;
import com.onecity.os.inform.service.InformInstructService;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

/**
 * 呈报批示app端
 *
 * @Author: hbs
 */
@Slf4j
@RestController
@RequestMapping("/reportApp")
@Api(tags = "呈报批示app端")
public class ReportAppController extends BaseController {

    @Autowired
    private InformInstructService informInstructService;
    /**
     * 全部文件(普通文件,图片,)后缀 支持的类型
     */
    private static final String[] FILE_SUFFIX_SUPPORT = {".xlsx", ".xls", ".doc", ".docx", ".txt",
            ".jpg", ".jpeg", ".png", ".pdf"};
    /**
     * 查询呈报信息列表
     */
    @GetMapping("/reportList")
    @ApiOperation(value = "查询呈报信息列表")
    public TableDataInfo reportList(@RequestParam(name = "userId") String userId,
                                    @RequestParam(name = "pageNum") Integer pageNum,
                                    @RequestParam(name = "pageSize") Integer pageSize)
    {
        startPage();
        List<ReportAppVo> result = new ArrayList<>();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return getDataTable(result);
        }
        result= informInstructService.reportList(loginUser.getUserid().toString());
        return getDataTable(result);
    }

    /**
     * 领导批示
     */
    @PostMapping("/instruction")
    @ApiOperation(value = "指标批示")
    public AjaxResult instruction(@RequestBody InstructionParam instructionParam)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return AjaxResult.error("未获取到当前用户，请联系管理员");
        }
        if(informInstructService.addInstruction(instructionParam) == 1){
            //批示完成，给具有"批示信息"通知权限的人发送消息
            informInstructService.addInstructionMsg(loginUser.getUserid().toString(),
                    loginUser.getSysUser().getNickName(), instructionParam.getInformId());
            return AjaxResult.success("批示成功！");
        }
        return AjaxResult.error("批示失败！");
    }

    /**
     * 查询我的批示
     */
    @GetMapping("/myInstructionList")
    @ApiOperation(value = "查询我的批示")
    public TableDataInfo myInstructionList(@RequestParam(name = "userId") String userId,
                                           @RequestParam(name = "pageNum") Integer pageNum,
                                           @RequestParam(name = "pageSize") Integer pageSize)
    {
        startPage();
        List<InstructionsVo> result = new ArrayList<>();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return getDataTable(result);
        }
        result = informInstructService.myInstructionList(userId);
        return getDataTable(result);
    }

    /**
     * 已读呈报
     */
    @GetMapping( "/readInform")
    @ApiOperation(value = "已读呈报")
    public AjaxResult readInform(@RequestParam(name = "userId") String userId,
                                 @RequestParam(name = "informId") String informId)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return AjaxResult.error("未获取到当前用户，请联系管理员");
        }
        informInstructService.readInform(loginUser.getUserid().toString(),informId);
        return AjaxResult.success("已读成功！");
    }

    @ApiOperation(value = "上传文件")
    @PostMapping("/uploadMatterFile")
    public AjaxResult uploadMatterFile(MultipartFile file) {
        log.info("uploadMatterFile start file size: {}", file.getSize());
        //获取文件大小
        double fileSize = (double) file.getSize() / 1048576;
        // 校验文件名字
        String originalFilename = file.getOriginalFilename();
        String suffix = originalFilename.substring(originalFilename.lastIndexOf('.'));
        String fileName = originalFilename.substring(0,originalFilename.lastIndexOf('.'));
        log.info("上传文件大小为=" + fileSize + "M");
        //校验文件是否被修改后缀
        String fileSuffixName = suffix.toLowerCase(Locale.ROOT);
        log.info("文件后缀为："+ JSONObject.toJSONString(fileSuffixName));
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            byte[] byteArray = new byte[4];
            inputStream.read(byteArray, 0, 4);
            StringBuilder hex = new StringBuilder();
            for (byte b : byteArray) {
                hex.append(String.format("%02X", b));
            }
            //获取文件的魔数png魔数-89504e470d0a1a0a jpg魔数-
            switch (fileSuffixName){
                case ".png":
                    if(!"89504E47".equals(hex.toString())){
                        return AjaxResult.error("文件后缀名被修改");
                    }
                    break;
                case ".pdf":
                    if(!"25504446".equals(hex.toString())){
                        return AjaxResult.error("文件后缀名被修改");
                    }
                    try {
                        // 判断文件xss攻击
                        PDDocument doc = PDDocument.load(inputStream);
                        String CosName = doc.getDocument().getTrailer().toString();
                        if (CosName.contains("COSName{JavaScript}") || CosName.contains("COSName{JS}")) {
                            return AjaxResult.error("文件包含攻击脚本");
                        }
                    } catch (Exception e) {
                        log.error("PDF效验异常：" + e.getMessage());
                        return AjaxResult.error("文件包含攻击脚本");
                    }
                    break;
                case ".jpg":
                    log.info("进入jpg比对");
                    if(!"FFD8FFE0".equals(hex.toString())){
                        return AjaxResult.error("文件后缀名被修改");
                    }
                    break;
                default:
                    return AjaxResult.error("文件格式不支持,请更换后重试!");
            }
        }catch (IOException e){
            log.error("打开文件报错："+e.getMessage());
            return AjaxResult.error("文件内容出错,请更换后重试!");
        }finally {
            IOUtils.closeQuietly(inputStream);
        }

        if(fileSize > 20 ){
            return AjaxResult.error("上传文件最大为20M!");
        }
        if(!originalFilename.contains(".")){
            return AjaxResult.error("文件不能没有后缀!");
        }
        if(!Arrays.asList(FILE_SUFFIX_SUPPORT).contains(suffix.toLowerCase(Locale.ROOT))){
            return AjaxResult.error("文件格式不支持,请更换后重试!");
        }
        if(fileName.length() > 200){
            return AjaxResult.error("文件名长度不允许超过200，请更换后重试！");
        }
        return AjaxResult.success(informInstructService.uploadInstruction(file));
    }
}
