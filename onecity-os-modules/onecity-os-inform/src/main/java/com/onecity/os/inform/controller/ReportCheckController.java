package com.onecity.os.inform.controller;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.inform.modules.inform.entity.InformLog;
import com.onecity.os.inform.modules.inform.entity.StepRoleLink;
import com.onecity.os.inform.modules.inform.vo.*;
import com.onecity.os.inform.service.InformInstructService;
import com.onecity.os.inform.service.InformLogService;
import com.onecity.os.inform.service.ReportCheckService;
import com.onecity.os.system.api.RemoteUserService;
import com.onecity.os.system.api.domain.SysUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 呈报审核
 *
 * @Author: hbs
 */
@Slf4j
@RestController
@RequestMapping("/reportCheck")
@Api(tags = "呈报审核api")
public class ReportCheckController extends BaseController {

    @Autowired
    private ReportCheckService reportCheckService;
    @Autowired
    private InformInstructService informInstructService;
    @Autowired
    private InformLogService informLogService;
    @Resource
    private RemoteUserService remoteUserService;
    /**
     * 查询待审核列表
     */
    @GetMapping("/pendingList")
    @ApiOperation(value = "查询待审核列表")
    public TableDataInfo pendingList(ReportCheckParam reportCheckParam)
    {
        //用户信息调整为自动获取
        SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
        reportCheckParam.setUserId(String.valueOf(sysUser.getUserId()));
        //获取当前用户角色id
        List<String> roleIds = Arrays.stream(sysUser.getRoleIds()).map(String::valueOf).collect(Collectors.toList());
        log.info("checkedList 1.1.1. : roleIds :{} ", JSONObject.toJSONString(roleIds));
        reportCheckParam.setRoleIds(roleIds);
        startPage();
        List<ReportCheckVo> reportCheckVoList = reportCheckService.pendingList(reportCheckParam);
        return getDataTable(reportCheckVoList);
    }
    /**
     * 查询已审核列表
     */
    @GetMapping("/checkedList")
    @ApiOperation(value = "查询已审核列表")
    public TableDataInfo checkedList(ReportCheckParam reportCheckParam)
    {
        //用户信息调整为自动获取
        SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
        reportCheckParam.setUserId(String.valueOf(sysUser.getUserId()));
        //获取当前用户角色id
        List<String> roleIds = Arrays.stream(sysUser.getRoleIds()).map(String::valueOf).collect(Collectors.toList());
        log.info("checkedList 1.1.1. : roleIds :{} ", JSONObject.toJSONString(roleIds));
        reportCheckParam.setRoleIds(roleIds);
        startPage();
        List<ReportCheckVo> reportCheckVoList = reportCheckService.checkedList(reportCheckParam);
        return getDataTable(reportCheckVoList);
    }

    /**
     * 审核呈报
     */
    @PostMapping("/check")
    @ApiOperation(value = "审核呈报")
    @Log(title = "阅批呈报-呈报审核",businessType = BusinessType.OTHER)
    public AjaxResult check(@RequestBody CheckParam checkParam) {
        //用户信息调整为自动获取
        SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
        checkParam.setUserId(String.valueOf(sysUser.getUserId()));
        checkParam.setUserName(sysUser.getNickName());
        checkParam.setReviewer(String.valueOf(sysUser.getUserId()));
        checkParam.setReviewerName(sysUser.getNickName());
        checkParam.setRoleIds(sysUser.getRoleIds());
        if (checkParam.getHandleType() == 1) {
            if (StringUtils.isEmpty(checkParam.getReviewer())) {
                return AjaxResult.error("审核人不能为空！");
            }
        }
        int result = reportCheckService.check(checkParam);
        if (result == 1) {
            return AjaxResult.success("审核呈报成功！");
        } else if (result == 2) {
            return AjaxResult.error("该呈报已审核！");
        } else if (result == 3) {
            return AjaxResult.error("该呈报已删除，请核查数据！");
        } else if (result == 4) {
            return AjaxResult.error("该呈报已驳回，请核查数据！");
        }{
            return AjaxResult.error("审核呈报失败！");
        }
    }

    /**
     * 修改接收人
     */
    @PostMapping("/updateReceiver")
    @ApiOperation(value = "修改接收人")
    public AjaxResult updateReceiver(@RequestBody RecipientParam recipientParam)
    {
        if(reportCheckService.updateReceiver(recipientParam) == 1){
            return AjaxResult.success("修改成功！");
        }
        return AjaxResult.error("修改失败！");
    }

    /**
     * 查看流程
     */
    @GetMapping("/processList")
    @ApiOperation(value = "查看流程")
    public AjaxResult processList(@RequestParam(name="informId",required=true) String informId)
    {
        List<InformLog> result = informLogService.getLogListById(informId);
        return AjaxResult.success(result);
    }

    /**
     * 查询批示列表
     */
    @GetMapping("/instructionsList")
    @ApiOperation(value = "查询批示列表")
    public TableDataInfo instructionsList(InstructionsListParam instructionsListParam)
    {
        startPage();
        List<InstructionsVo> result = informInstructService.instructionsList(instructionsListParam);
        return getDataTable(result);
    }

    @ApiOperation(value = "导出批示结果")
    @Log(title = "阅批呈报-批示信息结果导出",businessType = BusinessType.OTHER)
    @GetMapping("/downloadInstruction")
    public void downloadInstruction(@ApiParam("文件路径")String fileUrl,HttpServletResponse response) throws IOException {
        informInstructService.downloadInstruction(fileUrl,response);
    }
}
