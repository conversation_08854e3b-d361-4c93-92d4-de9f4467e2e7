package com.onecity.os.inform.controller;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.inform.modules.inform.dto.InformDto;
import com.onecity.os.inform.modules.inform.dto.InstructionDto;
import com.onecity.os.inform.modules.inform.vo.FeedUpParam;
import com.onecity.os.inform.modules.inform.vo.InstructionsVo;
import com.onecity.os.inform.modules.inform.vo.TransferParam;
import com.onecity.os.inform.modules.inform.vo.TransferVo;
import com.onecity.os.inform.service.InstructionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 批示信息相关接口
 */
@Slf4j
@RestController
@RequestMapping("/transfer")
@Api(tags = "转办管理相关接口")
public class TransferController extends BaseController {

    @Resource
    private InstructionService instructionService;



    @GetMapping("/transferList")
    @ApiOperation("转办管理--转办列表")
    public TableDataInfo informList(@RequestParam(name = "title") String title,
                                              @RequestParam(name = "type") String type,
                                              @RequestParam(name = "operatorName") String operatorName,
                                              @RequestParam(name = "pageNum") Integer pageNum,
                                              @RequestParam(name = "pageSize") Integer pageSize) {
        startPage();
        List<TransferVo> transferVos = instructionService.myTransferList(title,type,operatorName);
        return getDataTable(transferVos);
    }

    @PostMapping("/feedUp")
    @ApiOperation("转办管理--反馈")
    @Log(title = "阅批呈报-转办反馈",businessType = BusinessType.OTHER)
    public BaseResult feedUp(@RequestBody FeedUpParam feedUpParam) {
        return instructionService.feedUp(feedUpParam);
    }
}
