package com.onecity.os.inform.controller.app;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.inform.modules.inform.entity.InformInfo;
import com.onecity.os.inform.service.InformService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * app端呈报信息
 *
 * <AUTHOR>
 * @since 2024/9/4 9:29
 */
@Slf4j
@RestController
@RequestMapping("/app/inform")
@Api(tags = "呈报信息api入口")
public class AppInformController extends BaseController {

    @Resource
    private InformService informService;

    @GetMapping("/details")
    @ApiOperation("呈报信息详情接口")
    public BaseResult<InformInfo> queryDetails(@RequestParam(name = "id") String id) {
        return informService.queryDetails(id);
    }
}