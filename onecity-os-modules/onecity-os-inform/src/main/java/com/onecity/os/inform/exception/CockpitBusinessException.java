package com.onecity.os.inform.exception;

import com.onecity.os.inform.constant.ResultInfoEnum;
import lombok.Data;

/**
 * 业务异常
 *
 * <AUTHOR>
 */
@Data
public class CockpitBusinessException extends RuntimeException {


    private static final long serialVersionUID = 2499189342726925091L;

    private String status;

    private ResultInfoEnum e;

    public CockpitBusinessException(String message) {
        super(message);
    }

    public CockpitBusinessException(String message, String status) {
        super(message);
        this.status = status;
    }

    public CockpitBusinessException(ResultInfoEnum e) {
        super(e.getMsg());
        this.status = e.getCode();
        this.e = e;
    }
}
