package com.onecity.os.inform.feign.fallback;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.inform.feign.SuperviseFeignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 */
@Slf4j
@Component
public class SuperviseFeignServiceFallbackFactory implements FallbackFactory<SuperviseFeignService> {
    @Override
    public SuperviseFeignService create(Throwable cause) {
        return new SuperviseFeignService() {
            @Override
            public BaseResult queryByIndicator(String creator,String indicatorId) {
                log.info("queryByIndicator  : {} ", JSONObject.toJSONString(creator));
                log.error("根据用户id和指标id查询是否发起过督办异常！");
                return BaseResult.fail("根据用户id和指标id查询是否发起过督办异常");
            }

            @Override
            public BaseResult queryRemindByIndicator(String creator,String indicatorId) {
                log.info("queryByIndicator  : {} ", JSONObject.toJSONString(creator));
                log.error("根据用户id和指标id查询是否发起过红灯异常！");
                return BaseResult.fail("根据用户id和指标id查询是否发起过红灯异常");
            }

            @Override
            public BaseResult cancelIndicatorHistory(List<String> indicatorIds) {
                log.info("cancelIndicatorHistory  : {} ", JSONObject.toJSONString(indicatorIds));
                log.error("取消督办历史信息异常！");
                return BaseResult.fail("取消督办历史信息异常");
            }

            @Override
            public BaseResult cancelRemindIndicatorHistory(List<String> indicatorIds) {
                log.info("cancelRemindIndicatorHistory  : {} ", JSONObject.toJSONString(indicatorIds));
                log.error("取消红灯历史信息异常！");
                return BaseResult.fail("取消红灯历史信息异常");
            }
        };
    }
}
