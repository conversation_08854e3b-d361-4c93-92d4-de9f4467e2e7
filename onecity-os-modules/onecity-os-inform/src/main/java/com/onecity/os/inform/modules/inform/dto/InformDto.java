package com.onecity.os.inform.modules.inform.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: zack
 * @Date: 2022/9/2 16:07
 */
@Data
@ApiModel("批示信息--呈报列表返回参数")
public class InformDto {

    @ApiModelProperty(value = "呈报信息id")
    private String informId;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "类别")
    private String type;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "发布日期", example = "1990-01-01")
    private String releaseTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "最新批示时间", example = "1990-01-01")
    private String operationTime;

    @ApiModelProperty(value = "已读人数")
    private Integer readNum;

    @ApiModelProperty(value = "批示数量")
    private Integer operationNum;

    @ApiModelProperty(value = "状态")
    private String state;
    @ApiModelProperty(value = "批示文件url")
    private String fileUrl;

    @ApiModelProperty(value = "是否能归档")
    private Boolean isArchive;
}
