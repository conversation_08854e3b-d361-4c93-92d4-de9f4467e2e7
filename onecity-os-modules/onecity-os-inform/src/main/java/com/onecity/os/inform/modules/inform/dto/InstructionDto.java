package com.onecity.os.inform.modules.inform.dto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 *
 * <AUTHOR>
 */
@Data
@ApiModel("批示信息--呈报批示列表返回数据")
public class InstructionDto
{
    @ApiModelProperty(value = "呈报信息id")
    private String informId;

    @ApiModelProperty(value = "批示信息ID")
    private String instructionId;

    /**
     * 接受人id
     */
    @ApiModelProperty(value = "接受人id")
    private String recipient;
    @ApiModelProperty(value = "呈报对象")
    private String recipientName;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "呈报时间")
    private String releaseTime;

    @ApiModelProperty(value = "状态")
    private String state;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "批示时间")
    private String operationTime;
    @ApiModelProperty(value = "批示文件url")
    private String fileUrl;

    @ApiModelProperty(value = "是否能操作")
    private Boolean isOperate;
}
