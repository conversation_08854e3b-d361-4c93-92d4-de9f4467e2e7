package com.onecity.os.inform.modules.inform.entity;

import com.onecity.os.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 指标表历史数据对象 general_indicator_history
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
@Data
public class GeneralIndicatorHistory
{
    private static final long serialVersionUID = 1L;

    /** 指标ID */
    private String id;

    /** 指标名称 */
    private String indicatorName;

    /** 指标展现类型 */
    private String indicatorExhibitType;

    /** 父指标ID,如果为一级指标, 该字段为空 */
    private String parentId;

    /** 父指标名称,如果为一级指标, 该字段为空 */
    private String parentName;

    /** 图标地址 */
    private String iconUrl;

    /** 数据来源id */
    private String sourceId;

    /** 数据来源 */
    private String sourceName;

    /** 排序 */
    private Long sequence;

    /** 指标类型，0：指标，1：tab类型 */
    private Long indicatorType;

    /** 更新日期文本类型 */
    private String updateDate;

    /** 更新周期 */
    private String updateCycle;

    /** 面向领导 */
    private String leader;

    /** 是否删除0:否1:是 */
    private Integer isDelete;

    /** 创建人 */
    private String creater;

    /** 更新人 */
    private String updater;

    /** 分组类型 0指标 1网有 */
    private Long groupType;

    /** 分组网页 */
    private String groupUrl;

    /** 约定更新日期 */
    private String planUpdateDate;

    /** 是否展示(0-不展示,1-展示) */
    private Long isShow;

    /** 是否展示筛选框(0-不展示,1-展示) */
    private Long isScreen;

    /** 是否展示图例(0-不展示,1-展示) */
    private Long isLegend;

    /** 数据更新方式1-手动填报2数据对接 */
    private Integer dataUpdateMode;

    /** 数据配置id */
    private String dataConfigId;

    /** 业务id */
    private String serviceId;
}
