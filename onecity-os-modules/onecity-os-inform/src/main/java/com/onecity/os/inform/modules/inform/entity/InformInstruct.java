package com.onecity.os.inform.modules.inform.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Author: zack
 * @Date: 2022/9/1 16:39
 */
@Data
@Table(name = "inform_instruct")
public class InformInstruct {

    /**
     * 主键自增
     **/
    @Id
    @GeneratedValue
    private String id;

    /**
     * 呈报信息id
     */
    @Column(name = "inform_id")
    @ApiModelProperty(value = "呈报信息id")
    private String informId;

    /**
     * 批示人id
     */
    @Column(name = "operator")
    @ApiModelProperty(value = "批示人id")
    private String operator;

    /**
     * 批示人名称
     */
    @Column(name = "operator_name")
    @ApiModelProperty(value = "批示人名称")
    private String operatorName;

    /**
     * 批示时间
     */
    @Column(name = "operation_time")
    @ApiModelProperty(value = "批示时间")
    private Date operationTime;

    /**
     * 批示文件url
     */
    @Column(name = "file_url")
    @ApiModelProperty(value = "批示文件url")
    private String fileUrl;

    /**
     * 状态
     */
    @Column(name = "state")
    @ApiModelProperty(value = "状态, DELETED = 删除，ARCHIVED = 已归档")
    private String state;

    /**
     * 备用字段
     */
    @Column(name = "backup")
    private String backup;
}
