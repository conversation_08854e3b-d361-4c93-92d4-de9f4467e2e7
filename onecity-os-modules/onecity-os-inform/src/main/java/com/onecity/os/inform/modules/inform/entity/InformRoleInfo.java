package com.onecity.os.inform.modules.inform.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Author: zack
 * @Date: 2024/8/23 10:47
 */
@Data
@Table(name = "inform_role_info")
public class InformRoleInfo {

    /**
     * 主键自增
     **/
    @Id
    @GeneratedValue
    private String id;

    /**
     * 呈报信息id
     */
    @Column(name = "inform_id")
    @ApiModelProperty(value = "呈报信息id")
    private String informId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", example = "1990-01-01 00:00:00")
    private java.util.Date createTime;

    /**
     * 角色id
     */
    @Column(name = "role_id")
    @ApiModelProperty(value = "角色id")
    private Long roleId;

    /**
     * 备用字段
     */
    @Column(name = "backup")
    private String backup;
}
