package com.onecity.os.inform.modules.inform.entity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;

/**
 *
 * <AUTHOR>
 */
@Data
@ApiModel("转办表实体")
public class InterfaceUserInfo
{

    @ApiModelProperty(value = "转办Id")
    private Long userId;
    @ApiModelProperty(value = "呈报信息id")
    private String nickName;
    @ApiModelProperty(value = "批示信息ID")
    private String deptName;

    @ApiModelProperty(value = "转办人Id")
    private Long interfaceUserId;
    @ApiModelProperty(value = "转办人")
    private String interfaceNickName;

}
