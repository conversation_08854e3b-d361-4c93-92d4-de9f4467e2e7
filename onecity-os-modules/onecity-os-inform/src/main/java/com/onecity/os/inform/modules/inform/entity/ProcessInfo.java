package com.onecity.os.inform.modules.inform.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * processInfo
 *
 * <AUTHOR>
 * @since 2024/8/9 10:05
 */
@Data
@Table(name = "process_info")
public class ProcessInfo {
    /**
     * 主键id
     **/
    @Id
    @GeneratedValue
    private String id;

    /**
     * 流程名称
     */
    @Column(name = "process_name")
    @ApiModelProperty(value = "流程名称")
    private String processName;

    /**
     * 创建人名称
     */
    @Column(name = "creator_name")
    @ApiModelProperty(value = "创建人名称")
    private String creatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", example = "1990-01-01 00:00:00")
    private java.util.Date createTime;

    /**
     * 更新人名称
     */
    @Column(name = "updater_name")
    @ApiModelProperty(value = "更新人名称")
    private String updaterName;

    /**
     * 更新时间
     */
    @Column(name = "updater_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间", example = "1990-01-01 00:00:00")
    private java.util.Date updateTime;

    /**
     * 是否删除：0：未删除，1：已删除
     */
    @Column(name="is_delete")
    private Integer isDelete;

    /**
     * 描述
     */
    @Column(name="content")
    private String content;
}
