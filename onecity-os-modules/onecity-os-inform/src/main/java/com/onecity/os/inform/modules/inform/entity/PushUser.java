package com.onecity.os.inform.modules.inform.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * PushUser
 *
 * <AUTHOR>
 * @since 2024/8/9 10:33
 */
@Data
@Table(name = "push_user")
public class PushUser {
    /**
     * 主键id
     **/
    @Id
    @GeneratedValue
    private String id;

    /**
     * 呈报id
     */
    @Column(name = "inform_id")
    @ApiModelProperty(value = "呈报id")
    private String informId;

    /**
     * 流程id
     */
    @Column(name = "process_id")
    @ApiModelProperty(value = "流程id")
    private String processId;

    /**
     * 是否删除：0：未删除，1：已删除
     */
    @Column(name="is_delete")
    private Integer isDelete;

    /**
     * 创建人名称
     */
    @Column(name = "creator_name")
    @ApiModelProperty(value = "创建人名称")
    private String creatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", example = "1990-01-01 00:00:00")
    private java.util.Date createTime;

    /**
     * 更新人名称
     */
    @Column(name = "update_time")
    @ApiModelProperty(value = "更新人名称")
    private String updaterName;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间", example = "1990-01-01 00:00:00")
    private java.util.Date updateTime;

}
