package com.onecity.os.inform.modules.inform.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Author: zack
 * @Date: 2022/9/1 15:13
 */
@Data
@Table(name = "reviewer_info")
public class ReviewerInfo {

    /**
     * 主键自增
     **/
    @Id
    @GeneratedValue
    private String id;

    /**
     * 呈报信息id
     */
    @Column(name = "inform_id")
    @ApiModelProperty(value = "呈报信息id")
    private String informId;

    /**
     * 状态
     */
    @Column(name = "state")
    @ApiModelProperty(value = "状态",
            notes = "0-未处理，1-已处理",
            example = "0")
    private Integer state;

    /**
     * 审核人id
     */
    @Column(name = "reviewer_id")
    @ApiModelProperty(value = "审核人id")
    private String reviewerId;

    /**
     * 审核人名称
     */
    @Column(name = "reviewer_name")
    @ApiModelProperty(value = "审核人名称")
    private String reviewerName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", example = "1990-01-01 00:00:00")
    private java.util.Date createTime;
}
