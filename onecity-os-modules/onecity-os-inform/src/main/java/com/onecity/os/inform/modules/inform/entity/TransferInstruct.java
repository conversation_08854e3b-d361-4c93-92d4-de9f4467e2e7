package com.onecity.os.inform.modules.inform.entity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 */
@Data
@ApiModel("转办表实体")
public class TransferInstruct
{

    @ApiModelProperty(value = "转办Id")
    private String id;
    @ApiModelProperty(value = "呈报信息id")
    private String informId;
    @ApiModelProperty(value = "批示信息ID")
    private String instructionId;

    @ApiModelProperty(value = "转办人Id")
    private String transferId;
    @ApiModelProperty(value = "转办人")
    private String transferName;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "转办时间")
    private String transferTime;
    @ApiModelProperty(value = "转办备注")
    private String transferBackup;

    @ApiModelProperty(value = "转办接受人id")
    private String recipientId;
    @ApiModelProperty(value = "转办接受人名称")
    private String recipientName;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "转办处理时间")
    private String feedTime;
    @ApiModelProperty(value = "反馈内容")
    private String feedBackup;

    @ApiModelProperty(value = "状态")
    private Integer state1;
}
