package com.onecity.os.inform.modules.inform.feign;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.inform.modules.inform.feign.fallback.TodoFeignServiceFallbackFactory;
import com.onecity.os.inform.modules.inform.vo.ToDoVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: zack
 * @Date: 2022/9/27 15:24
 */
@FeignClient(name = "onecity-os-management", fallbackFactory = TodoFeignServiceFallbackFactory.class, decode404 = true)
public interface TodoFeignService {

    @PostMapping("/indicator/addToDo")
    BaseResult addTodo(@RequestBody ToDoVo toDoVo);

    @PostMapping("/indicator/deleteToDoByQuestionnaireId")
    BaseResult deleteToDo(@RequestBody String questionnaireId);

    @PostMapping("/indicator/deleteToDoByPram")
    BaseResult deleteToDoByPram(@RequestParam(name = "questionnaireId", required = false)String questionnaireId, @RequestParam(name = "source", required = false) String source);
}
