package com.onecity.os.inform.modules.inform.feign.fallback;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.inform.modules.inform.dto.RemindInfoDto;
import com.onecity.os.inform.modules.inform.feign.MessageFeignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2022/5/31 10:04
 */
@Slf4j
@Component
public class MessageFeignFallbackFactory implements FallbackFactory<MessageFeignService> {
    @Override
    public MessageFeignService create(Throwable cause) {
        return new MessageFeignService() {
            @Override
            public BaseResult addMsg(List<RemindInfoDto> remindInfo) {
                log.info("addMsg  : {} ", JSONObject.toJSONString(remindInfo));
                log.error("通知公告获取异常！");
                return BaseResult.fail("通知公告获取异常");
            }

            @Override
            public BaseResult deleteMessageByService(String serviceId, String serviceType) {
                log.info("deleteMessageByService  : serviceId:{} serviceType:{}", JSONObject.toJSONString(serviceId),JSONObject.toJSONString(serviceType));
                log.error("阅批呈报删除消息错误！");
                return BaseResult.fail("阅批呈报删除消息异常");
            }
        };
    }
}
