package com.onecity.os.inform.modules.inform.mapper;

import com.onecity.os.inform.modules.inform.entity.InformContentData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * InformContentDataMapper
 *
 * <AUTHOR>
 * @since 2024/8/12 15:59
 */
@Mapper
public interface InformContentDataMapper {

    /**
     * 插入呈报内容
     * @param contentData
     */
    void insertContentData(@Param("informContentData") InformContentData contentData);

    void delContentDataByContentId(@Param("contentId") String contentId);

    List<InformContentData> queryInformContentData(@Param("contentId") String contentId);
}
