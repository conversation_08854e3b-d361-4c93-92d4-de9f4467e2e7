package com.onecity.os.inform.modules.inform.mapper;

import com.onecity.os.inform.modules.inform.dto.InstructionDto;
import com.onecity.os.inform.modules.inform.entity.InformInstruct;
import com.onecity.os.inform.modules.inform.vo.DailyDataVo;
import com.onecity.os.inform.modules.inform.vo.InstructionsListParam;
import com.onecity.os.inform.modules.inform.vo.InstructionsVo;
import com.onecity.os.inform.modules.inform.vo.ReportAppVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @Author: hbs
 */
@Mapper
public interface InformInstructMapper {

    /**
     * 列表查询
     * @param instructionsListParam
     * @return
     */
    List<InstructionsVo> selectList(InstructionsListParam instructionsListParam);

    /**
     * 手机端查询呈报信息列表
     * @param userId
     * @return
     */
    List<ReportAppVo> reportList(String userId);

    /**
     * 手机端查询我的批示
     * @param userId
     * @return
     */
    List<InstructionsVo> myInstructionList(String userId);

    /**
     * 新增领导批示
     * @param informInstruct
     * @return
     */
    int addInstruction(InformInstruct informInstruct);

    /**
     * 已读呈报
     * @param userId
     * @param informId
     * @return
     */
    int readInform(@Param("userId")String userId,@Param("informId")String informId);
    Integer getReadByUserId(@Param("userId")String userId,@Param("informId")String informId);


    /**
     * 已批示呈报
     * @param userId
     * @param informId
     * @return
     */
    int instructInform(@Param("userId")String userId,@Param("informId")String informId);

    /**
     * 获取某人的批示
     * @param userId
     * @param informId
     * @return
     */
    InstructionDto getByUserIdAndInformId(@Param("userId")String userId, @Param("informId")String informId);

    /**
     * 获取总呈报数
     *
     * @return Long
     */
    Long queryAllInformCount(@Param("today") Date today);
    /**
     * 获取每日批示数
     *
     * @return Long
     */
    List<DailyDataVo> queryDailyInstructionsCount(@Param("dayStart") Date dayStart, @Param("dayEnd") Date dayEnd);

    void delByInformId(@Param("informId") String informId);
}
