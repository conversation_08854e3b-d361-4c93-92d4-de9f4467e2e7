package com.onecity.os.inform.modules.inform.mapper;

import com.onecity.os.inform.modules.inform.entity.InformRoleState;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2022/6/9 16:05
 */
@Mapper
public interface InformRoleStateMapper {

    /**
     * 新建 informRoleState
     *
     * @param informRoleState
     */
    void insert(@Param("informRoleState") InformRoleState informRoleState);


    /**
     * 更新状态
     *
     * @param informId
     * @param roleId
     * @param processId
     * @param currentStepId
     * @param state
     */
    void updatePyParam(@Param("informId") String informId, @Param("roleId") String roleId,
                       @Param("processId") String processId, @Param("currentStepId") String currentStepId, @Param("state") String state);

    /**
     * 根据informId更新状态
     * @param informId
     * @param state
     */
    void updatePyInformId(@Param("informId") String informId, @Param("state") String state);

    List<InformRoleState> queryByInformId(@Param("informId") String informId);

    List<InformRoleState> queryByParam(@Param("informId") String informId, @Param("roleId") String roleId,
                                       @Param("processId") String processId, @Param("currentStepId") String currentStepId, @Param("state") String state);

}
