package com.onecity.os.inform.modules.inform.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: zjs
 * @Date: 2024-08-09
 */
@Data
@ApiModel("统计数据-每日统计")
public class DailyDataVo {

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "时间", example = "1990-01-01")
    private String date;

    @ApiModelProperty(value = "日期简写", example = "01-01")
    private String abbrDate;

    @ApiModelProperty(value = "个数")
    private int num;
}
