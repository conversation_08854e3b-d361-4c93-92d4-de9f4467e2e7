package com.onecity.os.inform.modules.inform.vo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 */
@Data
@ApiModel("批示入参")
public class InstructionParam
{
    @ApiModelProperty(value = "当前登录人用户id")
    private String userId;
    @ApiModelProperty(value = "当前登录人姓名")
    private String userName;
    @ApiModelProperty(value = "呈报信息id")
    private String informId;
    @ApiModelProperty(value = "批示文件url")
    private String fileUrl;
}
