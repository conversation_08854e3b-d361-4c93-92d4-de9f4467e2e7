package com.onecity.os.inform.modules.inform.vo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 */
@Data
@ApiModel("查询批示列表入参")
public class InstructionsListParam
{
    @ApiModelProperty(value = "页码,必填")
    private Integer pageNum;
    @ApiModelProperty(value = "页面容量,必填")
    private Integer pageSize;
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "类型")
    private String type;
    @ApiModelProperty(value = "日期 格式：yyyy-MM-dd")
    private String time;
}
