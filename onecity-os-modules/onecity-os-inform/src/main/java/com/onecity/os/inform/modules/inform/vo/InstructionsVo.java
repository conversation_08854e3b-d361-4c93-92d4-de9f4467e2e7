package com.onecity.os.inform.modules.inform.vo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
@ApiModel("批示列表返回参数")
public class InstructionsVo
{
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "类型")
    private String type;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发布时间", example = "1990-01-01 00:00:00")
    private Date releaseTime;
    @ApiModelProperty(value = "批示人")
    private String operatorName;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "批示时间")
    private String operationTime;
    @ApiModelProperty(value = "批示文件url")
    private String fileUrl;
}
