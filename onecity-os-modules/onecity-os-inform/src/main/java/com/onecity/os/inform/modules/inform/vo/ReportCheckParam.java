package com.onecity.os.inform.modules.inform.vo;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.onecity.os.common.core.web.domain.BaseEntityWeb;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
@ApiModel("查询待审核/已审核列表入参")
public class ReportCheckParam
{
    @ApiModelProperty(value = "当前登录人用户id")
    private String userId;
    @ApiModelProperty(value = "页码,必填")
    private Integer pageNum;
    @ApiModelProperty(value = "页面容量,必填")
    private Integer pageSize;
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "类型")
    private String type;
    @ApiModelProperty(value = "同角色userIds")
    private List<String> userIds;
//    @JsonProperty("READ_ONLY")
    @ApiModelProperty(value = "roleIds")
    private List<String> roleIds;
}
