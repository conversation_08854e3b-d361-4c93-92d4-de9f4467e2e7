package com.onecity.os.inform.modules.inform.vo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
@ApiModel("查询待审核/已审核列表返回参数")
public class ReportCheckVo
{
    @ApiModelProperty(value = "呈报信息id")
    private String informId;
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "类型")
    private String type;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "提交时间", example = "1990-01-01 00:00:00")
    private Date publishTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", example = "1990-01-01 00:00:00")
    private Date createTime;
    @ApiModelProperty(value = "状态")
    private String state;
    @ApiModelProperty(value = "呈报状态")
    private String informState;
    @ApiModelProperty(value = "状态描述")
    private String stateStr;
    @ApiModelProperty(value = "呈报状态描述")
    private String informStateStr;
    @ApiModelProperty(value = "流程id")
    private String processId;
    @ApiModelProperty(value = "流程步骤id")
    private String currentStepId;
    @ApiModelProperty(value = "最后一个审核角色,1-是  0-否")
    private Integer last;
}
