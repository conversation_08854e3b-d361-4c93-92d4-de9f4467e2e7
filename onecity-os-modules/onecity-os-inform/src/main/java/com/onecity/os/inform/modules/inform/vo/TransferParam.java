package com.onecity.os.inform.modules.inform.vo;
import com.onecity.os.inform.modules.inform.entity.RecipientInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
@ApiModel("转办接口入参")
public class TransferParam
{
    @ApiModelProperty(value = "呈报信息id")
    private String informId;
    @ApiModelProperty(value = "批示信息ID")
    private String instructionId;
    @ApiModelProperty(value = "转办备注")
    private String transferBackup;
    @ApiModelProperty(value = "接受人信息 <recipient，recipientName，department> 必输字段")
    List<RecipientInfo> recipientList;
}
