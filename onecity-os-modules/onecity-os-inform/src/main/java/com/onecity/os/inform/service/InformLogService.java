package com.onecity.os.inform.service;

import com.onecity.os.inform.modules.inform.entity.InformLog;

import java.util.Date;
import java.util.List;

/**
 * @Author: zack
 * @Date: 2022/9/6 17:51
 */
public interface InformLogService {

    /**
     * 呈报信息操作日志记录
     * @param informId
     * @param userId
     * @param userName
     * @param content
     * @param operationTime
     * @return
     */
    boolean createLog(String informId, String userId, String userName, String content, Date operationTime, String backup);

    /**
     * 获取日志列表
     * @param informId
     * @return
     */
    List<InformLog> getLogListById(String informId);


    /**
     * 删除最近一次日志记录
     * @param content
     * @return
     */
    void deleteLastLog(String content, String informId);

    /**
     * 逻辑删除日志记录
     * @param informId
     * @return
     */
    void deleteLog(String informId);
}
