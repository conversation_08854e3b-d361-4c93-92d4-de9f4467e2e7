package com.onecity.os.inform.service;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.inform.modules.inform.entity.InformInfo;
import com.onecity.os.inform.modules.inform.entity.RecipientInfo;
import com.onecity.os.inform.modules.inform.vo.DailyDataVo;
import com.onecity.os.inform.modules.inform.vo.InformVo;

import java.util.Date;
import java.util.List;

/**
 * @Author: zack
 * @Date: 2022/6/9 15:58
 */
public interface InformService {

    /**
     * 新增/编辑，数据集接口
     * @param informInfo informInfo
     * @return result
     */
    BaseResult<InformInfo> saveData(InformInfo informInfo);

    /**
     * 呈报操作接口
     * @param informId informId
     * @param operation operation
     * @param userId userId
     * @param userName userName
     * @return result
     */
    BaseResult<String> informOperation(String informId, String operation, String userId, String userName);

    /**
     * 呈报信息列表分页查询接口
     * @param informVo informVo
     * @return result
     */
    List<InformInfo> queryPageListByParam(InformVo informVo);

    /**
     * 呈报信息详情查询
     * @param id id
     * @return result
     */
    BaseResult<InformInfo> queryDetails(String id);

    /**
     *  更新 指标图表类型 indicatorExhibitType
     * @param indicatorId 指标id
     * @param exhibitType 指标图表类型
     */
    public String updateIndExhibitType(String indicatorId, String exhibitType);

    /**
     * 续推
     * @param id 呈报id
     * @param recipients 接收人
     * @return
     */
    BaseResult push(String id, List<RecipientInfo> recipients);

    /**
     * 查询所有呈报的总数
     *
     * @return 返回通知的总数，返回类型为Long
     */
    Long queryAllInformCount(Date today);
    /**
     * 查询指定日期范围内的每日呈报数量
     *
     * @param dayStart 开始日期，格式为"yyyy-MM-dd"
     * @param dayEnd   结束日期，格式为"yyyy-MM-dd"
     * @return 返回一个包含每日呈报数量的列表，若输入为空或无效则返回空列表
     */
    List<DailyDataVo> queryDailyInformCount(String dayStart, String dayEnd);

    /**
     * 呈报信息详情查询
     * @param id id
     * @return result
     */
    BaseResult<List<RecipientInfo>> queryRecipients(String id);

    /**
     * 强制删除
     * @param informId informId
     * @return result
     */
    BaseResult<String> clean(String informId);
}
