package com.onecity.os.inform.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.exception.ServiceException;
import com.onecity.os.common.core.text.UUID;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.inform.constant.UtilConstants;
import com.onecity.os.inform.modules.inform.dto.RemindInfoDto;
import com.onecity.os.inform.modules.inform.entity.InformInfo;
import com.onecity.os.inform.modules.inform.entity.InformInstruct;
import com.onecity.os.inform.modules.inform.feign.MessageFeignService;
import com.onecity.os.inform.modules.inform.mapper.InformInstructMapper;
import com.onecity.os.inform.modules.inform.mapper.InformMapper;
import com.onecity.os.inform.modules.inform.mapper.InstructionMapper;
import com.onecity.os.inform.modules.inform.vo.*;
import com.onecity.os.inform.service.InformInstructService;
import com.onecity.os.inform.utils.DateUtils;
import com.onecity.os.inform.utils.FastDFSClient;
import com.onecity.os.system.api.RemoteUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 */
@Slf4j
@Service
public class InformInstructServiceImpl implements InformInstructService {

    @Value("${app.baseMessageUrl}")
    private String baseMessageUrl;

    @Value("${app.informMessageUrl}")
    private String informMessageUrl;

    @Autowired
    private InformInstructMapper informInstructMapper;
    @Resource
    private FastDFSClient dfsClient;
    @Resource
    private RemoteUserService remoteUserService;
    @Resource
    private MessageFeignService messageFeignService;
    @Autowired
    private InformMapper informMapper;
    @Autowired
    private InstructionMapper instructionMapper;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public List<InstructionsVo> instructionsList(InstructionsListParam instructionsListParam) {
        return informInstructMapper.selectList(instructionsListParam);
    }

    @Override
    public List<ReportAppVo> reportList(String userId) {
        List<ReportAppVo> reportAppVoList = informInstructMapper.reportList(userId);
        //判断批示状态
        for(ReportAppVo reportAppVo : reportAppVoList){
            if(2 == reportAppVo.getRead()){
                reportAppVo.setState(1);
                reportAppVo.setRead(1);
            }else {
                reportAppVo.setState(0);
            }
            InformInfo informInfo = informMapper.queryByInformId(reportAppVo.getInformId());
            if(UtilConstants.State.ARCHIVED.equals(informInfo.getState())){
                reportAppVo.setState(2);
            }
        }
        return reportAppVoList;
    }

    @Override
    public List<InstructionsVo> myInstructionList(String userId) {
        return informInstructMapper.myInstructionList(userId);
    }

    @Override
    @Transactional
    public int addInstruction(InstructionParam instructionParam) {
        InformInstruct informInstruct = new InformInstruct();
        informInstruct.setId(UUID.randomUUID().toString().trim());
        informInstruct.setInformId(instructionParam.getInformId());
        informInstruct.setOperator(instructionParam.getUserId());
        informInstruct.setOperatorName(instructionParam.getUserName());
        informInstruct.setOperationTime(DateUtils.getNowDate());
        informInstruct.setFileUrl(instructionParam.getFileUrl());
        //将状态置为已批示
        informInstructMapper.instructInform(instructionParam.getUserId(),instructionParam.getInformId());
        return informInstructMapper.addInstruction(informInstruct);
    }

    @Override
    public void downloadInstruction(String fileUrl, HttpServletResponse response) throws IOException {
        String fileName = DateUtils.getTime() + ".png";
        byte[] bytes = dfsClient.download(fileUrl);
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setCharacterEncoding("UTF-8");
        ServletOutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            outputStream.write(bytes);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                outputStream.flush();
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public String uploadInstruction(MultipartFile file) {
        String result;
        try {
            result = dfsClient.uploadFile(file);
        } catch (IOException e) {
            e.printStackTrace();
            result = "上传失败";
        }
        return result;
    }

    @Override
    public int readInform(String userId, String informId) {
        Integer read = informInstructMapper.getReadByUserId(userId,informId);
        if(read > 0){
            return 1;
        }else {
            return informInstructMapper.readInform(userId,informId);
        }
    }

    @Override
    public void addInstructionMsg(String userId, String userName, String informId) {
        //获取批示人接口人
        List<String> ids = instructionMapper.getInterfaceUserId(userId);
        if (CollectionUtils.isNotEmpty(ids)) {
            //【阅批呈报】批示提醒：“批示人”关于“标题”的批示，请注意查收
            InformInfo informInfo = informMapper.queryByInformId(informId);
            RemindInfoDto remind = new RemindInfoDto();
            remind.setRemindTitle("【阅批呈报】批示消息");
            remind.setIsRead((byte) 0);
            remind.setRemindedType((byte) 2);
            remind.setIsDelete((byte) 0);
            remind.setCreater(informInfo.getCreatorName());
            remind.setCreateTime(DateUtils.dateTimeIntact());
            remind.setRemindContent(userName + "对" + informInfo.getSimpleTitle() + "进行了批示，请及时查收。");
            remind.setLevel("3");
            remind.setAppUserId(null);
            //拼接接受人信息
            StringBuilder appUserIds = new StringBuilder();
            ids.forEach(recipient -> appUserIds.append(recipient).append(","));
            //去掉最后一个,
            String usrIds = appUserIds.substring(0, appUserIds.length() - 1);
            remind.setPcUserId(usrIds);
            remind.setSourceType(UtilConstants.Source.PC);
            remind.setMessageUrl(baseMessageUrl + informMessageUrl);
            remind.setAppMsgType("1");
            messageFeignService.addMsg(Collections.singletonList(remind));
        }
    }
    /**
     * 查询所有通知的总数
     *
     * @return 返回批示的总数，返回类型为Long
     */
    @Override
    public Long queryAllInformCount(Date today) {
        return informInstructMapper.queryAllInformCount(today);
    }


    /**
     * 查询指定日期范围内的每日批示数量
     *
     * @param dayStart 开始日期，格式为"yyyy-MM-dd"
     * @param dayEnd   结束日期，格式为"yyyy-MM-dd"
     * @return 返回一个包含每日批示数量的列表，若输入为空或无效则返回空列表
     */
    @Override
    public List<DailyDataVo> queryDailyInstructionsCount(String dayStart, String dayEnd) {
        // 检查输入日期是否为空或无效
        if (StringUtils.isAnyBlank(dayStart, dayEnd)) {
            throw new ServiceException("起始日期不能为空，请核对");
        }
        // 将字符串日期转换为Date对象
        Date dateStart = DateUtils.parseDate(dayStart);
        Date dateEnd = DateUtils.parseDate(dayEnd);
        if (dateStart == null || dateEnd == null) {
            log.error("查询指定日期范围内的每日呈报数量参数异常dayStart:{},dayEnd:{}", JSONObject.toJSONString(dayStart), JSONObject.toJSONString(dayEnd));
            throw new ServiceException("起始日期不合法，请重试选择");
        }
        // 确保开始日期小于等于结束日期
        if (dateStart.after(dateEnd)) {
            throw new ServiceException("起始日期错误，请重新选择");
        }
        // 计算日期差值
        Calendar startCal = Calendar.getInstance();
        Calendar endCal = Calendar.getInstance();
        startCal.setTime(dateStart);
        endCal.setTime(dateEnd);
        long daysBetween = (endCal.getTimeInMillis() - startCal.getTimeInMillis()) / (1000 * 60 * 60 * 24);

        // 检查日期差值是否合理
        if (daysBetween <= 90) {
            // 从数据库查询每日指令数量
            List<DailyDataVo> dailyDataVos = informInstructMapper.queryDailyInstructionsCount(dateStart, dateEnd);
            // 处理查询结果，填充每日数据
            return populateDailyData(dailyDataVos, dateStart, daysBetween);
        } else {
            throw new ServiceException("每次查询最多查90天的数据，请重新选择");
        }
    }


    /**
     * 根据起始日期和日期间隔填充每日数据
     * 如果每日数据列表中不存在对应日期的数据，则使用默认值填充
     *
     * @param dailyDataVos 每日数据列表
     * @param dateStart 起始日期
     * @param daysBetween 起始日期与结束日期之间的间隔天数
     * @return 填充后的每日数据列表
     */
    private List<DailyDataVo> populateDailyData(List<DailyDataVo> dailyDataVos, Date dateStart, long daysBetween) {
        // 初始化当前日期为起始日期
        LocalDate currentDate = dateStart.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        // 创建结果列表存储填充后的每日数据
        List<DailyDataVo> result = new ArrayList<>();

        // 循环处理，直到日期间隔小于0
        while (daysBetween >= 0) {
            // 格式化当前日期为字符串
            String dateStr = currentDate.format(FORMATTER);
            // 查找每日数据列表中与当前日期匹配的数据
            DailyDataVo dailyDataVo = dailyDataVos.stream()
                    .filter(vo -> vo.getDate().equals(dateStr))
                    .findFirst()
                    .orElseGet(() -> {
                        // 如果未找到，使用当前日期的简写形式创建默认每日数据对象
                        String abbrDate = dateStr.substring(dateStr.length() - 5);
                        return createDefaultDailyDataVo(abbrDate, dateStr);
                    });
            // 将每日数据添加到结果列表中
            result.add(dailyDataVo);
            // 递增日期
            currentDate = currentDate.plusDays(1);
            // 递减日期间隔
            daysBetween--;
        }
        // 返回填充后的每日数据列表
        return result;
    }
    /**
     * 创建一个默认的每日数据对象。
     *
     * @param abbrDate 简写日期
     * @param dateStr 完整日期字符串
     * @return 默认的每日数据对象
     */
    private DailyDataVo createDefaultDailyDataVo(String abbrDate, String dateStr) {
        DailyDataVo dailyDataVo = new DailyDataVo();
        dailyDataVo.setAbbrDate(abbrDate);
        dailyDataVo.setDate(dateStr);
        dailyDataVo.setNum(0);
        return dailyDataVo;
    }
}
