package com.onecity.os.inform.service.impl;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.onecity.os.common.core.text.UUID;
import com.onecity.os.inform.modules.inform.entity.InformInfo;
import com.onecity.os.inform.modules.inform.entity.InformLog;
import com.onecity.os.inform.modules.inform.entity.ProcessInfo;
import com.onecity.os.inform.modules.inform.mapper.InformLogMapper;
import com.onecity.os.inform.modules.inform.mapper.InformMapper;
import com.onecity.os.inform.modules.inform.mapper.ProcessInfoMapper;
import com.onecity.os.inform.service.InformLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Author: zack
 * @Date: 2022/9/6 17:52
 */
@Slf4j
@Service("informLogService")
public class InformLogServiceImpl implements InformLogService {

    @Resource
    private InformLogMapper informLogMapper;

    @Resource
    private InformMapper informMapper;

    @Resource
    private ProcessInfoMapper processInfoMapper;

    @Override
    public boolean createLog(String informId, String userId, String userName, String content, Date operationTime, String backup) {
        //记录操作信息
        InformLog log = new InformLog();
        log.setId(UUID.randomUUID().toString().trim());
        log.setInformId(informId);
        log.setOperator(userId);
        log.setOperatorName(userName);
        log.setContent(content);
        log.setOperationTime(operationTime);
        log.setBackup(backup);
        informLogMapper.insertLog(log);
        return true;
    }

    @Override
    public List<InformLog> getLogListById(String informId) {
        List<InformLog> informLogs = informLogMapper.getLogListById(informId);
        //匹配对应呈报
        InformInfo informInfo = informMapper.queryByInformId(informId);
        //获取流程名称
        if (StringUtil.isNotEmpty(informInfo.getProcessId())) {
            ProcessInfo processInfo = processInfoMapper.queryProcessInfoById(informInfo.getProcessId());
            if (null != processInfo) {
                for (InformLog informLog : informLogs) {
                    informLog.setProcessName(processInfo.getProcessName());
                }
            }
        }
        return informLogs;
    }

    @Override
    public void deleteLastLog(String content, String informId) {
        informLogMapper.deleteLastLog(content, informId);
    }

    @Override
    public void deleteLog(String informId) {
        informLogMapper.deleteLog(informId);
    }
}
