package com.onecity.os.inform.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.text.UUID;
import com.onecity.os.inform.constant.UtilConstants;
import com.onecity.os.inform.modules.inform.entity.InformRoleInfo;
import com.onecity.os.inform.modules.inform.entity.InformRoleState;
import com.onecity.os.inform.modules.inform.entity.ProcessStepInfo;
import com.onecity.os.inform.modules.inform.entity.StepRoleLink;
import com.onecity.os.inform.modules.inform.mapper.InformRoleInfoMapper;
import com.onecity.os.inform.modules.inform.mapper.InformRoleStateMapper;
import com.onecity.os.inform.modules.inform.mapper.ProcessStepInfoMapper;
import com.onecity.os.inform.modules.inform.mapper.StepRoleLinkMapper;
import com.onecity.os.inform.service.InformRoleStateService;
import com.onecity.os.system.api.domain.SysUser;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @Author: zack
 * @Date: 2024/8/19 12:01
 */
@Slf4j
@Service("informRoleStateService")
public class InformRoleStateServiceImpl implements InformRoleStateService {

    @Resource
    private ProcessStepInfoMapper processStepInfoMapper;

    @Resource
    private StepRoleLinkMapper stepRoleLinkMapper;

    @Resource
    private InformRoleStateMapper informRoleStateMapper;

    @Resource
    private InformRoleInfoMapper informRoleInfoMapper;

    @Override
    public void createInformRoleMap(String informId, String processId) {
        log.info("createInformRoleMap : informId : {} ; processId : {} ", JSONObject.toJSONString(informId), JSONObject.toJSONString(processId));
        //查询步骤
        List<ProcessStepInfo> stepInfos = processStepInfoMapper.queryProcessStepInfoList(processId);
        stepInfos.forEach(step -> {
            //角色
            List<StepRoleLink> roleLinks = stepRoleLinkMapper.queryByParam(step.getId(), processId);
            roleLinks.forEach(role -> {
                //创建信息
                InformRoleState roleState = new InformRoleState();
                roleState.setId(UUID.randomUUID().toString().trim());
                roleState.setInformId(informId);
                roleState.setProcessId(processId);
                roleState.setCurrentStepId(step.getId());
                roleState.setRoleId(role.getRoleId());
                roleState.setState(UtilConstants.State.AUDIT);
                log.info("createInformRoleMap : roleState : {} ", JSONObject.toJSONString(roleState));
                informRoleStateMapper.insert(roleState);
            });
        });

    }

    @Override
    public void updatePyInformId(String informId, String state) {
        informRoleStateMapper.updatePyInformId(informId, state);
    }

    @Override
    public void updateInformRoleMap(String informId, String processId) {
        log.info("updateInformRoleMap : informId : {} ; processId : {} ", JSONObject.toJSONString(informId), JSONObject.toJSONString(processId));
        //判断逻辑 ： 如未查询到对应数据则，是将草稿状态的呈报数据第一次保存，则走新增逻辑
        //查询
        List<InformRoleState> informRoleStates = informRoleStateMapper.queryByInformId(informId);
        if (CollectionUtils.isEmpty(informRoleStates)) {
            createInformRoleMap(informId, processId);
        } else {
            informRoleStates.forEach(roleState -> informRoleStateMapper.updatePyInformId(informId, UtilConstants.State.AUDIT));
        }
    }

    @Override
    public void createInformRoleInform(String informId) {
        SysUser user = SecurityUtils.getLoginUser().getSysUser();
        if (0 != user.getRoleIds().length) {
            Arrays.stream(user.getRoleIds()).forEach(roleId -> {
                InformRoleInfo informRoleInfo = new InformRoleInfo();
                informRoleInfo.setId(UUID.randomUUID().toString().trim());
                informRoleInfo.setCreateTime(new Date());
                informRoleInfo.setRoleId(roleId);
                informRoleInfo.setInformId(informId);
                informRoleInfoMapper.insert(informRoleInfo);
            });
        }
    }
}
