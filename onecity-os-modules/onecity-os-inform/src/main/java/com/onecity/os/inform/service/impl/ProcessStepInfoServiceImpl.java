package com.onecity.os.inform.service.impl;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.IdGen;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.inform.modules.inform.entity.ProcessInfo;
import com.onecity.os.inform.modules.inform.entity.ProcessStepInfo;
import com.onecity.os.inform.modules.inform.entity.StepRoleLink;
import com.onecity.os.inform.modules.inform.mapper.InformMapper;
import com.onecity.os.inform.modules.inform.mapper.ProcessInfoMapper;
import com.onecity.os.inform.modules.inform.mapper.ProcessStepInfoMapper;
import com.onecity.os.inform.modules.inform.mapper.StepRoleLinkMapper;
import com.onecity.os.inform.modules.inform.vo.RoleVo;
import com.onecity.os.inform.service.ProcessStepInfoService;
import com.onecity.os.system.api.RemoteUserService;
import com.onecity.os.system.api.domain.SysRole;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * ProcessStepInfoServiceImpl
 *
 * <AUTHOR>
 * @since 2024/8/9 14:54
 */
@Slf4j
@Service("processStepInfoService")
public class ProcessStepInfoServiceImpl implements ProcessStepInfoService {

    @Resource
    private ProcessStepInfoMapper processStepInfoMapper;

    @Resource
    private ProcessInfoMapper processInfoMapper;

    @Resource
    private StepRoleLinkMapper stepRoleLinkMapper;

    @Resource
    private InformMapper informMapper;

    @Resource
    private RemoteUserService remoteUserService;

    @Override
    public BaseResult move(String processStepInfoId, Integer type) {
        ProcessStepInfo processStepInfo = processStepInfoMapper.queryProcessStepInfo(processStepInfoId);
        if (processStepInfo == null) {
            return BaseResult.fail("流程不存在");
        }
        if (informMapper.queryProcessingByProcessId(processStepInfo.getProcessId()) != null) {
            return BaseResult.fail("目前有审核正在使用该流程，请在相关审核结束后进行操作。");
        }
        LoginUser user = SecurityUtils.getLoginUser();
        if (user == null) {
            return BaseResult.fail("获取用户信息失败");
        }
        ProcessStepInfo processStepInfoMd;
        if (type == 1) {
            processStepInfoMd = processStepInfoMapper.querySmallerProcessStepInfoByStepNum(processStepInfo.getProcessId(), processStepInfo.getStepNum());
        } else {
            processStepInfoMd = processStepInfoMapper.queryBiggerProcessStepInfoByStepNum(processStepInfo.getProcessId(), processStepInfo.getStepNum());
        }
        if (processStepInfoMd == null) {
            return BaseResult.fail("流程无法移动");
        }
        processStepInfoMapper.updateStepNum(processStepInfo.getId(), processStepInfoMd.getStepNum(), user.getUsername(), new Date());
        processStepInfoMapper.updateStepNum(processStepInfoMd.getId(), processStepInfo.getStepNum(), user.getUsername(), new Date());
        updateProcessUpdateTime(processStepInfo.getProcessId(), new Date(), user.getSysUser().getNickName());
        return BaseResult.ok();
    }

    @Override
    public BaseResult<List<ProcessStepInfo>> list(String processId) {
        if (StringUtils.isEmpty(processId)) {
            return BaseResult.fail("流程id不能为空");
        }

        List<ProcessStepInfo> processStepInfos = processStepInfoMapper.queryProcessStepInfoList(processId);
        for (ProcessStepInfo processStepInfo : processStepInfos) {
            List<StepRoleLink> stepRoleLinks = stepRoleLinkMapper.queryByStepId(processStepInfo.getId());
            List<RoleVo> roleVos = new ArrayList<>();
            if (stepRoleLinks != null && stepRoleLinks.size() > 0) {
                String[] roleIds = stepRoleLinks.stream().map(StepRoleLink::getRoleId).map(String::valueOf).toArray(String[]::new);
                BaseResult<List<SysRole>> roleList = remoteUserService.getRoleNameByRoleIds(roleIds);
                if (BaseResult.FAIL == roleList.getCode()) {
                    return BaseResult.fail("查询角色失败");
                }
                roleList.getData().forEach(r -> {
                    RoleVo roleVo = new RoleVo();
                    roleVo.setRoleId(r.getRoleId());
                    roleVo.setRoleName(r.getRoleName());
                    roleVos.add(roleVo);
                });
            }
            processStepInfo.setRoleIds(roleVos);
        }
        return BaseResult.ok(processStepInfos);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResult del(String id) {
        if (StringUtils.isEmpty(id)) {
            return BaseResult.fail("步骤id不能为空");
        }
        ProcessStepInfo stepInfoDB = processStepInfoMapper.queryProcessStepInfo(id);
        if (stepInfoDB == null) {
            return BaseResult.fail("步骤id不存在");
        }
        LoginUser user = SecurityUtils.getLoginUser();
        if (user == null) {
            return BaseResult.fail("获取用户信息失败");
        }
        if (informMapper.queryProcessingByProcessId(stepInfoDB.getProcessId()) != null) {
            return BaseResult.fail("目前有审核正在使用该流程，请在相关审核结束后进行操作。");
        }

        ProcessStepInfo processStepInfo = new ProcessStepInfo();
        processStepInfo.setId(id);
        processStepInfo.setUpdaterName(user.getUsername());
        processStepInfo.setUpdateTime(new Date());
        processStepInfoMapper.del(processStepInfo);
        stepRoleLinkMapper.delByStepId(id);
        updateProcessUpdateTime(stepInfoDB.getProcessId(), new Date(), user.getSysUser().getNickName());
        return BaseResult.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResult save(String id, String processId, String stepName, List<RoleVo> roleIds) {
        if ((StringUtils.isNotEmpty(stepName) && stepName.length() > 15)
                || StringUtils.isEmpty(processId)
                || roleIds == null || roleIds.size() == 0) {
            return BaseResult.fail("参数不合法");
        }
        if (processInfoMapper.queryProcessInfoById(processId) == null) {
            return BaseResult.fail("流程不存在");
        }
        if (informMapper.queryProcessingByProcessId(processId) != null) {
            return BaseResult.fail("目前有审核正在使用该流程，请在相关审核结束后进行操作。");
        }

        String[] rids = roleIds.stream().map(RoleVo::getRoleId).map(String::valueOf).toArray(String[]::new);
        BaseResult<List<SysRole>> roleList = remoteUserService.getRoleNameByRoleIds(rids);
        if (BaseResult.FAIL == roleList.getCode()) {
            return BaseResult.fail("查询角色失败");
        }
        List<SysRole> roles =  roleList.getData();
        for (RoleVo vo : roleIds) {
            boolean exist = false;
            for (SysRole role : roles) {
                if (role.getRoleId().equals(vo.getRoleId())) {
                    exist = true;
                    break;
                }
            }
            if (!exist) {
                return BaseResult.fail("角色不存在");
            }
        }

        LoginUser user = SecurityUtils.getLoginUser();
        if (user == null) {
            return BaseResult.fail("获取用户信息失败");
        }

        ProcessStepInfo processStepInfo = new ProcessStepInfo();
        processStepInfo.setStepName(stepName);
        processStepInfo.setProcessId(processId);

        if (StringUtils.isNotEmpty(id)) {
            if (processStepInfoMapper.queryProcessStepInfo(id) == null) {
                return BaseResult.fail("步骤不存在");
            }
            stepRoleLinkMapper.delByStepId(id);
            for (RoleVo vo : roleIds) {
                stepRoleLinkMapper.insert(processId, id, vo.getRoleId());
            }
            processStepInfo.setId(id);
            processStepInfo.setUpdateTime(new Date());
            processStepInfo.setUpdaterName(user.getSysUser().getNickName());
            processStepInfoMapper.updateProcessStepInfo(processStepInfo);
        } else {
            List<ProcessStepInfo> infoList = processStepInfoMapper.queryProcessStepInfoList(processId);
            int stepNum = 1;
            if (infoList != null && infoList.size() > 0) {
                stepNum = infoList.get(infoList.size() - 1).getStepNum() + 1;
            }
            String stepId = IdGen.uuid();
            for (RoleVo vo : roleIds) {
                stepRoleLinkMapper.insert(processId, stepId, vo.getRoleId());
            }
            processStepInfo.setStepNum(stepNum);
            processStepInfo.setId(stepId);
            processStepInfo.setCreateTime(new Date());
            processStepInfo.setCreatorName(user.getSysUser().getNickName());
            processStepInfoMapper.insertProcessStepInfo(processStepInfo);
        }
        updateProcessUpdateTime(processId, new Date(), user.getSysUser().getNickName());
        return BaseResult.ok();
    }

    private void updateProcessUpdateTime(String processId, Date updateTime, String updaterName) {
        ProcessInfo processInfo = new ProcessInfo();
        processInfo.setId(processId);
        processInfo.setUpdateTime(updateTime);
        processInfo.setUpdaterName(updaterName);
        processInfoMapper.updateProcessUpdateTime(processInfo);
    }
}
