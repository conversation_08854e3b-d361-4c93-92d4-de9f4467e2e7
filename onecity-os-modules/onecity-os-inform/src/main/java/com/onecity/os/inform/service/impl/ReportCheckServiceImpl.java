package com.onecity.os.inform.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.text.UUID;
import com.onecity.os.inform.constant.UtilConstants;
import com.onecity.os.inform.modules.inform.dto.RemindInfoDto;
import com.onecity.os.inform.modules.inform.entity.*;
import com.onecity.os.inform.modules.inform.feign.MessageFeignService;
import com.onecity.os.inform.modules.inform.feign.TodoFeignService;
import com.onecity.os.inform.modules.inform.mapper.*;
import com.onecity.os.inform.modules.inform.vo.*;
import com.onecity.os.inform.service.ReportCheckService;
import com.onecity.os.inform.utils.DateUtils;
import com.onecity.os.inform.utils.SMSUtil;
import com.onecity.os.system.api.RemoteUserService;
import com.onecity.os.system.api.domain.SysUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 */
@Slf4j
@Service
public class ReportCheckServiceImpl implements ReportCheckService {

    @Value("${app.baseMessageUrl}")
    private String baseMessageUrl;

    @Value("${app.informMessageUrl}")
    private String informMessageUrl;

    @Autowired
    private ReviewerMapper reviewerMapper;
    @Autowired
    private InformLogMapper informLogMapper;
    @Autowired
    private InformMapper informMapper;
    @Autowired
    private RecipientMapper recipientMapper;
    @Resource
    private TodoFeignService todoFeignService;
    @Resource
    private MessageFeignService messageFeignService;
    @Resource
    private ProcessInfoMapper processInfoMapper;
    @Resource
    private ProcessStepInfoMapper processStepInfoMapper;
    @Resource
    private PushUserMapper pushUserMapper;
    @Resource
    private InformRoleStateMapper informRoleStateMapper;
    @Resource
    private StepRoleLinkMapper stepRoleLinkMapper;
    @Resource
    private RemoteUserService remoteUserService;


    /**
     * 查询待审核列表
     * @param reportCheckParam
     * @return
     */
    @Override
    public List<ReportCheckVo> pendingList(ReportCheckParam reportCheckParam) {
        List<ReportCheckVo> result = reviewerMapper.pendingListByRoles(reportCheckParam, reportCheckParam.getRoleIds());
        result.forEach(reportCheck -> {
            //查询是否存在下一个步骤
            //查询审核流程步骤
            List<ProcessStepInfo> processStepInfos = processStepInfoMapper.queryProcessStepInfoList(reportCheck.getProcessId());
            //获取当前步骤
            ProcessStepInfo currentStepInfo = processStepInfos.stream()
                    .filter(processStepInfo -> processStepInfo.getId().equals(reportCheck.getCurrentStepId()))
                    .findFirst().orElse(null);
            //查询是否存在下一个步骤
            if (null != currentStepInfo) {
                ProcessStepInfo nextProcessStep = processStepInfos.stream()
                        .filter(step -> step.getStepNum().equals(currentStepInfo.getStepNum() + 1)).findFirst().orElse(null);
                if (null != nextProcessStep){
                    reportCheck.setLast(0);
                } else {
                    reportCheck.setLast(1);
                }
            }
        });
        /*翻译状态，方便前端展示*/
        this.tranStatus(result);
        return result;
    }

    /**
     *查询已审核列表
     * @param reportCheckParam
     * @return
     */
    @Override
    public List<ReportCheckVo> checkedList(ReportCheckParam reportCheckParam) {
        List<ReportCheckVo> result = reviewerMapper.checkedListByRoles(reportCheckParam,reportCheckParam.getRoleIds());
        /*翻译状态，方便前端展示*/
        this.tranStatus(result);
        return result;
    }

    private void tranStatus(List<ReportCheckVo> list){
        for(ReportCheckVo reportCheckVo : list){
            switch (reportCheckVo.getState()) {
                case UtilConstants.State.DRAFT:
                    reportCheckVo.setStateStr("未提交");
                    break;
                case UtilConstants.State.PUBLISH:
                    reportCheckVo.setStateStr("已发布");
                    break;
                case UtilConstants.State.AUDIT:
                    reportCheckVo.setStateStr("审核中");
                    break;
                case UtilConstants.State.EDIT:
                    reportCheckVo.setStateStr("编辑/保存");
                    break;
                case UtilConstants.State.OVERRULE:
                    reportCheckVo.setStateStr("被驳回");
                    break;
                case UtilConstants.State.REVOKE:
                    reportCheckVo.setStateStr("撤回");
                    break;
                case UtilConstants.State.DELETED:
                    reportCheckVo.setStateStr("删除");
                    break;
                case UtilConstants.State.SUBMIT:
                    reportCheckVo.setStateStr("提交");
                    break;
                case UtilConstants.State.PUSH_AUDIT:
                    reportCheckVo.setStateStr("续推审核中");
                    break;
                case UtilConstants.State.PUSH_PUBLISH:
                    reportCheckVo.setStateStr("续推已发布");
                    break;
                case UtilConstants.State.PUSH_OVERRULE:
                    reportCheckVo.setStateStr("续推被驳回");
                    break;
                case UtilConstants.State.ARCHIVED:
                    reportCheckVo.setStateStr("已归档");
                    break;
                case UtilConstants.State.AUDITED:
                    reportCheckVo.setStateStr("已审核");
                    break;
                case UtilConstants.State.PUSH_AUDITED:
                    reportCheckVo.setStateStr("续推已审核");
                    break;
                default:
                    continue;
            }
            switch (reportCheckVo.getInformState()) {
                case UtilConstants.State.DRAFT:
                    reportCheckVo.setInformStateStr("未提交");
                    break;
                case UtilConstants.State.PUBLISH:
                    reportCheckVo.setInformStateStr("已发布");
                    break;
                case UtilConstants.State.AUDIT:
                    reportCheckVo.setInformStateStr("审核中");
                    break;
                case UtilConstants.State.EDIT:
                    reportCheckVo.setInformStateStr("编辑/保存");
                    break;
                case UtilConstants.State.OVERRULE:
                    reportCheckVo.setInformStateStr("被驳回");
                    break;
                case UtilConstants.State.REVOKE:
                    reportCheckVo.setInformStateStr("撤回");
                    break;
                case UtilConstants.State.DELETED:
                    reportCheckVo.setInformStateStr("删除");
                    break;
                case UtilConstants.State.SUBMIT:
                    reportCheckVo.setInformStateStr("提交");
                    break;
                case UtilConstants.State.PUSH_AUDIT:
                    reportCheckVo.setInformStateStr("续推审核中");
                    break;
                case UtilConstants.State.PUSH_PUBLISH:
                    reportCheckVo.setInformStateStr("续推已发布");
                    break;
                case UtilConstants.State.PUSH_OVERRULE:
                    reportCheckVo.setInformStateStr("续推被驳回");
                    break;
                case UtilConstants.State.ARCHIVED:
                    reportCheckVo.setInformStateStr("已归档");
                    break;
                case UtilConstants.State.AUDITED:
                    reportCheckVo.setInformStateStr("已审核");
                    break;
                case UtilConstants.State.PUSH_AUDITED:
                    reportCheckVo.setInformStateStr("续推已审核");
                    break;
                default:
                    continue;
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int check(CheckParam checkParam) {
        //确认 inform
        InformInfo informInfo = informMapper.queryByInformId(checkParam.getInformId());
        if (UtilConstants.State.DELETED.equals(informInfo.getState())) {
            return 3;
        }
        if (UtilConstants.State.OVERRULE.equals(informInfo.getState()) || UtilConstants.State.PUSH_OVERRULE.equals(informInfo.getState())){
            return 4;
        }
        //判断 呈报在当前步骤角色下，是否已审核/驳回
        for (Long role : checkParam.getRoleIds()) {
            //判断已审核
            List<InformRoleState> checkAudited = informRoleStateMapper.queryByParam(checkParam.getInformId(), String.valueOf(role),
                    informInfo.getProcessId(), informInfo.getCurrentStepId(), UtilConstants.State.AUDITED);
            if (!CollectionUtils.isEmpty(checkAudited)) {
                log.info("呈报 ： {} ", JSONObject.toJSONString(checkParam.getInformId()));
                return 2;
            }
        }

        //修改审核记录为已处理
        reviewerMapper.updateStateByInformId(checkParam.getInformId());
        //清除审核人待办消息
        todoFeignService.deleteToDoByPram(checkParam.getInformId(), "3");
        //清除旧APP提醒消息
        messageFeignService.deleteMessageByService(checkParam.getInformId(), "reportCheck");
        //操作记录实体
        InformLog informLog = new InformLog();
        informLog.setId(UUID.randomUUID().toString().trim());
        informLog.setInformId(checkParam.getInformId());
        informLog.setOperator(checkParam.getUserId());
        informLog.setOperatorName(checkParam.getUserName());
        informLog.setOperationTime(DateUtils.getNowDate());
        informLog.setBackup(checkParam.getBackup());
        //不同操作判断
        switch (checkParam.getHandleType()) {
            case 0:
                //驳回操作
                informLog.setContent("驳回");
                //更新呈报状态 第一次驳回状态为 OVERRULE ；续推驳回状态未 PUSH_REVOKE
                List<PushUser> pushUsers = pushUserMapper.queryByInformId(checkParam.getInformId());
                if (!CollectionUtils.isEmpty(pushUsers)) {
                    informLog.setContent("续推驳回");
                    informMapper.updateStateById(checkParam.getInformId(), UtilConstants.State.PUSH_OVERRULE);
                } else {
                    informMapper.updateStateById(checkParam.getInformId(), UtilConstants.State.OVERRULE);
                }
                //清除提交时间
                informMapper.removePublishTimeById(checkParam.getInformId());
                //审核驳回后，提交人收到驳回待办消息：【阅批呈报】您提交的呈报信息被驳回，请尽快处理。
                InformInfo inform = informMapper.queryByInformId(checkParam.getInformId());
                //驳回待办信息
                ToDoVo rejectToDoVo = new ToDoVo();
                rejectToDoVo.setTitle("【阅批呈报】驳回待提交\r\n您提交的呈报信息被驳回，请及时调整后再提交。");
                rejectToDoVo.setToDoUserId(inform.getCreator());
                rejectToDoVo.setToDoUserName(inform.getCreatorName());
                rejectToDoVo.setCreateTime(DateUtils.getNowDate());
                rejectToDoVo.setIsDelete((byte) 0);
                rejectToDoVo.setToDoSource("3");
                rejectToDoVo.setQuestionnaireId(checkParam.getInformId());
                rejectToDoVo.setAuditResult("0");
                todoFeignService.addTodo(rejectToDoVo);
                //更新呈报审核人角色信息表状态未已驳回
                informRoleStateMapper.updatePyParam(checkParam.getInformId(), null, inform.getProcessId(),
                        inform.getCurrentStepId(), UtilConstants.State.OVERRULE);
                //将未呈报的接受人呈报状态（informFlag）调整未驳回 （2）
                recipientMapper.updateInformFlag(null , checkParam.getInformId(), 2);
                break;
            case 1:
                //提交
                informLog.setContent("审核通过");
                //新增审核人
                ReviewerInfo reviewerInfo = new ReviewerInfo();
                reviewerInfo.setId(UUID.randomUUID().toString().trim());
                reviewerInfo.setInformId(checkParam.getInformId());
                reviewerInfo.setReviewerId(checkParam.getReviewer());
                reviewerInfo.setReviewerName(checkParam.getReviewerName());
                reviewerInfo.setState(0);
                reviewerInfo.setCreateTime(DateUtils.getNowDate());
                reviewerMapper.insertReviewer(reviewerInfo);
                //查询审核流程
                ProcessInfo processInfo = processInfoMapper.queryProcessInfoById(informInfo.getProcessId());
                //查询审核流程步骤
                List<ProcessStepInfo> processStepInfos = processStepInfoMapper.queryProcessStepInfoList(processInfo.getId());
                //获取当前执行步骤
                // processStepId
                final String processStepId = informInfo.getCurrentStepId();
                ProcessStepInfo processStep = processStepInfos.stream()
                        .filter(step -> step.getId().equals(processStepId)).findFirst().orElse(null);
                //获取下一步步骤
                ProcessStepInfo nextProcessStep = null;
                if (null != processStep) {
                    nextProcessStep = processStepInfos.stream()
                            .filter(step -> step.getStepNum().equals(processStep.getStepNum() + 1)).findFirst().orElse(null);
                }
                if (null != nextProcessStep) {
                    //未审核到最后一步，更新inform信息到下一步骤
                    informMapper.updateProcessStepById(checkParam.getInformId(), nextProcessStep.getId());
                    //给下一步审核人，新增待办信息
                    List<StepRoleLink> stepRoleLinks = stepRoleLinkMapper.queryByParam(nextProcessStep.getId(), informInfo.getProcessId());
                    String[] roleIds = stepRoleLinks.stream().map(StepRoleLink::getRoleId).map(String::valueOf).toArray(String[]::new);
                    BaseResult<String> pcUserIdResult = remoteUserService.getUserIdsByRoles(roleIds);
                    if (null != pcUserIdResult && null != pcUserIdResult.getData()) {
                        String[] ids = pcUserIdResult.getData().split(",");
                        String[] uniqIds = Arrays.stream(ids).distinct().toArray(String[]::new);
                        BaseResult<List<SysUser>> userListResult =  remoteUserService.getUserByPcUserIds(uniqIds);
                        for (SysUser sysUser : userListResult.getData()) {
                            ToDoVo toDoVo = new ToDoVo();
                            toDoVo.setTitle("【阅批呈报】待审核\r\n您有新的呈报信息待审核，请尽快处理。");
                            toDoVo.setToDoUserId(String.valueOf(sysUser.getUserId()));
                            toDoVo.setToDoUserName(sysUser.getUserName());
                            toDoVo.setCreateTime(DateUtils.getNowDate());
                            toDoVo.setIsDelete((byte) 0);
                            toDoVo.setToDoSource("3");
                            toDoVo.setQuestionnaireId(checkParam.getInformId());
                            toDoVo.setAuditResult("2");
                            todoFeignService.addTodo(toDoVo);
                            addMsg(sysUser.getUserName(), "阅批呈报 待审核",
                                    "您有新的呈报信息待审核，请尽快处理。", String.valueOf(sysUser.getUserId()),
                                    "2", checkParam.getInformId(), "reportCheck");
                            //给下一步审核人，发送短信消息
                            SMSUtil.sendSms(sysUser.getPhonenumber());
                        }
                    }
                } else {
                    //最后一步时，记录为发布
                    informLog.setContent("发布");
                    //更新呈报状态 非续推流程 更新状态为PUBLISH 续推流程更新状态为 PUSH_PUBLISH
                    List<PushUser> push = pushUserMapper.queryByInformId(checkParam.getInformId());
                    if (!CollectionUtils.isEmpty(push)) {
                        //更新呈报状态
                        informMapper.updateStateById(checkParam.getInformId(), UtilConstants.State.PUSH_PUBLISH);
                    } else {
                        //更新呈报状态
                        informMapper.updateStateById(checkParam.getInformId(), UtilConstants.State.PUBLISH);
                        //更新首次发布时间
                        informMapper.updateFirstReleaseTimeById(checkParam.getInformId(), DateUtils.getNowDate());
                    }
                    //已经更新至最后一步
                    //更新发布时间
                    informMapper.updateReleaseTimeById(checkParam.getInformId(), DateUtils.getNowDate());
                    //更新接收人已经呈报字段
                    List<RecipientInfo> recipientInfoList = recipientMapper.queryRecipientByParam(informInfo.getId());
                    recipientInfoList.forEach(recipientInfo -> {
                        recipientMapper.updateInformFlag(recipientInfo.getId(), null, 1);
                        //给接收人发送待办消息
                        addMsg(recipientInfo.getRecipientName(), "阅批呈报 新呈报信息",
                                "您收到一条新的呈报信息，请及时查阅。", recipientInfo.getRecipient(),
                                "1", checkParam.getInformId(), informMessageUrl);
                        //根据接收人id查询手机号
                        String[] uniqIds = {recipientInfo.getRecipient()};
                        BaseResult<List<SysUser>> userListResult =  remoteUserService.getUserByPcUserIds(uniqIds);
                        //给接收人，发送短信消息
                        userListResult.getData().forEach(user -> SMSUtil.sendSms(user.getPhonenumber()));
                    });
                }
                //更新呈报审核人角色信息表
                //获取当前用户角色
                SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
                //获取当前用户角色id
                List<String> roleIds = Arrays.stream(sysUser.getRoleIds()).map(String::valueOf).collect(Collectors.toList());
                //更新呈报审核人角色信息表状态
                roleIds.forEach(roleId -> informRoleStateMapper.updatePyParam(informInfo.getId(), roleId, informInfo.getProcessId(),
                        processStepId , UtilConstants.State.AUDITED));
                break;

            default:
                return 0;
        }
        informLogMapper.insertLog(informLog);
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateReceiver(RecipientParam recipientParam) {
        //接受人信息保存(先清除旧数据)
        recipientMapper.deleteRecipientByInformId(recipientParam.getInformId(), null);
        //保存新记录
        List<RecipientInfo> recipients = recipientParam.getRecipient();
        for(RecipientInfo recipientInfo : recipients){
            recipientInfo.setId(UUID.randomUUID().toString().trim());
            recipientInfo.setInformId(recipientParam.getInformId());
            recipientInfo.setRead(0);
            recipientInfo.setCreateTime(DateUtils.getNowDate());
            recipientInfo.setState(UtilConstants.State.ENABLE);
        }
        recipientMapper.insertRecipientList(recipients);
        return 1;
    }

    /**
     * 阅批呈报发送APP提醒消息
     * @param informId
     */
    private void addAppMsg(String informId) {
        InformInfo informInfo = informMapper.queryByInformId(informId);
        RemindInfoDto remind = new RemindInfoDto();
        remind.setRemindTitle("阅批呈报 新呈报信息");
        remind.setIsRead((byte) 0);
        remind.setRemindedType((byte) 2);
        remind.setIsDelete((byte) 0);
        remind.setCreater(informInfo.getCreatorName());
        remind.setCreateTime(DateUtils.dateTimeIntact());
        remind.setRemindContent("您收到一条新的呈报信息，请及时查阅。");
        remind.setLevel("2");
        remind.setPcUserId(null);
        //获取接受人信息
        List<RecipientInfo> recipientInfoList = recipientMapper.queryRecipientByParam(informInfo.getId());
        //拼接接受人信息
        StringBuilder appUserIds = new StringBuilder();
        recipientInfoList.forEach(recipient -> appUserIds.append(recipient.getRecipient()).append(","));
        //去掉最后一个,
        String usrIds = appUserIds.substring(0, appUserIds.length() - 1);
        remind.setAppUserId(usrIds);
        remind.setSourceType(UtilConstants.Source.APP);
        remind.setMessageUrl(baseMessageUrl + informMessageUrl);
        remind.setAppMsgType("1");
        messageFeignService.addMsg(Collections.singletonList(remind));
    }

    private void addMsg(String creatorName, String title, String content, String userId, String appMsgType, String informId, String msgUrl) {
        RemindInfoDto remind = new RemindInfoDto();
        remind.setRemindTitle(title);
        remind.setIsRead((byte) 0);
        remind.setRemindedType((byte) 2);
        remind.setIsDelete((byte) 0);
        remind.setCreater(creatorName);
        remind.setCreateTime(com.onecity.os.common.core.utils.DateUtils.dateTimeIntact());
        remind.setRemindContent(content);
        remind.setLevel("3");
        remind.setPcUserId(null);
        remind.setAppUserId(userId);
        remind.setServiceId(informId);
        remind.setServiceType("reportCheck");
        remind.setSourceType("APP");
        remind.setAppMsgType(appMsgType);
        remind.setMessageUrl(baseMessageUrl + msgUrl);
        log.info("发送app消息ids:----"+JSONObject.toJSONString(remind.getAppUserId()));
        BaseResult result = messageFeignService.addMsg(Collections.singletonList(remind));
        log.info("发送app消息result:----"+result.getCode());
        log.info("发送app消息result:----"+result.getMsg());
    }
}
