package com.onecity.os.inform.utils;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @Date 2020/6/10 8:37
 */
public class AsyncTaskExecutorUtil {
    private static final com.onecity.os.inform.utils.AsyncTaskExecutorUtil ASYNC_TASK_EXECUTOR_UTIL = new com.onecity.os.inform.utils.AsyncTaskExecutorUtil();
    private ExecutorService executorService;

    public static void execute(Runnable task){
        if(ASYNC_TASK_EXECUTOR_UTIL.executorService==null){
            synchronized (ASYNC_TASK_EXECUTOR_UTIL){
                if(ASYNC_TASK_EXECUTOR_UTIL.executorService==null){
                    ASYNC_TASK_EXECUTOR_UTIL.executorService = Executors.newCachedThreadPool();
                }
            }
        }
        ASYNC_TASK_EXECUTOR_UTIL.executorService.execute(task);
    }
}
