package com.onecity.os.inform.utils;


import com.onecity.os.inform.constant.Constant;
import com.onecity.os.inform.constant.IdentifyEnum;
import com.onecity.os.inform.constant.IndicatorExhibitTypeEnum;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicatorDataTianbao;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicatorTianbao;
import com.onecity.os.inform.zhibiao.entity.IndicatorCommonExcel;
import com.onecity.os.inform.zhibiao.model.dto.IndicatorDetailDto;
import com.onecity.os.inform.utils.CommonUtil;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.util.IOUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/06/08 10:44
 */
public class ExcelUtilSelf {

    public static IndicatorDetailDto readExcel(MultipartFile file, String username) throws IOException {
        //返回结果
        IndicatorDetailDto result = new IndicatorDetailDto();
        List<GeneralIndicatorTianbao> indicatorList = new ArrayList<>();
        List<GeneralIndicatorDataTianbao> indicatorDataList = new ArrayList<>();
//        IndicatorCommonExcel indicatorCommonExcel = new IndicatorCommonExcel();
//        indicatorCommonExcel.setSourceSimpleName(sourceManage.getSourceSimpleName());
//        indicatorCommonExcel.setSourceName(sourceManage.getSourceName());
        Date now = new Date();
//        indicatorCommonExcel.setNow(now);
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();

            POIFSFileSystem fs = new POIFSFileSystem(inputStream);
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(fs);
//            if (StringUtils.isBlank(username)) {
//                username = sourceManage.getCreater();
//            }
//            indicatorCommonExcel.setUsername(username);
//            indicatorCommonExcel.setSourceId(sourceManage.getSourceId());
            //获取所有的sheet
            int numberOfSheets = hssfWorkbook.getNumberOfSheets();
            if (numberOfSheets > 0) {
                for (int i = 0; i < numberOfSheets; i++) {
                    //单独解析每个sheet
                    GeneralIndicatorTianbao indicator = readSheet(hssfWorkbook.getSheetAt(i), indicatorList, result,i);
                    readSheetForIndicator(hssfWorkbook.getSheetAt(i), indicatorList, indicatorDataList, indicator, result);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            IOUtils.closeQuietly(inputStream);
        }
        return result;
    }


    private static GeneralIndicatorTianbao readSheet(Sheet sheet, List<GeneralIndicatorTianbao> indicatorList,  IndicatorDetailDto result, Integer sheetSequence) {
        String sheetName = sheet.getSheetName();
        String indicatorId = StringUtil.getUUIDStr();
        //指标id
//        String indicatorId = indicatorCommonExcel.getSourceSimpleName()+"_"+ PinyinUtil.getAlpha(sheetName);
        //ICON保存
        GeneralIndicatorTianbao indicator = new GeneralIndicatorTianbao();
        indicator.setId(indicatorId);
        indicator.setIndicatorName(sheetName);
        indicator.setParentId(Constant.PARENT_ID_ONE_LEVEL);
        String username = "";
        LoginUser sysUser = (LoginUser) SecurityUtils.getLoginUser();
        if (sysUser != null) {
            username = sysUser.getUsername();
        }
        indicator.setCreater(username);
        Date now = new Date();
        indicator.setCreateTime(now);
        indicator.setUpdater(username);
        indicator.setUpdateTime(now);
//        indicator.setSourceId(indicatorCommonExcel.getSourceId());
//        indicator.setSourceName(indicatorCommonExcel.getSourceName());
        indicator.setIndicatorExhibitType(IndicatorExhibitTypeEnum.WORD.getValue());
        indicator.setSequence(sheetSequence);
        indicator.setIsDelete(Constant.DELETE_NO);
        indicatorList.add(indicator);
        result.setIndicatorList(indicatorList);
        return indicator;
    }

    private static void readSheetForIndicator(Sheet sheet, List<GeneralIndicatorTianbao> indicatorList, List<GeneralIndicatorDataTianbao> indicatorDataList,  GeneralIndicatorTianbao firstIndicator, IndicatorDetailDto result) {

        int lastRowNum = sheet.getLastRowNum();
        //数据开始行：第三行
        int dataRowNum = 2;
        Integer fistSequence = 1;
        Integer secondSequence = 1;
        String parentId = firstIndicator.getId();
        String parentName = firstIndicator.getIndicatorName();
        GeneralIndicatorTianbao indicator = setFistLevelIndicator(sheet, indicatorList, parentId,parentName,dataRowNum,fistSequence, result);
        fistSequence++;
        GeneralIndicatorTianbao indicator2 = setSecondLevelIndicator(sheet, indicatorList, indicator.getId(),indicator.getIndicatorName(), dataRowNum, secondSequence, result);
        secondSequence++;
        Integer dataSequence = 1;
        String indicatorId = indicator2.getId();
        String indicatorName = indicator2.getIndicatorName();
        setIndicatorData(sheet,indicatorDataList,indicatorId,indicatorName,dataSequence,dataRowNum,result);
        dataSequence++;
        parentId = indicator.getId();
        parentName = indicator.getIndicatorName();
        String secondIndicatorParentId = indicator.getId();
        String secondIndicatorParentName = indicator.getIndicatorName();
        for (int i = dataRowNum + 1; i <= lastRowNum; i++) {
            //获取一级指标
            String firstIndicatorName = sheet.getRow(i).getCell(0).getStringCellValue().trim();
            String lastFirstIndicatorName = sheet.getRow(i - 1).getCell(0).getStringCellValue().trim();

            if (!firstIndicatorName.equals(lastFirstIndicatorName)) {
                secondSequence=1;
                GeneralIndicatorTianbao indicator1 = setFistLevelIndicator(sheet, indicatorList,  parentId,parentName, i, fistSequence, result);
                fistSequence++;
                secondIndicatorParentId=indicator1.getId();
                secondIndicatorParentName = indicator1.getIndicatorName();
            }
            //二级指标
            String secondIndicatorName = sheet.getRow(i).getCell(1).getStringCellValue().trim();
            String lastSecondIndicatorName = sheet.getRow(i - 1).getCell(1).getStringCellValue().trim();
            if (!secondIndicatorName.equals(lastSecondIndicatorName)) {
                GeneralIndicatorTianbao secondIndicator = setSecondLevelIndicator(sheet, indicatorList, secondIndicatorParentId,secondIndicatorParentName, i, secondSequence, result);
                secondSequence++;
                indicatorId = secondIndicator.getId();
                indicatorName = secondIndicator.getIndicatorName();
                dataSequence=1;
            }
            setIndicatorData(sheet,indicatorDataList,indicatorId,indicatorName,dataSequence,i,result);
            dataSequence++;

        }
        //返回结果封装
        result.setIndicatorList(indicatorList);
        result.setIndicatorDataList(indicatorDataList);
    }

    /**
     * 设置一级指标
     *
     * @param sheet
     * @param indicatorList
     * @param result
     */
    private static GeneralIndicatorTianbao setFistLevelIndicator(Sheet sheet, List<GeneralIndicatorTianbao> indicatorList, String parentId, String parentName, int dataRow, Integer sequence, IndicatorDetailDto result) {
        GeneralIndicatorTianbao indicator = new GeneralIndicatorTianbao();
        String indicatorName = sheet.getRow(dataRow).getCell(0).getStringCellValue().trim();
        setIndicator(sheet, indicatorList,  parentId, parentName,dataRow, sequence, result, indicator, indicatorName);
        return indicator;
    }

    /**
     * 设置二级指标
     *
     * @param sheet
     * @param indicatorList
     * @param result
     */
    private static GeneralIndicatorTianbao setSecondLevelIndicator(Sheet sheet, List<GeneralIndicatorTianbao> indicatorList,  String parentId, String parentName, int dataRow, Integer sequence, IndicatorDetailDto result) {
        GeneralIndicatorTianbao indicator = new GeneralIndicatorTianbao();
        String indicatorName = sheet.getRow(dataRow).getCell(1).getStringCellValue().trim();
        setIndicator(sheet, indicatorList,parentId, parentName,dataRow, sequence, result, indicator, indicatorName);
        return indicator;
    }

    private static void setIndicator(Sheet sheet, List<GeneralIndicatorTianbao> indicatorList,  String parentId, String parentName, int dataRow, Integer sequence, IndicatorDetailDto result, GeneralIndicatorTianbao indicator, String indicatorName) {
        String indicatorExhibitTypeStr = sheet.getRow(dataRow).getCell(5).getStringCellValue();
        //展示方式
        String indicatorExhibitType = IndicatorExhibitTypeEnum.getByMessage(indicatorExhibitTypeStr).getValue();
        indicator.setIndicatorExhibitType(indicatorExhibitType);
        String indicatorId = StringUtil.getUUIDStr();
        //指标id
//        String indicatorId = indicatorCommonExcel.getSourceSimpleName()+"_"+ PinyinUtil.getAlpha(indicatorName);
        indicator.setId(indicatorId);
        indicator.setIndicatorName(indicatorName);
        indicator.setParentId(parentId);
        indicator.setParentName(parentName);
        String username = "";
        LoginUser sysUser = (LoginUser) SecurityUtils.getLoginUser();
        if (sysUser != null) {
            username = sysUser.getUsername();
        }
        indicator.setCreater(username);
        Date now = new Date();
        indicator.setCreateTime(now);
        indicator.setUpdater(username);
        indicator.setUpdateTime(now);
        String updateCycle = sheet.getRow(dataRow).getCell(6).getStringCellValue();
        indicator.setUpdateCycle(updateCycle);
        String updateDate = sheet.getRow(dataRow).getCell(7).getStringCellValue();
        indicator.setUpdateDate(updateDate);
        String leader = sheet.getRow(dataRow).getCell(8).getStringCellValue();
        indicator.setLeader(leader);
//        String sourceName = sheet.getRow(dataRow).getCell(9).getStringCellValue();
//        indicator.setSourceName(indicatorCommonExcel.getSourceName());
//        indicator.setSourceId(indicatorCommonExcel.getSourceId());
        indicator.setSequence(sequence);
        indicator.setIndicatorType(Constant.CORE_DATA);
//        indicator.setIndicatorType(Constant.GENERAL_INDICATOR);
        indicator.setIsDelete(Constant.DELETE_NO);
        indicatorList.add(indicator);
    }




    private static void setIndicatorData(Sheet sheet, List<GeneralIndicatorDataTianbao> indicatorDataList, String indicatorId, String indicatorName, Integer sequence, int dataRowNum, IndicatorDetailDto result) {
        GeneralIndicatorDataTianbao indicatorData = new GeneralIndicatorDataTianbao();
        String itemName = sheet.getRow(dataRowNum).getCell(2).getStringCellValue();
        Cell cell = sheet.getRow(dataRowNum).getCell(3);
        int cellType = cell.getCellType().getCode();
        String itemValue = "";
        switch (cellType) {
            case 0:
                itemValue = String.valueOf(cell.getNumericCellValue());
                break;
            case 1:
                itemValue = cell.getStringCellValue();
                break;
                default:
                    itemValue = String.valueOf(cell.getErrorCellValue());
        }
        String identify = "";
        if (StringUtils.isBlank(itemValue)) {
            identify = IdentifyEnum.TITLE.getValue();
        }
        String itemUnit = sheet.getRow(dataRowNum).getCell(4).getStringCellValue();
        Integer isFold = Constant.FOLD_NO;
        if (StringUtils.isBlank(identify)) {
            identify = IdentifyEnum.TEXT.getValue();
            //TODO 暂定超过18个字后折行
            int length = itemName.length() + itemValue.length();
            if (length > 20) {
                isFold = Constant.FOLD_YES;
            }
        }
        boolean numeric = CommonUtil.isNumeric(itemValue);
        if (numeric) {
            Float aFloat = Float.valueOf(itemValue);
            if (aFloat >= 0) {
                identify = IdentifyEnum.POSITIVE.getValue();
            } else {
                identify = IdentifyEnum.NEGATIVE.getValue();
            }
        }
        indicatorData.setIndicatorId(indicatorId);
        indicatorData.setIndicatorName(indicatorName);
        indicatorData.setItemName(itemName);
        indicatorData.setItemValue(itemValue);
        indicatorData.setItemUnit(itemUnit);
        indicatorData.setIsFold(isFold);
        indicatorData.setSequence(sequence);
        String updateDate = sheet.getRow(dataRowNum).getCell(7).getStringCellValue();
//        indicatorData.setCreater(indicatorCommonExcel.getUsername());
//        indicatorData.setUpdater(indicatorCommonExcel.getUsername());
//        indicatorData.setCreateTime(indicatorCommonExcel.getNow());
//        indicatorData.setUpdateTime(indicatorCommonExcel.getNow());
        String username = "";
        LoginUser sysUser = (LoginUser) SecurityUtils.getLoginUser();
        if (sysUser != null) {
            username = sysUser.getUsername();
        }
        indicatorData.setCreater(username);
        Date now = new Date();
        indicatorData.setCreateTime(now);
        indicatorData.setUpdater(username);
        indicatorData.setUpdateTime(now);
        String year = DateUtilsSelf.dateformat(updateDate, "yyyy");
        indicatorData.setUpdateDate(year);
        indicatorData.setIdentify(identify);
        indicatorData.setCurrentFlag(0);
        indicatorData.setStyle(0);
        indicatorData.setIsDelete(Constant.DELETE_NO);
        indicatorDataList.add(indicatorData);
    }


}
