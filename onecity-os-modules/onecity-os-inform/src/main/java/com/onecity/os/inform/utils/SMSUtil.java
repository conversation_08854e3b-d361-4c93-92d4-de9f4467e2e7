package com.onecity.os.inform.utils;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;


@Slf4j
public class SMSUtil {
    public static String baseUrl = "https://ioc-front-dev-32080.p.onecode.cmict.cloud/wgh/dev/api/sms/message/sendCustom";

    public static void sendSms(String phone){
        //发送短信先去掉，需要发送短信时放开即可
//        JSONObject params = new JSONObject();
//        params.put("templateId","SMS_474435101");
//        params.put("phone",phone);
//        String result = HttpRequest.post(baseUrl).body(JSON.toJSONString(params)).header("Content-Type", "application/json").timeout(20000).execute().body();
//        log.info("调用一网统管短信接口返回结果：" + JSON.parseObject(result));
    }

}
