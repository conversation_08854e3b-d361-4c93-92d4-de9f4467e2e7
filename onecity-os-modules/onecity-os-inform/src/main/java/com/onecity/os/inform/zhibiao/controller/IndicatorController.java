package com.onecity.os.inform.zhibiao.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.constant.HttpStatus;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.poi.ExcelUtil;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.inform.constant.UtilConstants;
import com.onecity.os.inform.zhibiao.service.IndicatorTitleService;
import com.onecity.os.system.api.model.LoginUser;
import com.onecity.os.inform.zhibiao.entity.*;
import com.onecity.os.inform.zhibiao.service.DataConfigService;
import com.onecity.os.inform.utils.ExcelUtilSelf;
import com.onecity.os.inform.zhibiao.model.dto.*;
import com.onecity.os.inform.zhibiao.model.vo.*;
import com.onecity.os.inform.zhibiao.service.IndicatorService;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 指标管理和指标数据控制层
 */
@Api(tags = "指标")
@Slf4j
@RestController
@RequestMapping("/indicator")
@Transactional
public class IndicatorController extends BaseController {

    Pattern pattern = Pattern.compile("^(\\-|\\+)?\\d+(\\.\\d+)?$");

    @Autowired
    private IndicatorService indicatorService;

    @Resource
    private DataConfigService dataConfigService;

    @Resource
    private IndicatorTitleService indicatorTitleService;
    /**
     * 全部文件(普通文件,图片,)后缀 支持的类型
     */
    private static final String[] FILE_SUFFIX_SUPPORT = {".xlsx", ".xls"};
    /**
     * 1 指标管理-指标树
     *
     * @RequestParam(name = "tabid") String tabId,
     */
    @ApiOperation(value = "指标管理-指标树")
    @GetMapping("/getTargetTreeList")
    public BaseResult<?> getTargetTreeList(@RequestParam(name = "tj", required = true) String tj) {
        String tabId = "0";
        List<IndicatorTreeDto> TreeVoList = new ArrayList<>();
        // 获取当前登录的用户信息
        LoginUser sysUser = SecurityUtils.getLoginUser();;
        String userId = null;
        if (ObjectUtil.isNotNull(sysUser)) {
            userId = sysUser.getUserid().toString();
        }

        TreeVoList = indicatorService.getTargetTreeList(tabId,tj);


        if (CollectionUtils.isEmpty(TreeVoList)) {
            return BaseResult.fail("数据不存在！");
        }
        return BaseResult.ok(TreeVoList);
    }

    /**
     * 2 指标管理-指标列表查询
     *
     * @param tabId
     * @return
     */
    @ApiOperation(value = "指标管理-根据tabid,tj,查询指标列表")
    @GetMapping("/getTargetList")
    public TableDataInfo getTargetList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                       @RequestParam(name = "tabid", required = true) String tabId,
                                       @RequestParam(name = "tj", required = true) String tj) {
        startPage();
        List<IndicatorDto> pageInfo = indicatorService.getTargetList(tabId, tj);
        if (ObjectUtil.isNotNull(pageInfo) && 0 != pageInfo.size()) {
            return getDataTable(pageInfo);
        } else {
            TableDataInfo rspData = new TableDataInfo();
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setRows(new ArrayList<>());
            rspData.setMsg("不存在此tab");
            rspData.setTotal(0);
            return rspData;
        }
    }

    /**
     * 3 指标管理-指标删除
     *
     * @return
     */
    @ApiOperation(value = "阅批呈报-指标删除")
    @Log( title = "阅批呈报-指标删除",businessType = BusinessType.DELETE)
    @PostMapping(value = "/deleteTargets")
    public BaseResult<?> deleteTarget(@RequestParam(name = "ids", required = true) String ids, @RequestParam(name = "tj", required = true) String tj) {
        if (StringUtils.isBlank(ids)) {
            return BaseResult.fail("id为null");
        }
        try {
            // 获取当前登录的用户信息
            LoginUser sysUser = (LoginUser) SecurityUtils.getLoginUser();
            String userName = null;
            if (ObjectUtil.isNotNull(sysUser)) {
                userName = sysUser.getUsername();
            }
            indicatorService.deleteTargets(ids, userName, tj);
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResult.fail("删除失败！");
        }
        return BaseResult.ok("删除成功！");
    }

    /**
     * 4 指标管理-新增指标
     *
     * @param
     * @return
     */
    @Log(title = "阅批呈报-新增分组",businessType = BusinessType.INSERT)
    @PostMapping("/addTarget")
    @ApiOperation(value = "阅批呈报-新增指标", notes = "阅批呈报-新增指标")
    public BaseResult<?> addTarget(@RequestBody List<IndicatorVo> vos, @RequestParam(name = "tj", required = true) String tj) {
        if (CollectionUtils.isEmpty(vos)) {
            return BaseResult.fail("指标分组或指标不能为空");
        }
        if (ObjectUtils.isEmpty(vos.get(0).getIndicatorType())) {
            return BaseResult.fail("指标类型，0：指标，1：tab类型(指标分组),不能为空");
        }
        // 指标分组,进行字段校验
        if (1 == vos.get(0).getIndicatorType()) {
            // 当选择一级分组时,图标不能为空
//            if (StringUtils.isBlank(vos.get(0).getParentId()) && StringUtils.isBlank(vos.get(0).getIconUrl())) {
//                return Result.error("一级分组时,图标不能为空");
//            }
            if (StringUtils.isBlank(vos.get(0).getIndicatorName())) {
                return BaseResult.fail("分组名称不能为空");
            }
            if (StringUtils.isBlank(vos.get(0).getSequence().toString())) {
                return BaseResult.fail("排序不能为空");
            }
            if (ObjectUtils.isEmpty(vos.get(0).getGroupType())) {
                return BaseResult.fail("展示内容不能为空");
            }
            // 展示内容为网页时,url不能为空
            if (1 == vos.get(0).getGroupType()) {
                if (StringUtils.isBlank(vos.get(0).getGroupUrl())) {
                    return BaseResult.fail("展示内容为网页时,url不能为空");
                }
            }
        } else {
            for (IndicatorVo vo1 : vos) {
                // 编辑指标时,展示样式和更新周期不能为空
//                if (StringUtils.isBlank(vo1.getIndicatorExhibitType()) || StringUtils.isBlank(vo1.getUpdateCycle())) {
//                    return Result.error("'展示样式'或'更新周期'不能为空");
//                }
                if(vo1.getDataUpdateMode()==1&&(StringUtils.isBlank(vo1.getIndicatorExhibitType()) || StringUtils.isBlank(vo1.getUpdateCycle()))){
                    return BaseResult.fail("'展示样式'或'更新周期'不能为空");
                }
                if (vo1.getDataUpdateMode()==2&&StringUtils.isBlank(vo1.getIndicatorExhibitType())) {
                    return BaseResult.fail("'展示样式'不能为空");
                }
                if (StringUtils.isBlank(vo1.getIndicatorName())) {
                    return BaseResult.fail("指标名称不能为空");
                }
                if (StringUtils.isBlank(vo1.getSequence().toString())) {
                    return BaseResult.fail("指标序号不能为空");
                }
            }
        }
        // 判断父指标是否存在
//        String tableName = tj + "_general_indicator";
        GeneralIndicatorTianbao parentIndicator = new GeneralIndicatorTianbao();
        if (StringUtils.isNotBlank(vos.get(0).getParentId())) {
            // 所选父级不是第一级是,判断父级是否存在
            if (!"0".equals(vos.get(0).getParentId())) {
                parentIndicator = indicatorService.getIndicatorById(vos.get(0).getParentId(), tj);
                if (ObjectUtils.isEmpty(parentIndicator)) {
                    return BaseResult.fail("所选父级不存在");
                } else {
                    // 指标,不能添加分组和指标
                    if (0 == parentIndicator.getIndicatorType()) {
                        return BaseResult.fail("指标数据下不能添加分组");
                    }
                }
            }
        } else {
            // 父指标为空,则为一级指标分组,将父指标id设置为0
            parentIndicator.setParentId("0");
            parentIndicator.setId("0");
        }
        BaseResult<?> res = new BaseResult<>();
        try {
            res = indicatorService.addIndicator(tj, vos, parentIndicator);
        } catch (Exception e) {
            return BaseResult.fail("添加失败！");
        }
        return res;
    }

    /**
     * 5 指标管理-更新指标
     *
     * @param
     * @return
     */
    @ApiOperation(value = "阅批呈报-更新指标")
    @Log(title = "阅批呈报-更新指标",businessType = BusinessType.UPDATE)
    @PostMapping("/updateTargets")
    public BaseResult<?> updateTargets(@RequestBody List<IndicatorVo> vos,
                                   @RequestParam(name = "tj", required = true) String tj,
                                   @RequestParam(name = "pageNo", required = false) Integer pageNo,
                                   @RequestParam(name = "pageSize", required = false) Integer pageSize,
                                       @RequestParam(name = "parentId", required = false) String parentId) {
        if (CollectionUtils.isEmpty(vos)) {
            GeneralIndicatorTianbao parentIndicator = indicatorService.getIndicatorById(parentId, tj);
            if (ObjectUtils.isEmpty(parentIndicator)) {
                return BaseResult.fail("所选父级不存在");
            }
            indicatorService.deleteTargetsByParentId(tj,parentId);
            return BaseResult.ok("更新成功！");
        }
        if (ObjectUtils.isEmpty(vos.get(0).getIndicatorType())) {
            return BaseResult.fail("指标类型，0：指标，1：tab类型(指标分组),不能为空");
        }
        // 指标分组,进行字段校验
        if (1 == vos.size() && 1 == vos.get(0).getIndicatorType()) {
            if (StringUtils.isBlank(vos.get(0).getId())) {
                return BaseResult.fail("指标分组id不能为空");
            }
            // 当选择一级分组时,图标不能为空
            if (StringUtils.isBlank(vos.get(0).getParentId()) && StringUtils.isBlank(vos.get(0).getIconUrl())) {
                return BaseResult.fail("一级分组时,图标不能为空");
            }
            if (StringUtils.isBlank(vos.get(0).getIndicatorName())) {
                return BaseResult.fail("分组名称不能为空");
            }
            if (StringUtils.isBlank(vos.get(0).getSequence().toString())) {
                return BaseResult.fail("排序不能为空");
            }
            if (ObjectUtils.isEmpty(vos.get(0).getGroupType())) {
                return BaseResult.fail("展示内容不能为空");
            }
            // 展示内容为网页时,url不能为空
            if (1 == vos.get(0).getGroupType()) {
                if (StringUtils.isBlank(vos.get(0).getGroupUrl())) {
                    return BaseResult.fail("展示内容为网页时,url不能为空");
                }
            }
        } else {
            for (IndicatorVo vo1 : vos) {
                // 编辑指标时,展示样式和更新周期不能为空
//                if (StringUtils.isBlank(vo1.getIndicatorExhibitType()) || StringUtils.isBlank(vo1.getUpdateCycle())) {
//                    return Result.error("'展示样式'或'更新周期'不能为空");
//                }
                if(vo1.getDataUpdateMode()==1&&(StringUtils.isBlank(vo1.getIndicatorExhibitType()) || StringUtils.isBlank(vo1.getUpdateCycle()))){
                    return BaseResult.fail("'展示样式'或'更新周期'不能为空");
                }
                if (vo1.getDataUpdateMode()==2&&StringUtils.isBlank(vo1.getIndicatorExhibitType())) {
                    return BaseResult.fail("'展示样式'不能为空");
                }
                if (StringUtils.isBlank(vo1.getIndicatorName())) {
                    return BaseResult.fail("指标名称不能为空");
                }
                if (StringUtils.isBlank(vo1.getSequence().toString())) {
                    return BaseResult.fail("指标序号不能为空");
                }
                if (StringUtils.isBlank(vo1.getParentId())) {
                    return BaseResult.fail("父指标id不能为空");
                }
            }
        }
        // 判断父指标是否存在
//        String tableName = tj + "_general_indicator";
        GeneralIndicatorTianbao parentIndicator = new GeneralIndicatorTianbao();
        // 父指标不为空
        if (StringUtils.isNotBlank(vos.get(0).getParentId()) && !vos.get(0).getParentId().equals("0")) {
            parentIndicator = indicatorService.getIndicatorById(vos.get(0).getParentId(), tj);
            if (ObjectUtils.isEmpty(parentIndicator)) {
                return BaseResult.fail("所选父级不存在");
            }
            // 指标,不能添加分组和指标
            if (0 == parentIndicator.getIndicatorType() && 1 == vos.get(0).getIndicatorType()) {
                return BaseResult.fail("指标数据下不能添加分组");
            }
        } else {
            // 父指标为空,则为一级指标分组,将父指标id设置为0
            parentIndicator.setParentId("0");
            parentIndicator.setId("0");
        }
        try {
            int result = indicatorService.editIndicator(tj, vos, parentIndicator, pageNo, pageSize);
            if (1 == result) {
                return BaseResult.fail("该指标分组不存在");
            }
        } catch (Exception e) {
            return BaseResult.fail("更新失败！");
        }
        return BaseResult.ok("更新成功！");
    }

    /**
     * 指标数据管理-新增数据配置
     *
     * @param dcList
     */
    @ApiOperation(value = "指标数据管理-批量新增或修改数据配置")
    @PostMapping("/addOrUpdateDataConfigList")
    @Log(title = "阅批呈报-数据配置",businessType = BusinessType.OTHER)
    public com.onecity.os.common.core.domain.BaseResult<?> addOrUpdateDataConfigList(@RequestBody List<DataConfig> dcList) {
        Set<String> dataValues = new HashSet<>();
        for (DataConfig dataConfig : dcList) {
            if (!ObjectUtils.isEmpty(dataConfig)) {
                if (ObjectUtils.isEmpty(dataConfig.getIndicatorId())) {
                    return com.onecity.os.common.core.domain.BaseResult.fail("指标id不能为空！");
                }
                dataValues.add(dataConfig.getDataValue());
            } else {
                return com.onecity.os.common.core.domain.BaseResult.fail("指标id不能为空！");
            }
        }
        if (dcList.size() != dataValues.size()) {
            return com.onecity.os.common.core.domain.BaseResult.fail("数据配置中数据值选择有重复，请重新选择！");
        }

        return com.onecity.os.common.core.domain.BaseResult.ok(dataConfigService.addOrUpdateDataConfigList(dcList));
    }

    /**
     * 指标数据管理-通过id获取数据配置详细信息
     * @param id
     */
    @ApiOperation(value ="指标数据管理-通过id获取数据配置详细信息")
    @GetMapping("/getDataConfigById")
    public BaseResult<?> getDataConfigById(@RequestParam String id){
        return BaseResult.ok(dataConfigService.getDataConfigById(id));
    }

    /**
     * 指标数据管理-通过id删除数据配置详细信息
     * @param id
     */
    @ApiOperation(value ="指标数据管理-通过id删除数据配置详细信息")
    @GetMapping("/deleteDataConfigById")
    public BaseResult<?> deleteDataConfigById(@RequestParam String id){
        dataConfigService.deleteDataConfigById(id);
        return BaseResult.ok("删除成功！");
    }

    /**
     * 指标数据管理-通过id获取数据配置详细信息
     *
     * @param id
     */
    @ApiOperation(value = "指标数据管理-通过指标id获取数据配置详细信息")
    @GetMapping("/getDataConfigByIndicatorId")
    public BaseResult<?> getDataConfigByIndicatorId(@RequestParam String id) {
        return BaseResult.ok(dataConfigService.getDataConfigByIndicatorId(id));
    }

    /**
     * 1 指标数据管理-查询指标数据列表
     *
     * @param indicatorId
     * @param tj
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "指标数据管理-查询指标数据列表")
    @GetMapping("/getIndicatorDetailList")
    public BaseResult<?> getTargetDetailList(@RequestParam(name = "indicatorId", required = true) String indicatorId,
                                             @RequestParam(name = "tj", required = true) String tj,
                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        if (org.springframework.util.StringUtils.isEmpty(indicatorId)) {
            return BaseResult.fail("查询id不能为空");
        }
        IndicatorDataDto dto = new IndicatorDataDto();
        // 查询指标信息
        GeneralIndicatorTianbao generalIndicator = indicatorService.getIndicatorById(indicatorId, tj);
        if (ObjectUtils.isEmpty(generalIndicator)) {
            return BaseResult.fail("分组下无数据！");
        }
        if (1 == generalIndicator.getIndicatorType()) {
            return BaseResult.fail("分组下无数据！");
        }
        dto.setTreeId(generalIndicator.getId());
        dto.setSource(generalIndicator.getSourceName());
        dto.setUpdateDate(generalIndicator.getUpdateDate());
        dto.setType(generalIndicator.getIndicatorExhibitType());
        dto.setUpdateCycle(generalIndicator.getUpdateCycle());
        dto.setIsLegend(generalIndicator.getIsLegend());
        // 查询指标头信息
        List<GeneralIndicatorDataTitle> titles = indicatorTitleService.queryById(generalIndicator.getId());
        dto.setTableHeaderList(titles);
        // 可用字段
        if (!CollectionUtils.isEmpty(titles)) {
            dto.setCheckColumn(new ArrayList<>(titles.get(0).getCheckColumn()));
        } else {
            //默认可用标头，逐条大小写转换
            dto.setCheckColumn(UtilConstants.getAllColumn().stream().map(UtilConstants::getItemMap).collect(Collectors.toList()));
        }
        startPage();
        PageInfo<IndicatorDataReBean> pageInfo = indicatorService.getTargetDetailList(indicatorId, tj, pageNo, pageSize);
        if (ObjectUtil.isNotNull(pageInfo)) {
            dto.setTotal(pageInfo.getTotal());
            if (0 != pageInfo.getSize()) {
                dto.setData(pageInfo.getList());
                dto.setSize(pageInfo.getSize());
                dto.setCurrent(pageInfo.getPageNum());
                dto.setPages(pageInfo.getPages());
            }
            return BaseResult.ok(dto);
        }
        if (null == pageInfo) {
            return BaseResult.ok(null, "分组下无数据！");
        }
        return BaseResult.ok(dto);
    }

    /**
     * 2 指标数据管理-指标数据批量修改和新增
     *
     * @param dataVo
     * @return
     */
    @ApiOperation(value = "阅批呈报-指标数据批量修改和新增")
    @Log(title = "阅批呈报-指标数据批量修改和新增", businessType = BusinessType.INSERT)
    @PostMapping(value = "/updateIndicatorDetail")
    public BaseResult<?> updateIndicatorDetail(@RequestBody IndicatorDataDto dataVo,
                                           @RequestParam(name = "tj", required = true) String tj,
                                           @RequestParam(name = "pageNo", required = false) Integer pageNo,
                                           @RequestParam(name = "pageSize", required = false) Integer pageSize) {
        if (StringUtils.isBlank(dataVo.getTreeId())) {
            return BaseResult.fail("指标id不能为空！");
        }
        GeneralIndicatorTianbao generalIndicatorTianbao = indicatorService.getIndicatorById(dataVo.getTreeId(), tj);
        String showType = generalIndicatorTianbao.getIndicatorExhibitType();
        //判断指标数据类型 identify，当类型为非文本的时候（不为 "table"，不为"word"），值必须为数字
        if (!CollectionUtils.isEmpty(dataVo.getData())) {
            for (IndicatorDataReBean data : dataVo.getData()) {
                if (!showType.equals(UtilConstants.IndType.TABLE) && !showType.equals(UtilConstants.IndType.WORD)
                        && !showType.equals(UtilConstants.IndType.TWOCOLUMNGRID)
                        && !showType.equals(UtilConstants.IndType.RECTWORD)
                        && !showType.equals(UtilConstants.IndType.MULTFRID)) {
                    boolean flag = true;
                    //数值校验 判断DATA 下所有存在（不为空）的字段值 所有指标值（0-6）全部校验
                    if (StringUtils.isNotEmpty(data.getItemValue()) && !pattern.matcher(data.getItemValue()).matches()) {
                        flag = false;
                    }
                    if (StringUtils.isNotEmpty(data.getItemValue1()) && !pattern.matcher(data.getItemValue1()).matches()) {
                        flag = false;
                    }
                    if (StringUtils.isNotEmpty(data.getItemValue2()) && !pattern.matcher(data.getItemValue2()).matches()) {
                        flag = false;
                    }
                    if (StringUtils.isNotEmpty(data.getItemValue3()) && !pattern.matcher(data.getItemValue3()).matches()) {
                        flag = false;
                    }
                    if (StringUtils.isNotEmpty(data.getItemValue4()) && !pattern.matcher(data.getItemValue4()).matches()) {
                        flag = false;
                    }
                    if (StringUtils.isNotEmpty(data.getItemValue5()) && !pattern.matcher(data.getItemValue5()).matches()) {
                        flag = false;
                    }
                    if (StringUtils.isNotEmpty(data.getItemValue6()) && !pattern.matcher(data.getItemValue6()).matches()) {
                        flag = false;
                    }
                    if (!flag) {
                        return BaseResult.fail("数值项中存在非数值类型，请修正数据格式");
                    }
                }
            }
        }
        try {
            // 获取当前登录的用户信息
            LoginUser sysUser = SecurityUtils.getLoginUser();
            String userName = null;
            if (ObjectUtil.isNotNull(sysUser)) {
                userName = sysUser.getUsername();
            }
            // 接收前端传来的指标数据ids
            List<Long> receivedDataIds = new ArrayList<>();
            // 新增和修改时,加入创建人/创建时间/修改人/修改时间
            if (CollUtil.isNotEmpty(dataVo.getData())) {
                for (int i = 0; i < dataVo.getData().size(); i++) {
                    receivedDataIds.add(dataVo.getData().get(i).getId());
                }
            }
            indicatorService.saveOrUpdateIndicatorData(dataVo, tj, receivedDataIds, userName, pageNo, pageSize);
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResult.fail("修改失败！");
        }
        return BaseResult.ok("修改成功！");
    }

    /**
     * 3 指标数据管理-指标数据删除
     *
     * @return
     */
    @ApiOperation(value = "阅批呈报-指标数据删除")
    @Log(title = "阅批呈报-指标数据删除",businessType = BusinessType.DELETE)
    @PostMapping(value = "/deleteIndicatorDetails")
    public BaseResult<?> deleteIndicatorDetails(@RequestParam(name = "ids", required = true) String ids, @RequestParam(name = "tj", required = true) String tj) {
        if (StringUtils.isBlank(ids)) {
            return BaseResult.fail("id不能为空");
        }
        try {
            // 获取当前登录的用户信息
            LoginUser sysUser = (LoginUser) SecurityUtils.getLoginUser();
            String userName = null;
            if (ObjectUtil.isNotNull(sysUser)) {
                userName = sysUser.getUsername();
            }
            indicatorService.deleteIndicatorDataByIds(ids, userName, tj);
        } catch (Exception e) {
            return BaseResult.fail("删除失败！");
        }
        return BaseResult.ok("删除成功！");
    }

//    @Log(title = "指标数据管理-导入",businessType = BusinessType.IMPORT)
    @PostMapping("/importExcel")
    public BaseResult<?> importExcel(@RequestBody MultipartFile file, String sourceId) throws Exception {
        //获取文件大小
        double fileSize = (double) file.getSize() / 1048576;
        // 校验文件名字
        String originalFilename = file.getOriginalFilename();
        String suffix = originalFilename.substring(originalFilename.lastIndexOf('.'));
        String fileName = originalFilename.substring(0,originalFilename.lastIndexOf('.'));
        log.info("上传文件大小为=" + fileSize + "M");
        //校验文件是否被修改后缀
        String fileSuffixName = suffix.toLowerCase(Locale.ROOT);
        log.info("文件后缀为："+ JSONObject.toJSONString(fileSuffixName));


        if(fileSize > 20 ){
            return BaseResult.fail("上传文件最大为20M!");
        }
        if(!originalFilename.contains(".")){
            return BaseResult.fail("文件不能没有后缀!");
        }
        if(!Arrays.asList(FILE_SUFFIX_SUPPORT).contains(suffix.toLowerCase(Locale.ROOT))){
            return BaseResult.fail("文件格式不支持,请更换后重试!");
        }
        if(fileName.length() > 200){
            return BaseResult.fail("文件名长度不允许超过200，请更换后重试！");
        }
        long start = System.currentTimeMillis();
        if (StringUtils.isBlank(sourceId)) {
            return BaseResult.fail("厅局id不能为空");
        }
        String username = "";
        LoginUser sysUser = (LoginUser) SecurityUtils.getLoginUser();
        if (sysUser != null) {
            username = sysUser.getUsername();
        }
//        MultipartFile file = reqVO.getFile();
        IndicatorDetailDto indicatorDetailDto = ExcelUtilSelf.readExcel(file, username);
        List<GeneralIndicatorTianbao> indicatorList = indicatorDetailDto.getIndicatorList();
        List<GeneralIndicatorDataTianbao> indicatorDataList = indicatorDetailDto.getIndicatorDataList();
        String sourceSimpleName = "ypcb";
        indicatorService.insertIndicatorList(indicatorList, sourceSimpleName);
        indicatorService.insertIndicatorDataList(indicatorDataList, sourceSimpleName);
        long end = System.currentTimeMillis();
        log.error("批量导入共耗时：" + (end - start) + "毫秒");//用时618毫秒
        return BaseResult.ok("导入成功");
    }

    /**
     * 多线程导入
     *
     * @param file
     * @param sourceId
     * @return
     * @throws Exception
     */
//    @Log(title = "指标数据管理-多线程导入",businessType = BusinessType.IMPORT)
//    @PostMapping("/importExcelMulti")
//    public BaseResult<?> importExcelMulti(@RequestBody MultipartFile file, String sourceId) throws Exception {
//        long start = System.currentTimeMillis();
//        if (StringUtils.isBlank(sourceId)) {
//            return BaseResult.fail("厅局id不能为空");
//        }
//        String username = "";
//        LoginUser sysUser = SecurityUtils.getLoginUser();
//        if (sysUser != null) {
//            username = sysUser.getUsername();
//        }
//        IndicatorDetailDto indicatorDetailDto = ExcelUtilSelf.readExcel(file, username);
//        List<GeneralIndicatorTianbao> indicatorList = indicatorDetailDto.getIndicatorList();
//        List<GeneralIndicatorDataTianbao> indicatorDataList = indicatorDetailDto.getIndicatorDataList();
//        String sourceSimpleName = "ypcb";
//        indicatorService.insertIndicatorListMulti(indicatorList, sourceSimpleName);
//        indicatorService.insertIndicatorDataListMulti(indicatorDataList, sourceSimpleName);
//        long end = System.currentTimeMillis();
//        log.error("批量导入共耗时：" + (end - start) + "毫秒");//用时691毫秒
//        return BaseResult.ok("导入成功");
//    }


    /**
     * 指标管理-移动指标分组到其他指标下面
     *
     * @param vo
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "指标管理-移动指标分组到其他指标下面")
//    @AutoLog(value = "指标管理-移动指标分组到其他指标下面")
    @PostMapping("/moveIndicator")
    public BaseResult<?> moveIndicator(@RequestBody @Validated MoveIndicatorVo vo) throws Exception {
        // 先根据指标id,判断该指标是否是二级
//        Integer res = indicatorService.checkIsSecondIndicatorById(vo);
//        if (1 == res) {
//            return Result.error("厅局管理中没有该厅局/区县模块,请先在配置管理中添加");
//        }
//        if (2 == res) {
//            return Result.error("所选指标等级不符合要求,请重新选择第三级指标");
//        }
        indicatorService.moveIndicator(vo);
        return BaseResult.ok();
    }

    /**
     * 根据指标id,导出指标数据管理数据(在使用)
     *
     * @param request
     */
//    @Log(title = "指标数据管理-导出excel",businessType = BusinessType.EXPORT)
    @RequestMapping(value = "/exportIndicatorDataXlsByIndicatorId")
    public void exportIndicatorDataXlsByIndicatorId(HttpServletRequest request,HttpServletResponse response) throws Exception {
        // 导出文件名称
//        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        // 根据指标id,查找指标名称和类型
        IndicatorDataExcel indicator = indicatorService.getIndicatorNameSourceNameById(request.getParameter("indicatorId"));
        // 指标为空
        if (ObjectUtils.isEmpty(indicator)) {
            throw new Exception("该指标不存在");
        }
        List<?> dtoList = indicatorService.getExportIndicatorDataXlsByIndicatorId(
                request.getParameter("indicatorId"), indicator);
        List<IndicatorDataChartExcel> dtoList1 = new ArrayList<>();
        List<IndicatorDataExcel> dtoList2 = new ArrayList<>();
        if(dtoList.size()==0){
            if ("word".equals(indicator.getType())) {
                IndicatorDataExcel indicatorDataExcel = new IndicatorDataExcel();
                indicatorDataExcel.setItemName("示例数据项");
                indicatorDataExcel.setItemValue("10");
                indicatorDataExcel.setItemUnit("万元");
                indicatorDataExcel.setCurrentFlag(1);
                indicatorDataExcel.setIdentify("文本");
                indicatorDataExcel.setUpdateDate("年度更新样例：2022\n" + "月度更新样例：2022-06\n" + "季度更新样例：2022-第一季度\n" + "半年更新样例：2022-上半年");
                indicatorDataExcel.setSequence(1);
                dtoList2.add(indicatorDataExcel);
                dtoList = dtoList2;
            }else{
                IndicatorDataChartExcel indicatorDataChartExcel = new IndicatorDataChartExcel();
                indicatorDataChartExcel.setItemName("示例数据项");
                indicatorDataChartExcel.setItemValue("10");
                indicatorDataChartExcel.setItemUnit("万元");
                indicatorDataChartExcel.setCurrentFlag(1);
                indicatorDataChartExcel.setIdentify("正数");
                indicatorDataChartExcel.setUpdateDate("年度更新样例：2022\n" + "月度更新样例：2022-06\n" + "季度更新样例：2022-第一季度\n" + "半年更新样例：2022-上半年");
                indicatorDataChartExcel.setSequence(1);
                dtoList1.add(indicatorDataChartExcel);
                dtoList = dtoList1;
            }

        }
        LoginUser user = SecurityUtils.getLoginUser();
        String userName = "";
        if (!ObjectUtils.isEmpty(user)) {
//            userName = user.getRealname();
            //若依里没有realname,先用username代替
            userName = user.getUsername();
        }
//        mv.addObject(NormalExcelConstants.FILE_NAME, "指标数据管理信息");
        ExcelUtil<IndicatorDataExcel> indicatorDataExcelExcelUtil = new ExcelUtil<>(IndicatorDataExcel.class);
        ExcelUtil<IndicatorDataChartExcel> indicatorDataChartExcelExcelUtil = new ExcelUtil<>(IndicatorDataChartExcel.class);
        if ("word".equals(indicator.getType())) {
//            mv.addObject(NormalExcelConstants.CLASS, IndicatorDataExcel.class);
            indicatorDataExcelExcelUtil.exportExcel(response,dtoList2,"指标数据管理信息","指标数据管理信息(指标名称：" + indicator.getSourceName()
                    + "/" + indicator.getIndicatorName() + ")导出人：" + userName+"导出信息");
        } else {
//            mv.addObject(NormalExcelConstants.CLASS, IndicatorDataChartExcel.class);
            indicatorDataChartExcelExcelUtil.exportExcel(response,dtoList1,"指标数据管理信息","指标数据管理信息(指标名称：" + indicator.getSourceName()
                    + "/" + indicator.getIndicatorName() + ")导出人：" + userName+"导出信息");
        }

//        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("指标数据管理信息(指标名称：" + indicator.getSourceName()
//                + "/" + indicator.getIndicatorName() + ")", "导出人：" + userName, "导出信息"));
//        mv.addObject(NormalExcelConstants.DATA_LIST, dtoList);
//        return mv;
    }

    /**
     * 指标数据管理-导入指标管理数据(在使用)
     *
     * @param request
     * @param response
     * @return
     */
//    @Log(title = "指标数据管理-通过excel导入指标数据管理的数据",businessType = BusinessType.IMPORT)
    @PostMapping(value = "/importIndicatorDataExcel")
    public BaseResult<?> importIndicatorDataExcel(HttpServletRequest request, HttpServletResponse response) {

        long start = System.currentTimeMillis();

        String indicatorId = request.getParameter("indicatorId");
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            //获取文件大小
            double fileSize = (double) file.getSize() / 1048576;
            // 校验文件名字
            String originalFilename = file.getOriginalFilename();
            String suffix = originalFilename.substring(originalFilename.lastIndexOf('.'));
            String fileName = originalFilename.substring(0,originalFilename.lastIndexOf('.'));
            log.info("上传文件大小为=" + fileSize + "M");
            //校验文件是否被修改后缀
            String fileSuffixName = suffix.toLowerCase(Locale.ROOT);
            log.info("文件后缀为："+ JSONObject.toJSONString(fileSuffixName));


            if(fileSize > 20 ){
                return BaseResult.fail("上传文件最大为20M!");
            }
            if(!originalFilename.contains(".")){
                return BaseResult.fail("文件不能没有后缀!");
            }
            if(!Arrays.asList(FILE_SUFFIX_SUPPORT).contains(suffix.toLowerCase(Locale.ROOT))){
                return BaseResult.fail("文件格式不支持,请更换后重试!");
            }
            if(fileName.length() > 200){
                return BaseResult.fail("文件名长度不允许超过200，请更换后重试！");
            }
//            ImportParams params = new ImportParams();
//            params.setTitleRows(2);
//            params.setHeadRows(1);
//            params.setNeedSave(true);
            try {
                return indicatorService.importIndicatorDataExcelRuoYi(file, indicatorId);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return BaseResult.fail("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        long end = System.currentTimeMillis();
        log.error("批量导入共耗时：" + (end - start) + "毫秒");
        return BaseResult.fail("文件导入成功！");
    }

}


























