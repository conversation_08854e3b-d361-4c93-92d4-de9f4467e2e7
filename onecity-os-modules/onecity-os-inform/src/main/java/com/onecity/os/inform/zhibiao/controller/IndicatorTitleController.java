package com.onecity.os.inform.zhibiao.controller;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicatorDataTitle;
import com.onecity.os.inform.zhibiao.entity.vo.IndicatorTitleVo;
import com.onecity.os.inform.zhibiao.service.IndicatorTitleService;
import com.onecity.os.system.api.RemoteUserService;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 指标数据标头
 *
 * @Author: zack
 * @Date: 2024/1/22 15:22
 */
@Slf4j
@RestController
@RequestMapping("/indicatorTitle")
@Api(tags = "指标数据标头")
public class IndicatorTitleController extends BaseController {

    @Resource
    private IndicatorTitleService indicatorTitleService;

    @Resource
    private RemoteUserService remoteUserService;

    /**
     * 编辑
     *
     * @param titles
     * @return
     */
    @PostMapping("/saveData")
    @ApiOperation("新建/编辑/删除")
    public BaseResult<List<GeneralIndicatorDataTitle>> saveData(@RequestBody @Valid List<GeneralIndicatorDataTitle> titles) {
        //判断操作权限
        String sourceSimpleName = titles.get(0).getSourceSimpleName();
        Long userId = SecurityUtils.getUserId();
        BaseResult<Boolean> resultPerms = remoteUserService.getUserIndicatorTitlePerms(sourceSimpleName,userId);
        Boolean perms = false;
        if(org.apache.commons.lang3.ObjectUtils.isNotEmpty(resultPerms)) {
            perms = resultPerms.getData();
        }
        if(!perms){
            return BaseResult.fail("无操作权限，请分配权限后重试！");
        }
        return indicatorTitleService.saveData(titles);
    }

    /**
     * 查询
     *
     * @param indicatorId
     * @return
     */
    @GetMapping("/list")
    @ApiOperation("查询")
    public BaseResult<List<GeneralIndicatorDataTitle>> queryById(@RequestParam(name = "indicatorId") String indicatorId) {
        List<GeneralIndicatorDataTitle> titles = indicatorTitleService.queryById(indicatorId);
        return BaseResult.ok(titles);
    }


    @PostMapping("/saveUnit")
    @ApiOperation("主值单位调整")
    public BaseResult<?> saveUnit(@RequestBody @Valid IndicatorTitleVo titleVo) {
        log.info("1.1.1 : titleVo : {}", JSONObject.toJSONString(titleVo));
        return indicatorTitleService.saveUnit(titleVo);
    }

}
