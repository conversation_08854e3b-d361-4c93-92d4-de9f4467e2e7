package com.onecity.os.inform.zhibiao.controller.app;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageInfo;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.inform.constant.UtilConstants;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicatorDataTitle;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicatorTianbao;
import com.onecity.os.inform.zhibiao.model.dto.IndicatorDataDto;
import com.onecity.os.inform.zhibiao.model.dto.IndicatorDataReBean;
import com.onecity.os.inform.zhibiao.service.DataConfigService;
import com.onecity.os.inform.zhibiao.service.IndicatorService;
import com.onecity.os.inform.zhibiao.service.IndicatorTitleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * app端指标
 *
 * <AUTHOR>
 * @since 2024/9/4 9:42
 */
@Api(tags = "指标")
@Slf4j
@RestController
@RequestMapping("/app/indicator")
@Transactional
public class AppIndicatorController extends BaseController {

    @Autowired
    private IndicatorService indicatorService;

    @Resource
    private DataConfigService dataConfigService;

    @Resource
    private IndicatorTitleService indicatorTitleService;

    /**
     * 指标数据管理-通过id获取数据配置详细信息
     *
     * @param id
     */
    @ApiOperation(value = "指标数据管理-通过指标id获取数据配置详细信息")
    @GetMapping("/getDataConfigByIndicatorId")
    public BaseResult<?> getDataConfigByIndicatorId(@RequestParam String id) {
        return BaseResult.ok(dataConfigService.getDataConfigByIndicatorId(id));
    }

    /**
     * 1 指标数据管理-查询指标数据列表
     *
     * @param indicatorId
     * @param tj
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "指标数据管理-查询指标数据列表")
    @GetMapping("/getIndicatorDetailList")
    public BaseResult<?> getTargetDetailList(@RequestParam(name = "indicatorId", required = true) String indicatorId,
                                             @RequestParam(name = "tj", required = true) String tj,
                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        if (org.springframework.util.StringUtils.isEmpty(indicatorId)) {
            return BaseResult.fail("查询id不能为空");
        }
        IndicatorDataDto dto = new IndicatorDataDto();
        // 查询指标信息
        GeneralIndicatorTianbao generalIndicator = indicatorService.getIndicatorById(indicatorId, tj);
        if (ObjectUtils.isEmpty(generalIndicator)) {
            return BaseResult.fail("分组下无数据！");
        }
        if (1 == generalIndicator.getIndicatorType()) {
            return BaseResult.fail("分组下无数据！");
        }
        dto.setTreeId(generalIndicator.getId());
        dto.setSource(generalIndicator.getSourceName());
        dto.setUpdateDate(generalIndicator.getUpdateDate());
        dto.setType(generalIndicator.getIndicatorExhibitType());
        dto.setUpdateCycle(generalIndicator.getUpdateCycle());
        dto.setIsLegend(generalIndicator.getIsLegend());
        // 查询指标头信息
        List<GeneralIndicatorDataTitle> titles = indicatorTitleService.queryById(generalIndicator.getId());
        dto.setTableHeaderList(titles);
        // 可用字段
        if (!CollectionUtils.isEmpty(titles)) {
            dto.setCheckColumn(new ArrayList<>(titles.get(0).getCheckColumn()));
        } else {
            //默认可用标头，逐条大小写转换
            dto.setCheckColumn(UtilConstants.getAllColumn().stream().map(UtilConstants::getItemMap).collect(Collectors.toList()));
        }
        startPage();
        PageInfo<IndicatorDataReBean> pageInfo = indicatorService.getTargetDetailList(indicatorId, tj, pageNo, pageSize);
        if (ObjectUtil.isNotNull(pageInfo)) {
            dto.setTotal(pageInfo.getTotal());
            if (0 != pageInfo.getSize()) {
                dto.setData(pageInfo.getList());
                dto.setSize(pageInfo.getSize());
                dto.setCurrent(pageInfo.getPageNum());
                dto.setPages(pageInfo.getPages());
            }
            return BaseResult.ok(dto);
        }
        if (null == pageInfo) {
            return BaseResult.ok(null, "分组下无数据！");
        }
        return BaseResult.ok(dto);
    }
}
