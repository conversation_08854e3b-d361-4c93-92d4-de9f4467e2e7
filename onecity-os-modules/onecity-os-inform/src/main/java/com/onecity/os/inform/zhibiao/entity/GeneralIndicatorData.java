package com.onecity.os.inform.zhibiao.entity;


import java.util.Date;
import javax.persistence.*;

@Table(name = "general_indicator_data")
public class GeneralIndicatorData {
    /**
     * 主键自增
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 指标Id
     */
    @Column(name = "indicator_id")
    private String indicatorId;

    /**
     * 指标名称
     */
    @Column(name = "indicator_name")
    private String indicatorName;

    /**
     * 指标项目
     */
    @Column(name = "item_name")
    private String itemName;

    /**
     * 指标项目值
     */
    @Column(name = "item_value")
    private String itemValue;

    /**
     * 单位
     */
    @Column(name = "item_unit")
    private String itemUnit;

    /**
     * 用来表示指标数据是增加还是减少，前端显示不同的颜色
     */
    private String identify;

    /**
     * 指标文字展示方式，0：平铺；1：加横线
     */
    private Integer style;

    /**
     * 是否折行0：否1：是
     */
    @Column(name = "is_fold")
    private Integer isFold;

    /**
     * 排序
     */
    private Integer sequence;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    private String updateDate;

    /**
     * 是否当前展示0：是1：否
     */
    @Column(name = "current_flag")
    private Integer currentFlag;

    /**
     * 是否删除0:否1:是
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人
     */
    private String creater;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 获取主键自增
     *
     * @return id - 主键自增
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键自增
     *
     * @param id 主键自增
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取指标Id
     *
     * @return indicator_id - 指标Id
     */
    public String getIndicatorId() {
        return indicatorId;
    }

    /**
     * 设置指标Id
     *
     * @param indicatorId 指标Id
     */
    public void setIndicatorId(String indicatorId) {
        this.indicatorId = indicatorId;
    }

    /**
     * 获取指标名称
     *
     * @return indicator_name - 指标名称
     */
    public String getIndicatorName() {
        return indicatorName;
    }

    /**
     * 设置指标名称
     *
     * @param indicatorName 指标名称
     */
    public void setIndicatorName(String indicatorName) {
        this.indicatorName = indicatorName;
    }

    /**
     * 获取指标项目
     *
     * @return item_name - 指标项目
     */
    public String getItemName() {
        return itemName;
    }

    /**
     * 设置指标项目
     *
     * @param itemName 指标项目
     */
    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    /**
     * 获取指标项目值
     *
     * @return item_value - 指标项目值
     */
    public String getItemValue() {
        return itemValue;
    }

    /**
     * 设置指标项目值
     *
     * @param itemValue 指标项目值
     */
    public void setItemValue(String itemValue) {
        this.itemValue = itemValue;
    }

    /**
     * 获取单位
     *
     * @return item_unit - 单位
     */
    public String getItemUnit() {
        return itemUnit;
    }

    /**
     * 设置单位
     *
     * @param itemUnit 单位
     */
    public void setItemUnit(String itemUnit) {
        this.itemUnit = itemUnit;
    }

    /**
     * 获取用来表示指标数据是增加还是减少，前端显示不同的颜色
     *
     * @return identify - 用来表示指标数据是增加还是减少，前端显示不同的颜色
     */
    public String getIdentify() {
        return identify;
    }

    /**
     * 设置用来表示指标数据是增加还是减少，前端显示不同的颜色
     *
     * @param identify 用来表示指标数据是增加还是减少，前端显示不同的颜色
     */
    public void setIdentify(String identify) {
        this.identify = identify;
    }

    /**
     * 获取指标文字展示方式，0：平铺；1：加横线
     *
     * @return style - 指标文字展示方式，0：平铺；1：加横线
     */
    public Integer getStyle() {
        return style;
    }

    /**
     * 设置指标文字展示方式，0：平铺；1：加横线
     *
     * @param style 指标文字展示方式，0：平铺；1：加横线
     */
    public void setStyle(Integer style) {
        this.style = style;
    }

    /**
     * 获取是否折行0：否1：是
     *
     * @return is_fold - 是否折行0：否1：是
     */
    public Integer getIsFold() {
        return isFold;
    }

    /**
     * 设置是否折行0：否1：是
     *
     * @param isFold 是否折行0：否1：是
     */
    public void setIsFold(Integer isFold) {
        this.isFold = isFold;
    }

    /**
     * 获取排序
     *
     * @return sequence - 排序
     */
    public Integer getSequence() {
        return sequence;
    }

    /**
     * 设置排序
     *
     * @param sequence 排序
     */
    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    /**
     * 获取更新日期
     *
     * @return update_date - 更新日期
     */
    public String getUpdateDate() {
        return updateDate;
    }

    /**
     * 设置更新日期
     *
     * @param updateDate 更新日期
     */
    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    /**
     * 获取是否当前展示0：是1：否
     *
     * @return current_flag - 是否当前展示0：是1：否
     */
    public Integer getCurrentFlag() {
        return currentFlag;
    }

    /**
     * 设置是否当前展示0：是1：否
     *
     * @param currentFlag 是否当前展示0：是1：否
     */
    public void setCurrentFlag(Integer currentFlag) {
        this.currentFlag = currentFlag;
    }

    /**
     * 获取是否删除0:否1:是
     *
     * @return is_delete - 是否删除0:否1:是
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    /**
     * 设置是否删除0:否1:是
     *
     * @param isDelete 是否删除0:否1:是
     */
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取创建人
     *
     * @return creater - 创建人
     */
    public String getCreater() {
        return creater;
    }

    /**
     * 设置创建人
     *
     * @param creater 创建人
     */
    public void setCreater(String creater) {
        this.creater = creater;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新人
     *
     * @return updater - 更新人
     */
    public String getUpdater() {
        return updater;
    }

    /**
     * 设置更新人
     *
     * @param updater 更新人
     */
    public void setUpdater(String updater) {
        this.updater = updater;
    }
}