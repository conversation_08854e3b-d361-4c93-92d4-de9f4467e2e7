package com.onecity.os.inform.zhibiao.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 普通指标数据
 *
 */
@Data
@Table(name = "general_indicator_data")
public class GeneralIndicatorDataTianbao {
    /**
     * 主键自增
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Long id;

    /**
     * 指标Id
     */
    @Column(name = "indicator_id")
    private String indicatorId;

    /**
     * 指标名称
     */
    @Column(name = "indicator_name")
    private String indicatorName;

    /**
     * 指标项目
     */
    @Column(name = "item_name")
    private String itemName;

    /**
     * 指标项目值
     */
    @Column(name = "item_value")
    private String itemValue;

    /**
     * 指标项目值1
     */
    @Column(name = "item_value1")
    private String itemValue1;

    /**
     * 指标项目值2
     */
    @Column(name = "item_value2")
    private String itemValue2;

    /**
     * 指标项目值3
     */
    @Column(name = "item_value3")
    private String itemValue3;

    /**
     * 指标项目值4
     */
    @Column(name = "item_value4")
    private String itemValue4;

    /**
     * 指标项目值5
     */
    @Column(name = "item_value5")
    private String itemValue5;

    /**
     * 指标项目值6
     */
    @Column(name = "item_value6")
    private String itemValue6;

    /**
     * 单位
     */
    @Column(name = "item_unit")
    private String itemUnit;

    /**
     * 第二单位
     */
    @Column(name = "item_unit_2nd")
    private String itemUnit2nd;

    /**
     * 用来表示指标数据是增加还是减少，前端显示不同的颜色
     */
    private String identify;

    /**
     * 指标文字展示方式，0：平铺；1：加横线
     */
    private Integer style;

    /**
     * 是否折行0：否1：是
     */
    @Column(name = "is_fold")
    private Integer isFold;

    /**
     * 排序
     */
    private Integer sequence;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    private String updateDate;

    /**
     * 是否当前展示0：否1：是
     */
    @Column(name = "current_flag")
    private Integer currentFlag;

    /**
     * 是否删除0:否1:是
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人
     */
    private String creater;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;
}
