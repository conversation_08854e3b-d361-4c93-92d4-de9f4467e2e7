package com.onecity.os.inform.zhibiao.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 指标标头标
 */

@Data
@Table(name = "general_indicator_data_title")
public class GeneralIndicatorDataTitle {
    /**
     * 主键自增
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private String id;

    /**
     * 指标Id
     */
    @Column(name = "indicator_id")
    private String indicatorId;

    /**
     * 指标名称
     */
    @Column(name = "indicator_name")
    private String indicatorName;

    /**
     * 指标项目
     */
    @Column(name = "item_name")
    private String itemName;

    /**
     * 是否删除 0:否 1:是
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @Column(name = "creator")
    private String creator;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @Column(name = "updater")
    private String updater;

    /**
     * 指标值名称
     */
    @Column(name = "item_value_name")
    private String itemValueName;

    /**
     * 指标值名称 （x轴，y轴）
     */
    @Column(name = "item_name_name")
    private String itemNameName;

    /**
     * 是否为主值项（主值-1；副值-0）
     */
    @Column(name = "main_value")
    private String mainValue;

    /**
     * 指标值字段名（对应指标数据表中字段名）
     */
    @Column(name = "column_name")
    private String columnName;

    /**
     * 排序
     */
    @Column(name = "sequence")
    private Integer sequence;

    /**
     * 返回给前端对应字段名称
     */
    @Transient
    private String frontColumnName;

    /**
     * 操作字段 表示本条数据本次操作
     * （新增--ADD; 删除--DEl; 调整--UPDATE; 保持--KEEP）
     */
    @Transient
    private String operate;

    /**
     * 可用字段名
     */
    @Transient
    private List<String> checkColumn;


    /**
     * 操作的厅局编码
     */
    @Transient
    private String sourceSimpleName;

}