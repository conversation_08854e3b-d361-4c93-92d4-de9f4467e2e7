package com.onecity.os.inform.zhibiao.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Table;
import java.util.Date;

/**
 * 普通指标
 *
 */
@Data
@Table(name = "general_indicator")
public class GeneralIndicatorTianbao {
    /**
     * 主键
     **/
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private String id;

    /**
     * 指标名称
     **/
    private String indicatorName;
    /**
     * 指标展现类型
     **/
    private String indicatorExhibitType;

    /**
     * 父指标ID,如果为一级指标, 该字段为空
     **/
    private String parentId;

    /**
     * 父指标名称,如果为一级指标, 该字段为空
     **/
    private String parentName;

    /**
     * 图标地址
     */
    private String iconUrl;

    /**
     * 数据来源id
     */
    private String sourceId;

    /**
     * 数据来源
     */
    private String sourceName;

    /**
     * 排序
     **/
    private Integer sequence;

    /**
     * 指标类型，0：指标，1：tab类型
     **/
    private Integer indicatorType;

    /**
     * 更新日期文本类型
     **/
    private String updateDate;

    /**
     * 更新周期
     **/
    private String updateCycle;
    /**
     * 面向领导
     */
    private String leader;

    /**
     * 是否删除0:否1:是
     */
    private Integer isDelete;

    /**
     * 创建时间
     **/
    private Date createTime;

    /**
     * 创建人
     **/
    private String creater;

    /**
     * 更新时间
     **/
    private Date updateTime;

    /**
     * 更新人
     **/
    private String updater;

    /**
     * 分组类型 0指标 1网页
     */
    private Integer groupType;

    /**
     * 分组网页
     */
    private String groupUrl;

    /**
     * 约定更新时间
     */
    private String planUpdateDate;

    /**
     * 是否展示 0-不展示1展示
     */
    @Column(name = "is_show")
    private Integer isShow;

    /**
     * 是否展示筛选框 0-不展示1展示
     */
    @Column(name = "is_screen")
    private Integer isScreen;

    /**
     * 是否展示筛选框 0-不展示1展示
     */
    @Column(name = "is_legend")
    private Integer isLegend;

    /**
     * 数据更新方式1-手动填报2数据对接
     */
    @Column(name = "data_update_mode")
    private Integer dataUpdateMode;

    /**
     * 数据配置id
     */
    @Column(name = "data_config_id")
    private String dataConfigId;
}
