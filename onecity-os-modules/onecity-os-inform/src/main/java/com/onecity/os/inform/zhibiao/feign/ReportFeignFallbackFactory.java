package com.onecity.os.inform.zhibiao.feign;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.inform.zhibiao.feign.ResponseBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * @Author: zack
 * @Date: 2022/5/31 10:04
 */
@Slf4j
@Component
public class ReportFeignFallbackFactory implements FallbackFactory<ReportFeignService> {
    @Override
    public ReportFeignService create(Throwable cause) {
        return new ReportFeignService() {
            @Override
            public ResponseBean detailById(@PathVariable("id") Long id) {
                log.info("detailById  : {} ", JSONObject.toJSONString(id));
                log.error("数据集数据获取异常！");
                ResponseBean responseBean = ResponseBean.builder().build();
                responseBean.setMessage("数据集数据获取异常！");
                return responseBean;
            }
        };
    }
}
