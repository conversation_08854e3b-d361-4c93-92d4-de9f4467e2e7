package com.onecity.os.inform.zhibiao.mapper;


import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.inform.zhibiao.entity.DataConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DataConfigMapper extends BaseMapper<DataConfig> {

    /**
     * 新增数据配置
     * @param dc
     */
    void addDataConfig(@Param("dc") DataConfig dc);

    /**
     * 修改数据配置
     * @param dc
     */
    void updateDataConfig(@Param("dc") DataConfig dc);

    /**
     * 根据id获取数据配置详情
     * @param id
     * @return
     */
    DataConfig getDataConfigById(@Param("id") String id);

    /**
     * 根据指标id获取数据配置详情列表
     * @param id
     * @return
     */
    List<DataConfig> getDataConfigByIndicatorId(@Param("id") String id);

    /**
     * 根据id删除数据配置
     * @param dc
     */
    void deleteDataConfigById(@Param("dc") DataConfig dc);

    /**
     * 根据指标id删除数据配置
     * @param dc
     */
    void deleteDataConfigByIndicatorId(@Param("dc") DataConfig dc);
}
