package com.onecity.os.inform.zhibiao.mapper;


import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicatorData;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicatorDataTianbao;
import com.onecity.os.inform.zhibiao.model.vo.IndicatorDataExcel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预览页用的数据表
 */
public interface GeneralIndicatorDataMapper extends BaseMapper<GeneralIndicatorData> {
    /**
     * 根据厅局来源,查找指标数据
     *
     * @param sources
     * @return
     */
    List<GeneralIndicatorDataTianbao> getIndicatorDataListBySourceIds(@Param("sources") List<String> sources);

    /**
     * 插入数据
     *
     * @param generalIndicatorData
     */
    void insertData(@Param("vo") GeneralIndicatorData generalIndicatorData);

    /**
     * 根据指标id,查找指标数据
     *
     * @param indicatorId
     * @return
     */
    List<IndicatorDataExcel> getExportIndicatorDataXlsByIndicatorId(@Param("indicatorId") String indicatorId);

    /**
     * 根据指标id,查找指标数据项名
     *
     * @param indicatorId
     * @return
     */
    List<String> getIndicatorDataNameListByIndicatorId(@Param("indicatorId") String indicatorId);

    /**
     * 插入指标数据
     *
     * @param generalIndicatorData
     */
    void insertTianBaoIndicatorData(@Param("vo") GeneralIndicatorData generalIndicatorData);

    /**
     * 根据指标id,删除指标数据
     *
     * @param indicatorId
     */
    void deleteDataByIndicatorId(@Param("indicatorId") String indicatorId);

    /**
     * 根据指标id,和指标数据项名称,更新指标数据
     *
     * @param generalIndicatorData
     */
    void updateByIndicatorIdAndItemName(@Param("vo") GeneralIndicatorData generalIndicatorData);
}















