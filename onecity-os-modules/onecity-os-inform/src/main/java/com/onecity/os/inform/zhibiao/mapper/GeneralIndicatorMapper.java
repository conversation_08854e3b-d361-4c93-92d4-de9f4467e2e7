package com.onecity.os.inform.zhibiao.mapper;



import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicator;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicatorTianbao;
import com.onecity.os.inform.zhibiao.model.vo.IndicatorDataExcel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface GeneralIndicatorMapper extends BaseMapper<GeneralIndicator> {
    /**
     * 根据厅局,查找预览页指标
     *
     * @param sources
     * @return
     */
    List<GeneralIndicatorTianbao> getIndicatorListBySourceIds(@Param("sources") List<String> sources);

    /**
     * 根据厅局,查找预览页指标
     *
     * @param id
     * @return
     */
    GeneralIndicator getIndicatorInfoById(@Param("id") String id);


    /**
     * 根据厅局id(source_manage的主键id),更新周期,起始时间,结束时间,查找更新的指标
     *
     * @param sourceId
     * @param parentIndicatorId
     * @param updateCycle
     * @param startTime
     * @param endTime
     * @return
     */
    @Deprecated
    List<String> getIndicatorListBySourceIdAndCycle(@Param("sourceId") String sourceId,
                                                    @Param("parentIndicatorId") String parentIndicatorId,
                                                    @Param("updateCycle") String updateCycle,
                                                    @Param("startTime") String startTime,
                                                    @Param("endTime") String endTime);

    /**
     * 根据厅局简称,查找父级指标id
     *
     * @param sourceSimpleName
     * @param startTime
     * @param endTime
     * @return
     */
    List<String> getParentIndicatorIdListBySourceId(@Param("sourceId") String sourceSimpleName,
                                                    @Param("startTime") String startTime,
                                                    @Param("endTime") String endTime);

    /**
     * 根据厅局简称,查找所有一级指标,parentId=0的指标id
     *
     * @param sourceSimpleName
     * @return
     */
    List<String> getParentIndicatorIdsBySourceId(@Param("sourceSimpleName") String sourceSimpleName);


    /**
     * 根据指标id,查找指标名称和类型
     *
     * @param indicatorId
     * @return
     */
    IndicatorDataExcel getIndicatorNameSourceNameById(@Param("indicatorId") String indicatorId);


    /**
     * 根据多个厅局简称,查找所有父级指标id
     *
     * @param sourceNames
     * @return
     */
    List<String> getParentIndicatorIdBySourceIds(@Param("sourceIds") List<String> sourceNames);

    /**
     * 根据多个厅局sourceNames,查找所有父级指标id
     *
     * @param sourceNames
     * @return
     */
    List<String> getParentIndicatorIdBySourceNames(@Param("sourceNames") List<String> sourceNames);

    /**
     * 根据多个厅局sourceName,查找所有父级指标id
     *
     * @param sourceName
     * @return
     */
    List<String> getParentIndicatorIdBySourceName(@Param("sourceName") String sourceName);

    /**
     * 根据父级指标id,查找所有子集指标信息
     *
     * @param indicatorParentIds
     * @return
     */
    List<GeneralIndicator> getIndicatorsByParentIds(@Param("parentIds") List<String> indicatorParentIds);


    /**
     * 根据父级指标id,查找所有子集指标ids
     *
     * @param indicatorParentIds
     * @return
     */
    List<String> getIndicatorIdsByParentIds(@Param("parentIds") List<String> indicatorParentIds);

   /**
     * 根据指标id,查找指标展示类型
     *
     * @param indicatorId
     * @return
     */
    String getIndicatorExhibitTypeById(@Param("indicatorId") String indicatorId);

    /**
     * 根据指标id,查找指标展示类型
     *
     * @param indicatorId
     * @return
     */
    String getIndicatorExhibitTypeByIdTianbao(@Param("indicatorId") String indicatorId);
}












