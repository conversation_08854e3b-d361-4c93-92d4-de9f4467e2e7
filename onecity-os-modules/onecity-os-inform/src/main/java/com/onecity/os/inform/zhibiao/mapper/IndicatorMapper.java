package com.onecity.os.inform.zhibiao.mapper;


import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicator;
import com.onecity.os.inform.zhibiao.model.dto.IndicatorDataReBean;
import com.onecity.os.inform.zhibiao.model.dto.IndicatorDto;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicatorTianbao;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicatorDataTianbao;
import com.onecity.os.inform.zhibiao.model.dto.IndicatorDataDto;
import com.onecity.os.inform.zhibiao.model.vo.MoveIndicatorVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 */
public interface IndicatorMapper extends BaseMapper<GeneralIndicatorDataTianbao> {
    /**
     * 根据指标id,查找部分指标信息
     *
     * @param tabId 指标id
     * @param tj    厅局来源
     * @return {@link GeneralIndicatorTianbao}
     */
    GeneralIndicatorTianbao getInfoById(@Param("tabId") String tabId, @Param("tj") String tj);

    /**
     * 根据指标数据id获取指标id
     * @param id
     * @return
     */
    String getIndicatorIdByDataId(@Param("id") String id);

    /**
     * 根据指标id,查找部分指标信息
     *
     * @param tabId 指标id
     * @return {@link GeneralIndicatorTianbao}
     */
    GeneralIndicatorTianbao getInfoOnlyById(@Param("tabId") String tabId);


    /**
     * 根据父id,查找指标子集
     *
     * @param tabId
     * @param tj
     * @return
     */
    List<GeneralIndicator> getByParentId(@Param("tabId") String tabId, @Param("tj") String tj);


    /**
     * 根据版块编码获取该板块下有更新的指标id
     * @param sourceSimpleName
     * @return
     */
    List<String> getUpdatedIndicatorIds(@Param("sourceSimpleName") String sourceSimpleName);
    /**
     * 根据父指标id,查找子集指标数据信息
     *
     * @param id
     * @param tj
     * @return
     */
    List<IndicatorDto> getIndicatorDataByParentId(@Param("id") String id, @Param("tj") String tj);

    /**
     * 根据指标ids,删除指标
     *
     * @param ids
     * @param userName
     * @param tj
     */
    void deleteTargets(@Param("ids") List<String> ids, @Param("userName") String userName, @Param("tj") String tj);

    /**
     * 新增指标分组(或指标)
     *
     * @param indicator
     * @param tj
     * @param sourceName
     */
    void addIndicator(@Param("vo") GeneralIndicatorTianbao indicator, @Param("tj") String tj, @Param("sourceName") String sourceName);

    /**
     * 新增指标分组(或指标)
     *
     * @param indicator
     * @param tableName
     */
    void insertIndicator(@Param("vo") GeneralIndicatorTianbao indicator, @Param("tableName") String tableName);

    /**
     * 新增指标分组(或指标)
     *
     * @param indicator
     * @param tableName
     */
    void insertIndicatorSelective(@Param("vo") GeneralIndicatorTianbao indicator, @Param("tableName") String tableName);

    void insertIndicatorBatch(@Param("indicatorList") List<GeneralIndicatorTianbao> indaaicatorList, @Param("tableName") String tableName);

    /**
     * 根据指标id,更新指标
     *
     * @param indicator
     * @param tj
     */
    void updateIndicator(@Param("vo") GeneralIndicatorTianbao indicator, @Param("tj") String tj);

    /**
     * 根据父指标id,查找指标ids
     *
     * @param parentId
     * @param tj
     * @return
     */
    List<String> getIdsByParentId(@Param("parentId") String parentId, @Param("sourceId") String tj);

    /**
     * 根据指标id,获取指标数据
     *
     * @param id
     * @return
     */
    List<IndicatorDataReBean> getDataListByIndicatorId(@Param("id") String id);

    /**
     * 根据指标id,更新指标信息
     *
     * @param indicator
     * @param tj
     */
    void updateIndicatorSourceAndDate(@Param("vo") IndicatorDataDto indicator, @Param("tj") String tj);

    /**
     * 根据指标id,查找指标数据
     *
     * @param treeId
     * @return
     */
    List<Long> getDataIdsByIndicatorId(@Param("indicatorId") String treeId);

    /**
     * 根据id,删除指标表数据
     *
     * @param existDataIds
     * @param userName
     */
    void deleteIndicatorDtaByIds(@Param("ids") List<Long> existDataIds, @Param("userName") String userName);

    /**
     * 向指标数据表中增加数据
     *
     * @param data
     */
    void insertIndicatorData(@Param("vo") GeneralIndicatorDataTianbao data);

    void insertIndicatorDataSelective(@Param("vo") GeneralIndicatorDataTianbao data, @Param("dataTableName") String dataTableName);

    void insertIndicatorDataBatch(@Param("indicatorDataList") List<GeneralIndicatorDataTianbao> indicatorDataList, @Param("dataTableName") String dataTableName);

    /**
     * 根据id,更新指标数据表
     *
     * @param data
     */
    void updateIndicatorDataById(@Param("vo") GeneralIndicatorDataTianbao data);

    /**
     * 根据ids,删除指标数据
     *
     * @param ids
     * @param userName
     */
    void deleteIndicatorDataByIds(@Param("ids") String[] ids, @Param("userName") String userName);


    /**
     * 根据id,查找父指标id
     *
     * @param id
     * @return
     */
    String getParentIdById(String id);

    /**
     * 调整指标分组
     *
     * @param vo
     * @param userName
     */
    void MoveIndicatorVo(@Param("vo") MoveIndicatorVo vo, @Param("userName") String userName);


    /**
     * 根据指标id,查找指标数据ids
     *
     * @param treeId
     * @return
     */
    List<Long> getDataIdsByIndicatorIdPage(@Param("indicatorId") String treeId);

    /**
     * 分页查询已经存在的指标id
     *
     * @param id
     * @param tj
     * @return
     */
    List<String> getIdsByParentIdPage(@Param("parentId") String id, @Param("sourceId") String tj);

    /**
     * 根据厅局,查找填报系统指标数据
     *
     * @param sources
     * @return
     */
    List<GeneralIndicatorDataTianbao> getIndicatorDataListBySourceIds(@Param("sources") List<String> sources);

    /**
     * 根据厅局,查找填报系统指标
     *
     * @param sources
     * @return
     */
    List<GeneralIndicatorTianbao> getIndicatorListBySourceIds(@Param("sources") List<String> sources);

    /**
     * 根据id,查找指标的约定更新时间
     *
     * @param id
     * @return
     */
    String getPlanUpdateById(@Param("id") String id);


}













