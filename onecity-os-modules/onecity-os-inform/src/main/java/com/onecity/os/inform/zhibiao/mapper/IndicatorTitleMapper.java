package com.onecity.os.inform.zhibiao.mapper;


import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicatorDataTitle;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2024/1/23 11:00
 */
@Mapper
public interface IndicatorTitleMapper extends BaseMapper<GeneralIndicatorDataTitle> {

    /**
     * 新建 IndicatorDataTitle
     *
     * @param title
     */
    void insertIndicatorTitle(@Param("title") GeneralIndicatorDataTitle title);

    /**
     * 删除指标标头
     *
     * @param id
     */
    void delIndicatorTitle(@Param("id") String id);

    /**
     * 更新指标标头
     *
     * @param title
     */
    void updateIndicatorTitle(@Param("title") GeneralIndicatorDataTitle title);

    /**
     * 查询指标标头
     *
     * @param indicatorId
     * @return
     */
    List<GeneralIndicatorDataTitle> queryById(@Param("indicatorId") String indicatorId);

    /**
     * 更新指标标头
     *
     * @param id
     */
    void updateTitleMain(@Param("id") String id, @Param("mainValue") String mainValue);


    /**
     * 更新指标数据单位
     *
     * @param indicatorId
     */
    void updateUnitByIndicatorId(@Param("indicatorId") String indicatorId, @Param("itemUnit") String itemUnit,
                                 @Param("itemUnit2nd") String itemUnit2nd);
}













