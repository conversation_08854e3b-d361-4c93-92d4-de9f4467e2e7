package com.onecity.os.inform.zhibiao.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicatorDataTitle;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 返回数据 指标数据
 *
 */
@Data
public class IndicatorDataDto {
    /**
     * treeId : 123
     * source : 操作成功！
     * updateDate : 33
     * data : [{"id":"2","sequence":"1","itemName":"嗯嗯","itemValue":"1","itemUnit":"1","identify":"1","style":"1"},{"id":"2","sequence":"2","itemName":"2","itemValue":"2","itemUnit":"2","identify":"2","style":"2"}]
     */
    /**
     * 指标id
     */
    private String treeId;

    /**
     * 数据来源
     */
    private String source;

    /**
     * 指标展示方式
     */
    private String type;

    /**
     * 数据更新时间
     */
    private String updateDate;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 分页:的数据总条数
     */
    private Long total;

    /**
     * 分页:查询出来的总数量
     */
    private Integer size;

    /**
     * 分页:当前页
     */
    private Integer current;

    /**
     * 分页:总页数
     */
    private Integer pages;

    /**
     * 指标是否展示
     */
    private Integer isShow;

    /**
     * 是否展示筛选框
     */
    private Integer isScreen;

    /**
     * 是否展示筛选框 0-不展示1展示
     */
    private Integer isLegend;

    /**
     * 更新周期
     */
    private String updateCycle;

    /**
     * 子集列表项
     */
    private List<IndicatorDataReBean> data;

    /**
     * id : 2
     * sequence : 1
     * itemName : 嗯嗯
     * itemValue : 1
     * itemUnit : 1
     * identify : 1
     * style : 1
     * isFold : 1
     */
    public static class DataReBean {
        /**
         * 主键id
         */
        private Long id;

        /**
         * 指标id
         */
        private String indicatorId;

        /**
         * 指标项
         */
        private String itemName;

        /**
         * 数值
         */
        private String itemValue;

        private String itemValue1;

        private String itemValue2;

        private String itemValue3;

        private String itemValue4;

        private String itemValue5;

        private String itemValue6;

        /**
         * 数值类型
         */
        private String identify;

        /**
         * 数值单位
         */
        private String itemUnit;

        /**
         * 第二单位
         */
        private String itemUnit2nd;

        /**
         * 排序
         */
        private Integer sequence;

        /**
         * 是否有分割线
         */
        private Integer style;

        /**
         * 是否折行
         */
        private Integer isFold;

        /**
         * 数据期
         */
        private String updateDate;

        /**
         * 是否展示 0：否1：是
         */
        private Integer currentFlag;

        /**
         * 创建人
         */
        private String creater;

        /**
         * 创建时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date createTime;

        /**
         * 更新人
         */
        private String updater;

        /**
         * 更新时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date updateTime;

        /**
         * 数据对接数据值列表接收
         */
        private List<ItemValues> itemValuesList;
        /**
         * x轴或指标项名称
         */
        private String dataKeyName;

        /**
         * 副值单位
         */
        private String secondaryUnit;

        public String getIndicatorId() {
            return indicatorId;
        }

        public void setIndicatorId(String indicatorId) {
            this.indicatorId = indicatorId;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public Integer getSequence() {
            return sequence;
        }

        public void setSequence(Integer sequence) {
            this.sequence = sequence;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public String getItemValue() {
            return itemValue;
        }

        public void setItemValue(String itemValue) {
            this.itemValue = itemValue;
        }

        public String getItemUnit() {
            return itemUnit;
        }

        public void setItemUnit(String itemUnit) {
            this.itemUnit = itemUnit;
        }

        public String getIdentify() {
            return identify;
        }

        public void setIdentify(String identify) {
            this.identify = identify;
        }

        public Integer getStyle() {
            return style;
        }

        public void setStyle(Integer style) {
            this.style = style;
        }

        public Integer getIsFold() {
            return isFold;
        }

        public void setIsFold(Integer isFold) {
            this.isFold = isFold;
        }

        public Date getCreateTime() {
            return createTime;
        }

        public void setCreateTime(Date createTime) {
            this.createTime = createTime;
        }

        public String getCreater() {
            return creater;
        }

        public void setCreater(String creater) {
            this.creater = creater;
        }

        public Date getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(Date updateTime) {
            this.updateTime = updateTime;
        }

        public String getUpdater() {
            return updater;
        }

        public void setUpdater(String updater) {
            this.updater = updater;
        }

        public String getUpdateDate() {
            return updateDate;
        }

        public void setUpdateDate(String updateDate) {
            this.updateDate = updateDate;
        }

        public Integer getCurrentFlag() {
            return currentFlag;
        }

        public void setCurrentFlag(Integer currentFlag) {
            this.currentFlag = currentFlag;
        }

        public List<ItemValues> getItemValuesList() {
            return itemValuesList;
        }

        public void setItemValuesList(List<ItemValues> itemValuesList) {
            this.itemValuesList = itemValuesList;
        }

        public String getDataKeyName() {
            return dataKeyName;
        }

        public void setDataKeyName(String dataKeyName) {
            this.dataKeyName = dataKeyName;
        }

        public String getSecondaryUnit() {
            return secondaryUnit;
        }

        public void setSecondaryUnit(String secondaryUnit) {
            this.secondaryUnit = secondaryUnit;
        }

    }

    /**
     * 指标头数据
     */
    private List<GeneralIndicatorDataTitle> tableHeaderList;

    /**
     * 可用字段名
     */
    private List<String> checkColumn;

}
