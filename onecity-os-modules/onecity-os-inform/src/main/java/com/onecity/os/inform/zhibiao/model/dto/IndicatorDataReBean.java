package com.onecity.os.inform.zhibiao.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 返回数据指标数据,子集列表项
 *
 * <AUTHOR>
 * @date 2020/11/3 10:32
 */
@Data
public class IndicatorDataReBean {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 指标id
     */
    private String indicatorId;

    /**
     * 指标项
     */
    private String itemName;

    /**
     * 数值
     */
    private String itemValue;

    private String itemValue1;

    private String itemValue2;

    private String itemValue3;

    private String itemValue4;

    private String itemValue5;

    private String itemValue6;

    /**
     * 数值类型
     */
    private String identify;

    /**
     * 数值单位
     */
    private String itemUnit;

    /**
     * 第二单位
     */
    private String itemUnit2nd;

    /**
     * 排序
     */
    private Integer sequence;

    /**
     * 是否有分割线
     */
    private Integer style;

    /**
     * 是否折行
     */
    private Integer isFold;

    /**
     * 数据期
     */
    private String updateDate;

    /**
     * 是否展示 0：是1：否
     */
    private Integer currentFlag;

    /**
     * 创建人
     */
    private String creater;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 数据对接数据值列表接收
     */
    private List<ItemValues> itemValuesList;
    /**
     * x轴或指标项名称
     */
    private String dataKeyName;

    /**
     * 副值单位
     */
    private String secondaryUnit;
}
