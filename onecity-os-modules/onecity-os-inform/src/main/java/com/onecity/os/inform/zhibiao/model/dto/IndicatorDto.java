package com.onecity.os.inform.zhibiao.model.dto;

import lombok.Data;

import java.util.Date;

/**
 * 指标管理出参
 *
 */
@Data
public class IndicatorDto {
    /**
     * 主键
     **/
    private String id;
    
    /**
     * 指标名称
     **/
    private String indicatorName;

    /**
     * 展示顺序
     **/
    private String sequence;

    /**
     * 展示样式
     **/
    private String indicatorExhibitType;

    /**
     * 更新周期
     */
    private String updateCycle;

    /**
     * 创建人
     **/
    private String creater;

    /**
     * 创建时间
     **/
    private Date createTime;

    /**
     * 更新人
     **/
    private String updater;

    /**
     * 更新时间
     **/
    private Date updateTime;

    /**
     * 是否展示
     */
    private Integer isShow;

    /**
     * 是否展示筛选框
     */
    private Integer isScreen;

    /**
     * 是否展示图例
     */
    private Integer isLegend;

    /**
     * 数据更新方式1-手动填报2数据对接
     */
    private Integer dataUpdateMode;

    /**
     * 数据配置id
     */
    private String dataConfigId;
}
