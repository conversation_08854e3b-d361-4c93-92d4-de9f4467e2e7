package com.onecity.os.inform.zhibiao.service;

import com.onecity.os.inform.zhibiao.entity.DataConfig;

import java.util.List;

public interface DataConfigService {


    /**
     * 新增数据配置
     * @param dc
     * @return
     */
    String addDataConfig(DataConfig dc);

    /**
     * 修改数据配置
     * @param dc
     * @return
     */
    String updateDataConfig(DataConfig dc);

    /**
     * 批量新增数据配置
     * @param dataConfigList
     * @return
     */
    String addOrUpdateDataConfigList(List<DataConfig> dataConfigList);

    /**
     * 根据id获取数据配置
     * @param id
     * @return
     */
    DataConfig getDataConfigById(String id);

    /**
     * 根据指标id获取数据配置列表
     * @param id
     * @return
     */
    List<DataConfig> getDataConfigByIndicatorId(String id);

    /**
     * 根据id删除数据配置
     * @param dcId
     * @return
     */
    void deleteDataConfigById(String dcId);
}
