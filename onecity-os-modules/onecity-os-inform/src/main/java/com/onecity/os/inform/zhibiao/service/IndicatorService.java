package com.onecity.os.inform.zhibiao.service;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.inform.zhibiao.model.dto.*;
import com.onecity.os.inform.zhibiao.model.vo.*;
import com.github.pagehelper.PageInfo;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicatorTianbao;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicatorDataTianbao;
import org.springframework.web.multipart.MultipartFile;


import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 *
 */
public interface IndicatorService {

    /**
     * 1 指标管理-指标树
     *
     * @param tabId
     * @param tj
     * @return
     */
    List<IndicatorTreeDto> getTargetTreeList(String tabId, String tj);

    /**
     * 根据指标id,查找指标名称和类型
     *
     * @param indicatorId
     * @return
     */
    IndicatorDataExcel getIndicatorNameSourceNameById(String indicatorId);

    /**
     * 2 指标管理-指标列表查询
     *
     * @param tabId
     * @param tj
     * @return
     */
    List<IndicatorDto> getTargetList(String tabId, String tj);

    /**
     * 3 指标管理-指标删除
     *
     * @param ids
     * @param userName
     * @param tj
     */
    void deleteTargets(String ids, String userName, String tj);

    /**
     * 根据指标id,查找是否存在该指标
     *
     * @param id
     * @param tj
     * @return
     */
    GeneralIndicatorTianbao getIndicatorById(String id, String tj);

    /**
     * 4 指标管理-新增指标
     *
     * @param tj
     * @param vos
     * @param parentIndicator
     */
    BaseResult<?> addIndicator(String tj, List<IndicatorVo> vos, GeneralIndicatorTianbao parentIndicator) throws Exception;

    /**
     * 5 指标管理-编辑指标
     *
     * @param tj
     * @param vos
     * @param parentIndicator
     * @param pageNo
     * @param pageSize
     * @return
     */
    int editIndicator(String tj, List<IndicatorVo> vos, GeneralIndicatorTianbao parentIndicator, Integer pageNo, Integer pageSize) throws Exception;

    /**
     * 1 指标数据管理-查询指标数据列表
     *
     * @param id
     * @param tj
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageInfo<IndicatorDataReBean> getTargetDetailList(String id, String tj, Integer pageNo, Integer pageSize);

    /**
     * 2 指标数据管理-指标数据批量修改和新增
     *
     * @param dataVo
     * @param tj
     * @param receivedDataIds
     * @param userName
     * @param pageNo
     * @param pageSize
     */
    void saveOrUpdateIndicatorData(IndicatorDataDto dataVo, String tj, List<Long> receivedDataIds, String userName, Integer pageNo, Integer pageSize) throws Exception;

    /**
     * 根据ids,删除指标数据
     *
     * @param ids
     * @param userName
     * @param tj
     */
    void deleteIndicatorDataByIds(String ids, String userName, String tj) throws Exception;

    /**
     * 批量导入指标
     *
     * @param indicatorList
     * @param sourceSimpleName
     */
    void insertIndicatorList(List<GeneralIndicatorTianbao> indicatorList, String sourceSimpleName);

    /**
     * 批量导入指标数据
     *
     * @param indicatorDataList
     * @param sourceSimpleName
     */
    void insertIndicatorDataList(List<GeneralIndicatorDataTianbao> indicatorDataList, String sourceSimpleName);


    /**
     * 批量导入指标-多线程
     *
     * @param indicatorList
     * @param sourceSimpleName
     */
    void insertIndicatorListMulti(List<GeneralIndicatorTianbao> indicatorList, String sourceSimpleName);

    /**
     * 批量导入指标数据-多线程
     *
     * @param indicatorDataList
     * @param sourceSimpleName
     */
    void insertIndicatorDataListMulti(List<GeneralIndicatorDataTianbao> indicatorDataList, String sourceSimpleName);

    /**
     * 根据指标id,判断该指标是否是二级
     *
     * @param vo
     * @return
     */
    Integer checkIsSecondIndicatorById(MoveIndicatorVo vo);

    /**
     * 移动指标分组
     *
     * @param vo
     */
    void moveIndicator(MoveIndicatorVo vo) throws Exception;

    /**
     * 根据指标id,查找指标数据信息
     *
     * @param indicatorId
     * @param indicator
     * @return
     */
    List<?> getExportIndicatorDataXlsByIndicatorId(String indicatorId, IndicatorDataExcel indicator);

    /**
     * 指标数据管理-多线程导入指标管理数据(在使用)
     *
     * @param file
     * @param params
     * @param indicatorId
     * @return
     */
//    BaseResult<?> importIndicatorDataExcel(MultipartFile file, ImportParams params, String indicatorId) throws IOException, Exception;

    /**
     * 指标数据管理-多线程导入指标管理数据(在使用)
     *
     * @param file
     * @param indicatorId
     * @return
     */
    BaseResult<?> importIndicatorDataExcelRuoYi(MultipartFile file, String indicatorId) throws IOException, Exception;

    void deleteTargetsByParentId(String tj,String parentId);

}
















