package com.onecity.os.inform.zhibiao.service;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicatorDataTitle;
import com.onecity.os.inform.zhibiao.entity.vo.IndicatorTitleVo;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2024/1/22 16:02
 */
public interface IndicatorTitleService {

    /**
     * 指标数据标头 新增/编辑
     *
     * @param titles
     */
    BaseResult<List<GeneralIndicatorDataTitle>> saveData(List<GeneralIndicatorDataTitle> titles);


    /**
     * 指标数据标头 查询
     *
     * @param indicatorId
     */
    List<GeneralIndicatorDataTitle> queryById(String indicatorId);

    /**
     *
     * @param titleVo
     * @return
     */
    BaseResult<?> saveUnit(IndicatorTitleVo titleVo);
}
