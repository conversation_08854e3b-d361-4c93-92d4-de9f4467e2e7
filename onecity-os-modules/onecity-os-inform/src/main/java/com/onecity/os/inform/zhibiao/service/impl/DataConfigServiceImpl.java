package com.onecity.os.inform.zhibiao.service.impl;


import com.onecity.os.inform.zhibiao.entity.DataConfig;
import com.onecity.os.inform.zhibiao.mapper.DataConfigMapper;
import com.onecity.os.inform.zhibiao.service.DataConfigService;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class DataConfigServiceImpl implements DataConfigService {

    @Resource
    private DataConfigMapper dataConfigMapper;



    @Override
    public String addDataConfig(DataConfig dc) {
        String id = UUID.randomUUID().toString().replace("-", "");
        dc.setId(id);
        //当前用户信息
        LoginUser sysUser = null;
        try {
            sysUser = SecurityUtils.getLoginUser();
            if (sysUser != null) {
                dc.setCreater(sysUser.getUsername());
                dc.setUpdater(sysUser.getUsername());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        Date currentTime = new Date();
        dc.setCreateTime(currentTime);
        dc.setUpdateTime(currentTime);
        dataConfigMapper.addDataConfig(dc);
        return id;
    }

    @Override
    public String updateDataConfig(DataConfig dc) {
        //当前用户信息
        LoginUser sysUser = null;
        try {
            sysUser = SecurityUtils.getLoginUser();
            if (sysUser != null) {
                dc.setUpdater(sysUser.getUsername());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        Date currentTime = new Date();
        dc.setUpdateTime(currentTime);
        dataConfigMapper.updateDataConfig(dc);
        return dc.getId();
    }
    @Override
    public String addOrUpdateDataConfigList(List<DataConfig> dataConfigList) {
        if(dataConfigList.size()>0) {
            DataConfig dc = new DataConfig();
            //当前用户信息
            LoginUser sysUser = null;
            try {
                sysUser = SecurityUtils.getLoginUser();
                if (sysUser != null) {
                    dc.setUpdater(sysUser.getUsername());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            String indicatorId = dataConfigList.get(0).getIndicatorId();
            dc.setUpdateTime(new Date());
            dc.setIndicatorId(indicatorId);
            dataConfigMapper.deleteDataConfigByIndicatorId(dc);
        }
        for(DataConfig dataConfig: dataConfigList){
            if(ObjectUtils.isEmpty(dataConfig.getId())){
                String id = UUID.randomUUID().toString().replace("-", "");
                dataConfig.setId(id);
                //当前用户信息
                LoginUser sysUser = null;
                try {
                    sysUser = SecurityUtils.getLoginUser();
                    if (sysUser != null) {
                        dataConfig.setCreater(sysUser.getUsername());
                        dataConfig.setUpdater(sysUser.getUsername());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                Date currentTime = new Date();
                dataConfig.setCreateTime(currentTime);
                dataConfig.setUpdateTime(currentTime);
                dataConfigMapper.addDataConfig(dataConfig);
            }else {
                //当前用户信息
                LoginUser sysUser = null;
                try {
                    sysUser = SecurityUtils.getLoginUser();
                    if (sysUser != null) {
                        dataConfig.setUpdater(sysUser.getUsername());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                Date currentTime = new Date();
                dataConfig.setUpdateTime(currentTime);
                dataConfigMapper.updateDataConfig(dataConfig);
            }
        }
        return "1";
    }

    @Override
    public DataConfig getDataConfigById(String id) {
        return dataConfigMapper.getDataConfigById(id);
    }

    @Override
    public List<DataConfig> getDataConfigByIndicatorId(String id) {
        return dataConfigMapper.getDataConfigByIndicatorId(id);
    }

    @Override
    public void deleteDataConfigById(String id) {
        DataConfig dataConfig = new DataConfig();
        dataConfig.setId(id);
        //当前用户信息
        LoginUser sysUser = null;
        try {
            sysUser = SecurityUtils.getLoginUser();
            if (sysUser != null) {
                dataConfig.setUpdater(sysUser.getUsername());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        Date currentTime = new Date();
        dataConfig.setUpdateTime(currentTime);
        dataConfigMapper.deleteDataConfigById(dataConfig);

    }
}
