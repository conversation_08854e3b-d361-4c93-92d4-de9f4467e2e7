package com.onecity.os.inform.zhibiao.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.onecity.os.inform.exception.CockpitBusinessException;
import com.onecity.os.common.core.utils.poi.ExcelUtil;

import com.onecity.os.inform.feign.SuperviseFeignService;
import com.onecity.os.inform.service.InformService;
import com.onecity.os.inform.zhibiao.feign.ResponseBean;
import com.onecity.os.inform.zhibiao.feign.ReportFeignService;
import com.onecity.os.inform.zhibiao.service.DataConfigService;
import com.onecity.os.inform.zhibiao.service.IndicatorTitleService;
import com.onecity.os.system.api.RemoteUserService;
import com.onecity.os.system.api.model.LoginUser;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.inform.constant.Constant;


import com.onecity.os.inform.zhibiao.entity.*;
import com.onecity.os.inform.zhibiao.mapper.*;
import com.onecity.os.inform.zhibiao.model.dto.*;
import com.onecity.os.inform.zhibiao.model.vo.*;
import com.onecity.os.inform.zhibiao.service.IndicatorService;
import com.onecity.os.inform.utils.AsyncTaskExecutorUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CountDownLatch;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class IndicatorServiceImpl implements IndicatorService {

    @Resource
    private IndicatorMapper indicatorMapper;


    @Resource
    private GeneralIndicatorDataMapper generalIndicatorDataMapper;

    @Resource
    private DataConfigService dataConfigService;

    @Resource
    private RemoteUserService remoteUserService;

    @Resource
    private ReportFeignService reportFeignService;

    @Resource
    private SuperviseFeignService superviseFeignService;

    @Resource
    private GeneralIndicatorMapper generalIndicatorMapper;

    @Resource
    private IndicatorTitleService indicatorTitleService;

    @Resource
    private InformService informService;


    @Override
    public List<IndicatorTreeDto> getTargetTreeList(String tabId, String tj) {
//        String tableName = tj + "_general_indicator";
        GeneralIndicator generalIndicator = new GeneralIndicator();
//        generalIndicator = indicatorMapper.getInfoById(tabId, tableName);
//        if (ObjectUtils.isEmpty(generalIndicator)) {
//            return null;
//        }
        generalIndicator.setId("0");
        //查询 厅局名称

        generalIndicator.setIndicatorName("阅批呈报");
        // 普通指标
        List<IndicatorTreeDto> treeList = new ArrayList<>();
        IndicatorTreeDto treeDto = new IndicatorTreeDto();
        treeDto.setId(generalIndicator.getId());
        treeDto.setLabel(generalIndicator.getIndicatorName());
//        treeDto.setParentId(generalIndicator.getParentId());
//        treeDto.setIconUrl(generalIndicator.getIconUrl());
        treeDto.setIndicatorType(1);
//        if (!ObjectUtils.isEmpty(sourceManage)) {
//            treeDto.setSourceId(sourceManage.getSourceSimpleName());
//            treeDto.setSourceName(sourceManage.getSourceName());
//        }
        treeDto.setSourceId("ypcb");
        treeDto.setSourceName("阅批呈报");
        treeDto.setChildren(new ArrayList<>());
        // 查询 tab 下的 指标list数据
        List<GeneralIndicator> dtoList = new ArrayList<>();
        dtoList = indicatorMapper.getByParentId(tabId, tj);
        List<IndicatorTreeDto> treeDataVoList = new ArrayList<IndicatorTreeDto>();
        treeDataVoList = getTreeList(dtoList, tj);
        treeDto.setChildren(treeDataVoList);
        treeList.add(treeDto);
        return treeList;
    }

    /**
     * 指标管理-递归查询下级指标列表
     *
     * @param dtoList
     * @param tj
     * @return
     */
    public List<IndicatorTreeDto> getTreeListUpdated(List<GeneralIndicator> dtoList, String tj,List<String> updatedIndicatorIds) {
        List<IndicatorTreeDto> treeDataDtoList = new ArrayList<>();
        for (GeneralIndicator entity : dtoList) {
            IndicatorTreeDto treeDto = new IndicatorTreeDto();
            treeDto.setId(entity.getId());
            treeDto.setLabel(entity.getIndicatorName());
            treeDto.setParentId(entity.getParentId());
            treeDto.setIconUrl(entity.getIconUrl());
            treeDto.setIndicatorType(entity.getIndicatorType());
            treeDto.setSequence(entity.getSequence().toString());
            treeDto.setGroupType(entity.getGroupType());
            treeDto.setGroupUrl(entity.getGroupUrl());
            treeDto.setIsShow(entity.getIsShow());
            treeDto.setIsScreen(entity.getIsScreen());
            treeDto.setIsLegend(entity.getIsLegend());
            treeDto.setDataUpdateMode(entity.getDataUpdateMode());
            treeDto.setDataConfigId(entity.getDataConfigId());
            if(updatedIndicatorIds.contains(entity.getId())){
                treeDto.setIsUpdate(1);
            }else {
                treeDto.setIsUpdate(0);
            }
            treeDto.setChildren(new ArrayList<>());
            //下级指标
            List<GeneralIndicator> erjiList = indicatorMapper.getByParentId(entity.getId(), tj);
            if (erjiList.size() > 0) {
                treeDto.getChildren().addAll(getTreeListUpdated(erjiList, tj,updatedIndicatorIds));
            }
            treeDataDtoList.add(treeDto);
        }
        return treeDataDtoList;
    }

    /**
     * 指标管理-递归查询下级指标列表
     *
     * @param dtoList
     * @param tj
     * @return
     */
    public List<IndicatorTreeDto> getTreeList(List<GeneralIndicator> dtoList, String tj) {
        List<IndicatorTreeDto> treeDataDtoList = new ArrayList<>();
        for (GeneralIndicator entity : dtoList) {
//            log.info("getTargetTreeList*****设置IndicatorTreeDto，entity："+entity.toString());
            IndicatorTreeDto treeDto = new IndicatorTreeDto();
            treeDto.setId(entity.getId());
            treeDto.setLabel(entity.getIndicatorName());
            treeDto.setParentId(entity.getParentId());
            treeDto.setIconUrl(entity.getIconUrl());
            treeDto.setIndicatorType(entity.getIndicatorType());
            treeDto.setSequence(entity.getSequence().toString());
            treeDto.setGroupType(entity.getGroupType());
            treeDto.setGroupUrl(entity.getGroupUrl());
            treeDto.setIsShow(entity.getIsShow());
            treeDto.setIsScreen(entity.getIsScreen());
            treeDto.setIsLegend(entity.getIsLegend());
            treeDto.setChildren(new ArrayList<>());
            treeDto.setDataUpdateMode(entity.getDataUpdateMode());
            treeDto.setDataConfigId(entity.getDataConfigId());
            treeDto.setIndicatorExhibitType(entity.getIndicatorExhibitType());
//            log.info("getTargetTreeList*****设置IndicatorTreeDto，treeDto："+treeDto.toString());
            //下级指标
            List<GeneralIndicator> erjiList = indicatorMapper.getByParentId(entity.getId(), tj);
            if (erjiList.size() > 0) {
                treeDto.getChildren().addAll(getTreeList(erjiList, tj));
            }
            treeDataDtoList.add(treeDto);
        }
        return treeDataDtoList;
    }

    @Override
    public List<IndicatorDto> getTargetList(String id, String tj) {
        // 根据父指标id,查找子集指标数据
        List<IndicatorDto>  generalIndicatorList = indicatorMapper.getIndicatorDataByParentId(id, tj);
        generalIndicatorList.forEach(dto -> {
            //创建人
            if (StringUtils.isNotEmpty(dto.getCreater())) {
                com.onecity.os.common.core.domain.BaseResult<String> nickName = remoteUserService.getNickNameByName(dto.getCreater());
                dto.setCreater(nickName.getData());
            }
            //更新人匹配
            if (StringUtils.isNotEmpty(dto.getUpdater())) {
                com.onecity.os.common.core.domain.BaseResult<String> nickName = remoteUserService.getNickNameByName(dto.getUpdater());
                dto.setUpdater(nickName.getData());
            }
        });
        return generalIndicatorList;
    }

    @Override
    public void deleteTargets(String ids, String userName, String tj) {
//        String tableName = tj + "_general_indicator";
//        for(String id:ids.split(",")) {
//            IndicatorDataExcel generalIndicatorTianbao = generalIndicatorMapper.getIndicatorNameSourceNameById(id);
//            IndicatorUpdateRecord record = new IndicatorUpdateRecord();
//            record.setIndicatorId(id);
//            record.setCreateTime(new Date());
//            record.setIndicatorName(generalIndicatorTianbao.getIndicatorName());
//            record.setDataFlag(0);
//            indicatorUpdateRecordMapper.insertIndicatorUpdateRecord(record);
//        }
        indicatorMapper.deleteTargets(Arrays.asList(ids.split(",")), userName, tj);
    }

    @Override
    public GeneralIndicatorTianbao getIndicatorById(String id, String tj) {
        return indicatorMapper.getInfoById(id, tj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResult<?> addIndicator(String tj, List<IndicatorVo> vos, GeneralIndicatorTianbao parentIndicator) throws Exception {

        String sourceName = "阅批呈报";
        GeneralIndicatorTianbao indicator = new GeneralIndicatorTianbao();
        Date currentTime = new Date();
        //当前用户信息
        LoginUser sysUser = null;
        try {
            sysUser = (LoginUser) SecurityUtils.getLoginUser();
            if (sysUser != null) {
                indicator.setCreater(sysUser.getUsername());
                indicator.setUpdater(sysUser.getUsername());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        indicator.setParentId(parentIndicator.getId());
        //indicator.setParentName(parentIndicator.getParentName());
        if (!CollectionUtils.isEmpty(vos)) {
            for (IndicatorVo vo1 : vos) {
                String id = UUID.randomUUID().toString().replace("-", "");
                indicator.setId(id);
                indicator.setIndicatorName(vo1.getIndicatorName());
                indicator.setSequence(vo1.getSequence());
                indicator.setIconUrl(vo1.getIconUrl());
                if (StringUtils.isBlank(vo1.getIndicatorExhibitType())) {
                    indicator.setIndicatorExhibitType("word");
                } else {
                    indicator.setIndicatorExhibitType(vo1.getIndicatorExhibitType());
                }
                if (StringUtils.isBlank(indicator.getParentId())) {
                    indicator.setParentId("0");
                }
                indicator.setIndicatorType(vo1.getIndicatorType());
                indicator.setUpdateCycle(vo1.getUpdateCycle());
                indicator.setGroupType(vo1.getGroupType());
                indicator.setGroupUrl(vo1.getGroupUrl());
                // 设置时间
                indicator.setCreateTime(currentTime);
                indicator.setUpdateTime(currentTime);
                indicator.setIsShow(1);
                indicator.setDataUpdateMode(vo1.getDataUpdateMode());
                indicator.setDataConfigId(vo1.getDataConfigId());
                indicatorMapper.addIndicator(indicator, tj, sourceName);
            }
        }
        return BaseResult.ok("添加成功");
    }

    @Override
    @Transactional
    public int editIndicator(String tj, List<IndicatorVo> vos, GeneralIndicatorTianbao parentIndicator, Integer pageNo, Integer pageSize) throws Exception {
        try {
            GeneralIndicatorTianbao indicator;
            Date currentTime = new Date();
            String userName = null;
            //当前用户信息
            LoginUser sysUser = null;
            try {
                sysUser = (LoginUser) SecurityUtils.getLoginUser();
                if (sysUser != null) {
                    userName = sysUser.getUsername();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            // 编辑指标分组
            if (1 == vos.size() && 1 == vos.get(0).getIndicatorType()) {
                // 根据指标id,查找该条指标信息
                indicator = indicatorMapper.getInfoById(vos.get(0).getId(), tj);
                if (ObjectUtils.isEmpty(indicator)) {
                    return 1;
                }
                indicator.setParentId(vos.get(0).getParentId());
                indicator.setIndicatorName(vos.get(0).getIndicatorName());
                indicator.setSequence(vos.get(0).getSequence());
                indicator.setIconUrl(vos.get(0).getIconUrl());
                if (StringUtils.isBlank(vos.get(0).getIndicatorExhibitType())) {
                    indicator.setIndicatorExhibitType("word");
                } else {
                    indicator.setIndicatorExhibitType(vos.get(0).getIndicatorExhibitType());
                }
                indicator.setUpdater(userName);
                indicator.setUpdateTime(currentTime);
                indicator.setGroupType(vos.get(0).getGroupType());
                indicator.setGroupUrl(vos.get(0).getGroupUrl());
                indicator.setIsShow(vos.get(0).getIsShow());
                indicator.setIsScreen(vos.get(0).getIsScreen());
                indicator.setIsLegend(vos.get(0).getIsLegend());
                indicator.setDataConfigId(vos.get(0).getDataConfigId());
                indicator.setDataUpdateMode(vos.get(0).getDataUpdateMode());
                indicatorMapper.updateIndicator(indicator, tj);
            } else {
                // 前端传来的指标ids
                List<String> receivedIds = new ArrayList<>();
                if (!CollectionUtils.isEmpty(vos)) {
                    // 获取指标id
                    for (IndicatorVo vo : vos) {
                        receivedIds.add(vo.getId());
                    }
                }
                // 编辑指标,先跟据父指标id,查询出已经存在的指标
                List<String> existIds;
                if (!ObjectUtils.isEmpty(pageNo) && !ObjectUtils.isEmpty(pageSize)) {
                    PageHelper.startPage(pageNo,pageSize);
                    existIds = indicatorMapper.getIdsByParentIdPage(parentIndicator.getId(), tj);
                    PageHelper.clearPage();
                } else {
                    existIds = indicatorMapper.getIdsByParentId(parentIndicator.getId(), tj);
                }
                // 获取要删除的指标
                existIds.removeAll(receivedIds);
                if (!CollectionUtils.isEmpty(existIds)) {
                    indicatorMapper.deleteTargets(existIds, userName, tj);
                }
                String sourceName = "阅批呈报";
                // 将传来的指标,进行新增/更新指标
                if (!CollectionUtils.isEmpty(vos)) {
                    for (IndicatorVo vo1 : vos) {
                        indicator = new GeneralIndicatorTianbao();
                        // 传来的,id为空
                        if (StringUtils.isBlank(vo1.getId())) {
                            String id = UUID.randomUUID().toString().replace("-", "");
                            indicator.setId(id);
                        } else {
                            // 传来的id,不为空
                            indicator.setId(vo1.getId());
                        }
                        indicator.setIndicatorName(vo1.getIndicatorName());
                        indicator.setSequence(vo1.getSequence());
                        indicator.setIconUrl(vo1.getIconUrl());
                        if (StringUtils.isBlank(vo1.getIndicatorExhibitType())) {
                            indicator.setIndicatorExhibitType("word");
                        } else {
                            indicator.setIndicatorExhibitType(vo1.getIndicatorExhibitType());
                        }
                        indicator.setParentId(parentIndicator.getId());
                        indicator.setIndicatorType(vo1.getIndicatorType());
                        indicator.setUpdateCycle(vo1.getUpdateCycle());
                        indicator.setIsShow(vo1.getIsShow());
                        indicator.setIsScreen(vo1.getIsScreen());
                        indicator.setIsLegend(vo1.getIsLegend());
                        indicator.setDataUpdateMode(vo1.getDataUpdateMode());
                        indicator.setDataConfigId(vo1.getDataConfigId());
                        indicator.setUpdateTime(currentTime);
                        indicator.setUpdater(userName);
                        // id为空新增
                        if (StringUtils.isBlank(vo1.getId())) {
                            indicator.setCreater(userName);
                            indicator.setCreateTime(currentTime);
                            if ("月度更新".equals(indicator.getUpdateCycle())) {
                                indicator.setPlanUpdateDate("10");
                            }
                            if ("季度更新".equals(indicator.getUpdateCycle())) {
                                indicator.setPlanUpdateDate("1-15");
                            }
                            if ("半年更新".equals(indicator.getUpdateCycle())) {
                                indicator.setPlanUpdateDate("1-25");
                            }
                            if ("年度更新".equals(indicator.getUpdateCycle())) {
                                indicator.setPlanUpdateDate("1-30");
                            }
                            indicatorMapper.addIndicator(indicator, tj, sourceName);
//                            //指标更新记录
//                            IndicatorUpdateRecord record = new IndicatorUpdateRecord();
//                            record.setIndicatorId(indicator.getId());
//                            record.setCreateTime(new Date());
//                            record.setIndicatorName(indicator.getIndicatorName());
//                            record.setDataFlag(0);
//                            indicatorUpdateRecordMapper.insertIndicatorUpdateRecord(record);
                        } else {
                            // 先跟据id,查出约定更新周期是否为空
                            String planUpdate = indicatorMapper.getPlanUpdateById(indicator.getId());
                            // 为空,设定为默认值
                            if (StringUtils.isEmpty(planUpdate)) {
                                log.info("3.6.5 log for test ");
                                if ("月度更新".equals(indicator.getUpdateCycle())) {
                                    indicator.setPlanUpdateDate("10");
                                }
                                if ("季度更新".equals(indicator.getUpdateCycle())) {
                                    indicator.setPlanUpdateDate("1-15");
                                }
                                if ("半年更新".equals(indicator.getUpdateCycle())) {
                                    indicator.setPlanUpdateDate("1-25");
                                }
                                if ("年度更新".equals(indicator.getUpdateCycle())) {
                                    indicator.setPlanUpdateDate("1-30");
                                }
                            } else {
                                // 不为空,判断更新周期是否改变,改变了,将其设置为默认值
                                if (!vo1.getUpdateCycle().equals(planUpdate)) {
                                    if ("月度更新".equals(indicator.getUpdateCycle())) {
                                        indicator.setPlanUpdateDate("10");
                                    }
                                    if ("季度更新".equals(indicator.getUpdateCycle())) {
                                        indicator.setPlanUpdateDate("1-15");
                                    }
                                    if ("半年更新".equals(indicator.getUpdateCycle())) {
                                        indicator.setPlanUpdateDate("1-25");
                                    }
                                    if ("年度更新".equals(indicator.getUpdateCycle())) {
                                        indicator.setPlanUpdateDate("1-30");
                                    }
                                    if ("日更新".equals(indicator.getUpdateCycle())) {
                                        indicator.setPlanUpdateDate("");
                                    }
                                } else {
                                    indicator.setPlanUpdateDate(planUpdate);
                                }
                            }
                            // id不为空,更新
                            indicatorMapper.updateIndicator(indicator, tj);
                            //获取审核后的指标信息
                            GeneralIndicator generalIndicator = generalIndicatorMapper.getIndicatorInfoById(indicator.getId());
//                            //对比审核前后的指标看是否发生改变,发生改变后记录指标更新
//                            if(ObjectUtil.isNotNull(generalIndicator)&&compareIndicator(indicator,generalIndicator)){
//                                //指标更新记录
//                                IndicatorUpdateRecord record = new IndicatorUpdateRecord();
//                                record.setIndicatorId(indicator.getId());
//                                record.setCreateTime(new Date());
//                                record.setIndicatorName(indicator.getIndicatorName());
//                                record.setDataFlag(0);
//                                indicatorUpdateRecordMapper.insertIndicatorUpdateRecord(record);
//                            }

                            //更新指标后，将指标图表类型 indicatorExhibitType 更新到inform表中
                            informService.updateIndExhibitType(vo1.getId(), vo1.getIndicatorExhibitType());
                        }
                        //新建/更新指标时，指标头数据处理：如果该指标存在指标头数据，则跳过处理，如果该指标不存在指标头数据，则新建
                        if (null != vo1.getTitles()) {
                            //查询现有指标头数据
                            List<GeneralIndicatorDataTitle> titles = vo1.getTitles();
                            String indId = indicator.getId();
                            List<GeneralIndicatorDataTitle> titleList = indicatorTitleService.queryById(indId);
                            if (CollectionUtils.isEmpty(titleList)) {
                                //当现有指标头为空时候才创建
                                titles.forEach(t -> t.setIndicatorId(indId));
                                indicatorTitleService.saveData(titles);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("保存失败");
        }
        return 0;
    }

    //判断指标是否发生更新
    public Boolean compareIndicator(GeneralIndicatorTianbao indicator,GeneralIndicator generalIndicator){
        //默认没有发生变化
        Boolean result = false;
        if(!indicator.getIndicatorExhibitType().equals(generalIndicator.getIndicatorExhibitType())){
            result = true;
        }
        if(!indicator.getIndicatorName().equals(generalIndicator.getIndicatorName())){
            result = true;
        }
        if(!indicator.getUpdateCycle().equals(generalIndicator.getUpdateCycle())){
            result = true;
        }
        if(!indicator.getSequence().equals(generalIndicator.getSequence())){
            result = true;
        }
        if(!indicator.getIsShow().equals(generalIndicator.getIsShow())) {
            result = true;
        }
        if(!indicator.getIsScreen().equals(generalIndicator.getIsScreen())){
            result = true;
        }
        if(!indicator.getIsLegend().equals(generalIndicator.getIsLegend())){
            result = true;
        }
        return result;
    }

    @Override
    public PageInfo<IndicatorDataReBean> getTargetDetailList(String id, String tj, Integer pageNo, Integer pageSize) {
        GeneralIndicator generalIndicator = generalIndicatorMapper.getIndicatorInfoById(id);
        if (generalIndicator.getDataUpdateMode()==1) {
            //查询指标数据
            List<IndicatorDataReBean> dataDtoList = new ArrayList<>();
            PageHelper.startPage(pageNo, pageSize);
            dataDtoList = indicatorMapper.getDataListByIndicatorId(id);
            for (int i = 0; i < dataDtoList.size(); i++) {
                //创建人匹配
                if (StringUtils.isNotEmpty(dataDtoList.get(i).getCreater())) {
                    BaseResult<String> nickName = remoteUserService.getNickNameByName(dataDtoList.get(i).getCreater());
                    dataDtoList.get(i).setCreater(nickName.getData());
                }
                //更新人匹配
                if (StringUtils.isNotEmpty(dataDtoList.get(i).getUpdater())) {
                    BaseResult<String> nickName = remoteUserService.getNickNameByName(dataDtoList.get(i).getUpdater());
                    dataDtoList.get(i).setUpdater(nickName.getData());
                }
            }
            return new PageInfo<>(dataDtoList);
        }else if(generalIndicator.getDataUpdateMode()==2) {
            //如果是数据对接，则请求数据集接口获取数据
            if (!ObjectUtil.isEmpty(generalIndicator.getDataConfigId())) {
                //数据配置id不是空的时，数据集等均不是空的，前端在数据配置弹窗中做了数据配置相关全部的必填项
                List<DataConfig> dataConfigList = dataConfigService.getDataConfigByIndicatorId(generalIndicator.getDataConfigId());
                if (!ObjectUtil.isEmpty(dataConfigList)) {
                    //todo 请求数据集接口获取数据
                    List<IndicatorDataReBean> dataUpdateMode2 = new ArrayList<>();
                    try {
                        //同一个指标配置的数据集是相同的，故只需要取第一个数据配置的数据集id即可
                        Long dataSetId = dataConfigList.get(0).getDataSetId();
                        //请求数据集接口获取数据
                        ResponseBean responseBean = reportFeignService.detailById(dataSetId);
                        log.info("获取到的数据集数据为data：" + responseBean.getData().toString());
                        //获取数据非空时将其处理为前端需要的格式
                        if (!ObjectUtil.isEmpty(responseBean.getData())) {
                            LinkedHashMap dataDto = (LinkedHashMap) responseBean.getData();
                            List<Map> dataArray = (List<Map>) dataDto.get("data");
                            if (dataArray.size() > 0) {
                                for (Map object : dataArray) {
                                    IndicatorDataReBean generalIndicatorDataVO = new IndicatorDataReBean();
                                    List<ItemValues> itemValuesList = new LinkedList<>();
                                    for(DataConfig dc : dataConfigList) {
                                        ItemValues itemValues = new ItemValues();
                                        //设置获取数据对接指标数据的数据key
                                        itemValues.setDataValue(dc.getDataValue());
                                        //设置值是主值还是副值
                                        itemValues.setIsMaster(dc.getIsMasterValue());
                                        //设置值的名称
                                        itemValues.setItemValueName(dc.getDataValueName());
                                        //设置值的具体数据
                                        if(null != object.get(dc.getDataKey())) {
                                            itemValues.setItemValueValue(object.get(dc.getDataValue()).toString());
                                        }
                                        //设置x轴的值
                                        if(null != object.get(dc.getDataValue())) {
                                            generalIndicatorDataVO.setItemName(object.get(dc.getDataKey()).toString());
                                        }
                                        //设置x轴的名称即x轴具体代表什么意思
                                        generalIndicatorDataVO.setDataKeyName(dc.getDataKeyName());
                                        //设置主值单位
                                        generalIndicatorDataVO.setItemUnit(dc.getDataUnit());
                                        //设置副值单位
                                        generalIndicatorDataVO.setSecondaryUnit(dc.getSecondaryUnit());
                                        itemValuesList.add(itemValues);
                                    }
                                    generalIndicatorDataVO.setItemValuesList(itemValuesList);
                                    dataUpdateMode2.add(generalIndicatorDataVO);
                                }
                            }
                        }
                        PageHelper.startPage(1,Integer.MAX_VALUE);
                        return new PageInfo<>(dataUpdateMode2);
                    } catch (Exception e) {
                        log.error("请求数据集接口获取数据异常！" + e.getMessage());
                    }
                }
            }
        }
        return new PageInfo<>(new ArrayList<>());
    }

    @Override
    @Transactional
    public void saveOrUpdateIndicatorData(IndicatorDataDto dataVo, String tj, List<Long> receivedDataIds, String userName, Integer pageNo, Integer pageSize) throws Exception {
//        String tableName = tj + "_general_indicator";
//        String dataTableName = tj + "_general_indicator_data";
        // 获取当前时间
        Date currentTime = new Date();
        //修改 指标 更新日期及数据来源
        try {
            dataVo.setUpdater(userName);
            indicatorMapper.updateIndicatorSourceAndDate(dataVo, tj);

            //增加指标修改记录
            GeneralIndicatorTianbao indicator = indicatorMapper.getInfoById(dataVo.getTreeId(), tj);
//            IndicatorUpdateRecord record = new IndicatorUpdateRecord();
//            record.setIndicatorId(dataVo.getTreeId());
//            record.setCreateTime(new Date());
//            if (indicator!=null){
//                record.setIndicatorName(indicator.getIndicatorName());
//            }
//            record.setDataFlag(1);
//            indicatorUpdateRecordMapper.insertIndicatorUpdateRecord(record);
            // 根据指标id,查找已经存在的指标数据
            List<Long> existDataIds;
            if (!ObjectUtils.isEmpty(pageNo) && !ObjectUtils.isEmpty(pageSize)) {
//                IPage iPage = new Page(pageNo, pageSize);
//                IPage<Long> pageList = indicatorMapper.getDataIdsByIndicatorIdPage(iPage, dataVo.getTreeId());
//                existDataIds = new ArrayList<>(pageList.getRecords());
                PageHelper.startPage(pageNo,pageSize);
                existDataIds = indicatorMapper.getDataIdsByIndicatorIdPage(dataVo.getTreeId());
                PageHelper.clearPage();
            } else {
                existDataIds = indicatorMapper.getDataIdsByIndicatorId(dataVo.getTreeId());
            }
            // 获取要删除的指标数据
            existDataIds.removeAll(receivedDataIds);
            if (!CollectionUtils.isEmpty(existDataIds)) {
                indicatorMapper.deleteIndicatorDtaByIds(existDataIds, userName);
            }
            // 新增/修改 指标数据
            for (IndicatorDataReBean dataBean : dataVo.getData()) {
                GeneralIndicatorDataTianbao data = new GeneralIndicatorDataTianbao();
                BeanUtils.copyProperties(dataBean, data);
                if (ObjectUtils.isEmpty(data.getId()) || 0 == data.getId()) {
                    data.setIndicatorId(dataVo.getTreeId());
                    data.setCreater(userName);
                    data.setCreateTime(currentTime);
                    data.setUpdater(userName);
                    data.setUpdateTime(currentTime);
                    indicatorMapper.insertIndicatorData(data);
                } else {
                    data.setUpdater(userName);
                    data.setUpdateTime(currentTime);
                    indicatorMapper.updateIndicatorDataById(data);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("编辑指标数据时,新增或更新数据失败");
        }
    }

    @Override
    public void deleteIndicatorDataByIds(String ids, String userName, String tj) throws Exception {
//        String dataTableName = tj + "_general_indicator_data";
        try {
            if(ids.split(",").length>0) {
                //获取删除指标数据的指标id
                String indicatorId = indicatorMapper.getIndicatorIdByDataId(ids.split(",")[0]);
                //增加指标修改记录
                GeneralIndicatorTianbao indicator = indicatorMapper.getInfoById(indicatorId, tj);
//                IndicatorUpdateRecord record = new IndicatorUpdateRecord();
//                record.setIndicatorId(indicatorId);
//                record.setCreateTime(new Date());
//                if (indicator != null) {
//                    record.setIndicatorName(indicator.getIndicatorName());
//                }
//                record.setDataFlag(1);
//                indicatorUpdateRecordMapper.insertIndicatorUpdateRecord(record);
            }
            indicatorMapper.deleteIndicatorDataByIds(ids.split(","), userName);
        } catch (Exception e) {
            throw new Exception("根据指标id,批量删除指标数据失败");
        }
    }


    @Override
    public void insertIndicatorList(List<GeneralIndicatorTianbao> indicatorList, String sourceSimpleName) {
        String tableName = sourceSimpleName + Constant.GENERAL_INDICATOR_TALBE_SUFFIX;
        //每次插入500条
        int groupSize = 500;
        int indicatorListSize = indicatorList.size();
        int groupNo = indicatorListSize / groupSize;
        if (indicatorListSize <= groupSize) {
            indicatorMapper.insertIndicatorBatch(indicatorList, tableName);
        } else {
            List<GeneralIndicatorTianbao> subList = null;
            for (int i = 0; i < groupNo; i++) {
                subList = indicatorList.subList(0, groupSize);
                indicatorMapper.insertIndicatorBatch(subList, tableName);
                indicatorList.subList(0, groupSize).clear();
            }
            if (indicatorList.size() > 0) {
                indicatorMapper.insertIndicatorBatch(indicatorList, tableName);
            }
        }
    }

    @Override
    public void insertIndicatorDataList(List<GeneralIndicatorDataTianbao> indicatorDataList, String sourceSimpleName) {
        String dataTableName = sourceSimpleName + Constant.GENERAL_INDICATOR_DATA_TALBE_SUFFIX;
        //每次插入500条
        int groupSize = 500;
        int indicatorListSize = indicatorDataList.size();
        int groupNo = indicatorListSize / groupSize;
        if (indicatorListSize <= groupSize) {
            indicatorMapper.insertIndicatorDataBatch(indicatorDataList, dataTableName);
        } else {
            List<GeneralIndicatorDataTianbao> subList = null;
            for (int i = 0; i < groupNo; i++) {
                subList = indicatorDataList.subList(0, groupSize);
                indicatorMapper.insertIndicatorDataBatch(subList, dataTableName);
                indicatorDataList.subList(0, groupSize).clear();
            }
            if (indicatorDataList.size() > 0) {
                indicatorMapper.insertIndicatorDataBatch(indicatorDataList, dataTableName);
            }
        }
    }

//    @Override
//    public List<GeneralIndicatorVO> listExportIndicator(GeneralIndicatorReqVO reqVO) {
//        String sourceSimpleName = reqVO.getSourceSimpleName();
//        String tableName = sourceSimpleName + Constant.GENERAL_INDICATOR_TALBE_SUFFIX;
//        String dataTableName = sourceSimpleName + Constant.GENERAL_INDICATOR_DATA_TALBE_SUFFIX;
//        reqVO.setTableName(tableName);
//        reqVO.setDataTableName(dataTableName);
//        List<GeneralIndicatorVO> list = indicatorMapper.selectExportIndicator(reqVO);
//        return list;
//    }


    /**
     * 将List按每组n分成多份
     *
     * @param source
     * @param n
     * @return
     */
    private <T> List<List<T>> averageAssign(List<T> source, int n) {
        if (source == null || source.size() == 0 || n < 1) {
            return null;
        }
        List<List<T>> result = new ArrayList<>();
        int arrSize = source.size() % n == 0 ? source.size() / n : source.size() / n + 1;
        for (int i = 0; i < arrSize; i++) {
            List<T> sub = new ArrayList<>();
            for (int j = i * n; j <= n * (i + 1) - 1; j++) {
                if (j <= source.size() - 1) {
                    sub.add(source.get(j));
                }
            }
            result.add(sub);
        }
        return result;
    }


    @Override
    public void insertIndicatorListMulti(List<GeneralIndicatorTianbao> indicatorList, String sourceSimpleName) {
        String tableName = sourceSimpleName + Constant.GENERAL_INDICATOR_TALBE_SUFFIX;
        int size = 200;
        List<List<GeneralIndicatorTianbao>> lists = averageAssign(indicatorList, size);
        if (lists != null) {
            CountDownLatch latch = new CountDownLatch(lists.size());
            for (List<GeneralIndicatorTianbao> indicators : lists) {
                AsyncTaskExecutorUtil.execute(new Runnable() {
                    @Override
                    public void run() {
                        indicatorMapper.insertIndicatorBatch(indicators, tableName);
                        latch.countDown();
                        log.info("线程执行完毕，剩余" + latch.getCount());

                    }
                });
            }
            try {
                latch.await();
                log.info("多线程执行完毕,切换到主线程");
            } catch (InterruptedException e) {
                log.error("计数器报错", e);
                throw new CockpitBusinessException("计数器报错", "500");
            }
        }

    }


    @Override
    public void insertIndicatorDataListMulti(List<GeneralIndicatorDataTianbao> indicatorDataList, String sourceSimpleName) {
        String dataTableName = sourceSimpleName + Constant.GENERAL_INDICATOR_DATA_TALBE_SUFFIX;
        int size = 200;
        List<List<GeneralIndicatorDataTianbao>> dataLists = averageAssign(indicatorDataList, size);
        if (dataLists != null) {
            CountDownLatch latch = new CountDownLatch(dataLists.size());
            for (List<GeneralIndicatorDataTianbao> indicatorDatas : dataLists) {
                AsyncTaskExecutorUtil.execute(new Runnable() {
                    @Override
                    public void run() {
                        indicatorMapper.insertIndicatorDataBatch(indicatorDatas, dataTableName);
                        latch.countDown();
                        log.info("线程执行完毕，剩余" + latch.getCount());

                    }
                });
            }
            try {
                latch.await();
                log.info("多线程执行完毕,切换到主线程");
            } catch (InterruptedException e) {
                log.error("计数器报错", e);
                throw new CockpitBusinessException("计数器报错", "500");
            }
        }
    }

    @Override
    public Integer checkIsSecondIndicatorById(MoveIndicatorVo vo) {
//        // 先跟据数据来源,判断该厅局是否在管理模块中添加过
//        Long id = indicatorMapper.getSourceMangeIdBySourceId(vo.getSourceId());
//        if (ObjectUtils.isEmpty(id)) {
//            return 1;
//        }
        // 根据id,查找父id
        String parentId1 = indicatorMapper.getParentIdById(vo.getId());
        if (StringUtils.isEmpty(parentId1)) {
            return 2;
        }
        // 根据parentId1,查找上级
        GeneralIndicatorTianbao generalIndicator = indicatorMapper.getInfoById(parentId1, vo.getSourceId());
        if (ObjectUtils.isEmpty(generalIndicator)) {
            return 2;
        }
        // 根据parentId1,查找上第一级
        GeneralIndicatorTianbao generalIndicator2 = indicatorMapper.getInfoById(generalIndicator.getParentId(), vo.getSourceId());
        if (ObjectUtils.isEmpty(generalIndicator2)) {
            return 2;
        }
        // 是第一级指标,返回true,不是的话返回false
        if (!"0".equals(generalIndicator2.getParentId())) {
            return 2;
        }
        return 0;
    }

    @Override
    public void moveIndicator(MoveIndicatorVo vo) throws Exception {
        try {
            //当前用户信息
            String userName = "";
            LoginUser sysUser = (LoginUser) SecurityUtils.getLoginUser();
            if (sysUser != null) {
                userName = sysUser.getUsername();
            }
            indicatorMapper.MoveIndicatorVo(vo, userName);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("调整分组失败");
        }
    }







    /**
     * 根据父指标ids,查找所有子集指标
     *
     * @param indicatorParentIds
     * @param allIndicatorIds
     * @return
     */
    private List<String> getIndicatorIdsByParentIds(List<String> indicatorParentIds, List<String> allIndicatorIds) {
        if (!CollectionUtils.isEmpty(indicatorParentIds)) {
            List<String> indicatorIds = generalIndicatorMapper.getIndicatorIdsByParentIds(indicatorParentIds);
            if (!CollectionUtils.isEmpty(indicatorIds)) {
                allIndicatorIds.addAll(indicatorIds);
                // 循环查询
                getIndicatorIdsByParentIds(indicatorIds, allIndicatorIds);
            }
        }
        return allIndicatorIds;
    }


    public IndicatorServiceImpl() {
        super();
    }

    @Override
    public IndicatorDataExcel getIndicatorNameSourceNameById(String indicatorId) {
        return generalIndicatorMapper.getIndicatorNameSourceNameById(indicatorId);
    }

    @Override
    public List<?> getExportIndicatorDataXlsByIndicatorId(String indicatorId, IndicatorDataExcel indicator) {
        if (!ObjectUtils.isEmpty(indicator)) {
            // 根据指标id,查找指标数据
            List<IndicatorDataExcel> dtos = generalIndicatorDataMapper.getExportIndicatorDataXlsByIndicatorId(indicatorId);
            if (!CollectionUtils.isEmpty(dtos)) {
                if ("word".equals(indicator.getType())) {
                    return dtos;
                }
                if (!"word".equals(indicator.getType())) {
                    List<IndicatorDataChartExcel> chartExcels = new ArrayList<>();
                    IndicatorDataChartExcel chartExcel;
                    for (IndicatorDataExcel dto : dtos) {
                        chartExcel = new IndicatorDataChartExcel();
                        BeanUtils.copyProperties(dto, chartExcel);
                        chartExcels.add(chartExcel);
                    }
                    return chartExcels;
                }
            }
        }
        return new ArrayList<>();
    }

    @Override
    public BaseResult<?> importIndicatorDataExcelRuoYi(MultipartFile file, String indicatorId) throws Exception {
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            ExcelUtil<IndicatorDataExcel> indicatorDataExcelExcelUtil = new ExcelUtil<IndicatorDataExcel>(IndicatorDataExcel.class);
            List<IndicatorDataExcel> listDatas = indicatorDataExcelExcelUtil.importExcel(inputStream,3);
//            List<IndicatorDataExcel> listDatas = ExcelImportUtil.importExcel(file.getInputStream(), IndicatorDataExcel.class, params);
            // 根据指标id,查找指标展示类型
            String type = generalIndicatorMapper.getIndicatorExhibitTypeByIdTianbao(indicatorId);
            if (StringUtils.isEmpty(type)) {
                return BaseResult.fail("指标展示方式为空,请先检查指标,再导入");
            }
            // 判断文件中的必填项,填写的内容格式
            for (int i = 0; i < listDatas.size(); i++) {
                if (StringUtils.isEmpty(listDatas.get(i).getItemName())) {
                    return BaseResult.fail("第" + (i + 1) + "行指标项为空，请填写完整再导入");
                }
                if (StringUtils.isEmpty(listDatas.get(i).getItemValue())) {
                    return BaseResult.fail("第" + (i + 1) + "行数值为空，请填写完整再导入");
                }
                if (ObjectUtils.isEmpty(listDatas.get(i).getSequence())) {
                    return BaseResult.fail("第" + (i + 1) + "排序为空，请填写完整再导入");
                }
                // 是否展示 0是   1否
                if (ObjectUtils.isEmpty(listDatas.get(i).getCurrentFlag())) {
                    listDatas.get(i).setCurrentFlag(0);
                }
                // 文本类型的指标,是否有分割线,是否折行,默认为否
                if ("word".equals(type)) {
                    if (ObjectUtils.isEmpty(listDatas.get(i).getStyle())) {
                        listDatas.get(i).setStyle(0);
                    }
                    if (ObjectUtils.isEmpty(listDatas.get(i).getIsFold())) {
                        listDatas.get(i).setIsFold(0);
                    }
                }
            }
            String loginName = null;
            LoginUser sysUser = (LoginUser) SecurityUtils.getLoginUser();
            if (ObjectUtil.isNotEmpty(sysUser)) {
                loginName = sysUser.getUsername();
            }
            // 将原来的指标数据删除,以excel表中的为准
            generalIndicatorDataMapper.deleteDataByIndicatorId(indicatorId);
            // 根据指标id,查找指标数据项名
            List<String> dataExistNames = generalIndicatorDataMapper.getIndicatorDataNameListByIndicatorId(indicatorId);
            GeneralIndicatorData generalIndicatorData;
            Date date = new Date();
            // 增加或修改
            for (int i = 0; i < listDatas.size(); i++) {
                IndicatorDataExcel dataExcel = listDatas.get(i);
                generalIndicatorData = new GeneralIndicatorData();
                BeanUtils.copyProperties(dataExcel, generalIndicatorData);
                generalIndicatorData.setIndicatorId(indicatorId);
                // 文件中的指标项,如果数据库中没有,则增加
                if (!dataExistNames.contains(dataExcel.getItemName())) {
                    generalIndicatorData.setCreater(loginName);
                    generalIndicatorData.setCreateTime(date);
                    generalIndicatorData.setUpdater(loginName);
                    generalIndicatorData.setUpdateTime(date);
                    generalIndicatorDataMapper.insertTianBaoIndicatorData(generalIndicatorData);
                } else {
                    // 有的话,则进行更新
                    generalIndicatorData.setUpdater(loginName);
                    generalIndicatorData.setUpdateTime(date);
                    generalIndicatorDataMapper.updateByIndicatorIdAndItemName(generalIndicatorData);
                }
            }
            //增加指标修改记录
//            GeneralIndicatorTianbao indicator = indicatorMapper.getInfoOnlyById(indicatorId);
//            IndicatorUpdateRecord record = new IndicatorUpdateRecord();
//            record.setIndicatorId(indicatorId);
//            record.setCreateTime(new Date());
//            if (indicator!=null){
//                record.setIndicatorName(indicator.getIndicatorName());
//            }
//            record.setDataFlag(1);
//            indicatorUpdateRecordMapper.insertIndicatorUpdateRecord(record);
            return BaseResult.ok("文件导入成功");
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("导入失败,请检查后重新导入");
        }finally {
            IOUtils.closeQuietly(inputStream);
        }
    }

    @Override
    public void deleteTargetsByParentId(String tj, String parentId) {
        List<String> existIds = indicatorMapper.getIdsByParentId(parentId, tj);
        // 获取要删除的指标
        //当前用户信息
        String userName = null;
        LoginUser sysUser = null;
        try {
            sysUser = SecurityUtils.getLoginUser();
            if (sysUser != null) {
                userName = sysUser.getUsername();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (!CollectionUtils.isEmpty(existIds)) {
            indicatorMapper.deleteTargets(existIds, userName, tj);
        }
    }


}
























