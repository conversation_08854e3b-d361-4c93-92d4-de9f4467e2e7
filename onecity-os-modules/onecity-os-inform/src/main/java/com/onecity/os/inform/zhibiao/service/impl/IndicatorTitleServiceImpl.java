package com.onecity.os.inform.zhibiao.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.inform.constant.UtilConstants;
import com.onecity.os.inform.zhibiao.entity.GeneralIndicatorDataTitle;
import com.onecity.os.inform.zhibiao.entity.vo.IndicatorTitleVo;
import com.onecity.os.inform.zhibiao.mapper.IndicatorTitleMapper;
import com.onecity.os.inform.zhibiao.service.IndicatorTitleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 指标数据标头
 *
 * @Author: zack
 * @Date: 2024/1/22 16:15
 */
@Slf4j
@Service("indicatorTitleService")
public class IndicatorTitleServiceImpl implements IndicatorTitleService {

    @Resource
    private IndicatorTitleMapper indicatorTitleMapper;

    @Override
    public BaseResult<List<GeneralIndicatorDataTitle>> saveData(List<GeneralIndicatorDataTitle> titles) {

        Date newDate = new Date();
        //组条数据处理
        for (GeneralIndicatorDataTitle title : titles) {
            //item_value字段值处理
            title.setColumnName(UtilConstants.getItemMap(title.getColumnName()));
            switch (title.getOperate()) {
                //新增
                case UtilConstants.Operate.ADD:
                    title.setId(UUID.randomUUID().toString().replace("-", ""));
                    indicatorTitleMapper.insertIndicatorTitle(title);
                    break;
                //删除
                case UtilConstants.Operate.DEL:
                    indicatorTitleMapper.delIndicatorTitle(title.getId());
                    break;
                //更新
                case UtilConstants.Operate.UPDATE:
                    title.setUpdateTime(newDate);
                    indicatorTitleMapper.updateIndicatorTitle(title);
                    break;
                //保持
                case UtilConstants.Operate.KEEP:
                    log.info(" Operate KEEP : {}", JSONObject.toJSONString(title));
                    break;
                default:
                    break;
            }
        }
        return BaseResult.ok(queryById(titles.get(0).getIndicatorId()));
    }

    @Override
    public List<GeneralIndicatorDataTitle> queryById(String indicatorId) {
        List<GeneralIndicatorDataTitle> titles = indicatorTitleMapper.queryById(indicatorId);
        //1.获取现有字段名称 eg:item_value item_value1 item_value2
        List<String> columnNames = titles.stream().map(GeneralIndicatorDataTitle::getColumnName).collect(Collectors.toList());
        //2.对比,获取完整字段名
        List<String> allColumnNames = UtilConstants.getAllColumn();
        //3.筛选出可用字段名
        List<String> checkColumnNames = new ArrayList<>();
        for (String column : allColumnNames) {
            if (!columnNames.contains(column)) {
                checkColumnNames.add(UtilConstants.getItemMap(column));
            }
        }
        //4.将可用字段传给接口
        for (GeneralIndicatorDataTitle title : titles) {
            title.setCheckColumn(checkColumnNames);
            title.setColumnName(UtilConstants.getItemMap(title.getColumnName()));
        }
        return titles;
    }

    @Override
    public BaseResult<?> saveUnit(IndicatorTitleVo titleVo) {
        //主值项处理,更新为主值
        titleVo.getMainValues().forEach(title -> indicatorTitleMapper.updateTitleMain(title.getId(), "1"));
        //副值项处理,更新为副值
        titleVo.getSubValues().forEach(title -> indicatorTitleMapper.updateTitleMain(title.getId(), "0"));
        //更新该指标下指标数据，第一单位更新itemUnit，第二单位itemUnit2nd
        if (StringUtils.isNotEmpty(titleVo.getIndicatorId())) {
            indicatorTitleMapper.updateUnitByIndicatorId(titleVo.getIndicatorId(), titleVo.getItemUnit(), titleVo.getItemUnit2nd());
        }
        return BaseResult.ok();
    }

}
