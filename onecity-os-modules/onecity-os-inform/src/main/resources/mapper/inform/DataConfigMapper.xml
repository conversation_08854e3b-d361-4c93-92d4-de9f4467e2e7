<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.onecity.os.inform.zhibiao.mapper.DataConfigMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.inform.zhibiao.entity.DataConfig">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="data_set_id" property="dataSetId" jdbcType="BIGINT"/>
        <result column="data_set_name" property="dataSetName" jdbcType="VARCHAR"/>
        <result column="data_unit" property="dataUnit" jdbcType="VARCHAR"/>
        <result column="data_value" property="dataValue" jdbcType="VARCHAR"/>
        <result column="data_key" property="dataKey" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creater" property="creater" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="is_master_value" property="isMasterValue" jdbcType="TINYINT"/>
        <result column="indicator_id" property="indicatorId" jdbcType="VARCHAR"/>
        <result column="secondary_unit" property="secondaryUnit" jdbcType="VARCHAR"/>
        <result column="data_value_name" property="dataValueName" jdbcType="VARCHAR"/>
        <result column="data_key_name" property="dataKeyName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List" >
        id as id,data_set_id as dataSetId,data_set_name as dataSetName,
        data_unit as dataUnit,data_value as dataValue,data_key as dataKey,
        create_time as createTime,update_time as updateTime,creater as creater,
        updater as updater,is_delete as isDelete,is_master_value as isMasterValue,
        indicator_id as indicatorId,secondary_unit as secondaryUnit,
        data_value_name as dataValueName,data_key_name as dataKeyName
    </sql>
    <insert id="addDataConfig">
        insert into data_config (
        id,
        data_set_id,
        data_set_name,
        data_unit,
        data_value,
        data_key,
        create_time,
        update_time,
        creater,
        updater,
        is_delete,
        is_master_value,
        indicator_id,
        secondary_unit,
        data_value_name,
        data_key_name
        )
        VALUES
	    (
		#{dc.id},
		#{dc.dataSetId},
		#{dc.dataSetName},
		#{dc.dataUnit},
		#{dc.dataValue},
		#{dc.dataKey},
		#{dc.createTime},
		#{dc.updateTime},
		#{dc.creater},
		#{dc.updater},
		0,
		#{dc.isMasterValue},
		#{dc.indicatorId},
		#{dc.secondaryUnit},
		#{dc.dataValueName},
		#{dc.dataKeyName}
	    );
    </insert>


    <update id="updateDataConfig">
        update data_config
        set data_set_id=#{dc.dataSetId},
        data_set_name=#{dc.dataSetName},
        data_unit=#{dc.dataUnit},
        data_value=#{dc.dataValue},
        data_key=#{dc.dataKey},
        create_time=#{dc.createTime},
        update_time=#{dc.updateTime},
        creater=#{dc.creater},
        updater=#{dc.updater},
        is_delete = #{dc.isDelete},
        is_master_value=#{dc.isMasterValue},
        secondary_unit=#{dc.secondaryUnit},
        data_value_name=#{dc.dataValueName},
        data_key_name=#{dc.dataKeyName}
        where id = #{dc.id}
    </update>

    <select id="getDataConfigByIndicatorId" resultType="com.onecity.os.inform.zhibiao.entity.DataConfig">
        select <include refid="Base_Column_List"/> from data_config
        where indicator_id = #{id}
        and is_delete = 0
    </select>

    <update id="deleteDataConfigById">
        update data_config
        set is_delete = 1,
        update_time = #{dc.updateTime},
        updater = #{dc.updater}
        where id = #{dc.id}

    </update>

    <update id="deleteDataConfigByIndicatorId">
        update data_config
        set is_delete = 1,
        update_time = #{dc.updateTime},
        updater = #{dc.updater}
        where indicator_id = #{dc.indicatorId}

    </update>

    <select id="getDataConfigById" resultType="com.onecity.os.inform.zhibiao.entity.DataConfig">
        select <include refid="Base_Column_List"/> from data_config
        where id = #{id}
        and is_delete = 0
    </select>

</mapper>













