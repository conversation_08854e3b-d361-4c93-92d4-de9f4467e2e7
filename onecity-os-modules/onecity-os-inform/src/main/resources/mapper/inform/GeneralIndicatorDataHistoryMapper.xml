<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.modules.inform.mapper.GeneralIndicatorDataHistoryMapper">
    
    <resultMap type="com.onecity.os.inform.modules.inform.entity.GeneralIndicatorDataHistory" id="GeneralIndicatorDataHistoryResult">
        <result property="id"    column="id"    />
        <result property="indicatorId"    column="indicator_id"    />
        <result property="indicatorName"    column="indicator_name"    />
        <result property="itemName"    column="item_name"    />
        <result property="itemValue"    column="item_value"    />
        <result property="itemValue1"    column="item_value1"    />
        <result property="itemValue2"    column="item_value2"    />
        <result property="itemValue3"    column="item_value3"    />
        <result property="itemValue4"    column="item_value4"    />
        <result property="itemValue5"    column="item_value5"    />
        <result property="itemValue6"    column="item_value6"    />
        <result property="itemUnit"    column="item_unit"    />
        <result property="itemUnit2nd"    column="item_unit_2nd"    />
        <result property="identify"    column="identify"    />
        <result property="style"    column="style"    />
        <result property="isFold"    column="is_fold"    />
        <result property="sequence"    column="sequence"    />
        <result property="updateDate"    column="update_date"    />
        <result property="currentFlag"    column="current_flag"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createTime"    column="create_time"    />
        <result property="creater"    column="creater"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updater"    column="updater"    />
        <result property="planUpdateDate"    column="plan_update_date"    />
    </resultMap>

    <sql id="selectGeneralIndicatorDataHistoryVo">
        select id, indicator_id, indicator_name, item_name, item_value, item_value1, item_value2, item_value3, item_value4, item_value5, item_value6, item_unit, item_unit_2nd, identify, style, is_fold, sequence, update_date, current_flag, is_delete, create_time, creater, update_time, updater, plan_update_date from general_indicator_data_history
    </sql>

    <select id="selectGeneralIndicatorDataHistoryList" parameterType="com.onecity.os.inform.modules.inform.entity.GeneralIndicatorDataHistory" resultMap="GeneralIndicatorDataHistoryResult">
        <include refid="selectGeneralIndicatorDataHistoryVo"/>
        <where>  
        </where>
    </select>
    
    <select id="selectGeneralIndicatorDataHistoryById" parameterType="Long" resultMap="GeneralIndicatorDataHistoryResult">
        <include refid="selectGeneralIndicatorDataHistoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertGeneralIndicatorDataHistory" parameterType="com.onecity.os.inform.modules.inform.entity.GeneralIndicatorDataHistory" useGeneratedKeys="true" keyProperty="id">
        insert into general_indicator_data_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="indicatorId != null">indicator_id,</if>
            <if test="indicatorName != null">indicator_name,</if>
            <if test="itemName != null">item_name,</if>
            <if test="itemValue != null">item_value,</if>
            <if test="itemValue1 != null">item_value1,</if>
            <if test="itemValue2 != null">item_value2,</if>
            <if test="itemValue3 != null">item_value3,</if>
            <if test="itemValue4 != null">item_value4,</if>
            <if test="itemValue5 != null">item_value5,</if>
            <if test="itemValue6 != null">item_value6,</if>
            <if test="itemUnit != null">item_unit,</if>
            <if test="itemUnit2nd != null">item_unit_2nd,</if>
            <if test="identify != null">identify,</if>
            <if test="style != null">style,</if>
            <if test="isFold != null">is_fold,</if>
            <if test="sequence != null">sequence,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="currentFlag != null">current_flag,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creater != null">creater,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updater != null">updater,</if>
            <if test="planUpdateDate != null">plan_update_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="indicatorId != null">#{indicatorId},</if>
            <if test="indicatorName != null">#{indicatorName},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="itemValue != null">#{itemValue},</if>
            <if test="itemValue1 != null">#{itemValue1},</if>
            <if test="itemValue2 != null">#{itemValue2},</if>
            <if test="itemValue3 != null">#{itemValue3},</if>
            <if test="itemValue4 != null">#{itemValue4},</if>
            <if test="itemValue5 != null">#{itemValue5},</if>
            <if test="itemValue6 != null">#{itemValue6},</if>
            <if test="itemUnit != null">#{itemUnit},</if>
            <if test="itemUnit2nd != null">#{itemUnit2nd},</if>
            <if test="identify != null">#{identify},</if>
            <if test="style != null">#{style},</if>
            <if test="isFold != null">#{isFold},</if>
            <if test="sequence != null">#{sequence},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="currentFlag != null">#{currentFlag},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creater != null">#{creater},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="planUpdateDate != null">#{planUpdateDate},</if>
         </trim>
    </insert>

    <update id="updateGeneralIndicatorDataHistory" parameterType="com.onecity.os.inform.modules.inform.entity.GeneralIndicatorDataHistory">
        update general_indicator_data_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="indicatorId != null">indicator_id = #{indicatorId},</if>
            <if test="indicatorName != null">indicator_name = #{indicatorName},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="itemValue != null">item_value = #{itemValue},</if>
            <if test="itemValue1 != null">item_value1 = #{itemValue1},</if>
            <if test="itemValue2 != null">item_value2 = #{itemValue2},</if>
            <if test="itemValue3 != null">item_value3 = #{itemValue3},</if>
            <if test="itemValue4 != null">item_value4 = #{itemValue4},</if>
            <if test="itemValue5 != null">item_value5 = #{itemValue5},</if>
            <if test="itemValue6 != null">item_value6 = #{itemValue6},</if>
            <if test="itemUnit != null">item_unit = #{itemUnit},</if>
            <if test="itemUnit2nd != null">item_unit_2nd = #{itemUnit2nd},</if>
            <if test="identify != null">identify = #{identify},</if>
            <if test="style != null">style = #{style},</if>
            <if test="isFold != null">is_fold = #{isFold},</if>
            <if test="sequence != null">sequence = #{sequence},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="currentFlag != null">current_flag = #{currentFlag},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creater != null">creater = #{creater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="planUpdateDate != null">plan_update_date = #{planUpdateDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGeneralIndicatorDataHistoryById" parameterType="Long">
        delete from general_indicator_data_history where id = #{id}
    </delete>

    <delete id="deleteGeneralIndicatorDataHistoryByIds" parameterType="String">
        delete from general_indicator_data_history where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>