<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.zhibiao.mapper.GeneralIndicatorDataMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.inform.zhibiao.entity.GeneralIndicatorData">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="indicator_id" jdbcType="VARCHAR" property="indicatorId"/>
        <result column="indicator_name" jdbcType="VARCHAR" property="indicatorName"/>
        <result column="item_name" jdbcType="VARCHAR" property="itemName"/>
        <result column="item_value" jdbcType="VARCHAR" property="itemValue"/>
        <result column="item_unit" jdbcType="VARCHAR" property="itemUnit"/>
        <result column="identify" jdbcType="VARCHAR" property="identify"/>
        <result column="style" jdbcType="INTEGER" property="style"/>
        <result column="is_fold" jdbcType="INTEGER" property="isFold"/>
        <result column="sequence" jdbcType="INTEGER" property="sequence"/>
        <result column="update_date" jdbcType="VARCHAR" property="updateDate"/>
        <result column="current_flag" jdbcType="INTEGER" property="currentFlag"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="creater" jdbcType="VARCHAR" property="creater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
    </resultMap>
    <sql id="Base_Column_List_data">
        id as id,
        indicator_id as indicatorId,
        indicator_name as indicatorName,
        item_name as itemName,
        item_value as itemValue,
        item_unit as itemUnit,
        identify as identify,
        style as style,
        is_fold as isFold,
        sequence as sequence,
        create_time as createTime,
        creater as creater,
        update_time as updateTime,
        updater as updater,
        update_date as updateDate,
        is_delete as isDelete,
        current_flag as currentFlag
    </sql>
    <select id="getIndicatorDataListBySourceIds"
            resultType="com.onecity.os.inform.zhibiao.entity.GeneralIndicatorDataTianbao">
        SELECT
        <include refid="Base_Column_List_data"/>
        FROM
        general_indicator_data
        LEFT JOIN general_indicator b ON b.id = general_indicator_data.indicator_id
        WHERE
        b.source_id IN
        <foreach collection="sources" item="source" open="(" separator="," close=")">
            #{source}
        </foreach>
        order by general_indicator_data.id
    </select>

    <insert id="insertData" parameterType="com.onecity.os.inform.zhibiao.entity.GeneralIndicatorData">
        INSERT INTO
        `general_indicator_data` (
            `id`,
            `indicator_id`,
            `indicator_name`,
            `item_name`,
            `item_value`,
            `item_unit`,
            `identify`,
            `style`,
            `is_fold`,
            `sequence`,
            `update_date`,
            `current_flag`,
            `is_delete`,
            `create_time`,
            `creater`,
            `update_time`,
            `updater`
        )
        VALUES
            (
                #{vo.id},
                #{vo.indicatorId},
                #{vo.indicatorName},
                #{vo.itemName},
                #{vo.itemValue},
                #{vo.itemUnit},
                #{vo.identify},
                #{vo.style},
                #{vo.isFold},
                #{vo.sequence},
                #{vo.updateDate},
                #{vo.currentFlag},
                #{vo.isDelete},
                #{vo.createTime},
                #{vo.creater},
                #{vo.updateTime},
                #{vo.updater}
            );
    </insert>

    <select id="getExportIndicatorDataXlsByIndicatorId"
            resultType="com.onecity.os.inform.zhibiao.model.vo.IndicatorDataExcel">
        SELECT
            a.<include refid="Base_Column_List_data"/>,
            b.indicator_name AS indicatorName,
            b.indicator_exhibit_type AS `type`
        FROM
            general_indicator_data a
            LEFT JOIN general_indicator b ON b.id = a.indicator_id
        WHERE
            a.indicator_id = #{indicatorId}
            AND a.is_delete = 0
        ORDER BY
            a.sequence,
            a.update_time
    </select>

    <select id="getIndicatorDataNameListByIndicatorId" resultType="String">
        SELECT
            item_name
        FROM
            general_indicator_data
        WHERE
            indicator_id = #{indicatorId}
            and is_delete = 0
    </select>

    <insert id="insertTianBaoIndicatorData">
        INSERT INTO `general_indicator_data` (
            `id`,
            `indicator_id`,
            `indicator_name`,
            `item_name`,
            `item_value`,
            `item_unit`,
            `identify`,
            `style`,
            `is_fold`,
            `sequence`,
            `update_date`,
            `current_flag`,
            `is_delete`,
            `create_time`,
            `creater`,
            `update_time`,
            `updater`
        )
        VALUES
            (
                NULL,
                #{vo.indicatorId},
                NULL,
                #{vo.itemName},
                #{vo.itemValue},
                #{vo.itemUnit},
                #{vo.identify},
                #{vo.style},
                #{vo.isFold},
                #{vo.sequence},
                #{vo.updateDate},
                #{vo.currentFlag},
                0,
                #{vo.createTime},
                #{vo.creater},
                #{vo.updateTime},
                #{vo.updater}
            );
    </insert>

    <update id="deleteDataByIndicatorId">
        UPDATE
        general_indicator_data
        SET is_delete = 1
        WHERE
            indicator_id = #{indicatorId}
            AND is_delete = 0
    </update>

    <update id="updateByIndicatorIdAndItemName">
        UPDATE
        `general_indicator_data`
        SET
        `item_name` = #{vo.itemName},
        `item_value` = #{vo.itemValue},
        `item_unit` = #{vo.itemUnit},
        `identify` = #{vo.identify},
        `style` = #{vo.style},
        `is_fold` = #{vo.isFold},
        `sequence` = #{vo.sequence},
        `update_date` = #{vo.updateDate},
        `current_flag` = #{vo.currentFlag},
        `is_delete` = 0,
        `update_time` = #{vo.updateTime},
        `updater` = #{vo.updater}
        WHERE
            `indicator_id` = #{vo.indicatorId} AND `item_name` = #{vo.itemName} ORDER BY create_time DESC LIMIT 1
    </update>


</mapper>





















