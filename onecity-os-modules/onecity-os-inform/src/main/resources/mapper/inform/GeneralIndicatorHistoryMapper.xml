<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.modules.inform.mapper.GeneralIndicatorHistoryMapper">
    
    <resultMap type="com.onecity.os.inform.modules.inform.entity.GeneralIndicatorHistory" id="GeneralIndicatorHistoryResult">
        <result property="id"    column="id"    />
        <result property="indicatorName"    column="indicator_name"    />
        <result property="indicatorExhibitType"    column="indicator_exhibit_type"    />
        <result property="parentId"    column="parent_id"    />
        <result property="parentName"    column="parent_name"    />
        <result property="iconUrl"    column="icon_url"    />
        <result property="sourceId"    column="source_id"    />
        <result property="sourceName"    column="source_name"    />
        <result property="sequence"    column="sequence"    />
        <result property="indicatorType"    column="indicator_type"    />
        <result property="updateDate"    column="update_date"    />
        <result property="updateCycle"    column="update_cycle"    />
        <result property="leader"    column="leader"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createTime"    column="create_time"    />
        <result property="creater"    column="creater"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updater"    column="updater"    />
        <result property="groupType"    column="group_type"    />
        <result property="groupUrl"    column="group_url"    />
        <result property="planUpdateDate"    column="plan_update_date"    />
        <result property="isShow"    column="is_show"    />
        <result property="isScreen"    column="is_screen"    />
        <result property="isLegend"    column="is_legend"    />
        <result property="dataUpdateMode"    column="data_update_mode"    />
        <result property="dataConfigId"    column="data_config_id"    />
        <result property="serviceId"    column="service_id"    />
    </resultMap>

    <sql id="selectGeneralIndicatorHistoryVo">
        select id, indicator_name, indicator_exhibit_type, parent_id, parent_name, icon_url, source_id, source_name, sequence, indicator_type, update_date, update_cycle, leader, is_delete, create_time, creater, update_time, updater, group_type, group_url, plan_update_date, is_show, is_screen, is_legend, data_update_mode, data_config_id, service_id from general_indicator_history
    </sql>

    <select id="selectGeneralIndicatorHistoryList" parameterType="com.onecity.os.inform.modules.inform.entity.GeneralIndicatorHistory" resultMap="GeneralIndicatorHistoryResult">
        <include refid="selectGeneralIndicatorHistoryVo"/>
        <where>  
            <if test="sourceId != null  and sourceId != ''"> and source_id = #{sourceId}</if>
            <if test="planUpdateDate != null  and planUpdateDate != ''"> and plan_update_date = #{planUpdateDate}</if>
            <if test="isShow != null "> and is_show = #{isShow}</if>
            <if test="isScreen != null "> and is_screen = #{isScreen}</if>
            <if test="isLegend != null "> and is_legend = #{isLegend}</if>
            <if test="dataUpdateMode != null "> and data_update_mode = #{dataUpdateMode}</if>
            <if test="dataConfigId != null  and dataConfigId != ''"> and data_config_id = #{dataConfigId}</if>
            <if test="serviceId != null  and serviceId != ''"> and service_id = #{serviceId}</if>
        </where>
    </select>
    
    <select id="selectGeneralIndicatorHistoryById" parameterType="String" resultMap="GeneralIndicatorHistoryResult">
        <include refid="selectGeneralIndicatorHistoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertGeneralIndicatorHistory" parameterType="com.onecity.os.inform.modules.inform.entity.GeneralIndicatorHistory">
        insert into general_indicator_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="indicatorName != null">indicator_name,</if>
            <if test="indicatorExhibitType != null">indicator_exhibit_type,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="parentName != null">parent_name,</if>
            <if test="iconUrl != null">icon_url,</if>
            <if test="sourceId != null">source_id,</if>
            <if test="sourceName != null">source_name,</if>
            <if test="sequence != null">sequence,</if>
            <if test="indicatorType != null">indicator_type,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="updateCycle != null">update_cycle,</if>
            <if test="leader != null">leader,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creater != null">creater,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updater != null">updater,</if>
            <if test="groupType != null">group_type,</if>
            <if test="groupUrl != null">group_url,</if>
            <if test="planUpdateDate != null">plan_update_date,</if>
            <if test="isShow != null">is_show,</if>
            <if test="isScreen != null">is_screen,</if>
            <if test="isLegend != null">is_legend,</if>
            <if test="dataUpdateMode != null">data_update_mode,</if>
            <if test="dataConfigId != null">data_config_id,</if>
            <if test="serviceId != null">service_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="indicatorName != null">#{indicatorName},</if>
            <if test="indicatorExhibitType != null">#{indicatorExhibitType},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="parentName != null">#{parentName},</if>
            <if test="iconUrl != null">#{iconUrl},</if>
            <if test="sourceId != null">#{sourceId},</if>
            <if test="sourceName != null">#{sourceName},</if>
            <if test="sequence != null">#{sequence},</if>
            <if test="indicatorType != null">#{indicatorType},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="updateCycle != null">#{updateCycle},</if>
            <if test="leader != null">#{leader},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creater != null">#{creater},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="groupType != null">#{groupType},</if>
            <if test="groupUrl != null">#{groupUrl},</if>
            <if test="planUpdateDate != null">#{planUpdateDate},</if>
            <if test="isShow != null">#{isShow},</if>
            <if test="isScreen != null">#{isScreen},</if>
            <if test="isLegend != null">#{isLegend},</if>
            <if test="dataUpdateMode != null">#{dataUpdateMode},</if>
            <if test="dataConfigId != null">#{dataConfigId},</if>
            <if test="serviceId != null">#{serviceId},</if>
         </trim>
    </insert>

    <update id="updateGeneralIndicatorHistory" parameterType="com.onecity.os.inform.modules.inform.entity.GeneralIndicatorHistory">
        update general_indicator_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="indicatorName != null">indicator_name = #{indicatorName},</if>
            <if test="indicatorExhibitType != null">indicator_exhibit_type = #{indicatorExhibitType},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="parentName != null">parent_name = #{parentName},</if>
            <if test="iconUrl != null">icon_url = #{iconUrl},</if>
            <if test="sourceId != null">source_id = #{sourceId},</if>
            <if test="sourceName != null">source_name = #{sourceName},</if>
            <if test="sequence != null">sequence = #{sequence},</if>
            <if test="indicatorType != null">indicator_type = #{indicatorType},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="updateCycle != null">update_cycle = #{updateCycle},</if>
            <if test="leader != null">leader = #{leader},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creater != null">creater = #{creater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="groupType != null">group_type = #{groupType},</if>
            <if test="groupUrl != null">group_url = #{groupUrl},</if>
            <if test="planUpdateDate != null">plan_update_date = #{planUpdateDate},</if>
            <if test="isShow != null">is_show = #{isShow},</if>
            <if test="isScreen != null">is_screen = #{isScreen},</if>
            <if test="isLegend != null">is_legend = #{isLegend},</if>
            <if test="dataUpdateMode != null">data_update_mode = #{dataUpdateMode},</if>
            <if test="dataConfigId != null">data_config_id = #{dataConfigId},</if>
            <if test="serviceId != null">service_id = #{serviceId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGeneralIndicatorHistoryById" parameterType="String">
        delete from general_indicator_history where id = #{id}
    </delete>

    <delete id="deleteGeneralIndicatorHistoryByIds" parameterType="String">
        delete from general_indicator_history where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>