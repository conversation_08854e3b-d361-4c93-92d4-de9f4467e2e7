<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.zhibiao.mapper.GeneralIndicatorMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.inform.zhibiao.entity.GeneralIndicator">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="indicator_name" jdbcType="VARCHAR" property="indicatorName"/>
        <result column="indicator_exhibit_type" jdbcType="VARCHAR" property="indicatorExhibitType"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="parent_name" jdbcType="VARCHAR" property="parentName"/>
        <result column="icon_url" jdbcType="VARCHAR" property="iconUrl"/>
        <result column="source_id" jdbcType="VARCHAR" property="sourceId"/>
        <result column="source_name" jdbcType="VARCHAR" property="sourceName"/>
        <result column="sequence" jdbcType="INTEGER" property="sequence"/>
        <result column="indicator_type" jdbcType="INTEGER" property="indicatorType"/>
        <result column="update_date" jdbcType="VARCHAR" property="updateDate"/>
        <result column="update_cycle" jdbcType="VARCHAR" property="updateCycle"/>
        <result column="leader" jdbcType="VARCHAR" property="leader"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="creater" jdbcType="VARCHAR" property="creater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="group_type" jdbcType="INTEGER" property="groupType"/>
        <result column="group_url" jdbcType="VARCHAR" property="groupUrl"/>
        <result column="plan_update_date" jdbcType="VARCHAR" property="planUpdateDate"/>
        <result property="isShow" column="is_show" jdbcType="INTEGER"/>
        <result property="isScreen" column="is_screen" jdbcType="INTEGER"/>
        <result property="isLegend" column="is_legend" jdbcType="INTEGER"/>
        <result property="dataUpdateMode" column="data_update_mode" jdbcType="INTEGER"/>
        <result property="dataConfigId" column="data_config_id" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id as id,
        indicator_name as indicatorName,
        indicator_exhibit_type as indicatorExhibitType,
        parent_id as parentId,
        parent_name as parentName,
        icon_url as iconUrl,
        source_id as sourceId,
        source_name as sourceName,
        sequence as sequence,
        indicator_type as indicatorType,
        update_date as updateDate,
        update_cycle as updateCycle,
        leader as leader,
        is_delete as isDelete,
        create_time as createTime,
        creater as creater,
        update_time as updateTime,
        updater as updater,
        group_type as groupType,
        group_url as groupUrl,
        is_show as isShow,
        is_screen as isScreen,
        is_legend as isLegend,
        data_update_mode as dataUpdateMode,
        data_config_id as dataConfigId
    </sql>
    <select id="getIndicatorListBySourceIds"
            resultType="com.onecity.os.inform.zhibiao.entity.GeneralIndicatorTianbao">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        general_indicator
        WHERE
        source_id IN
        <foreach collection="sources" item="source" open="(" separator="," close=")">
            #{source}
        </foreach>
        order by id
    </select>

    <select id="getIndicatorInfoById" resultType="com.onecity.os.inform.zhibiao.entity.GeneralIndicator">
        select <include refid="Base_Column_List"/> from general_indicator where id = #{id}
    </select>

    <select id="getIndicatorListBySourceIdAndCycle" resultType="String">
        SELECT
        c3.indicator_name
        FROM
        (
        SELECT
        c1.id,
        c1.parent_id,
        c1.indicator_name,
        c1.update_cycle,
        c1.indicator_type,
        IF
        (
        find_in_set( c1.parent_id, @p ) > 0,
        @p := concat( @p, ',', c1.id ),
        0
        ) AS childIds
        FROM
        (
        SELECT
        c.id,
        c.parent_id,
        c.indicator_name,
        c.update_cycle,
        c.indicator_type
        FROM
        general_indicator c
        WHERE
        c.source_id = #{sourceId}
        AND c.is_delete = 0
        ORDER BY
        c.update_cycle
        ) c1,
        ( SELECT @p := #{parentIndicatorId} ) c2
        ) c3
        WHERE
        c3.childIds != '0'
        AND c3.indicator_type = 0
        AND c3.update_cycle = #{updateCycle}
        <if test="null != startTime and '' != startTime">
            AND c3.update_time &gt;= #{startTime}
        </if>
        <if test="null != endTime and '' != endTime">
            AND c3.update_time &lt;= #{endTime}
        </if>
    </select>

    <select id="getParentIndicatorIdListBySourceId" resultType="String">
        SELECT id
        FROM general_indicator
        WHERE is_delete = 0
          AND parent_id = '0'
          AND source_id = #{sourceId}
    </select>


    <select id="getParentIndicatorIdsBySourceId" resultType="String">
        SELECT id
        FROM general_indicator
        WHERE is_delete = 0
          and parent_id = '0'
          and source_id = #{sourceSimpleName}
          and data_update_mode = 1
    </select>


    <select id="getParentIndicatorIdBySourceIds" resultType="String">
        SELECT
        id
        FROM
        general_indicator
        WHERE
        is_delete = 0
        AND parent_id = '0'
        AND source_id IN
        <foreach collection="sourceIds" item="sourceId" open="(" separator="," close=")">
            #{sourceId}
        </foreach>
    </select>

    <select id="getParentIndicatorIdBySourceNames" resultType="String">
        SELECT
        id
        FROM
        general_indicator
        WHERE
        is_delete = 0
        AND parent_id = '0'
        AND source_name IN
        <foreach collection="sourceNames" item="sourceName" open="(" separator="," close=")">
            #{sourceName}
        </foreach>
    </select>

    <select id="getParentIndicatorIdBySourceName" resultType="String">
        SELECT
        id
        FROM
        general_indicator
        WHERE
        is_delete = 0
        AND parent_id = '0'
        AND source_id = #{sourceName}
    </select>

    <select id="getIndicatorsByParentIds" resultType="com.onecity.os.inform.zhibiao.entity.GeneralIndicator">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        general_indicator
        WHERE
        is_delete = 0
        AND parent_id IN
        <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>
    </select>


    <select id="getIndicatorIdsByParentIds" resultType="String">
        SELECT
        id
        FROM
        general_indicator
        WHERE
        is_delete = 0
        and data_update_mode = 1
        AND parent_id IN
        <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>
    </select>

    <select id="getIndicatorNameSourceNameById"
            resultType="com.onecity.os.inform.zhibiao.model.vo.IndicatorDataExcel">
        SELECT a.indicator_name         AS indicatorName,
               a.indicator_exhibit_type AS type,
               '阅批呈报' as source_name
        FROM general_indicator_tianbao a
        WHERE a.id = #{indicatorId}
        LIMIT 1
    </select>

    <select id="getIndicatorExhibitTypeById" resultType="String">
        SELECT indicator_exhibit_type
        FROM general_indicator
        WHERE id = #{indicatorId}
          AND is_delete = 0 LIMIT 1
    </select>

    <select id="getIndicatorExhibitTypeByIdTianbao" resultType="String">
        SELECT indicator_exhibit_type
        FROM general_indicator
        WHERE id = #{indicatorId}
          AND is_delete = 0 LIMIT 1
    </select>

</mapper>










