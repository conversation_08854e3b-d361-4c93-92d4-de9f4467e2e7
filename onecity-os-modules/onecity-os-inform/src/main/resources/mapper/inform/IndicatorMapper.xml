<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.zhibiao.mapper.IndicatorMapper">

    <resultMap type="com.onecity.os.inform.zhibiao.entity.GeneralIndicatorTianbao" id="BaseResultMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="indicatorName" column="indicator_name" jdbcType="VARCHAR"/>
        <result property="indicatorExhibitType" column="indicator_exhibit_type" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="parentName" column="parent_name" jdbcType="VARCHAR"/>
        <result property="iconUrl" column="icon_url" jdbcType="VARCHAR"/>
        <result property="sourceId" column="source_id" jdbcType="VARCHAR"/>
        <result property="sourceName" column="source_name" jdbcType="VARCHAR"/>
        <result property="sequence" column="sequence" jdbcType="INTEGER"/>
        <result property="indicatorType" column="indicator_type" jdbcType="INTEGER"/>
        <result property="updateDate" column="update_date" jdbcType="VARCHAR"/>
        <result property="updateCycle" column="update_cycle" jdbcType="VARCHAR"/>
        <result property="leader" column="leader" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creater" column="creater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="groupType" column="group_type" jdbcType="INTEGER"/>
        <result property="groupUrl" column="group_url" jdbcType="VARCHAR"/>
        <result property="isShow" column="is_show" jdbcType="INTEGER"/>
        <result property="isScreen" column="is_screen" jdbcType="INTEGER"/>
        <result property="isLegend" column="is_legend" jdbcType="INTEGER"/>
        <result property="dataUpdateMode" column="data_update_mode" jdbcType="INTEGER"/>
        <result property="dataConfigId" column="data_config_id" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap type="com.onecity.os.inform.zhibiao.model.vo.GeneralIndicatorVO" id="ExportResultMap">
        <result property="fistIndicatorName" column="fist_indicator_name" jdbcType="VARCHAR"/>
        <result property="secondIndicatorName" column="second_indicator_name" jdbcType="VARCHAR"/>
        <result property="itemName" column="item_name" jdbcType="VARCHAR"/>
        <result property="itemValue" column="item_value" jdbcType="VARCHAR"/>
        <result property="itemUnit" column="item_unit" jdbcType="VARCHAR"/>
        <result property="indicatorExhibitType" column="indicator_exhibit_type" jdbcType="VARCHAR"/>
        <result property="updateCycle" column="update_cycle" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="VARCHAR"/>
        <result property="leader" column="leader" jdbcType="VARCHAR"/>
        <result property="sourceName" column="source_name" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        id as id,
        indicator_name as indicatorName,
        indicator_exhibit_type as indicatorExhibitType,
        parent_id as parentId,
        parent_name as parentName,
        icon_url as iconUrl,
        source_id as sourceId,
        source_name as sourceName,
        sequence as sequence,
        indicator_type as indicatorType,
        update_date as updateDate,
        update_cycle as updateCycle,
        leader as leader,
        is_delete as isDelete,
        create_time as createTime,
        creater as creater,
        update_time as updateTime,
        updater as updater,
        group_type as groupType,
        group_url as groupUrl,
        plan_update_date as planUpdateDate,
        is_show as isShow,
        is_screen as isScreen,
        is_legend as isLegend,
        data_update_mode as dataUpdateMode,
        data_config_id as dataConfigId
    </sql>
    <sql id="Base_Column_List_data">
        id as id,
        indicator_id as indicatorId,
        indicator_name as indicatorName,
        item_name as itemName,
        item_value as itemValue,
        item_unit as itemUnit,
        identify as identify,
        style as style,
        is_fold as isFold,
        sequence as sequence,
        create_time as createTime,
        creater as creater,
        update_time as updateTime,
        updater as updater,
        update_date as updateDate,
        is_delete as isDelete,
        current_flag as currentFlag
    </sql>
    <sql id="exportIndicator">
        a.parent_name fist_indicator_name,a.indicator_name second_indicator_name,b.item_name,
        b.item_value,b.item_unit,c.item_text indicator_exhibit_type,a.update_cycle,a.update_date,a.leader,a.source_name
    </sql>
    <sql id="where">
        <where>
            <if test="id != null">
                a.id = #{id}
            </if>
        </where>
    </sql>

    <select id="getInfoById" resultType="com.onecity.os.inform.zhibiao.entity.GeneralIndicatorTianbao">
        SELECT
        <include refid="Base_Column_List" />
        FROM
            general_indicator
        WHERE
            id = #{tabId}
            AND source_id = #{tj}
            AND is_delete = 0
            LIMIT 1
    </select>

    <select id="getIndicatorIdByDataId" resultType="java.lang.String">
        select
            indicator_id
        from general_indicator_data
        where id = #{id} limit 1
    </select>

    <select id="getInfoOnlyById" resultType="com.onecity.os.inform.zhibiao.entity.GeneralIndicatorTianbao">
        SELECT
        <include refid="Base_Column_List" />
        FROM
            general_indicator
        WHERE
            id = #{tabId}
            AND is_delete = 0
    </select>

    <select id="getByParentId" resultType="com.onecity.os.inform.zhibiao.entity.GeneralIndicator">
       SELECT
        id as id,
        indicator_name as indicatorName,
        indicator_exhibit_type as indicatorExhibitType,
        parent_id as parentId,
        parent_name as parentName,
        icon_url as iconUrl,
        source_id as sourceId,
        source_name as sourceName,
        sequence as sequence,
        indicator_type as indicatorType,
        update_date as updateDate,
        update_cycle as updateCycle,
        leader as leader,
        is_delete as isDelete,
        create_time as createTime,
        creater as creater,
        update_time as updateTime,
        updater as updater,
        group_type as groupType,
        group_url as groupUrl,
        is_show as isShow,
        is_screen as isScreen,
        is_legend as isLegend,
        data_update_mode as dataUpdateMode,
        data_config_id as dataConfigId
        FROM
            general_indicator
        WHERE
            parent_id = #{tabId}
            AND source_id = #{tj}
            AND is_delete = 0
        ORDER BY
        sequence,
        update_time
    </select>

    <select id="getUpdatedIndicatorIds" resultType="java.lang.String">
        select indicator_id from general_indicator_tianbao_update_record a,
        (SELECT
               max(audit_time) audit_time
           FROM
               indicator_audit_notes
           WHERE
               source_id = #{sourceSimpleName}
           AND audit_result = 1) b
         where a.indicator_id in (SELECT
	                              	id
	                              FROM
	                              	general_indicator_tianbao
	                              WHERE
	                              	source_id = #{sourceSimpleName}
	                              AND is_delete = 0
                                  )
        and a.create_time >= b.audit_time;
    </select>

    <select id="getIndicatorDataByParentId" resultType="com.onecity.os.inform.zhibiao.model.dto.IndicatorDto">
       SELECT
        id as id,
        indicator_name as indicatorName,
        indicator_exhibit_type as indicatorExhibitType,
        parent_id as parentId,
        parent_name as parentName,
        icon_url as iconUrl,
        source_id as sourceId,
        source_name as sourceName,
        sequence as sequence,
        indicator_type as indicatorType,
        update_date as updateDate,
        update_cycle as updateCycle,
        leader as leader,
        is_delete as isDelete,
        create_time as createTime,
        creater as creater,
        update_time as updateTime,
        updater as updater,
        group_type as groupType,
        group_url as groupUrl,
        is_show as isShow,
        is_screen as isScreen,
        is_legend as isLegend,
        data_update_mode as dataUpdateMode,
        data_config_id as dataConfigId
        FROM
            general_indicator
        WHERE
            parent_id = #{id}
            AND source_id = #{tj}
            AND is_delete = 0
            AND indicator_type = 0
        ORDER BY
        sequence,
        update_time
    </select>


    <update id="deleteTargets">
        UPDATE
        general_indicator
        SET is_delete = 1,
        update_time = NOW( ),
        updater = #{userName}
        WHERE
        source_id = #{tj}
        AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="addIndicator">
        INSERT INTO general_indicator (
        `id`,
        `indicator_name`,
        `indicator_exhibit_type`,
        `parent_id`,
        `parent_name`,
        `icon_url`,
        `source_id`,
        `source_name`,
        `sequence`,
        `indicator_type`,
        `update_date`,
        `update_cycle`,
        `leader`,
        `is_delete`,
        `create_time`,
        `creater`,
        `update_time`,
        `updater`,
        `group_type`,
        `group_url`,
        `plan_update_date`,
        `is_show`,
        `is_screen`,
        `is_legend`,
        `data_update_mode`,
        `data_config_id`
        )
        VALUES
	    (
		#{vo.id},
		#{vo.indicatorName},
		#{vo.indicatorExhibitType},
		#{vo.parentId},
		#{vo.parentName},
		#{vo.iconUrl},
		#{tj},
		#{sourceName},
		#{vo.sequence},
		#{vo.indicatorType},
		#{vo.updateDate},
		#{vo.updateCycle},
		#{vo.leader},
		0,
		#{vo.createTime},
		#{vo.creater},
		#{vo.updateTime},
	    #{vo.updater},
	    #{vo.groupType},
	    #{vo.groupUrl},
	    #{vo.planUpdateDate},
	    #{vo.isShow},
	    #{vo.isScreen},
        #{vo.isLegend},
	    #{vo.dataUpdateMode},
	    #{vo.dataConfigId}
	    );
    </insert>

    <insert id="insertIndicator">
        INSERT INTO ${tableName} (
        `id`,
        `indicator_name`,
        `indicator_exhibit_type`,
        `parent_id`,
        `parent_name`,
        `icon_url`,
        `source_id`,
        `source_name`,
        `sequence`,
        `indicator_type`,
        `update_date`,
        `update_cycle`,
        `leader`,
        `is_delete`,
        `create_time`,
        `creater`,
        `update_time`,
        `updater`
        )
        VALUES
	    (
		#{vo.id},
		#{vo.indicatorName},
		#{vo.indicatorExhibitType},
		#{vo.parentId},
		#{vo.parentName},
		#{vo.iconUrl},
		#{vo.sourceId},
		#{vo.sourceName},
		#{vo.sequence},
		#{vo.indicatorType},
		#{vo.updateDate},
		#{vo.updateCycle},
		#{vo.leader},
		0,
		#{vo.createTime},
		#{vo.creater},
		#{vo.updateTime},
	    #{vo.updater}
	    );
    </insert>
    <insert id="insertIndicatorSelective">
        INSERT INTO ${tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vo.id != null and vo.id != '' ">
                id,
            </if>
            <if test="vo.indicatorName != null and vo.indicatorName != '' ">
                indicator_name,
            </if>
            <if test="vo.indicatorExhibitType != null and vo.indicatorExhibitType != '' ">
                indicator_exhibit_type,
            </if>
            <if test="vo.parentId != null and vo.parentId != '' ">
                parent_id,
            </if>
            <if test="vo.parentName != null and vo.parentName != '' ">
                parent_name,
            </if>
            <if test="vo.iconUrl != null and vo.iconUrl != '' ">
                icon_url,
            </if>
            <if test="vo.sourceId != null and vo.sourceId != '' ">
                source_id,
            </if>
            <if test="vo.sourceName != null and vo.sourceName != '' ">
                source_name,
            </if>
            <if test="vo.sequence != null  ">
                sequence,
            </if>
            <if test="vo.indicatorType != null  ">
                indicator_type,
            </if>
            <if test="vo.updateDate != null  and vo.updateDate != '' ">
                update_date,
            </if>
            <if test="vo.updateCycle != null  and vo.updateCycle != '' ">
                update_cycle,
            </if>
            <if test="vo.leader != null  and vo.leader != '' ">
                leader,
            </if>
            <if test="vo.isDelete != null   ">
                is_delete,
            </if>
            <if test="vo.createTime != null   ">
                create_time,
            </if>
            <if test="vo.creater != null  and vo.creater != '' ">
                creater,
            </if>
            <if test="vo.updateTime != null ">
                update_time,
            </if>
            <if test="vo.updater != null  and vo.updater != '' ">
                updater,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vo.id != null and vo.id != '' ">
                #{vo.id,jdbcType=VARCHAR},
            </if>
            <if test="vo.indicatorName != null and vo.indicatorName != '' ">
                #{vo.indicatorName,jdbcType=VARCHAR},
            </if>
            <if test="vo.indicatorExhibitType != null and vo.indicatorExhibitType != '' ">
                #{vo.indicatorExhibitType,jdbcType=VARCHAR},
            </if>
            <if test="vo.parentId != null and vo.parentId != '' ">
                #{vo.parentId,jdbcType=VARCHAR},
            </if>
            <if test="vo.parentName != null and vo.parentName != '' ">
                #{vo.parentName,jdbcType=VARCHAR},
            </if>
            <if test="vo.iconUrl != null and vo.iconUrl != '' ">
                #{vo.iconUrl,jdbcType=VARCHAR},
            </if>
            <if test="vo.sourceId != null and vo.sourceId != '' ">
                #{vo.sourceId,jdbcType=VARCHAR},
            </if>
            <if test="vo.sourceName != null and vo.sourceName != '' ">
                #{vo.sourceName,jdbcType=VARCHAR},
            </if>
            <if test="vo.sequence != null  ">
                #{vo.sequence,jdbcType=INTEGER},
            </if>
            <if test="vo.indicatorType != null  ">
                #{vo.indicatorType,jdbcType=INTEGER},
            </if>
            <if test="vo.updateDate != null  and vo.updateDate != '' ">
                #{vo.updateDate,jdbcType=VARCHAR},
            </if>
            <if test="vo.updateCycle != null  and vo.updateCycle != '' ">
                #{vo.updateCycle,jdbcType=VARCHAR},
            </if>
            <if test="vo.leader != null  and vo.leader != '' ">
                #{vo.leader,jdbcType=VARCHAR},
            </if>
            <if test="vo.isDelete != null   ">
                #{vo.isDelete,jdbcType=INTEGER},
            </if>
            <if test="vo.createTime != null   ">
                #{vo.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="vo.creater != null  and vo.creater != '' ">
                #{vo.creater,jdbcType=VARCHAR},
            </if>
            <if test="vo.updateTime != null ">
                #{vo.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="vo.updater != null  and vo.updater != '' ">
                #{vo.updater,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertIndicatorBatch">
        INSERT INTO ${tableName} values
        <foreach collection="indicatorList" item="indicator" separator="," close=";">
            (
            #{indicator.id,jdbcType=VARCHAR},
            #{indicator.indicatorName,jdbcType=VARCHAR},
            #{indicator.indicatorExhibitType,jdbcType=VARCHAR},
            #{indicator.parentId,jdbcType=VARCHAR},
            #{indicator.parentName,jdbcType=VARCHAR},
            #{indicator.iconUrl,jdbcType=VARCHAR},
            #{indicator.sourceId,jdbcType=VARCHAR},
            #{indicator.sourceName,jdbcType=VARCHAR},
            #{indicator.sequence,jdbcType=INTEGER},
            #{indicator.indicatorType,jdbcType=INTEGER},
            #{indicator.updateDate,jdbcType=VARCHAR},
            #{indicator.updateCycle,jdbcType=VARCHAR},
            #{indicator.leader,jdbcType=VARCHAR},
            #{indicator.isDelete,jdbcType=INTEGER},
            #{indicator.createTime,jdbcType=TIMESTAMP},
            #{indicator.creater,jdbcType=VARCHAR},
            #{indicator.updateTime,jdbcType=TIMESTAMP},
            #{indicator.updater,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <update id="updateIndicator">
        UPDATE
        general_indicator
        SET
        `indicator_name` = #{vo.indicatorName},
        `indicator_exhibit_type` = #{vo.indicatorExhibitType},
        `parent_id` = #{vo.parentId},
        <if test="null != vo.parentName and '' != vo.parentName">
            `parent_name` = #{vo.parentName},
        </if>
        <if test="null != vo.iconUrl and '' != vo.iconUrl">
            `icon_url` = #{vo.iconUrl},
        </if>
        <if test="null != vo.updateDate and '' != vo.updateDate">
            `update_date` = #{vo.updateDate},
        </if>
        <if test="null != vo.leader and '' != vo.leader">
            `leader` = #{vo.leader},
        </if>
        <if test="null != vo.createTime">
            `create_time` = #{vo.createTime},
        </if>
        <if test="null != vo.creater and '' != vo.creater">
            `creater` = #{vo.creater},
        </if>
        <if test="null != vo.updateTime">
            `update_time` = #{vo.updateTime},
        </if>
        <if test="null != vo.updater and '' != vo.updater">
            `updater` = #{vo.updater},
        </if>
        <if test="null != vo.groupType">
            `group_type` = #{vo.groupType},
        </if>
        <if test="null != vo.groupUrl and '' != vo.groupUrl">
            `group_url` = #{vo.groupUrl},
        </if>
        `update_cycle` = #{vo.updateCycle},
        `sequence` = #{vo.sequence},
        `indicator_type` = #{vo.indicatorType},
        `plan_update_date` = #{vo.planUpdateDate},
        `is_delete` = 0,
        `is_show` = #{vo.isShow},
        `is_screen` = #{vo.isScreen},
        `is_legend` = #{vo.isLegend},
        `data_config_id` = #{vo.dataConfigId},
        `data_update_mode` = #{vo.dataUpdateMode}
        WHERE
        `id` = #{vo.id};
    </update>

    <select id="getIdsByParentId" resultType="java.lang.String">
       SELECT
            id
        FROM
            general_indicator
        WHERE
            parent_id = #{parentId}
            AND source_id = #{sourceId}
            AND is_delete = 0
            AND indicator_type = 0
        ORDER BY
        sequence,
        update_time
    </select>

    <select id="getIdsByParentIdPage" resultType="java.lang.String">
       SELECT
            id
        FROM
            general_indicator
        WHERE
            parent_id = #{parentId}
            AND source_id = #{sourceId}
            AND is_delete = 0
            AND is_delete = 0
            AND indicator_type = 0
        ORDER BY
        sequence,
        update_time
    </select>

    <select id="getDataListByIndicatorId"
            resultType="com.onecity.os.inform.zhibiao.model.dto.IndicatorDataReBean">
       SELECT
        id as id,
        indicator_id as indicatorId,
        indicator_name as indicatorName,
        item_name as itemName,
        item_value as itemValue,
        item_value1 as itemValue1,
        item_value2 as itemValue2,
        item_value3 as itemValue3,
        item_value4 as itemValue4,
        item_value5 as itemValue5,
        item_value6 as itemValue6,
        item_unit as itemUnit,
        item_unit_2nd as itemUnit2nd,
        identify as identify,
        style as style,
        is_fold as isFold,
        sequence as sequence,
        create_time as createTime,
        creater as creater,
        update_time as updateTime,
        updater as updater,
        update_date as updateDate,
        is_delete as isDelete,
        current_flag as currentFlag
        FROM
            general_indicator_data
        WHERE
            indicator_id = #{id}
            AND is_delete = 0
        ORDER BY
        sequence,
        update_time
    </select>

    <update id="updateIndicatorSourceAndDate"
            parameterType="com.onecity.os.inform.zhibiao.model.dto.IndicatorDataDto">
        UPDATE
        general_indicator
        SET
        `source_name` = #{vo.source},
        `update_date` = #{vo.updateDate},
        `updater` = #{vo.updater},
        `update_time` = NOW()
        WHERE
        id = #{vo.treeId};
    </update>

    <select id="getDataIdsByIndicatorId" resultType="java.lang.Long">
       SELECT
            id
        FROM
            general_indicator_data
        WHERE
            indicator_id = #{indicatorId}
            AND is_delete = 0
        ORDER BY
        sequence,
        update_time
    </select>

    <select id="getDataIdsByIndicatorIdPage" resultType="java.lang.Long">
       SELECT
            id
        FROM
            general_indicator_data
        WHERE
            indicator_id = #{indicatorId}
            AND is_delete = 0
        ORDER BY
        sequence,
        update_time
    </select>

    <update id="deleteIndicatorDtaByIds">
        UPDATE
        general_indicator_data
        SET is_delete = 1,
        update_time = NOW( ),
        updater = #{userName}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="insertIndicatorData">
        INSERT INTO general_indicator_data (
        `id`,
        `indicator_id`,
        `indicator_name`,
        `item_name`,
        `item_value`,
        `item_value1`,
        `item_value2`,
        `item_value3`,
        `item_value4`,
        `item_value5`,
        `item_value6`,
        `item_unit`,
        `item_unit_2nd`,
        `identify`,
        `style`,
        `is_fold`,
        `sequence`,
        `update_date`,
        `current_flag`,
        `is_delete`,
        `create_time`,
        `creater`,
        `update_time`,
        `updater`
        )
        VALUES
        (
		NULL,
        #{vo.indicatorId},
        #{vo.indicatorName},
        #{vo.itemName},
        #{vo.itemValue},
        #{vo.itemValue1},
        #{vo.itemValue2},
        #{vo.itemValue3},
        #{vo.itemValue4},
        #{vo.itemValue5},
        #{vo.itemValue6},
        #{vo.itemUnit},
        #{vo.itemUnit2nd},
        #{vo.identify},
        #{vo.style},
        #{vo.isFold},
        #{vo.sequence},
        #{vo.updateDate},
        #{vo.currentFlag},
        0,
        #{vo.createTime},
        #{vo.creater},
        #{vo.updateTime},
        #{vo.updater}
        );
    </insert>
    <insert id="insertIndicatorDataSelective">
        INSERT INTO ${dataTableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vo.id != null  ">
                id,
            </if>
            <if test="vo.indicatorId != null and vo.indicatorId != '' ">
                indicator_id,
            </if>
            <if test="vo.indicatorName != null and vo.indicatorName != '' ">
                indicator_name,
            </if>
            <if test="vo.itemName != null and vo.itemName != '' ">
                item_name,
            </if>
            <if test="vo.itemValue != null and vo.itemValue != '' ">
                item_value,
            </if>
            <if test="vo.itemUnit != null and vo.itemUnit != '' ">
                item_unit,
            </if>
            <if test="vo.identify != null and vo.identify != '' ">
                identify,
            </if>
            <if test="vo.style != null  ">
                style,
            </if>
            <if test="vo.isFold != null  ">
                is_fold,
            </if>
            <if test="vo.sequence != null  ">
                sequence,
            </if>
            <if test="vo.updateDate != null  and vo.updateDate != '' ">
                update_date,
            </if>
            <if test="vo.currentFlag != null ">
                current_flag,
            </if>
            <if test="vo.isDelete != null   ">
                is_delete,
            </if>
            <if test="vo.createTime != null   ">
                create_time,
            </if>
            <if test="vo.creater != null  and vo.creater != '' ">
                creater,
            </if>
            <if test="vo.updateTime != null ">
                update_time,
            </if>
            <if test="vo.updater != null  and vo.updater != '' ">
                updater,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vo.id != null  ">
                #{vo.id}
            </if>
            <if test="vo.indicatorId != null and vo.indicatorId != '' ">
                #{vo.indicatorId},
            </if>
            <if test="vo.indicatorName != null and vo.indicatorName != '' ">
                #{vo.indicatorName},
            </if>
            <if test="vo.itemName != null and vo.itemName != '' ">
                #{vo.itemName},
            </if>
            <if test="vo.itemValue != null and vo.itemValue != '' ">
                #{vo.itemValue},
            </if>
            <if test="vo.itemUnit != null and vo.itemUnit != '' ">
                #{vo.itemUnit},
            </if>
            <if test="vo.identify != null and vo.identify != '' ">
                #{vo.identify},
            </if>
            <if test="vo.style != null  ">
                #{vo.style},
            </if>
            <if test="vo.isFold != null  ">
                #{vo.isFold},
            </if>
            <if test="vo.sequence != null  ">
                #{vo.sequence},
            </if>
            <if test="vo.updateDate != null  and vo.updateDate != '' ">
                #{vo.updateDate},
            </if>
            <if test="vo.currentFlag != null ">
                #{vo.currentFlag},
            </if>
            <if test="vo.isDelete != null   ">
                #{vo.isDelete},
            </if>
            <if test="vo.createTime != null   ">
                #{vo.createTime},
            </if>
            <if test="vo.creater != null  and vo.creater != '' ">
                #{vo.creater},
            </if>
            <if test="vo.updateTime != null ">
                #{vo.updateTime},
            </if>
            <if test="vo.updater != null  and vo.updater != '' ">
                #{vo.updater},
            </if>
        </trim>
    </insert>
    <insert id="insertIndicatorDataBatch">
        INSERT INTO ${dataTableName}
        (indicator_id, indicator_name, item_name, item_value,item_unit,
        identify,style,is_fold,sequence,create_time,creater,update_time,updater,
        update_date,is_delete,current_flag)
        values
        <foreach collection="indicatorDataList" item="indicatorData" separator="," close=";">
            (
            #{indicatorData.indicatorId},
            #{indicatorData.indicatorName},
            #{indicatorData.itemName},
            #{indicatorData.itemValue},
            #{indicatorData.itemUnit},
            #{indicatorData.identify},
            #{indicatorData.style},
            #{indicatorData.isFold},
            #{indicatorData.sequence},
            #{indicatorData.createTime},
            #{indicatorData.creater},
            #{indicatorData.updateTime},
            #{indicatorData.updater},
            #{indicatorData.updateDate},
            #{indicatorData.isDelete},
            #{indicatorData.currentFlag}
            )
        </foreach>
    </insert>
    <update id="updateIndicatorDataById">
        UPDATE
        general_indicator_data
        SET
        `indicator_id` = #{vo.indicatorId},
        `indicator_name` = #{vo.indicatorName},
        `item_name` = #{vo.itemName},
        `item_value` = #{vo.itemValue},
        `item_value1` = #{vo.itemValue1},
        `item_value2` = #{vo.itemValue2},
        `item_value3` = #{vo.itemValue3},
        `item_value4` = #{vo.itemValue4},
        `item_value5` = #{vo.itemValue5},
        `item_value6` = #{vo.itemValue6},
        `item_unit` = #{vo.itemUnit},
        `item_unit_2nd` = #{vo.itemUnit2nd},
        `identify` = #{vo.identify},
        `style` = #{vo.style},
        `is_fold` = #{vo.isFold},
        `sequence` = #{vo.sequence},
        `update_date` = #{vo.updateDate},
        `current_flag` = #{vo.currentFlag},
        <if test="null != vo.updater and '' != vo.updater">
            `updater` = #{vo.updater},
        </if>
        `update_time` = #{vo.updateTime}
        WHERE
        `id` = #{vo.id}
    </update>

    <update id="deleteIndicatorDataByIds">
        UPDATE
        general_indicator_data
        SET
        `is_delete` = 1,
        `update_time` = NOW(),
        `updater` = #{userName}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getParentIdById" resultType="String">
        SELECT parent_id FROM general_indicator WHERE is_delete=0 AND id =#{id} LIMIT 1
    </select>

    <update id="MoveIndicatorVo">
        UPDATE general_indicator SET parent_id = #{vo.id}, source_id = #{vo.sourceId} ,source_name =
        #{vo.sourceName},
        updater=#{userName}, update_time=NOW() WHERE is_delete = 0 AND id IN
        <foreach collection="vo.ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>



    <select id="getIndicatorDataListBySourceIds"
            resultType="com.onecity.os.inform.zhibiao.entity.GeneralIndicatorDataTianbao">
        SELECT
        <include refid="Base_Column_List_data"/>
        FROM
        general_indicator_data
        LEFT JOIN general_indicator b ON b.id = general_indicator_data.indicator_id
        WHERE
        b.source_id IN
        <foreach collection="sources" item="source" open="(" separator="," close=")">
            #{source}
        </foreach>
        order by general_indicator_data.id
    </select>

    <select id="getIndicatorListBySourceIds"
            resultType="com.onecity.os.inform.zhibiao.entity.GeneralIndicatorTianbao">
        SELECT
        id as id,
        indicator_name as indicatorName,
        indicator_exhibit_type as indicatorExhibitType,
        parent_id as parentId,
        parent_name as parentName,
        icon_url as iconUrl,
        source_id as sourceId,
        source_name as sourceName,
        sequence as sequence,
        indicator_type as indicatorType,
        update_date as updateDate,
        update_cycle as updateCycle,
        leader as leader,
        is_delete as isDelete,
        create_time as createTime,
        creater as creater,
        update_time as updateTime,
        updater as updater,
        group_type as groupType,
        group_url as groupUrl,
        is_show as isShow,
        is_screen as isScreen,
        is_legend as isLegend,
        data_update_mode as dataUpdateMode,
        data_config_id as dataConfigId
        FROM
        general_indicator
        WHERE
        source_id IN
        <foreach collection="sources" item="source" open="(" separator="," close=")">
            #{source}
        </foreach>
        order by id
    </select>

    <select id="getPlanUpdateById" resultType="String">
        SELECT
            plan_update_date
        FROM
            general_indicator
        WHERE
            is_delete = 0
            AND id = #{id}
            LIMIT 1
    </select>



</mapper>





















