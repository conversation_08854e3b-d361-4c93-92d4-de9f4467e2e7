<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.modules.inform.mapper.InformContentDataMapper">

    <insert id="insertContentData"
            parameterType="com.onecity.os.inform.modules.inform.entity.InformContentData">
        INSERT INTO inform_content_data (id, content_id, type1, content, order_num, pic_url, indicator_pic_url,
                                         indicator,indicator_exhibit_type, data_config_type)
        VALUES (#{informContentData.id}, #{informContentData.contentId}, #{informContentData.type1}, #{informContentData.content},
                #{informContentData.orderNum}, #{informContentData.picUrl}, #{informContentData.indicatorPicUrl},
                #{informContentData.indicator}, #{informContentData.indicatorExhibitType}, #{informContentData.dataConfigType})
    </insert>
    <delete id="delContentDataByContentId" parameterType="java.lang.String">
        DELETE FROM inform_content_data where content_id = #{contentId}
    </delete>

    <select id="queryInformContentData"
            resultType="com.onecity.os.inform.modules.inform.entity.InformContentData">
        SELECT ic.id,
               ic.content_id             AS contentId,
               ic.type1                  AS type1,
               ic.content                AS content,
               ic.order_num              AS orderNum,
               ic.pic_url                AS picUrl,
               ic.indicator_pic_url      AS indicatorPicUrl,
               ic.indicator_exhibit_type AS indicatorExhibitType,
               ic.data_config_type       AS dataConfigType,
               ic.indicator
        FROM inform_content_data AS ic
        WHERE 1 = 1
          AND ic.content_id = #{contentId}
        ORDER BY order_num
    </select>
</mapper>