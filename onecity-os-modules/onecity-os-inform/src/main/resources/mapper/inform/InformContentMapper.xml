<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.modules.inform.mapper.InformContentMapper">

    <insert id="insertContent" parameterType="com.onecity.os.inform.modules.inform.entity.InformContent"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO inform_content(id, inform_id, creator, creator_name, create_time, state, type, title,
                                   content, `order`, indicator, indicator_pic_url, indicator_exhibit_type,
                                   data_config_type, backup)
        VALUES (#{informContent.id}, #{informContent.informId}, #{informContent.creator}, #{informContent.creatorName},
                #{informContent.createTime}, #{informContent.state}, #{informContent.type},
                #{informContent.title}, #{informContent.content}, #{informContent.order}, #{informContent.indicator},
                #{informContent.indicatorPicUrl}, #{informContent.indicatorExhibitType},
                #{informContent.dataConfigType},
                #{informContent.backup})
    </insert>

    <select id="queryContentByInformId"
            resultType="com.onecity.os.inform.modules.inform.entity.InformContent">
        SELECT ic.id,
               ic.inform_id              AS informId,
               ic.type                   AS type,
               ic.title                  AS title,
               ic.content                AS content,
               ic.indicator,
               ic.indicator_pic_url      AS indicatorPicUrl,
               ic.indicator_exhibit_type AS indicatorExhibitType,
               ic.data_config_type       AS dataConfigType,
               ic.`order`,
               ic.backup                 AS backup
        FROM inform_content AS ic
        WHERE 1 = 1
          AND ic.inform_id = #{informId}
          AND ic.state != "DELETED"
        ORDER BY `order`
    </select>

    <select id="queryContentByIndicatorId"
            resultType="com.onecity.os.inform.modules.inform.entity.InformContent">
        SELECT ic.id,
               ic.inform_id              AS informId,
               ic.type                   AS type,
               ic.title                  AS title,
               ic.content                AS content,
               ic.indicator,
               ic.indicator_pic_url      AS indicatorPicUrl,
               ic.indicator_exhibit_type AS indicatorExhibitType,
               ic.data_config_type       AS dataConfigType,
               ic.`order`,
               ic.backup                 AS backup
        FROM inform_content AS ic
        WHERE 1 = 1
          AND ic.indicator = #{indicatorId}
          AND ic.state != "DELETED" AND ic.state != "SUBMIT"
    </select>

    <update id="deleteContentByInformId" parameterType="String">
        UPDATE inform_content
        set state = "DELETED"
        WHERE inform_id = #{informId}
    </update>

    <update id="updateIndExhibitType" parameterType="String">
        UPDATE inform_content
        set indicator_exhibit_type = #{type}
        WHERE id = #{id}
    </update>

</mapper>