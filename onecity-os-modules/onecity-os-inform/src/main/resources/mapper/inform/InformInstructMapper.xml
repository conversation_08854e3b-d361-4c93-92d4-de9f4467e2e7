<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.modules.inform.mapper.InformInstructMapper">


    <select id="selectList" resultType="InstructionsVo">
        SELECT i.simple_title title,i.type,i.release_time releaseTime,ii.operation_time operationTime,
        ii.operator_name operatorName,ii.file_url fileUrl FROM inform_instruct ii
        left join inform i on ii.inform_id = i.id
        <where>
            <if test="title!=null and title!=''">
                and INSTR(i.simple_title, #{title})
            </if>
            <if test="type!=null and type!=''">
                and i.type = #{type}
            </if>
            <if test="time!=null and time!=''">
                and DATE_FORMAT(ii.operation_time,'%Y-%m-%d') = #{time}
            </if>
        </where>
        ORDER BY ii.operation_time DESC
    </select>

    <select id="reportList" resultType="ReportAppVo">
        SELECT i.id informId,i.simple_title title,i.type,i.release_time releaseTime,ri.`read`,
        (CASE
             WHEN i.state = 'ARCHIVED' THEN 3
               ELSE ri.`read` END) as orderNum
        FROM inform i
        LEFT JOIN recipient_info ri ON i.id = ri.inform_id
        WHERE CASE
              WHEN ri.continue_push_flag = 1 then i.state in ('PUBLISH','PUSH_PUBLISH')
              WHEN ri.continue_push_flag = 0 OR ri.continue_push_flag is NULL then i.state in ('PUBLISH','ARCHIVED','PUSH_AUDIT','PUSH_PUBLISH','PUSH_OVERRULE')
              END
        AND ri.recipient = #{userId} AND ri.state = 'ENABLE' AND ri.inform_flag = 1
        ORDER BY orderNum,i.release_time DESC
    </select>

    <select id="myInstructionList" resultType="InstructionsVo">
        SELECT i.simple_title title,i.type,i.release_time releaseTime,ii.operation_time operationTime,
        ii.operator_name operatorName,ii.file_url fileUrl FROM inform_instruct ii
        left join inform i on ii.inform_id = i.id
        WHERE ii.operator = #{userId} AND ii.state != 'DELETED'
        ORDER BY ii.operation_time DESC
    </select>

    <insert id="addInstruction" parameterType="InformInstruct">
        insert into inform_instruct
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="informId != null">inform_id,</if>
            <if test="operator != null">operator,</if>
            <if test="operatorName != null">operator_name,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="backup != null">backup,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="informId != null">#{informId},</if>
            <if test="operator != null">#{operator},</if>
            <if test="operatorName != null">#{operatorName},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="backup != null">#{backup},</if>
        </trim>
    </insert>

    <update id="readInform">
        update recipient_info SET `read` = 1
        where inform_id = #{informId} and recipient = #{userId} AND state = 'ENABLE'
    </update>

    <select id="getReadByUserId" resultType="Integer">
        SELECT `read`  FROM recipient_info
        where inform_id = #{informId} and recipient = #{userId} AND state = 'ENABLE'
    </select>

    <update id="instructInform">
        update recipient_info SET `read` = 2
        where inform_id = #{informId} and recipient = #{userId}
    </update>

    <select id="getByUserIdAndInformId" resultType="com.onecity.os.inform.modules.inform.dto.InstructionDto">
        SELECT id as instructionId,operation_time as operationTime,state,file_url fileUrl  FROM inform_instruct
        WHERE operator = #{userId} and inform_id = #{informId} and state != 'DELETED'
        ORDER BY operation_time DESC LIMIT 1
    </select>
    <!-- 获取批示总数 -->
    <select id="queryAllInformCount" resultType="long">
        select count(1) from inform_instruct where state != 'DELETED' and operation_time &lt; #{today}
    </select>
    <!-- 获取每日批示数 -->
    <select id="queryDailyInstructionsCount" resultType="com.onecity.os.inform.modules.inform.vo.DailyDataVo">
        select DATE_FORMAT(operation_time, '%Y-%m-%d') as date
        ,DATE_FORMAT(operation_time, '%m-%d') as abbrDate, count(*) num from inform_instruct where state != 'DELETED'
                and   DATE_FORMAT(operation_time, '%Y-%m-%d') &gt;= #{dayStart} and DATE_FORMAT(operation_time, '%Y-%m-%d') &lt;= #{dayEnd}
        GROUP BY date
        ORDER BY date
    </select>

    <update id="delByInformId" parameterType="String">
        UPDATE inform_instruct
        set state = 'DELETED'
        WHERE inform_id = #{informId}
    </update>
</mapper>