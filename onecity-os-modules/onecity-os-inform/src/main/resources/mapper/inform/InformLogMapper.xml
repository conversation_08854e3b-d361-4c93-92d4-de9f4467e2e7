<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.modules.inform.mapper.InformLogMapper">


    <insert id="insertLog"
            parameterType="com.onecity.os.inform.modules.inform.entity.InformLog"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO inform_log(id, inform_id, content, operator, operator_name, operation_time, backup)
        VALUES (#{log.id}, #{log.informId}, #{log.content}, #{log.operator}, #{log.operatorName},
                #{log.operationTime}, #{log.backup})
    </insert>

    <select id="getLogListById" resultType="InformLog">
        select id, inform_id informId, content, operator, operator_name operatorName, operation_time operationTime, backup from inform_log
        where inform_id = #{informId}
        ORDER BY operationTime DESC
    </select>

    <delete id="deleteLastLog" parameterType="String">
        DELETE
        FROM inform_log
        WHERE 1 = 1
          AND inform_id = #{informId}
          AND content = #{content} ORDER BY operation_time DESC
          LIMIT 1
    </delete>

    <update id="deleteLog" parameterType="String">
        UPDATE inform_log
        SET is_delete = 1
        WHERE inform_id = #{informId}
    </update>
</mapper>