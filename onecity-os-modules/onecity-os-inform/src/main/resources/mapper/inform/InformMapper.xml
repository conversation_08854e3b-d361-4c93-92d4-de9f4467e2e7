<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.modules.inform.mapper.InformMapper">

    <insert id="insertInform" parameterType="com.onecity.os.inform.modules.inform.entity.InformInfo"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO inform(id, creator, creator_name, create_time, state, type, content, simple_title, title,
                           background_image, publish_time, updater, updater_name, update_time, backup,
                           process_id, current_step_id)
        VALUES (#{informInfo.id}, #{informInfo.creator}, #{informInfo.creatorName}, #{informInfo.createTime},
                #{informInfo.state}, #{informInfo.type}, #{informInfo.content}, #{informInfo.simpleTitle},
                #{informInfo.title}, #{informInfo.backgroundImage}, #{informInfo.publishTime}, #{informInfo.updater},
                #{informInfo.updaterName}, #{informInfo.updateTime}, #{informInfo.backup},
                #{informInfo.processId}, #{informInfo.currentStepId})
    </insert>

    <select id="queryByInformId" parameterType="String"
            resultType="com.onecity.os.inform.modules.inform.entity.InformInfo">
        SELECT i.id,
               i.creator          AS creator,
               i.creator_name     AS creatorName,
               i.create_time      AS createTime,
               i.state            AS state,
               i.type             AS type,
               i.content          AS content,
               i.simple_title     AS simpleTitle,
               i.title            AS title,
               i.background_image AS backgroundImage,
               i.publish_time     AS publishTime,
               i.release_time     AS releaseTime,
               i.updater          AS updater,
               i.updater_name     AS updaterName,
               i.update_time      AS updateTime,
               i.backup           AS backup,
               i.process_id       AS processId,
               i.current_step_id  AS currentStepId
        FROM inform AS i
        WHERE 1 = 1
          AND i.id = #{informId}
    </select>

    <update id="updateInform" parameterType="com.onecity.os.inform.modules.inform.entity.InformInfo">
        UPDATE inform
        <trim prefix="SET" suffixOverrides=",">
            <if test="informInfo.state != null  and informInfo.state != ''">
                state = #{informInfo.state},
            </if>
            <if test="informInfo.type != null  and informInfo.type != ''">
                type = #{informInfo.type},
            </if>
            <if test="informInfo.content != null  and informInfo.content != ''">
                content = #{informInfo.content},
            </if>
            <if test="informInfo.simpleTitle != null  and informInfo.simpleTitle != ''">
                simple_title = #{informInfo.simpleTitle},
            </if>
            <if test="informInfo.title != null  and informInfo.title != ''">
                title = #{informInfo.title},
            </if>
            <if test="informInfo.backgroundImage != null  and informInfo.backgroundImage != ''">
                background_image = #{informInfo.backgroundImage},
            </if>
                publish_time = #{informInfo.publishTime},
            <if test="informInfo.updater != null">
                updater = #{informInfo.updater},
            </if>
            <if test="informInfo.updaterName != null">
                updater_name = #{informInfo.updaterName},
            </if>
            <if test="informInfo.updateTime!= null">
                update_time = #{informInfo.updateTime},
            </if>
            <if test="informInfo.processId != null">
                process_id = #{informInfo.processId},
            </if>
            <if test="informInfo.currentStepId != null">
                current_step_id = #{informInfo.currentStepId},
            </if>
        </trim>
        where id = #{informInfo.id}
    </update>

    <select id="queryInformByParam" resultType="com.onecity.os.inform.modules.inform.entity.InformInfo">
        SELECT
        i.id,
        i.creator AS creator,
        i.creator_name AS creatorName,
        i.create_time AS createTime,
        i.state AS state,
        i.type AS type,
        i.content AS content,
        i.simple_title AS simpleTitle,
        i.title AS title,
        i.background_image AS backgroundImage,
        i.publish_time AS publishTime,
               i.release_time AS releaseTime,
        i.updater AS updater,
        i.updater_name AS updaterName,
        i.update_time AS updateTime,
        i.backup AS backup
        FROM inform AS i
        WHERE 1 = 1
        AND i.state != "DELETED"
        <if test="null != inform.title and '' != inform.title">
            AND INSTR(i.simple_Title,#{inform.title})
        </if>
        <if test="null != inform.type and '' != inform.type">
            AND i.type = #{inform.type}
        </if>
        <if test="null != inform.state and '' != inform.state">
            AND i.state = #{inform.state}
        </if>
        <if test="inform.createDate != null and inform.createDate != ''">
            AND date_format(i.create_time,'%Y-%m-%d') = #{inform.createDate}
        </if>
        <if test="inform.submitDate != null and inform.submitDate != ''">
            AND date_format(i.publish_time,'%Y-%m-%d') = #{inform.submitDate}
        </if>
        ORDER BY i.create_time DESC
    </select>

    <select id="queryInformByRole" resultType="com.onecity.os.inform.modules.inform.entity.InformInfo">
        SELECT
        i.id,
        i.creator AS creator,
        i.creator_name AS creatorName,
        i.create_time AS createTime,
        i.state AS state,
        i.type AS type,
        i.content AS content,
        i.simple_title AS simpleTitle,
        i.title AS title,
        i.background_image AS backgroundImage,
        i.publish_time AS publishTime,
        i.release_time AS releaseTime,
        i.updater AS updater,
        i.updater_name AS updaterName,
        i.update_time AS updateTime,
        i.backup AS backup
        FROM inform AS i
        LEFT JOIN inform_role_info AS ir ON ir.inform_id = i.id
        WHERE 1 = 1
        AND i.state != "DELETED"
        <if test="null != inform.title and '' != inform.title">
            AND INSTR(i.simple_Title,#{inform.title})
        </if>
        <if test="null != inform.type and '' != inform.type">
            AND i.type = #{inform.type}
        </if>
        <if test="null != inform.state and '' != inform.state">
            AND i.state = #{inform.state}
        </if>
        <if test="inform.createDate != null and inform.createDate != ''">
            AND date_format(i.create_time,'%Y-%m-%d') = #{inform.createDate}
        </if>
        <if test="inform.submitDate != null and inform.submitDate != ''">
            AND date_format(i.publish_time,'%Y-%m-%d') = #{inform.submitDate}
        </if>
        AND ir.role_id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        GROUP BY i.id
        ORDER BY i.create_time DESC
    </select>

    <select id="queryProcessingByProcessId" resultType="com.onecity.os.inform.modules.inform.entity.InformInfo"
            parameterType="java.lang.String">
        SELECT * from inform WHERE process_id = #{processId} and (state = 'AUDIT' OR state = 'PUSH_AUDIT') limit 1;
    </select>

    <update id="updateStateById">
        update inform SET state = #{state}
        where id = #{informId}
    </update>

    <update id="removePublishTimeById">
        update inform SET publish_time = null
        where id = #{informId}
    </update>

    <update id="updatePublishTimeById">
        UPDATE inform SET publish_time = NOW()
        WHERE id = #{informId}
    </update>

    <update id="updateReleaseTimeById">
        update inform SET release_time = #{releaseTime}
        where id = #{informId}
    </update>

    <update id="updateFirstReleaseTimeById">
        update inform SET first_release_time = #{firstReleaseTime}
        where id = #{informId}
    </update>

    <update id="updateProcessStepById">
        update inform SET current_step_id = #{processStepId}
        where id = #{informId}
    </update>

    <!-- 获取呈报总数 -->
    <select id="queryAllInformCount" resultType="long">
        <!-- 统计已发布PUBLISH、已归档ARCHIVED、续推审核中PUSH_AUDIT、续推已发布PUSH_PUBLISH、续推被驳回状态PUSH_OVERRULE的呈报总数 -->
        select count(1) from inform where state in ('PUBLISH','ARCHIVED','PUSH_AUDIT','PUSH_PUBLISH','PUSH_OVERRULE') and first_release_time &lt; #{today}
    </select>
    <!-- 获取每日呈报数 -->
    <select id="queryDailyInformCount" resultType="com.onecity.os.inform.modules.inform.vo.DailyDataVo">
        select DATE_FORMAT(first_release_time, '%Y-%m-%d') as date
        ,DATE_FORMAT(first_release_time, '%m-%d') as abbrDate, count(*) num from inform where state != 'DELETED'
        and   DATE_FORMAT(first_release_time, '%Y-%m-%d')  &gt;= #{dayStart} and DATE_FORMAT(first_release_time, '%Y-%m-%d')  &lt;= #{dayEnd}
        GROUP BY date
        ORDER BY date;
    </select>
</mapper>