<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.modules.inform.mapper.InformRoleInfoMapper">

    <insert id="insert" parameterType="com.onecity.os.inform.modules.inform.entity.InformRoleInfo"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO inform_role_info(id, inform_id, create_time, role_id, backup)
        VALUES (#{role.id}, #{role.informId}, #{role.createTime}, #{role.roleId}, #{role.backup})
    </insert>

</mapper>