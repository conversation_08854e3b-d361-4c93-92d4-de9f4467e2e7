<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.modules.inform.mapper.InformRoleStateMapper">
    <insert id="insert" parameterType="com.onecity.os.inform.modules.inform.entity.InformRoleState"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO inform_role_state (id, inform_id, role_id, process_id, current_step_id, state)
        VALUES (#{informRoleState.id}, #{informRoleState.informId}, #{informRoleState.roleId},
                #{informRoleState.processId}, #{informRoleState.currentStepId}, #{informRoleState.state})
    </insert>

    <update id="updatePyParam" parameterType="String">
        UPDATE inform_role_state
        set state = #{state}
        WHERE inform_id = #{informId}
        <if test="roleId != null and roleId != ''">
            AND role_id = #{roleId}
        </if>
        AND process_id = #{processId}
        AND current_step_id = #{currentStepId}
    </update>

    <update id="updatePyInformId" parameterType="String">
        UPDATE inform_role_state
        set state = #{state}
        WHERE inform_id = #{informId}
    </update>

    <select id="queryByInformId" resultType="com.onecity.os.inform.modules.inform.entity.InformRoleState"
            parameterType="String">
        SELECT id,
               inform_id       AS informId,
               role_id         AS roleId,
               process_id      AS processId,
               current_step_id AS currentStepId,
               state
        FROM inform_role_state
        WHERE inform_id = #{informId}
    </select>

    <select id="queryByParam" resultType="com.onecity.os.inform.modules.inform.entity.InformRoleState"
            parameterType="String">
        SELECT id,
        inform_id AS informId,
        role_id AS roleId,
        process_id AS processId,
        current_step_id AS currentStepId,
        state
        FROM inform_role_state
        WHERE inform_id = #{informId}
        <if test="null != roleId ">
            AND role_id = #{roleId}
        </if>
        <if test="null != processId ">
            AND process_id = #{processId}
        </if>
        <if test="null != currentStepId ">
            AND current_step_id = #{currentStepId}
        </if>
        <if test="null != state ">
            AND state = #{state}
        </if>
    </select>

</mapper>