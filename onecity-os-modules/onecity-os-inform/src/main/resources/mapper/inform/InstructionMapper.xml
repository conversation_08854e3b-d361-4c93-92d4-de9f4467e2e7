<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.modules.inform.mapper.InstructionMapper">

    <select id="informList" resultType="com.onecity.os.inform.modules.inform.dto.InformDto">
        SELECT i.id informId,i.simple_title title,i.type,i.release_time releaseTime,i.state FROM inform i
        WHERE i.state in ('PUBLISH','PUSH_PUBLISH','ARCHIVED','PUSH_OVERRULE','PUSH_AUDIT' )
        <if test="null != title and '' != title">
            AND INSTR(i.simple_Title,#{title})
        </if>
        <if test="null != type and '' != type">
            AND i.type = #{type}
        </if>
        <if test="time != null and time != ''">
            AND date_format(i.release_time,'%Y-%m-%d') = #{time}
        </if>
        ORDER BY i.release_time DESC
    </select>

    <select id="getOperationTime" resultType="String">
        SELECT operation_time FROM inform_instruct WHERE inform_id = #{informId} ORDER BY operation_time DESC LIMIT 1
    </select>

    <select id="countReadNum" resultType="java.lang.Integer">
        SELECT count(*) FROM recipient_info
        WHERE inform_id = #{informId} AND state = 'ENABLE' AND `read` != 0
    </select>

    <select id="countOperationNum" resultType="java.lang.Integer">
        SELECT count(*) FROM inform_instruct
        WHERE inform_id = #{informId}
    </select>

    <select id="instructionList" resultType="com.onecity.os.inform.modules.inform.dto.InstructionDto">
        SELECT  ri.recipient,ri.recipient_name recipientName,i.release_time releaseTime,ri.`read` as state FROM recipient_info ri
        LEFT JOIN inform i ON i.id = ri.inform_id
		WHERE ri.inform_id = #{informId} AND ri.state = 'ENABLE' AND ri.inform_flag = 1
		ORDER BY CONVERT(ri.recipient_name USING gbk) COLLATE gbk_chinese_ci ASC
    </select>

    <select id="transferList" resultType="com.onecity.os.inform.modules.inform.vo.TransferVo">
        SELECT ti.id transferId,ii.operator operatorUserId,ii.operator_name operatorName,ii.operation_time operationTime,ti.recipient_name recipientName,
        ti.state1,ti.feed_time feedTime,ii.state FROM transfer_instruct ti
        LEFT JOIN inform_instruct ii ON ti.instruct_id = ii.id
        WHERE ti.inform_id = #{informId}
        ORDER BY CONVERT(ii.operator_name USING gbk) COLLATE gbk_chinese_ci ASC,CONVERT(ti.recipient_name USING gbk) COLLATE gbk_chinese_ci ASC
    </select>

    <select id="getInterfaceUser"  resultType="int">
		SELECT count(1) FROM interface_user_info
		where user_id = #{userId} and interface_user_id = #{interfaceUserId}
	</select>

    <update id="informArchive">
        update inform SET state = 'ARCHIVED'
        where id = #{informId}
    </update>

    <update id="instructionArchiveByInform">
        update inform_instruct SET state = 'ARCHIVED'
        where inform_id = #{informId}
    </update>

    <update id="updateRecipientByInform">
        update recipient_info SET `read` = 3
        where inform_id = #{informId}
    </update>

    <update id="instructionArchive">
        update inform_instruct SET state = 'ARCHIVED'
        where id = #{instructionId}
    </update>

    <select id="getInstructionDetails" resultType="com.onecity.os.inform.modules.inform.vo.InstructionsVo">
        SELECT i.simple_title title,i.type,i.release_time releaseTime,ii.operation_time operationTime,
        ii.operator_name operatorName,ii.file_url fileUrl FROM inform_instruct ii
        left join inform i on ii.inform_id = i.id
        WHERE ii.id = #{instructionId}
    </select>

    <select id="getTransferDetails" resultType="com.onecity.os.inform.modules.inform.vo.TransferVo">
        SELECT ii.operator_name operatorName,ii.operation_time operationTime,ii.id instructId,ii.file_url fileUrl,
        ti.transfer_name transferName,ti.transfer_time transferTime,ti.transfer_backup transferBackup,ii.state,
        ti.recipient_name recipientName,ti.feed_time feedTime,ti.feed_backup feedBackup,ti.state1,ti.transfer_id transferId
        FROM transfer_instruct ti
        LEFT JOIN inform_instruct ii ON ti.instruct_id = ii.id
        WHERE ti.id = #{transferId}
    </select>

    <insert id="insertTransfer" parameterType="com.onecity.os.inform.modules.inform.entity.TransferInstruct">
        INSERT INTO transfer_instruct(id, inform_id, instruct_id, transfer_id, transfer_name, transfer_backup,
        recipient_id, recipient_name, transfer_time,state1)
        VALUES (#{transferInstruct.id}, #{transferInstruct.informId}, #{transferInstruct.instructionId}, #{transferInstruct.transferId},
                #{transferInstruct.transferName}, #{transferInstruct.transferBackup}, #{transferInstruct.recipientId}, #{transferInstruct.recipientName},
                #{transferInstruct.transferTime}, #{transferInstruct.state1})
    </insert>

    <select id="myTransferList" resultType="com.onecity.os.inform.modules.inform.vo.TransferVo">
        SELECT ti.id transferId,i.simple_title title,i.type,ii.operator_name operatorName,ii.operation_time operationTime,
        ti.state1,ti.feed_time feedTime,ti.transfer_time transferTime,ii.state FROM transfer_instruct ti
        LEFT JOIN inform_instruct ii ON ti.instruct_id = ii.id
		LEFT JOIN inform i ON i.id = ti.inform_id
		where ti.recipient_id = #{userId}
        <if test="null != title and '' != title">
            AND INSTR(i.simple_Title,#{title})
        </if>
        <if test="null != type and '' != type">
            AND i.type = #{type}
        </if>
        <if test="operatorName != null and operatorName != ''">
            AND INSTR(ii.operator_name,#{operatorName})
        </if>
		ORDER BY ti.transfer_time DESC
    </select>

    <update id="feedUp">
        update transfer_instruct SET state1 = 1,feed_backup = #{feedBackup},feed_time = #{feedTime}
        where id = #{transferId}
    </update>

    <select id="interfaceUserList" resultType="com.onecity.os.inform.modules.inform.vo.InterfaceUserVo">
        SELECT user_id userId,nick_name nickName,dept_name deptName FROM interface_user_info
        where 1=1
        <if test="null != nickName and '' != nickName">
            AND INSTR(nick_name,#{nickName})
        </if>
        <if test="null != deptName and '' != deptName">
            AND INSTR(dept_name,#{deptName})
        </if>
        <if test="interfaceName != null and interfaceName != ''">
            AND INSTR(interface_nick_name,#{interfaceName})
        </if>
        GROUP BY user_id
        ORDER BY CONVERT(nick_name USING gbk) COLLATE gbk_chinese_ci ASC
    </select>

    <select id="interfaceUserListByUserId" resultType="com.onecity.os.inform.modules.inform.vo.InterfaceUserVo">
        SELECT interface_user_id userId,interface_nick_name nickName FROM interface_user_info
        where user_id = #{userId}
        ORDER BY user_id
    </select>

    <insert id="insertInterfaceUserVoList" parameterType="com.onecity.os.inform.modules.inform.entity.InterfaceUserInfo">
        insert into interface_user_info
        (user_id, nick_name, dept_name, interface_user_id, interface_nick_name)
        values
        <foreach collection="interfaceUserInfoList" item="item" separator=",">
            (#{item.userId}, #{item.nickName}, #{item.deptName}, #{item.interfaceUserId},
            #{item.interfaceNickName})
        </foreach>
    </insert>

    <delete id="deleteInterfaceUserByUserId">
        delete from interface_user_info where user_id = #{userId}
    </delete>

    <select id="getTransferIdByInformId" resultType="String">
        SELECT id FROM transfer_instruct WHERE inform_id = #{informId} AND state1 = 0
    </select>

    <select id="getTransferIdByInstructionId" resultType="String">
        SELECT id FROM transfer_instruct WHERE instruct_id = #{instructionId} AND state1 = 0
    </select>

    <delete id="deleteTransferByInformId">
        DELETE FROM transfer_instruct WHERE inform_id = #{informId}
    </delete>

    <select id="getInterfaceUserId" resultType="String">
        SELECT interface_user_id FROM interface_user_info WHERE user_id = #{userId}
    </select>
</mapper>