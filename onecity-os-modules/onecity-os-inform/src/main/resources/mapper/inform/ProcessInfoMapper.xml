<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.modules.inform.mapper.ProcessInfoMapper">

    <insert id="insertProcessInfo" parameterType="com.onecity.os.inform.modules.inform.entity.ProcessInfo">
        INSERT INTO process_info (id, process_name, creator_name, create_time, is_delete, content)
        VALUES (#{processInfo.id}, #{processInfo.processName}, #{processInfo.creatorName}, #{processInfo.createTime},
                0, #{processInfo.content})
    </insert>
    <update id="updateProcessInfo" parameterType="com.onecity.os.inform.modules.inform.entity.ProcessInfo">
        UPDATE process_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="processInfo.processName!= null  and processInfo.processName!= ''">
                process_name = #{processInfo.processName},
            </if>
            content = #{processInfo.content},
            <if test="processInfo.updaterName != null">
                updater_name = #{processInfo.updaterName},
            </if>
            <if test="processInfo.updateTime!= null">
                update_time = #{processInfo.updateTime},
            </if>
        </trim>
        WHERE id = #{processInfo.id} and is_delete = 0
    </update>
    <update id="del" parameterType="com.onecity.os.inform.modules.inform.entity.ProcessInfo">
        UPDATE process_info
        SET is_delete = 1,updater_name = #{processInfo.updaterName},update_time = #{processInfo.updateTime}
        WHERE id = #{processInfo.id} and is_delete = 0
    </update>
    <update id="updateProcessUpdateTime"
            parameterType="com.onecity.os.inform.modules.inform.entity.ProcessInfo">
        UPDATE process_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="processInfo.updaterName != null">
                updater_name = #{processInfo.updaterName},
            </if>
            <if test="processInfo.updateTime!= null">
                update_time = #{processInfo.updateTime},
            </if>
        </trim>
        WHERE id = #{processInfo.id} and is_delete = 0
    </update>
    <select id="queryProcessInfoById" resultType="com.onecity.os.inform.modules.inform.entity.ProcessInfo"
            parameterType="java.lang.String">
        SELECT
            id, process_name as processName, content, creator_name as creatorName,
            create_time as createTime, updater_name as updaterName, update_time as updateTime
        FROM process_info where id = #{processId} and is_delete = 0
    </select>
    <select id="queryProcessInfoList" resultType="com.onecity.os.inform.modules.inform.entity.ProcessInfo">
        SELECT id, process_name as processName, content, creator_name as creatorName,
               create_time as createTime, updater_name as updaterName, update_time as updateTime
        FROM process_info where is_delete = 0 order by create_time desc
    </select>
    <select id="queryProcessInfoByProcessName" resultType="com.onecity.os.inform.modules.inform.entity.ProcessInfo"
            parameterType="java.lang.String">
        SELECT id, process_name as processName, content, creator_name as creatorName,
               create_time as createTime, updater_name as updaterName, update_time as updateTime
        FROM process_info where process_name = #{processName} and is_delete = 0 limit 1
    </select>
    <select id="queryProcessInfoByProcessNameExcludeId"
            resultType="com.onecity.os.inform.modules.inform.entity.ProcessInfo">
        SELECT
            id, process_name as processName, content, creator_name as creatorName,
            create_time as createTime, updater_name as updaterName, update_time as updateTime
        FROM process_info where id != #{processId} and process_name = #{processName} and is_delete = 0
    </select>
</mapper>