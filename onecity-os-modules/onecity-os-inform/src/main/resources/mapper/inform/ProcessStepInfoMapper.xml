<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.modules.inform.mapper.ProcessStepInfoMapper">

    <insert id="insertProcessStepInfo"
            parameterType="com.onecity.os.inform.modules.inform.entity.ProcessStepInfo">
        INSERT INTO process_step_info (id, process_id, step_name, step_num, creator_name,
                                  create_time, is_delete, content)
        VALUES (#{processStepInfo.id}, #{processStepInfo.processId}, #{processStepInfo.stepName}, #{processStepInfo.stepNum},
                #{processStepInfo.creatorName}, #{processStepInfo.createTime}, 0, #{processStepInfo.content})
    </insert>

    <update id="updateProcessStepInfo"
            parameterType="com.onecity.os.inform.modules.inform.entity.ProcessStepInfo">
        UPDATE process_step_info
        <trim prefix="SET" suffixOverrides=",">
            step_name = #{processStepInfo.stepName},
            <if test="processStepInfo.stepNum!= null">
                step_num = #{processStepInfo.stepNum},
            </if>
            content = #{processStepInfo.content},
            <if test="processStepInfo.updaterName != null">
                updater_name = #{processStepInfo.updaterName},
            </if>
            <if test="processStepInfo.updateTime!= null">
                update_time = #{processStepInfo.updateTime},
            </if>
        </trim>
        WHERE id = #{processStepInfo.id} and is_delete = 0
    </update>
    <update id="updateStepNum">
        update process_step_info set step_num = #{stepNum}, update_time = #{updateTime}, updater_name = #{updaterName} where id = #{id} and is_delete = 0
    </update>
    <update id="delByProcessId" parameterType="com.onecity.os.inform.modules.inform.entity.ProcessStepInfo">
        UPDATE process_step_info
        SET is_delete = 1,updater_name = #{processStepInfo.updaterName},update_time = #{processStepInfo.updateTime}
        WHERE id = #{processStepInfo.processId} and is_delete = 0
    </update>

    <select id="queryProcessStepInfo" resultType="com.onecity.os.inform.modules.inform.entity.ProcessStepInfo"
            parameterType="java.lang.String">
        SELECT
            id, process_id as processId, step_name as stepName, step_num as stepNum, updater_name as updaterName,
               update_time as updateTime, creator_name as creatorName, create_time as createTime, content
        FROM process_step_info where id = #{processStepInfoId} and is_delete = 0;
    </select>

    <select id="queryProcessStepInfoList" resultType="com.onecity.os.inform.modules.inform.entity.ProcessStepInfo"
            parameterType="java.lang.String">
        SELECT
            id, process_id as processId, step_name as stepName, step_num as stepNum, updater_name as updaterName,
            update_time as updateTime, creator_name as creatorName, create_time as createTime, content
        FROM process_step_info WHERE process_id = #{processId} and is_delete = 0 order by step_num asc;
    </select>
    <select id="queryBiggerProcessStepInfoByStepNum"
            resultType="com.onecity.os.inform.modules.inform.entity.ProcessStepInfo">
        select
            id, process_id as processId, step_name as stepName, step_num as stepNum, updater_name as updaterName,
            update_time as updateTime, creator_name as creatorName, create_time as createTime, content
        from process_step_info
        where process_id = #{processId} and is_delete = 0 and step_num &gt; #{stepNum} order by step_num asc limit 1
    </select>
    <select id="querySmallerProcessStepInfoByStepNum"
            resultType="com.onecity.os.inform.modules.inform.entity.ProcessStepInfo">
        select
            id, process_id as processId, step_name as stepName, step_num as stepNum, updater_name as updaterName,
            update_time as updateTime, creator_name as creatorName, create_time as createTime, content
        from process_step_info
        where process_id = #{processId} and is_delete = 0 and step_num &lt; #{stepNum} order by step_num desc limit 1
    </select>

    <update id="del" parameterType="com.onecity.os.inform.modules.inform.entity.ProcessStepInfo">
        UPDATE process_step_info
        SET is_delete = 1,updater_name = #{processStepInfo.updaterName},update_time = #{processStepInfo.updateTime}
        WHERE id = #{processStepInfo.id} and is_delete = 0
    </update>
</mapper>