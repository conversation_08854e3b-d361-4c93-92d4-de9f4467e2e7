<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.modules.inform.mapper.PushUserMapper">
    <insert id="insertPushUser" parameterType="com.onecity.os.inform.modules.inform.entity.PushUser">
        INSERT INTO push_user (id, inform_id, process_id, is_delete, creator_name, create_time)
        values (#{pushUser.id}, #{pushUser.informId}, #{pushUser.processId}, 0, #{pushUser.creatorName}, #{pushUser.createTime})
    </insert>

    <update id="delPushUser" parameterType="String">
        UPDATE push_user
        SET is_delete = 1
        WHERE inform_id = #{informId}
    </update>

    <select id="queryByInformId" resultType="com.onecity.os.inform.modules.inform.entity.PushUser"
            parameterType="java.lang.String">
        SELECT id,
               inform_id    AS informId,
               process_id   AS processId,
               is_delete    AS isDelete,
               creator_name AS creatorName,
               create_time  AS createTime,
               update_time AS updateName,
               update_time AS updateTime
        FROM push_user
        WHERE inform_id = #{informId}
          AND is_delete = 0
    </select>
</mapper>