<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.modules.inform.mapper.RecipientMapper">

    <insert id="insertRecipient" parameterType="com.onecity.os.inform.modules.inform.entity.RecipientInfo"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO recipient_info(id, inform_id, state, recipient, recipient_name, department, create_time,
                                   `read`, backup, continue_push_flag, inform_flag)
        VALUES (#{recipient.id}, #{recipient.informId}, #{recipient.state}, #{recipient.recipient},
                #{recipient.recipientName}, #{recipient.department}, #{recipient.createTime}, #{recipient.read},
                #{recipient.backup}, #{recipient.continuePushFlag}, #{recipient.informFlag})
    </insert>

    <select id="queryRecipientByInformId"
            resultType="com.onecity.os.inform.modules.inform.entity.RecipientInfo">
        SELECT ic.id,
               ic.inform_id      AS informId,
               ic.recipient      AS recipient,
               ic.recipient_name AS recipientName,
               ic.department     AS department,
               ic.`read`,
               ic.inform_flag    AS informFlag,
               ic.inform_flag    AS submitted
        FROM recipient_info AS ic
        WHERE 1 = 1
          AND ic.inform_id = #{informId}
          AND ic.state != "DELETED"
    </select>

    <select id="queryRecipientByParam"
            resultType="com.onecity.os.inform.modules.inform.entity.RecipientInfo">
        SELECT ic.id,
               ic.inform_id      AS informId,
               ic.recipient      AS recipient,
               ic.recipient_name AS recipientName,
               ic.department     AS department,
               ic.`read`,
               ic.inform_flag    AS informFlag
        FROM recipient_info AS ic
        WHERE 1 = 1
          AND ic.inform_id = #{informId}
          AND ic.state != "DELETED"
          AND ic.inform_flag = 0
    </select>

    <update id="deleteRecipientByInformId" parameterType="String">
        UPDATE recipient_info
        SET state = "DELETED"
        WHERE inform_id = #{informId}
        <if test="informFlag != null ">
            AND inform_flag = #{informFlag}
        </if>
    </update>

    <insert id="insertRecipientList" parameterType="com.onecity.os.inform.modules.inform.entity.RecipientInfo">
        insert into recipient_info
        (id, inform_id, state, recipient, recipient_name, department, create_time,
         `read`, backup, continue_push_flag, inform_flag)
        values
        <foreach collection="recipientInfoList" item="item" separator=",">
            (#{item.id}, #{item.informId}, #{item.state}, #{item.recipient},
            #{item.recipientName}, #{item.department}, #{item.createTime},
            #{item.read}, #{item.backup}, #{item.continuePushFlag}, #{item.informFlag})
        </foreach>
    </insert>

    <update id="updateInformFlag" parameterType="String">
        UPDATE recipient_info
        set inform_flag = #{flag}
        WHERE inform_flag = 0
        <if test="id != null ">
            AND id = #{id}
        </if>
        <if test="informId != null ">
            AND inform_id = #{informId}
        </if>
    </update>

</mapper>