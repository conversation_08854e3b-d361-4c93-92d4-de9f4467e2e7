<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.modules.inform.mapper.ReviewerMapper">

    <insert id="insertReviewer" parameterType="com.onecity.os.inform.modules.inform.entity.ReviewerInfo"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO reviewer_info(id, inform_id, state, reviewer_id, reviewer_name, create_time)
        VALUES (#{reviewer.id}, #{reviewer.informId}, #{reviewer.state}, #{reviewer.reviewerId},
                #{reviewer.reviewerName}, #{reviewer.createTime})
    </insert>

    <select id="queryReviewerByInformId"
            resultType="com.onecity.os.inform.modules.inform.entity.ReviewerInfo">
        SELECT ic.id,
               ic.inform_id      AS informId,
               ic.state         AS state,
               ic.reviewer_id   AS reviewerId,
               ic.reviewer_name AS reviewerName,
               ic.create_time   AS createTime
        FROM reviewer_info AS ic
        WHERE 1 = 1
          AND ic.inform_id = #{informId}
          AND ic.state = 0
    </select>

    <update id="deleteReviewerByInformId" parameterType="String">
        UPDATE reviewer_info
        set state = 1
        WHERE inform_id = #{informId}
    </update>

    <select id="pendingList"  resultType="ReportCheckVo">
        SELECT i.id informId, i.simple_title title, i.type, i.create_time createTime, i.publish_time publishTime, i.state FROM reviewer_info r
        LEFT JOIN inform i ON r.inform_id = i.id
        WHERE r.state = 0
        AND r.reviewer_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND (i.state = 'AUDIT' OR i.state = 'PUSH_AUDIT')
        <if test="param.title!=null and param.title!=''">
            and INSTR(i.simple_title, #{param.title})
        </if>
        <if test="param.type!=null and param.type!=''">
            and i.type = #{param.type}
        </if>
        GROUP BY i.id
        ORDER BY i.publish_time desc
    </select>

    <select id="pendingListByRoles" resultType="com.onecity.os.inform.modules.inform.vo.ReportCheckVo">
        SELECT
        i.id informId,
        i.simple_title title,
        i.type,
        i.create_time createTime,
        i.publish_time publishTime,
        i.state informState,
        rs.state,
        i.process_id AS processId,
        i.current_step_id AS currentStepId
        FROM
        inform i
        LEFT JOIN inform_role_state AS rs ON rs.inform_id = i.id AND i.process_id = rs.process_id
        AND i.current_step_id = rs.current_step_id
        WHERE
        rs.role_id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        <if test="param.title!=null and param.title!=''">
            and INSTR(i.simple_title, #{param.title})
        </if>
        <if test="param.type!=null and param.type!=''">
            and i.type = #{param.type}
        </if>
        AND rs.state IN ("AUDIT","PUSH_AUDIT")
        ORDER BY
        i.publish_time DESC
    </select>

    <select id="checkedList"  resultType="ReportCheckVo">
        SELECT i.id informId, i.simple_title title, i.type, i.create_time createTime, i.publish_time publishTime, i.state FROM inform i
        WHERE i.id IN (
        SELECT inform_id FROM reviewer_info WHERE reviewer_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND state = 1)
        <if test="param.title!=null and param.title!=''">
            and INSTR(i.simple_title, #{param.title})
        </if>
        <if test="param.type!=null and param.type!=''">
            and i.type = #{param.type}
        </if>
        GROUP BY i.id
        ORDER BY i.publish_time desc
    </select>

    <select id="checkedListByRoles" resultType="com.onecity.os.inform.modules.inform.vo.ReportCheckVo">
        SELECT
        i.id informId,
        i.simple_title title,
        i.type,
        i.create_time createTime,
        i.publish_time publishTime,
        i.state informState,
        rs.state
        FROM
        inform i
        LEFT JOIN inform_role_state AS rs ON rs.inform_id = i.id AND i.process_id = rs.process_id
        WHERE
        rs.role_id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        <if test="param.title!=null and param.title!=''">
            and INSTR(i.simple_title, #{param.title})
        </if>
        <if test="param.type!=null and param.type!=''">
            and i.type = #{param.type}
        </if>
        AND rs.state IN ("AUDITED","PUSH_AUDITED","OVERRULE","PUSH_OVERRULE")
        ORDER BY
        i.publish_time DESC
    </select>

    <update id="updateStateByInformId" parameterType="String">
        update reviewer_info SET state = 1
        where inform_id = #{informId} and state = 0
    </update>

    <select id="queryReviewerByParam"
            resultType="com.onecity.os.inform.modules.inform.entity.ReviewerInfo">
        SELECT ri.id,
               ri.inform_id      AS informId,
               ri.state         AS state,
               ri.reviewer_id   AS reviewerId,
               ri.reviewer_name AS reviewerName,
               ri.create_time   AS createTime
        FROM reviewer_info AS ri
        WHERE 1 = 1
          AND ri.inform_id = #{informId}
          AND ri.state = #{state}
    </select>
</mapper>