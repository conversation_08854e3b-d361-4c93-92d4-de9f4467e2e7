<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.inform.modules.inform.mapper.StepRoleLinkMapper">
    <insert id="insert">
        INSERT INTO step_role_link (process_id, step_id, role_id)
        VALUES (#{processId}, #{stepId}, #{roleId})
    </insert>

    <delete id="delByStepId" parameterType="java.lang.String">
        DELETE FROM step_role_link WHERE step_id = #{stepId}
    </delete>
    <delete id="delByProcessId" parameterType="java.lang.String">
        DELETE FROM step_role_link WHERE process_id = #{processId}
    </delete>
    <select id="queryByStepId" resultType="com.onecity.os.inform.modules.inform.entity.StepRoleLink"
            parameterType="java.lang.String">
        SELECT process_id AS processId,
               step_id AS stepId,
               role_id AS roleId
        FROM step_role_link WHERE step_id = #{stepId}
    </select>

    <select id="queryByParam" resultType="com.onecity.os.inform.modules.inform.entity.StepRoleLink"
            parameterType="java.lang.String">
        SELECT process_id AS processId,
               step_id AS stepId,
               role_id AS roleId
        FROM step_role_link WHERE step_id = #{stepId} AND process_id = #{processId}
    </select>
</mapper>