package com.onecity.job.admin.core.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Result<T> implements Serializable {

    private T datas;
    private Integer resp_code;
    private String resp_msg;

    public static <T> Result<T> succeed(String resp_msg) {
        return of(null, CodeEnum.SUCCESS.getCode(), resp_msg);
    }

    public static <T> Result<T> succeed(T model, String resp_msg) {
        return of(model, CodeEnum.SUCCESS.getCode(), resp_msg);
    }

    public static <T> Result<T> succeed(T model) {
        return of(model, CodeEnum.SUCCESS.getCode(), "");
    }

    public static <T> Result<T> of(T datas, Integer resp_code, String resp_msg) {
        return new Result<>(datas, resp_code, resp_msg);
    }

    public static <T> Result<T> failed(String resp_msg) {
        return of(null, CodeEnum.ERROR.getCode(), resp_msg);
    }

    public static <T> Result<T> failed(T model, String resp_msg) {
        return of(model, CodeEnum.ERROR.getCode(), resp_msg);
    }

}
