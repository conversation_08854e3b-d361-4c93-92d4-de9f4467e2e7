server:
  port: 8080
  servlet:
    context-path: /xxl-job-admin
# spring配置
spring:
  #MVC配置
  mvc:
    servlet:
      load-on-startup: 0
    static-path-pattern: /static/**
    pathmatch:
      matching-strategy: ant_path_matcher
  main:
    allow-circular-references: true
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: onecity-os-nacos:8848
#        server-addr: *************:30309
  jmx:
    enabled: false
  application:
    name: onecity-os-job
  redis:
    host: onecity-os-redis
    port: 6379
    password: DONT(QkOmrw81EnDa0CDf9y1hywwU2sWxykwRkIHa8ZXhBvSUmcPtXUJerFvBv/77k4AxY40uLtxHEcjv86VWFXHNoPxPcGIgG6n10aMBOlsrWLwWZAjlOWr3N4JrG04kk+fPG+57s7x6Zu6algBVF8FWVJfIxkMWMIhU0IZGZ5njtok=)
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: *********************************************************************************************************************************************************
    url: ***********************************************************************************************************************************************************
    username: root
    password: DONT(QkOmrw81EnDa0CDf9y1hywwU2sWxykwRkIHa8ZXhBvSUmcPtXUJerFvBv/77k4AxY40uLtxHEcjv86VWFXHNoPxPcGIgG6n10aMBOlsrWLwWZAjlOWr3N4JrG04kk+fPG+57s7x6Zu6algBVF8FWVJfIxkMWMIhU0IZGZ5njtok=)
    #邮箱配置
  mail:
    host: smtphz.qiye.163.com
    port: 994
    username: <EMAIL>
    from: <EMAIL>
    password: xxx
    properties:
      mail:
        smtp:
          auth: false
          starttls:
            enable: false
            required: false
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
  #freemarker配置
  freemarker:
    templateLoaderPath=classpath: /templates/
    suffix: .ftl
    charset: UTF-8
    request-context-attribute: request
    settings:
      number_format: 0.##########
  web:
    resources:
      static-locations: classpath:/static/

#通用配置，开放端点
management:
  health:
    mail:
      enabled: false
    redis:
      enabled: false

# mybatis配置
mybatis:
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath:/mybatis-mapper/*Mapper.xml

#XXL-job配置
xxl:
  job:
    login:
      username: admin
      password: DONT(K22peq8/spLNzO6lMhrDad/dagR6MoikTAaaDzuFtI+KLINdM/9VIr2LnvsOWiF6Af1yECL2s+yFvmR4zitMArBsvUINdXnKQmZJ6mhSXtHYSHrsLB8DeprE15Nrb5RhW23imv8mLXj88R14m77el0wfukDeJS4A9E3ZUzqfNoo=)
    accessToken: jiashicang20179aSr@Mg%df25
    i18n: zh_CN
    #触发池
    triggerpool:
      fast:
        max: 200
      slow:
        max: 100
    logretentiondays: 30

com:
  onecity:
    enabled: true
