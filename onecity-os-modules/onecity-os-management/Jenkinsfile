pipeline {
  agent {
    node {
      label 'maven'
    }

  }
  //参数化构建:
  //流水线运行时需要输入参数，defaultValue中可以设置默认值
  //BRANCH在本例中代表拉取的分支名
  //CLUSTER_NAME在本例中代表部署的k8s集群名称
  parameters {
    string(name:'GIT_URL',defaultValue: 'http://git.onecode.ict.cmcc/YANFAYIBU/SHUZHICHUANGXINBU-CHANPINCHUANGXINZHONGXIN/SHUZIZHENGFULINGDAOJIASHICANGV2.0Group/OneScreenV2_backend.git',description:'代码git链接地址')
    string(name:'PROJECT_BRANCH',defaultValue: 'dev',description:'需要执行的代码分支名称')
    string(name:'REGISTRY',defaultValue: 'local.harbor.com',description:'镜像仓库域名')
    string(name:'HARBOR_PROJECT_NAME',defaultValue: 'onecity-os',description:'镜像仓库项目名,如:onecity-os')
    string(name:'IMAGE_NAME',defaultValue: 'onecity-os-backend-management:latest',description:'镜像名称及tag，用冒号隔开')
    booleanParam(name: 'NEED_DEPLOY', defaultValue: 'false', description: '是否需要执行部署,如需部署手动修改为true')
    string(name:'APP_NAME',defaultValue: 'onecity-os-backend-management',description:'应用名称,如:onecity-os-backend-system,部署的服务和工作负载会以此命名')
    string(name:'DEPLOY_NAMESPACE',defaultValue: 'onecity-os-dev',description: '需要部署的NAMESPACE即项目名称')
    string(name:'PROJECT_NAME',defaultValue: 'onecity-os-modules/onecity-os-management',description: '模块构件')
    string(name:'DEPLOY_KUBECONFIG',defaultValue: 'kubeconfig-test',description: '需要部署的集群,kubeconfig-dev为研发环境,kubeconfig-prod为演示环境')
  }
  
  environment {
    HARBOR_CREDENTIAL_ID = 'harbor-id'
    GITLAB_CREDENTIAL_ID = 'onecode-id'
  }
  
  stages {
    stage('代码拉取') {
      steps {
        git(url: '$GIT_URL', credentialsId: "$GITLAB_CREDENTIAL_ID", changelog: true, poll: false, branch: "$PROJECT_BRANCH")
        sh 'echo 拉取成功:$GIT_URL $PROJECT_BRANCH'
      }
    }
    
    stage('编译和构建镜像') {
      steps {
        container('maven') {
          sh 'mvn -Dmaven.test.skip=true -gs `pwd`/settings.xml clean package'
          sh 'echo 镜像名:$REGISTRY/$HARBOR_PROJECT_NAME/$IMAGE_NAME'
          sh 'cd $PROJECT_NAME && docker build --no-cache -f Dockerfile -t $REGISTRY/$HARBOR_PROJECT_NAME/$IMAGE_NAME .'
          withCredentials([usernamePassword(passwordVariable : 'DOCKER_PASSWORD' ,usernameVariable : 'DOCKER_USERNAME' ,credentialsId : "$HARBOR_CREDENTIAL_ID" ,)]) {
            sh 'echo "$DOCKER_PASSWORD" | docker login $REGISTRY -u "$DOCKER_USERNAME" --password-stdin'
            sh 'docker push  $REGISTRY/$HARBOR_PROJECT_NAME/$IMAGE_NAME'
          }
        }
      }
    }
    
    stage('是否确认部署') {
      when{
        expression { return params.NEED_DEPLOY }
      }
      steps {
        //审批确认
        //input(id: 'deploy', message: "即将部署至:${DEPLOY_KUBECONFIG}集群,${DEPLOY_NAMESPACE}项目,${APP_NAME}")
        sh 'echo 即将部署至:${DEPLOY_KUBECONFIG}集群,${DEPLOY_NAMESPACE}项目,${APP_NAME}'
        sh 'echo 删除资源'
        kubernetesDeploy(configs: "$PROJECT_NAME/**.yaml", deleteResource: true,enableConfigSubstitution: true, kubeconfigId: "$DEPLOY_KUBECONFIG")
        sh 'sleep 10'
        kubernetesDeploy(configs: "$PROJECT_NAME/**.yaml", deleteResource: false,enableConfigSubstitution: true, kubeconfigId: "$DEPLOY_KUBECONFIG")
      }
    }
  }
}
