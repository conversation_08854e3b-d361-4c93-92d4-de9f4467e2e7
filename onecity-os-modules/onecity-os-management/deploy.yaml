apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: $DEPLOY_NAMESPACE
  name: $APP_NAME
  labels:
    app: $APP_NAME
spec:
  replicas: 1
  selector:
    matchLabels:
      app: $APP_NAME
  template:
    metadata:
      labels:
        app: $APP_NAME
    spec:
      volumes:
        - name: apmdir
          emptyDir: {}
      initContainers:
        - name: install
          image: 'image.onecode.cmict.cloud/devops/apm-agent-with-init:2.11.0'
          command:
            - sh
            - /init.sh
          env:
            - name: CW_CONFIG_SERVER
              value: 'http://192.168.105.166:18080'
            - name: CW_DATA_SERVER
              value: 'http://192.168.105.166:18080'
            - name: CW_LICENSE_KEY
              value: J45Engw88NeHUZ4Q7qNsK8L47FTH**QvgW113IEnsNaBNMR5zZ**oj/g!!!!
            - name: CW_APP_NAME
              value: $APP_NAME-$PROJECT_BRANCH
          resources: {}
          volumeMounts:
            - name: apmdir
              mountPath: /apmdir
      containers:
      - name: $APP_NAME
        image: $REGISTRY/$HARBOR_PROJECT_NAME/$IMAGE_NAME
        ports:
        - name: http-8080
          containerPort: 8080
          protocol: TCP
        imagePullPolicy: Always
        volumeMounts:
          - name: apmdir
            mountPath: /apmdir
      dnsPolicy: ClusterFirst
      nodeSelector:
        internet: '1'
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  namespace: $DEPLOY_NAMESPACE
  name: $APP_NAME
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
  selector:
    app: $APP_NAME
