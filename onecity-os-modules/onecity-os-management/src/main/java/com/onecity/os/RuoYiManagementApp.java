package com.onecity.os;

import com.ruoyi.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import tk.mybatis.spring.annotation.MapperScan;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@EnableCustomSwagger2
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@MapperScan("com.onecity.os.*.*.mapper")
@EnableDiscoveryClient
@EnableScheduling
@ComponentScan(basePackages = {"com.onecity"})
@EnableFeignClients(basePackages = {"com.onecity"})
public class RuoYiManagementApp {
    public static void main(String[] args)
    {
        SpringApplication.run(RuoYiManagementApp.class, args);
    }
}