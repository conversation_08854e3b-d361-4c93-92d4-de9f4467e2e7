package com.onecity.os.management.appcontroller;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.management.articleManage.entity.ArticleAppManage;
import com.onecity.os.management.articleManage.service.ArticleManageService;
import com.onecity.os.management.configmanage.entity.vo.PermissionsParame;
import com.onecity.os.management.configmanage.entity.vo.PermissionsVo;
import com.onecity.os.management.configmanage.entity.vo.SourceManageTypeVO;
import com.onecity.os.management.configmanage.service.ConfigManageService;
import com.onecity.os.management.configmanage.service.SourceManageService;
import com.onecity.os.management.systemstyle.domain.SystemStyle;
import com.onecity.os.management.systemstyle.service.ISystemStyleService;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 *
 * @date 2024/09/04
 */
@Slf4j
@RestController
@RequestMapping("/app")
@Api(tags = "app端接口统一入口")
public class AppController extends BaseController {

    @Autowired
    private ArticleManageService articleManageService;
    @Autowired
    private ISystemStyleService systemStyleService;
    @Autowired
    private ConfigManageService configManageService;

    @Autowired
    private SourceManageService sourceManageService;

    /**
     * 根据类型,获取政务信息列表
     *
     * @param type 类型:1-长政信息; 2-长政简报
     * @return
     */
    @GetMapping("/articleManage/getArticleMsgListPage")
    @ApiOperation(value = "根据类型,获取政务信息列表")
    public TableDataInfo getArticleMsgListPage(@RequestParam(name = "type") Integer type,@RequestParam(name = "sourceId") String sourceId) {
        startPage();
        List<ArticleAppManage> pageList = articleManageService.getArticleListPage(type,sourceId);
        return getDataTable(pageList,true);
    }
    /**
     * 根据政务信息id,将信息设置为已读
     *
     * @param id
     * @return
     */
    @PostMapping("/articleManage/setArticleMsgRead")
    @ApiOperation(value = "根据政务信息id,将信息设置为已读")
    public BaseResult<?> setArticleMsgRead(@RequestParam(name = "id") String id) {
        int res = articleManageService.setArticleRead(id);
        if (0 < res) {
            return BaseResult.ok();
        }
        return BaseResult.fail("操作失败");
    }

    /**
     * 查询系统样式列表
     */
    @GetMapping("/style/getStyle")
    @ApiOperation(value = "获取系统样式配置")
    public AjaxResult list()
    {
        SystemStyle result = systemStyleService.selectSystemStyleById();
        return AjaxResult.success(result);
    }

    /**
     * 判断当前用户是否有圈批权限
     *
     * @param
     * @return
     */
    @ApiOperation(value = "移动端权限配置-判断当前用户是否有圈批权限")
    @GetMapping("/configManage/checkUserCircleApprovalPermission")
    public BaseResult<?> checkUserCircleApprovalPermission() {
        int result = configManageService.checkUserCircleApprovalPermission();
        if(1==result){
            return BaseResult.ok("该用户拥有圈批权限");
        }
        return BaseResult.fail("该用户没有圈批权限");
    }

    /**
     * 根据用户id和指标id,查找是否有批示权限
     */
    @ApiOperation(value = "根据用户角色和指标id,查找是否有督办和红灯权限")
    @PostMapping("/configManage/getPermissionsByRoleList")
    public BaseResult<PermissionsVo> getPermissionsByRoleList(@RequestBody PermissionsParame parame) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isNotEmpty(loginUser)) {
            List<Long> roleIdList = Arrays.asList(loginUser.getSysUser().getRoleIds());
            List<String> roleIdStringList = roleIdList.stream().map(Object::toString).collect(Collectors.toList());
            PermissionsVo result = configManageService.getPermissionsByRoleList(loginUser.getUserid().toString(), roleIdStringList, parame.getIndicatorId());
            return BaseResult.ok(result);
        }else {
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
    }

    /**
     * 判断当前用户是否拥有阅批呈报下的呈报审核和圈阅批示权限
     */
    @ApiOperation(value = "判断当前用户是否拥有阅批呈报下的呈报审核和圈阅批示权限")
    @GetMapping("/configManage/hasInformPermissions")
    public BaseResult<PermissionsVo> hasInformPermissions() {
        PermissionsVo result = configManageService.hasInformPermissions();
        return BaseResult.ok(result);
    }

    /**
     *
     * @return
     */
    @ApiOperation(value = "版块管理-获取板块分组列表")
    @ResponseBody
    @GetMapping("/sourceManage/getTypeList")
    public BaseResult getTypeList() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        List<SourceManageTypeVO> result = sourceManageService.getTypeList(loginUser.getSysUser().getRoleIds());
        return BaseResult.ok(result);
    }
}



















