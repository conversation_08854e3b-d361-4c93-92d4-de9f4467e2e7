package com.onecity.os.management.articleManage.controller;

import cn.hutool.core.util.ObjectUtil;
import com.onecity.os.common.core.constant.Constant;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.management.articleManage.entity.ArticleAppManage;
import com.onecity.os.management.articleManage.entity.ArticlePcManage;
import com.onecity.os.management.articleManage.model.vo.SaveArticleManageVo;
import com.onecity.os.management.articleManage.service.ArticleManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 政务信息管理
 *
 * <AUTHOR>
 * @date 2020/10/30 14:31
 */
@Slf4j
@RestController
@RequestMapping("/articleManage")
@Api(tags = "文章类板块管理")
public class ArticleManageController extends BaseController {

    @Autowired
    private ArticleManageService articleManageService;

    /**
     * 根据参数查询政务信息列表(分页)
     *
     * @param name 标题
     * @param type 类型:1-长政信息; 2-长政简报
     * @return
     */
    @ApiOperation(value = "文章板块管理-查询文章信息列表(分页)")
//    @AutoLog(value = "政务信息管理-查询政务信息列表(分页)")
    @GetMapping("/getArticleManageList")
    public TableDataInfo getArticleManageList(@RequestParam(name = "name", required = false) String name,
                                               @RequestParam(name = "type", required = false) Integer type,
                                              @RequestParam(name = "sourceId") String sourceId) {
        startPage();
        return getDataTable(articleManageService.getArticleManageList(name, type,sourceId), true);
    }

    /**
     * 新增/修改政务信息管理
     *
     * @param vo {@link SaveArticleManageVo}
     * @return
     */
    @ApiOperation(value = "新增/修改文章信息管理")
    @Log(title = "文章类板块管理-新增文章信息",businessType = BusinessType.INSERT)
    @PostMapping("/saveArticleManage")
    public BaseResult saveArticleManage(@RequestBody @Validated SaveArticleManageVo vo) {
        if (StringUtils.isNotEmpty(vo.getLabel())) {
            if (6 < vo.getLabel().length()) {
                return BaseResult.fail("标签长度不能超过6个字");
            }
        }
        if (StringUtils.isNotEmpty(vo.getContent()) && vo.getContent().length() > Constant.CONTEST_MAX) {
            return BaseResult.fail("无效格式内容过多，请减少后再操作");
        }
        int res = articleManageService.saveArticleManage(vo);
        if (0 < res) {
            return BaseResult.ok();
        } else {
            return BaseResult.fail("添加失败");
        }
    }

    /**
     * 根据政务信息id,删除政务信息
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据政务信息id,删除文章信息")
    @Log(title = "文章管理-删除文章信息",businessType = BusinessType.DELETE)
    @PostMapping("/deleteArticleManageByIdAndSourceId")
    public BaseResult deleteArticleManageByIdAndSourceId(@RequestParam(name = "id") String id,@RequestParam(name = "sourceId") String sourceId) {
        if (ObjectUtil.isNull(id)) {
            return BaseResult.fail("政务信息id不能为空");
        }
        int res = articleManageService.deleteArticleManageByIdAndSourceId(id,sourceId);
        if (0 < res) {
            return BaseResult.ok();
        } else {
            return BaseResult.fail("删除失败");
        }
    }

    /**
     * 根据类型,获取政务信息列表
     *
     * @param type 类型:1-长政信息; 2-长政简报
     * @return
     */
    @GetMapping("/getArticleMsgListPage")
    @ApiOperation(value = "根据类型,获取政务信息列表")
    public TableDataInfo getArticleMsgListPage(@RequestParam(name = "type") Integer type,@RequestParam(name = "sourceId") String sourceId) {
        startPage();
        List<ArticleAppManage> pageList = articleManageService.getArticleListPage(type,sourceId);
        return getDataTable(pageList,true);
    }

    /**
     * 根据政务信息id,将信息设置为已读
     *
     * @param id
     * @return
     */
    @PostMapping("/setArticleMsgRead")
    @ApiOperation(value = "根据政务信息id,将信息设置为已读")
    public BaseResult<?> setArticleMsgRead(@RequestParam(name = "id") String id) {
        int res = articleManageService.setArticleRead(id);
        if (0 < res) {
            return BaseResult.ok();
        }
        return BaseResult.fail("操作失败");
    }



}



















