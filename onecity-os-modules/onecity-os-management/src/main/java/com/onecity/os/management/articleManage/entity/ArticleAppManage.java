package com.onecity.os.management.articleManage.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * PC端政务信息管理
 *
 * <AUTHOR>
 * @date 2020/10/30 14:42
 */
@Data
@Table(name = "article_manage")
public class ArticleAppManage {
    /**
     * 主键id
     */
    @Id
    private String id;

    /**
     * 标题
     */
    private String name;

    /**
     * 类型:1-长政信息; 2-长政简报
     */
    private String type;

    /**
     * 类型:1-长政信息; 2-长政简报
     */
    @Transient
    private String typeValue;

    /**
     * 期刊号
     */
    private String journalNum;

    /**
     * 标签
     */
    private String label;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 图片地址
     */
    private String picPath;

    /**
     * 图片展示形式:1-大图展示;2-缩略展示
     */
    private Integer picIsBig;

    /**
     * 内容形式:1-文本;2-pdf
     */
    private Integer contentFormate;

    /**
     * 内容
     */
    private String content;

    /**
     * 是否删除:0否 1是
     */
    private Byte isDelete;

    /**
     * 是否已读:0-否;1-是
     */
    private Byte isRead;

    /**
     * 创建人
     */
    private String creater;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date updateTime;

    /**
     * 文章板块编码
     */
    private String sourceId;
}
