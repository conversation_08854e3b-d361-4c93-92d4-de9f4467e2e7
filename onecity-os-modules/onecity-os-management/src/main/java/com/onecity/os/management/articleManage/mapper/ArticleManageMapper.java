package com.onecity.os.management.articleManage.mapper;

import com.onecity.os.management.articleManage.entity.ArticleAppManage;
import com.onecity.os.management.articleManage.entity.ArticlePcManage;
import com.onecity.os.management.articleManage.model.dto.GetArticleManageListDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/30 14:45
 */
@Mapper
public interface ArticleManageMapper extends BaseMapper<ArticlePcManage> {
    /**
     * 根据政务信息id,逻辑删除政务信息
     *
     * @param id
     * @param userName
     * @return
     */
    @Update("UPDATE article_manage SET is_delete=1,updater=#{userName},update_time=NOW() WHERE id=#{id} and source_id=#{sourceId} LIMIT 1")
    int deleteArticleMsgByIdAndSourceId(@Param("id") String id, @Param("userName") String userName,@Param("sourceId") String sourceId);

    /**
     * 更新政务信息
     *
     * @param articlePcManage
     * @param userName
     * @return
     */
    @Update("UPDATE article_manage SET `name`=#{vo.name},`type`=#{vo.type}, journal_num=#{vo.journalNum}, `label`=#{vo.label}, " +
            "file_name=#{vo.fileName}, pic_path=#{vo.picPath},pic_is_big=#{vo.picIsBig}, content_formate=#{vo.contentFormate}, content=#{vo.content}," +
            "updater=#{userName},update_time=#{vo.updateTime} WHERE id=#{vo.id} and source_id=#{vo.sourceId} LIMIT 1")
    int updateZwInfo(@Param("vo") ArticlePcManage articlePcManage, @Param("userName") String userName);

    List<GetArticleManageListDto> selectPageByNameAndType(@Param("name")String name, @Param("type")Integer type,@Param("sourceId")String sourceId);

    List<ArticleAppManage> getListByType(@Param("type")Integer type, @Param("sourceId")String sourceId);
}







