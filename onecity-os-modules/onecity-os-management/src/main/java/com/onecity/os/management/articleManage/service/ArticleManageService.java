package com.onecity.os.management.articleManage.service;



import com.onecity.os.management.articleManage.entity.ArticleAppManage;
import com.onecity.os.management.articleManage.entity.ArticlePcManage;
import com.onecity.os.management.articleManage.model.dto.GetArticleManageListDto;
import com.onecity.os.management.articleManage.model.vo.SaveArticleManageVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/30 14:32
 */
public interface ArticleManageService {
    /**
     * 根据参数查询政务信息列表(分页)
     *
     * @param name
     * @param type
     * @return
     */
    List<GetArticleManageListDto> getArticleManageList(String name, Integer type, String sourceId);

    /**
     * 新增/修改政务信息管理
     *
     * @param vo
     */
    int saveArticleManage(SaveArticleManageVo vo);

    /**
     * 根据政务信息id,删除政务信息
     *
     * @param id
     * @return
     */
    int deleteArticleManageByIdAndSourceId(String id,String sourceId);

    /**
     * 根据类型,获取政务信息列表
     *
     * @param type
     * @return
     */
    List<ArticleAppManage> getArticleListPage(Integer type, String sourceId);

    /**
     * 根据政务信息id,将信息设置为已读
     *
     * @param id
     * @return
     */
    int setArticleRead(String id);
}












