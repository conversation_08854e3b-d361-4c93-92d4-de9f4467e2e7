package com.onecity.os.management.articleManage.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.common.core.utils.bean.BeanUtils;
import com.onecity.os.management.articleManage.entity.ArticleAppManage;
import com.onecity.os.management.articleManage.entity.ArticlePcManage;
import com.onecity.os.management.articleManage.mapper.ArticleManageMapper;
import com.onecity.os.management.articleManage.model.dto.GetArticleManageListDto;
import com.onecity.os.management.articleManage.model.vo.SaveArticleManageVo;
import com.onecity.os.management.articleManage.service.ArticleManageService;
import com.onecity.os.system.api.RemoteUserService;
import com.onecity.os.system.api.domain.SysDictData;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2020/10/30 14:32
 */
@Slf4j
@Service
public class ArticleManageServiceImpl implements ArticleManageService {

    @Resource
    private ArticleManageMapper articleManageMapper;

    @Resource
    private RemoteUserService remoteUserService;

    @Override
    public List<GetArticleManageListDto> getArticleManageList(String name, Integer type, String sourceId) {
        List<GetArticleManageListDto> dtos = articleManageMapper.selectPageByNameAndType(name, type,sourceId);
        //由于字典模块被归入到system的module中了，所以使用远程调用的方式分步解决需要跨库关联的逻辑
        BaseResult<List<SysDictData>> result = remoteUserService.dictType(sourceId);
        List<SysDictData> data = result.getData();
        log.info("远程调用结果1.0.0"+data.toString());
        if(ObjectUtils.isNotEmpty(data)){
            for (GetArticleManageListDto dto : dtos){
                for(int i=0;i<data.size();i++){
                    if(data.get(i).getDictValue().equals(dto.getTypeValue())){
                        dto.setType(data.get(i).getDictLabel());
                    }
                }
            }
        }
        return dtos;
    }

    @Override
    public int saveArticleManage(SaveArticleManageVo vo) {
        int res = 0;
        try {
            ArticlePcManage articlePcManage = new ArticlePcManage();
            BeanUtils.copyProperties(vo, articlePcManage);
            articlePcManage.setIsDelete((byte) 0);
            // 获取当前登录的用户信息
            LoginUser sysUser = SecurityUtils.getLoginUser();
            String userName = null;
            if (ObjectUtil.isNotNull(sysUser)) {
                userName = sysUser.getUsername();
            }
            if(StringUtils.isNotEmpty(vo.getPicPath())){
                String temp= vo.getPicPath().replace("http://","");
                String substring = temp.substring(temp.indexOf("/"), temp.length() - 1);
                vo.setPicPath(substring);
            }
            // id为空,新增
            if (ObjectUtil.isNull(vo.getId())) {
                String id = UUID.randomUUID().toString().replace("-", "");
                articlePcManage.setId(id);
                articlePcManage.setCreater(userName);
                articlePcManage.setCreateTime(new Date());
                articlePcManage.setUpdater(userName);
                articlePcManage.setIsRead((byte)0);
                articlePcManage.setUpdateTime(new Date());
                res = articleManageMapper.insert(articlePcManage);
            } else {
                // id不为空,更新
                articlePcManage.setUpdater(userName);
                articlePcManage.setUpdateTime(new Date());
                res = articleManageMapper.updateZwInfo(articlePcManage, userName);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("新增/修改政务信息管理失败");
        }
        return res;
    }

    @Override
    public int deleteArticleManageByIdAndSourceId(String id,String sourceId) {
        int res = 0;
        try {
            // 获取当前登录的用户信息
            LoginUser sysUser = SecurityUtils.getLoginUser();
            String userName = null;
            if (ObjectUtil.isNotNull(sysUser)) {
                userName = sysUser.getUsername();
            }
            res = articleManageMapper.deleteArticleMsgByIdAndSourceId(id, userName,sourceId);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("根据文章信息id与文章板块编码,删除文章信息失败");
        }
        return res;
    }

    @Override
    public List<ArticleAppManage> getArticleListPage(Integer type, String sourceId) {
        List<ArticleAppManage> zwPcMsgManage = articleManageMapper.getListByType(type,sourceId);
        return zwPcMsgManage;
    }

    @Override
    public int setArticleRead(String id) {
        ArticlePcManage articlePcManage = new ArticlePcManage();
        articlePcManage.setId(id);
        articlePcManage.setIsRead((byte) 1);
        articlePcManage.setUpdateTime(new Date());
        return articleManageMapper.updateByPrimaryKeySelective(articlePcManage);
    }
}
