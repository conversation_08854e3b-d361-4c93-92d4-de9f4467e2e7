package com.onecity.os.management.configmanage.controller;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.poi.ExcelUtil;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.management.api.vo.PageResult;
import com.onecity.os.management.configmanage.entity.RemindInfo;
import com.onecity.os.management.configmanage.entity.dto.*;
import com.onecity.os.management.configmanage.entity.vo.*;
import com.onecity.os.management.configmanage.service.ConfigManageService;
import com.onecity.os.management.zhibiao.model.dto.GetWaitDoneCenterPageListDto;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 配置管理-钉钉用户菜单相关接口
 *
 * <AUTHOR>
 * @date 2021/1/6 下午3:58
 */
@Slf4j
@RestController
@RequestMapping("/configManage")
@Api(tags = "数据填报提醒配置/移动端权限配置/首页消息中心")
public class ConfigManageController extends BaseController {

    @Autowired
    private ConfigManageService configManageService;

    /**
     * 获取移动端权限配置人员列表
     *
     * @param name   姓名
     * @param phone  手机号
     * @param depart 部门
     * @return
     */
    @ApiOperation(value = "移动端权限配置-获取移动端权限配置人员列表")
    @GetMapping("/getDingDingConfigPageList")
    public TableDataInfo getDingDingConfigPageList(@RequestParam(name = "name", required = false) String name,
                                                   @RequestParam(name = "phone", required = false) String phone,
                                                   @RequestParam(name = "depart", required = false) String depart,
                                                   @RequestParam(name = "position", required = false) String position,
                                                   @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
                                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        startPage();
        List<GetDingDingConfigPageListDto> result = configManageService.getDingDingConfigPageList(name, phone, depart, position);
        return getDataTable(result);
    }

    /**
     * 获取移动端权限菜单列表
     *
     * @param name 板块名称
     * @return
     */
    @ApiOperation(value = "移动端权限配置-权限配置菜单列表")
    @GetMapping("/getDingDingMenuList")
    public BaseResult<?> getDingDingMenuList(@RequestParam(name = "name", required = false) String name) {
        List<GetDingDingMenuListDto> list = configManageService.getDingDingMenuList(name);
        return BaseResult.ok(list);
    }

    /**
     * 给指定钉钉用户,添加权限
     *
     * @param vo
     * @return
     */
    @Log(title = "移动端权限配置-权限配置", businessType = BusinessType.OTHER)
    @ApiOperation(value = "移动端权限配置-给指定钉钉用户,添加权限")
    @PostMapping("/addDingDingUserMenu")
    public BaseResult holdDingDingUserMenu(@RequestBody @Validated AddDingDingUserMenuVo vo) throws Exception {
        configManageService.addDingDingUserMenu(vo);
        return BaseResult.ok();
    }

    /**
     * 根据用户id,查找已经分配的模块id
     *
     * @param roleId 角色id
     * @return
     */
    @ApiOperation(value = "移动端权限配置-根据用户id,查找已经分配的模块id")
    @GetMapping("/getMenuIdListByUserId")
    public BaseResult<?> getMenuIdListByUserId(@RequestParam(name = "roleId") Long roleId) {
        List<DingDingUserMenuVo> list = configManageService.getMenuIdListByUserId(roleId);
        return BaseResult.ok(list);
    }

    /**
     * 判断当前用户是否有圈批权限
     *
     * @param
     * @return
     */
    @ApiOperation(value = "移动端权限配置-判断当前用户是否有圈批权限")
    @GetMapping("/checkUserCircleApprovalPermission")
    public BaseResult<?> checkUserCircleApprovalPermission() {
        int result = configManageService.checkUserCircleApprovalPermission();
        if(1==result){
            return BaseResult.ok("该用户拥有圈批权限");
        }
        return BaseResult.fail("该用户没有圈批权限");
    }

    /**
     * 获取PC填报提醒人员列表
     *
     * @param name  姓名
     * @param phone 手机号
     * @return
     */
    @ApiOperation(value = "数据填报提醒配置-获取PC填报提醒人员列表")
    @GetMapping("/getTianBaoUserPageList")
    public TableDataInfo getTianBaoUserPageList(@RequestParam(name = "name", required = false) String name,
                                                                        @RequestParam(name = "phone", required = false) String phone,
                                                                        @RequestParam(name = "depart", required = false) String depart,
                                                                        @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
                                                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        TableDataInfo result = configManageService.getTianBaoUserPageList(name, phone,depart,pageNum,pageSize);
        return result;
    }

    /**
     * 获取填报提醒配置列表
     *
     * @param type       类型
     * @param sourceName 板块名称
     * @return
     */
    @ApiOperation(value = "配置管理-数据填报提醒配置-获取提醒配置列表")
    @GetMapping("/getRemindConfigPageList")
    public TableDataInfo getRemindConfigPageList(@RequestParam(name = "type", required = false) String type,
                                                                          @RequestParam(name = "sourceName", required = false) String sourceName,
                                                                          @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
                                                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) throws Exception {
        startPage();
        log.info("sourName:"+JSONObject.toJSONString(sourceName));
        List<GetRemindConfigPageListDto> result = configManageService.getRemindConfigPageList(type, sourceName);
        return getDataTable(result);
    }

    /**
     * 数据填报提醒配置,配置提醒人
     *
     * @param vo
     * @return
     */
    @ApiOperation(value = "配置管理-数据填报提醒配置-配置提醒人")
    @Log(title = "数据填报提醒配置-配置保存", businessType = BusinessType.UPDATE)
    @PostMapping("/addRemindUser")
    public BaseResult updateRemindUser(@RequestBody SaveRemindUserVo vo) throws Exception {
//        if (StringUtils.isEmpty(vo.getPcUserIds()) && StringUtils.isEmpty(vo.getAppUserIds())) {
//            return Result.error("管理平台提醒人和移动端提醒人,至少填写其中一个");
//        }
        configManageService.saveRemindUser(vo);
        return BaseResult.ok();
    }

    /**
     * 获取提醒中心列表
     *
     * @param userId
     * @return
     */
    @ApiOperation(value = "首页-获取提醒中心列表")
    @GetMapping("/getRemindInfoPageList")
    public TableDataInfo getRemindInfoPageList(@RequestParam(name = "userId") String userId,
                                                        @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
                                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        startPage();
        List<RemindInfo> result = new ArrayList<>();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)) {
            return getDataTable(result);
        }
        result = configManageService.getRemindInfoPageList(loginUser.getUserid().toString());
        return getDataTable(result);
    }

    /**
     * 根据提醒中心消息id,将消息设置为已读状态
     *
     * @param vo
     * @return
     */
    @ApiOperation(value = "首页-根据提醒中心消息id,将消息设置为已读状态")
    @PostMapping("/updateRemindIsReadById")
    public BaseResult updateRemindInfoById(@RequestBody HashMap<String, Long> vo) throws Exception {
        if (StringUtils.isEmpty(vo.get("id"))) {
            return BaseResult.fail("id必传");
        }
        configManageService.updateRemindIsReadById(vo.get("id"));
        return BaseResult.ok();
    }

    /**
     * 导出数据填报提醒配置列表excel
     *
     *
     */
    @RequestMapping(value = "/exportRemindConfigXls")
    public void exportRemindConfigXls(@RequestParam("type") String type, @RequestParam("sourceName") String sourceName, HttpServletResponse response) {
        List<ExportRemindConfigXlsDto> dtoList = configManageService.exportRemindConfigXls(
                type, sourceName);
        try {
            List<GetRemindConfigPageListDto> result = configManageService.getRemindConfigPageList(type, sourceName);
            log.info("1.1.2. : result {}", JSONObject.toJSONString(result));
            ExcelUtil<GetRemindConfigPageListDto> util = new ExcelUtil<>(GetRemindConfigPageListDto.class);
            util.exportExcel(response, result, "数据填报提醒配置列表");
        } catch (Exception e) {
            log.info("error message :" + e);
        }
    }

    /**
     * 根据部门id,获取移动端权限配置人员列表
     *
     * @param name     姓名
     * @param phone    手机号
     * @param departId 部门id
     * @return
     */
    @ApiOperation(value = "移动端权限配置-根据部门id,获取移动端权限配置人员列表")
    @GetMapping("/getDingDingConfigByDepartIdPageList")
    public TableDataInfo getDingDingConfigByDepartIdPageList(@RequestParam(name = "name", required = false) String name,
                                                             @RequestParam(name = "phone", required = false) String phone,
                                                             @RequestParam(name = "departId") String departId,
                                                             @RequestParam(name = "position", required = false) String position,
                                                             @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
                                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        startPage();
        List<GetDingDingConfigPageListDto> result = configManageService.getDingDingConfigByDepartIdPageList(name, phone, departId,position);
        return getDataTable(result);
    }

    @ApiOperation(value = "移动端权限配置导出")
    @RequestMapping("/exportDingConfig")
    public void exportDingConfig(@RequestParam(name = "name", required = false) String name,
                                         @RequestParam(name = "phone", required = false) String phone,
                                         @RequestParam(name = "position", required = false) String position,
                                         @RequestParam(name = "departId") String departId,
                                         @RequestParam(name = "departName") String departName,
                                         HttpServletResponse response) throws IOException{
        List<DingConExcel> excel = configManageService.exportDingConfig(name,phone,position,departId);
        ExcelUtil<DingConExcel> util = new ExcelUtil<>(DingConExcel.class);
        if(StringUtils.isEmpty(departId)){
            util.exportExcel(response, excel, "全部");
        }else {
            util.exportExcel(response, excel, departName);
        }
    }

    /**
     * 根据用户id和指标id,查找是否有批示权限
     */
    @ApiOperation(value = "根据用户id和指标id,查找是否有批示权限")
    @GetMapping("/getPermissionsByUserId")
    public BaseResult<?> getPermissionsByUserId(@RequestParam(name = "roleList") List<String> roleList,@RequestParam(name = "indicatorId") String indicatorId) {
        boolean result = configManageService.getPermissionsByUserId(roleList,indicatorId);
        return BaseResult.ok(result);
    }

    /**
     * 根据用户id和指标id,查找是否有批示权限
     */
    @ApiOperation(value = "根据用户角色和指标id,查找是否有督办和红灯权限")
    @PostMapping("/getPermissionsByRoleList")
    public BaseResult<PermissionsVo> getPermissionsByRoleList(@RequestBody PermissionsParame parame) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isNotEmpty(loginUser)) {
            List<Long> roleIdList = Arrays.asList(loginUser.getSysUser().getRoleIds());
            List<String> roleIdStringList = roleIdList.stream().map(Object::toString).collect(Collectors.toList());
            PermissionsVo result = configManageService.getPermissionsByRoleList(loginUser.getUserid().toString(), roleIdStringList, parame.getIndicatorId());
            return BaseResult.ok(result);
        }else {
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
    }

    /**
     * 判断当前用户是否拥有阅批呈报下的呈报审核和圈阅批示权限
     */
    @ApiOperation(value = "判断当前用户是否拥有阅批呈报下的呈报审核和圈阅批示权限")
    @GetMapping("/hasInformPermissions")
    public BaseResult<PermissionsVo> hasInformPermissions() {
        PermissionsVo result = configManageService.hasInformPermissions();
        return BaseResult.ok(result);
    }
}

















