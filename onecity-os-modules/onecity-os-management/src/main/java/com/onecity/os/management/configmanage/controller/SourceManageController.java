package com.onecity.os.management.configmanage.controller;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.enums.ResultInfoEnum;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.management.api.vo.PageResult;
import com.onecity.os.management.configmanage.check.SourceInsert;
import com.onecity.os.management.configmanage.check.SourceUpdate;
import com.onecity.os.management.configmanage.entity.SourceManage;
import com.onecity.os.management.configmanage.entity.dto.SourceManageIndicatorGroup;
import com.onecity.os.management.configmanage.entity.dto.SourceManageUrl;
import com.onecity.os.management.configmanage.entity.vo.SourceManageReqVO;
import com.onecity.os.management.configmanage.entity.vo.SourceManageResVO;
import com.onecity.os.management.configmanage.entity.vo.SourceManageTypeVO;
import com.onecity.os.management.configmanage.enums.TypeEnums;
import com.onecity.os.management.configmanage.service.SourceManageService;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 配置管理-板块管理
 *
 * <AUTHOR>
 * @since 2020-05-21 10:11:56
 */
@Api(tags = "配置管理-板块管理")
@RestController
@RequestMapping("/sourceManage")
public class SourceManageController extends BaseController {

    @Autowired
    private SourceManageService sourceManageService;

    @ApiOperation(value = "版块管理-开通新版块")
    @Log(title = "版块管理-添加机构信息", businessType = BusinessType.INSERT)
    @ResponseBody
    @PostMapping("/insert")
    public BaseResult insert(@Validated(SourceInsert.class) @RequestBody SourceManageReqVO requestVO) {
        //校验厅局简称是否唯一
        String newSourceSimpleName = this.checkParams(requestVO);
        requestVO.setSourceSimpleName(newSourceSimpleName);
        LoginUser sysUser = SecurityUtils.getLoginUser();
        if (sysUser != null) {
            requestVO.setCreater(sysUser.getUsername());
        }
        int i = sourceManageService.insertAndCreateTable(requestVO);
        if (i == 1) {
            return BaseResult.ok("操作完成");
        }
        return BaseResult.fail(ResultInfoEnum.CREATE_SOURCE_FAIL.getMsg());
    }

    private String checkParams(SourceManageReqVO requestVO) {

        String sourceSimpleName = requestVO.getSourceSimpleName();
        //存在重名则往上加数字如wjw1,wjw2....
        int count = sourceManageService.countBySourceSimpleName(sourceSimpleName);
        if (count == 0) {
            return sourceSimpleName;
        }
        String result = sourceSimpleName + count;
        return result;
    }

    /**
     * 获取厅局列表--分页
     *
     * @return
     */
    @ApiOperation(value = "版块管理-获取厅局列表")
    @ResponseBody
    @GetMapping("/getList")
    public TableDataInfo getList(@RequestParam(name = "type", required = false) String type,
                                 @RequestParam(name = "sourceName", required = false) String sourceName,
                                 @RequestParam(name = "isStart", required = false) Integer isStart,
                                 @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        startPage();
        List<SourceManageResVO> sourceManageResVOS = sourceManageService.listSource(type, sourceName,isStart);
        return getDataTable(sourceManageResVOS);
    }

    /**
     * 更新厅局信息
     *
     * @param requestVO
     * @return
     */
    @ApiOperation(value = "版块管理-编辑更新厅局信息")
    @Log(title = "版块管理-编辑更新厅局信息", businessType = BusinessType.UPDATE)
    @ResponseBody
    @PostMapping("/update")
    public BaseResult update(@Validated(SourceUpdate.class) @RequestBody SourceManageReqVO requestVO) {
        LoginUser sysUser = SecurityUtils.getLoginUser();
        if (sysUser != null) {
            requestVO.setUpdater(sysUser.getUsername());
        }
        int i = sourceManageService.updateSourceManageById(requestVO);
        if (i == 1) {
            return BaseResult.ok("更新完成");
        }
        return BaseResult.fail(ResultInfoEnum.UPDATE_SOURCE_FAIL.getMsg());
    }

    /**
     * 停用厅局
     *
     * @param vo id(多个用逗号分割)
     * @return
     */
    @ApiOperation(value = "版块管理-停用厅局")
    @Log(title = "版块管理-停用厅局", businessType = BusinessType.OTHER)
    @ResponseBody
    @PostMapping("/stop")
    public BaseResult stop(@Validated(SourceUpdate.class) @RequestBody HashMap<String, String> vo) throws Exception {
        String ids = vo.get("id");
        //判断是否是分组
        SourceManage sourceManage = sourceManageService.getById(ids);
        if(TypeEnums.GROUP.getCode().equals(sourceManage.getType())){
            //是分组，需要把分组下所有板块停用
            int i = sourceManageService.stopSourceManageGroupById(ids,sourceManage.getSourceSimpleName());
            if (i == 1) {
                return BaseResult.ok("操作完成");
            }
        }else {
            int i = sourceManageService.stopSourceManageById(ids);
            if (i == 1) {
                return BaseResult.ok("操作完成");
            }
        }
        return BaseResult.fail(ResultInfoEnum.STOP_SOURCE_FAIL.getMsg());
    }

    /**
     * 启用厅局
     *
     * @param vo id(多个用逗号分割)
     * @return
     */
    @ApiOperation(value = "版块管理-启用厅局")
    @Log(title = "版块管理-启用厅局", businessType = BusinessType.OTHER)
    @ResponseBody
    @PostMapping("/start")
    public BaseResult start(@Validated(SourceUpdate.class) @RequestBody HashMap<String, String> vo) throws Exception {
        String ids = vo.get("id");
        //判断是否是分组
        SourceManage sourceManage = sourceManageService.getById(ids);
        if(TypeEnums.GROUP.getCode().equals(sourceManage.getType())){
            //是分组，需要把分组下所有板块启用
            logger.info("启用分组");
            int i = sourceManageService.startSourceManageGroupById(ids,sourceManage.getSourceSimpleName());
            if (i == 1) {
                return BaseResult.ok("操作完成");
            }
        }else {
            logger.info("启用指标");
            int i = sourceManageService.startSourceManageById(ids);
            if (i == 1) {
                return BaseResult.ok("操作完成");
            }
        }
        return BaseResult.fail(ResultInfoEnum.START_SOURCE_FAIL.getMsg());
    }

    /**
     * 删除厅局
     *
     * @param requestVO
     * @return
     */
    @ApiOperation(value = "版块管理-删除厅局")
    @Log(title = "版块管理-删除厅局", businessType = BusinessType.DELETE)
    @ResponseBody
    @PostMapping("/delete")
    public BaseResult delete(@Validated(SourceUpdate.class) @RequestBody SourceManageReqVO requestVO) {
        Integer id = requestVO.getId();
        int i = sourceManageService.deleteSourceManageById(id);
        if (i == 1) {
            return BaseResult.ok("操作完成");
        }
        return BaseResult.fail(ResultInfoEnum.DELETE_SOURCE_FAIL.getMsg());
    }

    /**
     *
     * @return
     */
    @ApiOperation(value = "版块管理-获取板块分组列表")
    @ResponseBody
    @GetMapping("/getTypeList")
    public BaseResult getTypeList() {
        List<SourceManageTypeVO> result = sourceManageService.getTypeList(null);
        return BaseResult.ok(result);
    }

    /**
     *
     * @return
     */
    @ApiOperation(value = "版块管理-获取全部指标板块")
    @ResponseBody
    @GetMapping("/getAllIndicatorList")
    public BaseResult getAllIndicatorList() {
        List<SourceManage> sourceManageList = sourceManageService.getAllIndicatorsSourceManage();
        return BaseResult.ok(sourceManageList);
    }

    /**
     *
     * @return
     */
    @ApiOperation(value = "版块管理-新增修改板块链接")
    @ResponseBody
    @PostMapping("/addSourceManageUrl")
    public BaseResult addSourceManageUrl(@RequestBody SourceManageUrl sourceManageUrl) {
        return BaseResult.ok(sourceManageService.insertOrUpdateSourceManageUrl(sourceManageUrl.getUrlIds(),sourceManageUrl.getUrlName(),sourceManageUrl.getUrlType(),sourceManageUrl.getSourceSimpleName()));
    }

    /**
     *
     * @return
     */
    @ApiOperation(value = "版块管理-查询板块链接")
    @ResponseBody
    @GetMapping("/getSourceManageUrl")
    public BaseResult addSourceManageUrl(@RequestParam("sourceSN") String sourceSN) {
        return BaseResult.ok(sourceManageService.getSourceManageUrl(sourceSN));
    }

    /**
     *
     * @return
     */
    @ApiOperation(value = "版块管理-新增修改指标板块一二级分组展示方式")
    @ResponseBody
    @PostMapping("/addSourceManageGroupFlag")
    public BaseResult addSourceManageGroupFlag(@RequestBody SourceManageIndicatorGroup sourceManageIG) {
        return BaseResult.ok(sourceManageService.insertOrUpdateSourceManageIndicatorGroup(sourceManageIG.getFirstGroupFlag(),sourceManageIG.getSecondGroupFlag(),sourceManageIG.getAppReportInfoFlag(),sourceManageIG.getSourceSimpleName()));
    }

    /**
     *
     * @return
     */
    @ApiOperation(value = "版块管理-查询指标板块一二级分组展示方式")
    @ResponseBody
    @GetMapping("/getSourceManageGroupFlag")
    public BaseResult getSourceManageGroupFlag(@RequestParam("sourceSN") String sourceSN) {
        return BaseResult.ok(sourceManageService.getSourceManageIndicatorGroup(sourceSN));
    }

}