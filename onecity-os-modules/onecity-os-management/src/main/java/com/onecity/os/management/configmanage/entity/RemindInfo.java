package com.onecity.os.management.configmanage.entity;

import lombok.Data;
import java.util.Date;
import javax.persistence.*;

@Data
@Table(name = "remind_info")
public class RemindInfo {
    /**
     * 主键id
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Long id;

    /**
     * 提醒标题
     */
    @Column(name = "remind_title")
    private String remindTitle;

    /**
     * 厅局id(source_manage的主键id)
     */
    @Column(name = "source_id")
    private Long sourceId;

    /**
     * 是否已读 0-否;1-是
     */
    @Column(name = "is_read")
    private Byte isRead;

    /**
     * 已经提醒的次数:0-日提醒;1-首次提醒;2-逾期黄色提醒;3-逾期红色提醒;4-统计分析提醒
     */
    @Column(name = "reminded_count")
    private Byte remindedCount;

    /**
     * 提醒类型:0-非日提醒;1-日提醒
     */
    @Column(name = "reminded_type")
    private Byte remindedType;

    /**
     * 是否删除 0-否;1-是
     */
    @Column(name = "is_delete")
    private Byte isDelete;

    /**
     * 创建人
     */
    private String creater;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 提醒内容
     */
    @Column(name = "remind_content")
    private String remindContent;

    /**
     * HTML提醒内容
     */
    @Column(name = "html_content")
    private String htmlContent;

    /**
     * 提醒附属内容1，用于拆分提醒内容
     * (发送消息时，会根据sourceID进行消息合并，
     * 所以需要消息拆分，便于推送消息时多条消息合并)
     */
    @Column(name = "content_remark1")
    private String contentRemark1;

    /**
     * HTML提醒附属内容1，用于拆分提醒内容
     * (发送消息时，会根据sourceID进行消息合并，
     * 所以需要消息拆分，便于推送消息时多条消息合并)
     */
    @Column(name = "html_content_remark1")
    private String htmlContentRemark1;

    /**
     * 提醒附属内容2，用于拆分提醒内容
     * (发送消息时，会根据sourceID进行消息合并，
     * 所以需要消息拆分，便于推送消息时多条消息合并)
     */
    @Column(name = "content_remark2")
    private String contentRemark2;

    /**
     * HTML提醒附属内容2，用于拆分提醒内容
     * (发送消息时，会根据sourceID进行消息合并，
     * 所以需要消息拆分，便于推送消息时多条消息合并)
     */
    @Column(name = "html_content_remark2")
    private String htmlContentRemark2;

    /**
     * 管理平台提醒人ids,多个用英文逗号隔开
     */
    @Column(name = "pc_user_ids")
    private String pcUserIds;

    /**
     * 移动端提醒人,多个用英文逗号隔开
     */
    @Column(name = "app_user_ids")
    private String appUserIds;

    /**
     * 提醒时间,用于出发提醒job推送消息时间筛选
     */
    @Column(name = "remind_date")
    private Date remindDate;

    /**
     * 提醒类型，用于区分推送类型
     */
    @Column(name = "type")
    private String type;

    /**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取提醒标题
     *
     * @return remind_title - 提醒标题
     */
    public String getRemindTitle() {
        return remindTitle;
    }

    /**
     * 设置提醒标题
     *
     * @param remindTitle 提醒标题
     */
    public void setRemindTitle(String remindTitle) {
        this.remindTitle = remindTitle;
    }

    /**
     * 获取是否已读 0-否;1-是
     *
     * @return is_read - 是否已读 0-否;1-是
     */
    public Byte getIsRead() {
        return isRead;
    }

    /**
     * 设置是否已读 0-否;1-是
     *
     * @param isRead 是否已读 0-否;1-是
     */
    public void setIsRead(Byte isRead) {
        this.isRead = isRead;
    }

    /**
     * 获取已经提醒的次数
     *
     * @return reminded_count - 已经提醒的次数:0-日提醒;1-首次提醒;2-逾期黄色提醒;3-逾期红色提醒;4-统计分析提醒
     */
    public Byte getRemindedCount() {
        return remindedCount;
    }

    /**
     * 设置已经提醒的次数
     *
     * @param remindedCount 已经提醒的次数:0-日提醒;1-首次提醒;2-逾期黄色提醒;3-逾期红色提醒;4-统计分析提醒
     */
    public void setRemindedCount(Byte remindedCount) {
        this.remindedCount = remindedCount;
    }

    /**
     * 获取是否删除 0-否;1-是
     *
     * @return is_delete - 是否删除 0-否;1-是
     */
    public Byte getIsDelete() {
        return isDelete;
    }

    /**
     * 设置是否删除 0-否;1-是
     *
     * @param isDelete 是否删除 0-否;1-是
     */
    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * 获取创建人
     *
     * @return creater - 创建人
     */
    public String getCreater() {
        return creater;
    }

    /**
     * 设置创建人
     *
     * @param creater 创建人
     */
    public void setCreater(String creater) {
        this.creater = creater;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改人
     *
     * @return updater - 修改人
     */
    public String getUpdater() {
        return updater;
    }

    /**
     * 设置修改人
     *
     * @param updater 修改人
     */
    public void setUpdater(String updater) {
        this.updater = updater;
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取提醒内容
     *
     * @return remind_content - 提醒内容
     */
    public String getRemindContent() {
        return remindContent;
    }

    /**
     * 设置提醒内容
     *
     * @param remindContent 提醒内容
     */
    public void setRemindContent(String remindContent) {
        this.remindContent = remindContent;
    }

    /**
     * 获取管理平台提醒人ids,多个用英文逗号隔开
     *
     * @return pc_user_ids - 管理平台提醒人ids,多个用英文逗号隔开
     */
    public String getPcUserIds() {
        return pcUserIds;
    }

    /**
     * 设置管理平台提醒人ids,多个用英文逗号隔开
     *
     * @param pcUserIds 管理平台提醒人ids,多个用英文逗号隔开
     */
    public void setPcUserIds(String pcUserIds) {
        this.pcUserIds = pcUserIds;
    }

    /**
     * 获取移动端提醒人,多个用英文逗号隔开
     *
     * @return app_user_ids - 移动端提醒人,多个用英文逗号隔开
     */
    public String getAppUserIds() {
        return appUserIds;
    }

    /**
     * 设置移动端提醒人,多个用英文逗号隔开
     *
     * @param appUserIds 移动端提醒人,多个用英文逗号隔开
     */
    public void setAppUserIds(String appUserIds) {
        this.appUserIds = appUserIds;
    }
}