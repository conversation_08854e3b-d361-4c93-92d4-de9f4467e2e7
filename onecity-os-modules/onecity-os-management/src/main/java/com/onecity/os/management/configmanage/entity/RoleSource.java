package com.onecity.os.management.configmanage.entity;

import javax.persistence.*;
import java.util.Date;

@Table(name = "role_source")
public class RoleSource {
    /**
     * 主键id
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Long id;

    /**
     * 钉钉用户id
     */
    @Column(name = "role_id")
    private Long roleId;

    /**
     * 板块来源id
     */
    @Column(name = "source_id")
    private Long sourceId;

    /**
     * 创建人
     */
    @Column(name = "creater")
    private String creater;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
    /**
     * 批示权限：0 无权限，1有权限
     */
    @Column(name = "indicators_permissions")
    private Integer indicatorsPermissions;

    /**
     * 督办权限：0 无权限，1有权限
     */
    @Column(name = "supervise_permissions")
    private Integer supervisePermissions;

    /**
     * 红灯权限：0 无权限，1有权限
     */
    @Column(name = "red_permissions")
    private Integer redPermissions;
    /**
     *呈报审核模块权限：0 无权限，1有权限
     */
    @Column(name = "inform_audit_permissions")
    private Integer informAuditPermissions;
    /**
     *圈阅批示模块权限：0 无权限，1有权限
     */
    @Column(name = "circle_batch_permissions")
    private Integer circleBatchPermissions;
    /**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    /**
     * 获取板块来源id
     *
     * @return source_id - 板块来源id
     */
    public Long getSourceId() {
        return sourceId;
    }

    /**
     * 设置板块来源id
     *
     * @param sourceId 板块来源id
     */
    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    /**
     * 获取创建人
     *
     * @return creater - 创建人
     */
    public String getCreater() {
        return creater;
    }

    /**
     * 设置创建人
     *
     * @param creater 创建人
     */
    public void setCreater(String creater) {
        this.creater = creater;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getIndicatorsPermissions() {
        return indicatorsPermissions;
    }

    public void setIndicatorsPermissions(Integer indicatorsPermissions) {
        this.indicatorsPermissions = indicatorsPermissions;
    }

    public Integer getSupervisePermissions() {
        return supervisePermissions;
    }

    public void setSupervisePermissions(Integer supervisePermissions) {
        this.supervisePermissions = supervisePermissions;
    }

    public Integer getRedPermissions() {
        return redPermissions;
    }

    public void setRedPermissions(Integer redPermissions) {
        this.redPermissions = redPermissions;
    }

    public Integer getInformAuditPermissions() {
        return informAuditPermissions;
    }

    public void setInformAuditPermissions(Integer informAuditPermissions) {
        this.informAuditPermissions = informAuditPermissions;
    }

    public Integer getCircleBatchPermissions() {
        return circleBatchPermissions;
    }

    public void setCircleBatchPermissions(Integer circleBatchPermissions) {
        this.circleBatchPermissions = circleBatchPermissions;
    }
}