package com.onecity.os.management.configmanage.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import java.time.LocalDateTime;

/**
 * (SourceManage)表实体类
 *
 * <AUTHOR>
 * @since 2020-05-21 10:11:54
 */
@Data
public class SourceManage{
    @Id
    private Integer id;
    //来源id
    private String sourceId;
    //来源名称
    private String sourceName;
    //来源简称
    private String sourceSimpleName;
    //图标地址
    private String iconUrl;
    private Integer sequence;
    //是否删除0：否1：是
    private Integer isStart;
    //是否删除0：否1：是
    private Integer isDelete;
    //创建时间
    private LocalDateTime createTime;
    //创建人
    private String creater;
    //更新时间
    private LocalDateTime updateTime;
    //更新人
    private String updater;
    //类型
    private String type;
    //是否是指标板块：1 指标板块，2文章板块，3其他,4按钮权限，5项目板块
    private Integer isIndicators;
    /**
     * 关联指标id或者外部链接
     */
    private String urlIds;
    /**
     * 链接名称
     */
    private String urlName;

    /**
     * 链接类型0-无链接1-内部2-外部
     */
    private String urlType;

    /**
     * 指标板块一级分组是否隐藏0-隐藏1-展示
     */
    private String firstGroupFlag;

    /**
     * 指标板块二级分组是否隐藏0-隐藏1-展示
     */
    private String secondGroupFlag;

    /**
     * 指标板块信息简报是否展示0-隐藏1-展示
     */
    private String appReportInfoFlag;

}