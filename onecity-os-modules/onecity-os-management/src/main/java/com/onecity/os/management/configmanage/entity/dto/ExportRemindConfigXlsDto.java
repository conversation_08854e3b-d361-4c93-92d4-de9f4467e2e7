package com.onecity.os.management.configmanage.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.onecity.os.common.core.annotation.Excel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;

/**
 * 导出数据填报提醒配置列表excel出参
 *
 * <AUTHOR>
 * @date 2021/3/19 上午9:49
 */
@Data
public class ExportRemindConfigXlsDto {
    /**
     * 提醒主键id
     */
    private String id;
    /**
     * 板块名称
     */
    @Excel(name = "版块名称", width = 15)
    private String sourceName;

    /**
     * 展示顺序
     */
//    @Excel(name = "展示顺序", width = 10)
    private String sequence;

    /**
     * 板块类型
     */
    @Excel(name = "版块类型", width = 15)
    private String typeName;

    private String pcUserIds;

    /**
     * 管理平台提醒人
     */
    @Excel(name = "填报平台提醒人", width = 30)
    private String pcUserNames;

    private String appUserIds;

    /**
     * 移动端提醒人
     */
//    @Excel(name = "移动端提醒人", width = 30)
    private String appUserNames;

    /**
     * 更新人
     */
    @Excel(name = "更新人", width = 15)
    private String updater;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 20, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}














