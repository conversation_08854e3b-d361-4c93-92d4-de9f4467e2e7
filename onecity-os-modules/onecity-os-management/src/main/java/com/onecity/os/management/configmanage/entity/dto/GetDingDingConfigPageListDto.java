package com.onecity.os.management.configmanage.entity.dto;

import lombok.Data;

import java.util.Date;

/**
 * 获取移动端权限配置人员列表出参
 *
 * <AUTHOR>
 * @date 2021/1/6 下午4:20
 */
@Data
public class GetDingDingConfigPageListDto {
    /**
     * 钉钉用户id
     */
    private String userId;

    /**
     * 钉钉用户名
     */
    private String userName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 职位
     */
    private String position;

    /**
     * 部门
     */
    private String depart;

    /**
     * 角色
     */
    private String roleName;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 部门ids
     */
    private String departmentIds;

    /**
     * 角色ids
     */
    private String roles;
}
