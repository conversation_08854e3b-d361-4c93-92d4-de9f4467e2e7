package com.onecity.os.management.configmanage.entity.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 给指定钉钉用户,添加权限入参
 *
 * <AUTHOR>
 * @date 2021/1/7 上午9:27
 */
@Data
public class DingDingUserMenuVo {
    /**
     * 菜单列表id
     */
    @NotBlank(message = "菜单列表id不能为空")
    private Long id;

    /**
     *批示权限：0 无权限，1有权限
     */
    private Integer indicatorsPermissions;

    /**
     *督办权限：0 无权限，1有权限
     */
    private Integer supervisePermissions;

    /**
     *红灯权限：0 无权限，1有权限
     */
    private Integer redPermissions;
    /**
     *呈报审核模块权限：0 无权限，1有权限
     */
    private Integer informAuditPermissions;
    /**
     *圈阅批示模块权限：0 无权限，1有权限
     */
    private Integer circleBatchPermissions;
}
