package com.onecity.os.management.configmanage.entity.vo;

import lombok.Data;

@Data
public class PermissionsVo {

    /**
     * 督办
     */
    private boolean supervisePermissions;
    private boolean hasSupervise;

    /**
     * 红灯
     */
    private boolean redPermissions;
    private boolean hasRed;

    /**
     *呈报审核模块权限：0 无权限，1有权限
     */
    private boolean informAuditPermissions;
    /**
     *圈阅批示模块权限：0 无权限，1有权限
     */
    private boolean circleBatchPermissions;
}