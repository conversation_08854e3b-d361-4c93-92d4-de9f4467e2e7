package com.onecity.os.management.configmanage.entity.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;


/**
 * 厅局返回实体
 *
 * <AUTHOR>
 * @since 2020-05-21 10:11:54
 */
@Data
public class SourceManageResVO implements Serializable{
    private static final long serialVersionUID = 4971491472357995647L;

    private Integer id;
    //来源id
    private String sourceId;
    //来源名称
    private String sourceName;
    //来源简称
    private String sourceSimpleName;
    //图标地址
    private String iconUrl;
    //是否启用0：停用1：启用
    private Integer isStart;

    private Integer sequence;

    private String creater;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String updater;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    private String type;
    private String typeName;

    //是否是指标板块：0 否 1 是
    private Integer isIndicators;

}