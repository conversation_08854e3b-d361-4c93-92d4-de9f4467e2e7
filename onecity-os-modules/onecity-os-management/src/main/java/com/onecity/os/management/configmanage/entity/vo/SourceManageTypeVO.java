package com.onecity.os.management.configmanage.entity.vo;

import com.onecity.os.management.configmanage.entity.dto.GetMenuListDto;
import lombok.Data;
import java.io.Serializable;
import java.util.List;


/**
 */
@Data
public class SourceManageTypeVO implements Serializable{
    public SourceManageTypeVO(String name,String code) {
        this.name = name;
        this.code = code;
    }
    //名称
    private String name;
    //code
    private String code;
    //分组下的板块
    private List<GetMenuListDto> menuList;
}