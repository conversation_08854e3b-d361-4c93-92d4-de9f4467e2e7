package com.onecity.os.management.configmanage.mapper;

import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.management.configmanage.entity.DingmailUserMenu;
import com.onecity.os.management.configmanage.entity.RoleSource;
import com.onecity.os.management.configmanage.entity.dto.DingConExcel;
import com.onecity.os.management.configmanage.entity.dto.GetDingDingConfigPageListDto;
import com.onecity.os.management.configmanage.entity.dto.GetDingDingMenuListDto;
import com.onecity.os.management.configmanage.entity.vo.DingDingUserMenuVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.Date;
import java.util.List;
@Mapper
public interface DingmailUserMenuMapper extends BaseMapper<DingmailUserMenu> {
    /**
     * 获取移动端权限配置人员列表
     *
     * @param name
     * @param phone
     * @param departIds
     * @param position
     * @param depart
     * @return
     */
    List<GetDingDingConfigPageListDto> getDingDingConfigPageList(@Param("name") String name,
                                                                 @Param("phone") String phone,
                                                                 @Param("departIds") String departIds,
                                                                 @Param("position") String position,
                                                                 @Param("depart") String depart);

    /**
     * 获取移动端菜单列表
     *
     * @param name
     * @return
     */
    List<GetDingDingMenuListDto> getDingDingMenuList(@Param("name") String name);

    /**
     * 根据钉钉用户id,更新钉钉用户表的更新时间
     *
     * @param userId
     * @param date
     * @param loginName
     */
    void updateDingUserUpdateTimeByUserId(@Param("userId") String userId, @Param("date") Date date,
                                          @Param("loginName") String loginName);

    /**
     * 根据用户id,查找已经分配的模块id
     *
     * @param userId
     * @return
     */
    List<DingDingUserMenuVo> getSourceIdByUserId(@Param("userId") String userId);

    /**
     * 根据名称,查找出自定义的市直机关或者区县纵横
     *
     * @param name
     * @return
     */
    GetDingDingMenuListDto getDingDingMenuSzjgOrQxzh(@Param("name") String name);

    /**
     * 根据厅局id,获取钉钉通讯录用户信息列表--分页
     *
     * @param name
     * @param phone
     * @param departId
     * @param position
     * @return
     */
    List<GetDingDingConfigPageListDto> getDingDingConfigByDepartIdPageList(@Param("name") String name,
                                                                           @Param("phone") String phone,
                                                                           @Param("departId") String departId,
                                                                           @Param("position") String position);

    /**
     * 获取部门权限导出信息
     * @param departId
     * @return
     */
    List<DingConExcel> exportDingConfig(@Param("name") String name,
                                        @Param("phone") String phone,
                                        @Param("position") String position,
                                        @Param("departId") String departId);

    /**
     * 获取已分配模块名称
     * @param userId
     * @return
     */
    List<String> getSourceByUserId(@Param("userId") String userId);


    List<String> getPermissionsByUserId(@Param("roleList") List<String> roleList, @Param("indicatorId") String indicatorId);

    List<String> getSupervisePermissions(@Param("roleList") List<String> roleList, @Param("indicatorId") String indicatorId);
    List<String> getRedPermissions(@Param("roleList") List<String> roleList, @Param("indicatorId") String indicatorId);
    List<Long> checkUserCircleApprovalPermission(@Param("roleList") List<Long> roleList);
    void deleteByUserId(@Param("userId") String userId);
    List<RoleSource> getInformPermissions(@Param("roleList") List<String> roleList,@Param("sourceId") Integer sourceId);
}





