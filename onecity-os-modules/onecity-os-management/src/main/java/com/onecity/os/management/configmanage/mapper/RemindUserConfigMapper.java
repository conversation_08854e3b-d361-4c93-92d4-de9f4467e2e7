package com.onecity.os.management.configmanage.mapper;

import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.management.configmanage.entity.RemindUserConfig;
import com.onecity.os.management.configmanage.entity.dto.ExportRemindConfigXlsDto;
import com.onecity.os.management.configmanage.entity.dto.GetRemindConfigPageListDto;
import com.onecity.os.management.configmanage.entity.dto.RemindUserConfigDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface RemindUserConfigMapper extends BaseMapper<RemindUserConfig> {

    /**
     * 获取填报提醒配置列表
     *
     * @param type
     * @param sourceName
     * @return
     */
    List<GetRemindConfigPageListDto> getRemindPageList(@Param("type") String type,
                                                       @Param("sourceName") String sourceName);

    /**
     * 根据用户id,查找pc端姓名
     *
     * @param ids
     * @return
     */
    String getPcUserNamesByPcUserIds(@Param("ids") String[] ids);

    /**
     * 根据用户ids,查找APP端姓名
     *
     * @param ids
     * @return
     */
    String getAppUserNamesByPcUserIds(@Param("ids") String[] ids);

    /**
     * 获取填报提醒配置信息
     *
     * @return
     */
    List<RemindUserConfigDto> getRemindInfoList();

    /**
     * 获取填报提醒配置信息
     *
     * @return
     */
    RemindUserConfigDto getRemindInfoBySourceId(Long sourceId);

    /**
     * 根据用户名,查找pc端人员id
     *
     * @param names
     * @return
     */
    String getPcUserIdsByPcUserNames(@Param("names") String[] names);

    /**
     * 根据用户名,查找app端人员id
     *
     * @param names
     * @return
     */
    String getAppUserIdsByPcUserNames(@Param("names") String[] names);

    /**
     * 根据主键id,更新PC提醒人
     *
     * @param remindUserConfig
     */
    void updatePcUserIdsById(@Param("vo") RemindUserConfig remindUserConfig);

    /**
     * 根据主键id,更新APP提醒人
     *
     * @param remindUserConfig
     */
    void updateAppUserIdsById(@Param("vo") RemindUserConfig remindUserConfig);

    /**
     * 查找所有开通的厅局sourceId
     *
     * @return
     */
    List<RemindUserConfigDto> getAllStartedTjSourceList();

    /**
     * 导出数据填报提醒配置列表excel
     *
     * @param type
     * @param sourceName
     * @return
     */
    List<ExportRemindConfigXlsDto> exportRemindConfigXls(@Param("type") String type, @Param("sourceName") String sourceName);

    /**
     * 查找PC端提醒人
     *
     * @return
     */
    List<ExportRemindConfigXlsDto> getPcUserNames();

    /**
     * 查找APP端提醒人
     *
     * @return
     */
    List<ExportRemindConfigXlsDto> getAppUserNames();

    /**
     *
     * @param remindUserConfig
     * @return
     */
    int updateBySourceId(RemindUserConfig remindUserConfig);
}














