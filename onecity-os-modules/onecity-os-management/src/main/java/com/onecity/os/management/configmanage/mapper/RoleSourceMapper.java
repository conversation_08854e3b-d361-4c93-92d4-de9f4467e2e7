package com.onecity.os.management.configmanage.mapper;

import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.management.configmanage.entity.DingmailUserMenu;
import com.onecity.os.management.configmanage.entity.RoleSource;
import com.onecity.os.management.configmanage.entity.dto.DingConExcel;
import com.onecity.os.management.configmanage.entity.dto.GetDingDingConfigPageListDto;
import com.onecity.os.management.configmanage.entity.dto.GetDingDingMenuListDto;
import com.onecity.os.management.configmanage.entity.vo.DingDingUserMenuVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface RoleSourceMapper extends BaseMapper<RoleSource> {

    /**
     * 根据用户id,查找已经分配的模块id
     *
     * @param roleId
     * @return
     */
    List<DingDingUserMenuVo> getSourceIdByRoleId(@Param("roleId") Long roleId);


    /**
     * 获取已分配模块名称
     * @param roleId
     * @return
     */
    List<String> getSourceByRoleId(@Param("roleId") Long roleId);


    String getPermissionsByRoleId(@Param("roleId") Long roleId, @Param("indicatorId") String indicatorId);

    void deleteByRoleId(@Param("roleId") Long roleId);
}





