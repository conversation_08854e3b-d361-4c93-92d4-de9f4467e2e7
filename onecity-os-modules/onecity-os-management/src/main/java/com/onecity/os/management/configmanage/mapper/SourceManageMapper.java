package com.onecity.os.management.configmanage.mapper;

import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.management.configmanage.entity.SourceManage;
import com.onecity.os.management.configmanage.entity.dto.GetMenuListDto;
import com.onecity.os.management.configmanage.entity.dto.SourceManageIndicatorGroup;
import com.onecity.os.management.configmanage.entity.dto.SourceManageUrl;
import com.onecity.os.management.configmanage.entity.dto.SourceTableDTO;
import com.onecity.os.management.configmanage.entity.vo.SourceManageTypeVO;
import com.onecity.os.management.statisticanalysis.entity.dto.GetStatisticAnalysisPageListDto;
import com.onecity.os.management.configmanage.entity.vo.SourceManageResVO;
import com.onecity.os.management.zhibiaowarning.entity.vo.SourceManageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * (SourceManage)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-05-21 10:11:56
 */
@Mapper
public interface SourceManageMapper extends BaseMapper<SourceManage> {

    void createIndicatorTable(SourceTableDTO req);

    int updateByPrimaryKeySelective(SourceManage req);

    /**
     * 根据厅局简称,查找厅局信息
     *
     * @param sourceSimpleName
     * @return
     */
    SourceManage getSourceInfoBySourceSimpleName(@Param("sourceSimpleName") String sourceSimpleName);

    /**
     * 根据id,查找简称
     *
     * @param sourceId
     * @return
     */
    String getSourceSimpleNameBySourceId(@Param("sourceId") Long sourceId);

    /**
     * 新版本--统计分析--查找厅局信息
     *
     * @param sourceName
     * @return
     */
    List<GetStatisticAnalysisPageListDto> getSourceManagePageList(@Param("sourceName") String sourceName);

    /**
     * 根据厅局id,获取厅局名称
     *
     * @param sourceId
     * @return
     */
    String getSourceNameBySourceId(@Param("sourceId") Long sourceId);

    /**
     * 获取所有厅局 source_simple_name
     * @return
     */
    List<String> getAllSourceManage();

    /**
     * 获取所有板块
     */
    List<SourceManage> getAllSourceManage1();

    /**
     * 新版本--统计分析--查找厅局信息
     *
     * @return
     */
    List<GetStatisticAnalysisPageListDto> getSourceForAnalysis();

    /**
     *
     * @param sourceSimpleName
     * @return
     */
    int countBySourceSimpleName(@Param("sourceSimpleName") String sourceSimpleName);

    int countAll();

    List<SourceManageResVO> selectPage(@Param("type") String type,@Param("sourceName") String sourceName,@Param("isStart") Integer isStart);

    SourceManage getSourceInfoBySourceId(@Param("sourceId") String sourceId);

    /**
     * @return
     */
    SourceManage getSourceInfoById(@Param("id") String id);

    /**
     * 获取分类列表
     * @return
     */
    List<SourceManageTypeVO> getTypeList(@Param("type") String type);

    /**
     * 获取某一分组下所有板块id
     * @param type
     * @return
     */
    List<String> getIdListByType(@Param("type") String type,@Param("isStart") Integer isStart);

    /**
     * 获取分组下的板块
     * @param roleIds
     * @param type
     * @return
     */
    List<GetMenuListDto> getMenuListByRoleIdAndType(@Param("roleIds") Long[] roleIds, @Param("type") String type);

    /**
     * 新增或修改版块的链接
     */
    int insertOrUpdateSourceManageUrl(@Param("urlIds") String urlIds,@Param("urlName")String urlName,@Param("urlType") String urlType,@Param("sourceSimpleName") String sourceSimpleName);
    /**
     * 查询板块的链接
     */
    SourceManageUrl getSourceManageUrl(@Param("sourceSimpleName") String sourceSimpleName);

    /**
     * 新增或修改指标版块的一级二级分组的展示标识
     */
    int insertOrUpdateSourceManageIndicatorGroup(@Param("firstGroupFlag") String firstGroupFlag,@Param("secondGroupFlag") String secondGroupFlag,@Param("appReportInfoFlag") String appReportInfoFlag,@Param("sourceSimpleName") String sourceSimpleName);
    /**
     * 查询板块的链接
     */
    SourceManageIndicatorGroup getSourceManageIndicatorGroup(@Param("sourceSimpleName") String sourceSimpleName);

    /**
     * 根据用户ID查询有权限的版块列表
     *
     * @param sourceSimpleNameList 版块编码列表
     * @return 版块列表
     */
    List<SourceManageVo> getSourceManageBySourceSimpleNameList(@Param("sourceSimpleNameList") List<String> sourceSimpleNameList);
}