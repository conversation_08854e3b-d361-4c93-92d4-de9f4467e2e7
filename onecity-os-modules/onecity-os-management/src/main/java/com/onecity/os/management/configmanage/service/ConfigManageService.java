package com.onecity.os.management.configmanage.service;

import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.management.api.vo.PageResult;
import com.onecity.os.management.configmanage.entity.RemindInfo;
import com.onecity.os.management.configmanage.entity.dto.*;
import com.onecity.os.management.configmanage.entity.vo.AddDingDingUserMenuVo;
import com.onecity.os.management.configmanage.entity.vo.DingDingUserMenuVo;
import com.onecity.os.management.configmanage.entity.vo.PermissionsVo;
import com.onecity.os.management.configmanage.entity.vo.SaveRemindUserVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/6 下午3:58
 */
public interface ConfigManageService {
    /**
     * 获取移动端权限配置人员列表
     *
     * @param name
     * @param phone
     * @param depart
     * @param position
     * @return
     */
    List<GetDingDingConfigPageListDto> getDingDingConfigPageList(String name, String phone, String depart, String position);

    /**
     * 获取移动端菜单列表
     *
     * @param name
     * @return
     */
    List<GetDingDingMenuListDto> getDingDingMenuList(String name);

    /**
     * 给指定钉钉用户,添加权限
     *
     * @param vo
     */
    void addDingDingUserMenu(AddDingDingUserMenuVo vo) throws Exception;

    /**
     * 根据角色id,查找已经分配的模块id
     *
     * @param roleId
     * @return
     */
    List<DingDingUserMenuVo> getMenuIdListByUserId(Long roleId);

    /**
     * 判断当前用户是否有圈批权限
     * @return
     */
    int checkUserCircleApprovalPermission();

    /**
     * 获取PC填报系统人员列表
     *
     * @param name
     * @param phone
     * @param depart
     * @return
     */
    TableDataInfo getTianBaoUserPageList(String name, String phone, String depart, Integer pageNum, Integer pageSize);

    /**
     * 获取填报提醒配置列表
     *
     * @param type
     * @param sourceName
     * @return
     */
    List<GetRemindConfigPageListDto> getRemindConfigPageList(String type, String sourceName) throws Exception;

    /**
     * 数据填报提醒配置,配置提醒人
     *
     * @param vo
     */
    void saveRemindUser(SaveRemindUserVo vo) throws Exception;

    /**
     * 获取提醒中心列表
     *
     * @param userId
     * @return
     */
    List<RemindInfo> getRemindInfoPageList(String userId);

    /**
     * 根据提醒中心消息id,将消息设置为已读状态
     *
     * @param id
     */
    void updateRemindIsReadById(Long id) throws Exception;

    /**
     * 导出数据填报提醒配置列表excel
     *
     * @param type
     * @param sourceName
     * @return
     */
    List<ExportRemindConfigXlsDto> exportRemindConfigXls(String type, String sourceName);

    /**
     * 根据部门id,获取移动端权限配置人员列表
     *
     * @param name
     * @param phone
     * @param departId
     * @param position
     * @return
     */
    List<GetDingDingConfigPageListDto> getDingDingConfigByDepartIdPageList(String name, String phone, String departId, String position);

    /**
     * 移动端权限配置导出
     * @param departId
     * @return
     */
    List<DingConExcel> exportDingConfig(String name, String phone, String position, String departId);


    boolean getPermissionsByUserId(List<String> roleList, String indicatorId);

    PermissionsVo getPermissionsByRoleList(String userId,List<String> roleList, String indicatorId);

    PermissionsVo hasInformPermissions();

}










