package com.onecity.os.management.configmanage.service;

/**
 * 填报提醒信息
 *
 * <AUTHOR>
 * @date 2021/1/12 下午3:57
 */
public interface RemindInfoService {

    /**
     * 保存填报提醒信息内容(首次填报提醒)
     */
    void addRemindInfo();

    /**
     * 填报逾期提醒
     */
    void overDueRemind();

    /**
     * CHBN-填报统计分析计算
     */
    void CHBNStatisticalAnalysisCal(int monFlag);

    /**
     * CHBN-填报统计分析计算
     */
    void sendRemindMsg();
}

















