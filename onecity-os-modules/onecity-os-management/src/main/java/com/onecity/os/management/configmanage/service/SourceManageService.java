package com.onecity.os.management.configmanage.service;

import com.onecity.os.management.configmanage.entity.SourceManage;
import com.onecity.os.management.configmanage.entity.dto.SourceManageIndicatorGroup;
import com.onecity.os.management.configmanage.entity.dto.SourceManageUrl;
import com.onecity.os.management.configmanage.entity.vo.SourceManageReqVO;
import com.onecity.os.management.configmanage.entity.vo.SourceManageResVO;
import com.onecity.os.management.configmanage.entity.vo.SourceManageTypeVO;
import com.onecity.os.management.zhibiaowarning.entity.vo.SourceManageVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (SourceManage)表服务接口
 *
 * <AUTHOR>
 * @since 2020-05-21 10:11:56
 */
public interface SourceManageService {
    int  countBySourceSimpleName(String sourceSimpleName);

    int insertAndCreateTable(SourceManageReqVO sourceManageVO);

    int updateSourceManageById(SourceManageReqVO sourceManageVO);

    int stopSourceManageGroupById(String id,String type) throws Exception;
    int stopSourceManageById(String id) throws Exception;

    int startSourceManageGroupById(String id,String type) throws Exception;
    int startSourceManageById(String id) throws Exception;

    int deleteSourceManageById(Integer id);

    List<SourceManageResVO> listSource(String type, String sourceName,Integer isStart);

    /**
     * 根据sourceId获取厅局信息
     * @param sourceId
     * @return
     */
    SourceManage getBySourceId(String sourceId);
    /**
     * 根据id获取厅局信息
     * @return
     */
    SourceManage getById(String id);


    /**
     * 获取分类列表
     * @return
     */
    List<SourceManageTypeVO> getTypeList(Long[] roleIds);

    /**
     * 获取全部指标板块
     */
    List<SourceManage> getAllIndicatorsSourceManage();

    /**
     * 新增或修改版块的链接
     */
    int insertOrUpdateSourceManageUrl(String urlIds, String urlName,String urlType,String sourceSimpleName);
    /**
     * 查询板块的链接
     */
    SourceManageUrl getSourceManageUrl(String sourceSimpleName);

    /**
     * 新增或修改指标版块的一级二级分组的展示标识
     */
    int insertOrUpdateSourceManageIndicatorGroup(String first,String second,String appReportInfoFlag,String sourceSimpleName);
    /**
     * 查询板块的链接
     */
    SourceManageIndicatorGroup getSourceManageIndicatorGroup(String sourceSimpleName);

    /**
     * 根据版块编码列表的指标版块和项目版块
     *
     * @return 版块列表
     */
    List<SourceManageVo> getSourceManageBySourceSimpleNameList(List<String> sourceSimpleNameList);

}