package com.onecity.os.management.configmanage.service.impl;

import com.onecity.os.common.core.utils.UUIDGenerator;
import com.onecity.os.management.configmanage.entity.vo.CheckTokenVo;
import com.onecity.os.management.configmanage.service.AuthManageService;
import com.ruoyi.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/3/3 上午9:27
 */
@Slf4j
@Service
public class AuthManageServiceImpl implements AuthManageService {

    @Autowired
    private RedisService redisService;

    @Override
    public String getTokenByUserName(String userName) throws Exception {
        try {
            // 先校验该手机号,在数据库中是否存在
            int count = 0;
                    //sysUserMapper.checkPhoneIsExist(userName);
            // 不存在,返回null
            if (0 >= count) {
                return null;
            }
            long timeStamp = System.currentTimeMillis();
            // 存在,生成token,存入到redis,以便后续校验
            String token = DigestUtils.md5DigestAsHex((UUIDGenerator.generate() + timeStamp).getBytes());
            // token为key,用户名(手机号)为value,有效时间1小时
            redisService.setCacheObject(token, userName, 60 * 60L, TimeUnit.SECONDS);
            return token;
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("生成token失败");
        }
    }

    @Override
    public int checkToken(CheckTokenVo vo) throws Exception {
        try {
            // 获取时间戳,判断是否是1小时以内的
            Long timeStampReceived = vo.getTimeStamp();
            // 1小时后的时间戳
            long timeStamp1HourLater = System.currentTimeMillis() + 60 * 60 * 1000;
            // 验证时间已过期
            if (0 > timeStamp1HourLater - timeStampReceived) {
                return 1;
            }
            // 根据token,从redis中获取用户名(手机号)
            Object userName = redisService.getCacheObject(vo.getToken());
            // 没有value,时间过期或者token无效
            if (ObjectUtils.isEmpty(userName)) {
                return 1;
            }
            String signStr = userName + String.valueOf(vo.getTimeStamp()) + vo.getToken();
            String sign = DigestUtils.md5DigestAsHex(signStr.getBytes());
            // 比对两边的签名,一致则返回0,不一致则返回1
            if (sign.equals(vo.getSign())) {
                return 0;
            }
            return 1;
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("验证失败");
        }
    }



}


























