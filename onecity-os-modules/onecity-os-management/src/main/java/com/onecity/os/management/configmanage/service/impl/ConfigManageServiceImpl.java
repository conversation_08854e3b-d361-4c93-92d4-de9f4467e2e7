package com.onecity.os.management.configmanage.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.exception.ServiceException;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.management.configmanage.entity.*;
import com.onecity.os.management.configmanage.entity.dto.*;
import com.onecity.os.management.configmanage.entity.vo.AddDingDingUserMenuVo;
import com.onecity.os.management.configmanage.entity.vo.DingDingUserMenuVo;
import com.onecity.os.management.configmanage.entity.vo.PermissionsVo;
import com.onecity.os.management.configmanage.entity.vo.SaveRemindUserVo;
import com.onecity.os.management.configmanage.enums.TypeEnums;
import com.onecity.os.management.configmanage.mapper.*;
import com.onecity.os.management.configmanage.service.ConfigManageService;
import com.onecity.os.management.feign.SuperviseFeignService;
import com.onecity.os.system.api.RemoteUserService;
import com.onecity.os.system.api.domain.SysMenu;
import com.onecity.os.system.api.domain.SysUser;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/1/6 下午3:58
 */
@Slf4j
@Service
public class ConfigManageServiceImpl implements ConfigManageService {

    @Resource
    private DingmailUserMenuMapper dingdingUserMenuMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    @Resource
    private RemindUserConfigMapper remindUserConfigMapper;

    @Resource
    private RemindInfoMapper remindInfoMapper;

    @Resource
    private SourceManageMapper sourceManageMapper;

    @Value("${ypcb.sourceSimpleName}")
    private String ypcbSourceSimpleName;

//    @Resource
//    DingMailDepartmentMapper dingMailDepartmentMapper;
//
//    @Resource
//    DingMailRoleMapper dingMailRoleMapper;

    @Resource
    private DingmailUserMenuMapper dingmailUserMenuMapper;
    @Resource
    private RoleSourceMapper roleSourceMapper;
    @Autowired
    SuperviseFeignService superviseFeignService;

    @Override
    public List<GetDingDingConfigPageListDto> getDingDingConfigPageList(String name, String phone, String depart, String position) {
        // 先查找部门ids(用竖线 | 隔开)
        String departIds = "";
        if (StringUtils.isNotEmpty(depart)) {
//            departIds = dingMailDepartmentMapper.getDepartIdsByName(depart);
        }
        List<GetDingDingConfigPageListDto> dtoList = dingdingUserMenuMapper.getDingDingConfigPageList(name, phone, departIds, position, depart);
        if (CollectionUtils.isNotEmpty(dtoList)) {
            // 查询角色
            for (GetDingDingConfigPageListDto dto : dtoList) {
                if (StringUtils.isNotBlank(dto.getRoles())) {
                    List<String> roles = Arrays.asList(dto.getRoles().split(","));
//                    User user = dingMailRoleMapper.getRoleNameByRoleIds(roles);
//                    if (null != user) {
//                        dto.setRoles(user.getRoles());
//                        dto.setRoleName(user.getRoles());
//                    }
                }
                // 如果有多个部门的,查询多个部门(用逗号拼接)
                if (StringUtils.isNotBlank(dto.getDepartmentIds()) && dto.getDepartmentIds().contains(",")) {
                    List<String> departs = Arrays.asList(dto.getDepartmentIds().split(","));
//                    String departName = dingMailDepartmentMapper.getDepartNamesByDeparts(departs);
                    String departName = "";
                    dto.setDepart(departName);
                }
            }
        }
        return dtoList;
    }

    @Override
    public List<GetDingDingMenuListDto> getDingDingMenuList(String name) {
        List<GetDingDingMenuListDto> listDtos = dingdingUserMenuMapper.getDingDingMenuList(name);
        List<GetDingDingMenuListDto> resultList = new ArrayList<>();
        //其它分组
        GetDingDingMenuListDto other = new GetDingDingMenuListDto();
        other.setId(-1L);
        other.setSourceName(TypeEnums.OTHER.getName());
        other.setSourceSimpleName(TypeEnums.OTHER.getCode());
        resultList.add(other);
        if(null != listDtos && listDtos.size() > 0){
            //创建的分组
            List<GetDingDingMenuListDto> groupList = listDtos.stream().filter(getDingDingMenuListDto -> TypeEnums.GROUP.getCode().equals(getDingDingMenuListDto.getType())).collect(Collectors.toList());
            if(null != groupList){
                resultList.addAll(groupList);
            }
            //查询分组下的子集
            for(GetDingDingMenuListDto getDingDingMenuListDto : resultList){
                //查询分组下板块
                List<GetDingDingMenuListDto> chlidList = listDtos.stream().filter(menuListDto -> getDingDingMenuListDto.getSourceSimpleName().equals(menuListDto.getType())).collect(Collectors.toList());
                getDingDingMenuListDto.setChildMenu(chlidList);
            }
        }
        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addDingDingUserMenu(AddDingDingUserMenuVo vo) throws Exception {
        try {
            // 先根据用户id,将用户菜单关系删除
            roleSourceMapper.deleteByRoleId(vo.getRoleId());
            if (CollectionUtils.isNotEmpty(vo.getIds())) {
                //当前用户信息
                String loginName = null;
                LoginUser sysUser = SecurityUtils.getLoginUser();
                if (ObjectUtils.isNotEmpty(sysUser)) {
                    loginName = sysUser.getUsername();
                }
                Date date = new Date();
                // 再根据用户id,和菜单id,添加数据
                for (DingDingUserMenuVo menuVo : vo.getIds()) {
                    RoleSource roleSource = new RoleSource();
                    roleSource.setRoleId(vo.getRoleId());
                    roleSource.setSourceId(menuVo.getId());
                    roleSource.setIndicatorsPermissions(menuVo.getIndicatorsPermissions());
                    roleSource.setSupervisePermissions(menuVo.getSupervisePermissions());
                    roleSource.setRedPermissions(menuVo.getRedPermissions());
                    roleSource.setInformAuditPermissions(menuVo.getInformAuditPermissions());
                    roleSource.setCircleBatchPermissions(menuVo.getCircleBatchPermissions());
                    roleSource.setCreater(loginName);
                    roleSource.setCreateTime(date);
                    roleSourceMapper.insert(roleSource);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("给指定钉钉用户,添加权限失败");
            throw new Exception("给指定钉钉用户,添加权限失败");
        }
    }

    @Override
    public List<DingDingUserMenuVo> getMenuIdListByUserId(Long roleId) {
        return roleSourceMapper.getSourceIdByRoleId(roleId);
    }

    @Override
    public TableDataInfo getTianBaoUserPageList(String name, String phone, String depart,Integer pageNum,Integer pageSize) {
        SysUser user = new SysUser();
        user.setNickName(name);
        user.setPhonenumber(phone);
        user.setPageNum(pageNum);
        user.setPageSize(pageSize);
        TableDataInfo result = remoteUserService.list(user);
        if(null == result){
            throw new ServiceException("远程调用，查询用户列表为空");
        }
        return result;
    }

    @Override
    public List<GetRemindConfigPageListDto> getRemindConfigPageList(String type, String sourceName) throws Exception {
        try {
            List<GetRemindConfigPageListDto> pageList = remindUserConfigMapper.getRemindPageList(type, sourceName);
            if (CollectionUtils.isNotEmpty(pageList)) {
                RemindUserConfig remindUserConfig = new RemindUserConfig();
                for (GetRemindConfigPageListDto dto : pageList) {
                    // 查找填报系统人员名称
                    if (StringUtils.isNotEmpty(dto.getPcUserIds()) && dto.getPcUserIds().split(",").length > 0) {
                        //远程调用
                        BaseResult<String> pcUserNameResult = remoteUserService.getPcUserNamesByPcUserIds(dto.getPcUserIds().split(","));
                        if (BaseResult.FAIL == pcUserNameResult.getCode())
                        {
                            throw new ServiceException(pcUserNameResult.getMsg());
                        }
                        String pcUserName = pcUserNameResult.getData();
                        dto.setPcUserNames(pcUserName);
                        // 再根据人名,查找id,更新填报提醒配置的用户id(原因是因为,删除了用户后,remind_user_config表的用户id没有更新)
                        if (StringUtils.isNotEmpty(pcUserName)) {
                            //远程调用
                            BaseResult<String> pcUserIdResult = remoteUserService.getPcUserIdsByPcUserNames(pcUserName.split(","));
                            if (BaseResult.FAIL == pcUserIdResult.getCode())
                            {
                                throw new ServiceException(pcUserIdResult.getMsg());
                            }
                            String pcUserId = pcUserIdResult.getData();
                            remindUserConfig.setId(dto.getZhujianid());
                            remindUserConfig.setPcUserIds(pcUserId);
                            remindUserConfigMapper.updatePcUserIdsById(remindUserConfig);
                        }
                    }
                    // 查找APP人员名称
                    if (StringUtils.isNotEmpty(dto.getAppUserIds()) && dto.getAppUserIds().split(",").length > 0) {
                        String appUserName = remindUserConfigMapper.getAppUserNamesByPcUserIds(dto.getAppUserIds().split(","));
                        dto.setAppUserNames(appUserName);
                        // 再根据人名,查找id,更新填报提醒配置的用户id(原因是因为,删除了用户后,remind_user_config表的用户id没有更新)
                        if (StringUtils.isNotEmpty(appUserName)) {
                            String appUserId = remindUserConfigMapper.getAppUserIdsByPcUserNames(appUserName.split(","));
                            remindUserConfig.setId(dto.getZhujianid());
                            remindUserConfig.setAppUserIds(appUserId);
                            remindUserConfigMapper.updateAppUserIdsById(remindUserConfig);
                        }
                    }
                    //调整更新人展示
                    //更新人匹配
                    if (StringUtils.isNotEmpty(dto.getUpdater())) {
                        BaseResult<String> nickName = remoteUserService.getNickNameByName(dto.getUpdater());
                        dto.setUpdater(nickName.getData());
                    }
                }
            }
            return pageList;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取填报提醒配置列表--查询失败");
            throw new Exception("查询失败");
        }
    }

    @Override
    public void saveRemindUser(SaveRemindUserVo vo) throws Exception {
        try {
            RemindUserConfig remindUserConfig = new RemindUserConfig();
            remindUserConfig.setPcUserIds(vo.getPcUserIds());
            remindUserConfig.setAppUserIds(vo.getPcUserIds());
            remindUserConfig.setUpdateTime(new Date());
            LoginUser sysUser = SecurityUtils.getLoginUser();
            if (sysUser != null) {
                remindUserConfig.setUpdater(sysUser.getUsername());
            }
            remindUserConfig.setSourceId(vo.getId());
            int res = remindUserConfigMapper.updateBySourceId(remindUserConfig);
            if (0 >= res) {
                // 新增
                remindUserConfig.setSourceId(vo.getId());
                remindUserConfig.setIsDelete((byte) 0);
                remindUserConfigMapper.insert(remindUserConfig);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("保存失败");
        }
    }

    @Override
    public List<RemindInfo> getRemindInfoPageList(String userId) {
        List<RemindInfo> pageList = remindInfoMapper.getRemindInfoPageList(userId);
        return pageList;
    }

    @Override
    public void updateRemindIsReadById(Long id) throws Exception {
        try {
            remindInfoMapper.updateIsRead(id);
        } catch (Exception e) {
            throw new Exception("将消息设置为已读失败");
        }
    }

    @Override
    public List<ExportRemindConfigXlsDto> exportRemindConfigXls(String type, String sourceName) {
        List<ExportRemindConfigXlsDto> dtos = remindUserConfigMapper.exportRemindConfigXls(type, sourceName);
        if (CollectionUtils.isNotEmpty(dtos)) {
            // 查找PC端提醒人
//            List<ExportRemindConfigXlsDto> dtosPC = remindUserConfigMapper.getPcUserNames();
            // 查找APP端提醒人
            List<ExportRemindConfigXlsDto> dtosApp = remindUserConfigMapper.getAppUserNames();
            for (ExportRemindConfigXlsDto dto : dtos) {
                if (StringUtils.isNotEmpty(dto.getId())) {
                    if (ObjectUtils.isNotEmpty(dto.getPcUserIds())) {
                        dto.setPcUserNames(remoteUserService.getPcUserNamesByPcUserIds(dto.getPcUserIds().split(",")).getData());
                    }
//                    for (ExportRemindConfigXlsDto pc : dtosPC) {
//                        if (dto.getId().equals(pc.getId())) {
//                            dto.setPcUserNames(pc.getPcUserNames());
//                        }
//                    }
                    for (ExportRemindConfigXlsDto app : dtosApp) {
                        if (dto.getId().equals(app.getId())) {
                            dto.setAppUserNames(app.getAppUserNames());
                        }
                    }
                }
                //更新人匹配
                if (StringUtils.isNotEmpty(dto.getUpdater())) {
                    BaseResult<String> nickName = remoteUserService.getNickNameByName(dto.getUpdater());
                    dto.setUpdater(nickName.getData());
                }
            }
        }
        return dtos;
    }

    @Override
    public List<GetDingDingConfigPageListDto> getDingDingConfigByDepartIdPageList(String name, String phone, String departId, String position) {
        List<GetDingDingConfigPageListDto> dtoList = dingdingUserMenuMapper.getDingDingConfigByDepartIdPageList(name, phone, departId, position);
        if (CollectionUtils.isNotEmpty(dtoList)) {
            // 查询角色
            for (GetDingDingConfigPageListDto dto : dtoList) {
                if (StringUtils.isNotBlank(dto.getRoles())) {
                    List<String> roles = Arrays.asList(dto.getRoles().split(","));
//                    User user = dingMailRoleMapper.getRoleNameByRoleIds(roles);
//                    if (null != user) {
//                        dto.setRoleName(user.getRoles());
//                    }
                    // 如果有多个部门的,查询多个部门(用逗号拼接)
                    if (StringUtils.isNotBlank(dto.getDepartmentIds()) && dto.getDepartmentIds().contains(",")) {
                        List<String> departs = Arrays.asList(dto.getDepartmentIds().split(","));
//                        String departName = dingMailDepartmentMapper.getDepartNamesByDeparts(departs);
                        String departName = "";
                        dto.setDepart(departName);
                    }
                }
            }
        }
        return dtoList;
    }

    @Override
    public List<DingConExcel> exportDingConfig(String name,String phone,String position,String departId) {
        List<DingConExcel> excel = dingmailUserMenuMapper.exportDingConfig(name,phone,position,departId);
        excel.forEach(exl -> {
            List<String> sourceName = dingmailUserMenuMapper.getSourceByUserId(exl.getUserId());
            if (CollectionUtils.isNotEmpty(sourceName)) {
                StringBuilder permission = new StringBuilder();
                sourceName.forEach(s -> permission.append(s).append("、"));
                exl.setPermission(permission.substring(0, permission.length() - 1));
            }
        });

        return excel;
    }

    @Override
    public boolean getPermissionsByUserId(List<String> roleList, String indicatorId) {
        boolean result = false;
        List<String> permissions = dingmailUserMenuMapper.getPermissionsByUserId(roleList,indicatorId);
        for(String permission:permissions) {
            if(permission.equals("1")) {
                result = permission.equals("1");
                break;
            }
        }
        return result;
    }

    @Override
    public PermissionsVo getPermissionsByRoleList(String creator,List<String> roleList, String indicatorId) {
        PermissionsVo permissionsVo = new PermissionsVo();
        boolean supervisePermissions = false;
        boolean redPermissions = false;
        boolean hasSupervise = false;
        boolean hasRed = false;
        //督办权限
        List<String> superviseList = dingmailUserMenuMapper.getSupervisePermissions(roleList,indicatorId);
        for(String supervise : superviseList) {
            if(supervise.equals("1")) {
                supervisePermissions = supervise.equals("1");
                break;
            }
        }
        //是否有督办
        log.info("从督办服务查询是否发起过督办参数，creator=" + JSONObject.toJSONString(creator) + "---and indicatorId=" + JSONObject.toJSONString(indicatorId) );
        BaseResult result = superviseFeignService.queryByIndicator(creator,indicatorId);
        log.info("从督办服务查询是否发起过督办，返回结果=" + JSON.toJSONString(result));
        hasSupervise = (Boolean)result.getData();
        //红灯权限
        List<String> redList = dingmailUserMenuMapper.getRedPermissions(roleList,indicatorId);
        for(String red : redList) {
            if(red.equals("1")) {
                redPermissions = red.equals("1");
                break;
            }
        }
        //是否有红灯
        log.info("从督办服务查询是否发起过红灯参数，creator=" + JSONObject.toJSONString(creator) + "---and indicatorId=" + JSONObject.toJSONString(indicatorId) );
        BaseResult remindResult = superviseFeignService.queryRemindByIndicator(creator,indicatorId);
        log.info("从督办服务查询是否发起过红灯，返回结果=" + JSON.toJSONString(remindResult));
        hasRed = (Boolean)remindResult.getData();

        permissionsVo.setSupervisePermissions(supervisePermissions);
        permissionsVo.setRedPermissions(redPermissions);
        permissionsVo.setHasSupervise(hasSupervise);
        permissionsVo.setHasRed(hasRed);
        return permissionsVo;
    }

    @Override
    public PermissionsVo hasInformPermissions() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (ObjectUtils.isEmpty(loginUser)){
            log.error("获取不到当前用户");
            return new PermissionsVo();
        }
        Long[] roleIds = loginUser.getSysUser().getRoleIds();
        List<String> roleIdStrings = new ArrayList<>();
        for(Long roleId :roleIds){
            roleIdStrings.add(roleId.toString());
        }
        SourceManage sourceManage = sourceManageMapper.getSourceInfoBySourceSimpleName(ypcbSourceSimpleName);
        if(ObjectUtils.isEmpty(sourceManage)){
            log.error("根据配置的阅批呈报编码查询不到阅批呈报板块");
            return new PermissionsVo();
        }
        List<RoleSource> roleSourceList = dingdingUserMenuMapper.getInformPermissions(roleIdStrings,sourceManage.getId());
        PermissionsVo result = new PermissionsVo();
        result.setCircleBatchPermissions(false);
        result.setInformAuditPermissions(false);
        for(RoleSource roleSource:roleSourceList){
            if (roleSource.getInformAuditPermissions()==1){
                result.setInformAuditPermissions(true);
            }
            if (roleSource.getCircleBatchPermissions()==1){
                result.setCircleBatchPermissions(true);
            }
        }
        return result;
    }

    @Override
    public int checkUserCircleApprovalPermission() {
        LoginUser sysUser = SecurityUtils.getLoginUser();
        if (ObjectUtils.isNotEmpty(sysUser)) {
            List<Long> roleIdList = Arrays.asList(sysUser.getSysUser().getRoleIds());
            List<Long> roleSourceIdList = dingmailUserMenuMapper.checkUserCircleApprovalPermission(roleIdList);
            if(roleSourceIdList.size()>0){
                return 1;
            }
        }
        return 0;
    }
}

















