package com.onecity.os.management.configmanage.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.constant.UtilConstants;
import com.onecity.os.common.core.utils.DateThis;
import com.onecity.os.common.core.utils.DateUtils;
import com.onecity.os.common.core.utils.bean.BeanUtils;
import com.onecity.os.management.configmanage.entity.RemindInfo;
import com.onecity.os.management.configmanage.entity.dto.RemindIndicatorInfoDto;
import com.onecity.os.management.configmanage.entity.dto.RemindUserConfigDto;
import com.onecity.os.management.configmanage.mapper.RemindInfoMapper;
import com.onecity.os.management.configmanage.mapper.RemindUserConfigMapper;
import com.onecity.os.management.configmanage.mapper.SourceManageMapper;
import com.onecity.os.management.configmanage.service.RemindInfoService;
import com.onecity.os.management.statisticanalysis.entity.IndicatorStatisticsAnalysis;
import com.onecity.os.management.statisticanalysis.entity.dto.GetStatisticInfoByIdDto;
import com.onecity.os.management.statisticanalysis.mapper.IndicatorStatisticsAnalysisMapper;
import com.onecity.os.management.statisticanalysis.service.IndicatorStatisticAnalysisService;
import com.onecity.os.management.zhibiao.feign.BaseResult;
import com.onecity.os.management.zhibiao.feign.MessageFeignService;
import com.onecity.os.management.zhibiao.mapper.GeneralIndicatorMapper;
import com.onecity.os.management.zhibiao.model.dto.RemindInfoDto;
import com.onecity.os.management.zhibiao.service.IndicatorService;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 填报提醒信息表 日,月,季度,半年,年
 *
 * <AUTHOR>
 * @date 2021/1/12 下午3:57
 */
@Slf4j
@Service
public class RemindInfoServiceImpl implements RemindInfoService {

    @Resource
    private RemindUserConfigMapper remindUserConfigMapper;

    @Resource
    private RemindInfoMapper remindInfoMapper;

    @Resource
    private MessageFeignService messageFeignService;

    @Autowired
    private IndicatorStatisticAnalysisService indicatorStatisticAnalysisService;

    @Resource
    private SourceManageMapper sourceManageMapper;

    @Resource
    private GeneralIndicatorMapper generalIndicatorMapper;

    @Resource
    private IndicatorService indicatorService;

    @Resource
    private IndicatorStatisticsAnalysisMapper indicatorStatisticsAnalysisMapper;

    @Override
    public void addRemindInfo() {
        try {
            Calendar cal = Calendar.getInstance();
            // 获取当前月(如: 1,2,3...)
            int y = cal.get(Calendar.MONTH) + 1;
//            DateThis dateThis = new DateThis();
            Date date = new Date();
            RemindInfo remindInfo = new RemindInfo();
            remindInfo.setIsRead((byte) 0);
            remindInfo.setRemindedCount((byte) 1);
            remindInfo.setRemindedType((byte) 0);
            remindInfo.setIsDelete((byte) 0);
            remindInfo.setCreateTime(date);
            remindInfo.setUpdateTime(date);
            // 查找数据填报提醒配置表数据,只查找开通的厅局
            List<RemindUserConfigDto> remindUserConfigList = remindUserConfigMapper.getRemindInfoList();
            if (CollectionUtils.isNotEmpty(remindUserConfigList)) {
                for (RemindUserConfigDto config : remindUserConfigList) {
                    remindInfo.setPcUserIds(config.getPcUserIds());
                    remindInfo.setAppUserIds(config.getAppUserIds());
                    remindInfo.setSourceId(config.getSourceId());
                    List<String> parentIndicatorIdList = new ArrayList<>();
                    // 根据父id,查找所有符合条件的子集
                    if (CollectionUtils.isNotEmpty(parentIndicatorIdList)) {
                        // 所有指标和分组
                        List<RemindIndicatorInfoDto> indicatorList = new ArrayList<>();
                        getIndicatorsByParentIds(parentIndicatorIdList, indicatorList, "月度更新", null, null);
                        // 筛选指标名称
                        List<String> monthIndicatorList = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(indicatorList)) {
                            indicatorList.forEach(e -> monthIndicatorList.add(e.getIndicatorName()));
                        }
                        if (CollectionUtils.isNotEmpty(monthIndicatorList)) {
                            remindInfo.setRemindTitle("【月度数据更新提醒】" + config.getSourceName() + "，新一轮的填报周期已开始，请您于本月10日前完成月度数据更新！");
                            remindInfo.setRemindContent(config.getSourceName() + "，月度更新的指标有：" + monthIndicatorList + "，请核对校验。");
                            remindInfoMapper.insert(remindInfo);
                        }
                        // 季度更新
                        if (1 == y || 4 == y || 7 == y || 10 == y) {
                            List<RemindIndicatorInfoDto> indicatorList2 = new ArrayList<>();
                            getIndicatorsByParentIds(parentIndicatorIdList, indicatorList2, "季度更新", null, null);
                            // 筛选指标名称
                            List<String> seasonIndicatorList = new ArrayList<>();
                            if (CollectionUtils.isNotEmpty(indicatorList2)) {
                                indicatorList2.forEach(e -> seasonIndicatorList.add(e.getIndicatorName()));
                            }
                            if (CollectionUtils.isNotEmpty(seasonIndicatorList)) {
                                remindInfo.setRemindTitle("【季度数据更新提醒】" + config.getSourceName() + "，新一轮的填报周期已开始，请您于本月20日前完成季度数据更新！");
                                remindInfo.setRemindContent(config.getSourceName() + "，季度更新的指标有：" + seasonIndicatorList + "，请核对校验。");
                                remindInfoMapper.insert(remindInfo);
                            }
                        }
                        // 半年更新
                        if (1 == y || 7 == y) {
                            List<RemindIndicatorInfoDto> indicatorList3 = new ArrayList<>();
                            getIndicatorsByParentIds(parentIndicatorIdList, indicatorList3, "半年更新", null, null);
                            // 筛选指标名称
                            List<String> halfYearIndicatorList = new ArrayList<>();
                            if (CollectionUtils.isNotEmpty(indicatorList3)) {
                                indicatorList3.forEach(e -> halfYearIndicatorList.add(e.getIndicatorName()));
                            }
                            if (CollectionUtils.isNotEmpty(halfYearIndicatorList)) {
                                remindInfo.setRemindTitle("【半年度数据更新提醒】" + config.getSourceName() + "，新一轮的填报周期已开始，请您于本月25日前完成半年度数据更新！");
                                remindInfo.setRemindContent(config.getSourceName() + "，半年度更新的指标有：" + halfYearIndicatorList + "，请核对校验。");
                                remindInfoMapper.insert(remindInfo);
                            }
                        }
                        // 年度更新
                        if (1 == y) {
                            List<RemindIndicatorInfoDto> indicatorList4 = new ArrayList<>();
                            getIndicatorsByParentIds(parentIndicatorIdList, indicatorList4, "年度更新", null, null);
                            // 筛选指标名称
                            List<String> yearIndicatorList = new ArrayList<>();
                            if (CollectionUtils.isNotEmpty(indicatorList4)) {
                                indicatorList4.forEach(e -> yearIndicatorList.add(e.getIndicatorName()));
                            }
                            if (CollectionUtils.isNotEmpty(yearIndicatorList)) {
                                remindInfo.setRemindTitle("【年度数据更新提醒】" + config.getSourceName() + "，新一轮的填报周期已开始，请您于2月1日前完成年度数据更新！");
                                remindInfo.setRemindContent(config.getSourceName() + "，年度更新的指标有：" + yearIndicatorList + "，请核对校验。");
                                remindInfoMapper.insert(remindInfo);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("填报提醒:向remind_info插入首次提醒数据失败");
        }
    }

    @Override
    public void overDueRemind() {
        try {
            // 查找数据填报提醒配置表数据,只查找开通的厅局
            List<RemindUserConfigDto> remindUserConfigList = remindUserConfigMapper.getRemindInfoList();
            if (CollectionUtils.isNotEmpty(remindUserConfigList)) {
                Calendar cal = Calendar.getInstance();
                // 获取当前月(如: 1,2,3...) 当前日
                int y = cal.get(Calendar.MONTH) + 1;
                int z = cal.get(Calendar.DATE);
                Date date = new Date();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒");
                SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy年MM月dd日");
                String nowDateString = dateFormat.format(date);
                String afterThreeDateString = dateFormat2.format(System.currentTimeMillis() + 3 * 24 * 60 * 60 * 1000);
                DateThis dateThis = new DateThis();
                // 获取当前月初(结束时间)
                String thisMonth = dateThis.thisMonth();
                // 创建实体
                RemindInfo remindInfo = new RemindInfo();
                remindInfo.setIsRead((byte) 0);
                remindInfo.setIsDelete((byte) 0);
                remindInfo.setRemindedType((byte) 0);
                remindInfo.setCreateTime(date);
                remindInfo.setUpdateTime(date);
                for (RemindUserConfigDto config : remindUserConfigList) {
                    remindInfo.setPcUserIds(config.getPcUserIds());
                    remindInfo.setAppUserIds(config.getAppUserIds());
                    remindInfo.setSourceId(config.getSourceId());
                    // 根据厅局简称,查找所有父级指标id
                    List<String> parentIndicatorIdList = generalIndicatorMapper.getParentIndicatorIdListBySourceId(
                            config.getSourceSimpleName(), null, null);
                    if (CollectionUtils.isNotEmpty(parentIndicatorIdList)) {
                        // 根据父id,查找所有符合条件的子集
                        // 月度更新提醒
                        checkRemind("月度更新", config, parentIndicatorIdList, thisMonth, z, remindInfo, nowDateString,
                                afterThreeDateString, y, "月");
                        // 季度更新提醒
                        int y2 = y;
                        if (3 < y && y <= 6) {
                            y2 = y - 3;
                        }
                        if (6 < y && y <= 9) {
                            y2 = y - 6;
                        }
                        if (9 < y && y <= 12) {
                            y2 = y - 9;
                        }
                        checkRemind("季度更新", config, parentIndicatorIdList, thisMonth, z, remindInfo, nowDateString,
                                afterThreeDateString, y2, "季度");
                        // 半年更新提醒
                        int y3 = y;
                        if (6 < y) {
                            y3 = y - 6;
                        }
                        checkRemind("半年更新", config, parentIndicatorIdList, thisMonth, z, remindInfo, nowDateString,
                                afterThreeDateString, y3, "半年");
                        // 年度更新提醒
                        checkRemind("年度更新", config, parentIndicatorIdList, thisMonth, z, remindInfo, nowDateString,
                                afterThreeDateString, y, "年");
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("填报提醒:向remind_info插入逾期提醒数据失败");
        }
    }

    /**
     * 判断是否要逾期更新提醒
     *
     * @param updateCycle
     * @param config
     * @param parentIndicatorIdList
     * @param thisMonth
     * @param z
     * @param remindInfo
     * @param nowDateString
     * @param afterThreeDateString
     * @param y
     * @param date
     * @throws ParseException
     */
    private void checkRemind(String updateCycle, RemindUserConfigDto config, List<String> parentIndicatorIdList, String thisMonth, int z,
                             RemindInfo remindInfo, String nowDateString, String afterThreeDateString, int y, String date) throws ParseException {

        // 根据条件,查找判断要更新的指标
        List<RemindIndicatorInfoDto> indicatorList = new ArrayList<>();
        getIndicatorsByParentIds(parentIndicatorIdList, indicatorList, updateCycle, null, null);
        if (CollectionUtils.isNotEmpty(indicatorList)) {
            List<String> threeDayNotUpdate = new ArrayList<>();
            List<String> oneDayNotUpdate = new ArrayList<>();
            // 将约定的更新日期,与当前日期进行判断是否逾期提醒
            for (RemindIndicatorInfoDto indicator : indicatorList) {
                // 月度更新/季度更新/半年更新/年度更新
                if ("月度更新".equals(updateCycle)) {
                    if (StringUtils.isEmpty(indicator.getPlanUpdateDate())) {
                        indicator.setPlanUpdateDate("10");
                    }
                    // 第一次逾期提醒
                    if (3 == Integer.parseInt(indicator.getPlanUpdateDate()) - z) {
                        // 判断指标更新时间,如果不是本月进行的更新,将其记录
                        if (thisMonth.compareTo(indicator.getUpdateTime()) > 0) {
                            threeDayNotUpdate.add(indicator.getParentName() + "-" + indicator.getIndicatorName());
                        }
                    }
                    // 第二次逾期提醒
                    if (1 == z - Integer.parseInt(indicator.getPlanUpdateDate())) {
                        // 判断指标更新时间,如果不是本月进行的更新,将其记录
                        if (thisMonth.compareTo(indicator.getUpdateTime()) > 0) {
                            oneDayNotUpdate.add(indicator.getParentName() + "-" + indicator.getIndicatorName());
                        }
                    }
                } else {
                    if ("季度更新".equals(updateCycle)) {
                        if (StringUtils.isEmpty(indicator.getPlanUpdateDate())) {
                            indicator.setPlanUpdateDate("1-15");
                        }
                        if (!indicator.getPlanUpdateDate().contains("-")) {
                            indicator.setPlanUpdateDate("1-15");
                        }
                    }
                    if ("半年更新".equals(updateCycle)) {
                        if (StringUtils.isEmpty(indicator.getPlanUpdateDate())) {
                            indicator.setPlanUpdateDate("1-25");
                        }
                        if (!indicator.getPlanUpdateDate().contains("-")) {
                            indicator.setPlanUpdateDate("1-25");
                        }
                    }
                    if ("年度更新".equals(updateCycle)) {
                        if (StringUtils.isEmpty(indicator.getPlanUpdateDate())) {
                            indicator.setPlanUpdateDate("1-30");
                        }
                        if (!indicator.getPlanUpdateDate().contains("-")) {
                            indicator.setPlanUpdateDate("1-30");
                        }
                    }
                    // 拆分约定提醒日期
                    String month = indicator.getPlanUpdateDate().split("-")[0];
                    String day = indicator.getPlanUpdateDate().split("-")[1];
                    if (y == Integer.parseInt(month)) {
                        // 第一次逾期提醒
                        if (3 == Integer.parseInt(day) - z) {
                            // 判断指标更新时间,判断是否在对应周期更新过
                            if (!checkUpdate(indicator)) {
                                threeDayNotUpdate.add(indicator.getParentName() + "-" + indicator.getIndicatorName());
                            }
                        }
                        // 第二次逾期提醒
                        if (1 == z - Integer.parseInt(day)) {
                            // 判断指标更新时间,如果不是本月进行的更新,将其记录
                            if (!checkUpdate(indicator)) {
                                oneDayNotUpdate.add(indicator.getParentName() + "-" + indicator.getIndicatorName());
                            }
                        }
                    }
                }
            }
            // 到期前3天提醒
            if (CollectionUtils.isNotEmpty(threeDayNotUpdate)) {
                RemindInfo over3Remind = new RemindInfo();
                BeanUtils.copyProperties(remindInfo, over3Remind);
                over3Remind.setRemindedCount((byte) 2);
                //提醒内容：【指标更新提醒】XX单位，请您于本月10日前完成【A指标、B指标】更新。（指标应带入上层结构）
                over3Remind.setRemindTitle("【" + config.getSourceName() + "】 数据更新临期提醒");
                over3Remind.setContentRemark1("【指标更新提醒】" + config.getSourceName() + "，请您于本月" +
                        (z + 3) + "日前完成 ");
                StringBuilder stringBuilder = new StringBuilder();
                threeDayNotUpdate.forEach(three -> stringBuilder.append(three).append("、"));
                //去除最后一个、
                String content = stringBuilder.substring(0, stringBuilder.length() - 1);
                over3Remind.setRemindContent(content);
                over3Remind.setContentRemark2("更新。");
                //逾期3天提醒
                over3Remind.setType(UtilConstants.RemindType.OVER3);
                //数据提醒时间
                over3Remind.setRemindDate(new Date());

                //html内容
                over3Remind.setHtmlContentRemark1("<div> 该版块下存在未更新数据的指标项，即将到期，请您于本月" + (z + 3)
                        +"日（约定更新时间）前尽快完成以下指标项的数据更新。 <div class='flex'>");
                StringBuffer htmlOver3Content = new StringBuffer();
                threeDayNotUpdate.forEach(three -> htmlOver3Content.append("<div>").append(three).append("</div>"));
                over3Remind.setHtmlContent(String.valueOf(htmlOver3Content));
                over3Remind.setHtmlContentRemark2("</div>");

                remindInfoMapper.insert(over3Remind);
            }
            // 到期过后1天提醒
            if (CollectionUtils.isNotEmpty(oneDayNotUpdate)) {
                //提醒内容：【指标逾期提醒】XX单位，【A指标、B指标】已逾期，请您尽快完成更新。（指标应带入上层结构）
                RemindInfo over1Remind = new RemindInfo();
                BeanUtils.copyProperties(remindInfo, over1Remind);
                over1Remind.setRemindedCount((byte) 3);
                over1Remind.setRemindTitle("【" + config.getSourceName() + "】 数据更新逾期提醒");
                over1Remind.setContentRemark1("【指标逾期提醒】" + config.getSourceName() + "，");
                StringBuilder stringBuilder = new StringBuilder();
                oneDayNotUpdate.forEach(one -> stringBuilder.append(one).append("、"));
                //去除最后一个、
                String content = stringBuilder.substring(0, stringBuilder.length() - 1);
                over1Remind.setRemindContent(content);
                over1Remind.setContentRemark2("已逾期，请您尽快完成更新。");
                //逾期1天提醒
                over1Remind.setType(UtilConstants.RemindType.OVER1);
                //数据提醒时间
                over1Remind.setRemindDate(new Date());

                //html内容
                over1Remind.setHtmlContentRemark1("<div> 该版块下存在未更新数据的指标项，已逾期，请您尽快完成以下指标项的数据更新。"
                        + " <div class='flex'>");
                StringBuffer htmlOver1Content = new StringBuffer();
                oneDayNotUpdate.forEach(one -> htmlOver1Content.append("<div>").append(one).append("</div>"));
                over1Remind.setHtmlContent(String.valueOf(htmlOver1Content));
                over1Remind.setHtmlContentRemark2("</div>");
                remindInfoMapper.insert(over1Remind);
            }
        }
    }

    /**
     * 统计分析记录
     *
     * @param source
     * @param parentIndicatorIdList
     * @param dateString
     * @param updateCycle
     * @param currentMonth
     * @param yearFormat
     * @param monthFormat
     * @param dayFormat
     * @param thisMonth
     * @param thisSeasonFirst
     * @param halfYearFirst
     * @param yearFirst
     * @param dateThis
     * @param startTime
     * @param endTime
     */
    @Transactional(rollbackFor = Exception.class)
    void analysisRemind(List<String> parentIndicatorIdList, String dateString, String updateCycle,
                        int currentMonth, RemindUserConfigDto source, SimpleDateFormat yearFormat, SimpleDateFormat monthFormat,
                        SimpleDateFormat dayFormat, String thisMonth, String thisSeasonFirst, String halfYearFirst,
                        String yearFirst, DateThis dateThis, String startTime, String endTime, int monFlag) throws Exception {
        if (CollectionUtils.isNotEmpty(parentIndicatorIdList)) {
            if ("月度更新".equals(updateCycle)) {
                endTime = null;
            }
            // 所有指标和分组
            List<RemindIndicatorInfoDto> indicatorList = new ArrayList<>();
            getIndicatorsByParentIds(parentIndicatorIdList, indicatorList, updateCycle, null, endTime);

            log.info("统计分析提醒--统计的所有指标有:" + JSONObject.toJSONString(indicatorList));
            if (CollectionUtils.isNotEmpty(indicatorList)) {
                // 按时更新,逾期更新,未更新
                List<String> onTimeUpdate = new ArrayList<>();
                List<String> overdueUpdate = new ArrayList<>();
                List<String> notUpdate = new ArrayList<>();
                if ("月度更新".equals(updateCycle)) {
                    monthAnalysisRemind(source, thisMonth, dayFormat, dateString, updateCycle, indicatorList,
                            onTimeUpdate, overdueUpdate, notUpdate, currentMonth, yearFormat, monFlag);
                }
                if ("季度更新".equals(updateCycle)) {
                    seasonAnalysisRemind(source, thisSeasonFirst, monthFormat, dayFormat, dateString, updateCycle,
                            indicatorList, onTimeUpdate, overdueUpdate, notUpdate, currentMonth, yearFormat, dateThis,
                            startTime, endTime, monFlag);
                }
                if ("半年更新".equals(updateCycle)) {
                    halfYearAnalysisRemind(source, halfYearFirst, monthFormat, dayFormat, dateString, updateCycle,
                            indicatorList, onTimeUpdate, overdueUpdate, notUpdate, currentMonth, yearFormat, dateThis,
                            startTime, endTime, monFlag);
                }
                if ("年度更新".equals(updateCycle)) {
                    yearAnalysisRemind(source, yearFirst, monthFormat, dayFormat, dateString, updateCycle,
                            indicatorList, onTimeUpdate, overdueUpdate, notUpdate, currentMonth, yearFormat, dateThis,
                            startTime, endTime, monFlag);
                }
            }
        }
    }

    /**
     * 根据父级指标id,更新周期,创建时间(起始)查找所有子集指标全部信息
     *
     * @param indicatorParentIds
     * @param indicators
     * @param updateCycle
     * @param startTime
     * @param endTime
     * @return
     */
    private List<RemindIndicatorInfoDto> getIndicatorsByParentIds(List<String> indicatorParentIds, List<RemindIndicatorInfoDto> indicators,
                                                                  String updateCycle, String startTime, String endTime) {
        if (!CollectionUtils.isEmpty(indicatorParentIds)) {
            List<RemindIndicatorInfoDto> indicatorInfos = generalIndicatorMapper.getIndicatorsByParentIdsAndupdateCycle(
                    indicatorParentIds, null, startTime, endTime);
            if (!CollectionUtils.isEmpty(indicatorInfos)) {
                indicators.addAll(indicatorInfos);
                List<String> ids = new ArrayList<>();
                for (RemindIndicatorInfoDto indicatorInfo : indicatorInfos) {
                    ids.add(indicatorInfo.getId());
                }
                // 循环查询
                getIndicatorsByParentIds(ids, indicators, updateCycle, startTime, endTime);
            }
        }
        if (CollectionUtils.isNotEmpty(indicators)) {
            for (int i = 0; i < indicators.size(); i++) {
                // 筛选指标
                if ("0".equals(indicators.get(i).getIndicatorType())) {
                    if (!updateCycle.equals(indicators.get(i).getUpdateCycle())) {
                        indicators.remove(indicators.get(i));
                        i--;
                    }
                } else {
                    indicators.remove(indicators.get(i));
                    i--;
                }
            }
        }
        return indicators;
    }

    /**
     * 年度统计分析
     *
     * @param source
     * @param yearFirst
     * @param monthFormat
     * @param dayFormat
     * @param dateString
     * @param updateCycle
     * @param indicatorList
     * @param onTimeUpdate
     * @param overdueUpdate
     * @param notUpdate
     * @param currentMonth
     * @param yearFormat
     * @param dateThis
     * @param startTime
     * @param endTime
     */
    @Transactional(rollbackFor = Exception.class)
    void yearAnalysisRemind(RemindUserConfigDto source, String yearFirst,
                            SimpleDateFormat monthFormat, SimpleDateFormat dayFormat, String dateString,
                            String updateCycle, List<RemindIndicatorInfoDto> indicatorList, List<String> onTimeUpdate,
                            List<String> overdueUpdate, List<String> notUpdate, int currentMonth,
                            SimpleDateFormat yearFormat, DateThis dateThis, String startTime, String endTime,
                            int monFlag) throws Exception {
        // 最后更新时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 最后更新时间
        Date lastUpdateTime = sdf.parse(startTime);
        List<Date> lastTime = new ArrayList<>();
        for (RemindIndicatorInfoDto indicator : indicatorList) {
            // 筛选更新时间
            lastTime.add(indicator.getUpdateTimeDate());
            // 用上年起始时间进行筛选,查询的年更新的指标
            if (indicator.getUpdateTime().compareTo(startTime) >= 0 && indicator.getUpdateTime().compareTo(endTime) <= 0) {
                // 获取最后的更新时间
//                if (indicator.getUpdateTimeDate().compareTo(lastUpdateTime) > 0) {
//                    lastUpdateTime = indicator.getUpdateTimeDate();
//                }
                if (StringUtils.isEmpty(indicator.getPlanUpdateDate())) {
                    indicator.setPlanUpdateDate("1-30");
                }
                if (!indicator.getPlanUpdateDate().contains("-")) {
                    indicator.setPlanUpdateDate("1-30");
                }
                Integer monthPlanUpdateDate = Integer.valueOf(indicator.getPlanUpdateDate().split("-")[0]);
                Integer dayPlanUpdateDate = Integer.valueOf(indicator.getPlanUpdateDate().split("-")[1]);
                // 筛选年更新了的指标(预期和非逾期的)
                Integer monthUpdate = Integer.valueOf(monthFormat.format(indicator.getUpdateTimeDate()));
                Integer dayUpdate = Integer.valueOf(dayFormat.format(indicator.getUpdateTimeDate()));
                // 判断是否逾期,逾期,实际更新的月,大于约定更新的月
                if (monthUpdate - monthPlanUpdateDate > 0) {
                    overdueUpdate.add(indicator.getIndicatorName());
                }
                // 逾期,实际更新的月,等于约定更新的月,实际更新的日大于约定的日
                if (monthUpdate - monthPlanUpdateDate == 0 && dayUpdate - dayPlanUpdateDate > 0) {
                    overdueUpdate.add(indicator.getIndicatorName());
                } else {
                    onTimeUpdate.add(indicator.getIndicatorName());
                }
            } else {
                // 未更新的
                notUpdate.add(indicator.getIndicatorName());
            }
        }
        if (CollectionUtils.isNotEmpty(lastTime)) {
            lastUpdateTime = CollectionUtil.max(lastTime);
        }
        // 插入数据
        insertAnalysisRemind(source, dateString, updateCycle, indicatorList, onTimeUpdate,
                overdueUpdate, notUpdate, currentMonth, yearFormat, lastUpdateTime, monFlag);
    }

    /**
     * 半年统计分析
     *
     * @param source
     * @param halfYearFirst
     * @param monthFormat
     * @param dayFormat
     * @param dateString
     * @param updateCycle
     * @param indicatorList
     * @param onTimeUpdate
     * @param overdueUpdate
     * @param notUpdate
     * @param currentMonth
     * @param yearFormat
     * @param dateThis
     * @param startTime
     * @param endTime
     */
    @Transactional(rollbackFor = Exception.class)
    void halfYearAnalysisRemind(RemindUserConfigDto source, String halfYearFirst,
                                SimpleDateFormat monthFormat, SimpleDateFormat dayFormat, String dateString,
                                String updateCycle, List<RemindIndicatorInfoDto> indicatorList,
                                List<String> onTimeUpdate, List<String> overdueUpdate, List<String> notUpdate,
                                int currentMonth, SimpleDateFormat yearFormat, DateThis dateThis, String startTime,
                                String endTime, int monFlag) throws Exception {
        // 最后更新时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date lastUpdateTime = sdf.parse(startTime);
        List<Date> lastTime = new ArrayList<>();
        for (RemindIndicatorInfoDto indicator : indicatorList) {
            // 筛选更新时间
            lastTime.add(indicator.getUpdateTimeDate());
            // 用半年起始时间进行筛选,查询的半年更新的指标
            if (indicator.getUpdateTime().compareTo(startTime) >= 0 && indicator.getUpdateTime().compareTo(endTime) <= 0) {
                // 获取最后的更新时间
//                if (indicator.getUpdateTimeDate().compareTo(lastUpdateTime) > 0) {
//                    lastUpdateTime = indicator.getUpdateTimeDate();
//                }
                if (StringUtils.isEmpty(indicator.getPlanUpdateDate())) {
                    indicator.setPlanUpdateDate("1-25");
                }
                if (!indicator.getPlanUpdateDate().contains("-")) {
                    indicator.setPlanUpdateDate("1-25");
                }
                Integer monthPlanUpdateDate = Integer.valueOf(indicator.getPlanUpdateDate().split("-")[0]);
                Integer dayPlanUpdateDate = Integer.valueOf(indicator.getPlanUpdateDate().split("-")[1]);
                // 筛选上个半年更新了的指标(预期和非逾期的)
                Integer monthUpdate = Integer.valueOf(monthFormat.format(indicator.getUpdateTimeDate()));
                Integer dayUpdate = Integer.valueOf(dayFormat.format(indicator.getUpdateTimeDate()));
                if (7 <= monthUpdate) {
                    monthUpdate = monthUpdate - 6;
                }
                // 判断是否逾期,逾期,实际更新的月,大于约定更新的月
                if (monthUpdate - monthPlanUpdateDate > 0) {
                    overdueUpdate.add(indicator.getIndicatorName());
                }
                // 逾期,实际更新的月,等于约定更新的月,再判断日
                if (monthUpdate - monthPlanUpdateDate == 0 && dayUpdate - dayPlanUpdateDate > 0) {
                    overdueUpdate.add(indicator.getIndicatorName());
                } else {
                    onTimeUpdate.add(indicator.getIndicatorName());
                }
            } else {
                // 未更新的
                notUpdate.add(indicator.getIndicatorName());
            }
        }
        if (CollectionUtils.isNotEmpty(lastTime)) {
            lastUpdateTime = CollectionUtil.max(lastTime);
        }
        // 插入数据
        insertAnalysisRemind(source, dateString, updateCycle, indicatorList, onTimeUpdate,
                overdueUpdate, notUpdate, currentMonth, yearFormat, lastUpdateTime, monFlag);
    }

    /**
     * 季度统计分析
     *
     * @param source
     * @param thisSeasonFirst
     * @param monthFormat
     * @param dayFormat
     * @param dateString
     * @param updateCycle
     * @param indicatorList
     * @param onTimeUpdate
     * @param overdueUpdate
     * @param notUpdate
     * @param currentMonth
     * @param yearFormat
     * @param dateThis
     * @param startTime
     * @param endTime
     */
    @Transactional(rollbackFor = Exception.class)
    void seasonAnalysisRemind(RemindUserConfigDto source, String thisSeasonFirst,
                              SimpleDateFormat monthFormat, SimpleDateFormat dayFormat, String dateString,
                              String updateCycle, List<RemindIndicatorInfoDto> indicatorList, List<String> onTimeUpdate,
                              List<String> overdueUpdate, List<String> notUpdate, int currentMonth,
                              SimpleDateFormat yearFormat, DateThis dateThis, String startTime, String endTime,
                              int monFlag) throws Exception {
        // 最后更新时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date lastUpdateTime = sdf.parse(startTime);
        List<Date> lastTime = new ArrayList<>();
        for (RemindIndicatorInfoDto indicator : indicatorList) {
            // 筛选更新时间
            lastTime.add(indicator.getUpdateTimeDate());
            // 用季度起始时间进行筛选,查询的季度更新的指标
            if (indicator.getUpdateTime().compareTo(startTime) >= 0 && indicator.getUpdateTime().compareTo(endTime) <= 0) {
//                if (indicator.getUpdateTimeDate().compareTo(lastUpdateTime) > 0) {
//                    lastUpdateTime = indicator.getUpdateTimeDate();
//                }
                if (StringUtils.isEmpty(indicator.getPlanUpdateDate())) {
                    indicator.setPlanUpdateDate("1-15");
                }
                if (!indicator.getPlanUpdateDate().contains("-")) {
                    indicator.setPlanUpdateDate("1-15");
                }
                Integer monthPlanUpdateDate = Integer.valueOf(indicator.getPlanUpdateDate().split("-")[0]);
                Integer dayPlanUpdateDate = Integer.valueOf(indicator.getPlanUpdateDate().split("-")[1]);
                // 筛选这个季度更新了的指标(预期和非逾期的)
                // 指标更新时间,比上个季度初的时间大,则表示已更新了
                Integer monthUpdate = Integer.valueOf(monthFormat.format(indicator.getUpdateTimeDate()));
                Integer dayUpdate = Integer.valueOf(dayFormat.format(indicator.getUpdateTimeDate()));
                if (4 <= monthUpdate && 6 >= monthUpdate) {
                    monthUpdate = monthUpdate - 3;
                }
                if (7 <= monthUpdate && 9 >= monthUpdate) {
                    monthUpdate = monthUpdate - 6;
                }
                if (10 <= monthUpdate) {
                    monthUpdate = monthUpdate - 9;
                }
                // 判断是否逾期,逾期,实际更新的月,大于约定更新的月
                if (monthUpdate - monthPlanUpdateDate > 0) {
                    overdueUpdate.add(indicator.getIndicatorName());
                }
                // 逾期,实际更新的月,等于约定更新的月,再判断日
                if (monthUpdate - monthPlanUpdateDate == 0 && dayUpdate - dayPlanUpdateDate > 0) {
                    overdueUpdate.add(indicator.getIndicatorName());
                } else {
                    onTimeUpdate.add(indicator.getIndicatorName());
                }
            } else {
                // 查询季度未更新的
                notUpdate.add(indicator.getIndicatorName());
            }
        }
        // 获取最后更新时间
        if (CollectionUtils.isNotEmpty(lastTime)) {
            lastUpdateTime = Collections.max(lastTime);
        }
        // 插入数据
        insertAnalysisRemind(source, dateString, updateCycle, indicatorList, onTimeUpdate,
                overdueUpdate, notUpdate, currentMonth, yearFormat, lastUpdateTime, monFlag);
    }

    /**
     * 月度统计分析
     *
     * @param source
     * @param thisMonth
     * @param dayFormat
     * @param dateString
     * @param updateCycle
     * @param indicatorList
     * @param onTimeUpdate
     * @param overdueUpdate
     * @param notUpdate
     * @param currentMonth
     * @param yearFormat
     */
    @Transactional(rollbackFor = Exception.class)
    void monthAnalysisRemind(RemindUserConfigDto source, String thisMonth,
                             SimpleDateFormat dayFormat, String dateString, String updateCycle,
                             List<RemindIndicatorInfoDto> indicatorList, List<String> onTimeUpdate,
                             List<String> overdueUpdate, List<String> notUpdate, int currentMonth,
                             SimpleDateFormat yearFormat, int monFlag) throws Exception {
        // 最后更新时间
        Date lastUpdateTime = indicatorList.get(0).getUpdateTimeDate();
        for (RemindIndicatorInfoDto indicator : indicatorList) {
            // 获取最后的更新时间
            if (indicator.getUpdateTimeDate().compareTo(lastUpdateTime) > 0) {
                lastUpdateTime = indicator.getUpdateTimeDate();
            }
            if (StringUtils.isEmpty(indicator.getPlanUpdateDate())) {
                indicator.setPlanUpdateDate("10");
            }
            // 筛选这个月更新了的指标(逾期和非逾期的)
            if (indicator.getUpdateTime().compareTo(thisMonth) > 0) {
                // 判断是否逾期
                if (Integer.parseInt(dayFormat.format(indicator.getUpdateTimeDate()))
                        - Integer.parseInt(indicator.getPlanUpdateDate()) > 0) {
                    // 逾期更新
                    overdueUpdate.add(indicator.getIndicatorName());
                } else {
                    // 及时更新
                    onTimeUpdate.add(indicator.getIndicatorName());
                }
            } else {
                // 未更新的
                notUpdate.add(indicator.getIndicatorName());
            }
        }
        // 插入数据
        insertAnalysisRemind(source, dateString, updateCycle, indicatorList, onTimeUpdate,
                overdueUpdate, notUpdate, currentMonth, yearFormat, lastUpdateTime, monFlag);
    }

    /**
     * 向提醒数据表中插入统计分析数据
     *
     * @param source
     * @param dateString
     * @param updateCycle
     * @param indicatorList
     * @param onTimeUpdate
     * @param overdueUpdate
     * @param notUpdate
     * @param currentMonth
     * @param yearFormat
     * @param lastUpdateTime
     * @param monFlag        月初，月末标记， 月初 = 0 月末 =1；
     */
    @Transactional(rollbackFor = Exception.class)
    void insertAnalysisRemind(RemindUserConfigDto source, String dateString,
                              String updateCycle, List<RemindIndicatorInfoDto> indicatorList, List<String> onTimeUpdate,
                              List<String> overdueUpdate, List<String> notUpdate, int currentMonth,
                              SimpleDateFormat yearFormat, Date lastUpdateTime, int monFlag) throws Exception {
        // 1.月初操作，把indicator_statistics_analysis表中上月数据归纳维历史数据
        // 2.向indicator_statistics_analysis表,插入本月，指标更新情况统计数据
        // 3.月末无需进行indicator_statistics_analysis表操作
        if (0 == monFlag) {
            insertIndicatorStatisticsAnalysis(source, indicatorList, overdueUpdate, notUpdate, lastUpdateTime, updateCycle, currentMonth);
        }
    }

    /**
     * 向indicator_statistics_analysis表,插入指标更新情况统计数据
     *
     * @param source
     * @param indicatorList
     * @param overdueUpdate
     * @param notUpdate
     * @param lastUpdateTime
     * @param updateCycle
     * @param currentMonth
     */
    @Transactional(rollbackFor = Exception.class)
    void insertIndicatorStatisticsAnalysis(RemindUserConfigDto source, List<RemindIndicatorInfoDto> indicatorList,
                                           List<String> overdueUpdate, List<String> notUpdate, Date lastUpdateTime,
                                           String updateCycle, int currentMonth) throws Exception {
        // 必须是有审核后的厅局,才再统计分析中显示
        // 根据厅局模块id,查找最后审核时间
//        Date lastAuditTime = indicatorAuditNotesMapper.getAuditTimeBySourceId(source.getSourceId());
        Date lastAuditTime = new Date();
        if (ObjectUtils.isNotEmpty(lastAuditTime)) {
//            IndicatorStatisticsAnalysis analysis = new IndicatorStatisticsAnalysis();
            // 设置标题
            Date dateNow = new Date();
            String dateTime = "";
            SimpleDateFormat dateFormatYearMonth = new SimpleDateFormat("yyyy年MM月");
            SimpleDateFormat dateFormatYear = new SimpleDateFormat("yyyy年");
            SimpleDateFormat dateFormatMonth = new SimpleDateFormat("MM");
            SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");
            int totalCount = 0;
            if ("月度更新".equals(updateCycle)) {
                dateTime = dateFormatYearMonth.format(dateNow);
                totalCount = (int) indicatorList.stream().filter(ind -> "月度更新".equals(ind.getUpdateCycle())).count();
            }
            if ("季度更新".equals(updateCycle)) {
                if (4 == currentMonth) {
                    dateTime = dateFormatYear.format(dateNow) + "第一季度";
                }
                if (7 == currentMonth) {
                    dateTime = dateFormatYear.format(dateNow) + "第二季度";
                }
                if (10 == currentMonth) {
                    dateTime = dateFormatYear.format(dateNow) + "第三季度";
                }
                if (1 == currentMonth) {
                    dateTime = Integer.parseInt(yearFormat.format(dateNow)) - 1 + "年第四季度";
                }
                totalCount = (int) indicatorList.stream().filter(ind -> "季度更新".equals(ind.getUpdateCycle())).count();
            }
            if ("半年更新".equals(updateCycle)) {
                String month = dateFormatMonth.format(dateNow);
                if (1 <= currentMonth && currentMonth <= 6) {
                    dateTime = Integer.parseInt(yearFormat.format(dateNow)) - 1 + "年下半年";
                }
                if (7 <= currentMonth && currentMonth <= 12) {
                    dateTime = dateFormatYear.format(dateNow) + "下半年";
                }
                totalCount = (int) indicatorList.stream().filter(ind -> "半年更新".equals(ind.getUpdateCycle())).count();
            }
            if ("年度更新".equals(updateCycle)) {
                dateTime = Integer.parseInt(yearFormat.format(dateNow)) - 1 + "年度";
                totalCount = (int) indicatorList.stream().filter(ind -> "年度更新".equals(ind.getUpdateCycle())).count();
            }
//            analysis.setTitle(source.getSourceName() + dateTime + "数据填报统计分析详情");
//            analysis.setSourceId(source.getSourceId().intValue());
//            // 生成数据全为非历史数据，历史数据通过修改上月数据生成
//            analysis.setIsHistory((byte) 0);
//            // 数据时间, 202207，该字段表示当前条数据的统计时间
//            analysis.setDataDate(DateUtils.dateTimeNow("YYYYMM"));
//            // 总计
//            analysis.setTotalCount(totalCount);
//            // 未更新
//            if (indicatorList.size() == notUpdate.size()) {
//                analysis.setIsUpdate((byte) 0);
//            }
//            // 已更新
//            if (0 == notUpdate.size()) {
//                analysis.setIsUpdate((byte) 1);
//            }
//            // 部分更新
//            if (indicatorList.size() != notUpdate.size() && notUpdate.size() > 0) {
//                analysis.setIsUpdate((byte) 2);
//            }
//            // 是否逾期
//            if (overdueUpdate.size() == 0 && notUpdate.size() == 0) {
//                analysis.setIsOverDue((byte) 0);
//            } else {
//                analysis.setIsOverDue((byte) 1);
//            }
//            analysis.setShouldUpdate(indicatorList.size() + "条");
//            analysis.setActualUpdate((totalCount - notUpdate.size()) + "条");
//            analysis.setOverDueUpdate(overdueUpdate.size() + "条");
//            analysis.setNotUpdate(notUpdate.size() + "条");
//            // 缺更率(未更新指标/应更新指标)
//            if (0 != indicatorList.size()) {
//                double k = ((double) notUpdate.size() / indicatorList.size()) * 100;
//                BigDecimal big = new BigDecimal(k);
//                String l = big.setScale(0, BigDecimal.ROUND_HALF_UP).intValue() + "%";
//                analysis.setMissUpdateRatio(l);
//            } else {
//                analysis.setMissUpdateRatio("0%");
//            }
//            // 逾更率(逾期更新指标/已更新指标)
//            if (0 != (indicatorList.size() - notUpdate.size())) {
//                double k = ((double) overdueUpdate.size() / (indicatorList.size() - notUpdate.size())) * 100;
//                BigDecimal big = new BigDecimal(k);
//                String l = big.setScale(0, BigDecimal.ROUND_HALF_UP).intValue() + "%";
//                analysis.setOverDueUpdateRatio(l);
//            } else {
//                analysis.setOverDueUpdateRatio("0%");
//            }
//            analysis.setLastUpdateTime(lastUpdateTime);
//
//            if (ObjectUtils.isNotEmpty(lastAuditTime)) {
//                analysis.setLastAuditTime(lastAuditTime);
//            }
//            analysis.setIsDelete((byte) 0);
//            analysis.setCreateTime(new Date());
//            analysis.setUpdateTime(new Date());
//            analysis.setUpdateCycle(updateCycle);
            try {
//                更新上月数据为历史数据
//                List<IndicatorStatisticsAnalysis> historyDate = indicatorStatisticsAnalysisMapper
//                        .getAnalysisBySource(source.getSourceId(), analysis.getUpdateCycle(), monthCal(analysis.getDataDate()), "0");
//                historyDate.forEach(history -> {
//                    history.setIsHistory((byte) 1);
//                    indicatorStatisticsAnalysisMapper.updateByPrimaryKey(history);
//                });
//                //生成本月数据
//                indicatorStatisticsAnalysisMapper.insert(analysis);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("向indicator_statistics_analysis表,插入指标更新情况统计数据失败");
                throw new Exception("保存失败");
            }
        }
    }

    @Override
    public void CHBNStatisticalAnalysisCal(int monFlag) {
        Calendar cal = Calendar.getInstance();
        // 获取当前天(如: 1,2,3...)
        int currentDay = cal.get(Calendar.DATE);
        // 获取当前月(如: 1,2,3...)
        int currentMonth = cal.get(Calendar.MONTH) + 1;
        // 获取当前月第一天,最后一天(如: 1,2,3...)
        int firstDayOfMonth = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        int finalDayOfMonth = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        // 格式化日期到天
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒");
        String dateString = dateFormat.format(new Date());
        DateThis dateThis = new DateThis();
        // 获取当前月月初,月底
        String thisMonth = dateThis.thisMonth();
        String thisMonthEnd = dateThis.thisMonthEnd();
        // 获取这个季度初
        String thisSeasonFirst = dateThis.thisSeason();
        // 获取半年初
        String halfYearFirst = dateThis.halfYearFirst();
        // 获取年初
        String yearFirst = dateThis.thisYear();
        // 设置季度统计开始和结束时的查询时间
        String startSeasonTime = thisSeasonFirst;
        String endSeasonTime = dateThis.thisSeasonEnd();
        // 设置半年统计开始和结束时的查询时间
        String startHalfYearTime = halfYearFirst;
        String endHalfYearTime = dateThis.halfYearEnd();
        // 设置年度统计开始和结束时的查询时间
        String startYearTime = yearFirst;
        String endYearTime = dateThis.thisYearEnd();
        // 获取格式化的日期
        SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");
        SimpleDateFormat monthFormat = new SimpleDateFormat("MM");
        SimpleDateFormat dayFormat = new SimpleDateFormat("dd");
        // 以下为测试完成后打开逻辑
        //monFlag 月初，月末标记， 月初 = 0 月末 =1；
        // 非月初和月末时，检查指标数据，如果没有当月数据，则检验为月初数据生成失败，重新触发一次生成
        log.info("CHBNStatisticalAnalysisCal monFlag : {}; currentDay :{}; firstDayOfMonth : {}; finalDayOfMonth : {}",
                monFlag, currentDay, firstDayOfMonth, finalDayOfMonth);
        if (currentDay == firstDayOfMonth) {
            log.info("CHBNStatisticalAnalysisCal monFlag 0 : {}", monFlag);
            monFlag = 0;
        } else if (currentDay == finalDayOfMonth) {
            log.info("CHBNStatisticalAnalysisCal monFlag 1 : {}", monFlag);
            monFlag = 1;
        } else {
            //判断月初数据生成失败，重新触发一次生成
            //查询当月统计数据
            List<IndicatorStatisticsAnalysis> analyses = indicatorStatisticsAnalysisMapper.getAnalysisHistory();
            //查找所有开通的厅局sourceId,和厅局简称
            List<RemindUserConfigDto> sourceManages = remindUserConfigMapper.getAllStartedTjSourceList();
            if (sourceManages.size() * 5 == analyses.size()) {
                monFlag = 0;
                log.info("CHBNStatisticalAnalysisCal check monFlag 0 : {}", monFlag);
            } else {
                log.info("CHBNStatisticalAnalysisCal 定时器非月初和月末时间,执行停止。");
                return;
            }
        }
        int finalMonFlag = monFlag;
        //月初逻辑，1.统计上月历史数据，生成新一份月事实数据，删除上月实时数据
        //月初逻辑，2.生成月初提醒数据
        // 查找所有开通的厅局sourceId,和厅局简称
        List<RemindUserConfigDto> sourceManages = remindUserConfigMapper.getAllStartedTjSourceList();
        for (RemindUserConfigDto source : sourceManages) {
            //统计分析 逻辑更换

//            // 根据厅局简称,查找所有父级指标id
//            List<String> parentIndicatorId = generalIndicatorMapper.getParentIndicatorIdListBySourceId(
//                    source.getSourceSimpleName(), null, null);
//            if (CollectionUtils.isNotEmpty(parentIndicatorId)) {
//                try {
//                    // 月度统计
//                    log.info("统计分析提醒--月度统计");
//                    //调用函数堆入参的写法，是前人"睿智"历史代码，后续如果有时间人天再做调整。
//                    analysisRemind(parentIndicatorId, dateString, "月度更新",
//                            currentMonth, source, yearFormat, monthFormat, dayFormat, thisMonth, thisSeasonFirst,
//                            halfYearFirst, yearFirst, dateThis, null, null, finalMonFlag);
//
//                    // 季度统计
//                    log.info("统计分析提醒--季度统计");
//                    analysisRemind(parentIndicatorId, dateString, "季度更新",
//                            currentMonth, source, yearFormat, monthFormat, dayFormat, thisMonth,
//                            thisSeasonFirst, halfYearFirst, yearFirst, dateThis, startSeasonTime,
//                            endSeasonTime, finalMonFlag);
//                    // 半年统计
//                    log.info("统计分析提醒--半年统计");
//                    analysisRemind(parentIndicatorId, dateString, "半年更新",
//                            currentMonth, source, yearFormat, monthFormat, dayFormat, thisMonth,
//                            thisSeasonFirst, halfYearFirst, yearFirst, dateThis, startHalfYearTime,
//                            endHalfYearTime, finalMonFlag);
//                    // 年度统计
//                    analysisRemind(parentIndicatorId, dateString, "年度更新",
//                            currentMonth, source, yearFormat, monthFormat, dayFormat, thisMonth,
//                            thisSeasonFirst, halfYearFirst, yearFirst, dateThis, startYearTime, endYearTime,
//                            finalMonFlag);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
            //调整为新逻辑

            // 获取当前登录的用户信息
            LoginUser sysUser = SecurityUtils.getLoginUser();
            String userName = null;
            if (ObjectUtil.isNotNull(sysUser)) {
                userName = sysUser.getUsername();
            } else {
                userName = "admin";
            }
            try {
                indicatorService.newStatisticAnalysis(Collections.singletonList(source.getSourceSimpleName()), null, userName);
            } catch (Exception e) {
                log.info("newStatisticAnalysis error");
                e.printStackTrace();
            }

            //月初月末统计分析提醒(调用统计分析页面接口，组装结果)
            //1.获取当前厅局提醒人
            RemindUserConfigDto sourceRemindUser = remindUserConfigMapper.getRemindInfoBySourceId(source.getSourceId());
            String sourceSimpleName = sourceManageMapper.getSourceSimpleNameBySourceId(source.getSourceId());
            List<String> indicatorParentIds = generalIndicatorMapper.getParentIndicatorIdsBySourceId(sourceSimpleName);
            // 所有指标和分组
            List<String> allIndicatorIds = indicatorStatisticAnalysisService.getIndicatorIdsByParentIds(indicatorParentIds, new ArrayList<>());
            List<GetStatisticInfoByIdDto> orgRemindDto = indicatorStatisticAnalysisService
                    .getStatisticInfoBySourceId(indicatorParentIds, allIndicatorIds, source.getSourceId());
            if (null != orgRemindDto && CollectionUtils.isNotEmpty(orgRemindDto) && null != sourceRemindUser) {
                List<GetStatisticInfoByIdDto> orgRemindInfos = orgRemindDto;
                // 创建实体类
                RemindInfo remindInfo = new RemindInfo();
                remindInfo.setIsRead((byte) 0);
                remindInfo.setRemindedCount((byte) 4);
                remindInfo.setRemindedType((byte) 0);
                remindInfo.setIsDelete((byte) 0);
                remindInfo.setCreateTime(new Date());
                remindInfo.setUpdateTime(new Date());
                //提醒类型
                remindInfo.setType(UtilConstants.RemindType.MON);
                //提醒时间
                remindInfo.setRemindDate(new Date());
                // 设置了提醒人的厅局,在消息提醒中心存储
                remindInfo.setPcUserIds(sourceRemindUser.getPcUserIds());
                remindInfo.setAppUserIds(sourceRemindUser.getAppUserIds());
                remindInfo.setSourceId(source.getSourceId());
                if (finalMonFlag == 0) {
                    //月初，【本月数据更新提醒】XX单位（板块名称），新一轮的填报周期已开始，请您于本月完成以下指标更新。本月应更新的指标有：
                    // 【A指标、B指标、C指标、D指标】请核对校验。（指标应带入上层结构）
                    // 标题
                    remindInfo.setRemindTitle("【" + source.getSourceName() + "】 指标数据应更提醒");
                    // 内容
                    StringBuilder content = new StringBuilder();
                    content.append(source.getSourceName())
                            .append("新一轮的填报周期已开始，请您于本月完成以下指标更新。本月应更新的指标有：【");
                    orgRemindInfos.forEach(orgRemindInfo -> content.append(orgRemindInfo.getIndicatorName()).append("、"));
                    //去除最后一个、
                    String strContent = content.substring(0, content.length() - 1) + "】请核对校验。";
                    remindInfo.setRemindContent(strContent);

                    //html内容
                    StringBuilder htmlContent = new StringBuilder();
                    htmlContent.append("<div>新一轮的填报周期已开始，请及时完成相关指标数据更新，本月该版块下应更新的指标如下。</div>")
                            .append("<div class='flex'>");
                    orgRemindInfos.forEach(orgRemindInfo -> htmlContent.append("<div>")
                            .append(orgRemindInfo.getIndicatorName()).append("</div>"));
                    htmlContent.append("</div>");
                    remindInfo.setHtmlContent(String.valueOf(htmlContent));

                } else {
                    //月末
                    //提醒内容：【本月更新情况统计】
                    //本月应更新指标数：5条
                    //实际更新指标数：3条
                    //其中逾期更新指标数：1条
                    //未更新指标数：2条
                    //未更新指标有：A指标、B指标
                    // 标题
                    remindInfo.setRemindTitle("【" + source.getSourceName() + "】 本月数据更新结果");
                    // 内容
                    StringBuilder content = new StringBuilder();
                    content.append(" 本月该版块应更新指标数：").append(orgRemindInfos.stream()
                                    .filter(info -> UtilConstants.StateFlag.TRUE.equals(info.getIsShouldUpdate())).count())
                            .append("条。\r\n");
                    content.append(" 实际更新指标数：").append(orgRemindInfos.stream()
                                    .filter(info -> UtilConstants.StateFlag.TRUE.equals(info.getIsUpdate())).count())
                            .append("条。\r\n");
                    content.append(" 其中逾期更新指标数：").append(orgRemindInfos.stream()
                                    .filter(info -> UtilConstants.StateFlag.TRUE.equals(info.getIsUpdate())
                                            && UtilConstants.StateFlag.TRUE.equals(info.getIsOverDue())).count())
                            .append("条。\r\n");
                    //未更新指标
                    List<GetStatisticInfoByIdDto> missUpdate = orgRemindInfos.stream()
                            .filter(info -> UtilConstants.StateFlag.FALSE.equals(info.getIsUpdate())).collect(Collectors.toList());
                    content.append(" 未更新指标数：").append(missUpdate.size()).append("条。\r\n");
                    if (CollectionUtils.isNotEmpty(missUpdate)) {
                        content.append(" 未更新指标有：");
                        missUpdate.forEach(orgRemindInfo -> content.append(orgRemindInfo.getIndicatorName()).append("、"));
                        //去除最后一个、
                        String strContent = content.substring(0, content.length() - 1) + "。\r\n";
                        remindInfo.setRemindContent(strContent);
                    } else {
                        remindInfo.setRemindContent(String.valueOf(content));
                    }

                    //html内容
                    StringBuilder htmlContent = new StringBuilder();
                    htmlContent
                            .append("<div>本月该版块应更新指标数：")
                            .append(orgRemindInfos.stream()
                                    .filter(info -> UtilConstants.StateFlag.TRUE.equals(info.getIsShouldUpdate())).count())
                            .append("条。</div>")
                            .append("<div>实际更新指标数：")
                            .append(orgRemindInfos.stream()
                                    .filter(info -> UtilConstants.StateFlag.TRUE.equals(info.getIsUpdate())).count())
                            .append("条。</div>")
                            .append("<div>其中逾期更新指标数：")
                            .append(orgRemindInfos.stream()
                                    .filter(info -> UtilConstants.StateFlag.TRUE.equals(info.getIsUpdate())
                                            && UtilConstants.StateFlag.TRUE.equals(info.getIsOverDue())).count())
                            .append("条。</div>")
                            .append("<div>未更新指标数：").append(missUpdate.size()).append("条。</div>")
                            .append("<div>未更新指标有：</div>")
                            .append("<div class='flex'>");
                    missUpdate.forEach(orgRemindInfo -> htmlContent.append("<div>")
                            .append(orgRemindInfo.getIndicatorName()).append("</div>"));
                    htmlContent.append("</div>");
                    remindInfo.setHtmlContent(String.valueOf(htmlContent));

                }
                remindInfoMapper.insert(remindInfo);
            }
        }

    }


    /**
     * 向前数月份，跨年，补0
     *
     * @param dateMonth
     * @return
     */
    private String monthCal(String dateMonth) {
        int month = Integer.parseInt(dateMonth.substring(dateMonth.length() - 2));
        int year = Integer.parseInt(dateMonth.substring(0, 4));
        if (1 == month) {
            dateMonth = String.valueOf(year - 1) + 12;
        } else {
            dateMonth = year + String.format("%02d", month - 1);
        }
        return dateMonth;
    }


    /**
     * 每日PC端信息推送
     */
    @Override
    public void sendRemindMsg() {
        List<RemindInfo> remindInfos = remindInfoMapper.getRemindForMsg();
        //月初，月末提醒
        remindInfos.stream().filter(remindInfo -> StringUtils.isNotEmpty(remindInfo.getPcUserIds())
                        && UtilConstants.RemindType.MON.equals(remindInfo.getType()))
                .forEach(remindInfo -> {
                    List<String> pcUserIds = Arrays.asList(remindInfo.getPcUserIds().split(","));
                    pcUserIds.forEach(pcUserId -> {
                        RemindInfoDto remindInfoDto = new RemindInfoDto();
                        remindInfoDto.setRemindTitle(remindInfo.getRemindTitle());
                        remindInfoDto.setIsRead((byte) 0);
                        remindInfoDto.setRemindedType((byte) 2);
                        remindInfoDto.setIsDelete((byte) 0);
                        remindInfoDto.setCreater(null);
                        remindInfoDto.setCreateTime(DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss"));
                        remindInfoDto.setRemindContent(remindInfo.getRemindContent());
                        remindInfoDto.setPcUserId(pcUserId);
                        remindInfoDto.setAppUserId(null);
                        remindInfoDto.setSourceType("PC");
                        remindInfoDto.setHtmlContent(remindInfo.getHtmlContent());
                        if (UtilConstants.RemindType.MON.equals(remindInfo.getType())) {
                            remindInfoDto.setLevel("3");
                        } else {
                            return;
                        }
                        log.info("月初，月末提醒逾期相关提醒日志："+JSONObject.toJSONString(remindInfoDto));
                        BaseResult result = messageFeignService.addMsg(Collections.singletonList(remindInfoDto));
                        log.info("sendRemindMsg cron addMsg result : {}", JSONObject.toJSONString(result));
                    });
                });

        //逾期3天前提醒 （根据 sourceId，分组合并提醒消息）
        Map<Long, List<RemindInfo>> over3InfoMap = remindInfos.stream().filter(remindInfo -> StringUtils.isNotEmpty(remindInfo.getPcUserIds())
                        && UtilConstants.RemindType.OVER3.equals(remindInfo.getType())).
                collect(Collectors.groupingBy(RemindInfo::getSourceId));
        over3InfoMap.forEach((key, value) -> {
            RemindInfo remindInfo = value.get(0);
            //统计内容
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(remindInfo.getContentRemark1()).append("【");
            value.forEach(info -> stringBuilder.append(info.getRemindContent()).append("、"));
            //去除最后一个、
            String content = stringBuilder.substring(0, stringBuilder.length() - 1);
            content = content + "】" + remindInfo.getContentRemark2();
            List<String> pcUserIds = Arrays.asList(remindInfo.getPcUserIds().split(","));
            String finalContent = content;

            //HTML内容合并拼接
            StringBuilder htmlContent = new StringBuilder();
            htmlContent.append(remindInfo.getHtmlContentRemark1());
            value.forEach(info -> htmlContent.append(info.getHtmlContent()));
            htmlContent.append(remindInfo.getHtmlContentRemark2());

            pcUserIds.forEach(pcUserId -> {
                RemindInfoDto remindInfoDto = new RemindInfoDto();
                remindInfoDto.setRemindTitle(remindInfo.getRemindTitle());
                remindInfoDto.setIsRead((byte) 0);
                remindInfoDto.setRemindedType((byte) 2);
                remindInfoDto.setIsDelete((byte) 0);
                remindInfoDto.setCreater(null);
                remindInfoDto.setCreateTime(DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss"));
                remindInfoDto.setRemindContent(finalContent);
                remindInfoDto.setPcUserId(pcUserId);
                remindInfoDto.setAppUserId(null);
                remindInfoDto.setSourceType("PC");
                remindInfoDto.setLevel("2");
                remindInfoDto.setHtmlContent(String.valueOf(htmlContent));
                log.info("逾期3天前提醒 （根据 sourceId，分组合并提醒消息）相关提醒日志："+remindInfoDto.toString());
                BaseResult result = messageFeignService.addMsg(Collections.singletonList(remindInfoDto));
                log.info("sendRemindMsg cron addMsg result : {}", JSONObject.toJSONString(result));
            });
        });

        //逾期1天提醒 （根据 sourceId，分组合并提醒消息）
        Map<Long, List<RemindInfo>> over1InfoMap = remindInfos.stream().filter(remindInfo -> StringUtils.isNotEmpty(remindInfo.getPcUserIds())
                        && UtilConstants.RemindType.OVER1.equals(remindInfo.getType())).
                collect(Collectors.groupingBy(RemindInfo::getSourceId));
        over1InfoMap.forEach((key, value) -> {
            RemindInfo remindInfo = value.get(0);
            //统计内容
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(remindInfo.getContentRemark1()).append("【");
            value.forEach(info -> stringBuilder.append(info.getRemindContent()).append("、"));
            //去除最后一个、
            String content = stringBuilder.substring(0, stringBuilder.length() - 1);
            content = content + "】" + remindInfo.getContentRemark2();
            List<String> pcUserIds = Arrays.asList(remindInfo.getPcUserIds().split(","));
            String finalContent = content;

            //HTML内容合并拼接
            StringBuilder htmlContent = new StringBuilder();
            htmlContent.append(remindInfo.getHtmlContentRemark1());
            value.forEach(info -> htmlContent.append(info.getHtmlContent()));
            htmlContent.append(remindInfo.getHtmlContentRemark2());

            pcUserIds.forEach(pcUserId -> {
                RemindInfoDto remindInfoDto = new RemindInfoDto();
                remindInfoDto.setRemindTitle(remindInfo.getRemindTitle());
                remindInfoDto.setIsRead((byte) 0);
                remindInfoDto.setRemindedType((byte) 2);
                remindInfoDto.setIsDelete((byte) 0);
                remindInfoDto.setCreater(null);
                remindInfoDto.setCreateTime(DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss"));
                remindInfoDto.setRemindContent(finalContent);
                remindInfoDto.setPcUserId(pcUserId);
                remindInfoDto.setAppUserId(null);
                remindInfoDto.setSourceType("PC");
                remindInfoDto.setLevel("1");
                remindInfoDto.setHtmlContent(String.valueOf(htmlContent));
                log.info("逾期1天提醒 （根据 sourceId，分组合并提醒消息）相关提醒日志："+remindInfoDto.toString());
                BaseResult result = messageFeignService.addMsg(Collections.singletonList(remindInfoDto));
                log.info("sendRemindMsg cron addMsg result : {}", JSONObject.toJSONString(result));
            });
        });
    }

    /**
     * 判断当前指标是否在对应周期内更新过,更新过返回true，未更新返回false
     *
     * @param indicator
     * @return
     */
    private boolean checkUpdate(RemindIndicatorInfoDto indicator) {
        if (StringUtils.isNotEmpty(indicator.getUpdateTime())) {
            Calendar cal = Calendar.getInstance();
            int mon = cal.get(Calendar.MONTH) + 1;
            try {
                switch (indicator.getUpdateCycle()) {
                    case "年度更新":
                        //年初
                        Date yDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                                .parse(new SimpleDateFormat("yyyy").format(new Date()) + "-01-01 00:00:00");
                        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(indicator.getUpdateTime()).after(yDate);
                    case "半年更新":
                        //当前半年初
                        if (mon >= 6) {
                            mon = 6;
                        } else {
                            mon = 1;
                        }
                        Date hyDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                                .parse(new SimpleDateFormat("yyyy").format(new Date()) + "-" + mon + "-01 00:00:00");
                        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(indicator.getUpdateTime()).after(hyDate);
                    case "季度更新":
                        //当前季度初
                        if (mon <= 3) {
                            mon = 1;
                        } else if (mon <= 6) {
                            mon = 3;
                        } else if (mon <= 9) {
                            mon = 6;
                        } else {
                            mon = 9;
                        }
                        Date sDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                                .parse(new SimpleDateFormat("yyyy").format(new Date()) + "-" + mon + "-01 00:00:00");
                        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(indicator.getUpdateTime()).after(sDate);
                    default:
                        return false;
                }
            } catch (Exception e) {
                log.error(" checkUpdate error : {}", e.getMessage());
                return false;
            }
        } else {
            return false;
        }
    }
}



















