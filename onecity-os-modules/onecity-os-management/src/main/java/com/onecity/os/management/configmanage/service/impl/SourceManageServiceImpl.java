package com.onecity.os.management.configmanage.service.impl;

import com.onecity.os.common.core.constant.CommonConstant;
import com.onecity.os.common.core.constant.Constant;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.enums.ResultInfoEnum;
import com.onecity.os.common.core.exception.CockpitBusinessException;
import com.onecity.os.common.core.exception.ServiceException;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.management.configmanage.entity.SourceManage;
import com.onecity.os.management.configmanage.entity.SysPermission;
import com.onecity.os.management.configmanage.entity.dto.GetMenuListDto;
import com.onecity.os.management.configmanage.entity.dto.SourceManageIndicatorGroup;
import com.onecity.os.management.configmanage.entity.dto.SourceManageUrl;
import com.onecity.os.management.configmanage.entity.vo.SourceManageReqVO;
import com.onecity.os.management.configmanage.entity.vo.SourceManageResVO;
import com.onecity.os.management.configmanage.entity.vo.SourceManageTypeVO;
import com.onecity.os.management.configmanage.enums.TypeEnums;
import com.onecity.os.management.configmanage.mapper.SourceManageMapper;
import com.onecity.os.management.configmanage.service.SourceManageService;
import com.onecity.os.management.configmanage.service.SysSeqConfService;
import com.onecity.os.management.zhibiaowarning.entity.vo.SourceManageVo;
import com.onecity.os.system.api.RemoteUserService;
import com.onecity.os.system.api.domain.SysDictData;
import com.onecity.os.system.api.domain.SysDictType;
import com.onecity.os.system.api.domain.SysMenu;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * (SourceManage)表服务实现类
 *
 * <AUTHOR>
 * @since 2020-05-21 10:11:56
 */
@Slf4j
@Service("sourceManageService")
public class SourceManageServiceImpl implements SourceManageService {
    @Resource
    private SourceManageMapper sourceManageMapper;
    @Resource
    private SysSeqConfService sysSeqConfService;

    @Value("${indicator.manage.permission.id}")
    private String indicatorManagePermissionId;

    @Value("${indicatorData.manage.permission.id}")
    private String indicatorDataManagePermissionId;

    @Value("${source.manage.permission.id}")
    private String sourceDataManagePermissionId;

    @Autowired
    private RemoteUserService remoteUserService;

    @Override
    public int countBySourceSimpleName(String sourceSimpleName) {
        return sourceManageMapper.countBySourceSimpleName(sourceSimpleName);
    }

    /**
     * 新增厅局
     *
     * @param sourceManageVO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertAndCreateTable(SourceManageReqVO sourceManageVO) {
        SourceManage sourceManage = new SourceManage();
        BeanUtils.copyProperties(sourceManageVO, sourceManage);
        String sourceId = sysSeqConfService.getStringSequence(Constant.SOURCE_ID);
        sourceManage.setSourceId(sourceId);
        Integer sequence = sourceManageVO.getSequence();
        if (sequence == null) {
            int count = sourceManageMapper.countAll();
            sequence = count + 1;
        }
        sourceManage.setSequence(sequence);
        LocalDateTime now = LocalDateTime.now();
        sourceManage.setCreateTime(now);
        sourceManage.setUpdateTime(now);
        String creater = sourceManageVO.getCreater();
        if (StringUtils.isBlank(creater)) {
            creater = sourceManageVO.getSourceName();
        }
        sourceManage.setCreater(creater);
        sourceManage.setUpdater(creater);
        sourceManage.setIsDelete(0);
        sourceManage.setIsStart(0);
        sourceManage.setFirstGroupFlag("1");
        sourceManage.setSecondGroupFlag("1");
        sourceManage.setAppReportInfoFlag("0");
        int insert = sourceManageMapper.insert(sourceManage);

        //创建厅局表
//        String sourceSimpleName = sourceManageVO.getSourceSimpleName();
//        SourceTableDTO sourceTableDTO = new SourceTableDTO();
//        sourceTableDTO.setGeneralIndicatorTableName(sourceSimpleName+Constant.GENERAL_INDICATOR_TALBE_SUFFIX);
//        sourceTableDTO.setGeneralIndicatorDataTableName(sourceSimpleName+Constant.GENERAL_INDICATOR_DATA_TALBE_SUFFIX);
//        sourceManageMapper.createIndicatorTable(sourceTableDTO);

        return insert;
    }

    @Override
    public int updateSourceManageById(SourceManageReqVO sourceManageVO) {
        SourceManage sourceManage = new SourceManage();
        BeanUtils.copyProperties(sourceManageVO, sourceManage);
        sourceManage.setUpdateTime(LocalDateTime.now());
        int i = sourceManageMapper.updateByPrimaryKeySelective(sourceManage);
        return i;
    }

    @Override
    public int stopSourceManageGroupById(String id,String type) throws Exception {
        //先查询分组下所有启用板块id
        List<String> idList = sourceManageMapper.getIdListByType(type,Constant.START_YES);
        if(null != idList && idList.size() > 0){
            //停用分组下板块
            String ids = String.join(",",idList);
            this.stopSourceManageById(ids);
        }
        //停用分组
        SourceManage sourceManage = new SourceManage();
        sourceManage.setId(Integer.valueOf(id));
        sourceManage.setIsStart(Constant.START_NO);
        sourceManageMapper.updateByPrimaryKeySelective(sourceManage);
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int stopSourceManageById(String id1) throws Exception {
        try {
            List<String> ids = Arrays.asList(id1.split(","));
            if (CollectionUtils.isNotEmpty(ids)) {
                for (String id : ids) {
                    //停用厅局将菜单逻辑删除
                    SourceManage sourceManage1 = sourceManageMapper.getSourceInfoById(id);
                    //String sourceName = sourceManage1.getSourceName();
                    String sourceSimpleName = sourceManage1.getSourceSimpleName();
                    String menuPath = Constant.SOURCE_URL + sourceSimpleName;
                    if(sourceManage1.getIsIndicators()==2){
                        menuPath = Constant.COMPREHENSIVE_ANALYSIS_URL + sourceSimpleName;
                    }
                    if(sourceManage1.getIsIndicators()==5){
                        menuPath = Constant.INVESTMENT_PROMOTION_URL + sourceSimpleName;
                    }
                    // 先跟据厅局名称查找现在存在的厅局菜单
                    //BaseResult<SysMenu> menuResult = remoteUserService.getInfoByNameAndParentId(sourceName,sourceDataManagePermissionId);
                    BaseResult<SysMenu> menuResult = remoteUserService.getInfoBySourceSimpleNameAndPath(sourceSimpleName,menuPath);
                    if (BaseResult.FAIL == menuResult.getCode())
                    {
                        throw new ServiceException(menuResult.getMsg());
                    }
                    SysMenu sysMenuExist = menuResult.getData();
                    if (null != sysMenuExist) {
                        // 将原来的厅局删除
//                        BaseResult resultParent = remoteUserService.updateStatusByParam(sourceName,Long.valueOf(delPartentId),"1");
                        //BaseResult resultParent = remoteUserService.updateStatusByParam(sourceName,Long.valueOf(sourceDataManagePermissionId),"1");
                        BaseResult resultParent = remoteUserService.updateStatusBySourceSimpleNameAndPath("1", sourceSimpleName, menuPath);
                        if (BaseResult.FAIL == resultParent.getCode())
                        {
                            throw new ServiceException(resultParent.getMsg());
                        }
                        if(sourceManage1.getIsIndicators()==1 || sourceManage1.getIsIndicators()==5 ){
                            //指标板块
                            // 根据父id,将子菜单删除
                            BaseResult resultChild = remoteUserService.updateStatusByParam(null,sysMenuExist.getMenuId(),"1");
                            if (BaseResult.FAIL == resultChild.getCode())
                            {
                                throw new ServiceException(resultChild.getMsg());
                            }
                        }
                    }
                    SourceManage sourceManage = new SourceManage();
                    sourceManage.setId(Integer.valueOf(id));
                    sourceManage.setIsStart(Constant.START_NO);
                    int i = sourceManageMapper.updateByPrimaryKeySelective(sourceManage);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("停用失败");
        }
        return 1;

    }

    @Override
    public int startSourceManageGroupById(String id, String type) throws Exception {
        //先查询分组下所有停用板块id
        List<String> idList = sourceManageMapper.getIdListByType(type,Constant.START_NO);
        if(null != idList && idList.size() > 0){
            //启用分组下板块
            String ids = String.join(",",idList);
            this.startSourceManageById(ids);
        }
        //启用分组
        SourceManage sourceManage = new SourceManage();
        sourceManage.setId(Integer.valueOf(id));
        sourceManage.setIsStart(Constant.START_YES);
        sourceManageMapper.updateByPrimaryKeySelective(sourceManage);
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int startSourceManageById(String id1) throws Exception {
        try {
            List<String> ids = Arrays.asList(id1.split(","));
            if (CollectionUtils.isNotEmpty(ids)) {
                for (String id : ids) {
                    //启用厅局创建菜单或恢复菜单
                    SourceManage sourceManage1 = sourceManageMapper.getSourceInfoById(id);
                    String sourceName = sourceManage1.getSourceName();
                    String sourceSimpleName = sourceManage1.getSourceSimpleName();
                    String menuPath = Constant.SOURCE_URL + sourceSimpleName;
                    if(sourceManage1.getIsIndicators()==2){
                        menuPath = Constant.COMPREHENSIVE_ANALYSIS_URL + sourceSimpleName;
                    }
                    if(sourceManage1.getIsIndicators()==5){
                        menuPath = Constant.INVESTMENT_PROMOTION_URL + sourceSimpleName;
                    }
                    //查找之前的记录
//                    BaseResult<SysMenu> menuResult = remoteUserService.getInfoByNameAndParentId(sourceName,sourceDataManagePermissionId);
                    //根据板块编码查找之前的记录
                    BaseResult<SysMenu> menuResult = remoteUserService.getInfoBySourceSimpleNameAndPath(sourceSimpleName,menuPath);
                    if (BaseResult.FAIL == menuResult.getCode())
                    {
                        throw new ServiceException(menuResult.getMsg());
                    }
                    SysMenu sysMenuExist = menuResult.getData();
                    if (ObjectUtils.isNotEmpty(sysMenuExist)) {
                        log.info("进入停用后启用逻辑");
                        if(sourceManage1.getIsIndicators()==1) {
                            //如果菜单名称与板块名称不一致时，将菜单名称修改为与板块名称一致
                            if (!sourceManage1.getSourceName().equals(sysMenuExist.getMenuName())) {
                                BaseResult updateMenuNameResult = remoteUserService.updateNameBySourceSimpleNameAndPath(sourceName, sourceSimpleName, menuPath);
                                if (BaseResult.FAIL == updateMenuNameResult.getCode()) {
                                    log.error("更新菜单名称失败" + updateMenuNameResult.getMsg());
                                }
                            }
                            // 将原来删除的进行恢复
                            BaseResult resultParent = remoteUserService.updateStatusByParam(sourceName, Long.valueOf(sourceDataManagePermissionId), "0");
                            if (BaseResult.FAIL == resultParent.getCode()) {
                                throw new ServiceException(resultParent.getMsg());
                            }
                            List<String> names = new ArrayList<>();
                            names.add("指标项管理");
                            names.add("指标数据管理");
                            names.add("信息简报");
                            names.add("指标数据审核");
                            names.add("提交审核记录");
                            names.add("指标更新周期管理");
                            names.add("指标数据列增删");
                            for (String name : names) {
                                BaseResult resultChild = remoteUserService.updateStatusByParam(name, sysMenuExist.getMenuId(), "0");
                                if (BaseResult.FAIL == resultChild.getCode()) {
                                    throw new ServiceException(resultChild.getMsg());
                                }
                                int res = (int) resultChild.getData();
                                if (0 == res) {
                                    if ("指标项管理".equals(name)) {
                                        SysMenu menu = new SysMenu();
                                        menu.setMenuType(CommonConstant.MENU_TYPE_C);
                                        menu.setParentId(sysMenuExist.getMenuId());
                                        menu.setMenuName(Constant.INDICATOR_MENU);
                                        menu.setPath(Constant.INDICATOR_URL + sourceSimpleName);
                                        menu.setComponent(Constant.INDICATOR_COMPONENT);
                                        //menu.setIcon(Constant.INDICATOR_MENU_ICON);
                                        menu.setIsCache(Constant.MENU_NO_CACHE);
                                        menu.setOrderNum("1");
                                        //远程调用新增菜单
                                        log.debug("远程调用新增菜单参数：{}", menu);
                                        BaseResult result = remoteUserService.addMenu(menu);
                                        if (BaseResult.FAIL == result.getCode()) {
                                            throw new ServiceException(result.getMsg());
                                        }
                                    }
                                    if ("指标数据管理".equals(name)) {
                                        //指标数据管理菜单
                                        SysMenu menu = new SysMenu();
                                        menu.setMenuType(CommonConstant.MENU_TYPE_C);
                                        menu.setParentId(sysMenuExist.getMenuId());
                                        menu.setMenuName(Constant.INDICATOR_DATA_MENU);
                                        menu.setPath(Constant.INDICATOR_DATA_URL + sourceSimpleName);
                                        menu.setComponent(Constant.INDICATOR_DATA_COMPONENT);
                                        //menu.setIcon(Constant.INDICATOR_MENU_ICON);
                                        menu.setIsCache(Constant.MENU_NO_CACHE);
                                        menu.setOrderNum("2");
                                        //远程调用新增菜单
                                        log.debug("远程调用新增菜单参数：{}", menu);
                                        BaseResult result = remoteUserService.addMenu(menu);
                                        if (BaseResult.FAIL == result.getCode()) {
                                            throw new ServiceException(result.getMsg());
                                        }
                                    }
                                    if ("信息简报".equals(name)) {
                                        SysMenu menu = new SysMenu();
                                        menu.setMenuType(CommonConstant.MENU_TYPE_C);
                                        menu.setParentId(sysMenuExist.getMenuId());
                                        menu.setMenuName(Constant.MESSAGE_INDICATOR_MENU);
                                        menu.setPath(Constant.MESSAGE_INDICATOR_URL + sourceSimpleName);
                                        menu.setComponent(Constant.MESSAGE_INDICATOR_COMPONENT);
                                        //menu.setIcon(Constant.INDICATOR_MENU_ICON);
                                        menu.setIsCache(Constant.MENU_NO_CACHE);
                                        menu.setOrderNum("6");
                                        //远程调用新增菜单
                                        log.debug("远程调用新增菜单参数：{}", menu);
                                        BaseResult result = remoteUserService.addMenu(menu);
                                        if (BaseResult.FAIL == result.getCode()) {
                                            throw new ServiceException(result.getMsg());
                                        }
                                    }
                                    if ("指标数据审核".equals(name)) {
                                        SysMenu menu = new SysMenu();
                                        menu.setMenuType(CommonConstant.MENU_TYPE_C);
                                        menu.setParentId(sysMenuExist.getMenuId());
                                        menu.setMenuName(Constant.INDICATOR_AUDIT_MENU);
                                        menu.setPath(Constant.INDICATOR_AUDIT_URL + sourceSimpleName);
                                        menu.setComponent(Constant.INDICATOR_AUDIT_COMPONENT);
                                        //menu.setIcon(Constant.INDICATOR_MENU_ICON);
                                        menu.setIsCache(Constant.MENU_NO_CACHE);
                                        menu.setOrderNum("3");
                                        //远程调用新增菜单
                                        log.debug("远程调用新增菜单参数：{}", menu);
                                        BaseResult result = remoteUserService.addMenu(menu);
                                        if (BaseResult.FAIL == result.getCode()) {
                                            throw new ServiceException(result.getMsg());
                                        }
                                    }
                                    if ("提交审核记录".equals(name)) {
                                        SysMenu menu = new SysMenu();
                                        menu.setMenuType(CommonConstant.MENU_TYPE_C);
                                        menu.setParentId(sysMenuExist.getMenuId());
                                        menu.setMenuName(Constant.INDICATOR_AUDIT_RECORD_MENU);
                                        menu.setPath(Constant.INDICATOR_AUDIT_RECORD_URL + sourceSimpleName);
                                        menu.setComponent(Constant.INDICATOR_AUDIT_RECORD_COMPONENT);
                                        //menu.setIcon(Constant.INDICATOR_MENU_ICON);
                                        menu.setIsCache(Constant.MENU_NO_CACHE);
                                        menu.setOrderNum("4");
                                        //远程调用新增菜单
                                        log.debug("远程调用新增菜单参数：{}", menu);
                                        BaseResult result = remoteUserService.addMenu(menu);
                                        if (BaseResult.FAIL == result.getCode()) {
                                            throw new ServiceException(result.getMsg());
                                        }
                                    }
                                    if ("指标更新周期管理".equals(name)) {
                                        SysMenu menu = new SysMenu();
                                        menu.setMenuType(CommonConstant.MENU_TYPE_C);
                                        menu.setParentId(sysMenuExist.getMenuId());
                                        menu.setMenuName(Constant.INDICATOR_UPDATE_CYCLE_MENU);
                                        menu.setPath(Constant.INDICATOR_UPDATE_CYCLE_URL + sourceSimpleName);
                                        menu.setComponent(Constant.INDICATOR_UPDATE_CYCLE_COMPONENT);
                                        //menu.setIcon(Constant.INDICATOR_MENU_ICON);
                                        menu.setIsCache(Constant.MENU_NO_CACHE);
                                        menu.setOrderNum("6");
                                        //远程调用新增菜单
                                        log.debug("远程调用新增菜单参数：{}", menu);
                                        BaseResult result = remoteUserService.addMenu(menu);
                                        if (BaseResult.FAIL == result.getCode()) {
                                            throw new ServiceException(result.getMsg());
                                        }
                                    }
                                    if ("指标数据列增删".equals(name)) {
                                        SysMenu menu = new SysMenu();
                                        menu.setMenuType(CommonConstant.MENU_TYPE_F);
                                        menu.setParentId(sysMenuExist.getMenuId());
                                        menu.setMenuName(Constant.INDICATOR_UPDATE_TITLE_BUTTON);
                                        menu.setIsCache(Constant.MENU_NO_CACHE);
                                        menu.setOrderNum("7");
                                        menu.setPerms(sourceSimpleName);
                                        //远程调用新增菜单
                                        log.debug("远程调用新增菜单参数：{}", menu);
                                        BaseResult result = remoteUserService.addMenu(menu);
                                        if (BaseResult.FAIL == result.getCode()) {
                                            throw new ServiceException(result.getMsg());
                                        }
                                    }
                                }
                            }
                        }
                        if(sourceManage1.getIsIndicators()==2){
                            log.info("进入停用后启用综合分析文章类板块逻辑");
                            //如果菜单名称与板块名称不一致时，将菜单名称修改为与板块名称一致
                            if (!sourceManage1.getSourceName().equals(sysMenuExist.getMenuName())) {
                                BaseResult updateMenuNameResult = remoteUserService.updateNameBySourceSimpleNameAndPath(sourceName, sourceSimpleName, menuPath);
                                if (BaseResult.FAIL == updateMenuNameResult.getCode()) {
                                    log.error("更新菜单名称失败" + updateMenuNameResult.getMsg());
                                }
                            }
                            // 将原来删除的进行恢复
                            BaseResult resultChild = remoteUserService.updateStatusByParam(sourceName, 0L, "0");
                            if (BaseResult.FAIL == resultChild.getCode()) {
                                throw new ServiceException(resultChild.getMsg());
                            }
                            int resultArticle = (int) resultChild.getData();
                            if (0==resultArticle) {
                                SysMenu menu = new SysMenu();
                                menu.setMenuType(CommonConstant.MENU_TYPE_M);
                                menu.setParentId(Long.valueOf(0));
                                menu.setMenuName(sourceName);
                                menu.setPath(Constant.COMPREHENSIVE_ANALYSIS_URL + sourceSimpleName);
                                menu.setComponent(Constant.COMPREHENSIVE_ANALYSIS_COMPONENT);
                                //menu.setIcon(Constant.INDICATOR_MENU_ICON);
                                menu.setIsCache(Constant.MENU_NO_CACHE);
                                //远程调用新增菜单
                                log.debug("远程调用新增菜单参数：{}", menu);
                                BaseResult result = remoteUserService.addMenu(menu);
                                if (BaseResult.FAIL == result.getCode()) {
                                    throw new ServiceException(result.getMsg());
                                }
                            }
                            //判断是否有字典类型
                            BaseResult<SysDictType> sysDictTypeResult1 = remoteUserService.getInfoByType(sourceSimpleName);
                            if(200==sysDictTypeResult1.getCode()&&ObjectUtils.isEmpty(sysDictTypeResult1.getData())){
                                //新增数据字典类型
                                SysDictType sysDictType = new SysDictType();
                                sysDictType.setDictName(sourceName);
                                sysDictType.setDictType(sourceSimpleName);
                                sysDictType.setStatus("0");
                                remoteUserService.addDictType(sysDictType);
                            }else if(200==sysDictTypeResult1.getCode()&&ObjectUtils.isNotEmpty(sysDictTypeResult1.getData())){
                                if("1".equals(sysDictTypeResult1.getData().getStatus())){
                                    remoteUserService.back(sysDictTypeResult1.getData().getDictId());
                                }
                            }
                            BaseResult<List<SysDictData>> sysDictTypeResult = remoteUserService.dictType(sourceSimpleName);
                            if(200==sysDictTypeResult.getCode()&&sysDictTypeResult.getData().size()==0) {
                                //新增字典类型数据
                                SysDictData sysDictData = new SysDictData();
                                sysDictData.setDictType(sourceSimpleName);
                                sysDictData.setDictValue("1");
                                sysDictData.setDictLabel("政务信息");
                                sysDictData.setDictSort(1L);
                                sysDictData.setStatus("0");
                                remoteUserService.addDictData(sysDictData);
                                SysDictData sysDictData1 = new SysDictData();
                                sysDictData1.setDictType(sourceSimpleName);
                                sysDictData1.setDictValue("2");
                                sysDictData1.setDictLabel("政务简报");
                                sysDictData1.setDictSort(2L);
                                sysDictData1.setStatus("0");
                                remoteUserService.addDictData(sysDictData1);
                            }
                        }
                        //招商引资类板块停用后启用
                        if(sourceManage1.getIsIndicators()==5) {
                            //如果菜单名称与板块名称不一致时，将菜单名称修改为与板块名称一致
                            if (!sourceManage1.getSourceName().equals(sysMenuExist.getMenuName())) {
                                BaseResult updateMenuNameResult = remoteUserService.updateNameBySourceSimpleNameAndPath(sourceName, sourceSimpleName, menuPath);
                                if (BaseResult.FAIL == updateMenuNameResult.getCode()) {
                                    log.error("更新菜单名称失败" + updateMenuNameResult.getMsg());
                                }
                            }
                            // 将原来删除的进行恢复
                            BaseResult resultParent = remoteUserService.updateStatusByParam(sourceName, 0L, "0");
                            if (BaseResult.FAIL == resultParent.getCode()) {
                                throw new ServiceException(resultParent.getMsg());
                            }
                            List<String> names = new ArrayList<>();
                            names.add("指标项管理");
                            names.add("指标数据管理");
                            names.add("信息简报");
                            names.add("指标数据审核");
                            names.add("提交审核记录");
                            names.add("指标更新周期管理");
                            names.add("项目管理");
                            names.add("指标数据列增删");
                            //隐藏路由的菜单
                            names.add("项目进展管理");
                            for (String name : names) {
                                BaseResult resultChild = remoteUserService.updateStatusByParam(name, sysMenuExist.getMenuId(), "0");
                                if (BaseResult.FAIL == resultChild.getCode()) {
                                    throw new ServiceException(resultChild.getMsg());
                                }
                                int res = (int) resultChild.getData();
                                if (0 == res) {
                                    if ("指标项管理".equals(name)) {
                                        SysMenu menu = new SysMenu();
                                        menu.setMenuType(CommonConstant.MENU_TYPE_C);
                                        menu.setParentId(sysMenuExist.getMenuId());
                                        menu.setMenuName(Constant.INDICATOR_MENU);
                                        menu.setPath("/"+sourceSimpleName+Constant.INDICATOR_URL + sourceSimpleName);
                                        menu.setComponent(Constant.INDICATOR_COMPONENT);
                                        //menu.setIcon(Constant.INDICATOR_MENU_ICON);
                                        menu.setIsCache(Constant.MENU_NO_CACHE);
                                        menu.setOrderNum("1");
                                        //远程调用新增菜单
                                        log.debug("远程调用新增菜单参数：{}", menu);
                                        BaseResult result = remoteUserService.addMenu(menu);
                                        if (BaseResult.FAIL == result.getCode()) {
                                            throw new ServiceException(result.getMsg());
                                        }
                                    }
                                    if ("指标数据管理".equals(name)) {
                                        //指标数据管理菜单
                                        SysMenu menu = new SysMenu();
                                        menu.setMenuType(CommonConstant.MENU_TYPE_C);
                                        menu.setParentId(sysMenuExist.getMenuId());
                                        menu.setMenuName(Constant.INDICATOR_DATA_MENU);
                                        menu.setPath("/"+sourceSimpleName+Constant.INDICATOR_DATA_URL + sourceSimpleName);
                                        menu.setComponent(Constant.INDICATOR_DATA_COMPONENT);
                                        //menu.setIcon(Constant.INDICATOR_MENU_ICON);
                                        menu.setIsCache(Constant.MENU_NO_CACHE);
                                        menu.setOrderNum("2");
                                        //远程调用新增菜单
                                        log.debug("远程调用新增菜单参数：{}", menu);
                                        BaseResult result = remoteUserService.addMenu(menu);
                                        if (BaseResult.FAIL == result.getCode()) {
                                            throw new ServiceException(result.getMsg());
                                        }
                                    }
                                    if ("信息简报".equals(name)) {
                                        SysMenu menu = new SysMenu();
                                        menu.setMenuType(CommonConstant.MENU_TYPE_C);
                                        menu.setParentId(sysMenuExist.getMenuId());
                                        menu.setMenuName(Constant.MESSAGE_INDICATOR_MENU);
                                        menu.setPath("/"+sourceSimpleName+Constant.MESSAGE_INDICATOR_URL + sourceSimpleName);
                                        menu.setComponent(Constant.MESSAGE_INDICATOR_COMPONENT);
                                        //menu.setIcon(Constant.INDICATOR_MENU_ICON);
                                        menu.setIsCache(Constant.MENU_NO_CACHE);
                                        menu.setOrderNum("6");
                                        //远程调用新增菜单
                                        log.debug("远程调用新增菜单参数：{}", menu);
                                        BaseResult result = remoteUserService.addMenu(menu);
                                        if (BaseResult.FAIL == result.getCode()) {
                                            throw new ServiceException(result.getMsg());
                                        }
                                    }
                                    if ("指标数据审核".equals(name)) {
                                        SysMenu menu = new SysMenu();
                                        menu.setMenuType(CommonConstant.MENU_TYPE_C);
                                        menu.setParentId(sysMenuExist.getMenuId());
                                        menu.setMenuName(Constant.INDICATOR_AUDIT_MENU);
                                        menu.setPath("/"+sourceSimpleName+Constant.INDICATOR_AUDIT_URL + sourceSimpleName);
                                        menu.setComponent(Constant.INDICATOR_AUDIT_COMPONENT);
                                        //menu.setIcon(Constant.INDICATOR_MENU_ICON);
                                        menu.setIsCache(Constant.MENU_NO_CACHE);
                                        menu.setOrderNum("3");
                                        //远程调用新增菜单
                                        log.debug("远程调用新增菜单参数：{}", menu);
                                        BaseResult result = remoteUserService.addMenu(menu);
                                        if (BaseResult.FAIL == result.getCode()) {
                                            throw new ServiceException(result.getMsg());
                                        }
                                    }
                                    if ("提交审核记录".equals(name)) {
                                        SysMenu menu = new SysMenu();
                                        menu.setMenuType(CommonConstant.MENU_TYPE_C);
                                        menu.setParentId(sysMenuExist.getMenuId());
                                        menu.setMenuName(Constant.INDICATOR_AUDIT_RECORD_MENU);
                                        menu.setPath("/"+sourceSimpleName+Constant.INDICATOR_AUDIT_RECORD_URL + sourceSimpleName);
                                        menu.setComponent(Constant.INDICATOR_AUDIT_RECORD_COMPONENT);
                                        //menu.setIcon(Constant.INDICATOR_MENU_ICON);
                                        menu.setIsCache(Constant.MENU_NO_CACHE);
                                        menu.setOrderNum("4");
                                        //远程调用新增菜单
                                        log.debug("远程调用新增菜单参数：{}", menu);
                                        BaseResult result = remoteUserService.addMenu(menu);
                                        if (BaseResult.FAIL == result.getCode()) {
                                            throw new ServiceException(result.getMsg());
                                        }
                                    }
                                    if ("指标更新周期管理".equals(name)) {
                                        SysMenu menu = new SysMenu();
                                        menu.setMenuType(CommonConstant.MENU_TYPE_C);
                                        menu.setParentId(sysMenuExist.getMenuId());
                                        menu.setMenuName(Constant.INDICATOR_UPDATE_CYCLE_MENU);
                                        menu.setPath("/"+sourceSimpleName+Constant.INDICATOR_UPDATE_CYCLE_URL + sourceSimpleName);
                                        menu.setComponent(Constant.INDICATOR_UPDATE_CYCLE_COMPONENT);
                                        //menu.setIcon(Constant.INDICATOR_MENU_ICON);
                                        menu.setIsCache(Constant.MENU_NO_CACHE);
                                        menu.setOrderNum("6");
                                        //远程调用新增菜单
                                        log.debug("远程调用新增菜单参数：{}", menu);
                                        BaseResult result = remoteUserService.addMenu(menu);
                                        if (BaseResult.FAIL == result.getCode()) {
                                            throw new ServiceException(result.getMsg());
                                        }
                                    }
                                    if ("指标数据列增删".equals(name)) {
                                        SysMenu menu = new SysMenu();
                                        menu.setMenuType(CommonConstant.MENU_TYPE_F);
                                        menu.setParentId(sysMenuExist.getMenuId());
                                        menu.setMenuName(Constant.INDICATOR_UPDATE_TITLE_BUTTON);
                                        menu.setIsCache(Constant.MENU_NO_CACHE);
                                        menu.setOrderNum("9");
                                        menu.setPerms(sourceSimpleName);
                                        //远程调用新增菜单
                                        log.debug("远程调用新增菜单参数：{}", menu);
                                        BaseResult result = remoteUserService.addMenu(menu);
                                        if (BaseResult.FAIL == result.getCode()) {
                                            throw new ServiceException(result.getMsg());
                                        }
                                    }
                                    if ("项目管理".equals(name)) {
                                        SysMenu menu = new SysMenu();
                                        menu.setMenuType(CommonConstant.MENU_TYPE_C);
                                        menu.setParentId(sysMenuExist.getMenuId());
                                        menu.setMenuName(Constant.PROJECT_MANAGEMENT);
                                        menu.setPath(Constant.PROJECT_MANAGEMENT_URL + sourceSimpleName);
                                        menu.setComponent(Constant.PROJECT_MANAGEMENT_COMPONENT);
                                        menu.setIsCache(Constant.MENU_NO_CACHE);
                                        menu.setOrderNum("7");
                                        menu.setPerms(sourceSimpleName);
                                        //远程调用新增菜单
                                        log.debug("远程调用新增菜单参数：{}", menu);
                                        BaseResult result = remoteUserService.addMenu(menu);
                                        if (BaseResult.FAIL == result.getCode()) {
                                            throw new ServiceException(result.getMsg());
                                        }
                                    }
                                    if ("项目进展管理".equals(name)) {
                                        SysMenu menu = new SysMenu();
                                        menu.setMenuType(CommonConstant.MENU_TYPE_C);
                                        menu.setParentId(sysMenuExist.getMenuId());
                                        menu.setMenuName(Constant.PROJECT_PROGRESS);
                                        menu.setPath(Constant.PROJECT_PROGRESS_URL + sourceSimpleName);
                                        menu.setComponent(Constant.PROJECT_PROGRESS_COMPONENT);
                                        menu.setIsCache(Constant.MENU_NO_CACHE);
                                        menu.setVisible("1");
                                        menu.setOrderNum("8");
                                        menu.setPerms(sourceSimpleName);
                                        //远程调用新增菜单
                                        log.debug("远程调用新增菜单参数：{}", menu);
                                        BaseResult result = remoteUserService.addMenu(menu);
                                        if (BaseResult.FAIL == result.getCode()) {
                                            throw new ServiceException(result.getMsg());
                                        }
                                    }

                                }

                            }
                            //判断是否有字典类型-招商引资项目类型
                            BaseResult<SysDictType> sysDictTypeResult1 = remoteUserService.getInfoByType(sourceSimpleName+Constant.PROJECT_TAGS);
                            if(200==sysDictTypeResult1.getCode()&&ObjectUtils.isEmpty(sysDictTypeResult1.getData())){
                                //新增数据字典类型
                                SysDictType sysDictType = new SysDictType();
                                sysDictType.setDictName(sourceName+"标签");
                                sysDictType.setDictType(sourceSimpleName+Constant.PROJECT_TAGS);
                                sysDictType.setStatus("0");
                                remoteUserService.addDictType(sysDictType);
                            }else if(200==sysDictTypeResult1.getCode()&&ObjectUtils.isNotEmpty(sysDictTypeResult1.getData())){
                                if("1".equals(sysDictTypeResult1.getData().getStatus())){
                                    remoteUserService.back(sysDictTypeResult1.getData().getDictId());
                                }
                            }
                            BaseResult<List<SysDictData>> sysDictTypeResult = remoteUserService.dictType(sourceSimpleName+Constant.PROJECT_TAGS);
                            if(200==sysDictTypeResult.getCode()&&sysDictTypeResult.getData().size()==0) {
                                //新增字典类型数据能源、化工、食品、医药、汽车、轻工
                                SysDictData sysDictData = new SysDictData();
                                sysDictData.setDictType(sourceSimpleName+Constant.PROJECT_TAGS);
                                sysDictData.setDictValue("1");
                                sysDictData.setDictLabel("能源");
                                sysDictData.setDictSort(1L);
                                sysDictData.setStatus("0");
                                remoteUserService.addDictData(sysDictData);
                                SysDictData sysDictData1 = new SysDictData();
                                sysDictData1.setDictType(sourceSimpleName+Constant.PROJECT_TAGS);
                                sysDictData1.setDictValue("2");
                                sysDictData1.setDictLabel("化工");
                                sysDictData1.setDictSort(2L);
                                sysDictData1.setStatus("0");
                                remoteUserService.addDictData(sysDictData1);
                                SysDictData sysDictData2 = new SysDictData();
                                sysDictData2.setDictType(sourceSimpleName+Constant.PROJECT_TAGS);
                                sysDictData2.setDictValue("3");
                                sysDictData2.setDictLabel("食品");
                                sysDictData2.setDictSort(3L);
                                sysDictData2.setStatus("0");
                                remoteUserService.addDictData(sysDictData2);
                                SysDictData sysDictData3 = new SysDictData();
                                sysDictData3.setDictType(sourceSimpleName+Constant.PROJECT_TAGS);
                                sysDictData3.setDictValue("4");
                                sysDictData3.setDictLabel("医药");
                                sysDictData3.setDictSort(4L);
                                sysDictData3.setStatus("0");
                                remoteUserService.addDictData(sysDictData3);
                                SysDictData sysDictData4 = new SysDictData();
                                sysDictData4.setDictType(sourceSimpleName+Constant.PROJECT_TAGS);
                                sysDictData4.setDictValue("5");
                                sysDictData4.setDictLabel("汽车");
                                sysDictData4.setDictSort(5L);
                                sysDictData4.setStatus("0");
                                remoteUserService.addDictData(sysDictData4);
                                SysDictData sysDictData5 = new SysDictData();
                                sysDictData5.setDictType(sourceSimpleName+Constant.PROJECT_TAGS);
                                sysDictData5.setDictValue("6");
                                sysDictData5.setDictLabel("轻工");
                                sysDictData5.setDictSort(6L);
                                sysDictData5.setStatus("0");
                                remoteUserService.addDictData(sysDictData5);
                            }
                            //判断是否有字典类型-招商引资地区字典
                            BaseResult<SysDictType> sysDictTypeResultSite = remoteUserService.getInfoByType(sourceSimpleName+Constant.PROJECT_SITE);
                            if(200==sysDictTypeResultSite.getCode()&&ObjectUtils.isEmpty(sysDictTypeResultSite.getData())){
                                //新增数据字典类型
                                SysDictType sysDictType = new SysDictType();
                                sysDictType.setDictName(sourceName+"地区");
                                sysDictType.setDictType(sourceSimpleName+Constant.PROJECT_SITE);
                                sysDictType.setStatus("0");
                                remoteUserService.addDictType(sysDictType);
                            }else if(200==sysDictTypeResultSite.getCode()&&ObjectUtils.isNotEmpty(sysDictTypeResultSite.getData())){
                                if("1".equals(sysDictTypeResultSite.getData().getStatus())){
                                    remoteUserService.back(sysDictTypeResultSite.getData().getDictId());
                                }
                            }
                            BaseResult<List<SysDictData>> sysDictTypeResultSiteData = remoteUserService.dictType(sourceSimpleName+Constant.PROJECT_SITE);
                            if(200==sysDictTypeResultSiteData.getCode()&&sysDictTypeResultSiteData.getData().size()==0) {
                                //新增字典类型数据区域1、区域2、区域3
                                SysDictData sysDictData = new SysDictData();
                                sysDictData.setDictType(sourceSimpleName+Constant.PROJECT_SITE);
                                sysDictData.setDictValue("1");
                                sysDictData.setDictLabel("区域1");
                                sysDictData.setDictSort(1L);
                                sysDictData.setStatus("0");
                                remoteUserService.addDictData(sysDictData);
                                SysDictData sysDictData1 = new SysDictData();
                                sysDictData1.setDictType(sourceSimpleName+Constant.PROJECT_SITE);
                                sysDictData1.setDictValue("2");
                                sysDictData1.setDictLabel("区域2");
                                sysDictData1.setDictSort(2L);
                                sysDictData1.setStatus("0");
                                remoteUserService.addDictData(sysDictData1);
                                SysDictData sysDictData2 = new SysDictData();
                                sysDictData2.setDictType(sourceSimpleName+Constant.PROJECT_SITE);
                                sysDictData2.setDictValue("3");
                                sysDictData2.setDictLabel("区域3");
                                sysDictData2.setDictSort(3L);
                                sysDictData2.setStatus("0");
                                remoteUserService.addDictData(sysDictData2);
                            }
                        }
                    } else {
                        log.info("进入首次启用版块逻辑");
                        // 自定义不创建菜单
//                        if (!"zdy".equals(sourceManage1.getType())
//                                || "经济运行".equals(sourceManage1.getSourceName())
//                                || "统计报告".equals(sourceManage1.getSourceName()))
                        //如果是指标板块则创建菜单
                        if(sourceManage1.getIsIndicators()==1){
                            //厅局菜单
                            SysMenu menu = new SysMenu();
                            menu.setMenuType(CommonConstant.MENU_TYPE_C);
                            menu.setParentId(Long.valueOf(sourceDataManagePermissionId));
                            menu.setMenuName(sourceName);
                            menu.setPath(Constant.SOURCE_URL + sourceSimpleName);
                            menu.setComponent(Constant.SOURCE_COMPONENT);
                            //menu.setIcon(Constant.INDICATOR_MENU_ICON);
                            menu.setIsCache(Constant.MENU_NO_CACHE);
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}",menu);
                            BaseResult result = remoteUserService.addMenu(menu);
                            if (BaseResult.FAIL == result.getCode())
                            {
                                throw new ServiceException(result.getMsg());
                            }
                            Long parentId = Long.parseLong(result.getData().toString());

                            //指标管理菜单
                            SysMenu indicatorMenu = new SysMenu();
                            indicatorMenu.setMenuType(CommonConstant.MENU_TYPE_C);
                            indicatorMenu.setParentId(parentId);
                            indicatorMenu.setMenuName(Constant.INDICATOR_MENU);
                            indicatorMenu.setPath(Constant.INDICATOR_URL + sourceSimpleName);
                            indicatorMenu.setComponent(Constant.INDICATOR_COMPONENT);
                            //indicatorMenu.setIcon(Constant.INDICATOR_MENU_ICON);
                            indicatorMenu.setIsCache(Constant.MENU_NO_CACHE);
                            indicatorMenu.setOrderNum("1");
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}",indicatorMenu);
                            BaseResult resultIndicator = remoteUserService.addMenu(indicatorMenu);
                            if (BaseResult.FAIL == resultIndicator.getCode())
                            {
                                throw new ServiceException(resultIndicator.getMsg());
                            }

                            //指标数据管理菜单
                            SysMenu indicatorDataMenu = new SysMenu();
                            indicatorDataMenu.setMenuType(CommonConstant.MENU_TYPE_C);
                            indicatorDataMenu.setParentId(parentId);
                            indicatorDataMenu.setMenuName(Constant.INDICATOR_DATA_MENU);
                            indicatorDataMenu.setPath(Constant.INDICATOR_DATA_URL + sourceSimpleName);
                            indicatorDataMenu.setComponent(Constant.INDICATOR_DATA_COMPONENT);
                            //indicatorDataMenu.setIcon(Constant.INDICATOR_MENU_ICON);
                            indicatorDataMenu.setIsCache(Constant.MENU_NO_CACHE);
                            indicatorDataMenu.setOrderNum("2");
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}",indicatorDataMenu);
                            BaseResult resultIndicatorData = remoteUserService.addMenu(indicatorDataMenu);
                            if (BaseResult.FAIL == resultIndicatorData.getCode())
                            {
                                throw new ServiceException(resultIndicatorData.getMsg());
                            }

                            //信息简报菜单
                            SysMenu messageMenu = new SysMenu();
                            messageMenu.setMenuType(CommonConstant.MENU_TYPE_C);
                            messageMenu.setParentId(parentId);
                            messageMenu.setMenuName(Constant.MESSAGE_INDICATOR_MENU);
                            messageMenu.setPath(Constant.MESSAGE_INDICATOR_URL + sourceSimpleName);
                            messageMenu.setComponent(Constant.MESSAGE_INDICATOR_COMPONENT);
                            //messageMenu.setIcon(Constant.INDICATOR_MENU_ICON);
                            messageMenu.setIsCache(Constant.MENU_NO_CACHE);
                            messageMenu.setOrderNum("6");
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}",messageMenu);
                            BaseResult resultMessage = remoteUserService.addMenu(messageMenu);
                            if (BaseResult.FAIL == resultMessage.getCode())
                            {
                                throw new ServiceException(resultMessage.getMsg());
                            }

                            //指标数据审核
                            SysMenu dataAuditMenu = new SysMenu();
                            dataAuditMenu.setMenuType(CommonConstant.MENU_TYPE_C);
                            dataAuditMenu.setParentId(parentId);
                            dataAuditMenu.setMenuName(Constant.INDICATOR_AUDIT_MENU);
                            dataAuditMenu.setPath(Constant.INDICATOR_AUDIT_URL + sourceSimpleName);
                            dataAuditMenu.setComponent(Constant.INDICATOR_AUDIT_COMPONENT);
                            //dataAuditMenu.setIcon(Constant.INDICATOR_MENU_ICON);
                            dataAuditMenu.setIsCache(Constant.MENU_NO_CACHE);
                            dataAuditMenu.setOrderNum("3");
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}",dataAuditMenu);
                            BaseResult resultDataAudit = remoteUserService.addMenu(dataAuditMenu);
                            if (BaseResult.FAIL == resultDataAudit.getCode())
                            {
                                throw new ServiceException(resultDataAudit.getMsg());
                            }

                            //提交审核记录
                            SysMenu auditRecordMenu = new SysMenu();
                            auditRecordMenu.setMenuType(CommonConstant.MENU_TYPE_C);
                            auditRecordMenu.setParentId(parentId);
                            auditRecordMenu.setMenuName(Constant.INDICATOR_AUDIT_RECORD_MENU);
                            auditRecordMenu.setPath(Constant.INDICATOR_AUDIT_RECORD_URL + sourceSimpleName);
                            auditRecordMenu.setComponent(Constant.INDICATOR_AUDIT_RECORD_COMPONENT);
                            //auditRecordMenu.setIcon(Constant.INDICATOR_MENU_ICON);
                            auditRecordMenu.setIsCache(Constant.MENU_NO_CACHE);
                            auditRecordMenu.setOrderNum("4");
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}",auditRecordMenu);
                            BaseResult resultAuditRecord = remoteUserService.addMenu(auditRecordMenu);
                            if (BaseResult.FAIL == resultAuditRecord.getCode())
                            {
                                throw new ServiceException(resultAuditRecord.getMsg());
                            }

                            // 指标更新周期管理
                            SysMenu indicatorCycleMenu = new SysMenu();
                            indicatorCycleMenu.setMenuType(CommonConstant.MENU_TYPE_C);
                            indicatorCycleMenu.setParentId(parentId);
                            indicatorCycleMenu.setMenuName(Constant.INDICATOR_UPDATE_CYCLE_MENU);
                            indicatorCycleMenu.setPath(Constant.INDICATOR_UPDATE_CYCLE_URL + sourceSimpleName);
                            indicatorCycleMenu.setComponent(Constant.INDICATOR_UPDATE_CYCLE_COMPONENT);
                            //indicatorCycleMenu.setIcon(Constant.INDICATOR_MENU_ICON);
                            indicatorCycleMenu.setIsCache(Constant.MENU_NO_CACHE);
                            indicatorCycleMenu.setOrderNum("5");
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}",indicatorCycleMenu);
                            BaseResult resultIndicatorCycle = remoteUserService.addMenu(indicatorCycleMenu);
                            if (BaseResult.FAIL == resultIndicatorCycle.getCode())
                            {
                                throw new ServiceException(resultIndicatorCycle.getMsg());
                            }
                            //指标数据列增删
                            SysMenu buttonMenu = new SysMenu();
                            buttonMenu.setMenuType(CommonConstant.MENU_TYPE_F);
                            buttonMenu.setParentId(parentId);
                            buttonMenu.setMenuName(Constant.INDICATOR_UPDATE_TITLE_BUTTON);
                            buttonMenu.setIsCache(Constant.MENU_NO_CACHE);
                            buttonMenu.setOrderNum("7");
                            buttonMenu.setPerms(sourceSimpleName);
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}",buttonMenu);
                            BaseResult buttonResult = remoteUserService.addMenu(buttonMenu);
                            if (BaseResult.FAIL == buttonResult.getCode())
                            {
                                throw new ServiceException(buttonResult.getMsg());
                            }
                        }
                        //如果版块是综合分析文章类版块也创建菜单
                        if(sourceManage1.getIsIndicators()==2){
                            log.info("进入综合分析文章类板块首次创建菜单逻辑");
                            //综合分析文章类菜单
                            SysMenu menu = new SysMenu();
                            menu.setMenuType(CommonConstant.MENU_TYPE_M);
                            menu.setParentId(Long.valueOf(0));
                            menu.setMenuName(sourceName);
                            menu.setPath(Constant.COMPREHENSIVE_ANALYSIS_URL + sourceSimpleName);
                            menu.setComponent(Constant.COMPREHENSIVE_ANALYSIS_COMPONENT);
                            //menu.setIcon(Constant.INDICATOR_MENU_ICON);
                            menu.setIsCache(Constant.MENU_NO_CACHE);
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}",menu);
                            BaseResult result = remoteUserService.addMenu(menu);
                            if (BaseResult.FAIL == result.getCode())
                            {
                                throw new ServiceException(result.getMsg());
                            }
                            //判断是否有字典类型
                            BaseResult<SysDictType> sysDictTypeResult1 = remoteUserService.getInfoByType(sourceSimpleName);
                            if(200==sysDictTypeResult1.getCode()&&ObjectUtils.isEmpty(sysDictTypeResult1.getData())){
                                //新增数据字典类型
                                SysDictType sysDictType = new SysDictType();
                                sysDictType.setDictName(sourceName);
                                sysDictType.setDictType(sourceSimpleName);
                                sysDictType.setStatus("0");
                                remoteUserService.addDictType(sysDictType);
                            }else if(200==sysDictTypeResult1.getCode()&&ObjectUtils.isNotEmpty(sysDictTypeResult1.getData())){
                                if("1".equals(sysDictTypeResult1.getData().getStatus())){
                                    remoteUserService.back(sysDictTypeResult1.getData().getDictId());
                                }
                            }
                            BaseResult<List<SysDictData>> sysDictTypeResult = remoteUserService.dictType(sourceSimpleName);
                            if(200==sysDictTypeResult.getCode()&&sysDictTypeResult.getData().size()==0) {
                                //新增字典类型数据
                                SysDictData sysDictData = new SysDictData();
                                sysDictData.setDictType(sourceSimpleName);
                                sysDictData.setDictValue("1");
                                sysDictData.setDictLabel("政务信息");
                                sysDictData.setDictSort(1L);
                                sysDictData.setStatus("0");
                                remoteUserService.addDictData(sysDictData);
                                SysDictData sysDictData1 = new SysDictData();
                                sysDictData1.setDictType(sourceSimpleName);
                                sysDictData1.setDictValue("2");
                                sysDictData1.setDictLabel("政务简报");
                                sysDictData1.setDictSort(2L);
                                sysDictData1.setStatus("0");
                                remoteUserService.addDictData(sysDictData1);
                            }

                        }
                        //招商引资类板块创建菜单
                        if(sourceManage1.getIsIndicators()==5){
                            //招商引资板块菜单
                            SysMenu menu = new SysMenu();
                            menu.setMenuType(CommonConstant.MENU_TYPE_M);
                            menu.setParentId(0L);
                            menu.setMenuName(sourceName);
                            menu.setPath(Constant.INVESTMENT_PROMOTION_URL + sourceSimpleName);
                            menu.setComponent(Constant.INVESTMENT_PROMOTION_COMPONENT);
                            //menu.setIcon(Constant.INDICATOR_MENU_ICON);
                            menu.setIsCache(Constant.MENU_NO_CACHE);
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}",menu);
                            BaseResult result = remoteUserService.addMenu(menu);
                            if (BaseResult.FAIL == result.getCode())
                            {
                                throw new ServiceException(result.getMsg());
                            }
                            Long parentId = Long.parseLong(result.getData().toString());

                            //指标管理菜单
                            SysMenu indicatorMenu = new SysMenu();
                            indicatorMenu.setMenuType(CommonConstant.MENU_TYPE_C);
                            indicatorMenu.setParentId(parentId);
                            indicatorMenu.setMenuName(Constant.INDICATOR_MENU);
                            indicatorMenu.setPath("/"+sourceSimpleName+Constant.INDICATOR_URL + sourceSimpleName);
                            indicatorMenu.setComponent(Constant.INDICATOR_COMPONENT);
                            //indicatorMenu.setIcon(Constant.INDICATOR_MENU_ICON);
                            indicatorMenu.setIsCache(Constant.MENU_NO_CACHE);
                            indicatorMenu.setOrderNum("1");
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}",indicatorMenu);
                            BaseResult resultIndicator = remoteUserService.addMenu(indicatorMenu);
                            if (BaseResult.FAIL == resultIndicator.getCode())
                            {
                                throw new ServiceException(resultIndicator.getMsg());
                            }

                            //指标数据管理菜单
                            SysMenu indicatorDataMenu = new SysMenu();
                            indicatorDataMenu.setMenuType(CommonConstant.MENU_TYPE_C);
                            indicatorDataMenu.setParentId(parentId);
                            indicatorDataMenu.setMenuName(Constant.INDICATOR_DATA_MENU);
                            indicatorDataMenu.setPath("/"+sourceSimpleName+Constant.INDICATOR_DATA_URL + sourceSimpleName);
                            indicatorDataMenu.setComponent(Constant.INDICATOR_DATA_COMPONENT);
                            //indicatorDataMenu.setIcon(Constant.INDICATOR_MENU_ICON);
                            indicatorDataMenu.setIsCache(Constant.MENU_NO_CACHE);
                            indicatorDataMenu.setOrderNum("2");
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}",indicatorDataMenu);
                            BaseResult resultIndicatorData = remoteUserService.addMenu(indicatorDataMenu);
                            if (BaseResult.FAIL == resultIndicatorData.getCode())
                            {
                                throw new ServiceException(resultIndicatorData.getMsg());
                            }

                            //信息简报菜单
                            SysMenu messageMenu = new SysMenu();
                            messageMenu.setMenuType(CommonConstant.MENU_TYPE_C);
                            messageMenu.setParentId(parentId);
                            messageMenu.setMenuName(Constant.MESSAGE_INDICATOR_MENU);
                            messageMenu.setPath("/"+sourceSimpleName+Constant.MESSAGE_INDICATOR_URL + sourceSimpleName);
                            messageMenu.setComponent(Constant.MESSAGE_INDICATOR_COMPONENT);
                            //messageMenu.setIcon(Constant.INDICATOR_MENU_ICON);
                            messageMenu.setIsCache(Constant.MENU_NO_CACHE);
                            messageMenu.setOrderNum("6");
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}",messageMenu);
                            BaseResult resultMessage = remoteUserService.addMenu(messageMenu);
                            if (BaseResult.FAIL == resultMessage.getCode())
                            {
                                throw new ServiceException(resultMessage.getMsg());
                            }

                            //指标数据审核
                            SysMenu dataAuditMenu = new SysMenu();
                            dataAuditMenu.setMenuType(CommonConstant.MENU_TYPE_C);
                            dataAuditMenu.setParentId(parentId);
                            dataAuditMenu.setMenuName(Constant.INDICATOR_AUDIT_MENU);
                            dataAuditMenu.setPath("/"+sourceSimpleName+Constant.INDICATOR_AUDIT_URL + sourceSimpleName);
                            dataAuditMenu.setComponent(Constant.INDICATOR_AUDIT_COMPONENT);
                            //dataAuditMenu.setIcon(Constant.INDICATOR_MENU_ICON);
                            dataAuditMenu.setIsCache(Constant.MENU_NO_CACHE);
                            dataAuditMenu.setOrderNum("3");
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}",dataAuditMenu);
                            BaseResult resultDataAudit = remoteUserService.addMenu(dataAuditMenu);
                            if (BaseResult.FAIL == resultDataAudit.getCode())
                            {
                                throw new ServiceException(resultDataAudit.getMsg());
                            }

                            //提交审核记录
                            SysMenu auditRecordMenu = new SysMenu();
                            auditRecordMenu.setMenuType(CommonConstant.MENU_TYPE_C);
                            auditRecordMenu.setParentId(parentId);
                            auditRecordMenu.setMenuName(Constant.INDICATOR_AUDIT_RECORD_MENU);
                            auditRecordMenu.setPath("/"+sourceSimpleName+Constant.INDICATOR_AUDIT_RECORD_URL + sourceSimpleName);
                            auditRecordMenu.setComponent(Constant.INDICATOR_AUDIT_RECORD_COMPONENT);
                            //auditRecordMenu.setIcon(Constant.INDICATOR_MENU_ICON);
                            auditRecordMenu.setIsCache(Constant.MENU_NO_CACHE);
                            auditRecordMenu.setOrderNum("4");
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}",auditRecordMenu);
                            BaseResult resultAuditRecord = remoteUserService.addMenu(auditRecordMenu);
                            if (BaseResult.FAIL == resultAuditRecord.getCode())
                            {
                                throw new ServiceException(resultAuditRecord.getMsg());
                            }

                            // 指标更新周期管理
                            SysMenu indicatorCycleMenu = new SysMenu();
                            indicatorCycleMenu.setMenuType(CommonConstant.MENU_TYPE_C);
                            indicatorCycleMenu.setParentId(parentId);
                            indicatorCycleMenu.setMenuName(Constant.INDICATOR_UPDATE_CYCLE_MENU);
                            indicatorCycleMenu.setPath("/"+sourceSimpleName+Constant.INDICATOR_UPDATE_CYCLE_URL + sourceSimpleName);
                            indicatorCycleMenu.setComponent(Constant.INDICATOR_UPDATE_CYCLE_COMPONENT);
                            //indicatorCycleMenu.setIcon(Constant.INDICATOR_MENU_ICON);
                            indicatorCycleMenu.setIsCache(Constant.MENU_NO_CACHE);
                            indicatorCycleMenu.setOrderNum("5");
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}",indicatorCycleMenu);
                            BaseResult resultIndicatorCycle = remoteUserService.addMenu(indicatorCycleMenu);
                            if (BaseResult.FAIL == resultIndicatorCycle.getCode())
                            {
                                throw new ServiceException(resultIndicatorCycle.getMsg());
                            }
                            //项目管理菜单
                            SysMenu projectManagementMenu = new SysMenu();
                            projectManagementMenu.setMenuType(CommonConstant.MENU_TYPE_C);
                            projectManagementMenu.setParentId(parentId);
                            projectManagementMenu.setMenuName(Constant.PROJECT_MANAGEMENT);
                            projectManagementMenu.setPath(Constant.PROJECT_MANAGEMENT_URL + sourceSimpleName);
                            projectManagementMenu.setComponent(Constant.PROJECT_MANAGEMENT_COMPONENT);
                            projectManagementMenu.setIsCache(Constant.MENU_NO_CACHE);
                            projectManagementMenu.setOrderNum("7");
                            projectManagementMenu.setPerms(sourceSimpleName);
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}", projectManagementMenu);
                            BaseResult resultProjectManagementMenu = remoteUserService.addMenu(projectManagementMenu);
                            if (BaseResult.FAIL == result.getCode()) {
                                throw new ServiceException(resultProjectManagementMenu.getMsg());
                            }
                            //项目进展管理菜单
                            SysMenu menuProjectProgress = new SysMenu();
                            menuProjectProgress.setMenuType(CommonConstant.MENU_TYPE_C);
                            menuProjectProgress.setParentId(parentId);
                            menuProjectProgress.setMenuName(Constant.PROJECT_PROGRESS);
                            menuProjectProgress.setPath(Constant.PROJECT_PROGRESS_URL + sourceSimpleName);
                            menuProjectProgress.setComponent(Constant.PROJECT_PROGRESS_COMPONENT);
                            menuProjectProgress.setIsCache(Constant.MENU_NO_CACHE);
                            menuProjectProgress.setVisible("1");
                            menuProjectProgress.setOrderNum("8");
                            menuProjectProgress.setPerms(sourceSimpleName);
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}", menuProjectProgress);
                            BaseResult resultProjectProgress = remoteUserService.addMenu(menuProjectProgress);
                            if (BaseResult.FAIL == result.getCode()) {
                                throw new ServiceException(resultProjectProgress.getMsg());
                            }
                            //指标数据列增删
                            SysMenu buttonMenu = new SysMenu();
                            buttonMenu.setMenuType(CommonConstant.MENU_TYPE_F);
                            buttonMenu.setParentId(parentId);
                            buttonMenu.setMenuName(Constant.INDICATOR_UPDATE_TITLE_BUTTON);
                            buttonMenu.setIsCache(Constant.MENU_NO_CACHE);
                            buttonMenu.setOrderNum("9");
                            buttonMenu.setPerms(sourceSimpleName);
                            //远程调用新增菜单
                            log.debug("远程调用新增菜单参数：{}",buttonMenu);
                            BaseResult buttonResult = remoteUserService.addMenu(buttonMenu);
                            if (BaseResult.FAIL == buttonResult.getCode())
                            {
                                throw new ServiceException(buttonResult.getMsg());
                            }
                            //判断是否有字典类型-招商引资项目类型
                            BaseResult<SysDictType> sysDictTypeResult1 = remoteUserService.getInfoByType(sourceSimpleName+Constant.PROJECT_TAGS);
                            if(200==sysDictTypeResult1.getCode()&&ObjectUtils.isEmpty(sysDictTypeResult1.getData())){
                                //新增数据字典类型
                                SysDictType sysDictType = new SysDictType();
                                sysDictType.setDictName(sourceName+"标签");
                                sysDictType.setDictType(sourceSimpleName+Constant.PROJECT_TAGS);
                                sysDictType.setStatus("0");
                                remoteUserService.addDictType(sysDictType);
                            }else if(200==sysDictTypeResult1.getCode()&&ObjectUtils.isNotEmpty(sysDictTypeResult1.getData())){
                                if("1".equals(sysDictTypeResult1.getData().getStatus())){
                                    remoteUserService.back(sysDictTypeResult1.getData().getDictId());
                                }
                            }
                            BaseResult<List<SysDictData>> sysDictTypeResult = remoteUserService.dictType(sourceSimpleName+Constant.PROJECT_TAGS);
                            if(200==sysDictTypeResult.getCode()&&sysDictTypeResult.getData().size()==0) {
                                //新增字典类型数据能源、化工、食品、医药、汽车、轻工
                                SysDictData sysDictData = new SysDictData();
                                sysDictData.setDictType(sourceSimpleName+Constant.PROJECT_TAGS);
                                sysDictData.setDictValue("1");
                                sysDictData.setDictLabel("能源");
                                sysDictData.setDictSort(1L);
                                sysDictData.setStatus("0");
                                remoteUserService.addDictData(sysDictData);
                                SysDictData sysDictData1 = new SysDictData();
                                sysDictData1.setDictType(sourceSimpleName+Constant.PROJECT_TAGS);
                                sysDictData1.setDictValue("2");
                                sysDictData1.setDictLabel("化工");
                                sysDictData1.setDictSort(2L);
                                sysDictData1.setStatus("0");
                                remoteUserService.addDictData(sysDictData1);
                                SysDictData sysDictData2 = new SysDictData();
                                sysDictData2.setDictType(sourceSimpleName+Constant.PROJECT_TAGS);
                                sysDictData2.setDictValue("3");
                                sysDictData2.setDictLabel("食品");
                                sysDictData2.setDictSort(3L);
                                sysDictData2.setStatus("0");
                                remoteUserService.addDictData(sysDictData2);
                                SysDictData sysDictData3 = new SysDictData();
                                sysDictData3.setDictType(sourceSimpleName+Constant.PROJECT_TAGS);
                                sysDictData3.setDictValue("4");
                                sysDictData3.setDictLabel("医药");
                                sysDictData3.setDictSort(4L);
                                sysDictData3.setStatus("0");
                                remoteUserService.addDictData(sysDictData3);
                                SysDictData sysDictData4 = new SysDictData();
                                sysDictData4.setDictType(sourceSimpleName+Constant.PROJECT_TAGS);
                                sysDictData4.setDictValue("5");
                                sysDictData4.setDictLabel("汽车");
                                sysDictData4.setDictSort(5L);
                                sysDictData4.setStatus("0");
                                remoteUserService.addDictData(sysDictData4);
                                SysDictData sysDictData5 = new SysDictData();
                                sysDictData5.setDictType(sourceSimpleName+Constant.PROJECT_TAGS);
                                sysDictData5.setDictValue("6");
                                sysDictData5.setDictLabel("轻工");
                                sysDictData5.setDictSort(6L);
                                sysDictData5.setStatus("0");
                                remoteUserService.addDictData(sysDictData5);
                            }
                            //判断是否有字典类型-招商引资地区字典
                            BaseResult<SysDictType> sysDictTypeResultSite = remoteUserService.getInfoByType(sourceSimpleName+Constant.PROJECT_SITE);
                            if(200==sysDictTypeResultSite.getCode()&&ObjectUtils.isEmpty(sysDictTypeResultSite.getData())){
                                //新增数据字典类型
                                SysDictType sysDictType = new SysDictType();
                                sysDictType.setDictName(sourceName+"地区");
                                sysDictType.setDictType(sourceSimpleName+Constant.PROJECT_SITE);
                                sysDictType.setStatus("0");
                                remoteUserService.addDictType(sysDictType);
                            }else if(200==sysDictTypeResultSite.getCode()&&ObjectUtils.isNotEmpty(sysDictTypeResultSite.getData())){
                                if("1".equals(sysDictTypeResultSite.getData().getStatus())){
                                    remoteUserService.back(sysDictTypeResultSite.getData().getDictId());
                                }
                            }
                            BaseResult<List<SysDictData>> sysDictTypeResultSiteData = remoteUserService.dictType(sourceSimpleName+Constant.PROJECT_SITE);
                            if(200==sysDictTypeResultSiteData.getCode()&&sysDictTypeResultSiteData.getData().size()==0) {
                                //新增字典类型数据区域1、区域2、区域3
                                SysDictData sysDictData = new SysDictData();
                                sysDictData.setDictType(sourceSimpleName+Constant.PROJECT_SITE);
                                sysDictData.setDictValue("1");
                                sysDictData.setDictLabel("区域1");
                                sysDictData.setDictSort(1L);
                                sysDictData.setStatus("0");
                                remoteUserService.addDictData(sysDictData);
                                SysDictData sysDictData1 = new SysDictData();
                                sysDictData1.setDictType(sourceSimpleName+Constant.PROJECT_SITE);
                                sysDictData1.setDictValue("2");
                                sysDictData1.setDictLabel("区域2");
                                sysDictData1.setDictSort(2L);
                                sysDictData1.setStatus("0");
                                remoteUserService.addDictData(sysDictData1);
                                SysDictData sysDictData2 = new SysDictData();
                                sysDictData2.setDictType(sourceSimpleName+Constant.PROJECT_SITE);
                                sysDictData2.setDictValue("3");
                                sysDictData2.setDictLabel("区域3");
                                sysDictData2.setDictSort(3L);
                                sysDictData2.setStatus("0");
                                remoteUserService.addDictData(sysDictData2);
                            }

                        }
                    }
                    SourceManage sourceManage = new SourceManage();
                    sourceManage.setId(Integer.valueOf(id));
                    sourceManage.setIsStart(Constant.START_YES);
                    sourceManageMapper.updateByPrimaryKeySelective(sourceManage);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("厅局启用失败");
        }
        return 1;
    }

    @Override
    public int deleteSourceManageById(Integer id) {
        SourceManage sourceManage = new SourceManage();
        sourceManage.setId(id);
        sourceManage.setIsDelete(Constant.DELETE_YES);
        int i = sourceManageMapper.updateByPrimaryKeySelective(sourceManage);
        return i;
    }

    /**
     * 查询厅局列表
     *
     * @return
     */
    @Override
    public List<SourceManageResVO> listSource(String type, String sourceName,Integer isStart) {
        //查询
        List<SourceManageResVO> result = sourceManageMapper.selectPage(type,sourceName,isStart);
        result.forEach(dto -> {
            //创建人匹配
            if (StringUtils.isNotEmpty(dto.getCreater())) {
                BaseResult<String> nickName = remoteUserService.getNickNameByName(dto.getCreater());
                dto.setCreater(nickName.getData());
            }
            //更新人匹配
            if (StringUtils.isNotEmpty(dto.getUpdater())) {
                BaseResult<String> nickName = remoteUserService.getNickNameByName(dto.getUpdater());
                dto.setUpdater(nickName.getData());
            }
            //板块类型匹配
            if(TypeEnums.GROUP.getCode().equals(dto.getType())){
                dto.setTypeName(TypeEnums.GROUP.getName());
            }
            if(TypeEnums.OTHER.getCode().equals(dto.getType())){
                dto.setTypeName(TypeEnums.OTHER.getName());
            }
        });
        return result;
    }

    @Override
    public SourceManage getBySourceId(String sourceId) {
        SourceManage sourceManage = sourceManageMapper.getSourceInfoBySourceId(sourceId);
        if (sourceManage == null || sourceManage.getId() == null) {
            throw new CockpitBusinessException(ResultInfoEnum.SOURCE_MANAGE_NULL);
        }
        return sourceManage;
    }

    @Override
    public SourceManage getById(String id) {
        SourceManage sourceManage = sourceManageMapper.getSourceInfoById(id);
        if (sourceManage == null || sourceManage.getId() == null) {
            throw new CockpitBusinessException(ResultInfoEnum.SOURCE_MANAGE_NULL);
        }
        return sourceManage;
    }

    @Override
    public List<SourceManageTypeVO> getTypeList(Long[] roleIds) {
        List<SourceManageTypeVO> result = new ArrayList<>();
        //添加默认选项
        SourceManageTypeVO group = new SourceManageTypeVO(TypeEnums.GROUP.getName(),TypeEnums.GROUP.getCode());
        result.add(group);
        SourceManageTypeVO other = new SourceManageTypeVO(TypeEnums.OTHER.getName(),TypeEnums.OTHER.getCode());
        result.add(other);
        //查询创建的分组
        List<SourceManageTypeVO> typeResult = sourceManageMapper.getTypeList(TypeEnums.GROUP.getCode());
        if(null != typeResult && typeResult.size() > 0){
            result.addAll(typeResult);
        }
        //查询分组下的板块
        if(null != roleIds && roleIds.length > 0){
            for(SourceManageTypeVO sourceManageTypeVO : result){
                List<GetMenuListDto> menuList = sourceManageMapper.getMenuListByRoleIdAndType(roleIds,sourceManageTypeVO.getCode());
                sourceManageTypeVO.setMenuList(menuList);
            }
        }
        return result;
    }

    @Override
    public List<SourceManage> getAllIndicatorsSourceManage() {
        return sourceManageMapper.getAllSourceManage1();
    }

    @Override
    public int insertOrUpdateSourceManageUrl(String urlIds,String urlName, String urlType, String sourceSimpleName) {
        return sourceManageMapper.insertOrUpdateSourceManageUrl(urlIds,urlName,urlType,sourceSimpleName);
    }

    @Override
    public SourceManageUrl getSourceManageUrl(String sourceSimpleName) {
        return sourceManageMapper.getSourceManageUrl(sourceSimpleName);
    }

    @Override
    public int insertOrUpdateSourceManageIndicatorGroup(String first, String second,String appReportInfoFlag, String sourceSimpleName) {
        return sourceManageMapper.insertOrUpdateSourceManageIndicatorGroup(first,second,appReportInfoFlag,sourceSimpleName);
    }

    @Override
    public SourceManageIndicatorGroup getSourceManageIndicatorGroup(String sourceSimpleName) {
        return sourceManageMapper.getSourceManageIndicatorGroup(sourceSimpleName);
    }

    /**
     * 根据用户权限获取用户拥有的指标版块和项目版块
     *
     * @return 版块列表
     */
    @Override
    public List<SourceManageVo> getSourceManageBySourceSimpleNameList(List<String> sourceSimpleNameList) {
        // 调用Mapper查询用户有权限的版块列表
        return sourceManageMapper.getSourceManageBySourceSimpleNameList(sourceSimpleNameList);
    }
}