package com.onecity.os.management.configmanage.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.exception.CockpitBusinessException;
import com.onecity.os.management.configmanage.entity.SysSeqConf;
import com.onecity.os.management.configmanage.mapper.SysSeqConfMapper;
import com.onecity.os.management.configmanage.service.SysSeqConfService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2020/5/21 10:38
 */
@Service
public class SysConfServiceImpl implements SysSeqConfService {
    private Logger log = LoggerFactory.getLogger(SysConfServiceImpl.class);
    @Autowired
    private SysSeqConfMapper sysSeqConfMapper;

    /**修改序列返回下一个值（String）
     * @param seqId
     * @return
     */
    @Override
    public String getStringSequence(String seqId){
        log.info("Start - getStringSequence");
        String seq = null;
        log.info("Parameter - seqId:"+seqId
        );
        if(seqId!=null&&!"".equals(seqId)){
            SysSeqConf sysSeqConf = sysSeqConfMapper.selectByPrimaryKey(seqId);
            if(sysSeqConf!=null){
                seq = this.seqCurrentNext(sysSeqConf);
            }else{
                log.error("通过seqId："+seqId+"查找主键序列失败，返回值为空！");
                throw new CockpitBusinessException("系统异常，请联系管理员！");
            }
        }else{
            log.error("查找序列失败，key值为空！");
            throw new CockpitBusinessException("系统异常，请联系管理员！");
        }
        log.info("End - getStringSequence");
        return seq;
    }

    /**修改序列值加1
     * @param sysSeqConf
     * @return
     */
    @Override
    public String seqCurrentNext(SysSeqConf sysSeqConf){
        log.info("Start-seqCurrentNext");
        //声明返回值
        String seq = null;
        if(sysSeqConf!=null){
            long seqCurrent = sysSeqConf.getSeqCurrent();
            long seqMaximum = sysSeqConf.getSeqMaximum();
            if(seqCurrent!=0){
                //获取下一位
                seqCurrent +=1;
                //判断序列值是否超过最大值
                if(seqCurrent>seqMaximum){
                    log.error("序列值已经超出最大值！");
                    throw new CockpitBusinessException("系统异常，请联系管理员！");
                }else{
                    sysSeqConf.setSeqCurrent(seqCurrent);
                    //修改数据库
                    int i = sysSeqConfMapper.updateByPrimaryKeySelective(sysSeqConf);
                    //判断修改是否成功
                    if(i==1){
                        //转成字符串
                        seq = Long.toString(seqCurrent);
                        log.info("获取序列成功，seqId："+ JSONObject.toJSONString(sysSeqConf.getSeqId())+"seqCurrent："+JSONObject.toJSONString(seqCurrent));
                    }else{
                        log.info("主键序列修改失败seqId："+JSONObject.toJSONString(sysSeqConf.getSeqId()));
                        throw new CockpitBusinessException("系统异常，请联系管理员！");
                    }
                }
            }else{
                String seqId = sysSeqConf.getSeqId();
                log.info("seqId:"+JSONObject.toJSONString(seqId)+"-序列当前值为空");
                throw new CockpitBusinessException("系统异常，请联系管理员！");
            }
        }else{
            log.error("调用seqCurrentNext方法传入对对象为空！");
            throw new CockpitBusinessException("系统异常，请联系管理员！");
        }
        log.info("End-seqCurrentNext");
        return seq;
    }
}
