package com.onecity.os.management.constant;

/**
 * 系统用常量
 *
 * <AUTHOR>
 */
public interface Constant {

    // 关注直联
    String attention = "关注";
    String connection = "直联";

    /**
     * 是否删除
     */
    Integer DELETE_NO = 0;
    Integer DELETE_YES = 1;

    /**
     * 是否启用
     */
    Integer START_NO = 0;
    Integer START_YES = 1;

    /**
     * 是否展示
     */
    Integer SHOW_NO = 0;
    Integer SHOW_YES = 1;

    /**
     * 是否展示筛选框
     */
    Integer SCREEN_NO = 0;
    Integer SCREEN_YES = 1;

    /**
     * 一级指标
     */
    String PARENT_ID_ONE_LEVEL = "0";

    String SOURCE_ID = "SOURCE_ID";

    String GENERAL_INDICATOR_TALBE_SUFFIX = "_general_indicator";
    String GENERAL_INDICATOR_DATA_TALBE_SUFFIX = "_general_indicator_data";


    /**
     * 核心和解读指标
     */
    Integer CORE_DATA = 0;
    Integer DETAIL_DATA = 1;

    /**
     * Tab和正常指标
     */
    Integer GENERAL_INDICATOR = 0;
    Integer TAB_INDICATOR = 1;


    /**
     * 是否当前数据
     */
    Integer CURRENT_DATA_YES = 1;
    Integer CURRENT_DATA_NO = 0;

    /**
     * 是否折行显示 0：否1：是
     */
    Integer FOLD_NO = 0;
    Integer FOLD_YES = 1;

    String SUCCESS = "success";

    String SOURCE_COMPONENT = "layouts/RouteView";
    String SOURCE_URL = "/tjzt/";
    String INDICATOR_COMPONENT = "layouts/RouteView";
    String INDICATOR_URL = "/tjzt/target/index/";
    String INDICATOR_DATA_COMPONENT = "layouts/RouteView";
    String INDICATOR_DATA_URL = "/tjzt/dataTarget/index/";
    String MESSAGE_INDICATOR_COMPONENT = "layouts/RouteView";
    String MESSAGE_INDICATOR_URL = "/tjzt/xxjb/index/";
    String INDICATOR_AUDIT_URL = "/tjzt/dataTarget/checking/";
    String INDICATOR_AUDIT_COMPONENT = "tjzt/dataTarget/checking";
    String INDICATOR_AUDIT_RECORD_URL = "/tjzt/tjshjl/index/";
    String INDICATOR_AUDIT_RECORD_COMPONENT = "tjzt/tjshjl/index";
    String INDICATOR_UPDATE_CYCLE_URL = "/tjzt/dataUpdate/index/";
    String INDICATOR_UPDATE_CYCLE_COMPONENT = "tjzt/dataUpdate/index";

    /**
     * 厅局编码长度
     */
    Integer SOURCE_SIMPLE_NAME_LENGTH = 10;

    String INDICATOR_MENU = "指标管理";
    String INDICATOR_DATA_MENU = "指标数据管理";
    String MESSAGE_INDICATOR_MENU = "信息简报";
    String INDICATOR_AUDIT_MENU = "指标数据审核";
    String INDICATOR_AUDIT_RECORD_MENU = "提交审核记录";
    String INDICATOR_UPDATE_CYCLE_MENU = "指标更新周期管理";


}
