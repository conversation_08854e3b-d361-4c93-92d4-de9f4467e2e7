package com.onecity.os.management.constant;

/**
 *是否核心区数据
 */
public enum DataStyleEnum {

    CORE_DATA(0, "是"),
    GENERAL_DATA(1, "否");
    private final Integer code;
    private final String message;

    DataStyleEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getValue() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static DataStyleEnum getByMessage(String message){
        DataStyleEnum[] arr =  DataStyleEnum.values();
        for (int i = 0; i < arr.length; i++) {
            if (arr[i].message.equals(message)){
                return arr[i];
            }
        }
        return null;
    }
}
