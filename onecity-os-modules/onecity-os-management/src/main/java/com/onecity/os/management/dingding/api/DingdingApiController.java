package com.onecity.os.management.dingding.api;

import com.alibaba.fastjson.JSON;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.management.dingding.model.vo.DisposalNoticeVO;
import com.onecity.os.management.dingding.model.vo.NoticeMessageVO;
import com.onecity.os.management.dingding.utils.AppManageUtil;
import com.onecity.os.management.dingding.utils.AuthHelper;
import com.onecity.os.management.dingding.utils.DingEVN;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: yuk
 * @menu
 * @Description:
 * @Date: 2022/6/25 16:11
 * @Version: 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/dingdingapi")
@Api(tags = "钉钉feign调用接口")
public class DingdingApiController {
    @Autowired
    private AppManageUtil appManageUtil;
    /**
     * 批示功能发送通知
     */
    @PostMapping("/sendDisposalNotice")
    @ApiOperation(value = "批示功能发送通知")
    public BaseResult sendDisposalNotice(@RequestBody DisposalNoticeVO disposalNoticeVO) {
        log.info("*********批示功能发送通知:" + JSON.toJSONString(disposalNoticeVO));
        appManageUtil.sendDisposalNotice(disposalNoticeVO);
        return BaseResult.ok("发送成功");
    }

    /**
     * 督办功能发送通知
     */
    @PostMapping("/sendSuperviseNote")
    @ApiOperation(value = "督办功能发送通知")
    public BaseResult sendSuperviseNote(@RequestBody DisposalNoticeVO disposalNoticeVO) {
        log.info("*********督办功能发送通知::" + JSON.toJSONString(disposalNoticeVO));
        appManageUtil.sendSuperviseNote(disposalNoticeVO);
        return BaseResult.ok("发送成功");
    }

    /**
     * 通知公告发送通知
     */
    @PostMapping("/sendNoticeNote")
    @ApiOperation(value = "通知公告功能发送通知")
    public BaseResult sendNoticeNote(@RequestBody NoticeMessageVO noticeMessageVO) {
        log.info("*********通知公告发送通知::" + JSON.toJSONString(noticeMessageVO));
        if(noticeMessageVO.getUserIds().size() == 0 ){
            return BaseResult.fail("发送失败，用户不能为空");
        }
        String userIds = StringUtils.join(noticeMessageVO.getUserIds(),",");
        String accessToken = null;
        try {
            accessToken = AuthHelper.getAccessToken(DingEVN.AppKey, DingEVN.AppSecret);
            log.info("获取accessToken成功, --> {}", accessToken);
        } catch (Exception e) {
            log.error("获取accessToken失败! --> {} ", e);
        }
        appManageUtil.sendTianBaoRemindMsg(accessToken, DingEVN.AgentId, userIds,
                noticeMessageVO.getMessage(), noticeMessageVO.getSource(), noticeMessageVO.getHost());
        return BaseResult.ok("发送成功");
    }
}