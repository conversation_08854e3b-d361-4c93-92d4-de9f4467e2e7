package com.onecity.os.management.dingding.controller;


import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.poi.ExcelUtil;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.management.dingding.service.DingMailService;
import com.onecity.os.system.api.RemoteLogService;
import com.onecity.os.system.api.domain.DingOperateDataExcel;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * 钉钉通讯录管理
 */
@Slf4j
@RestController
@RequestMapping("/dingLog")
@Api(tags = "钉钉通讯录")
public class DingLogController extends BaseController {

    @Autowired
    DingMailService dingMailService;

    @Autowired
    RemoteLogService remoteLogService;

//    @Autowired
//    DingMailLogService dingMailLogService;

    /**
     * 查询APP操作日志
     * @param pageNo pageNo
     * @param pageSize pageSize
     * @param dayStart dayStart
     * @param dayEnd dayEnd
     * @return
     */
//    @RequestMapping(value = "/getOperateLog", method = RequestMethod.GET)
//    public TableDataInfo queryAppPageList(@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
//                                          @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
//                                          @RequestParam(name="dayStart") String dayStart,
//                                          @RequestParam(name="dayEnd") String dayEnd) throws ParseException {
//        startPage();
//        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        List<DingOperateVo> voList = dingMailLogService.getDingOperateLog(format.parse(dayStart), format.parse(dayEnd));
//        return getDataTable(voList);
//    }

    /**
     * 导出APP操作日志
     * @param dayStart dayStart
     * @param dayEnd dayEnd
     * @return
     * @throws ParseException
     */
    @RequestMapping(value = "/exportOperateLog")
    public BaseResult exportOperateLog(@RequestParam(name="dayStart") String dayStart,
                                       @RequestParam(name="dayEnd") String dayEnd, HttpServletResponse response) throws ParseException {
        // 导出文件名称
        List<DingOperateDataExcel> excel = remoteLogService.exportOperateLog(dayStart, dayEnd);
        ExcelUtil<DingOperateDataExcel> util = new ExcelUtil<DingOperateDataExcel>(DingOperateDataExcel.class);
        try {
            util.exportExcel(response,excel, "移动端访问记录");
        } catch (IOException e) {
            e.printStackTrace();
        }
        return BaseResult.ok();
    }
}

























