package com.onecity.os.management.dingding.mapper;

import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.management.dingding.model.po.Role;
import com.onecity.os.management.dingding.model.po.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DingMailRoleMapper extends BaseMapper<Role> {

    Role selectRoleByRoleid(String roleid);

    Integer getRoleLevelUp(@Param("roleids") List<String> roleids);

    List<String> selectRoleByLevel(Integer levelUp, Integer levelDown);

    void saveRole(Role role);

    void truncateData();

    /**
     * 根据角色ids,查找角色名称
     *
     * @param roleIds
     * @return
     */
    User getRoleNameByRoleIds(@Param("roleIds") List<String> roleIds);
}
