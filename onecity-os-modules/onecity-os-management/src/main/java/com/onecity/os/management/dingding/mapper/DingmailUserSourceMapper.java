package com.onecity.os.management.dingding.mapper;

import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.management.dingding.model.po.DingmailUserSource;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/28 14:06
 */
@Mapper
public interface DingmailUserSourceMapper extends BaseMapper<DingmailUserSource> {

    @Select("SELECT source_id FROM dingmail_user_source WHERE user_id=#{userId}")
    List<String> getSourcesByUserId(String userId);

    @Delete({"<script>",
            "DELETE FROM dingmail_user_source WHERE source_id IN ",
            "<foreach item='existSource' collection='existSources'", "open='(' separator=',' close=')'>",
            "#{existSource}",
            "</foreach>",
            "</script>"})
    void deleteBySources(@Param("existSources") List<String> existSources);

    @Select("SELECT source_id FROM dingmail_user_source WHERE user_id=#{userId}")
    List<String> getSourceListByUserId(String userId);
}
