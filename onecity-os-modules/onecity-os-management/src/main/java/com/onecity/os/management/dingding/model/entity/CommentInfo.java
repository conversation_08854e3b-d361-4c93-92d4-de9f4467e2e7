package com.onecity.os.management.dingding.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 审批内容传递参数类
 */
@Data
public class CommentInfo {

    @ApiModelProperty(value = "接收人姓名")
    private String receiver;
    @ApiModelProperty(value = "接收人userid")
    private String receiverid;
    @ApiModelProperty(value = "文件id,不是fileId")
    private Integer fileid;
    @ApiModelProperty(value = "审批标题")
    private String commenttitle;
    @ApiModelProperty(value = "审批内容")
    private String commentcontent;
    @ApiModelProperty(value = "需要审批的信息id")
    private Integer adminid;
}
