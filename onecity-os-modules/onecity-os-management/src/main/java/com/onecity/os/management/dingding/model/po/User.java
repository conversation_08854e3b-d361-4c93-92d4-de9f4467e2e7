package com.onecity.os.management.dingding.model.po;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 用户类
 * <AUTHOR>
 * @Date 2020/3/19
 * @Version V1.0
 **/
@Data
@Table(name = "dingmail_user")
@ApiModel("用户信息表")
public class User {

    @Id
    private Long id;
    private String userid;
    private String name;     //必须

    /**成员所属部门id列表**/
    private String departments;    //必须
    /**手机号码**/
    private String mobile;            //必须
    /**角色id**/
    private String roles;
    /**角色等级**/
    private Integer rolelevel;
    /**职位信息**/
    private String position;
    /**部门中的排序信息**/
    private String orderindepts;

    /**
     * 部门ids
     */
    private String departmentIds;

}
