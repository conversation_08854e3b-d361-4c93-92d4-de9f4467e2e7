package com.onecity.os.management.dingding.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/7/21 11:00
 */
@Data
public class DingdingUserVo {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 钉钉用户userid
     */
    private String userid;

    /**
     * 钉钉用户名称
     */
    private String name;

    /**
     * 手机号码
     **/
    private String mobile;

    /**
     * 职位信息
     **/
    private String position;

    /**
     * 成员所属部门
     **/
    private String departments;

    /**
     * 成员所属部门ids
     */
    @JsonIgnore
    private String departmentIds;

    /**
     * 角色
     **/
    private String roles;

}
