package com.onecity.os.management.dingding.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.exception.ServiceException;
import com.onecity.os.management.dingding.mapper.DingMailUserMapper;
import com.onecity.os.management.dingding.model.entity.DingWorkMsgInfo;
import com.onecity.os.management.dingding.model.po.User;
import com.onecity.os.management.dingding.model.vo.DingFileVo;
import com.onecity.os.management.dingding.model.vo.DingdingVo;
import com.onecity.os.management.dingding.model.vo.DisposalNoticeVO;
import com.onecity.os.management.utils.DateUtils;
import com.onecity.os.management.zhibiao.feign.MessageFeignService;
import com.onecity.os.management.zhibiao.model.dto.RemindInfoDto;
import com.onecity.os.system.api.RemoteUserService;
import com.taobao.api.ApiException;
import com.taobao.api.FileItem;
import com.taobao.api.internal.util.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 钉钉接口调用管理
 */
@Slf4j
@Component
public class AppManageUtil {

    private static final String POST_SINGLE_URL = "https://oapi.dingtalk.com/file/upload/single?access_token=ACCESS_TOKEN&agent_id=AGENT_ID&file_size=FILE_SIZE";
    private static final String POST_SINGLECHAT_URL = "https://oapi.dingtalk.com/cspace/add_to_single_chat?access_token=ACCESS_TOKEN&agent_id=AGENT_ID&userid=USERID&media_id=MEDIA_ID&file_name=FILE_NAME";
    private static final String GET_SPACE_URL = "https://oapi.dingtalk.com/cspace/get_custom_space?access_token=ACCESS_TOKEN&domain=DOMAIN&agent_id=AGENT_ID";
    private static final String GRANT_SPACE_URL = "https://oapi.dingtalk.com/cspace/grant_custom_space?access_token=ACCESS_TOKEN&agent_id=AGENT_ID&domain=DOMAIN&userid=USERID&duration=DURATION&type=TYPE";
    //工作通知消息发送地址
    private static final String POST_WORK_MSG = "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2?access_token=ACCESS_TOKEN";
    //权限有效时间，有效范围为0~3600秒，超出此范围或不传默认为30秒
    private static final Integer DURATION = 3600;
    private static final String DOMAIN = "preview";

    @Value("${dingding.baseMessageUrl}")
    private String baseMessageUrl;
    @Value("${dingding.superviseMessageUrl}")
    private String superviseMessageUrl;
    @Value("${dingding.noticeMessageUrl}")
    private String noticeMessageUrl;
    @Value("${dingding.disposalMessageUrl}")
    private String disposalMessageUrl;
    @Value("${dingding.indicatorMessageUrl}")
    private String indicatorMessageUrl;
    @Value("${dingding.indicatorWarnMessageUrl}")
    private String indicatorWarnMessageUrl;
    @Value("${dingding.picUrl}")
    private String picUrl;

    @Resource
    DingMailUserMapper dingMailUserMapper;

    @Resource
    private MessageFeignService messageFeignService;

    @Autowired
    private RemoteUserService remoteUserService;

    //单步文件上传

    /**
     * 根据accessToken, agentid 调用钉盘单步文件上传接口
     *
     * @param accessToken
     * @param agent_id
     * @throws Exception void
     * @desc ：
     * media_id	文件存储id
     */
    public JSONObject uploadDingFile(String accessToken, String agent_id, MultipartFile file) {
        if (file == null || file.getSize() == 0) {
            return null;
        }
        try {
            OapiFileUploadSingleRequest req = new OapiFileUploadSingleRequest();
            req.setAgentId(agent_id);
            req.setFileSize(file.getSize());
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/file/upload/single?" + WebUtils.buildQuery(req.getTextParams(), "utf-8"));
            // 必须重新new一个请求
            req = new OapiFileUploadSingleRequest();
            req.setFile(new FileItem(Base64Util.multipartFileToFile(file)));
            OapiFileUploadSingleResponse rsp = client.execute(req, accessToken);
            JSONObject jsonObject = JSONObject.parseObject(rsp.getBody());
            log.info("钉盘请求respones --> {}", jsonObject);
            if (null != jsonObject) {
                //3.解析结果
                if (null != jsonObject) {
                    //4.请求成功,则返回jsonObject
                    if (0 == jsonObject.getInteger("errcode")) {
                        return jsonObject;
                    }
                    //5.错误消息处理
                    if (0 != jsonObject.getInteger("errcode")) {
                        int errCode = jsonObject.getInteger("errcode");
                        String errMsg = jsonObject.getString("errmsg");
                        throw new Exception("error code:" + errCode + ", error message:" + errMsg);
                    }
                }
            }
            return jsonObject;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    //发送钉盘文件给指定用户
    public String sendDingFile(String code, String accessToken, String agent_id, String userid, String media_id, String file_name) {
        OapiCspaceAddToSingleChatRequest request = new OapiCspaceAddToSingleChatRequest();
        request.setAgentId(agent_id);
        request.setUserid(userid);
        request.setMediaId(media_id);
        request.setFileName(file_name);
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/cspace/add_to_single_chat?access_token=" + accessToken + WebUtils.buildQuery(request.getTextParams(), "utf-8"));
            OapiCspaceAddToSingleChatResponse response = client.execute(request, accessToken);
            log.info("发送文件给指定用户 respones ---> {}", response);
            if (null != response && null != response.getErrcode()) {
                if (0 == response.getErrcode()) {
                    log.info("发送文件给指定用户成功!");
                    //获取企业自定义钉盘空间
                    //请求成功返回 空间id spaceid
                    JSONObject customSpace = getCustomSpace(accessToken, DingEVN.AgentId);
                    String spaceId = "";
                    if (null != customSpace && null != customSpace.getString("spaceid")) {
                        log.info("获取企业自定义空间spaceId为 --> {}", customSpace.getString("spaceid"));
                        spaceId = customSpace.getString("spaceid");
                    }
                    //新增文件到自定义空间
                    JSONObject jsonObject = addDingFileToSpace(code, accessToken, agent_id, media_id, spaceId, file_name);
                    DingFileVo dingFileVo = new DingFileVo();
                    if (null != jsonObject && null != jsonObject.getString("dentry")) {
                        JSONObject jsonObject1 = JSONObject.parseObject(jsonObject.getString("dentry"));
                        String fileId = jsonObject1.getString("id");
                        Integer fileSize = jsonObject1.getInteger("size");
                        String fileType = jsonObject1.getString("extension");
                        String fileName = jsonObject1.getString("name");
                        dingFileVo.setFileId(fileId);
                        dingFileVo.setFileName(fileName);
                        dingFileVo.setFileSize(fileSize);
                        dingFileVo.setFileType(fileType);
                        dingFileVo.setSpaceId(spaceId);
                        log.info("新增文件到钉盘自定义空间 文件信息为 ---> {}", JSONObject.toJSON(dingFileVo));
                    }
                    return "success";
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
//        //1.获取请求url
//        String url=POST_SINGLECHAT_URL.replace("ACCESS_TOKEN", accessToken).replace("AGENT_ID", agent_id).replace("USERID",userid).replace("MEDIA_ID",media_id).replace("FILE_NAME",file_name);
//        try {
//            JSONObject jsonObject = HttpHelper.httpPost(url);
//            //3.解析结果
//            if (null != jsonObject) {
//                //4.请求成功,则返回jsonObject
//                if (0==jsonObject.getInteger("errcode")) {
//                    return jsonObject;
//                }
//                //5.错误消息处理
//                if (0 != jsonObject.getInteger("errcode")) {
//                    int errCode = jsonObject.getInteger("errcode");
//                    String errMsg = jsonObject.getString("errmsg");
//                    throw new Exception("error code:"+errCode+", error message:"+errMsg);
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        return null;
    }

    public DingFileVo sendDingFileToUser(String createid, String code, String accessToken, String agent_id, List<String> userids, String media_id, String file_name) {
        OapiCspaceAddToSingleChatRequest request;
        for (String userid : userids) {
            request = new OapiCspaceAddToSingleChatRequest();
            request.setAgentId(agent_id);
            request.setUserid(userid);
            request.setMediaId(media_id);
            request.setFileName(file_name);
            try {
//                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/cspace/add_to_single_chat?"+WebUtils.buildQuery(request.getTextParams(),"utf-8"));
                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/cspace/add_to_single_chat?" + WebUtils.buildQuery(request.getTextParams(), "utf-8"));
                OapiCspaceAddToSingleChatResponse response = client.execute(new OapiCspaceAddToSingleChatRequest(), accessToken);
                log.info("发送文件给指定用户userid--->{}, respones ---> {}", userid, JSONObject.toJSON(response));
                if (0 == response.getErrcode()) {
                    log.info("发送文件给指定用户userid--->{}, respones ---> {} 成功", userid, JSONObject.toJSON(response));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //授权用户访问企业钉盘空间
        try {
            //授权用户访问企业自定义空间
            JSONObject jsonObject = grantCustomSpace(accessToken, DingEVN.AgentId, "add", createid, null);
            log.info("授权用户访问企业自定义空间 userid {} , grantCustomSpace --> {}", createid, jsonObject.toJSONString());
        } catch (Exception e) {
            log.error("授权用户访问企业自定义空间失败 ! --> {}", e);
        }
        try {
            //获取企业自定义钉盘空间
            //请求成功返回 空间id spaceid
            JSONObject customSpace = getCustomSpace(accessToken, DingEVN.AgentId);
            String spaceId = "";
            if (null != customSpace && null != customSpace.getString("spaceid")) {
                log.info("获取企业自定义空间spaceId为 --> {}", customSpace.getString("spaceid"));
                spaceId = customSpace.getString("spaceid");
            }
            //新增文件到自定义空间
            JSONObject jsonObject = addDingFileToSpace(code, accessToken, agent_id, media_id, spaceId, file_name);
            DingFileVo dingFileVo = new DingFileVo();
            if (null != jsonObject && null != jsonObject.getString("dentry")) {
                JSONObject jsonObject1 = JSONObject.parseObject(jsonObject.getString("dentry"));
                String fileId = jsonObject1.getString("id");
                Integer fileSize = jsonObject1.getInteger("size");
                String fileType = jsonObject1.getString("extension");
                String fileName = jsonObject1.getString("name");
                dingFileVo.setFileId(fileId);
                dingFileVo.setFileName(fileName);
                dingFileVo.setFileSize(fileSize);
                dingFileVo.setFileType(fileType);
                dingFileVo.setSpaceId(spaceId);
                log.info("新增文件到钉盘自定义空间 文件信息为 ---> {}", JSONObject.toJSON(dingFileVo));
            }
            return dingFileVo;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new DingFileVo();
    }

    /**
     * @param accessToken
     * @param agent_id
     * @return {
     * "errcode":0,
     * "errmsg":"ok",
     * "spaceid":"xxxx"
     * }
     * @throws Exception
     * @描述 根据accessToken agentid 获取企业下的自定义空间
     */
    public JSONObject getCustomSpace(String accessToken, String agent_id) throws Exception {
        //1.获取请求url
        String url = GET_SPACE_URL.replace("ACCESS_TOKEN", accessToken).replace("DOMAIN", DOMAIN).replace("AGENT_ID", agent_id);
        //2.发起GET请求，获取返回结果
        JSONObject jsonObject = HttpHelper.httpGet(url);
        System.out.println("jsonObject:" + jsonObject.toString());

        //3.解析结果，获取spaceid
        if (null != jsonObject) {
            //4.请求成功,则返回jsonObject
            if (0 == jsonObject.getInteger("errcode")) {
                return jsonObject;
            }
            //5.错误消息处理
            if (0 != jsonObject.getInteger("errcode")) {
                int errCode = jsonObject.getInteger("errcode");
                String errMsg = jsonObject.getString("errmsg");
                throw new Exception("error code:" + errCode + ", error message:" + errMsg);
            }
        }
        return null;
    }

    /**
     * @param accessToken 调用接口凭证
     * @param agent_id    授权访问指定微应用的自定义空间
     * @param type        权限类型，目前支持上传和下载，上传请传add，下载请传download
     * @param userid      企业用户userid
     * @param fields      授权访问的文件id列表，id之间用英文逗号隔开，如"fileId1,fileId2", type=download时必须传递
     * @return
     * @throws Exception
     * @描述 授权用户访问企业下的自定义空间
     */
    public JSONObject grantCustomSpace(String accessToken, String agent_id, String type, String userid, String fields) throws Exception {
        //1.根据type类型生成url,
        String url = getGrantSpaceUrl(accessToken, agent_id, type, userid, fields, DURATION);
        System.out.println("url:" + url);
        //2.发起GET请求，获取返回结果
        JSONObject jsonObject = HttpHelper.httpGet(url);
        //3.解析结果，获取spaceid
        if (null != jsonObject) {
            //4.请求成功,则返回jsonObject
            if (0 == jsonObject.getInteger("errcode")) {
                return jsonObject;
            }
            //5.错误消息处理
            if (0 != jsonObject.getInteger("errcode")) {
                int errCode = jsonObject.getInteger("errcode");
                String errMsg = jsonObject.getString("errmsg");
                throw new Exception("error code:" + errCode + ", error message:" + errMsg);
            }
        }
        return null;
    }

    public void sendWorkMsg(String accessToken, String agent_id, String userid_list, String dept_id_list, DingWorkMsgInfo dingWorkMsgInfo) {

        //1.获取请求url
        String url = POST_WORK_MSG.replace("ACCESS_TOKEN", accessToken);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("agent_id", agent_id);
        jsonObject.put("userid_list", userid_list);
        if (StringUtils.isNotBlank(dept_id_list)) {
            jsonObject.put("dept_id_list", dept_id_list);
        }
        log.info("撤回人姓名 --> {}", dingWorkMsgInfo.getUsername());
        log.info("发送人单位 --> {}", dingWorkMsgInfo.getDepartments());
        log.info("发送模块 --> {}", dingWorkMsgInfo.getModule());
        log.info("发送文件列表 --> {}", dingWorkMsgInfo.getFilenames());
        log.info("发送时间 --> {}", dingWorkMsgInfo.getTime());

        JSONObject msgJson = new JSONObject();
        msgJson.put("msgtype", "text");
        JSONObject textJson = new JSONObject();
//        textJson.put("content","您收到文件 [ "+ fileNames + " ] 信息报送! 请及时处理.");
        textJson.put("content", dingWorkMsgInfo.getTime() + " 发送人：" + dingWorkMsgInfo.getUsername() + "撤销了文件名为：" + dingWorkMsgInfo.getFilenames() + "的文件");
        msgJson.put("text", textJson);
        jsonObject.put("msg", msgJson);
        try {
            JSONObject jsonObject1 = HttpHelper.httpPost(url, jsonObject);
            log.info("撤回消息发送通知结果为 --> {}", jsonObject1);
        } catch (Exception e) {
            log.error("消息发送失败 --> {}", e);
        }
    }

    /**
     * 钉钉消息通知 添加消息实体类 DingWorkMsgInfo
     * 发送人姓名
     * 发送人单位
     * 发送模块
     * 发送文件名列表
     */
    public Long sendWorkMsgUpdate(String accessToken, String agent_id, String userid_list, String dept_id_list, DingWorkMsgInfo dingWorkMsgInfo) {
        //1.获取请求url
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");

        OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
        request.setUseridList(userid_list);
        request.setAgentId(Long.valueOf(agent_id));
        request.setToAllUser(false);

        log.info("发送人姓名 --> {}", dingWorkMsgInfo.getUsername());
        log.info("发送人单位 --> {}", dingWorkMsgInfo.getDepartments());
        log.info("发送模块 --> {}", dingWorkMsgInfo.getModule());
        log.info("发送文件列表 --> {}", dingWorkMsgInfo.getFilenames());
        log.info("发送时间 --> {}", dingWorkMsgInfo.getTime());
        log.info("链接地址 --> {}", dingWorkMsgInfo.getMessageUrl());
        String[] fileNames = dingWorkMsgInfo.getFilenames().split(",");
        List<String> fileArrays = new ArrayList<>();

        for (String fileName : fileNames) {
            fileArrays.add(getFileInfo(fileName));
        }
//        String content = dingWorkMsgInfo.getTime()+" 收到来自【"+dingWorkMsgInfo.getModule()+"】消息通知,发送人:"+dingWorkMsgInfo.getUsername()+";通知文件:“"+String.join(",", fileArrays)+"” 请及时处理!";
        String title = dingWorkMsgInfo.getTime() + " 收到来自【" + dingWorkMsgInfo.getModule() + "】消息通知:";
        String content = "发送人:" + dingWorkMsgInfo.getUsername() + ";通知文件:“" + String.join(",", fileArrays) + "” 请及时处理!";
        OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
        msg.setMsgtype("link");
        msg.setLink(new OapiMessageCorpconversationAsyncsendV2Request.Link());
        msg.getLink().setTitle(title);
        msg.getLink().setText(content);
        //link.messageUrl 链接地址
        msg.getLink().setMessageUrl(dingWorkMsgInfo.getMessageUrl());
        //link.picUrl 图片地址
        if (dingWorkMsgInfo.getModule().equals("要情专报")) {
            msg.getLink().setPicUrl(picUrl);
        } else if (dingWorkMsgInfo.getModule().equals("通知公告")) {
            msg.getLink().setPicUrl(picUrl);
        } else {
            msg.getLink().setPicUrl("test");
        }

        request.setMsg(msg);

//        textJson.put("content", content);
//        msgJson.put("text", textJson);
//        jsonObject.put("msg",msgJson);
        try {
//            JSONObject jsonObject1 = HttpHelper.httpPost(url, jsonObject);
            OapiMessageCorpconversationAsyncsendV2Response response = client.execute(request, accessToken);
            DingdingVo dingdingVo = JSONObject.parseObject(response.getBody(), DingdingVo.class);
            if (dingdingVo.getErrcode() == 0) {
                Long task_id = dingdingVo.getTask_id();
                log.info("工作消息id--->{}", task_id);
                return task_id;
            }
            log.info("消息发送通知结果为 --> {}", dingdingVo);
            return null;
        } catch (Exception e) {
            log.error("消息发送失败 --> {}", e);
            return null;
        }
    }

    /**
     * 根据type生成grantspaceurl
     **/
    public String getGrantSpaceUrl(String accessToken, String agent_id, String type, String userid, String fields, Integer duration) {
        String url = GRANT_SPACE_URL.replace("ACCESS_TOKEN", accessToken).replace("AGENT_ID", agent_id).replace("DOMAIN", DOMAIN).replace("USERID", userid).replace("DURATION", duration + "").replace("TYPE", type);
        if (type.equals("add")) {
            //钉盘默认上传路径
            String path = "/";
            url = url + "&path=" + path;
            return url;
        } else {
            url = url + "&fileids=" + fields;
            return url;
        }
    }

    /**
     * 新增文件到自定义空间
     */
    public JSONObject addDingFileToSpace(String code, String accessToken, String agent_id, String media_id, String spaceid, String filename) {
        OapiCspaceAddRequest req = new OapiCspaceAddRequest();
        req.setAgentId(agent_id);
        req.setCode(code);
        req.setMediaId(media_id);
        req.setSpaceId(spaceid);
//        request.setFolderId("1234");
        req.setName(filename);
        req.setOverwrite(true);
        req.setHttpMethod("GET");
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/cspace/add?" + WebUtils.buildQuery(req.getTextParams(), "utf-8"));
            OapiCspaceAddResponse rsp = client.execute(new OapiCspaceAddRequest(), accessToken);
            log.info("新增文件到钉盘自定义空间请求respones --> {}", rsp.getBody());
            return JSONObject.parseObject(rsp.getBody());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


/***钉钉部门/用户调用接口first****************************************************************************************************************************************/
    /**
     * 查询钉钉部门列表
     */
    public List<OapiDepartmentListResponse.Department> getDepartmentInfos(String accessToken, String departmentId, Boolean fetchchild) {
        OapiDepartmentListRequest req = new OapiDepartmentListRequest();
        if (null == departmentId || StringUtils.isBlank(departmentId)) {
            req.setId("1");
        } else {
            req.setId(departmentId);
        }
        req.setHttpMethod("GET");
        req.setFetchChild(fetchchild);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/department/list");
        try {
            OapiDepartmentListResponse resp = client.execute(req, accessToken);
            if (null != resp && resp.getErrcode() == 0) {
                List<OapiDepartmentListResponse.Department> departmentList = resp.getDepartment();
                log.info("查询子部门列表结果为 -->{}, ", JSONObject.toJSONString(departmentList));
                if (CollectionUtils.isNotEmpty(departmentList)) {
                    return departmentList;
                }
            }
        } catch (ApiException e) {
            log.error("查询子部门失败 --> {}", e);
        }
        return null;
    }

    /**
     * 删除部门
     */
    public void delDepartment(String accessToken, String departmentId) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/department/delete");
        OapiDepartmentDeleteRequest request = new OapiDepartmentDeleteRequest();
        request.setId(departmentId);
        request.setHttpMethod("GET");
        try {
            OapiDepartmentDeleteResponse response = client.execute(request, accessToken);
            if (null != response.getErrcode() && response.getErrcode() == 0) {
                log.info("删除部门成功 --> {}", departmentId);
            }
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }

    /**
     * 更新部门
     */
    public void updateDepartmentInfo(String accessToken, OapiDepartmentUpdateRequest updateRequest) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/department/update");
        try {
            OapiDepartmentUpdateResponse response = client.execute(updateRequest, accessToken);
            if (null != response.getErrcode() && response.getErrcode() == 0) {
                log.info("更新部门信息成功! --> {}", JSONObject.toJSONString(updateRequest));
            }
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除用户
     */
    public void delDingUser(String accessToken, String userid) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/user/delete");
        OapiUserDeleteRequest request = new OapiUserDeleteRequest();
        request.setUserid(userid);
        request.setHttpMethod("GET");
        try {
            OapiUserDeleteResponse response = client.execute(request, accessToken);
            if (null != response.getErrcode() && response.getErrcode() == 0) {
                log.info("删除用户成功 --> {}", userid);
            }
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }

    /**
     * 更新用户
     */
    public void updateUserInfo(String accessToken, OapiUserUpdateRequest updateRequest) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/department/update");
        try {
            OapiUserUpdateResponse response = client.execute(updateRequest, accessToken);
            if (null != response.getErrcode() && response.getErrcode() == 0) {
                log.info("更新用户信息成功! --> {}", JSONObject.toJSONString(updateRequest));
            }
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取企业员工数
     */
    public Long getOrgUserCount(String accessToken) {
        OapiUserGetOrgUserCountRequest req = new OapiUserGetOrgUserCountRequest();
        req.setHttpMethod("GET");
        //不包含未激活的钉钉用户
        req.setOnlyActive(1L);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/user/get_org_user_count");
        try {
            OapiUserGetOrgUserCountResponse resp = client.execute(req, accessToken);
            if (null != resp && resp.getErrcode() == 0) {
                log.info("企业员工数为 {} ", resp.getCount());
                return resp.getCount();
            }
        } catch (ApiException e) {
            e.printStackTrace();
        }
        return 0L;
    }

    /**
     * 查询各部门员工信息
     */
    public List<OapiUserListbypageResponse.Userlist> getUserInfos(String accessToken, String departmentId, Long offset, List<OapiUserListbypageResponse.Userlist> result) {
        OapiUserListbypageRequest req = new OapiUserListbypageRequest();
        if (StringUtils.isBlank(departmentId)) req.setDepartmentId(1L);
        else req.setDepartmentId(Long.valueOf(departmentId));
        req.setOffset(offset);
        req.setSize(100L);
        req.setHttpMethod("GET");
        req.setOrder("custom");
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/user/listbypage");
        try {
            OapiUserListbypageResponse resp = client.execute(req, accessToken);
            if (null != resp && resp.getErrcode() == 0) {
                List<OapiUserListbypageResponse.Userlist> userlist = resp.getUserlist();
                log.info("查询子部门列表结果为 -->{}, ", JSONObject.toJSONString(userlist));
                if (CollectionUtils.isNotEmpty(userlist)) {
                    if (userlist.size() < 100) {
                        result.addAll(userlist);
                        return userlist;
                    } else {
                        offset = offset + 100;
                        getUserInfos(accessToken, departmentId, offset, result);
                    }
                }
            }
        } catch (ApiException e) {
            log.error("查询子部门失败 --> {}", e);
        }
        return result;
    }

    /**
     * 根据部门id获取userid列表
     * departmentid 为空, 则查询根部门所有userid
     */
    public List<String> getUserIds(String accessToken, String departmentId) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/user/getDeptMember");
        OapiUserGetDeptMemberRequest req = new OapiUserGetDeptMemberRequest();
        if (StringUtils.isNotBlank(departmentId)) req.setDeptId(departmentId);
        else req.setDeptId("1");
        req.setHttpMethod("GET");
        try {
            OapiUserGetDeptMemberResponse rsp = client.execute(req, accessToken);
            List<String> userIds = rsp.getUserIds();
            return userIds;
        } catch (ApiException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据userid查询用户详情
     */
    public User getUserInfo(String accessToken, String userId) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/user/get");
        OapiUserGetRequest request = new OapiUserGetRequest();
        request.setUserid(userId);
        request.setHttpMethod("GET");
        try {
            OapiUserGetResponse response = client.execute(request, accessToken);
            if (response.getErrcode() == 0) {
                User user = new User();
                String mobile = response.getMobile();
                List<Long> departmentids = response.getDepartment();
                log.info("同步用户信息-----根据userid查询用户详情,返回的response.getDepartment()---" + response.getDepartment());
                List<OapiUserGetResponse.Roles> roles = response.getRoles();
                //过滤 DingEVN.DingRoleGroupName 角色信息
                if (null != roles && CollectionUtils.isNotEmpty(roles)) {
                    //DingEVN.DingRoleGroupName
                    List<OapiUserGetResponse.Roles> roles2 = roles.stream().collect(Collectors.toList());
                    user.setRoles(String.join(",", roles2.stream().map(s -> String.valueOf(s.getId())).collect(Collectors.toList())));
                }
                user.setUserid(userId);
                user.setMobile(mobile);
                user.setName(response.getName());
                user.setOrderindepts(response.getOrderInDepts());
                log.info("同步用户信息-----根据userid查询用户详情,返回的response.getOrderInDepts()---" + response.getOrderInDepts());
                if (null != response.getPosition() && StringUtils.isNotBlank(response.getPosition()))
                    user.setPosition(response.getPosition());
                user.setDepartments(String.join(",", departmentids.stream().map(s -> String.valueOf(s)).collect(Collectors.toList())));
                log.info("同步用户信息-----根据userid查询用户详情,返回的response中的部门---" + user.getDepartments());
                return user;
            }
        } catch (ApiException e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 查询钉钉自定义晋中市角色信息
     */
    public List<OapiRoleListResponse.OpenRoleGroup> getRoleInfos(String accessToken) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/role/list");
        OapiRoleListRequest request = new OapiRoleListRequest();
//        request.setOffset(0L);
//        request.setSize(100L);
        try {
            OapiRoleListResponse response = client.execute(request, accessToken);
            List<OapiRoleListResponse.OpenRoleGroup> list = response.getResult().getList();
            // log.info("查询角色信息数量 -->{}, ", JSONObject.toJSONString(list.size()));
            //log.info("查询角色信息数量11 -->{}, ", JSONObject.toJSONString(DingEVN.DingRoleGroupName));
            List<OapiRoleListResponse.OpenRoleGroup> openRoleGroups = list.stream().collect(Collectors.toList());
            //List<OapiRoleListResponse.OpenRoleGroup> openRoleGroups = list.stream().filter(openRoleGroup -> openRoleGroup.getName().equals("驾驶舱")).collect(Collectors.toList());
            // log.info("查询角色信息数量2 -->{}, ", JSONObject.toJSONString(openRoleGroups.get(0)));
            return openRoleGroups;
        } catch (ApiException e) {
            e.printStackTrace();
        }
        return null;
    }

/***钉钉部门/用户调用接口end****************************************************************************************************************************************/


    /**
     *
     */
    public static String getFileInfo(String fileName) {
        int index = fileName.lastIndexOf(".");
        int firstIndex = fileName.lastIndexOf("(");
        int secondIndex = index - 1;
        if (fileName.substring(secondIndex, secondIndex + 1).equals(")") && fileName.substring(firstIndex, firstIndex + 1).equals("(") && NumberValidationUtil.isNumeric(fileName.substring(firstIndex + 1, secondIndex))) {
            fileName = fileName.replace(fileName.substring(firstIndex, secondIndex + 1), "");
        }
        return fileName;
    }

    /**
     * 钉钉消息通知 撤回消息通知
     * 发送人姓名
     * 发送人单位
     * 发送模块
     * 发送文件名列表
     */
    public Long withdrawWorkMsgUpdate(String accessToken, String agent_id, Long task_id) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/recall");
        OapiMessageCorpconversationRecallRequest request = new OapiMessageCorpconversationRecallRequest();
        request.setAgentId(Long.valueOf(agent_id));
        request.setMsgTaskId(task_id);

        try {
            OapiMessageCorpconversationRecallResponse response = client.execute(request, accessToken);
            DingdingVo dingdingVo = JSONObject.parseObject(response.getBody(), DingdingVo.class);

            log.info("消息撤回 通知结果为 --> {}", dingdingVo);
            return dingdingVo.getErrcode();
        } catch (Exception e) {
            log.error("消息撤回失败 --> {}", e);
            return null;
        }
    }

    /**
     * 填报系统更新数据后,给指定钉钉用户发送更新数据的消息提醒
     *
     * @param accessToken 调用接口凭证
     * @param agentId     授权访问指定微应用的自定义空间
     * @param userIdList  钉钉用户ids,多个用英文逗号隔开
     * @param message     要发送的消息
     * @param source      要发送消息的来源
     * @param host        ip地址
     */
    public void sendTianBaoRemindMsg(String accessToken, String agentId, String userIdList, String message, String source, String host) {
        if (StringUtils.isNotEmpty(userIdList)) {
            // 1.获取请求url
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setUseridList(userIdList);
            request.setAgentId(Long.valueOf(agentId));
            request.setToAllUser(false);
            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setMsgtype("link");
            msg.setLink(new OapiMessageCorpconversationAsyncsendV2Request.Link());
            // 设置标题和内容
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String title = dateFormat.format(new Date()) + " 收到来自【" + source + "】消息通知: \r\n";
            msg.getLink().setTitle(title);
            msg.getLink().setText(message);
            msg.getLink().setPicUrl(picUrl);
            log.info("通知公告和要情专报,跳转前接收到的IP地址:host-----" + JSONObject.toJSONString(host));
            if (source.equals("要情专报")) {
                msg.getLink().setMessageUrl(baseMessageUrl + "yqzb");
            } else if (source.equals("通知公告")) {
                log.info("配置msg的messageUrl--->{}"+ JSONObject.toJSONString(baseMessageUrl) + JSONObject.toJSONString(noticeMessageUrl));
                msg.getLink().setMessageUrl(baseMessageUrl + noticeMessageUrl + "?currentView=notice");
            } else if (source.equals("公文")) {
                log.info("配置msg的messageUrl--->{}"+JSONObject.toJSONString(baseMessageUrl) + JSONObject.toJSONString(noticeMessageUrl));
                msg.getLink().setMessageUrl(baseMessageUrl + noticeMessageUrl + "?currentView=gongWen");
            }else if (source.equals("指标批示")) {
                log.info("配置msg的superviseMessageUrl--->{}"+JSONObject.toJSONString(baseMessageUrl) + JSONObject.toJSONString(disposalMessageUrl));
                msg.getLink().setMessageUrl(baseMessageUrl + disposalMessageUrl);
            }else if (source.equals("督办信息")) {
                log.info("配置msg的superviseMessageUrl--->{}"+JSONObject.toJSONString(baseMessageUrl) + JSONObject.toJSONString(superviseMessageUrl));
                msg.getLink().setMessageUrl(baseMessageUrl + superviseMessageUrl);
            }else if (source.equals("指标更新")) {
                log.info("配置msg的indicatorMessageUrl--->{}"+JSONObject.toJSONString(baseMessageUrl) + JSONObject.toJSONString(indicatorMessageUrl));
                msg.getLink().setMessageUrl(baseMessageUrl + indicatorMessageUrl);
            }else {
                msg.getLink().setPicUrl("test");
            }
            request.setMsg(msg);
            try {
                log.info("发送消息getMsg--->{}", request.getMsg());
                log.info("发送消息msg--->{}", msg);
                log.info("发送消息messageUrl--->{}", msg.getLink().getMessageUrl());
                OapiMessageCorpconversationAsyncsendV2Response response = client.execute(request, accessToken);
                DingdingVo dingdingVo = JSONObject.parseObject(response.getBody(), DingdingVo.class);
                log.info("消息发送通知结果为 --> {}", dingdingVo);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("消息发送失败 --> {}", e);
            }
        }
    }

    public void sendAppRemindMsg(String title, String content, Byte remindedType, String loginName,
                                  String appUserIds, String level, String source, String htmlContent,String appMsgType,String serviceId) {
        RemindInfoDto remindInfoDto = new RemindInfoDto();
        remindInfoDto.setRemindTitle(title);
        remindInfoDto.setIsRead((byte) 0);
        remindInfoDto.setRemindedType(remindedType);
        remindInfoDto.setIsDelete((byte) 0);
        remindInfoDto.setCreater(loginName);
        remindInfoDto.setCreateTime(DateUtils.dateTimeIntact());
        remindInfoDto.setRemindContent(content);
        remindInfoDto.setPcUserId(null);
        remindInfoDto.setAppUserId(appUserIds);
        remindInfoDto.setSourceType("APP");
        remindInfoDto.setLevel(level);
        remindInfoDto.setHtmlContent(htmlContent);
        remindInfoDto.setServiceId(serviceId);
        remindInfoDto.setAppMsgType(appMsgType);
        if ("要情专报".equals(source)) {
            remindInfoDto.setMessageUrl(baseMessageUrl + "yqzb");
        } else if ("通知公告".equals(source)) {
            remindInfoDto.setMessageUrl(baseMessageUrl + noticeMessageUrl + "?currentView=notice");
        } else if ("公文".equals(source)) {
            remindInfoDto.setMessageUrl(baseMessageUrl + noticeMessageUrl + "?currentView=gongWen");
        } else if ("指标批示".equals(source)) {
            remindInfoDto.setMessageUrl(baseMessageUrl + disposalMessageUrl);
            remindInfoDto.setServiceType("zbps");
        } else if ("督办信息".equals(source)) {
            remindInfoDto.setMessageUrl(baseMessageUrl + superviseMessageUrl);
        } else if ("指标更新".equals(source)) {
            remindInfoDto.setMessageUrl(baseMessageUrl + indicatorMessageUrl);
        } else if ("指标预警".equals(source)) {
            remindInfoDto.setMessageUrl(baseMessageUrl + indicatorWarnMessageUrl);
        } else if ("指标统计".equals(source)) {
            //指标统计不发送消息
//            remindInfoDto.setMessageUrl(baseMessageUrl + indicatorMessageUrl);
        }
        messageFeignService.addMsg(Collections.singletonList(remindInfoDto));
    }


    public Boolean sendDisposalNotice(DisposalNoticeVO disposalNoticeVO) {
        String roleIds = disposalNoticeVO.getRoleIds();
        //远程调用
        BaseResult<String> pcUserIdResult = remoteUserService.getUserIdsByRoles(roleIds.split(","));
        log.info("**********根据角色查询用户id参数：：" + JSONObject.toJSONString(roleIds));
        if (BaseResult.FAIL == pcUserIdResult.getCode())
        {
            throw new ServiceException(pcUserIdResult.getMsg());
        }
        String pcUserId = pcUserIdResult.getData();
        StringBuffer stringBuffer = new StringBuffer();
        log.info("**********发送通知给用户：：" + pcUserId);
        if(StringUtils.isBlank(pcUserId)){
            return false;
        }
        this.sendAppRemindMsg("指标批示 批示待处理", disposalNoticeVO.getMessage(), (byte) 0, "admin",
                pcUserId, "3", "指标批示", null,disposalNoticeVO.getAppMsgType(),disposalNoticeVO.getServiceId());
        return true;
    }

    public Boolean sendSuperviseNote(DisposalNoticeVO disposalNoticeVO) {
        List<String> userIdList = Arrays.asList(disposalNoticeVO.getUserIds().split(","));
        StringBuffer stringBuffer = new StringBuffer();
        log.info("**********发送通知给用户：：" + JSON.toJSONString(userIdList));
        if(userIdList.size() == 0 ){
            return false;
        }
        for(String str : userIdList){
            stringBuffer.append(str).append(",");
        }
        stringBuffer.deleteCharAt(stringBuffer.length()-1);
        String accessToken = null;
        try {
            accessToken = AuthHelper.getAccessToken(DingEVN.AppKey, DingEVN.AppSecret);
            log.info("获取accessToken成功, --> {}", accessToken);
        } catch (Exception e) {
            log.error("获取accessToken失败! --> {} ", e);
        }
        sendTianBaoRemindMsg(accessToken, DingEVN.AgentId, stringBuffer.toString(),
                disposalNoticeVO.getMessage(), "督办信息", disposalNoticeVO.getHost());

        return true;
    }

    /**
     * 填报系统更新数据后,给指定钉钉用户发送更新数据的消息提醒
     *
     * @param accessToken 调用接口凭证
     * @param agentId     授权访问指定微应用的自定义空间
     * @param userIdList  钉钉用户ids,多个用英文逗号隔开
     * @param msg         要发送的消息
     */
    public void sendTianBaoRemindMsg(String accessToken, String agentId, String userIdList, String msg) {
        if (StringUtils.isNotEmpty(userIdList)) {
            //1.获取请求url
            String url = POST_WORK_MSG.replace("ACCESS_TOKEN", accessToken);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("agent_id", agentId);
            jsonObject.put("userid_list", userIdList);

            JSONObject msgJson = new JSONObject();
            msgJson.put("msgtype", "text");
            JSONObject textJson = new JSONObject();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            String title = dateFormat.format(new Date()) + " 收到来自【系统管理】消息通知: \r\n" + msg;
            textJson.put("content", title);
            msgJson.put("text", textJson);
            jsonObject.put("msg", msgJson);
            msgJson.put("text", textJson);
            jsonObject.put("msg", msgJson);
            try {
                JSONObject jsonObject1 = HttpHelper.httpPost(url, jsonObject);
                log.info("填报系统更新数据后,消息发送通知结果为 --> {}", jsonObject1);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("填报系统更新数据后,消息发送失败 --> {}", e);
            }
        }
    }

    /**
     * 通知公告,要情专报,撤回消息提醒
     *
     * @param accessToken
     * @param agentId
     * @param userIdList
     * @param message
     * @param source
     */
    public void sendTianBaoCheHuiMsg(String accessToken, String agentId, String userIdList, String message, String source) {
        if (StringUtils.isNotEmpty(userIdList)) {
            // 1.获取请求url
            String url = POST_WORK_MSG.replace("ACCESS_TOKEN", accessToken);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("agent_id", agentId);
            jsonObject.put("userid_list", userIdList);

            JSONObject msgJson = new JSONObject();
            msgJson.put("msgtype", "text");
            JSONObject textJson = new JSONObject();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String title = dateFormat.format(new Date()) + " 收到来自【" + source + "】消息通知: \r\n" + message;
            textJson.put("content", title);
            msgJson.put("text", textJson);
            jsonObject.put("msg", msgJson);
            try {
                JSONObject jsonObject1 = HttpHelper.httpPost(url, jsonObject);
                log.info("填报系统更新数据后,消息发送通知结果为 --> {}", jsonObject1);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("填报系统更新数据后,消息发送失败 --> {}", e);
            }
        }
    }

}
