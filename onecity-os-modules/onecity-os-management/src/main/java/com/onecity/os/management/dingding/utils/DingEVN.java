package com.onecity.os.management.dingding.utils;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;


/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/2/14
 * @Version V1.0
 **/
@Log4j2
@Configuration
public class DingEVN {

    /**AgentId   ：462856094
     AppKey    ：dingfqy3lmt9pkodbqc2
     AppSecret ：PT-ClCOrDg4toDSxiR8VN9EW4eZ9msrbGb2B3wBGc51s3z4iXnSCzpPt6zyI0t7i
     CorpId:ding54cc4615ae85dfd1a1320dcb25e91351
     * 企业应用接入秘钥相关
     */

//jinzhogn  测试
//    public static final String CORP_ID = "dingwstqa9fhk8zhb6xk";
//    public static final String CORP_SECRET = "6WowrH7ggvfUpa5OhIYYQhNiSIDw0fES5-1Fvo9kn2tsYjUh-NFm5gWEvKjlf7zL";
//    public static final String AGENTID = "468723840";
//    public static final String CORP_ID2 = "dingb06e5a01a4c7deadf5bf40eda33b7ba0";
//jinzhogn  正式


    public static String AppKey;

    public static String AppSecret;

    public static String AgentId;

    public static String CorpId;

    public static String SignedUrl;

    public static String DingRoleGroupName;
//    dingding.DingRoleGroupName=驾驶舱
//    dingding.DingRoleLevel=1,2,3,4,5
//    dingding.DingRoleLevelName=一级职位,二级职位,三级职位,四级职位,五级职位
//#钉钉向上查看通讯录范围, 应为通讯录为五级,所有 5 就是向上看到所有的
//    dingding.DingRoleLevelUp=5
//            #钉钉向下查看通讯录范围
//    dingding.DingRoleLevelDown=2
    /**钉钉自定义角色组**/
//    public static String DingRoleGroupName="驾驶舱演示版";
    public static String[] DingRoleLevels={"1","2","3","4","5"};
    public static String[] DingRoleLevelNames={"一级职位","二级职位","三级职位","四级职位","五级职位"};
    public static Integer DingRoleLevelUp=5;
    public static Integer DingRoleLevelDown=2;

    @Value("${dingding.AppKey}")
    public  void setAppKey(String appKey) {
        AppKey = appKey;
    }
    @Value("${dingding.AppSecret}")
    public  void setAppSecret(String appSecret) {
        AppSecret = appSecret;
    }
    @Value("${dingding.AgentId}")
    public  void setAgentId(String agentId) {
        AgentId = agentId;
    }
    @Value("${dingding.CorpId}")
    public  void setCorpId(String corpId) {
        CorpId = corpId;
    }
    @Value("${dingding.SignedUrl}")
    public  void setSignedUrl(String signedUrl) {
        SignedUrl = signedUrl;
    }
    @Value("${dingding.DingRoleGroupName}")
    public  void setDingRoleGroupName(String roleGroupName) {
        DingRoleGroupName = roleGroupName;
    }
//    @Value("${dingding.DingRoleLevelUp}")
//    public  void setDingRoleLevelUp(String roleLevelUp) {
//        DingRoleLevelUp = Integer.valueOf(roleLevelUp);
//    }
//    @Value("${dingding.DingRoleLevelDown}")
//    public  void setDingRoleLevelDown(String roleLevelDown) {
//        DingRoleLevelDown = Integer.valueOf(roleLevelDown);
//    }
//    @Value("${dingding.DingRoleLevel}")
//    public  void setDingRoleLevels(String dingRoleLevel) {
//        DingRoleLevels = dingRoleLevel.split(",");
//    }
//    @Value("${dingding.DingRoleLevelName}")
//    public  void setDingRoleLevelNames(String dingRoleLevelName) {
//        DingRoleLevelNames = dingRoleLevelName.split(",");
//    }
//    public static final String AppKey = "dingmxecllsgpoywcuoa";
//    public static final String AppSecret = "YqKdya1gfgtA5CI9FM_mewENMaUvwIUgebfA9qCr3FyX7JBDyNBjHbnk1U6jMDzs";
//    public static final String AgentId = "498425204";
//    public static final String CorpId = "dingcb0ece4941b1d6caf2c783f7214b6d69";
//    public static final String SignedUrl = "http://**************:8081/jz/";
    /**
     * DING API地址
     */
    public static final String OAPI_HOST = "https://oapi.dingtalk.com";
    /**
     * 企业应用后台地址，用户管理后台免登使用
     */
    public static final String OA_BACKGROUND_URL = "";


    /**
     * 企业通讯回调加密Token，注册事件回调接口时需要传递给钉钉服务器
     */
    public static final String TOKEN = "";
    public static final String ENCODING_AES_KEY = "";



}
