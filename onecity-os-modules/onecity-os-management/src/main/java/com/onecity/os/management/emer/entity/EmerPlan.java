package com.onecity.os.management.emer.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;


/**
 * 应急预案表
 *
 * <AUTHOR>
 * @date 2020/10/29 09:43
 */
@Data
@Table(name = "emer_plan")
public class EmerPlan {
    /**
     * 主键自增
     **/
    @Id
    private String id;

    /**
     * 预案名称
     */
    private String name;

    /**
     * 预案类型
     */
    private Integer type;

    /**
     * 排序
     */
    private Integer sequence;

    /**
     * 状态
     */
    private String  state;

    /**
     * 编制单位
     */
    private String  createCompany;

    /**
     * 生效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date effectDate;

    /**
     * 文件名
     */
    private String  fileName;

    /**
     * 文件路径
     */
    private String  filePath;

    /**
     * 是否删除（0否，1是）
     */
    private Integer isDelete=0;

    /**
     * 创建人
     */
    private String creater;

    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date  createTime;

    /**
     * 修改人
     */
    private String  updater;

    /**
     * 修改日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date  updateTime;

    private Integer nums=0;

}
