package com.onecity.os.management.emer.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.onecity.os.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 信息指标表
 *
 * <AUTHOR>
 * @date 2020/6/10 16:01
 */
@Data
@Table(name = "emer_report")
public class EmerReportIndicator {
    /**
     * 主键
     **/
    @Id
    private String id;

    /**
     * 评估报告名称
     **/
    @ApiModelProperty(value = "评估报告名称", example = "安全生产应急救援队伍")
    private String name;

    /**
     * 灾害类型
     */
    @Excel(name = "灾害类型", width = 15)
    @ApiModelProperty(value = "灾害类型", example = "自然灾害")
    private String type;

    /**
     * 排序
     **/
    @Excel(name = "报告排序", width = 15)
    @ApiModelProperty(value = "报告排序")
    private Long sequence;

    /**
     * 编制单位
     **/
    @Excel(name = "编制单位", width = 15)
    @ApiModelProperty(value = "编制单位", example = "市应急管理局")
    private String createCompany;

    /**
     * 文件名
     */
    @Excel(name = "文件名", width = 15)
    @ApiModelProperty(value = "文件名", example = "文件12")
    private String fileName;

    /**
     * 文件路径
     **/
    @Excel(name = "文件路径", width = 15)
    @ApiModelProperty(value = "文件路径")
    private String filePath;


    /**
     * 是否 删除
     **/
    @Excel(name = "删除状态", width = 15)
    @ApiModelProperty(value = "删除状态")
    private Integer isDelete;


    /**
     * 创建时间
     **/
    @Excel(name = "创建时间", width = 20, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;


    /**
     * 创建人
     **/
    @Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人", example = "admin")
    private String creater;

    /**
     * 更新时间
     **/
    @Excel(name = "最新更新时间", width = 20, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最新更新时间")
    private Date updateTime;


    /**
     * 修改人
     **/
    @Excel(name = "修改人", width = 15)
    @ApiModelProperty(value = "修改人")
    private String updater;


}
