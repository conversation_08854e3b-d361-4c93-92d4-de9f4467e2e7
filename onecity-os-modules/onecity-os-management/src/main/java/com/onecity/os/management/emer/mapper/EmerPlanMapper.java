package com.onecity.os.management.emer.mapper;

import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.management.emer.entity.EmerPlan;
import com.onecity.os.management.emer.entity.EmerPlanPageReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

@Mapper
public interface EmerPlanMapper extends BaseMapper<EmerPlan> {

//    @Update({"<script>",
//            "UPDATE", "emer_plan  SET is_delete=1, update_time=NOW(), updater=#{name}", "WHERE id IN",
//            "<foreach item='id' collection='ids'", "open='(' separator=',' close=')'>",
//            "#{id}",
//            "</foreach>",
//            "</script>"})
    void deleteByIdss(@Param("ids") List<String> ids,@Param("name") String name);

    List<EmerPlan> getListByPage(@Param("req") EmerPlanPageReq req);

    List<Long> getIdsBySequence(@Param("id") String id, @Param("sequence") Integer sequence);

    void updateEmerPlanById(@Param("id")String id, @Param("name")String name, @Param("type") Integer type, @Param("sequence")Integer sequence, @Param("state")String state,
                               @Param("createCompany")String createCompany, @Param("effectDate")Date effectDate, @Param("fileName")String fileName, @Param("filePath")String filePath, @Param("isDelete")Integer isDelete,
                               @Param("creater")String creater, @Param("createTime")Date createTime, @Param("updater")String updater, @Param("updateTime")Date updateTime);

    Integer insertEmerPlan(@Param("id")String id, @Param("name")String name, @Param("type")Integer type, @Param("sequence")Integer sequence,@Param("state")String state,
                           @Param("createCompany")String createCompany,@Param("effectDate")Date effectDate, @Param("fileName")String fileName, @Param("filePath")String filePath,
                           @Param("creater")String creater, @Param("createTime")Date createTime, @Param("updater")String updater, @Param("updateTime")Date updateTime);
}
