package com.onecity.os.management.emer.mapper;

import com.onecity.os.management.emer.entity.EmerRanksIndicator;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;
@Mapper
public interface EmerRanksDataMapper{

    /**
     * 根据指标ids,删除指标
     *
     * @param ids
     */
    @Update({"<script>",
            "UPDATE", "emer_ranks  SET is_delete=1, update_time=NOW(), updater=#{userName}", "WHERE id IN",
            "<foreach item='id' collection='ids'", "open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"})
    void deleteRanks(@Param("ids") String[] ids, @Param("userName") String userName);


    List<EmerRanksIndicator> selectEmerRanksPage(@Param("contacts")String contacts,
                                                 @Param("name")String name, @Param("district")String district, @Param("type")Integer type);
}
