package com.onecity.os.management.emer.mapper;

import com.onecity.os.management.emer.entity.EmerReportIndicator;
import com.onecity.os.management.emer.entity.vo.EmerReportIndicatorVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.common.BaseMapper;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;
@Mapper
public interface EmerReportMapper extends BaseMapper<EmerReportIndicator> {
    @Update({"<script>",
            "UPDATE", "emer_report SET is_delete=1, update_time=NOW(), updater=#{userName}", "WHERE id IN",
            "<foreach item='id' collection='ids'", "open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"})
    void deleteTargets(@Param("ids") String[] ids, @Param("userName") String userName);

    @Select(" SELECT id, name, type, sequence,create_company, file_name, file_path, is_delete, create_time,creater,update_time, updater" +
            " from emer_report  WHERE id = #{id} order by sequence , create_time desc")
    EmerReportIndicator getTargets(@Param("id") String id);

    @Select("SELECT id, name, type, sequence,create_company, file_path, is_delete, create_time,creater,update_time, updater " +
            "FROM  eme" +
            "" +
            "r_report  WHERE " +
            "name LIKE   concat('%', #{vo.name}, '%') " +
            " AND create_company LIKE  concat('%',#{vo.createCompany}, '%') " +
            "AND type LIKE  concat('%',#{vo.type}, '%') " +
            "AND is_delete = 0" )
    List<EmerReportIndicator> getListTargets(@Param("vo") EmerReportIndicatorVo vo);

    List<String> getIdsBySequence(@Param("id") String id,@Param("sequence") Long sequence);

    List<EmerReportIndicator> selectList(@Param("name")String name, @Param("createCompany")String createCompany, @Param("type")String type);

    Integer insertEmerReport(@Param("id")String id, @Param("name")String name, @Param("type")String type, @Param("sequence")Long sequence,
                             @Param("createCompany")String createCompany, @Param("fileName")String fileName, @Param("filePath")String filePath,
                             @Param("creater")String creater, @Param("createTime")Date createTime, @Param("updater")String updater, @Param("updateTime")Date updateTime);

    Integer updateEmerReportById(@Param("id")String id, @Param("name")String name, @Param("type")String type, @Param("sequence")Long sequence,
                                 @Param("createCompany")String createCompany, @Param("fileName")String fileName, @Param("filePath")String filePath,@Param("isDelete")Integer isDelete,
                                 @Param("creater")String creater, @Param("createTime")Date createTime, @Param("updater")String updater, @Param("updateTime")Date updateTime);
}
