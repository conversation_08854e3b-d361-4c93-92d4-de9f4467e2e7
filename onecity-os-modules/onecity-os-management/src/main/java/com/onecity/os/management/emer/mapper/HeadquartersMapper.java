package com.onecity.os.management.emer.mapper;

import com.onecity.os.management.emer.model.entiy.GetHeadquarters;
import com.onecity.os.management.emer.model.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface HeadquartersMapper {
    /**
     * 指挥部管理新增
     *
     * @param param
     * @return
     */
    Integer insertHeadquarters(HeadquartersParam param);

    /**
     * 指挥部人员新增
     *
     * @param param
     * @return
     */
    Integer insertHeadquartersUser(HeadquartersUserParam param);

    /**
     * 根据指挥部名称/指挥长名称查询
     *
     * @param hqtName
     * @param userName
     * @return
     */
    List<GetHeadquarters> getHeadquarters(@Param("hqtName") String hqtName, @Param("userName") String userName);

    /**
     * 根据id删除指挥部
     *
     * @param id
     * @return
     */
    Integer deleteHeadquarters(@Param("id") Long id);

//     /**
//      * 修改指挥部
//      * @param updateHeadquartersParam
//      * @return
//      */
    //  Integer updateHeadquarters(UpdateHeadquartersParam updateHeadquartersParam);


    Integer getIdExits(Long id);

    Integer updateDepartment(HeadquartersParam param);

    Integer updateUser(HeadquartersUserParam param);


    Integer deleteDepartmentById(Long id);

    /**
     * 根据指挥部id,查找指挥部信息
     *
     * @param id
     * @return
     */
    GetHeadquarterByIdDto getHeadquarterById(@Param("id") Long id);

    /**
     * 根据指挥部id,查找人员
     *
     * @param id
     * @param station
     * @return
     */
    List<EmerUsersDto> getEmerUsersByDepartId(@Param("id") Long id, @Param("station") String station);

    /**
     * 根据指挥部id,指挥部类型,查找岗位
     *
     * @param id
     * @param type
     * @return
     */
    List<String> getStation(@Param("id") Long id, @Param("type") Integer type);

    /**
     * 根据指挥部id,删除人员信息
     *
     * @param departIdReceive
     * @param userName
     */
    void deleteEmerUserByDepartId(@Param("departIdReceive") Long departIdReceive, @Param("userName") String userName);

    /**
     * 根据指挥部id,职称查找指挥部下的人员
     *
     * @param id
     * @param station
     * @return
     */
    String getEmerZhiHuiUsersByDepartIdAndStation(@Param("id") Long id, @Param("station") String station);

    Integer countSequences(@Param("id") String id,@Param("sequence")Integer sequence);
}



