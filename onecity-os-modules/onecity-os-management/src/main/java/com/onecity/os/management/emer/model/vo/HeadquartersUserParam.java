package com.onecity.os.management.emer.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.Date;

@Data
public class HeadquartersUserParam {

    private Long id;

    @NotEmpty(message = "name 不能为空")
    private String name;

    private Long departId;

//    @NotEmpty(message = "duty 不能为空")
    private String duty;

    @NotEmpty(message = "station 不能为空")
    private String station;

//    @NotEmpty(message = "phone 不能为空")
//    @Pattern(regexp = "^((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(18[0,5-9]))\\\\\\\\d{8}$")
    private String phone;

    private Integer isDelete;

    private String creater;

    @JsonFormat(shape=JsonFormat.Shape.STRING,pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    private String updater;

    @JsonFormat(shape=JsonFormat.Shape.STRING,pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;


}
