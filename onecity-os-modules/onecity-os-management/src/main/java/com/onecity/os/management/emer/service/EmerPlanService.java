package com.onecity.os.management.emer.service;
import com.onecity.os.management.emer.entity.EmerPlan;
import com.onecity.os.management.emer.entity.EmerPlanPageReq;

import java.util.List;

public interface EmerPlanService{


    void deleteByIds(List<String> ids,String name);

    /**
     * 分页查询企业标签列表
     * @param req
     * @return
     */
    List<EmerPlan> getListByPage(EmerPlanPageReq req);

    Boolean isSequenceExist(String id,Integer sequence);

    void updateById(EmerPlan emerPlan);

    Integer save(EmerPlan emerPlan);
}
