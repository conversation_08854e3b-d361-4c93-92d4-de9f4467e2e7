package com.onecity.os.management.emer.service;

import com.onecity.os.management.emer.entity.EmerReportIndicator;
import com.onecity.os.management.emer.entity.vo.EmerReportIndicatorVo;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/29
 */
@Service
public interface EmerReportService {
    void deleteTargets(String ids,String name);

    EmerReportIndicator getTargets(String ids);

    List<EmerReportIndicator> getListTargets(EmerReportIndicatorVo vo);

    Boolean isSequenceExist(String id, Long sequence);

    Integer updateById(EmerReportIndicator detail);

    Integer save(EmerReportIndicator emerReportIndicator);

    List<EmerReportIndicator> selectList(String name, String createCompany, String type);
}
