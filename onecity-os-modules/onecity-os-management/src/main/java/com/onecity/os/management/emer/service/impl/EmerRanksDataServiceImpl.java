package com.onecity.os.management.emer.service.impl;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.management.emer.entity.EmerRanksIndicator;
import com.onecity.os.management.emer.mapper.EmerRanksDataMapper;
import com.onecity.os.management.emer.service.EmerRanksDataService;
import com.onecity.os.system.api.RemoteUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 指标详情
 *
 * <AUTHOR>
 * @date 2020/6/3 11:20
 */
@Service
public class EmerRanksDataServiceImpl implements EmerRanksDataService {

    @Resource
    private EmerRanksDataMapper emerRanksDataMapper;

    @Autowired
    private RemoteUserService remoteUserService;
    /**
     * 删除
     *
     * @param ids
     */
    @Override
    public void deleteRanks(String ids,String name) {
        emerRanksDataMapper.deleteRanks(ids.split(","),name);
    }

    @Override
    public List<EmerRanksIndicator> selectEmerRanksPage(String contacts, String name, String district, Integer type) {
        List<EmerRanksIndicator> emerRanksIndicators = emerRanksDataMapper.selectEmerRanksPage(contacts,name,district,type);
        //更新人匹配
        emerRanksIndicators.stream().filter(dto -> StringUtils.isNotEmpty(dto.getCreater())).forEach(dto -> {
            BaseResult<String> nickName = remoteUserService.getNickNameByName(dto.getCreater());
            dto.setCreater(nickName.getData());
        });
        return emerRanksIndicators;
    }
}
