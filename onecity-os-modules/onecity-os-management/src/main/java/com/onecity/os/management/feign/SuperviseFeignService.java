package com.onecity.os.management.feign;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.management.feign.fallback.SuperviseFeignServiceFallbackFactory;
import com.onecity.os.management.feign.fallback.SystemFeignServiceFallbackFactory;
import com.onecity.os.system.api.domain.SysDept;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 */
@FeignClient(name = "onecity-os-supervise", fallbackFactory = SuperviseFeignServiceFallbackFactory.class, decode404 = true)
public interface SuperviseFeignService {

    @GetMapping("/supervise/queryByIndicator")
    BaseResult queryByIndicator(@RequestParam(name = "creator") String creator,@RequestParam(name = "indicatorId") String indicatorId);

    @GetMapping("/remind/queryByIndicator")
    BaseResult queryRemindByIndicator(@RequestParam(name = "creator") String creator,@RequestParam(name = "indicatorId") String indicatorId);

    @GetMapping("/supervise/cancelIndicatorHistory")
    BaseResult cancelIndicatorHistory(@RequestParam(name = "indicatorIds") List<String> indicatorIds);

    @GetMapping("/remind/cancelIndicatorHistory")
    BaseResult cancelRemindIndicatorHistory(@RequestParam(name = "indicatorIds") List<String> indicatorIds);
}
