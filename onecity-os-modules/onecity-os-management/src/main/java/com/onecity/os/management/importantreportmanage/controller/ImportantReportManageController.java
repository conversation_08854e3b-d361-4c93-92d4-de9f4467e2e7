package com.onecity.os.management.importantreportmanage.controller;

import com.onecity.os.common.core.constant.Constant;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.management.api.vo.PageResult;
import com.onecity.os.management.importantreportmanage.model.dto.GetImportantReportAuditListByIdDto;
import com.onecity.os.management.importantreportmanage.model.dto.GetImportantReportReceiveUsersByIdDto;
import com.onecity.os.management.importantreportmanage.model.entity.ImportantReportManage;
import com.onecity.os.management.importantreportmanage.service.ImportantReportManageService;
import com.onecity.os.system.api.domain.SysUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

/**
 * 要情专报管理
 *
 * <AUTHOR>
 * @date 2021/4/7 上午8:52
 */
@Slf4j
@RestController
@RequestMapping("/importReportManage")
@Api(tags = "要情专报管理")
public class ImportantReportManageController extends BaseController {

    @Resource
    private ImportantReportManageService importReportManageService;

    /**
     * 根据专报主题和发布时间查看要情专报列表(分页)
     *
     * @param title
     * @param publicDate
     * @return
     */
    @ApiOperation(value = "要情专报管理-查询分页列表")
    @GetMapping("/getImportantReportManagePageList")
    public TableDataInfo getImportantReportManagePageList(@RequestParam(name = "title", required = false) String title,
                                                           @RequestParam(name = "publicDate", required = false) String publicDate,
                                                           @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
                                                           @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        startPage();
        List<ImportantReportManage> pageList = importReportManageService.getImportantReportManagePageList(title, publicDate);
        return getDataTable(pageList, true);
    }

    /**
     * 根据专报id查看审批列表
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "要情专报管理-根据专报id查看审批列表")
    @GetMapping("/getImportantReportAuditListById")
    public BaseResult getImportantReportAuditListById(@RequestParam(name = "id") Long id) {
        List<GetImportantReportAuditListByIdDto> lists = importReportManageService.getImportantReportAuditListById(id);
        return BaseResult.ok(lists);
    }

    /**
     * 根据专报id,修改状态(发送,撤回)
     *
     * @param vo
     * @return
     */
    @Log(title = "根据专报id,修改状态(发送,撤回)", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "根据专报id,修改状态")
    @PostMapping("/updateImportantReportStatusById")
    public BaseResult updateImportantReportStatusById(@RequestBody ImportantReportManage vo) throws Exception {
        if (ObjectUtils.isEmpty(vo.getId())) {
            return BaseResult.fail("id不能为空");
        }
        if (ObjectUtils.isEmpty(vo.getStatus())) {
            return BaseResult.fail("状态不能为空");
        }
        if (ObjectUtils.isEmpty(vo.getHost())) {
            return BaseResult.fail("ip地址不能为空");
        }
        if (StringUtils.isNotEmpty(vo.getContent()) && vo.getContent().length() > Constant.CONTEST_MAX) {
            return BaseResult.fail("无效格式内容过多，请减少后再操作");
        }
        // 获取请求地址host和端口
        String host = vo.getHost();
        try {
            int res = importReportManageService.updateImportantReportStatusById(vo.getId(), vo.getStatus(), host);
            if (0 >= res) {
                if (1 == vo.getStatus()) {
                    return BaseResult.fail("发送失败");
                }
                if (2 == vo.getStatus()) {
                    if (-1 == res) {
                        return BaseResult.fail("该要情专报已批示，不能撤回");
                    }
                    return BaseResult.fail("撤回失败");
                }
            }
            if (1 == vo.getStatus()) {
                return BaseResult.ok("发送成功");
            }
            if (2 == vo.getStatus()) {
                return BaseResult.ok("撤回成功");
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("修改要情专报状态失败");
        }
    }

    /**
     * 根据专报id,删除专报
     *
     * @param id
     * @return
     */
    @Log(title = "根据专报id,删除专报", businessType = BusinessType.DELETE)
    @ApiOperation(value = "根据专报id,删除专报")
    @DeleteMapping("/deleteImportantReportById")
    public BaseResult deleteImportantReportById(@RequestParam(name = "id") Long id) {
        int res = importReportManageService.deleteImportantReportById(id);
        if (0 >= res) {
            return BaseResult.fail("删除失败");
        }
        return BaseResult.ok("删除成功");
    }

    /**
     * 新增/修改要情专报
     *
     * @param vo
     * @return
     */
    @Log(title = "新增/修改要情专报", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增/修改要情专报")
    @PostMapping("/saveImportantReport")
    public BaseResult addImportantReport(@RequestBody ImportantReportManage vo) throws Exception {
        if (StringUtils.isEmpty(vo.getTitle())) {
            return BaseResult.fail("专报主题不能为空");
        }
        if (StringUtils.isEmpty(vo.getPublicSource())) {
            return BaseResult.fail("来源单位不能为空");
        }
        if (ObjectUtils.isEmpty(vo.getHost())) {
            return BaseResult.fail("ip地址不能为空");
        }
        if (StringUtils.isNotEmpty(vo.getContent()) && vo.getContent().length() > Constant.CONTEST_MAX) {
            return BaseResult.fail("无效格式内容过多，请减少后再操作");
        }
        // 获取请求地址host和端口
        String host = vo.getHost();
        // 判断已发布的不能修改
        if (ObjectUtils.isNotEmpty(vo.getId())) {
            // 根据id,查找通知公告状态
            Byte status = importReportManageService.getStatusById(vo.getId());
            if (1 == status) {
                return BaseResult.fail("已发送的要情专报不能修改");
            }
            // 已撤回的,保存后,还是已撤回状态
            if (2 == status && 1 != vo.getStatus()) {
                vo.setStatus((byte) 2);
            }
        }
        int res = importReportManageService.saveImportantReport(vo, host);
        if (0 >= res) {
            return BaseResult.fail("保存失败");
        }
        return BaseResult.ok("保存成功");
    }

    /**
     * 根据专报id,查看已发送的人员列表
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据专报id,查看已发送的人员列表")
    @GetMapping("/getImportantReportReceiveUsersById")
    public BaseResult<List<SysUser>> getImportantReportReceiveUsersById(@RequestParam(name = "id") Long id) {
        return importReportManageService.getImportantReportReceiveUsersById(id);
    }

    /**
     * 根据专报id,查看详细信息
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据专报id,查看详细信息")
    @GetMapping("/getImportantReportInfoById")
    public BaseResult getImportantReportInfoById(@RequestParam(name = "id") Long id) {
        ImportantReportManage dto = importReportManageService.getImportantReportInfoById(id);
        return BaseResult.ok(dto, true);
    }

}





























