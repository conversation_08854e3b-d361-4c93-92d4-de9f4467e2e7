package com.onecity.os.management.importantreportmanage.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 根据专报id查看审批列表出参
 *
 * <AUTHOR>
 * @date 2021/4/7 上午9:11
 */
@Data
public class GetImportantReportAuditListByIdDto {
    /**
     * 审批人
     */
    private String auditUserName;

    /**
     * 批示时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /**
     * 批示意见
     */
    private String auditView;
}
