package com.onecity.os.management.importantreportmanage.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import javax.persistence.*;

@Data
@Table(name = "important_report_manage")
public class ImportantReportManage {
    /**
     * 主键id
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Long id;

    /**
     * ip地址
     */
    @Transient
    private String host;

    /**
     * 标题
     */
    private String title;

    /**
     * 发布来源
     */
    @Column(name = "public_source")
    private String publicSource;

    /**
     * 发送时间
     */
    @Column(name = "send_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 文件名
     */
    @Column(name = "file_name")
    private String fileName;

    /**
     * 文件路径
     */
    @Column(name = "file_path")
    private String filePath;

    /**
     * 状态(0-草稿;1-已发布;2已撤回)
     */
    private Byte status;

    /**
     * 是否已读 0-否;1-是
     */
    @Column(name = "is_read")
    private Byte isRead;

    /**
     * 阅读次数
     */
    @Column(name = "read_count")
    private Integer readCount;

    /**
     * 是否删除 0-否;1-是
     */
    @Column(name = "is_delete")
    private Byte isDelete;

    /**
     * 创建人
     */
    private String creater;

    @Column(name = "create_id")
    private String createId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 接收人ids
     */
    @Column(name = "receive_user_id")
    private String receiveUserId;

    /**
     * 批示人的userIds
     */
    @Column(name = "audit_user_ids")
    private String auditUserIds;

    /**
     * 专报内容
     */
    private String content;

    /**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取标题
     *
     * @return title - 标题
     */
    public String getTitle() {
        return title;
    }

    /**
     * 设置标题
     *
     * @param title 标题
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 获取发布来源
     *
     * @return public_source - 发布来源
     */
    public String getPublicSource() {
        return publicSource;
    }

    /**
     * 设置发布来源
     *
     * @param publicSource 发布来源
     */
    public void setPublicSource(String publicSource) {
        this.publicSource = publicSource;
    }

    /**
     * 获取文件名
     *
     * @return file_name - 文件名
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * 设置文件名
     *
     * @param fileName 文件名
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * 获取文件路径
     *
     * @return file_path - 文件路径
     */
    public String getFilePath() {
        return filePath;
    }

    /**
     * 设置文件路径
     *
     * @param filePath 文件路径
     */
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    /**
     * 获取状态(0-草稿;1-已发布;2已撤回)
     *
     * @return status - 状态(0-草稿;1-已发布;2已撤回)
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置状态(0-草稿;1-已发布;2已撤回)
     *
     * @param status 状态(0-草稿;1-已发布;2已撤回)
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * 获取是否已读 0-否;1-是
     *
     * @return is_read - 是否已读 0-否;1-是
     */
    public Byte getIsRead() {
        return isRead;
    }

    /**
     * 设置是否已读 0-否;1-是
     *
     * @param isRead 是否已读 0-否;1-是
     */
    public void setIsRead(Byte isRead) {
        this.isRead = isRead;
    }

    /**
     * 获取阅读次数
     *
     * @return read_count - 阅读次数
     */
    public Integer getReadCount() {
        return readCount;
    }

    /**
     * 设置阅读次数
     *
     * @param readCount 阅读次数
     */
    public void setReadCount(Integer readCount) {
        this.readCount = readCount;
    }

    /**
     * 获取是否删除 0-否;1-是
     *
     * @return is_delete - 是否删除 0-否;1-是
     */
    public Byte getIsDelete() {
        return isDelete;
    }

    /**
     * 设置是否删除 0-否;1-是
     *
     * @param isDelete 是否删除 0-否;1-是
     */
    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * 获取创建人
     *
     * @return creater - 创建人
     */
    public String getCreater() {
        return creater;
    }

    /**
     * 设置创建人
     *
     * @param creater 创建人
     */
    public void setCreater(String creater) {
        this.creater = creater;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改人
     *
     * @return updater - 修改人
     */
    public String getUpdater() {
        return updater;
    }

    /**
     * 设置修改人
     *
     * @param updater 修改人
     */
    public void setUpdater(String updater) {
        this.updater = updater;
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取接收人ids
     *
     * @return receive_user_id - 接收人ids
     */
    public String getReceiveUserId() {
        return receiveUserId;
    }

    /**
     * 设置接收人ids
     *
     * @param receiveUserId 接收人ids
     */
    public void setReceiveUserId(String receiveUserId) {
        this.receiveUserId = receiveUserId;
    }

    /**
     * 获取专报内容
     *
     * @return content - 专报内容
     */
    public String getContent() {
        return content;
    }

    /**
     * 设置专报内容
     *
     * @param content 专报内容
     */
    public void setContent(String content) {
        this.content = content;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }
}