package com.onecity.os.management.importantreportmanage.service;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.management.api.vo.PageResult;
import com.onecity.os.management.importantreportmanage.model.dto.GetImportantReportAuditListByIdDto;
import com.onecity.os.management.importantreportmanage.model.dto.GetImportantReportReceiveUsersByIdDto;
import com.onecity.os.management.importantreportmanage.model.entity.DisposalNoticeVO;
import com.onecity.os.management.importantreportmanage.model.entity.ImportantReportManage;
import com.onecity.os.system.api.domain.SysUser;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/7 上午8:53
 */
public interface ImportantReportManageService {
    /**
     * 根据专报主题和发布时间查看要情专报列表(分页)
     *
     * @param title
     * @param publicDate
     * @return
     */
    List<ImportantReportManage> getImportantReportManagePageList(String title, String publicDate);

    /**
     * 根据专报id查看审批列表
     *
     * @param id
     * @return
     */
    List<GetImportantReportAuditListByIdDto> getImportantReportAuditListById(Long id);

    /**
     * 根据专报id,修改状态(发送,撤回)
     *
     * @param id
     * @param status
     * @param host
     * @return
     */
    int updateImportantReportStatusById(Long id, Byte status, String host);

    /**
     * 根据专报id,删除专报
     *
     * @param id
     * @return
     */
    int deleteImportantReportById(Long id);

    /**
     * 新增/修改要情专报
     *
     * @param vo
     * @param host
     * @return
     */
    int saveImportantReport(ImportantReportManage vo, String host) throws Exception;

    /**
     * 根据专报id,查看已发送的人员列表
     *
     * @param id
     * @return
     */
    BaseResult<List<SysUser>> getImportantReportReceiveUsersById(Long id);

    /**
     * 根据id,查找状态
     *
     * @param id
     * @return
     */
    Byte getStatusById(Long id);

    /**
     * 根据专报id,查看详细信息
     *
     * @param id
     * @return
     */
    ImportantReportManage getImportantReportInfoById(Long id);

    Boolean sendDisposalNotice(DisposalNoticeVO disposalNoticeVO);

    Boolean sendSuperviseNote(DisposalNoticeVO disposalNoticeVO);
}
















