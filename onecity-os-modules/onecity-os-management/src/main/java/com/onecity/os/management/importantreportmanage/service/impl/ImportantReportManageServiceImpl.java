package com.onecity.os.management.importantreportmanage.service.impl;

import com.alibaba.fastjson.JSON;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.management.dingding.utils.AuthHelper;
import com.onecity.os.management.dingding.utils.DingEVN;
import com.onecity.os.management.importantreportmanage.mapper.ImportantReportManageMapper;
import com.onecity.os.management.importantreportmanage.model.dto.GetImportantReportAuditListByIdDto;
import com.onecity.os.management.importantreportmanage.model.dto.GetImportantReportReceiveUsersByIdDto;
import com.onecity.os.management.importantreportmanage.model.entity.DisposalNoticeVO;
import com.onecity.os.management.importantreportmanage.model.entity.ImportantReportManage;
import com.onecity.os.management.importantreportmanage.service.ImportantReportManageService;
import com.onecity.os.management.utils.DateUtils;
import com.onecity.os.management.zhibiao.feign.MessageFeignService;
import com.onecity.os.management.zhibiao.model.dto.RemindInfoDto;
import com.onecity.os.system.api.RemoteUserService;
import com.onecity.os.system.api.domain.SysUser;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/4/7 上午8:53
 */
@Slf4j
@Service
public class ImportantReportManageServiceImpl implements ImportantReportManageService {

    @Value("${dingding.baseMessageUrl}")
    private String baseMessageUrl;

    @Resource
    private ImportantReportManageMapper importReportManageMapper;

    @Resource
    private MessageFeignService messageFeignService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Override
    public List<ImportantReportManage> getImportantReportManagePageList(String title, String publicDate) {
        List<ImportantReportManage> page1 = importReportManageMapper.getImportantReportManagePageList(title, publicDate);
        return page1;
    }

    @Override
    public List<GetImportantReportAuditListByIdDto> getImportantReportAuditListById(Long id) {
        return importReportManageMapper.getImportantReportAuditListById(id);
    }

    @Override
    public int updateImportantReportStatusById(Long id, Byte status, String host) {
        // 当前用户信息
        String loginName = "";
        LoginUser sysUser = SecurityUtils.getLoginUser();
        if (ObjectUtils.isNotEmpty(sysUser)) {
            loginName = sysUser.getSysUser().getNickName();
        }
        //获取accessToken
        String accessToken = null;
        try {
            accessToken = AuthHelper.getAccessToken(DingEVN.AppKey, DingEVN.AppSecret);
            log.info("获取accessToken成功, --> {}", accessToken);
        } catch (Exception e) {
            log.error("获取accessToken失败! --> {} ", e);
        }
        ImportantReportManage manage = importReportManageMapper.getInfoById(id);
        // 发布的话,发送钉钉消息
        if (1 == status) {
            // 将批示内容删除
            importReportManageMapper.deleteReportAuditByReportId(id);
            // 将要情专报表批示人字段清空
            importReportManageMapper.updateAuditUserIdById(id);
            // 发送发布消息
            if (ObjectUtils.isNotEmpty(manage)) {
                //APP发送通知消息
                extracted(String.valueOf(id),"要情专报 新专报提请", "您有新的专报提请--" + manage.getTitle() + "，请查收。", loginName, manage.getReceiveUserId(), false);
            }
            // 修改状态
            importReportManageMapper.updateImportantReportStatusById(id, status, loginName);
            return 1;
        }
        // 撤回
        if (2 == status) {
            // 已经批示的,不能撤回
            Long idIsAudit = importReportManageMapper.getIsAuditById(id);
            if (ObjectUtils.isNotEmpty(idIsAudit)) {
                return -1;
            }
            // 发送发布消息
            if (ObjectUtils.isNotEmpty(manage)) {
                //APP发送通知消息
                extracted(String.valueOf(id), "要情专报 专报撤回",  "您收到的专报提请--" + manage.getTitle() + "，已撤回。", loginName, manage.getReceiveUserId(), true);
            }
            // 修改状态
            importReportManageMapper.updateImportantReportStatusById(id, status, loginName);
        }
        return 1;
    }

    @Override
    public int deleteImportantReportById(Long id) {
        String loginName = "";
        LoginUser sysUser = SecurityUtils.getLoginUser();
        if (ObjectUtils.isNotEmpty(sysUser)) {
            loginName = sysUser.getUsername();
        }
        return importReportManageMapper.deleteImportantReportById(id, loginName);
    }

    @Override
    public int saveImportantReport(ImportantReportManage vo, String host) throws Exception {
        try {
            String loginName = "";
            String loginUserId = "";
            LoginUser sysUser = SecurityUtils.getLoginUser();
            if (ObjectUtils.isNotEmpty(sysUser)) {
                loginName = sysUser.getSysUser().getNickName();
                loginUserId = sysUser.getUserid().toString();
            }
            vo.setAuditUserIds("");
            // 新增
            if (ObjectUtils.isEmpty(vo.getId())) {
                vo.setCreateId(loginUserId);
                vo.setCreater(loginName);
                vo.setCreateTime(new Date());
                vo.setUpdater(loginName);
                vo.setUpdateTime(new Date());
                vo.setIsRead((byte) 0);
                vo.setIsDelete((byte) 0);
                // 发送的话,设置发送时间
                if (1 == vo.getStatus()) {
                    vo.setSendTime(new Date());
                    importReportManageMapper.insert(vo);
                    List<ImportantReportManage> reportManagePageList = importReportManageMapper.getImportantReportManagePageList(null, null);
                    String id = "";
                    for (ImportantReportManage reportManage : reportManagePageList) {
                        if (reportManage.getCreater().equals(loginName)) {
                            id = String.valueOf(reportManage.getId());
                            break;
                        }
                    }
                    //APP发送通知消息
                    extracted(id, "要情专报 新专报提请", "您有新的专报提请--" + vo.getTitle() + "，请查收。", loginName, vo.getReceiveUserId(), false);
                    return 1;
                } else {
                    return importReportManageMapper.insert(vo);
                }
            } else {
                vo.setUpdater(loginName);
                vo.setUpdateTime(new Date());
                vo.setCreater(null);
                // 发送的话,设置发送时间
                if (1 == vo.getStatus()) {
                    vo.setSendTime(new Date());
                    importReportManageMapper.updateByPrimaryKeySelective(vo);
                    //APP发送通知消息
                    extracted(String.valueOf(vo.getId()),"要情专报 新专报提请", "您有新的专报提请--" + vo.getTitle() + "，请查收。", loginName, vo.getReceiveUserId(), false);
                    return 1;
                } else {
                    vo.setIsRead((byte) 0);
                    vo.setReadCount(0);
                    return importReportManageMapper.updateByPrimaryKeySelective(vo);
                }
            }
        } catch (Exception e) {
            log.error("新增/修改要情专报失败");
            throw new Exception("新增/修改要情专报失败");
        }
    }

    @Override
    public BaseResult<List<SysUser>> getImportantReportReceiveUsersById(Long id) {
        String userIds = importReportManageMapper.getImportantReportReceiveUsersById(id);
        if (StringUtils.isNotEmpty(userIds)) {
            // 查找人名
            return remoteUserService.getUserByPcUserIds(userIds.split(","));
        }
        return BaseResult.ok();
    }

    @Override
    public Byte getStatusById(Long id) {
        return importReportManageMapper.getStatusById(id);
    }

    @Override
    public ImportantReportManage getImportantReportInfoById(Long id) {
        return importReportManageMapper.getImportantReportInfoById(id);
    }

    @Override
    public Boolean sendDisposalNotice(DisposalNoticeVO disposalNoticeVO) {
        String roleIds = disposalNoticeVO.getRoleIds();
        //List<String> userIdList = dingMailUserMapper.selectUserByRoleIds(Arrays.asList(roleIds.split(",")));
        List<String> userIdList = new ArrayList<>();
        StringBuffer stringBuffer = new StringBuffer();
        log.info("**********发送通知给用户：：" + JSON.toJSONString(userIdList));
        if(userIdList.size() == 0 ){
            return false;
        }
        for(String str : userIdList){
            stringBuffer.append(str).append(",");
        }
        stringBuffer.deleteCharAt(stringBuffer.length()-1);
        String accessToken = null;
        try {
            //accessToken = AuthHelper.getAccessToken(DingEVN.AppKey, DingEVN.AppSecret);
            log.info("获取accessToken成功, --> {}", accessToken);
        } catch (Exception e) {
            log.error("获取accessToken失败! --> {} ", e);
        }
//        dingDingManageUtil.sendTianBaoRemindMsg(accessToken, DingEVN.AgentId, stringBuffer.toString(),
//                disposalNoticeVO.getMessage(), "领导批示", disposalNoticeVO.getHost());

        return true;
    }

    @Override
    public Boolean sendSuperviseNote(DisposalNoticeVO disposalNoticeVO) {
        List<String> userIdList = Arrays.asList(disposalNoticeVO.getUserIds().split(","));
        StringBuffer stringBuffer = new StringBuffer();
        log.info("**********发送通知给用户：：" + JSON.toJSONString(userIdList));
        if(userIdList.size() == 0 ){
            return false;
        }
        for(String str : userIdList){
            stringBuffer.append(str).append(",");
        }
        stringBuffer.deleteCharAt(stringBuffer.length()-1);
        String accessToken = null;
        try {
            //accessToken = AuthHelper.getAccessToken(DingEVN.AppKey, DingEVN.AppSecret);
            log.info("获取accessToken成功, --> {}", accessToken);
        } catch (Exception e) {
            log.error("获取accessToken失败! --> {} ", e);
        }
//        dingDingManageUtil.sendTianBaoRemindMsg(accessToken, DingEVN.AgentId, stringBuffer.toString(),
//                disposalNoticeVO.getMessage(), "督办信息", disposalNoticeVO.getHost());

        return true;
    }

    /**
     * APP消息推送
     *
     * @param id         要情专报id
     * @param title      标题
     * @param content    内容
     * @param loginName  用户
     * @param appUserIds 通知人信息
     */
    private void extracted(String id, String title, String content, String loginName, String appUserIds, boolean withdraw) {
        RemindInfoDto remindInfoDto = new RemindInfoDto();
        remindInfoDto.setRemindTitle(title);
        remindInfoDto.setIsRead((byte) 0);
        remindInfoDto.setRemindedType((byte) 2);
        remindInfoDto.setIsDelete((byte) 0);
        remindInfoDto.setCreater(loginName);
        remindInfoDto.setCreateTime(DateUtils.dateTimeIntact());
        remindInfoDto.setRemindContent(content);
        remindInfoDto.setPcUserId(null);
        remindInfoDto.setAppUserId(appUserIds);
        remindInfoDto.setSourceType("APP");
        remindInfoDto.setLevel("3");
        //撤回消息时候不跳转链接
        if (withdraw) {
            remindInfoDto.setMessageUrl(null);
        } else {
            remindInfoDto.setMessageUrl(baseMessageUrl + "yqzb");
        }
        remindInfoDto.setAppMsgType(title.contains("专报撤回") ? "1" : "2");
        remindInfoDto.setServiceId(id);
        remindInfoDto.setServiceType("yqzb");
        messageFeignService.addMsg(Collections.singletonList(remindInfoDto));
    }
}

















