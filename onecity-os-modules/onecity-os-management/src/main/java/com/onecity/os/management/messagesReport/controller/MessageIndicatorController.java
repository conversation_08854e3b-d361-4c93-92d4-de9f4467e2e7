package com.onecity.os.management.messagesReport.controller;

import cn.hutool.core.util.ObjectUtil;
import com.onecity.os.common.core.constant.Constant;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.onecity.os.management.messagesReport.entity.MessageIndicator;
import com.onecity.os.management.messagesReport.entity.MessageIndicatorDetail;
import com.onecity.os.management.messagesReport.mapper.MessageIndMapper;
import com.onecity.os.management.messagesReport.service.MessageIndDataService;
import com.onecity.os.management.messagesReport.service.MessageIndService;
import com.onecity.os.system.api.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 指标详情,指标管理和指标数据控制层
 *
 * <AUTHOR>
 * @date 2020/6/28 11:10
 */
@Slf4j
@RestController
@RequestMapping("/messageIndicators")
@Transactional
@Api(tags = "指标详情/信息简报")
public class MessageIndicatorController extends BaseController {

    @Autowired
    private MessageIndService messageIndService;
    @Resource
    private MessageIndMapper messageIndMapper;
    @Autowired
    private MessageIndDataService messageIndDataService;

    /**
     * 1 指标详情管理-指标树
     */
    @ApiOperation(value = "信息简报-查询信息简报列表")
    @GetMapping("/getTargetList")
    public TableDataInfo getTargetTreeList(
            @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            MessageIndicator messageIndicator) {
        startPage();
        List<MessageIndicator> pageList = messageIndService.getPageList(messageIndicator);
        for (MessageIndicator mess : pageList){
            mess.setNums(messageIndDataService.countByIndicatorId(mess.getId()));
        }
        return getDataTable(pageList);
    }

    /**
     * 2 指标详情管理-新增指标
     *
     * @param
     * @return
     */
    @ApiOperation(value = "信息简报-新增信息简报")
    @Log(title = "信息简报-新增信息简报", businessType = BusinessType.INSERT)
    @PostMapping("/addTarget")
    public BaseResult<?> addTarget(@RequestBody MessageIndicator messageIndicator) {
        if (StringUtils.isEmpty(messageIndicator.getIndicatorName())) {
            return BaseResult.fail("名称不能为空");
        }
        try {
            //当前用户信息
            LoginUser sysUser = SecurityUtils.getLoginUser();
            if (sysUser != null) {
                messageIndicator.setUpdater(sysUser.getUsername());
                messageIndicator.setCreater(sysUser.getUsername());
            }
            Date currentTime = new Date();
            messageIndicator.setCreateTime(currentTime);
            messageIndicator.setUpdateTime(currentTime);
            String id = UUID.randomUUID().toString().replace("-", "");
            messageIndicator.setId(id);
            messageIndicator.setIsDelete(0);
            messageIndicator.setSequence(0);
            messageIndMapper.insert(messageIndicator);
        } catch (Exception e) {
            return BaseResult.fail("添加失败！");
        }
        return BaseResult.ok("添加成功！");
    }

    /**
     * 3 指标详情管理-更新指标
     *
     * @param
     * @return
     */
    @ApiOperation(value = "信息简报-修改信息简报")
    @Log(title = "信息简报-修改信息简报", businessType = BusinessType.UPDATE)
    @PostMapping("/updateTargets")
    public BaseResult<?> updateTargets(@RequestBody MessageIndicator messageIndicator) {
        if (StringUtils.isBlank(messageIndicator.getId())) {
            return BaseResult.fail("指标id不能为空");
        }
        if (StringUtils.isBlank(messageIndicator.getIndicatorName())) {
            return BaseResult.fail("指标名称不能为空");
        }

        try {
            LoginUser sysUser = SecurityUtils.getLoginUser();
            if (sysUser != null) {
                messageIndicator.setUpdater(sysUser.getUsername());
            }
            Date currentTime = new Date();
            MessageIndicator old = messageIndService.getById(messageIndicator.getId());
            messageIndicator.setUpdateTime(currentTime);
            messageIndMapper.updateByPrimaryKeySelective(messageIndicator);
        } catch (Exception e) {
            return BaseResult.fail("更新失败！");
        }
        return BaseResult.ok("更新成功！");
    }

    /**
     * 3 指标详情管理-删除指标
     *
     * @param
     * @return
     */
    @ApiOperation(value = "信息简报-删除信息简报")
    @Log(title = "信息简报-删除信息简报", businessType = BusinessType.DELETE)
    @PostMapping("/deleteTargets")
    public BaseResult<?> deleteTargets(@RequestParam(name = "ids", required = true) String ids) {
        if (StringUtils.isBlank(ids)) {
            return BaseResult.fail("id为null");
        }
        try {
            //当前用户信息
            String name = "";
            LoginUser sysUser = SecurityUtils.getLoginUser();
            if (sysUser != null) {
                name = sysUser.getUsername();
            }
            messageIndService.deleteTargets(ids, name);
        } catch (Exception e) {
            return BaseResult.fail("删除失败！");
        }
        return BaseResult.ok("删除成功！");
    }
    /**
     * 3 指标详情管理-置顶指标
     *
     * @param
     * @return
     */
    @ApiOperation(value = "信息简报-置顶/取消置顶")
    @Log(title = "信息简报-置顶/取消置顶", businessType = BusinessType.OTHER)
    @PostMapping("/topTarget")
    public BaseResult<?> topTarget(@RequestParam(name = "id", required = true) String id, @RequestParam(name = "top", required = true) int top) {
        if (StringUtils.isBlank(id)) {
            return BaseResult.fail("id为null");
        }
        if (top>1) {
            return BaseResult.fail("参数不正确："+top);
        }
        MessageIndicator messageIndicator = messageIndService.getById(id);
        //更新时间不变
        try {
            LoginUser sysUser = SecurityUtils.getLoginUser();
            if (sysUser != null) {
                messageIndicator.setUpdater(sysUser.getUsername());
            }
            Date currentTime = new Date();
            messageIndicator.setUpdateTime(currentTime);
            if (1 == top) {
                messageIndicator.setTopUpdateTime(currentTime);
            }
            if (0 == top) {
                messageIndicator.setTopUpdateTime(null);
            }
            messageIndicator.setId(id);
            messageIndicator.setSequence(top);
            //messageIndService.updateById(messageIndicator);
            messageIndService.updateByPram(messageIndicator);
        } catch (Exception e) {
            log.info(" error message : " + e.getMessage());
            return BaseResult.fail("更新失败！");
        }
        return BaseResult.ok("操作成功！");
    }
    //##===========================================================================##//

    /**
     * 1 指标详情-查询列表
     *
     * @param
     * @return
     */
    @ApiOperation(value = "信息简报-详情页-查询列表")
    @GetMapping("/getDetailList")
    public TableDataInfo getDetailList(@RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req, MessageIndicatorDetail messageIndicatorDetail) {
         startPage();
         List<MessageIndicatorDetail> pageList = messageIndDataService.getPageList(messageIndicatorDetail);
        return getDataTable(pageList, true);
    }

    /**
     * 2 指标详情-修改
     *
     * @param detail
     * @return
     */
    @ApiOperation(value = "信息简报-指标详情-修改")
    @Log(title = "信息简报-详情页-修改", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/updateDetail")
    public BaseResult<?> updateDetail(@RequestBody MessageIndicatorDetail detail) {
        try {
            if (StringUtils.isNotEmpty(detail.getContent()) && detail.getContent().length() > Constant.CONTEST_MAX) {
                return BaseResult.fail("无效格式内容过多，请减少后再操作");
            }
            // 获取当前登录的用户信息
            LoginUser sysUser = SecurityUtils.getLoginUser();
            String userName = null;
            if (ObjectUtil.isNotNull(sysUser)) {
                userName = sysUser.getUsername();
            }
            Date currentTime = new Date();
            detail.setUpdateTime(currentTime);

            detail.setUpdater(userName);
            messageIndDataService.updateById(detail);
            messageIndService.updateIndicatorTimeById(detail);
        } catch (Exception e) {
            return BaseResult.fail("修改失败！");
        }
        return BaseResult.ok("修改成功！");
    }

    /**
     * 3 指标详情-新增
     *
     * @param detail
     * @return
     */
    @ApiOperation(value = "信息简报-详情页-新增")
    @Log(title = "信息简报-详情页-新增", businessType = BusinessType.INSERT)
    @PostMapping(value = "/addDetail")
    public BaseResult<?> addDetail(@RequestBody MessageIndicatorDetail detail) {
        if (StringUtils.isEmpty(detail.getIndicatorName())) {
            return BaseResult.fail("名称不能为空");
        }
        if (StringUtils.isNotEmpty(detail.getContent()) && detail.getContent().length() > Constant.CONTEST_MAX) {
            return BaseResult.fail("无效格式内容过多，请减少后再操作");
        }
        try {
            //当前用户信息
            LoginUser sysUser = SecurityUtils.getLoginUser();
            if (sysUser != null) {
                detail.setUpdater(sysUser.getUsername());
                detail.setCreater(sysUser.getUsername());
            }
            Date currentTime = new Date();
            detail.setCreateTime(currentTime);
            detail.setUpdateTime(currentTime);
            String id = UUID.randomUUID().toString().replace("-", "");
            // detail.setId(id);
            detail.setIsDelete(0);
            messageIndDataService.insert(detail);
            messageIndService.updateIndicatorTimeById(detail);
        } catch (Exception e) {
            return BaseResult.fail("添加失败！");
        }
        return BaseResult.ok("新增成功！");
    }

    /**
     * 4 指标详情-删除
     *
     * @param
     * @return
     */
    @ApiOperation(value = "信息简报-详情页-删除")
    @Log(title = "信息简报-详情页-删除", businessType = BusinessType.DELETE)
    @PostMapping(value = "/deleteDetail")
    public BaseResult<?> deleteDetail(@RequestParam(name = "ids", required = true) String ids) {
        if (StringUtils.isBlank(ids)) {
            return BaseResult.fail("id不能为空");
        }
        try {
            //当前用户信息
            String name = "";
            LoginUser sysUser = SecurityUtils.getLoginUser();
            if (sysUser != null) {
                name = sysUser.getUsername();
            }
            messageIndDataService.deleteTargets(ids, name);
            //根据ids 调整找到删除后剩余数据最后一条，更新头上时间
            String[] detailIds = ids.split(",");
            Arrays.stream(detailIds).forEach(detailId -> {
                //根据detailId获取IndicatorID
                MessageIndicatorDetail detail = messageIndDataService.getById(detailId);
                if (null != detail) {
                    messageIndService.delIndicatorTimeById(detail);
                }
            });

        } catch (Exception e) {
            return BaseResult.fail("删除失败！");
        }
        return BaseResult.ok("删除成功！");
    }


}


























