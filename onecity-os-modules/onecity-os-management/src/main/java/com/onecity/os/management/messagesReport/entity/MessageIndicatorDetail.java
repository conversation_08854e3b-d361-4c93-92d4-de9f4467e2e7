package com.onecity.os.management.messagesReport.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 信息指标详情表
 *
 * <AUTHOR>
 * @date 2020/6/10 15:13
 */
@Data
@Table(name = "message_indicator_detail")
public class MessageIndicatorDetail {
    /**
     * 主键自增
     **/
    @Id
    private Integer id;
    /**
     * 指标Id
     **/
    @Column(name = "indicator_id")
    private String indicatorId;
    /**
     * 指标名称
     **/
    @Column(name = "indicator_name")
    private String indicatorName;
    /**
     * 内容
     **/
    @Column(name = "content")
    private String content;
    /**
     * 更新日期
     **/
    @Column(name = "update_date")
    private String updateDate;
    /**
     * 更新人
     **/
    @Column(name = "file_url")
    private String fileUrl;

    /**
     * 更新人
     **/
    @Column(name = "file_name")
    private String fileName;
    /**
     * 是否 删除
     **/
    @Column(name = "is_delete")
    private Integer isDelete;
    /**
     * 创建时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @Column(name = "create_time")
    private Date createTime;
    /**
     * 创建人
     **/
    @Column(name = "creater")
    private String creater;
    /**
     * 更新时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;
    /**
     * 更新人
     **/
    @Column(name = "updater")
    private String updater;



}
