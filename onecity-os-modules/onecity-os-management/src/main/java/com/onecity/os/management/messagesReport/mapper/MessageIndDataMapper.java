package com.onecity.os.management.messagesReport.mapper;

import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.management.messagesReport.entity.MessageIndicatorDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020-6-28
 * @Version V1.0
 **/
@Mapper
public interface MessageIndDataMapper extends BaseMapper<MessageIndicatorDetail> {

    /**
     * 根据指标ids,删除指标
     *
     * @param ids
     */
    @Update({"<script>",
            "UPDATE", "message_indicator_detail  SET is_delete=1, update_time=NOW(), updater=#{userName}", "WHERE id IN",
            "<foreach item='id' collection='ids'", "open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"})
    void deleteTargets(@Param("ids") String[] ids, @Param("userName") String userName);

    /**
     * 根据message_indicator.sourceId 查询当月，message_indicator_detail更新数量
     * @param sourceId
     * @return
     */
    @Select("SELECT COUNT(*) FROM message_indicator_detail AS midt LEFT JOIN message_indicator AS mi ON midt.indicator_id = mi.id " +
            " WHERE mi.is_delete = 0 " +
            " AND midt.is_delete = 0 " +
            " AND midt.update_time >= #{startTime} " +
            " AND midt.update_time <= #{endTime}" +
            " AND mi.source_id = #{sourceId} ")
    int selectMonDetail(@Param("sourceId") String sourceId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     *
     * @param indicatorId
     * @return
     */
    Integer countByIndicatorId(@Param("indicatorId") String indicatorId);

    /**
     *
     * @return
     */
    List<MessageIndicatorDetail> getPageList(@Param("messageIndicatorDetail") MessageIndicatorDetail messageIndicatorDetail);
}
