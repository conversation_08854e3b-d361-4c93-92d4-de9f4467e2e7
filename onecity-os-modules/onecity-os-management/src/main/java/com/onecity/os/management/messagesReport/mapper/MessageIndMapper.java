package com.onecity.os.management.messagesReport.mapper;

import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.management.messagesReport.entity.MessageIndicator;
import com.onecity.os.management.messagesReport.entity.MessageIndicatorDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

@Mapper
public interface MessageIndMapper extends BaseMapper<MessageIndicator> {

    /**
     * 根据指标ids,删除指标
     *
     * @param ids
     */
    @Update({"<script>",
            "UPDATE", "message_indicator SET is_delete=1, update_time=NOW(), updater=#{userName}", "WHERE id IN",
            "<foreach item='id' collection='ids'", "open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"})
     void deleteTargets(@Param("ids") String[] ids, @Param("userName") String userName) ;


    @Select("SELECT * FROM `message_indicator_detail` " +
            "where indicator_id=#{indicator_id} and " +
            " is_delete='0' ORDER BY update_date DESC  LIMIT 1 ")
    MessageIndicatorDetail findMostNewMessageIndicator(@Param("indicator_id") String indicator_id);

    @Update("UPDATE message_indicator set update_time = #{updateDate} " +
            "WHERE id =#{indicator_id}")
    void updateIdsTimeById(@Param("updateDate") Date updateDate, @Param("indicator_id") String indicator_id);

    /**
     *
     * @return
     */
    List<MessageIndicator> getPageList(@Param("messageIndicator") MessageIndicator messageIndicator);

    void updateByPram(@Param("indicator") MessageIndicator messageIndicator);
}
