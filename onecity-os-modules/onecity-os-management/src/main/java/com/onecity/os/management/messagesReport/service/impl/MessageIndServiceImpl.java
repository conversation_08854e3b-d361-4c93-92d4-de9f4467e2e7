package com.onecity.os.management.messagesReport.service.impl;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.management.messagesReport.entity.MessageIndicator;
import com.onecity.os.management.messagesReport.entity.MessageIndicatorDetail;
import com.onecity.os.management.messagesReport.mapper.MessageIndMapper;
import com.onecity.os.management.messagesReport.service.MessageIndService;
import com.onecity.os.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 指标详情
 *
 * <AUTHOR>
 * @date 2020/6/3 11:20
 */
@Service
public class MessageIndServiceImpl implements MessageIndService {

    @Autowired
    private MessageIndMapper indicatorMapper;
    @Autowired
    private RemoteUserService remoteUserService;


    /**
     * 删除
     *
     * @param ids
     */
    @Override
    public void deleteTargets(String ids, String name) {
        indicatorMapper.deleteTargets(ids.split(","), name);
    }

    @Override
    public void updateIndicatorTimeById(MessageIndicatorDetail detail) {

        //获取该专题下最晚的更新日期
//        MessageIndicatorDetail mostNewMessageIndicator = indicatorMapper.findMostNewMessageIndicator(detail.getIndicatorId());
//        Date updateDate = mostNewMessageIndicator == null ? detail.getUpdateTime() : mostNewMessageIndicator.getUpdateTime();

        //跟新专题的日期
        indicatorMapper.updateIdsTimeById(detail.getUpdateTime(),detail.getIndicatorId());
    }

    @Override
    public void delIndicatorTimeById(MessageIndicatorDetail detail) {

        //获取该专题下最晚的更新日期
        MessageIndicatorDetail mostNewMessageIndicator = indicatorMapper.findMostNewMessageIndicator(detail.getIndicatorId());
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date newDate = new Date();
        Date updateDate = mostNewMessageIndicator == null ? newDate : mostNewMessageIndicator.getUpdateTime();

        //跟新专题的日期
        indicatorMapper.updateIdsTimeById(updateDate,detail.getIndicatorId());
    }

    @Override
    public List<MessageIndicator> getPageList(MessageIndicator messageIndicator) {
        List<MessageIndicator> result = indicatorMapper.getPageList(messageIndicator);
        for(MessageIndicator messageIndicator1 : result){
            //查询人名，方便前端显示
            BaseResult<String> cNickName = remoteUserService.getNickNameByName(messageIndicator1.getCreater());
            messageIndicator1.setCreater(cNickName.getData());
            BaseResult<String> uNickName = remoteUserService.getNickNameByName(messageIndicator1.getUpdater());
            messageIndicator1.setUpdater(uNickName.getData());
        }
        return result;
    }

    @Override
    public void insert(MessageIndicator messageIndicator) {
        indicatorMapper.insert(messageIndicator);
    }

    @Override
    public MessageIndicator getById(String id) {
        return indicatorMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateById(MessageIndicator messageIndicator) {
        return indicatorMapper.updateByPrimaryKeySelective(messageIndicator);
    }

    @Override
    public void updateByPram(MessageIndicator messageIndicator) {
        indicatorMapper.updateByPram(messageIndicator);
    }
}























