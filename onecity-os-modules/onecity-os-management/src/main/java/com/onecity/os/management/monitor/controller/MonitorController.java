package com.onecity.os.management.monitor.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.sql.SqlUtil;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.PageDomain;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.core.web.page.TableSupport;
import com.onecity.os.management.monitor.entity.ProjectKeyMonitor;
import com.onecity.os.management.monitor.service.MonitorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 重点项目监控管理层
 *
 * <AUTHOR>
 * @date 2020/11/18
 */
@Slf4j
@RestController
@RequestMapping("/monitor")
@Transactional
@Api(tags = "重点项目监控功能接口")
public class MonitorController extends BaseController {

    @Autowired(required = false)
    private MonitorService monitorService;

    @GetMapping(value = "/getListByPage")
    @ApiOperation(value = "获取重点项目列表")
    public TableDataInfo getListByPage() {

        startPage();
        List<ProjectKeyMonitor> pageList = monitorService.getListByPage();
        return getDataTable(pageList);
    }

    /**
     * 获取视频流地址(调用第三方接口)
     *
     * @param indexCode
     * @param protocol
     * @return
     */
    @GetMapping("/getVideoUrl")
    public BaseResult getVideoUrl(@RequestParam(name = "indexCode") String indexCode,
                                  @RequestParam(name = "protocol") String protocol) throws Exception {
        String videoUrl = monitorService.getVideoUrl(indexCode, protocol);
        if (StringUtils.isEmpty(videoUrl)) {
            return BaseResult.fail("获取视频流地址失败,或者调用三方接口服务失败");
        }
        if ("1".equals(videoUrl)) {
            return BaseResult.fail("获取视频流地址失败:调用三方接口获取登录秘钥失败");
        }
        if ("2".equals(videoUrl)) {
            return BaseResult.fail("获取视频流地址失败:使用秘钥调用三方获取视频流接口地址失败");
        }
        if ("3".equals(videoUrl)) {
            return BaseResult.fail("获取视频流地址失败:获取没有唯一标识的视频流地址失败");
        }
        if ("4".equals(videoUrl)) {
            return BaseResult.fail("获取视频流地址失败:获取没有唯一标识的视频流地址,返回的data为空");
        }
        return BaseResult.ok(videoUrl);
    }
    @GetMapping("/getZhxzTest")
    public BaseResult getZhxzTest(@RequestParam(name = "playType") String playType) throws Exception {
        if(StringUtils.isEmpty(playType)){
            return BaseResult.fail("参数为空");
        }
        String re;
        HttpClient client = HttpClients.createDefault();
        // 调用三方接口
        String url = "http://39.98.154.144:8060/robot/liveStream?deviceId=34020000001310000002&playType="+playType+"&subDeviceId=34020000001310000002&type=2";
        HttpGet get = new HttpGet(url);
        get.addHeader("content-type", "text/xml");
        HttpResponse res = client.execute(get);
        String jsData = EntityUtils.toString(res.getEntity());
        String result1 = JSONObject.parseObject(jsData).getString("code");
        // 调取结果失败,返回null
        if (!"200".equals(result1)) {
            log.error("没有视频唯一标识的,调用第三方接口,获取视频流失败---" + jsData);
            re= "3";
        }
        JSONObject dataJson = JSONObject.parseObject(jsData).getJSONObject("data");
        if (ObjectUtils.isEmpty(dataJson)) {
            re= "4";
        }
        re= dataJson.getString("playAddr");
        if (StringUtils.isEmpty(re)) {
            return BaseResult.fail("获取视频流地址失败,或者调用三方接口服务失败");
        }
        if ("1".equals(re)) {
            return BaseResult.fail("获取视频流地址失败:调用三方接口获取登录秘钥失败");
        }
        if ("2".equals(re)) {
            return BaseResult.fail("获取视频流地址失败:使用秘钥调用三方获取视频流接口地址失败");
        }
        if ("3".equals(re)) {
            return BaseResult.fail("获取视频流地址失败:获取没有唯一标识的视频流地址失败");
        }
        if ("4".equals(re)) {
            return BaseResult.fail("获取视频流地址失败:获取没有唯一标识的视频流地址,返回的data为空");
        }
        return BaseResult.ok(re);
    }
}
















