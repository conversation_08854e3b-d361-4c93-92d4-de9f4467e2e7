package com.onecity.os.management.monitor.entity;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Table;
import java.util.Date;

/**
 * 重点项目监控表
 * <AUTHOR>
 * @date 2020/11/18
 * */
@Data
@Table(name = "project_key_monitor")
public class ProjectKeyMonitor {
    /**
     * 主键自增
     **/
    private String id;

    /**
     * 标题(非null)
     */
    private String titleName;

    /**
     * 标签
     */
    private String label;

    /**
     * 视频/图片地址
     */
    private String picPath;

    /**
     * 责任单位
     */
    private String unitResponse;

    /**
     * 计划总投资
     */
    private String planTotalInvest;

    /**
     * 计划开工日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date planStartTime;

    /**
     * 项目建设内容
     */
    private String constructionContent;

    /**
     * 建设具体进度
     */
    private String constructionProgress;

    /**
     * 形象进度
     */
    private String imageProgress;

    /**
     * 目前主要问题
     */
    private String mainProblem;

    /**
     * 责任人
     */
    private String responsePerson;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 工程编码
     */
    private String engineCode;

    /**
     * 工程状态
     */
    private String engineState;

    /**
     * 项目类型
     */
    private String projectType;

    /**
     * 监控点唯一标识
     */
    private String monitorUniqueIdentity;

    /**
     * 是否删除（0否，1是）
     */
    private Integer isDelete=0;

    /**
     * 创建人
     */
    private String creater;

    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date  createTime;

    /**
     * 修改人
     */
    private String  updater;

    /**
     * 修改日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date  updateTime;
}
