package com.onecity.os.management.monitor.service;


import com.onecity.os.management.monitor.entity.ProjectKeyMonitor;

import java.util.List;

public interface MonitorService {

    /**
     * 分页查询
     * @param
     * @return
     */
    List<ProjectKeyMonitor> getListByPage();

    /**
     * 获取视频流地址(调用第三方接口)
     *
     * @param indexCode
     * @param protocol
     * @return
     */
    String getVideoUrl(String indexCode, String protocol) throws Exception;
}
