package com.onecity.os.management.programme.service;



import com.onecity.os.management.programme.model.entity.Programme;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/2 下午3:57
 */
public interface ProgrammeService {
    /**
     * 根据标题和发布时间查看通知公告列表(分页)
     *
     * @param createrId
     * @param dateTime
     * @return
     */
    List<Programme> getProgrammeList(String createrId, String dateTime);

    /**
     * 新增/修改日程
     *
     * @param vo
     * @return
     */
    int saveProgramme(Programme vo) throws Exception;

    /**
     * 根据创建人id,查找所有有日程的日期
     *
     * @param createrId
     * @return
     */
    List getAllDate(String createrId);

    /**
     * 根据id,删除通知公告
     *
     * @param id
     * @return
     */
    int deleteProgrammeById(Long id);

    /**
     * 根据id,查询日程详情
     *
     * @param id
     * @return
     */
    Programme getProgrammeInfoById(Long id);
}














