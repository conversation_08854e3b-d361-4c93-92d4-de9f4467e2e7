package com.onecity.os.management.programme.service.impl;


import com.ruoyi.common.security.utils.SecurityUtils;
import com.onecity.os.management.programme.mapper.ProgrammeMapper;
import com.onecity.os.management.programme.model.entity.Programme;
import com.onecity.os.management.programme.service.ProgrammeService;
import com.onecity.os.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/2 下午3:57
 */
@Slf4j
@Service
public class ProgrammeServiceImpl implements ProgrammeService {


    @Resource
    private ProgrammeMapper programmeMapper;

    @Override
    public List<Programme> getProgrammeList(String createrId, String dateTime) {
        return programmeMapper.getProgrammeList(createrId, dateTime);
    }

    @Override
    public int saveProgramme(Programme vo) {
        // 当前用户信息
        String loginName = "";
        String loginUserID = "";
        LoginUser sysUser = SecurityUtils.getLoginUser();
        if (ObjectUtils.isNotEmpty(sysUser)) {
            loginName = sysUser.getUsername();
            loginUserID = sysUser.getUserid().toString();
        }
        //id如果为空则新增，不为空则修改
        if (ObjectUtils.isEmpty(vo.getId())) {
            vo.setCreater(loginName);
            vo.setCreaterId(loginUserID);
            vo.setCreateTime(new Date());
            vo.setUpdater(loginName);
            vo.setUpdateTime(new Date());
            vo.setIsDelete(0);
            programmeMapper.insert(vo);
        }
        else {
            vo.setUpdater(loginName);
            vo.setUpdateTime(new Date());
            programmeMapper.updateByPrimaryKeySelective(vo);
        }
        return 1;
    }

    @Override
    public List getAllDate(String createrId) {
        return programmeMapper.getAllDate(createrId);
    }

    @Override
    public int deleteProgrammeById(Long id) {
        return programmeMapper.deleteByPrimaryKey(id);
    }

    @Override
    public Programme getProgrammeInfoById(Long id) {
        return programmeMapper.getProgrammeInfoById(id);
    }

}





















