package com.onecity.os.management.report.controller;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.management.report.domain.ReportContentExcel;
import com.onecity.os.management.report.domain.WordData;
import com.onecity.os.management.report.domain.po.ReportContentExcelPo;
import com.onecity.os.management.report.domain.po.WordDataPo;
import com.onecity.os.management.report.domain.vo.ReportContentExcelVo;
import com.onecity.os.management.report.service.IReportContentExcelService;
import com.onecity.os.management.report.service.IWordDataService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * Excel报告内容
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Controller
@RequestMapping("/reportExcel")
public class ReportContentExcelController extends BaseController
{
    @Autowired
    private IReportContentExcelService reportContentExcelService;
    @Autowired
    private IWordDataService wordDataService;


    /**
     * 新增保存Excel报告内容
     */
    @ApiOperation(value = "报告管理-新增保存Excel报告内容")
    @Log(title = "报告管理", businessType = BusinessType.INSERT)
    @PostMapping("/saveReportExcelModel")
    @ResponseBody
    public AjaxResult saveReportExcelModel(@RequestBody ReportContentExcelVo reportContentExcelVo)
    {
        return toAjax(reportContentExcelService.insertReportContentExcel(reportContentExcelVo));
    }

    /**
     * 获取Excel报告内容
     */
    @ApiOperation(value = "报告管理-获取Excel报告内容")
    @GetMapping( "/getReportExcelModel")
    @ResponseBody
    public BaseResult<ReportContentExcelPo> getReportExcelModel(@RequestParam(name = "reportId") String reportId)
    {
        return BaseResult.ok(reportContentExcelService.selectReportContentExcelById(reportId),true);
    }

    /**
     * 新增保存word报告内容
     */
    @ApiOperation(value = "报告管理-新增保存word报告内容")
    @Log(title = "报告管理", businessType = BusinessType.INSERT)
    @PostMapping("/saveReportWordModel")
    @ResponseBody
    public AjaxResult saveReportWordModel(@RequestBody WordDataPo wordDataPo)
    {
        return toAjax(wordDataService.insertWordData(wordDataPo));
    }

    /**
     * 获取word报告内容
     */
    @ApiOperation(value = "报告管理-获取Excel报告内容")
    @GetMapping( "/getReportWordModel")
    @ResponseBody
    public BaseResult<WordDataPo> getReportWordModel(@RequestParam(name = "reportId") String reportId)
    {
        return BaseResult.ok(wordDataService.selectWordDataById(reportId),true);
    }
}
