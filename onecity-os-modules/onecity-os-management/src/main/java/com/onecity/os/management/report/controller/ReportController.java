package com.onecity.os.management.report.controller;

import java.util.List;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.management.report.domain.Report;
import com.onecity.os.management.report.domain.ReportHistory;
import com.onecity.os.management.report.domain.vo.ReportVo;
import com.onecity.os.management.report.service.IReportHistoryService;
import com.onecity.os.management.report.service.IReportService;
import com.onecity.os.system.api.model.LoginUser;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

/**
 * 报告管理Controller
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Controller
@RequestMapping("/report")
public class ReportController extends BaseController
{

    @Autowired
    private IReportService reportService;
    @Autowired
    private IReportHistoryService reportHistoryService;

    /**
     * 查询报告管理列表
     */
    @ApiOperation(value = "报告管理-查询报告列表")
    @GetMapping("/getReportList")
    @ResponseBody
    public TableDataInfo list(@RequestParam(name = "reportName", required = false) String reportName,
                              @RequestParam(name = "reportType", required = false) String reportType,
                              @RequestParam(name = "reportStatus", required = false) String reportStatus,
                              @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize)
    {
        startPage();
        List<ReportVo> list = reportService.selectReportList(reportName, reportType, reportStatus);
        return getDataTable(list,true);
    }


    /**
     * 新增保存报告管理
     */
    @ApiOperation(value = "报告管理-新增保存报告")
    @Log(title = "报告管理", businessType = BusinessType.INSERT)
    @PostMapping("/addReport")
    @ResponseBody
    public AjaxResult addSave(@RequestBody Report report)
    {
        return toAjax(reportService.insertReport(report));
    }


    /**
     * 修改保存报告管理
     */
    @ApiOperation(value = "报告管理-修改保存报告")
    @Log(title = "报告管理", businessType = BusinessType.UPDATE)
    @PostMapping("/editReport")
    @ResponseBody
    public AjaxResult editSave(@RequestBody Report report)
    {
        return toAjax(reportService.updateReport(report));
    }

    /**
     * 删除报告管理
     */
    @ApiOperation(value = "报告管理-删除报告")
    @Log(title = "报告管理", businessType = BusinessType.DELETE)
    @GetMapping( "/deleteReport")
    @ResponseBody
    public AjaxResult remove(@RequestParam(name = "reportId") String reportId)
    {
        return toAjax(reportService.deleteReportByReportId(reportId));
    }

    /**
     * 更新报告状态
     */
    @ApiOperation(value = "报告管理-更新报告状态")
    @Log(title = "报告管理", businessType = BusinessType.UPDATE)
    @GetMapping( "/editReportStatus")
    @ResponseBody
    public AjaxResult editReportStatus(@RequestParam(name = "reportId") String reportId,@RequestParam(name = "reportStatus") String reportStatus)
    {
        return toAjax(reportService.editReportStatus(reportId,reportStatus));
    }

    /**
     * 获取报告详情
     */
    @ApiOperation(value = "报告管理-获取报告详情")
    @GetMapping( "/getReportDetail")
    @ResponseBody
    public BaseResult<ReportVo> getReportDetail(@RequestParam(name = "reportId") String reportId)
    {
        return BaseResult.ok(reportService.selectReportByReportId(reportId),true);
    }

    /**
     * 查询报告管理列表
     */
    @ApiOperation(value = "报告管理-查询报告列表")
    @GetMapping("/getReportHistoryList")
    @ResponseBody
    public TableDataInfo list(@RequestParam(name = "reportId") String reportId,
                              @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize)
    {
        startPage();
        List<ReportHistory> list = reportHistoryService.selectReportHistoryList(reportId);
        return getDataTable(list);
    }
}
