package com.onecity.os.management.report.controller;

import java.util.List;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.management.report.domain.Report;
import com.onecity.os.management.report.domain.ReportModle;
import com.onecity.os.management.report.domain.vo.ReportModleVo3;
import com.onecity.os.management.report.service.IReportModleService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 报告模版Controller
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Controller
@RequestMapping("/reportModle")
public class ReportModleController extends BaseController
{
    @Autowired
    private IReportModleService reportModleService;

    /**
     * 查询报告模版列表
     */
    @ApiOperation(value = "报告管理-查询报告模版")
    @GetMapping("/getReportModleList")
    @ResponseBody
    public TableDataInfo list(@RequestParam(name = "reportModleName",required = false) String reportModleName,
                              @RequestParam(name = "reportModleType", required = false) String reportModleType,
                              @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize)
    {
        startPage();
        List<ReportModle> list = reportModleService.selectReportModleList(reportModleName,reportModleType);
        return getDataTable(list);
    }


    /**
     * 新增保存报告模版
     */
    @ApiOperation(value = "报告管理-新增保存报告模版")
    @Log(title = "报告管理", businessType = BusinessType.INSERT)
    @PostMapping("/addReportModle")
    @ResponseBody
    public AjaxResult addSave(@RequestBody ReportModle reportModle)
    {
        return toAjax(reportModleService.insertReportModle(reportModle));
    }

    /**
     * 修改保存报告模版
     */
    @ApiOperation(value = "报告管理-修改保存报告模版")
    @Log(title = "报告管理", businessType = BusinessType.UPDATE)
    @PostMapping("/editReportModle")
    @ResponseBody
    public AjaxResult editSave(@RequestBody ReportModle reportModle)
    {
        return toAjax(reportModleService.updateReportModle(reportModle));
    }

    /**
     * 删除报告模版
     */
    @ApiOperation(value = "报告管理-删除报告模版")
    @Log(title = "报告管理", businessType = BusinessType.DELETE)
    @GetMapping( "/deleteReportModle")
    @ResponseBody
    public AjaxResult remove(@RequestParam(name = "reportModleId") String reportModleId)
    {
        return toAjax(reportModleService.deleteReportModleByReportModleId(reportModleId));
    }

    /**
     * 获取报告详情
     */
    @ApiOperation(value = "报告管理-获取报告模板详情")
    @GetMapping( "/getReportModleDetail")
    @ResponseBody
    public BaseResult<ReportModle> getReportModleDetail(@RequestParam(name = "reportModleId") String reportModleId)
    {
        return BaseResult.ok(reportModleService.selectReportModleByReportModleId(reportModleId));
    }

    /**
     * 获取报告模版内容
     */
    @ApiOperation(value = "报告管理-获取报告模板内容")
    @GetMapping( "/getReportModleContent")
    @ResponseBody
    public BaseResult getReportModleContent(@RequestParam(name = "reportModleId") String reportModleId)
    {
        return BaseResult.ok(reportModleService.selectReportModleContentByReportModleId(reportModleId));
    }

    /**
     * 保存报告模版内容
     */
    @ApiOperation(value = "报告管理-保存报告模板内容")
    @PostMapping( "/saveReportModleContent")
    @ResponseBody
    public BaseResult saveReportModleContent(@RequestBody ReportModleVo3 reportModleVo3)
    {
        return BaseResult.ok(reportModleService.saveReportModleContent(reportModleVo3));
    }

}
