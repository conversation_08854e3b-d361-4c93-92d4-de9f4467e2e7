package com.onecity.os.management.report.domain;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 报告管理对象 report_history
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
public class ReportHistory implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 历史报告Id */
    private String reportHistoryId;

    /** 报告Id */
    private String reportId;

    /** 历史报告名称 */
    private String reportHistoryName;

    /** 报告生成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date generateTime;

    /** 报告内容 */
    private String reportHistoryContent;

    /** 更新人 */
    private String updater;

    /** 创建人 */
    private String creater;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
