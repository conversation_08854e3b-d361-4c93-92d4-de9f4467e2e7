package com.onecity.os.management.report.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 报告管理对象 report_modle
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
public class ReportModle implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 模板Id */
    private String reportModleId;

    /** 模板名称 */
    private String reportModleName;

    /** 模板类型 */
    private String reportModleType;

    /** 模板内容 */
    private String reportModleContent;

    /** 更新人 */
    private String updater;

    /** 创建人 */
    private String creater;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
