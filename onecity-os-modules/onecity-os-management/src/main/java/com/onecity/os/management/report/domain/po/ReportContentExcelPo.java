package com.onecity.os.management.report.domain.po;


import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.onecity.os.management.report.domain.DataSet;
import lombok.Data;

/**
 * 报告管理对象 report_content_excel
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
public class ReportContentExcelPo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 内容Id */
    private String reportContentId;

    /** 报告Id */
    private String reportId;

    /** 报告内容 */
    private String reportContent;

    /** 内容类型 */
    private String contentType;

    /** 内容所用数据集 */
    private List<DataSet> dataSetList;
    /** 更新人 */
    private String updater;

    /** 创建人 */
    private String creater;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
