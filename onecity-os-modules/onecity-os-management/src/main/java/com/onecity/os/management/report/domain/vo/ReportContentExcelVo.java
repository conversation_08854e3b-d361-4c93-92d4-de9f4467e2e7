package com.onecity.os.management.report.domain.vo;


import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.onecity.os.management.report.domain.DataSet;
import lombok.Data;

/**
 * 报告管理对象 report_content_excel
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
public class ReportContentExcelVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 内容Id */
    private String reportContentId;

    /** 报告Id */
    private String reportId;

    /** 报告内容 */
    private String reportContent;

    /** 内容类型 */
    private String contentType;

    private List<DataSet> dataSetList;
}
