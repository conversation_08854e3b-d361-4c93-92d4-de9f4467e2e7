package com.onecity.os.management.report.domain.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.onecity.os.system.api.domain.SysUser;
import lombok.Data;

/**
 * 报告管理对象 report
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
public class ReportVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 报告Id */
    private String reportId;

    /** 排序 */
    private Integer orderNum;

    /** 报告名称 */
    private String reportName;

    /** 报告类型 */
    private String reportType;

    /** 是否是周期报告1-是0-否 */
    private String isPeriodic;

    /** 报告周期 */
    private String period;

    /** 接收人 */
    private String recipients;

    /** 报告状态 */
    private String reportStatus;

    /** 更新人 */
    private String updater;

    /** 创建人 */
    private String creater;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 接收人列表
     */
    private List<SysUserVo> sysUserList;
}
