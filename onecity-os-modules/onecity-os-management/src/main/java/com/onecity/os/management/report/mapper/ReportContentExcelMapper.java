package com.onecity.os.management.report.mapper;

import java.util.List;
import com.onecity.os.management.report.domain.ReportContentExcel;
/**
 * 报告管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface ReportContentExcelMapper 
{
    /**
     * 查询报告管理
     * 
     * @param reportId 报告管理主键
     * @return 报告管理
     */
     ReportContentExcel selectReportContentExcelById(String reportId);

     /**
     * 查询报告管理
     *
     * @param reportContentId 报告管理主键
     * @return 报告管理
     */
     ReportContentExcel selectReportContentExcelByReportContentId(String reportContentId);

    /**
     * 查询报告管理列表
     * 
     * @param reportContentExcel 报告管理
     * @return 报告管理集合
     */
    public List<ReportContentExcel> selectReportContentExcelList(ReportContentExcel reportContentExcel);

    /**
     * 新增报告管理
     * 
     * @param reportContentExcel 报告管理
     * @return 结果
     */
    public int insertReportContentExcel(ReportContentExcel reportContentExcel);

    /**
     * 修改报告管理
     * 
     * @param reportContentExcel 报告管理
     * @return 结果
     */
    public int updateReportContentExcel(ReportContentExcel reportContentExcel);

    /**
     * 删除报告管理
     * 
     * @param reportContentId 报告管理主键
     * @return 结果
     */
    public int deleteReportContentExcelByReportContentId(String reportContentId);

    /**
     * 批量删除报告管理
     * 
     * @param reportContentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteReportContentExcelByReportContentIds(String[] reportContentIds);
}
