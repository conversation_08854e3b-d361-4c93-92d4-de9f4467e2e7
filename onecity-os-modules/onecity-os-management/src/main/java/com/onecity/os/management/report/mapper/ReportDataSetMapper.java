package com.onecity.os.management.report.mapper;

import com.onecity.os.management.report.domain.ReportDataSet;

import java.util.List;

public interface ReportDataSetMapper {
    public int deleteReportDataSetByReportId(String reportId);
    public int insertReportDataSetBatch(List<ReportDataSet> reportDataSetList, String reportId);
    public List<ReportDataSet> selectReportDataSetListByReportId(String reportId);
}
