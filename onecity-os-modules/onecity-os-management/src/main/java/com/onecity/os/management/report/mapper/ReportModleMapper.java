package com.onecity.os.management.report.mapper;

import java.util.List;
import com.onecity.os.management.report.domain.ReportModle;
import com.onecity.os.management.report.domain.vo.ReportModleVo3;
import org.apache.ibatis.annotations.Param;

/**
 * 报告管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface ReportModleMapper 
{
    /**
     * 查询报告管理
     * 
     * @param reportModleId 报告管理主键
     * @return 报告管理
     */
    public ReportModle selectReportModleByReportModleId(String reportModleId);
    /**
     * 获取报告模版内容
     */
    public String selectReportModleContentByReportModleId(String reportModleId);
    /**
     * 保存报告模版内容
     */
    public int saveReportModleContent(ReportModle reportModle);

    /**
     * 查询报告管理列表
     *
     * @return 报告管理集合
     */
     List<ReportModle> selectReportModleList(@Param("reportModleName") String reportModleName,@Param("reportModleType") String reportModleType);

    /**
     * 新增报告管理
     * 
     * @param reportModle 报告管理
     * @return 结果
     */
    public int insertReportModle(ReportModle reportModle);

    /**
     * 修改报告管理
     * 
     * @param reportModle 报告管理
     * @return 结果
     */
    public int updateReportModle(ReportModle reportModle);

    /**
     * 删除报告管理
     * 
     * @param reportModleId 报告管理主键
     * @return 结果
     */
    public int deleteReportModleByReportModleId(String reportModleId);

    /**
     * 批量删除报告管理
     * 
     * @param reportModleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteReportModleByReportModleIds(String[] reportModleIds);
}
