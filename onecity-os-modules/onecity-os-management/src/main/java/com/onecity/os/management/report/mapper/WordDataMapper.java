package com.onecity.os.management.report.mapper;

import com.onecity.os.management.report.domain.WordData;
import com.onecity.os.management.report.domain.po.WordDataPo;

import java.util.List;

/**
 * 报告管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface WordDataMapper 
{
    /**
     * 查询报告管理
     * 
     * @param reportId 报告管理主键
     * @return 报告管理
     */
     WordDataPo selectWordDataById(String reportId);

     /**
     * 查询报告管理
     *
     * @param reportContentId 报告管理主键
     * @return 报告管理
     */
     WordData selectWordDataByWordDataId(String reportContentId);

    /**
     * 查询报告管理列表
     * 
     * @param wordData 报告管理
     * @return 报告管理集合
     */
    public List<WordData> selectWordDataList(WordData wordData);

    /**
     * 新增报告管理
     * 
     * @param wordDataPo 报告管理
     * @return 结果
     */
    public int insertWordData(WordDataPo wordDataPo);

    /**
     * 修改报告管理
     * 
     * @param wordDataPo 报告管理
     * @return 结果
     */
    public int updateWordData(WordDataPo wordDataPo);

    /**
     * 删除报告管理
     * 
     * @param wordDataId 报告管理主键
     * @return 结果
     */
    public int deleteWordDataByWordDataId(String wordDataId);

    /**
     * 批量删除报告管理
     * 
     * @param wordDataIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWordDataByWordDataIds(String[] wordDataIds);
}
