package com.onecity.os.management.report.service;

import com.onecity.os.management.report.domain.ReportContentExcel;
import com.onecity.os.management.report.domain.po.ReportContentExcelPo;
import com.onecity.os.management.report.domain.vo.ReportContentExcelVo;

import java.util.List;

/**
 * 报告管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface IReportContentExcelService 
{
    /**
     * 查询报告管理
     *
     * @return 报告管理
     */
     ReportContentExcelPo selectReportContentExcelById(String reportId);

    /**
     * 查询报告管理列表
     * 
     * @param reportContentExcel 报告管理
     * @return 报告管理集合
     */
     List<ReportContentExcel> selectReportContentExcelList(ReportContentExcel reportContentExcel);

    /**
     * 新增报告管理
     * 
     * @param reportContentExcelVo 报告管理
     * @return 结果
     */
     int insertReportContentExcel(ReportContentExcelVo reportContentExcelVo);

    /**
     * 修改报告管理
     * 
     * @param reportContentExcel 报告管理
     * @return 结果
     */
     int updateReportContentExcel(ReportContentExcel reportContentExcel);

    /**
     * 批量删除报告管理
     * 
     * @param reportContentIds 需要删除的报告管理主键集合
     * @return 结果
     */
     int deleteReportContentExcelByReportContentIds(String reportContentIds);

    /**
     * 删除报告管理信息
     * 
     * @param reportContentId 报告管理主键
     * @return 结果
     */
     int deleteReportContentExcelByReportContentId(String reportContentId);
}
