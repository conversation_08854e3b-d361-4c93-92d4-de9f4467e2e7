package com.onecity.os.management.report.service;

import com.onecity.os.management.report.domain.ReportHistory;

import java.util.List;

/**
 * 报告管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface IReportHistoryService 
{
    /**
     * 查询报告管理
     * 
     * @param reportHistoryId 报告管理主键
     * @return 报告管理
     */
    public ReportHistory selectReportHistoryByReportHistoryId(String reportHistoryId);

    /**
     * 查询报告管理列表
     *
     * @return 报告管理集合
     */
     List<ReportHistory> selectReportHistoryList(String reportId);

    /**
     * 新增报告管理
     * 
     * @param reportHistory 报告管理
     * @return 结果
     */
    public int insertReportHistory(ReportHistory reportHistory);

    /**
     * 修改报告管理
     * 
     * @param reportHistory 报告管理
     * @return 结果
     */
    public int updateReportHistory(ReportHistory reportHistory);

    /**
     * 批量删除报告管理
     * 
     * @param reportHistoryIds 需要删除的报告管理主键集合
     * @return 结果
     */
    public int deleteReportHistoryByReportHistoryIds(String reportHistoryIds);

    /**
     * 删除报告管理信息
     * 
     * @param reportHistoryId 报告管理主键
     * @return 结果
     */
    public int deleteReportHistoryByReportHistoryId(String reportHistoryId);
}
