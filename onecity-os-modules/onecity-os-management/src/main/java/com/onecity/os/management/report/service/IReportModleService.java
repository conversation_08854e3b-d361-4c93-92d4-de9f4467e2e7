package com.onecity.os.management.report.service;

import com.onecity.os.management.report.domain.ReportModle;
import com.onecity.os.management.report.domain.vo.ReportModleContentSet;
import com.onecity.os.management.report.domain.vo.ReportModleVo3;

import java.util.List;

/**
 * 报告管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface IReportModleService 
{
    /**
     * 查询报告管理
     * 
     * @param reportModleId 报告管理主键
     * @return 报告管理
     */
    public ReportModle selectReportModleByReportModleId(String reportModleId);
    /**
     * 获取报告模版内容
     */
    public ReportModleContentSet selectReportModleContentByReportModleId(String reportModleId);
    /**
     * 保存报告模版内容
     */
    public int saveReportModleContent(ReportModleVo3 reportModleVo3);

    /**
     * 查询报告管理列表
     *
     * @return 报告管理集合
     */
     List<ReportModle> selectReportModleList(String reportModleName,String reportModleType);

    /**
     * 新增报告管理
     * 
     * @param reportModle 报告管理
     * @return 结果
     */
    public int insertReportModle(ReportModle reportModle);

    /**
     * 修改报告管理
     * 
     * @param reportModle 报告管理
     * @return 结果
     */
     int updateReportModle(ReportModle reportModle);

    /**
     * 批量删除报告管理
     * 
     * @param reportModleIds 需要删除的报告管理主键集合
     * @return 结果
     */
    public int deleteReportModleByReportModleIds(String reportModleIds);

    /**
     * 删除报告管理信息
     * 
     * @param reportModleId 报告管理主键
     * @return 结果
     */
    public int deleteReportModleByReportModleId(String reportModleId);
}
