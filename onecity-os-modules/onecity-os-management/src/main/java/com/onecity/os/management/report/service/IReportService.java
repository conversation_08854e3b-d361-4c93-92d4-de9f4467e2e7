package com.onecity.os.management.report.service;

import com.onecity.os.management.report.domain.Report;
import com.onecity.os.management.report.domain.vo.ReportVo;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 报告管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface IReportService 
{
    /**
     * 查询报告管理
     * 
     * @param reportId 报告管理主键
     * @return 报告管理
     */
    public ReportVo selectReportByReportId(String reportId);

    /**
     * 查询报告管理列表
     *
     * @return 报告管理集合
     */
    public List<ReportVo> selectReportList(String reportName,String reportType,String reportStatus);

    List<Report> getReportListByUser();

    /**
     * 新增报告管理
     * 
     * @param report 报告管理
     * @return 结果
     */
    public int insertReport(Report report);

    /**
     * 修改报告管理
     * 
     * @param report 报告管理
     * @return 结果
     */
     int updateReport(Report report);

    /**
     * 批量删除报告管理
     * 
     * @param reportIds 需要删除的报告管理主键集合
     * @return 结果
     */
     int deleteReportByReportIds(String reportIds);

    /**
     * 删除报告管理信息
     * 
     * @param reportId 报告管理主键
     * @return 结果
     */
     int deleteReportByReportId(String reportId);
     /**
     * 修改报告状态
     *
     * @param reportId
     * @param reportStatus
     * @return
     */
     int editReportStatus(String reportId,String reportStatus);
}
