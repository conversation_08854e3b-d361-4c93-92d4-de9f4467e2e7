package com.onecity.os.management.report.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.text.Convert;
import com.onecity.os.common.core.utils.DateUtils;
import com.onecity.os.common.core.utils.IdUtils;
import com.onecity.os.management.report.domain.DataSet;
import com.onecity.os.management.report.domain.ReportContentExcel;
import com.onecity.os.management.report.domain.ReportDataSet;
import com.onecity.os.management.report.domain.po.ReportContentExcelPo;
import com.onecity.os.management.report.domain.vo.ReportContentExcelVo;
import com.onecity.os.management.report.mapper.ReportContentExcelMapper;
import com.onecity.os.management.report.mapper.ReportDataSetMapper;
import com.onecity.os.management.report.service.IReportContentExcelService;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 报告管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Service
@Slf4j
public class ReportContentExcelServiceImpl implements IReportContentExcelService
{
    @Autowired
    private ReportContentExcelMapper reportContentExcelMapper;
    @Autowired
    private ReportDataSetMapper reportDataSetMapper;

    /**
     * 查询报告管理
     * 
     * @param reportId 报告管理主键
     * @return 报告管理
     */
    @Override
    public ReportContentExcelPo selectReportContentExcelById(String reportId)
    {
        ReportContentExcelPo reportContentExcelPo = new ReportContentExcelPo();
        ReportContentExcel reportContentExcel = reportContentExcelMapper.selectReportContentExcelById(reportId);
        if (reportContentExcel != null) {
            reportContentExcelPo = BeanUtil.copyProperties(reportContentExcel, ReportContentExcelPo.class);
            List<ReportDataSet> reportDataSetList = reportDataSetMapper.selectReportDataSetListByReportId(reportId);
            if (reportDataSetList!=null){
                //相同dataSetId的设置成一个DataSet对象
                Set<Long> dataSetIdSet = new HashSet<>();
                for(ReportDataSet reportDataSet : reportDataSetList){
                    dataSetIdSet.add(reportDataSet.getDataSetId());
                }
                for (Long dataSetId : dataSetIdSet){
                    List<String> dataSetColumnList = new ArrayList<>();
                    for (ReportDataSet reportDataSet : reportDataSetList){
                        if (reportDataSet.getDataSetId().equals(dataSetId)){
                            dataSetColumnList.add(reportDataSet.getDataSetColumn());
                        }
                    }
                    DataSet dataSet = new DataSet();
                    dataSet.setDataSetId(dataSetId);
                    dataSet.setDataSetColumnList(dataSetColumnList);
                    reportContentExcelPo.getDataSetList().add(dataSet);
                }
            }
        }

        return reportContentExcelPo;
    }

    /**
     * 查询报告管理列表
     * 
     * @param reportContentExcel 报告管理
     * @return 报告管理
     */
    @Override
    public List<ReportContentExcel> selectReportContentExcelList(ReportContentExcel reportContentExcel)
    {
        return reportContentExcelMapper.selectReportContentExcelList(reportContentExcel);
    }

    /**
     * 新增报告管理
     * 
     * @param reportContentExcelVo 报告管理
     * @return 结果
     */
    @Override
    public int insertReportContentExcel(ReportContentExcelVo reportContentExcelVo)
    {
        ReportContentExcel reportContentExcel = BeanUtil.copyProperties(reportContentExcelVo, ReportContentExcel.class);
        //当前用户信息
        LoginUser sysUser = null;
        try {
            sysUser = (LoginUser) SecurityUtils.getLoginUser();
            if (sysUser != null) {
                reportContentExcel.setCreater(sysUser.getUsername());
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            return 0;
        }
        ReportContentExcel reportContentExcel1 = reportContentExcelMapper.selectReportContentExcelById(reportContentExcel.getReportId());
        ReportContentExcel reportContentExcel2 = reportContentExcelMapper.selectReportContentExcelByReportContentId(reportContentExcel.getReportContentId());
        if (reportContentExcel1 == null && reportContentExcel2 == null) {
            //新增
            reportContentExcel.setReportContentId(IdUtils.simpleUUID());
            reportContentExcel.setCreateTime(DateUtils.getNowDate());
            reportContentExcel.setCreater(sysUser.getUsername());
            reportContentExcel.setUpdater(sysUser.getUsername());
            reportContentExcel.setUpdateTime(DateUtils.getNowDate());
            //新增数据集id及字段
            List<ReportDataSet> reportDataSetList = new ArrayList<>();
            if (reportContentExcelVo.getDataSetList() != null && reportContentExcelVo.getDataSetList().size()>0) {
                log.info("新增数据集id及字段:{}", JSONObject.toJSONString(reportContentExcelVo.getDataSetList()));
                for(DataSet dataSet : reportContentExcelVo.getDataSetList()) {
                    if (dataSet.getDataSetId() != null && dataSet.getDataSetColumnList() != null) {
                        for (String dataSetColumn : dataSet.getDataSetColumnList()) {
                            ReportDataSet reportDataSet = new ReportDataSet();
                            reportDataSet.setReportId(reportContentExcelVo.getReportId());
                            reportDataSet.setReportDataSetId(IdUtils.simpleUUID());
                            reportDataSet.setDataSetId(dataSet.getDataSetId());
                            reportDataSet.setDataSetColumn(dataSetColumn);
                            reportDataSetList.add(reportDataSet);
                        }
                        log.info("sql新增数据集id及字段:{}", JSONObject.toJSONString(reportDataSetList));
                        reportDataSetMapper.insertReportDataSetBatch(reportDataSetList, reportContentExcelVo.getReportId());
                    }
                }
            }
            log.info("新增报告管理:{}", JSONObject.toJSONString(reportContentExcel));
            return reportContentExcelMapper.insertReportContentExcel(reportContentExcel);
        }else if(reportContentExcel1 != null && reportContentExcel2 != null) {
            //修改
            reportContentExcel.setUpdater(sysUser.getUsername());
            reportContentExcel.setUpdateTime(DateUtils.getNowDate());
            List<ReportDataSet> reportDataSetList = new ArrayList<>();
            if (reportContentExcelVo.getDataSetList() != null) {
                for (DataSet dataSet : reportContentExcelVo.getDataSetList()) {
                    if (dataSet.getDataSetId() != null && dataSet.getDataSetColumnList() != null) {
                        for (String dataSetColumn : dataSet.getDataSetColumnList()) {
                            ReportDataSet reportDataSet = new ReportDataSet();
                            reportDataSet.setReportId(reportContentExcelVo.getReportId());
                            reportDataSet.setReportDataSetId(IdUtils.simpleUUID());
                            reportDataSet.setDataSetId(dataSet.getDataSetId());
                            reportDataSet.setDataSetColumn(dataSetColumn);
                            reportDataSetList.add(reportDataSet);
                        }
                        reportDataSetMapper.deleteReportDataSetByReportId(reportContentExcelVo.getReportId());
                        reportDataSetMapper.insertReportDataSetBatch(reportDataSetList, reportContentExcelVo.getReportId());
                    }
                }
            }else {
                reportDataSetMapper.deleteReportDataSetByReportId(reportContentExcelVo.getReportId());
            }
            return reportContentExcelMapper.updateReportContentExcel(reportContentExcel);
        }else {
            return 0;
        }
    }

    /**
     * 修改报告管理
     * 
     * @param reportContentExcel 报告管理
     * @return 结果
     */
    @Override
    public int updateReportContentExcel(ReportContentExcel reportContentExcel)
    {
        reportContentExcel.setUpdateTime(DateUtils.getNowDate());
        return reportContentExcelMapper.updateReportContentExcel(reportContentExcel);
    }

    /**
     * 批量删除报告管理
     * 
     * @param reportContentIds 需要删除的报告管理主键
     * @return 结果
     */
    @Override
    public int deleteReportContentExcelByReportContentIds(String reportContentIds)
    {
        return reportContentExcelMapper.deleteReportContentExcelByReportContentIds(Convert.toStrArray(reportContentIds));
    }

    /**
     * 删除报告管理信息
     * 
     * @param reportContentId 报告管理主键
     * @return 结果
     */
    @Override
    public int deleteReportContentExcelByReportContentId(String reportContentId)
    {
        return reportContentExcelMapper.deleteReportContentExcelByReportContentId(reportContentId);
    }
}
