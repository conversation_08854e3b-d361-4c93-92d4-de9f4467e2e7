package com.onecity.os.management.report.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.onecity.os.common.core.text.Convert;
import com.onecity.os.common.core.utils.BeanHelper;
import com.onecity.os.common.core.utils.DateUtils;
import com.onecity.os.common.core.utils.IdUtils;
import com.onecity.os.management.report.domain.DataSet;
import com.onecity.os.management.report.domain.ReportDataSet;
import com.onecity.os.management.report.domain.ReportModle;
import com.onecity.os.management.report.domain.vo.ReportModleContentSet;
import com.onecity.os.management.report.domain.vo.ReportModleVo3;
import com.onecity.os.management.report.mapper.ReportDataSetMapper;
import com.onecity.os.management.report.mapper.ReportModleMapper;
import com.onecity.os.management.report.service.IReportModleService;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 报告管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Service
@Slf4j
public class ReportModleServiceImpl implements IReportModleService
{
    @Autowired
    private ReportModleMapper reportModleMapper;
    @Autowired
    private ReportDataSetMapper reportDataSetMapper;

    /**
     * 查询报告管理
     * 
     * @param reportModleId 报告管理主键
     * @return 报告管理
     */
    @Override
    public ReportModle selectReportModleByReportModleId(String reportModleId)
    {
        return reportModleMapper.selectReportModleByReportModleId(reportModleId);
    }

    /**
     * 获取报告模版内容
     */
    @Override
    public ReportModleContentSet selectReportModleContentByReportModleId(String reportModleId)
    {
        ReportModleContentSet reportModleContentSet = new ReportModleContentSet();
        reportModleContentSet.setReportModleContent(reportModleMapper.selectReportModleContentByReportModleId(reportModleId));
        List<ReportDataSet> reportDataSetList = reportDataSetMapper.selectReportDataSetListByReportId(reportModleId);

        if (reportDataSetList!=null){
            //相同dataSetId的设置成一个DataSet对象
            Set<Long> dataSetIdSet = new HashSet<>();
            for(ReportDataSet reportDataSet : reportDataSetList){
                dataSetIdSet.add(reportDataSet.getDataSetId());
            }
            for (Long dataSetId : dataSetIdSet){
                List<String> dataSetColumnList = new ArrayList<>();
                for (ReportDataSet reportDataSet : reportDataSetList){
                    if (reportDataSet.getDataSetId().equals(dataSetId)){
                        dataSetColumnList.add(reportDataSet.getDataSetColumn());
                    }
                }
                DataSet dataSet = new DataSet();
                dataSet.setDataSetId(dataSetId);
                dataSet.setDataSetColumnList(dataSetColumnList);
                reportModleContentSet.getDataSetList().add(dataSet);
            }
        }
        return reportModleContentSet;
    }

    /**
     * 保存报告模版内容
     */
    @Override
    public int saveReportModleContent(ReportModleVo3 reportModleVo3)
    {
        ReportModle reportModle = BeanHelper.copyProperties(reportModleVo3, ReportModle.class);
        //当前用户信息
        LoginUser sysUser = null;
        try {
            sysUser = (LoginUser) SecurityUtils.getLoginUser();
            if (sysUser != null) {
                reportModle.setUpdater(sysUser.getUsername());
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            return 0;
        }
        reportModle.setUpdateTime(DateUtils.getNowDate());
        List<ReportDataSet> reportDataSetList = new ArrayList<>();
        if(reportModleVo3.getDataSetList()!=null) {
            for (DataSet dataSet : reportModleVo3.getDataSetList()) {
                for (String dataSetColumn : dataSet.getDataSetColumnList()) {
                    ReportDataSet reportDataSet = new ReportDataSet();
                    reportDataSet.setReportId(reportModle.getReportModleId());
                    reportDataSet.setReportDataSetId(IdUtils.simpleUUID());
                    reportDataSet.setDataSetId(dataSet.getDataSetId());
                    reportDataSet.setDataSetColumn(dataSetColumn);
                    reportDataSetList.add(reportDataSet);
                }
            }
            reportDataSetMapper.deleteReportDataSetByReportId(reportModle.getReportModleId());
            reportDataSetMapper.insertReportDataSetBatch(reportDataSetList, reportModle.getReportModleId());
        }else {
            reportDataSetMapper.deleteReportDataSetByReportId(reportModle.getReportModleId());
        }
        return reportModleMapper.saveReportModleContent(reportModle);
    }

    /**
     * 查询报告管理列表
     *
     * @return 报告管理
     */
    @Override
    public List<ReportModle> selectReportModleList(String reportModleName,String reportModleType)
    {
        return reportModleMapper.selectReportModleList(reportModleName,reportModleType);
    }

    /**
     * 新增报告管理
     * 
     * @param reportModle 报告管理
     * @return 结果
     */
    @Override
    public int insertReportModle(ReportModle reportModle)
    {
        //当前用户信息
        LoginUser sysUser = null;
        try {
            sysUser = (LoginUser) SecurityUtils.getLoginUser();
            if (sysUser != null) {
                reportModle.setCreater(sysUser.getUsername());
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            return 0;
        }
        reportModle.setReportModleId(IdUtils.simpleUUID());
        reportModle.setCreateTime(DateUtils.getNowDate());
        return reportModleMapper.insertReportModle(reportModle);
    }

    /**
     * 修改报告管理
     * 
     * @param reportModle 报告管理
     * @return 结果
     */
    @Override
    public int updateReportModle(ReportModle reportModle)
    {
        //当前用户信息
        LoginUser sysUser = null;
        try {
            sysUser = (LoginUser) SecurityUtils.getLoginUser();
            if (sysUser != null) {
                reportModle.setUpdater(sysUser.getUsername());
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            return 0;
        }
        reportModle.setUpdateTime(DateUtils.getNowDate());
        return reportModleMapper.updateReportModle(reportModle);
    }

    /**
     * 批量删除报告管理
     * 
     * @param reportModleIds 需要删除的报告管理主键
     * @return 结果
     */
    @Override
    public int deleteReportModleByReportModleIds(String reportModleIds)
    {
        return reportModleMapper.deleteReportModleByReportModleIds(Convert.toStrArray(reportModleIds));
    }

    /**
     * 删除报告管理信息
     * 
     * @param reportModleId 报告管理主键
     * @return 结果
     */
    @Override
    public int deleteReportModleByReportModleId(String reportModleId)
    {
        return reportModleMapper.deleteReportModleByReportModleId(reportModleId);
    }
}
