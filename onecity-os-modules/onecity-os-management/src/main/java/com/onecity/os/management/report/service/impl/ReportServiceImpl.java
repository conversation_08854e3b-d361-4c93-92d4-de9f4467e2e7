package com.onecity.os.management.report.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import cn.hutool.core.bean.BeanUtil;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.text.Convert;
import com.onecity.os.common.core.utils.BeanHelper;
import com.onecity.os.common.core.utils.DateUtils;
import com.onecity.os.common.core.utils.IdUtils;
import com.onecity.os.management.report.domain.Report;
import com.onecity.os.management.report.domain.vo.ReportVo;
import com.onecity.os.management.report.domain.vo.SysUserVo;
import com.onecity.os.management.report.mapper.ReportMapper;
import com.onecity.os.management.report.service.IReportService;
import com.onecity.os.system.api.RemoteUserService;
import com.onecity.os.system.api.domain.SysUser;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 报告管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Service
@Slf4j
public class ReportServiceImpl implements IReportService
{
    @Autowired
    private ReportMapper reportMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询报告管理
     * 
     * @param reportId 报告管理主键
     * @return 报告管理
     */
    @Override
    public ReportVo selectReportByReportId(String reportId)
    {
        Report report = reportMapper.selectReportByReportId(reportId);
        ReportVo reportVo = BeanUtil.copyProperties(report, ReportVo.class);
        List<SysUserVo> sysUserList = new ArrayList<>();
        BaseResult<List<SysUser>> userListResult =  remoteUserService.getUserByPcUserIds(report.getRecipients().split(","));
        if (userListResult != null && userListResult.getCode() == 200) {
            List<SysUser> userList = userListResult.getData();
            sysUserList = userList.stream().map(user -> {
                SysUserVo sysUserVo = new SysUserVo();
                sysUserVo.setUserId(user.getUserId());
                sysUserVo.setNickName(user.getNickName());
                return sysUserVo;
            }).collect(Collectors.toList());
            reportVo.setSysUserList(sysUserList);
        }
        return reportVo;
    }

    /**
     * 查询报告管理列表
     *
     * @return 报告管理
     */
    @Override
    public List<ReportVo> selectReportList(String reportName,String reportType,String reportStatus)
    {
        List<ReportVo> reportVoList = new ArrayList<>();
        List<Report> reportList = reportMapper.selectReportList(reportName, reportType, reportStatus);
        reportVoList = BeanHelper.copyWithCollection(reportList, ReportVo.class);
        List recipients = new ArrayList();

        if (reportVoList != null && reportVoList.size() > 0) {
            reportVoList.forEach(reportVo -> {
                recipients.addAll(Arrays.asList(reportVo.getRecipients().split(",")));}
                    );
            // 去重
            List recipients1 = (List) recipients.stream().distinct().collect(Collectors.toList());
            BaseResult<List<SysUser>> userListResult =  remoteUserService.getUserByPcUserIds((String[]) recipients1.toArray(new String[0]));
            if (userListResult != null && userListResult.getCode() == 200) {
                List<SysUser> userList = userListResult.getData();
                List<SysUserVo> sysUserList = userList.stream().map(user -> {
                    SysUserVo sysUserVo = new SysUserVo();
                    sysUserVo.setUserId(user.getUserId());
                    sysUserVo.setNickName(user.getNickName());
                    return sysUserVo;
                }).collect(Collectors.toList());
                for(ReportVo reportVo1 : reportVoList){
                    List<SysUserVo> sysUserList1 = new ArrayList<>();
                    for(String userId : reportVo1.getRecipients().split(",")){
                        for(SysUserVo sysUserVo : sysUserList){
                            SysUserVo sysUserVo1 = new SysUserVo();
                            if(sysUserVo.getUserId().equals(userId)){
                                sysUserVo1.setUserId(sysUserVo.getUserId());
                                sysUserVo1.setNickName(sysUserVo.getNickName());
                                sysUserList1.add(sysUserVo1);
                            }
                        }
                    }
                    reportVo1.setSysUserList(sysUserList1);
                }
            }

        }
        return reportVoList;
    }

    @Override
    public List<Report> getReportListByUser() {
        //当前用户信息
        LoginUser sysUser = null;
        try {
            sysUser = (LoginUser) SecurityUtils.getLoginUser();
            if (sysUser != null) {
                return reportMapper.getReportListByUser(sysUser.getUserid().toString());
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            return new ArrayList<>();
        }
        return new ArrayList<>();
    }

    /**
     * 新增报告管理
     * 
     * @param report 报告管理
     * @return 结果
     */
    @Override
    public int insertReport(Report report)
    {
        //当前用户信息
        LoginUser sysUser = null;
        try {
            sysUser = (LoginUser) SecurityUtils.getLoginUser();
            if (sysUser != null) {
                report.setCreater(sysUser.getUsername());
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            return 0;
        }
        report.setReportId(IdUtils.simpleUUID());
        report.setCreateTime(DateUtils.getNowDate());
        report.setReportStatus("0");
        return reportMapper.insertReport(report);
    }

    /**
     * 修改报告管理
     * 
     * @param report 报告管理
     * @return 结果
     */
    @Override
    public int updateReport(Report report)
    {
        //当前用户信息
        LoginUser sysUser = null;
        try {
            sysUser = (LoginUser) SecurityUtils.getLoginUser();
            if (sysUser != null) {
                report.setUpdater(sysUser.getUsername());
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            return 0;
        }
        report.setUpdateTime(DateUtils.getNowDate());
        return reportMapper.updateReport(report);
    }

    /**
     * 批量删除报告管理
     * 
     * @param reportIds 需要删除的报告管理主键
     * @return 结果
     */
    @Override
    public int deleteReportByReportIds(String reportIds)
    {
        return reportMapper.deleteReportByReportIds(Convert.toStrArray(reportIds));
    }

    /**
     * 删除报告管理信息
     * 
     * @param reportId 报告管理主键
     * @return 结果
     */
    @Override
    public int deleteReportByReportId(String reportId)
    {
        return reportMapper.deleteReportByReportId(reportId);
    }

    @Override
    public int editReportStatus(String reportId, String reportStatus) {
        return reportMapper.editReportStatus(reportId, reportStatus);
    }
}
