package com.onecity.os.management.report.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.onecity.os.common.core.text.Convert;
import com.onecity.os.common.core.utils.DateUtils;
import com.onecity.os.common.core.utils.IdUtils;
import com.onecity.os.management.report.domain.DataSet;
import com.onecity.os.management.report.domain.ReportDataSet;
import com.onecity.os.management.report.domain.WordData;
import com.onecity.os.management.report.domain.po.WordDataPo;
import com.onecity.os.management.report.mapper.ReportDataSetMapper;
import com.onecity.os.management.report.mapper.WordDataMapper;
import com.onecity.os.management.report.service.IWordDataService;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 报告管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Service
@Slf4j
public class WordDataServiceImpl implements IWordDataService
{
    @Autowired
    private WordDataMapper wordDataMapper;
    @Autowired
    private ReportDataSetMapper reportDataSetMapper;

    /**
     * 查询报告管理
     * 
     * @param reportId 报告管理主键
     * @return 报告管理
     */
    @Override
    public WordDataPo selectWordDataById(String reportId)
    {
        WordDataPo wordDataPo = wordDataMapper.selectWordDataById(reportId);
        if (wordDataPo != null) {
            List<ReportDataSet> reportDataSetList = reportDataSetMapper.selectReportDataSetListByReportId(reportId);
            if (reportDataSetList!=null){
                //相同dataSetId的设置成一个DataSet对象
                Set<Long> dataSetIdSet = new HashSet<>();
                for(ReportDataSet reportDataSet : reportDataSetList){
                    dataSetIdSet.add(reportDataSet.getDataSetId());
                }
                for (Long dataSetId : dataSetIdSet){
                    List<String> dataSetColumnList = new ArrayList<>();
                    for (ReportDataSet reportDataSet : reportDataSetList){
                        if (reportDataSet.getDataSetId().equals(dataSetId)){
                            dataSetColumnList.add(reportDataSet.getDataSetColumn());
                        }
                    }
                    DataSet dataSet = new DataSet();
                    dataSet.setDataSetId(dataSetId);
                    dataSet.setDataSetColumnList(dataSetColumnList);
                    wordDataPo.getDataSetList().add(dataSet);
                }
            }
        }
        return wordDataMapper.selectWordDataById(reportId);
    }

    /**
     * 查询报告管理列表
     * 
     * @param wordData 报告管理
     * @return 报告管理
     */
    @Override
    public List<WordData> selectWordDataList(WordData wordData)
    {
        return wordDataMapper.selectWordDataList(wordData);
    }

    /**
     * 新增报告管理
     * 
     * @param wordDataPo 报告管理
     * @return 结果
     */
    @Override
    public int insertWordData(WordDataPo wordDataPo)
    {
        //当前用户信息
        LoginUser sysUser = null;
        try {
            sysUser = (LoginUser) SecurityUtils.getLoginUser();
            if (sysUser != null) {
                wordDataPo.setCreater(sysUser.getUsername());
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            return 0;
        }
        WordDataPo oldWordData = selectWordDataById(wordDataPo.getReportId());
        WordData wordData2 = wordDataMapper.selectWordDataByWordDataId(wordDataPo.getWordDataId());
        if (oldWordData == null && wordData2==null) {
            //新增
            wordDataPo.setUpdateTime(DateUtils.getNowDate());
            wordDataPo.setUpdater(sysUser.getUsername());
            wordDataPo.setCreater(sysUser.getUsername());
            wordDataPo.setCreateTime(DateUtils.getNowDate());
            wordDataPo.setWordDataId(IdUtils.simpleUUID());
            //新增数据集id及字段
            List<ReportDataSet> reportDataSetList = new ArrayList<>();
            if (wordDataPo.getDataSetList() != null) {
                for (DataSet dataSet : wordDataPo.getDataSetList()) {
                    if (dataSet.getDataSetId() != null && dataSet.getDataSetColumnList() != null) {
                        for (String dataSetColumn : dataSet.getDataSetColumnList()) {
                            ReportDataSet reportDataSet = new ReportDataSet();
                            reportDataSet.setReportId(wordDataPo.getReportId());
                            reportDataSet.setReportDataSetId(IdUtils.simpleUUID());
                            reportDataSet.setDataSetId(dataSet.getDataSetId());
                            reportDataSet.setDataSetColumn(dataSetColumn);
                            reportDataSetList.add(reportDataSet);
                        }
                        reportDataSetMapper.insertReportDataSetBatch(reportDataSetList, wordDataPo.getReportId());
                    }
                }
            }else {
                reportDataSetMapper.deleteReportDataSetByReportId(wordDataPo.getReportId());
            }
            return wordDataMapper.insertWordData(wordDataPo);
        }else if (oldWordData!=null && wordData2!=null){
            //修改
            wordDataPo.setUpdater(sysUser.getUsername());
            wordDataPo.setUpdateTime(DateUtils.getNowDate());
            List<ReportDataSet> reportDataSetList = new ArrayList<>();
            for(DataSet dataSet : wordDataPo.getDataSetList()) {
                if (dataSet.getDataSetId() != null && dataSet.getDataSetColumnList() != null) {
                    for (String dataSetColumn : dataSet.getDataSetColumnList()) {
                        ReportDataSet reportDataSet = new ReportDataSet();
                        reportDataSet.setReportId(wordDataPo.getReportId());
                        reportDataSet.setReportDataSetId(IdUtils.simpleUUID());
                        reportDataSet.setDataSetId(dataSet.getDataSetId());
                        reportDataSet.setDataSetColumn(dataSetColumn);
                        reportDataSetList.add(reportDataSet);
                    }
                reportDataSetMapper.deleteReportDataSetByReportId(wordDataPo.getReportId());
                reportDataSetMapper.insertReportDataSetBatch(reportDataSetList,wordDataPo.getReportId());
                }
            }
            return wordDataMapper.updateWordData(wordDataPo);
        }else{
            return 0;
        }
    }

    /**
     * 修改报告管理
     * 
     * @param wordDataPo 报告管理
     * @return 结果
     */
    @Override
    public int updateWordData(WordDataPo wordDataPo)
    {
        wordDataPo.setUpdateTime(DateUtils.getNowDate());
        return wordDataMapper.updateWordData(wordDataPo);
    }

    /**
     * 批量删除报告管理
     * 
     * @param wordDataIds 需要删除的报告管理主键
     * @return 结果
     */
    @Override
    public int deleteWordDataByWordDataIds(String wordDataIds)
    {
        return wordDataMapper.deleteWordDataByWordDataIds(Convert.toStrArray(wordDataIds));
    }

    /**
     * 删除报告管理信息
     * 
     * @param wordDataId 报告管理主键
     * @return 结果
     */
    @Override
    public int deleteWordDataByWordDataId(String wordDataId)
    {
        return wordDataMapper.deleteWordDataByWordDataId(wordDataId);
    }
}
