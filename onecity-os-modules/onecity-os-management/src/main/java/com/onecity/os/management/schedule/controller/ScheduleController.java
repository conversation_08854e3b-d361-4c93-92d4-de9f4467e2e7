package com.onecity.os.management.schedule.controller;


import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.management.schedule.service.UserSynchroService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;


/**
 * <AUTHOR>
 * @since 2020-02-27 15:51:52
 */
@Controller
@RequestMapping("/schedule")
@Slf4j
public class ScheduleController {

    @Autowired
    private UserSynchroService userSynchroService;

    @ApiOperation(value = "逾期指标计算触发")
    @PostMapping("/testUserSynchro")
    public BaseResult testUserSynchro() {
        userSynchroService.UserSynchro();
        return BaseResult.ok();
    }


}