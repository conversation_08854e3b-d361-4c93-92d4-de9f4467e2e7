package com.onecity.os.management.schedule.entity;

import lombok.Data;

/**
 * @Author: zack
 * @Date: 2022/12/23 00:07
 */
@Data
public class OneOfficeRspHead {

    /**
     * responseId
     */
    private String responseId;

    /**
     * 时间戳
     */
    private Long timeStamp;

    /**
     * 响应结果代码 0成功1失败
     */
    private String resultCode;

    /**
     * 响应结果描述
     */
    private String resultDesc;

    /**
     *错误代码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorInfo;

    /**
     * 请求消息ID
     */
    private String requestId;

}
