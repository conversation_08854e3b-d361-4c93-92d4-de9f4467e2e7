package com.onecity.os.management.schedule.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;

/**
 * @Author: zack
 * @Date: 2022/12/22 17:37
 */
@Data
public class UserSynRequest {

    /**
     * 租户ID
     */
    private String tenementId;

    /**
     * 组织架构ID
     */
    private String orgId;


    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间", example = "1990-01-01 00:00:00")
    private java.util.Date modifyTime;
}
