package com.onecity.os.management.schedule.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Author: zack
 * @Date: 2022/12/22 17:44
 */
@Data
public class UserSynResponse {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 所属租户ID
     */
    private String tenementId;

    /**
     * 所属组织架构ID
     */
    private String orgId;

    /**
     * 用户登录账号
     */
    private String userAccount;

    /**
     * 用户头像
     */
    private String userPhoto;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 性别代码 1：男 2：女
     */
    private Integer genderCode;

    /**
     * 出生日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "出生日期", example = "1990-01-01 00:00:00")
    private java.util.Date birthDate;

    /**
     * 身份证号
     */
    private String idNumber;

    /**
     * 职务
     */
    private String position;

    /**
     * 员工编号
     */
    private String empNumber;

    /**
     * 入职日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "入职日期", example = "1990-01-01 00:00:00")
    private java.util.Date entryDate;

    /**
     * 移动电话
     */
    private String mobileTelephone;

    /**
     * 办公电话
     */
    private String officePhone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 序号
     */
    private Integer serialNumber;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态标识 true：正常；false：删除
     */
    private Boolean statusFlag;

    /**
     * 停启用标识 true：正常；false：停用
     */
    private Boolean availableFlag;

    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间", example = "1990-01-01 00:00:00")
    private java.util.Date modifyTime;

}
