package com.onecity.os.management.statisticanalysis.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.poi.ExcelUtil;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.onecity.os.management.api.vo.PageResult;
import com.onecity.os.management.configmanage.mapper.SourceManageMapper;
import com.onecity.os.management.configmanage.service.RemindInfoService;
import com.onecity.os.management.zhibiao.mapper.GeneralIndicatorMapper;
import com.onecity.os.management.zhibiao.service.IndicatorService;
import com.onecity.os.system.api.model.LoginUser;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import com.onecity.os.management.statisticanalysis.entity.dto.*;
import com.onecity.os.management.statisticanalysis.service.IndicatorStatisticAnalysisService;
import com.onecity.os.management.utils.BeanHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 指标统计分析
 *
 * <AUTHOR>
 * @date 2021/1/25 下午2:58
 */
@Slf4j
@RestController
@RequestMapping("/indicatorStatisticAnalysis")
@Api(tags = "统计分析")
public class IndicatorStatisticAnalysis extends BaseController {

    @Autowired
    private IndicatorStatisticAnalysisService indicatorStatisticAnalysisService;

    @Autowired
    private RemindInfoService remindInfoService;

    @Resource
    private SourceManageMapper sourceManageMapper;

    @Autowired
    private IndicatorService indicatorService;

    @Resource
    private GeneralIndicatorMapper generalIndicatorMapper;

    /**
     * 旧版本--获取指标统计分析列表
     *
     * @param updateCycle     更新周期
     * @param startUpdateTime 开始更新时间
     * @param endUpdateTime   结束更新时间
     * @param sourceName      板块名称
     * @param isHistory       0-实时数据;1-历史数据
     * @param isOverDue       是否逾期0-否;1-是
     * @param version         版本0-旧版;1-新版
     * @return
     */
    @ApiOperation(value = "旧版本--获取指标统计分析列表")
    @GetMapping("/getStatisticAnalysisPageList")
    public TableDataInfo getStatisticAnalysisPageList(
            @RequestParam(name = "updateCycle", required = false) String updateCycle,
            @RequestParam(name = "startUpdateTime", required = false) String startUpdateTime,
            @RequestParam(name = "endUpdateTime", required = false) String endUpdateTime,
            @RequestParam(name = "sourceName", required = false) String sourceName,
            @RequestParam(name = "isHistory", defaultValue = "0", required = false) String isHistory,
            @RequestParam(name = "isOverDue", required = false) String isOverDue,
            @RequestParam(name = "version") Byte version) {
        startPage();
        List<GetStatisticAnalysisPageListDto> pageResult = indicatorStatisticAnalysisService.getStatisticAnalysisPageList(updateCycle,
                startUpdateTime, endUpdateTime, sourceName, isHistory, isOverDue, version);
        return getDataTable(pageResult);
    }

    /**
     * 新版本--获取指标统计分析列表
     *
     * @param sourceName 板块名称
     * @return
     */
    @ApiOperation(value = "新版本--获取指标统计分析列表")
    @GetMapping("/getNewVerStatisticAnalysisPageList")
    public TableDataInfo getNewVerStatisticAnalysisPageList(
            @RequestParam(name = "sourceName", required = false) String sourceName) throws ParseException {
        startPage();
        List<GetStatisticAnalysisPageListDto> pageResult = indicatorStatisticAnalysisService.getNewVerStatisticAnalysisPageList(sourceName);
        return getDataTable(pageResult);
    }


    /**
     * 根据id,获取指标统计分析详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id,获取指标统计分析详情")
    @GetMapping("/getStatisticInfoById")
    public BaseResult getStatisticInfoById(@RequestParam(name = "id") Long id,
                                           @RequestParam(name = "dateTime", required = false) String dateTime,
                                           @RequestParam(name = "page", defaultValue = "1") Integer page,
                                           @RequestParam(name = "size", defaultValue = "10") Integer size) {
        GetStatisticInfoByIdDto dto = indicatorStatisticAnalysisService.getStatisticInfoById(id, dateTime);
        if (ObjectUtils.isEmpty(dto)) {
            return BaseResult.fail("未找到相关信息");
        }
        return BaseResult.ok(dto);
    }

    /**
     * 根据厅局id,获取指标统计分析详情
     *
     * @param sourceId
     * @return
     */
    @ApiOperation(value = "根据id,获取指标统计分析详情")
    @GetMapping("/getStatisticInfoBySourceId")
    public TableDataInfo getStatisticInfoBySourceId(@RequestParam(name = "sourceId") Long sourceId) throws ParseException {

        String sourceSimpleName = sourceManageMapper.getSourceSimpleNameBySourceId(sourceId);
        List<String> indicatorParentIds = generalIndicatorMapper.getParentIndicatorIdsBySourceId(sourceSimpleName);
        // 所有指标和分组
        List<String> allIndicatorIds = indicatorStatisticAnalysisService.getIndicatorIdsByParentIds(indicatorParentIds, new ArrayList<>());
        startPage();
        List<GetStatisticInfoByIdDto> pageResult = indicatorStatisticAnalysisService.getStatisticInfoBySourceId(indicatorParentIds,allIndicatorIds, sourceId);
        return getDataTable(pageResult);
    }

    /**
     * 根据厅局id,导出指标统计分析详情excel
     *
     * @param sourceId
     */
    @RequestMapping(value = "/exportStatisticDetailXls")
    @Log(title = "更新统计-详情页-导出报表",businessType = BusinessType.EXPORT)
    public void exportStatisticDetailXls(@RequestParam(name = "sourceId") String sourceId, @RequestParam(name = "title") String title,
                                         HttpServletResponse response) throws IOException {
        String sourceSimpleName = sourceManageMapper.getSourceSimpleNameBySourceId(Long.valueOf(sourceId));
        List<String> indicatorParentIds = generalIndicatorMapper.getParentIndicatorIdsBySourceId(sourceSimpleName);
        // 所有指标和分组
        List<String> allIndicatorIds = indicatorStatisticAnalysisService.getIndicatorIdsByParentIds(indicatorParentIds, new ArrayList<>());
        List<GetStatisticInfoByIdDto> pageResult = indicatorStatisticAnalysisService.getStatisticInfoBySourceId(indicatorParentIds, allIndicatorIds, Long.valueOf(sourceId));
        LoginUser user = SecurityUtils.getLoginUser();
        String userName = "";
        if (ObjectUtils.isNotEmpty(user)) {
            userName = user.getSysUser().getNickName();
        }
        ExcelUtil<GetStatisticInfoByIdDto> excelUtil = new ExcelUtil<>(GetStatisticInfoByIdDto.class);
        if (ObjectUtils.isNotEmpty(pageResult)) {
            excelUtil.exportExcel(response, pageResult, pageResult.get(0).getTitle(), pageResult.get(0).getTitle() + "导出人：" + userName);
        } else {
            SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy年MM月");
            excelUtil.exportExcel(response, pageResult, title, title + dateFormat2.format(new Date()) + "指标更新统计分析详情" + "导出人：" + userName);
        }
    }

    /**
     * 导出统计分析excel
     *
     * @param isOverDue
     */
    @RequestMapping(value = "/exportStatisticInfoXls")
    @Log(title = "指标统计分析-导出报表", businessType = BusinessType.EXPORT)
    public void exportStatisticInfoXls(@RequestParam(name = "isHistory") String isHistory, @RequestParam(name = "isOverDue") String isOverDue,
                                       @RequestParam(name = "updateCycle") String updateCycle, @RequestParam(name = "startUpdateTime") String startUpdateTime,
                                       @RequestParam(name = "endUpdateTime") String endUpdateTime, @RequestParam(name = "sourceName") String sourceName,
                                       HttpServletResponse response) {
        // 导出文件名称
        List<ExportStatisticInfoXlsDto> dtoList = indicatorStatisticAnalysisService.getExportStatisticInfoXls(
                isHistory, isOverDue, updateCycle,
                startUpdateTime, endUpdateTime, sourceName);
        //追加更新率计算
        dtoList.forEach(dto -> {
            if (StringUtils.isNotEmpty(dto.getActualUpdate()) && StringUtils.isNotEmpty(dto.getShouldUpdate())) {
                NumberFormat percentFormat = NumberFormat.getPercentInstance();
                BigDecimal shouldUpdate = new BigDecimal(dto.getShouldUpdate().substring(0, dto.getShouldUpdate().length() - 1));
                BigDecimal actualUpdate = new BigDecimal(dto.getActualUpdate().substring(0, dto.getActualUpdate().length() - 1));
                if (BigDecimal.ZERO.equals(shouldUpdate) || BigDecimal.ZERO.equals(actualUpdate)) {
                    dto.setUpdateRatio("0%");
                } else {
                    String renewalRate = percentFormat.format(actualUpdate.divide(shouldUpdate, 2, RoundingMode.HALF_UP));
                    dto.setUpdateRatio(renewalRate);
                }
            }
        });
        LoginUser user = SecurityUtils.getLoginUser();
        String userName = "";
        if (ObjectUtils.isNotEmpty(user)) {
            userName = user.getSysUser().getNickName();
        }
        ExcelUtil<ExportStatisticInfoXlsDto> excelUtil = new ExcelUtil<>(ExportStatisticInfoXlsDto.class);
        try {
            excelUtil.exportExcel(response,dtoList,"指标统计分析数据","指标统计分析数据"+"导出人：" + userName);
        } catch (IOException e) {
            log.info("导出失败，错误信息为："+e.getMessage());
        }
    }

    @ApiOperation(value = "新版本--指标更新统计")
    @GetMapping("/getIndexCensusPageList")
    public TableDataInfo getIndexCensusPageList(@RequestParam(name = "startTime", required = true) String startTime,
                                                             @RequestParam(name = "endTime", required = true) String endTime,
                                                             @RequestParam(name = "sourceName", required = false) String sourceName,
                                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer page,
                                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer size) throws ParseException {
        startPage();
        List<IndexCensusDto> pageResult = indicatorStatisticAnalysisService.getIndexCensusPageList(startTime, endTime, sourceName, page, size);
        return getDataTable(pageResult);
    }

    //todo 导出待调整

    @ApiOperation(value = "新版本--指标更新统计--导出Excel")
    @GetMapping(value = "/exportIndexCensusXls")
    @Log(title = "更新统计-导出报表",businessType = BusinessType.EXPORT)
    public void exportIndexCensusXls(@RequestParam(name = "date", required = true) String date, HttpServletResponse response) throws Exception {

        List<GetStatisticAnalysisPageListDto> pageList = indicatorStatisticAnalysisService.getUpdateStatistics(date);
        List<IndexCensusExcelBean> dataList = pageList.stream()
                .map(indexCensusDto -> {
                    IndexCensusExcelBean bean = BeanHelper.copyProperties(indexCensusDto, IndexCensusExcelBean.class);
                    //年度
                    bean.setYearTotalCount(indexCensusDto.getYearIndex().getTotalCount());
                    bean.setYearActualCount(indexCensusDto.getYearIndex().getActualCount());
                    bean.setYearShouldCount(indexCensusDto.getYearIndex().getShouldCount());
                    bean.setYearUnUpdateCount(indexCensusDto.getYearIndex().getUnUpdateCount());
                    bean.setYearOverdueCount(indexCensusDto.getYearIndex().getOverdueCount());
                    //半年度
                    bean.setHalfYearTotalCount(indexCensusDto.getHalfYearIndex().getTotalCount());
                    bean.setHalfYearActualCount(indexCensusDto.getHalfYearIndex().getActualCount());
                    bean.setHalfYearShouldCount(indexCensusDto.getHalfYearIndex().getShouldCount());
                    bean.setHalfYearUnUpdateCount(indexCensusDto.getHalfYearIndex().getUnUpdateCount());
                    bean.setHalfYearOverdueCount(indexCensusDto.getHalfYearIndex().getOverdueCount());
                    //季度
                    bean.setSeasonTotalCount(indexCensusDto.getSeasonIndex().getTotalCount());
                    bean.setSeasonActualCount(indexCensusDto.getSeasonIndex().getActualCount());
                    bean.setSeasonShouldCount(indexCensusDto.getSeasonIndex().getShouldCount());
                    bean.setSeasonUnUpdateCount(indexCensusDto.getSeasonIndex().getUnUpdateCount());
                    bean.setSeasonOverdueCount(indexCensusDto.getSeasonIndex().getOverdueCount());
                    //月度
                    bean.setMonthTotalCount(indexCensusDto.getMonthIndex().getTotalCount());
                    bean.setMonthActualCount(indexCensusDto.getMonthIndex().getActualCount());
                    bean.setMonthShouldCount(indexCensusDto.getMonthIndex().getShouldCount());
                    bean.setMonthUnUpdateCount(indexCensusDto.getMonthIndex().getUnUpdateCount());
                    bean.setMonthOverdueCount(indexCensusDto.getMonthIndex().getOverdueCount());
                    //当月合计
                    bean.setTotalCount(indexCensusDto.getTotalCount());
                    bean.setShouldCount(indexCensusDto.getShouldCount());
                    bean.setChangedCount(indexCensusDto.getChangedCount());
                    //信息简报
                    bean.setLittleMessageCount(indexCensusDto.getLittleMessageCount());
                    int i = pageList.indexOf(indexCensusDto);
                    bean.setIndex(i + 1);
                    return bean;
                })
                .collect(Collectors.toList());


        LoginUser user = SecurityUtils.getLoginUser();
        String userName = "";
        if (ObjectUtils.isNotEmpty(user)) {
            userName = user.getUsername();
        }
        String secondTitle = "导出人：" + userName + "  时间：" + date;
        ExcelUtil<IndexCensusExcelBean> excelUtil = new ExcelUtil<>(IndexCensusExcelBean.class);
        excelUtil.exportExcel(response,dataList,"指标更新统计结果","指标更新统计结果");
    }


    @ApiOperation(value = "指标更新统计-202204")
    @GetMapping("/getUpdateStatistics")
    public TableDataInfo getUpdateStatistics(@RequestParam(name = "date", required = true) String date,
                                                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        startPage();
        List<GetStatisticAnalysisPageListDto> pageResult = indicatorStatisticAnalysisService.getUpdateStatistics(date);
        return getDataTable(pageResult);
    }

    @ApiOperation(value = "月初月末统计分析触发")
    @GetMapping("/testAnalysisCal")
    public BaseResult testAnalysisCal(@RequestParam(name = "monFlag", required = true) Integer monFlag) {
        log.info("test AnalysisCal");
        remindInfoService.CHBNStatisticalAnalysisCal(monFlag);
        return BaseResult.ok();
    }

    @ApiOperation(value = "逾期指标计算触发")
    @PostMapping("/testOverDueRemind")
    public BaseResult testOverDueRemind() {
        log.info("test OverDueRemind");
        remindInfoService.overDueRemind();
        return BaseResult.ok();
    }

    @ApiOperation(value = "PC用户消息发送")
    @PostMapping("/testSendRemindMsg")
    public BaseResult testSendRemindMsg() {
        log.info("test sendRemindMsg");
        remindInfoService.sendRemindMsg();
        return BaseResult.ok();
    }

    @ApiOperation(value = "指标更新统计刷新接口")
    @PostMapping("/updateData")
    public BaseResult updateData(@RequestParam(name = "source", defaultValue = "ALL") String source) throws Exception {
        // 获取当前登录的用户信息
        LoginUser sysUser = SecurityUtils.getLoginUser();
        String userName = null;
        if (ObjectUtil.isNotNull(sysUser)) {
            userName = sysUser.getUsername();
        } else {
            userName = "admin";
        }
        if (source.equals("ALL")) {
            List<String> sources = sourceManageMapper.getAllSourceManage();
            indicatorService.newStatisticAnalysis(sources, new Date(), userName);
        } else {
            indicatorService.newStatisticAnalysis(Collections.singletonList(source), new Date(), userName);
        }
        return BaseResult.ok();
    }

}































