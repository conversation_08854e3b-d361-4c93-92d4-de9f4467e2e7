package com.onecity.os.management.statisticanalysis.entity;


import io.swagger.models.auth.In;
import lombok.Data;

import java.util.Date;
import javax.persistence.*;

@Data
@Table(name = "indicator_statistics_analysis")
public class IndicatorStatisticsAnalysis {
    /**
     * 主键id
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    @TableId(value = "id", type = IdType.AUTO)
    @Id
    private Long id;

    /**
     * 厅局模块的主键id
     */
    @Column(name = "source_id")
    private Integer sourceId;

    /**
     * 标题
     */
    @Column(name = "title")
    private String title;

    /**
     * 指标更新周期
     */
    @Column(name = "update_cycle")
    private String updateCycle;

    /**
     * 更新情况0-未更新;1-已更新;2-部分更新
     */
    @Column(name = "is_update")
    private Byte isUpdate;

    /**
     * 是否逾期0-否;1-是
     */
    @Column(name = "is_over_due")
    private Byte isOverDue;

    /**
     * 应该更新指标数量
     */
    @Column(name = "should_update")
    private String shouldUpdate;

    /**
     * 指标总数(包含:月,季度,半年,年度)
     */
    @Column(name = "total_update")
    private String totalUpdate;

    /**
     * 实际更新指标数量
     */
    @Column(name = "actual_update")
    private String actualUpdate;

    /**
     * 逾期更新指标数量
     */
    @Column(name = "over_due_update")
    private String overDueUpdate;

    /**
     * 未更新指标数量
     */
    @Column(name = "not_update")
    private String notUpdate;

    /**
     * 缺更率(未更新指标/应更新指标)
     */
    @Column(name = "miss_update_ratio")
    private String missUpdateRatio;

    /**
     * 逾更率(逾期更新指标/已更新指标)
     */
    @Column(name = "over_due_update_ratio")
    private String overDueUpdateRatio;

    /**
     * 指标最后更新时间
     */
    @Column(name = "last_update_time")
    private Date lastUpdateTime;

    /**
     * 指标最后审核时间
     */
    @Column(name = "last_audit_time")
    private Date lastAuditTime;

    /**
     * 是否历史数据0:实时数据1:历史数据
     */
    @Column(name = "is_history")
    private Byte isHistory;

    /**
     * 是否删除0:否1:是
     */
    @Column(name = "is_delete")
    private Byte isDelete;

    /**
     * 审核人
     */
    @Column(name = "audit_name")
    private String auditName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;


    /**
     * 统计版本0:旧版1:新版
     */
    @Column(name = "version")
    private Byte version;

    /**
     * 指标总数; 用于统计统计分析页面展示时候的指标总数数据
     */
    @Column(name = "total_count")
    private int totalCount;

    /**
     * 数据时间，202207，该字段表示当前条数据的统计时间
     */
    @Column(name = "data_date")
    private String dataDate;

    /**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取厅局模块的主键id
     *
     * @return source_id - 厅局模块的主键id
     */
    public Integer getSourceId() {
        return sourceId;
    }

    /**
     * 设置厅局模块的主键id
     *
     * @param sourceId 厅局模块的主键id
     */
    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    /**
     * 获取指标更新周期
     *
     * @return update_cycle - 指标更新周期
     */
    public String getUpdateCycle() {
        return updateCycle;
    }

    /**
     * 设置指标更新周期
     *
     * @param updateCycle 指标更新周期
     */
    public void setUpdateCycle(String updateCycle) {
        this.updateCycle = updateCycle;
    }

    /**
     * 获取更新情况0-未更新;1-已更新;2-部分更新
     *
     * @return is_update - 更新情况0-未更新;1-已更新;2-部分更新
     */
    public Byte getIsUpdate() {
        return isUpdate;
    }

    /**
     * 设置更新情况0-未更新;1-已更新;2-部分更新
     *
     * @param isUpdate 更新情况0-未更新;1-已更新;2-部分更新
     */
    public void setIsUpdate(Byte isUpdate) {
        this.isUpdate = isUpdate;
    }

    /**
     * 获取是否逾期0-否;1-是
     *
     * @return is_over_due - 是否逾期0-否;1-是
     */
    public Byte getIsOverDue() {
        return isOverDue;
    }

    /**
     * 设置是否逾期0-否;1-是
     *
     * @param isOverDue 是否逾期0-否;1-是
     */
    public void setIsOverDue(Byte isOverDue) {
        this.isOverDue = isOverDue;
    }

    /**
     * 获取应该更新指标数量
     *
     * @return should_update - 应该更新指标数量
     */
    public String getShouldUpdate() {
        return shouldUpdate;
    }

    /**
     * 设置应该更新指标数量
     *
     * @param shouldUpdate 应该更新指标数量
     */
    public void setShouldUpdate(String shouldUpdate) {
        this.shouldUpdate = shouldUpdate;
    }

    /**
     * 获取实际更新指标数量
     *
     * @return actual_update - 实际更新指标数量
     */
    public String getActualUpdate() {
        return actualUpdate;
    }

    /**
     * 设置实际更新指标数量
     *
     * @param actualUpdate 实际更新指标数量
     */
    public void setActualUpdate(String actualUpdate) {
        this.actualUpdate = actualUpdate;
    }

    /**
     * 获取逾期更新指标数量
     *
     * @return over_due_update - 逾期更新指标数量
     */
    public String getOverDueUpdate() {
        return overDueUpdate;
    }

    /**
     * 设置逾期更新指标数量
     *
     * @param overDueUpdate 逾期更新指标数量
     */
    public void setOverDueUpdate(String overDueUpdate) {
        this.overDueUpdate = overDueUpdate;
    }

    /**
     * 获取未更新指标数量
     *
     * @return not_update - 未更新指标数量
     */
    public String getNotUpdate() {
        return notUpdate;
    }

    /**
     * 设置未更新指标数量
     *
     * @param notUpdate 未更新指标数量
     */
    public void setNotUpdate(String notUpdate) {
        this.notUpdate = notUpdate;
    }

    /**
     * 获取缺更率(未更新指标/应更新指标)
     *
     * @return miss_update_ratio - 缺更率(未更新指标/应更新指标)
     */
    public String getMissUpdateRatio() {
        return missUpdateRatio;
    }

    /**
     * 设置缺更率(未更新指标/应更新指标)
     *
     * @param missUpdateRatio 缺更率(未更新指标/应更新指标)
     */
    public void setMissUpdateRatio(String missUpdateRatio) {
        this.missUpdateRatio = missUpdateRatio;
    }

    /**
     * 获取逾更率(逾期更新指标/已更新指标)
     *
     * @return over_due_update_ratio - 逾更率(逾期更新指标/已更新指标)
     */
    public String getOverDueUpdateRatio() {
        return overDueUpdateRatio;
    }

    /**
     * 设置逾更率(逾期更新指标/已更新指标)
     *
     * @param overDueUpdateRatio 逾更率(逾期更新指标/已更新指标)
     */
    public void setOverDueUpdateRatio(String overDueUpdateRatio) {
        this.overDueUpdateRatio = overDueUpdateRatio;
    }

    /**
     * 获取指标最后更新时间
     *
     * @return last_update_time - 指标最后更新时间
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 设置指标最后更新时间
     *
     * @param lastUpdateTime 指标最后更新时间
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * 获取指标最后审核时间
     *
     * @return last_audit_time - 指标最后审核时间
     */
    public Date getLastAuditTime() {
        return lastAuditTime;
    }

    /**
     * 设置指标最后审核时间
     *
     * @param lastAuditTime 指标最后审核时间
     */
    public void setLastAuditTime(Date lastAuditTime) {
        this.lastAuditTime = lastAuditTime;
    }

    /**
     * 获取是否删除0:否1:是
     *
     * @return is_delete - 是否删除0:否1:是
     */
    public Byte getIsDelete() {
        return isDelete;
    }

    /**
     * 设置是否删除0:否1:是
     *
     * @param isDelete 是否删除0:否1:是
     */
    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}