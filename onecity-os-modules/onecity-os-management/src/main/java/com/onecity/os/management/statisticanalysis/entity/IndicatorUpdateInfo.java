package com.onecity.os.management.statisticanalysis.entity;

import lombok.Data;

import java.util.Date;

/**
 * @Author: yuk
 * @menu
 * @Description:
 * @Date: 2022/7/7 11:06
 * @Version: 1.0.0
 */
@Data
public class IndicatorUpdateInfo {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 指标的主键id
     */
    private String indicatorId;

    /**
     * 指标的标题
     */
    private String indicatorName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 是否通知钉钉端，0-否;1-是
     */
    private Integer isSendDing;

    /**
     * 板块名称
     */
    private String sourceName;

    /**
     * 是否是数据更新
     * @return
     */
    private String dataFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getIndicatorId() {
        return indicatorId;
    }

    public void setIndicatorId(String indicatorId) {
        this.indicatorId = indicatorId;
    }

    public String getIndicatorName() {
        return indicatorName;
    }

    public void setIndicatorName(String indicatorName) {
        this.indicatorName = indicatorName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getIsSendDing() {
        return isSendDing;
    }

    public void setIsSendDing(Integer isSendDing) {
        this.isSendDing = isSendDing;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }
    public String getDataFlag() {
        return dataFlag;
    }

    public void setDataFlag(String dataFlag) {
        this.dataFlag = dataFlag;
    }
}