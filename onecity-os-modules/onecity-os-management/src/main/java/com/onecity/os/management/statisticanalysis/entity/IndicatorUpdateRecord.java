package com.onecity.os.management.statisticanalysis.entity;


import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 指标的更新记录
 */
@Data
@Table(name = "general_indicator_tianbao_update_record")
public class IndicatorUpdateRecord {
    /**
     * 主键id
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    @TableId(value = "id", type = IdType.AUTO)
    @Id
    private Long id;

    /**
     * 指标的主键id
     */
    @Column(name = "indicator_id")
    private String indicatorId;

    /**
     * 指标的标题
     */
    @Column(name = "indicator_name")
    private String indicatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 是否通知钉钉端，0-否;1-是
     */
    @Column(name = "is_send_ding")
    private Integer isSendDing;

    /**
     * 是否是指标数据更新，0-否;1-是
     */
    @Column(name = "data_flag")
    private Integer dataFlag;
}
