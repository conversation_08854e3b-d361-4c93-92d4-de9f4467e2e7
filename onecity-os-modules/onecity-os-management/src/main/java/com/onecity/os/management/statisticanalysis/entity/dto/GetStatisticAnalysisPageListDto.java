package com.onecity.os.management.statisticanalysis.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;

/**
 * 获取指标统计分析列表出参
 *
 * <AUTHOR>
 * @date 2021/1/25 下午3:09
 */
@Data
public class GetStatisticAnalysisPageListDto {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 厅局id
     */
    private Integer sourceId;

    /**
     * 板块名称
     */
    private String sourceName;

    /**
     * 更新周期
     */
    private String updateCycle;

    /**
     * 更新情况 0-未更新;1-已更新;2-部分更新
     */
    private Integer isUpdate;

    /**
     * 板块类型
     */
    private String type;

    /**
     * 数据填报负责人ids
     */
    @JsonIgnore
    private String pcUserIds;

    /**
     * 数据填报负责人
     */
    private String pcUserNames;

    /**
     * 所属单位
     */
    private String departName;

    /**
     * 移动端负责人ids
     */
    @JsonIgnore
    private String appUserIds;

    /**
     * 移动端负责人
     */
    private String appUserNames;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date lastUpdateTime;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date lastAuditTime;

    /**
     * 是否逾期0-否;1-是
     */
    private Integer isOverDue;

    /**
     * 是否历史数据0:实时数据1:历史数据
     */
    private Integer isHistory;

    /**
     * 创建时间
     */
    @JsonIgnore
    private String creatTime;

    /**
     * 约定更新时间
     */
    @JsonIgnore
    private String planUpdateDate;

    /**
     * 厅局简称
     */
    @JsonIgnore
    private String sourceSimpleName;

    /**
     * 指标总数
     */
    private int totalCount;

    /**
     * 本月应更新指标数
     */
    private int shouldCount;

    /**
     * 本月实际更新指标数
     */
    private int actualCount;

    /**
     * 已更指标总数
     */
    private int changedCount;

    /**
     * 年度指标
     */
    private IndexCensusBean yearIndex;
    /**
     * 年度指标
     */
    private IndexCensusBean halfYearIndex;

    /**
     * 月度指标
     */
    private IndexCensusBean monthIndex;

    /**
     * 季度指标
     */
    private IndexCensusBean seasonIndex;

    /**
     * 信息简报 发布条数
     */
    private int littleMessageCount;
}

