package com.onecity.os.management.statisticanalysis.entity.dto;

import com.onecity.os.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class IndexCensusBean {

    /**
     * 总指标数
     */
    @Excel(name = "指标总数", width = 10)
    private int totalCount;
    /**
     * 应更新指标数
     */
    @Excel(name = "应更新数", width = 10)
    private int shouldCount;

    /**
     * 实际更新指标数
     */
    @Excel(name = "实更新数", width = 10)
    private int actualCount;

    /**
     * 未更新指标数
     */
    @Excel(name = "未更新数", width = 10)
    private int unUpdateCount;

    /**
     * 逾期指标数
     */
    @Excel(name = "逾期数", width = 10)
    private int overdueCount;
}