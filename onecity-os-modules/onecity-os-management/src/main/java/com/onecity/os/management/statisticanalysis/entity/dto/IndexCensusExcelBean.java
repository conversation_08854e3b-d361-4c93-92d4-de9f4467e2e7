package com.onecity.os.management.statisticanalysis.entity.dto;

import com.onecity.os.common.core.annotation.Excel;
import lombok.Data;

/**
 * 获取指标统计分析列表出参
 *
 * <AUTHOR>
 * @date 2021/1/25 下午3:09
 */
@Data
public class IndexCensusExcelBean {


    @Excel(name = "序号", width = 15)
    private int index;

    /**
     * 板块名称
     */
    @Excel(name = "单位名称", width = 15)
    private String sourceName;


    /**
     * 年度指标===============================
     */

    /**
     * 总指标数
     */
    @Excel(name = "年度指标-指标总数", width = 10)
    private int yearTotalCount;
    /**
     * 应更新指标数
     */
    @Excel(name = "年度指标-应更新数", width = 10)
    private int yearShouldCount;

    /**
     * 实际更新指标数
     */
    @Excel(name = "年度指标-实更新数", width = 10)
    private int yearActualCount;

    /**
     * 未更新指标数
     */
    @Excel(name = "年度指标-未更新数", width = 10)
    private int yearUnUpdateCount;

    /**
     * 逾期指标数
     */
    @Excel(name = "年度指标-逾期数", width = 10)
    private int yearOverdueCount;

    /**
     * 半年度指标========================================
     */
    /**
     * 总指标数
     */
    @Excel(name = "半年度指标-指标总数", width = 10)
    private int halfYearTotalCount;
    /**
     * 应更新指标数
     */
    @Excel(name = "半年度指标-应更新数", width = 10)
    private int halfYearShouldCount;

    /**
     * 实际更新指标数
     */
    @Excel(name = "半年度指标-实更新数", width = 10)
    private int halfYearActualCount;

    /**
     * 未更新指标数
     */
    @Excel(name = "半年度指标-未更新数", width = 10)
    private int halfYearUnUpdateCount;

    /**
     * 逾期指标数
     */
    @Excel(name = "半年度指标-逾期数", width = 10)
    private int halfYearOverdueCount;


    /**
     * 季度指标=======================================
     */
    /**
     * 总指标数
     */
    @Excel(name = "季度指标-指标总数", width = 10)
    private int seasonTotalCount;
    /**
     * 应更新指标数
     */
    @Excel(name = "季度指标-应更新数", width = 10)
    private int seasonShouldCount;

    /**
     * 实际更新指标数
     */
    @Excel(name = "季度指标-实更新数", width = 10)
    private int seasonActualCount;

    /**
     * 未更新指标数
     */
    @Excel(name = "季度指标-未更新数", width = 10)
    private int seasonUnUpdateCount;

    /**
     * 逾期指标数
     */
    @Excel(name = "季度指标-逾期数", width = 10)
    private int seasonOverdueCount;


    /**
     * 月度指标=======================================
     */
    /**
     * 总指标数
     */
    @Excel(name = "月度指标-指标总数", width = 10)
    private int monthTotalCount;
    /**
     * 应更新指标数
     */
    @Excel(name = "月度指标-应更新数", width = 10)
    private int monthShouldCount;

    /**
     * 实际更新指标数
     */
    @Excel(name = "月度指标-实更新数", width = 10)
    private int monthActualCount;

    /**
     * 未更新指标数
     */
    @Excel(name = "月度指标-未更新数", width = 10)
    private int monthUnUpdateCount;

    /**
     * 逾期指标数
     */
    @Excel(name = "月度指标-逾期数", width = 10)
    private int monthOverdueCount;


    /**
     * 当月合计-总数
     */
    @Excel(name = "当月合计-总数", width = 10)
    private int totalCount;

    /**
     * 当月合计-应更
     */
    @Excel(name = "当月合计-应更", width = 10)
    private int shouldCount;

    /**
     * 当月合计-已更
     */
    @Excel(name = "当月合计-已更", width = 10)
    private int changedCount;

    /**
     * 信息简报 发布条数
     */
    @Excel(name = "工作动态-发布条数", width = 25)
    private int littleMessageCount;


    @Excel(name = "备注", width = 100)
    private String otherInfo;

}

