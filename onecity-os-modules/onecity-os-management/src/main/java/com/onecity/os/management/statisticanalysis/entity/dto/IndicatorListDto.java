package com.onecity.os.management.statisticanalysis.entity.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class IndicatorListDto {

    // 月度逾期,未更新,准时更新
    private List<String> monthOverdueUpdate =new ArrayList<>();
    private List<String> monthNotUpdate =new ArrayList<>();
    private List<String> monthOnTimeUpdate =new ArrayList<>();
    private List<String> monthNotOverdueUpdate =new ArrayList<>();
    // 季度逾期,未更新,准时更新
    private List<String> seasonOverdueUpdate =new ArrayList<>();
    private List<String> seasonNotUpdate =new ArrayList<>();
    private List<String> seasonOnTimeUpdate =new ArrayList<>();
    // 半年逾期,未更新,准时更新,未更新且未逾期的
    private List<String> halfYearOverdueUpdate =new ArrayList<>();
    private List<String> halfYearNotUpdate =new ArrayList<>();
    private List<String> halfYearOnTimeUpdate =new ArrayList<>();
    // 年逾期,未更新,准时更新
    private List<String> yearOverdueUpdate =new ArrayList<>();
    private List<String> yearNotUpdate =new ArrayList<>();
    private List<String> yearOnTimeUpdate =new ArrayList<>();
    private List<String> yearNotOverdueUpdate =new ArrayList<>();
    // 月度,季度,半年,年度的指标
    private List<String> monthIndicators =new ArrayList<>();
    private List<String> seasonIndicators =new ArrayList<>();
    private List<String> halfYearIndicators =new ArrayList<>();
    private List<String> yearIndicators =new ArrayList<>();
    // 季度,半年,年度的指标
    private List<String> seasonShouldIndicators =new ArrayList<>();
    private List<String> halfYearShouldIndicators =new ArrayList<>();
    private List<String> yearShouldIndicators =new ArrayList<>();
}
