package com.onecity.os.management.statisticanalysis.mapper;


import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.management.statisticanalysis.entity.IndicatorStatisticsAnalysis;
import com.onecity.os.management.statisticanalysis.entity.dto.ExportStatisticInfoXlsDto;
import com.onecity.os.management.statisticanalysis.entity.dto.GetStatisticAnalysisPageListDto;
import com.onecity.os.management.statisticanalysis.entity.dto.GetStatisticInfoByIdDto;
import com.onecity.os.management.zhibiao.model.dto.IndicatorCycleDto;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface IndicatorStatisticsAnalysisMapper extends BaseMapper<IndicatorStatisticsAnalysis> {
    /**
     * 获取指标统计分析列表
     *
     * @param updateCycle
     * @param startUpdateTime
     * @param endUpdateTime
     * @param sourceName
     * @param isHistory
     * @param isOverDue
     * @param version
     * @return
     */
    List<GetStatisticAnalysisPageListDto> getStatisticAnalysisHistoryPageList(@Param("updateCycle") String updateCycle,
                                                                               @Param("startUpdateTime") String startUpdateTime,
                                                                               @Param("endUpdateTime") String endUpdateTime,
                                                                               @Param("sourceName") String sourceName,
                                                                               @Param("isHistory") String isHistory,
                                                                               @Param("isOverDue") String isOverDue,
                                                                               @Param("version") Byte version);

    /**
     * 根据id,获取指标统计分析详情
     *
     * @param id
     * @return
     */
    GetStatisticInfoByIdDto getStatisticInfoById(@Param("id") Long id);

    /**
     * 根据指标统计分析id,获取指标最新更新时间
     *
     * @param id
     * @return
     */
    Date getLastestTimebyStatisticsId(@Param("id") Long id);

    /**
     * 根据厅局id和更新情况,查找是否有最新统计数据,有的话更新,没有的话新增
     *
     * @param sourceId
     * @param updateCycle
     * @param dataDate
     * @return
     */
    Long getStatisticSourceIdBySourceSimpleName(@Param("sourceId") Integer sourceId, @Param("updateCycle") String updateCycle,
                                                @Param("dataDate") String dataDate);

    /**
     * 导出统计分析excel
     *
     * @param isHistory
     * @param isOverDue
     * @param updateCycle
     * @param startUpdateTime
     * @param endUpdateTime
     * @param isHistory
     * @return
     */
    List<ExportStatisticInfoXlsDto> getExportStatisticListByIsHistory(@Param("isHistory") String isHistory,
                                                                      @Param("isOverDue") String isOverDue,
                                                                      @Param("updateCycle") String updateCycle,
                                                                      @Param("startUpdateTime") String startUpdateTime,
                                                                      @Param("endUpdateTime") String endUpdateTime,
                                                                      @Param("sourceName") String sourceName);

    /**
     * 根据指标ids,分页查找指标
     *
     * @param allIndicators
     * @return
     */
    List<GetStatisticInfoByIdDto> getStatisticInfoBySourceId(@Param("ids") List<String> allIndicators);

    /**
     * 根据厅局id,查找审核时间,审核人
     *
     * @param sourceId
     * @return
     */
    GetStatisticInfoByIdDto getAuditNameBySourceId(@Param("sourceId") Long sourceId);

    /**
     * 根据sourceId,查找标题名
     *
     * @param sourceId
     * @return
     */
    String getTitleBySourceId(@Param("sourceId") Long sourceId);

    /**
     * 根据指标ids,查找所有指标
     *
     * @param allIndicatorIds
     * @return
     */
    List<GetStatisticInfoByIdDto> getAllIndicatorByIndicatorIds(@Param("ids") List<String> allIndicatorIds);

    /**
     * 获取历史整合数据
     * @param sourceIds sourceIds
     * @param date date
     * @return IndicatorStatisticsAnalysis
     */
    List<IndicatorStatisticsAnalysis> getAnalysisBysourceId(@Param("sourceIds") Set<Integer> sourceIds, @Param("date") String date);

    /**
     * 根据sourceId,
     * @param sourceId
     * @param dataDate
     * @param isHistory
     * @return
     */
    List<IndicatorStatisticsAnalysis> getAnalysisBySource(@Param("sourceId") Long sourceId, @Param("updateCycle") String updateCycle, @Param("dataDate") String dataDate, @Param("isHistory") String isHistory);

    /**
     * 根据sourceId,
     * @param sourceId
     * @param dataDate
     * @param isHistory
     * @return 参数
     */
    List<IndicatorStatisticsAnalysis> getAnalysisByParameter(@Param("sourceId") Integer sourceId, @Param("dataDate") String dataDate, @Param("isHistory") String isHistory);

    /**
     * 根据source_id，is_history,date_date更新indicator_statistics_analysis表
     */
    void updateBySourceIdAndDateDateAndIsHistory(@Param("vo") IndicatorStatisticsAnalysis vo,@Param("sourceId") Integer sourceId,@Param("isHistory") int isHistory,@Param("dateDate") String dateDate,@Param("updateLastUpdateTimeFlag") int updateLastUpdateTimeFlag,
                                                 @Param("updateCycle") String updateCycle);

    /**
     * 根据source_id，is_history,date_date更新indicator_statistics_analysis表
     */
    void updateBySourceIdAndDateDateAndIsHistoryNoFlag(@Param("vo") IndicatorStatisticsAnalysis vo,@Param("sourceId") Integer sourceId,@Param("isHistory") int isHistory,@Param("dateDate") String dateDate,
                                                       @Param("updateCycle") String updateCycle);

    void updateHistoryFlag(@Param("sourceId") Integer sourceId, @Param("updateCycle") String updateCycle,
                           @Param("dataDate") String dataDate);

    /**
     * 获取当月统计更新生成数据
     * 用于校验是否成功生成数据
     * @return
     */
    List<IndicatorStatisticsAnalysis> getAnalysisHistory();
}










