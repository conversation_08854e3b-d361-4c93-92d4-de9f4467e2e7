package com.onecity.os.management.statisticanalysis.mapper;


import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.management.statisticanalysis.entity.IndicatorUpdateRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

public interface IndicatorUpdateRecordMapper extends BaseMapper<IndicatorUpdateRecord> {

    /**
     * 获取 更新记录
     * data_flag 不传则查询所有 不限制指标更新还是指标数据更新
     * @return
     */
    List<IndicatorUpdateRecord> getUpdateRecordList(@Param("startTime") String startTime,
                                                    @Param("endTime") String endTime,
                                                    @Param("indicatorIdList") List<String> indicatorIdList,
                                                    @Param("dataFlag") Integer dataFlag);

    /**
     * 增加 更新的记录
     * @param data
     */
    void insertIndicatorUpdateRecord(@Param("data") IndicatorUpdateRecord data);
    /**
     * 批量增加 更新的记录
     * @param dataList
     */
    @Async
    void insertIndicatorUpdateRecordBatch(@Param("dataList") List<IndicatorUpdateRecord> dataList);

    /**
     * 修改更新的指标的发送通知状态
     * @param indicatorIdList
     */
    void updateSendByIndicatorIds(@Param("indicatorIdList")List<String> indicatorIdList);

    /**
     * 根据指标id查询关注的用户id
     * @param
     */
    String selectAttentionUsersByIndicatorId(@Param("indicatorId")String indicatorId);
}
