package com.onecity.os.management.statisticanalysis.service;


import com.onecity.os.management.statisticanalysis.entity.dto.ExportStatisticInfoXlsDto;
import com.onecity.os.management.statisticanalysis.entity.dto.GetStatisticAnalysisPageListDto;
import com.onecity.os.management.statisticanalysis.entity.dto.GetStatisticInfoByIdDto;
import com.onecity.os.management.statisticanalysis.entity.dto.IndexCensusDto;

import java.text.ParseException;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/1/25 下午3:02
 */
public interface IndicatorStatisticAnalysisService {
    /**
     * 获取指标统计分析列表
     *
     * @param updateCycle
     * @param startUpdateTime
     * @param endUpdateTime
     * @param sourceName
     * @param isHistory
     * @param isOverDue
     * @param version
     * @return
     */
    List<GetStatisticAnalysisPageListDto> getStatisticAnalysisPageList(String updateCycle, String startUpdateTime,
                                                                       String endUpdateTime, String sourceName,
                                                                       String isHistory, String isOverDue, Byte version);

    /**
     * 根据id,获取指标统计分析详情
     *
     * @param id
     * @param dateTime
     * @return
     */
    GetStatisticInfoByIdDto getStatisticInfoById(Long id, String dateTime);

    /**
     * 根据厅局id,获取指标统计分析详情
     *
     * @param sourceId
     * @return
     */
    List<GetStatisticInfoByIdDto> getStatisticInfoBySourceId(List<String> indicatorParentIds, List<String> allIndicatorIds, Long sourceId);

    /**
     * 导出统计分析excel
     *
     * @param history
     * @param isOverDue
     * @param updateCycle
     * @param startUpdateTime
     * @param endUpdateTime
     * @param sourceName
     * @return
     */
    List<ExportStatisticInfoXlsDto> getExportStatisticInfoXls(String history, String isOverDue, String updateCycle,
                                                              String startUpdateTime, String endUpdateTime, String sourceName);

    /**
     * 新版本--获取指标统计分析列表
     *
     * @param sourceName
     * @return
     */
    List<GetStatisticAnalysisPageListDto> getNewVerStatisticAnalysisPageList(String sourceName);

    /**
     * 查询所有年度的、季度的、月度的统计情况
     *
     * @param startTime
     * @param endTime
     * @param sourceName
     * @param page
     * @param size
     * @return
     * @throws ParseException
     */
    List<IndexCensusDto> getIndexCensusPageList(String startTime, String endTime, String sourceName, Integer page, Integer size) throws ParseException;
    /**
     * 根据厅局id,导出指标统计分析详情excel
     *
     * @param sourceId
     * @return
     */
    List<GetStatisticInfoByIdDto> getExportStatisticDetailXls(String sourceId);

    List<GetStatisticAnalysisPageListDto> getUpdateStatistics(String date);

    List<String> getIndicatorIdsByParentIds(List<String> indicatorParentIds, List<String> allIndicatorIds);

}














