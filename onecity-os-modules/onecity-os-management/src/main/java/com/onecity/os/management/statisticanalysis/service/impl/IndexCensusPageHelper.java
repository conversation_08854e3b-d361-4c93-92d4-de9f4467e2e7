package com.onecity.os.management.statisticanalysis.service.impl;

import com.onecity.os.management.statisticanalysis.entity.IndicatorUpdateRecord;
import com.onecity.os.management.statisticanalysis.entity.dto.IndexCensusBean;
import com.onecity.os.management.statisticanalysis.entity.dto.IndexCensusDto;
import com.onecity.os.management.statisticanalysis.entity.dto.IndicatorCensusBean;
import com.onecity.os.management.statisticanalysis.mapper.IndicatorUpdateRecordMapper;
import com.onecity.os.management.zhibiao.entity.GeneralIndicator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class IndexCensusPageHelper {
    @Resource
    private IndicatorUpdateRecordMapper indicatorUpdateRecordMapper;

    /**
     * 判断某个指定厅局下,指标是已更新,未更新,还是部分更新;是否逾期,更新数量等信息
     */
    public IndicatorCensusBean checkIndicatorsIsUpdateAndOverDue(List<GeneralIndicator> indicators,
                                                                 String startTime,
                                                                 String endTime) {
        List<String> idList = indicators.stream()
                .map(GeneralIndicator::getId)
                .collect(Collectors.toList());
        //查询出所有的记录
        List<IndicatorUpdateRecord> recordList = indicatorUpdateRecordMapper.getUpdateRecordList(startTime, endTime, idList, null);
        //根据指标id，得到记录的map
        Map<String, List<IndicatorUpdateRecord>> recordMap;
        if (!CollectionUtils.isEmpty(recordList)) {
            recordMap = recordList.stream()
                    .collect(Collectors.toMap(IndicatorUpdateRecord::getIndicatorId, indicatorUpdateRecord -> {
                        ArrayList<IndicatorUpdateRecord> list = new ArrayList<>();
                        list.add(indicatorUpdateRecord);
                        return list;
                    }, (list1, list2) -> {
                        list1.addAll(list2);
                        return list1;
                    }));
        } else {
            recordMap = new HashMap<>();
        }

        //指标分类
        List<GeneralIndicator> yearList = new ArrayList<>();
        List<GeneralIndicator> halfYearList = new ArrayList<>();
        List<GeneralIndicator> seasonList = new ArrayList<>();
        List<GeneralIndicator> monthList = new ArrayList<>();
        for (GeneralIndicator indicator : indicators) {
            if ("年度更新".equals(indicator.getUpdateCycle())) {
                yearList.add(indicator);
            } else if ("半年更新".equals(indicator.getUpdateCycle())) {
                halfYearList.add(indicator);
            } else if ("季度更新".equals(indicator.getUpdateCycle())) {
                seasonList.add(indicator);
            } else if ("月度更新".equals(indicator.getUpdateCycle())) {
                monthList.add(indicator);
            }
        }
        IndicatorCensusBean indicatorCensusBean = new IndicatorCensusBean();

        Date start = IndicatorDateHelper.strToDate(startTime);
        Date end = IndicatorDateHelper.strToDate(endTime);
        if (start == null || end == null) {
            return null;
        }
        censusYear(recordMap, yearList, indicatorCensusBean, start, end);
        censusHalfYear(recordMap, halfYearList, indicatorCensusBean, start, end);
        censusSeason(recordMap, seasonList, indicatorCensusBean, start, end);
        censusMonth(recordMap, monthList, indicatorCensusBean, start, end);


        return indicatorCensusBean;
    }

    //统计月
    private void censusMonth(Map<String, List<IndicatorUpdateRecord>> recordMap,
                             List<GeneralIndicator> monthList,
                             IndicatorCensusBean indicatorCensusBean,
                             Date startTime,
                             Date endTime) {

        int shouldNum = 0;
        int actualNum = 0;
        int overTimeNum = 0;
        //应更新指标数
        for (GeneralIndicator indicator : monthList) {
            String planUpdateDate = indicator.getPlanUpdateDate();
            if (StringUtils.isEmpty(planUpdateDate)) {
                planUpdateDate = "10";// 默认是10号更新
            }
            shouldNum += IndicatorDateHelper.monthShouldNum(startTime, endTime, planUpdateDate);

            List<IndicatorUpdateRecord> recordList = recordMap.get(indicator.getId());
            if (CollectionUtils.isEmpty(recordList)) {
                continue;
            }
            //获得该指标在每个月的最晚的更新date的map，key是月份的标识，date是这个月最晚的那天
            //就是找到那个月最晚的那个日期
            //shouldnum=实际更新数+逾期更新数+未更新数
            Map<String, Date> monthMap = recordList.stream()
                    .collect(Collectors.toMap(indicatorUpdateRecord -> {
                                Date createTime = indicatorUpdateRecord.getCreateTime();
                                Calendar calendar = Calendar.getInstance();
                                calendar.setTime(createTime);
                                int year = calendar.get(Calendar.YEAR);
                                int month = calendar.get(Calendar.MONTH) + 1;
                                return year + "_" + month;
                            }, IndicatorUpdateRecord::getCreateTime,
                            (date, date2) -> date.after(date2) ? date : date2));

            int[] nums = IndicatorDateHelper.monthActualAndOverTimeNum(monthMap, planUpdateDate);
            actualNum += nums[0];
            overTimeNum += nums[1];
        }
        indicatorCensusBean.setMonthActualNum(actualNum);
        indicatorCensusBean.setMonthShouldNum(shouldNum);
        indicatorCensusBean.setMonthTotalNum(monthList.size());
        indicatorCensusBean.setMonthOverTimeNum(overTimeNum);
        int unUpdate = shouldNum - actualNum - overTimeNum;
        indicatorCensusBean.setMonthUnUpdateNum(Math.max(unUpdate, 0));
    }

    //统计季度
    private void censusSeason(Map<String, List<IndicatorUpdateRecord>> recordMap,
                              List<GeneralIndicator> seasonList,
                              IndicatorCensusBean indicatorCensusBean,
                              Date startTime,
                              Date endTime) {
        int shouldNum = 0;
        int actualNum = 0;
        int overTimeNum = 0;
        //应更新指标数
        for (GeneralIndicator indicator : seasonList) {
            String planUpdateDate = indicator.getPlanUpdateDate();
            if (StringUtils.isEmpty(planUpdateDate) || !planUpdateDate.contains("-")) {
                planUpdateDate = "1-10";// 默认是10号更新
            }
            shouldNum += IndicatorDateHelper.rangeShouldNum(startTime, endTime, planUpdateDate, 0);

            List<IndicatorUpdateRecord> recordList = recordMap.get(indicator.getId());
            if (CollectionUtils.isEmpty(recordList)) {
                continue;
            }
            //获得该指标在每个的最晚的更新date的map，key是季度的标识，date是这个季度最晚的那天
            //(就是每个季度，只找到一个最晚的日期)
            //shouldnum=实际更新数+逾期更新数+未更新数
            Map<String, Date> seasonMap = recordList.stream()
                    .collect(Collectors.toMap(indicatorUpdateRecord -> {
                                Date createTime = indicatorUpdateRecord.getCreateTime();
                                Calendar calendar = Calendar.getInstance();
                                calendar.setTime(createTime);
                                int month = calendar.get(Calendar.MONTH);
                                int year = calendar.get(Calendar.YEAR);

                                return year + "_" + (month / 3 + 1);
                            }, IndicatorUpdateRecord::getCreateTime,
                            (date, date2) -> date.after(date2) ? date : date2));

            int[] nums = IndicatorDateHelper.rangeActualAndOverTimeNum(seasonMap, planUpdateDate, 0);
            actualNum += nums[0];
            overTimeNum += nums[1];
        }
        indicatorCensusBean.setSeasonActualNum(actualNum);
        indicatorCensusBean.setSeasonShouldNum(shouldNum);
        indicatorCensusBean.setSeasonTotalNum(seasonList.size());
        indicatorCensusBean.setSeasonOverTimeNum(overTimeNum);
        int unUpdate = shouldNum - actualNum - overTimeNum;
        indicatorCensusBean.setSeasonUnUpdateNum(Math.max(unUpdate, 0));
    }

    //统计半年
    private void censusHalfYear(Map<String, List<IndicatorUpdateRecord>> recordMap,
                                List<GeneralIndicator> halfYearList,
                                IndicatorCensusBean indicatorCensusBean,
                                Date startTime,
                                Date endTime) {
        int shouldNum = 0;
        int actualNum = 0;
        int overTimeNum = 0;
        //应更新指标数
        for (GeneralIndicator indicator : halfYearList) {
            String planUpdateDate = indicator.getPlanUpdateDate();
            if (StringUtils.isEmpty(planUpdateDate) || !planUpdateDate.contains("-")) {
                planUpdateDate = "1-10";// 默认是10号更新
            }
            shouldNum += IndicatorDateHelper.rangeShouldNum(startTime, endTime, planUpdateDate, 1);

            List<IndicatorUpdateRecord> recordList = recordMap.get(indicator.getId());
            //获得该指标在每个半年的最晚的更新date的map，key是半年的标识，date是这个季度最晚的那天
            // (就是每个半年，只找到一个最晚的日期)
            //shouldnum=实际更新数+逾期更新数+未更新数
            if (CollectionUtils.isEmpty(recordList)) {
                continue;
            }
            Map<String, Date> halfYearMap = recordList.stream()
                    .collect(Collectors.toMap(indicatorUpdateRecord -> {
                                Date createTime = indicatorUpdateRecord.getCreateTime();
                                Calendar calendar = Calendar.getInstance();
                                calendar.setTime(createTime);
                                int month = calendar.get(Calendar.MONTH);
                                int year = calendar.get(Calendar.YEAR);

                                return year + "_" + (month / 6 + 1);
                            }, IndicatorUpdateRecord::getCreateTime,
                            (date, date2) -> date.after(date2) ? date : date2));

            int[] nums = IndicatorDateHelper.rangeActualAndOverTimeNum(halfYearMap, planUpdateDate, 1);
            actualNum += nums[0];
            overTimeNum += nums[1];
        }
        indicatorCensusBean.setHalfYearActualNum(actualNum);
        indicatorCensusBean.setHalfYearShouldNum(shouldNum);
        indicatorCensusBean.setHalfYearTotalNum(halfYearList.size());
        indicatorCensusBean.setHalfYearOverTimeNum(overTimeNum);
        int unUpdate = shouldNum - actualNum - overTimeNum;
        indicatorCensusBean.setHalfYearUnUpdateNum(Math.max(unUpdate, 0));
    }

    //统计年
    private void censusYear(Map<String, List<IndicatorUpdateRecord>> recordMap,
                            List<GeneralIndicator> yearList,
                            IndicatorCensusBean indicatorCensusBean,
                            Date startTime,
                            Date endTime) {
        int shouldNum = 0;
        int actualNum = 0;
        int overTimeNum = 0;
        //应更新指标数
        for (GeneralIndicator indicator : yearList) {
            String planUpdateDate = indicator.getPlanUpdateDate();
            if (StringUtils.isEmpty(planUpdateDate) || !planUpdateDate.contains("-")) {
                planUpdateDate = "1-10";// 默认是10号更新
            }
            shouldNum += IndicatorDateHelper.rangeShouldNum(startTime, endTime, planUpdateDate, 2);

            List<IndicatorUpdateRecord> recordList = recordMap.get(indicator.getId());
            //获得该指标在每个半年的最晚的更新date的map，key是半年的标识，date是这个季度最晚的那天
            // (就是每个半年，只找到一个最晚的日期)
            //shouldnum=实际更新数+逾期更新数+未更新数
            if (CollectionUtils.isEmpty(recordList)) {
                continue;
            }
            Map<String, Date> yearMap = recordList.stream()
                    .collect(Collectors.toMap(indicatorUpdateRecord -> {
                                Date createTime = indicatorUpdateRecord.getCreateTime();
                                Calendar calendar = Calendar.getInstance();
                                calendar.setTime(createTime);
                                int month = calendar.get(Calendar.MONTH);
                                int year = calendar.get(Calendar.YEAR);

                                return year + "_" + (month + 1);
                            }, IndicatorUpdateRecord::getCreateTime,
                            (date, date2) -> date.after(date2) ? date : date2));

            int[] nums = IndicatorDateHelper.rangeActualAndOverTimeNum(yearMap, planUpdateDate, 2);
            actualNum += nums[0];
            overTimeNum += nums[1];
        }
        indicatorCensusBean.setYearActualNum(actualNum);
        indicatorCensusBean.setYearShouldNum(shouldNum);
        indicatorCensusBean.setYearTotalNum(yearList.size());
        indicatorCensusBean.setYearOverTimeNum(overTimeNum);
        int unUpdate = shouldNum - actualNum - overTimeNum;
        indicatorCensusBean.setYearUnUpdateNum(Math.max(unUpdate, 0));

    }

    /**
     * 指标数据统计
     */
    public void initIndexCensusDto(IndexCensusDto indexCensusDto,
                                   IndicatorCensusBean bean) {

        IndexCensusBean year = new IndexCensusBean();
        IndexCensusBean halfYear = new IndexCensusBean();
        IndexCensusBean season = new IndexCensusBean();
        IndexCensusBean month = new IndexCensusBean();

        //年度指标统计
        year.setShouldCount(bean.getYearShouldNum());
        year.setActualCount(bean.getYearActualNum());
        year.setUnUpdateCount(bean.getYearUnUpdateNum());
        year.setOverdueCount(bean.getYearOverTimeNum());
        year.setTotalCount(bean.getYearTotalNum());

        //半年 指标统计
        halfYear.setShouldCount(bean.getHalfYearShouldNum());
        halfYear.setActualCount(bean.getHalfYearActualNum());
        halfYear.setUnUpdateCount(bean.getHalfYearUnUpdateNum());
        halfYear.setOverdueCount(bean.getHalfYearOverTimeNum());
        halfYear.setTotalCount(bean.getHalfYearTotalNum());

        //季度指标统计
        season.setShouldCount(bean.getSeasonShouldNum());
        season.setActualCount(bean.getSeasonActualNum());
        season.setUnUpdateCount(bean.getSeasonUnUpdateNum());
        season.setOverdueCount(bean.getSeasonOverTimeNum());
        season.setTotalCount(bean.getSeasonTotalNum());

        //月度指标统计
        month.setShouldCount(bean.getMonthShouldNum());
        month.setActualCount(bean.getMonthActualNum());
        month.setUnUpdateCount(bean.getMonthUnUpdateNum());
        month.setOverdueCount(bean.getMonthOverTimeNum());
        month.setTotalCount(bean.getMonthTotalNum());

        indexCensusDto.setYearIndex(year);
        indexCensusDto.setHalfYearIndex(halfYear);
        indexCensusDto.setSeasonIndex(season);
        indexCensusDto.setMonthIndex(month);
    }

    //设置初始为0
    public void initIndexCensusDto(IndexCensusDto indexCensusDto) {
        IndexCensusBean year = new IndexCensusBean();
        IndexCensusBean halfYear = new IndexCensusBean();
        IndexCensusBean season = new IndexCensusBean();
        IndexCensusBean month = new IndexCensusBean();

        indexCensusDto.setYearIndex(year);
        indexCensusDto.setHalfYearIndex(halfYear);
        indexCensusDto.setSeasonIndex(season);
        indexCensusDto.setMonthIndex(month);
    }
}
