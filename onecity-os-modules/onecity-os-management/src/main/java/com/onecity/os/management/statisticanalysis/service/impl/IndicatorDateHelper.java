package com.onecity.os.management.statisticanalysis.service.impl;

import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

@Slf4j
public class IndicatorDateHelper {

    public static Date strToDate(String strTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date date = sdf.parse(strTime);
            return date;
        } catch (ParseException e) {
            e.printStackTrace();
            log.error("IndicatorDateHelper strToDate error :", e);
        }
        return null;
    }

    public static int monthShouldNum(Date startTime, Date endTime, String planUpdateDate) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        int count = 0;
        while (calendar.getTime().compareTo(endTime) <= 0) {
            if (calendar.get(Calendar.DAY_OF_MONTH) == Integer.parseInt(planUpdateDate)) {
                count++;
            }
            calendar.add(Calendar.DAY_OF_YEAR, 1);
        }
        return count;
    }

    /**
     * 计算 实际更新数 和 逾期更新数
     *
     * @param monthMap       该指标在每个月的最晚的更新date的map，key是月份，
     *                       date是这个月最晚的那天。最晚的那天逾期即为逾期更新。
     * @param planUpdateDate
     * @return
     */
    public static int[] monthActualAndOverTimeNum(Map<String, Date> monthMap, String planUpdateDate) {
        int actualNum = 0;
        int overTimeNum = 0;
        for (String key : monthMap.keySet()) {
            Date value = monthMap.get(key);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(value);
            if (calendar.get(Calendar.DAY_OF_MONTH) > Integer.parseInt(planUpdateDate)) {
                overTimeNum++;
            } else {
                actualNum++;
            }
        }
        return new int[]{actualNum, overTimeNum};
    }

    /**
     * @param startTime
     * @param endTime
     * @param planUpdateDate
     * @param type           类型 0 season  , 1 halfyear, 2 year
     * @return
     */
    public static int rangeShouldNum(Date startTime, Date endTime, String planUpdateDate, int type) {

        String[] split = planUpdateDate.split("-");
        int monthPlan = Integer.parseInt(split[0]);
        int dayPlan = Integer.parseInt(split[1]);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        int count = 0;
        while (calendar.getTime().compareTo(endTime) <= 0) {
            if (calendar.get(Calendar.DAY_OF_MONTH) == dayPlan) {
                if (type == 0) {
                    //季度
                    if (calendar.get(Calendar.MONTH) + 1 == monthPlan
                            || calendar.get(Calendar.MONTH) + 1 == monthPlan + 3
                            || calendar.get(Calendar.MONTH) + 1 == monthPlan + 6
                            || calendar.get(Calendar.MONTH) + 1 == monthPlan + 9) {
                        count++;
                    }

                } else if (type == 1) {
                    //半年
                    if (calendar.get(Calendar.MONTH) + 1 == monthPlan
                            || calendar.get(Calendar.MONTH) + 1 == monthPlan + 6) {
                        count++;
                    }

                } else if (type == 2) {
                    //year
                    if (calendar.get(Calendar.MONTH) + 1 == monthPlan) {
                        count++;
                    }
                }

            }
            calendar.add(Calendar.DAY_OF_YEAR, 1);
        }
        return count;
    }

    /**
     * 计算 实际更新数 和 逾期更新数
     *
     * @param map            该指标在每个月的最晚的更新date的map，key是月份，
     *                       date是这个月最晚的那天。最晚的那天逾期即为逾期更新。
     * @param planUpdateDate
     * @param type           类型 0 season  , 1 halfyear, 2 year
     * @return
     */
    public static int[] rangeActualAndOverTimeNum(Map<String, Date> map, String planUpdateDate, int type) {
        int actualNum = 0;
        int overTimeNum = 0;
        String[] split = planUpdateDate.split("-");
        int monthPlan = Integer.parseInt(split[0]);
        int dayPlan = Integer.parseInt(split[1]);

        for (String key : map.keySet()) {
            Date value = map.get(key);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(value);

            int day = calendar.get(Calendar.DAY_OF_MONTH);
            int month = calendar.get(Calendar.MONTH);
            int monthCount = 0;//这个区间(season\halfyear\year)第几个月
            if (type == 0) {
                //季度
                monthCount = month % 3 + 1;
            } else if (type == 1) {
                //半年
                monthCount = month % 6 + 1;
            } else if (type == 2) {
                monthCount = month + 1;
            }
            if (monthCount > monthPlan) {
                overTimeNum++;
            } else if (monthCount == monthPlan && day > dayPlan) {
                overTimeNum++;
            } else {
                actualNum++;
            }

        }
        return new int[]{actualNum, overTimeNum};
    }
}
