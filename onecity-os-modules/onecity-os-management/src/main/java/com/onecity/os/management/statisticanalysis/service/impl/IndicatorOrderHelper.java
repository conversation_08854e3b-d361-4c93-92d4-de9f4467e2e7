package com.onecity.os.management.statisticanalysis.service.impl;

import com.onecity.os.management.statisticanalysis.entity.dto.GetStatisticAnalysisPageListDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class IndicatorOrderHelper {

    public static List<GetStatisticAnalysisPageListDto> orderIndicator(List<GetStatisticAnalysisPageListDto> dataList) {
        //排序 单位名称必须严格按顺序填写（ 县区、高新区、经开区、市直）
        ArrayList<GetStatisticAnalysisPageListDto> resultList = new ArrayList();
        //20220411调整新的排序规则 指定排序顺序
        //指定展示顺序
        List<String> order = new ArrayList<String>() {
            {
                this.add("潞州区");
                this.add("上党区");
                this.add("屯留区");
                this.add("潞城区");
                this.add("襄垣县");
                this.add("平顺县");
                this.add("黎城县");
                this.add("壶关县");
                this.add("长子县");
                this.add("武乡县");
                this.add("沁县");
                this.add("沁源县");
                this.add("高新区");
                this.add("经开区");
                this.add("市发改委");
                this.add("市教育局");
                this.add("市科技局");
                this.add("市工信局");
                this.add("市公安局");
                this.add("市民政局");
                this.add("市司法局");
                this.add("市财政局");
                this.add("市人社局");
                this.add("规划和自然资源局");
                this.add("生态环境局");
                this.add("市住建局");
                this.add("市城管局");
                this.add("市交通局");
                this.add("市水利局");
                this.add("市农业局");
                this.add("市商务局");
                this.add("市文旅局");
                this.add("市卫健委");
                this.add("市退役军人局");
                this.add("市应急局");
                this.add("外事办");
                this.add("市监管局");
                this.add("市体育局");
                this.add("市统计局");
                this.add("市医保局");
                this.add("市审批局");
                this.add("市信访局");
                this.add("市金融办");
                this.add("市能源局");
                this.add("市扶贫办");
                this.add("中小企业");
                this.add("市园林服务中心");
                this.add("市招商局");
                this.add("畜牧兽医服务中心");
                this.add("经济运行");
            }
        };
        //内层排序
        List<String> sedOrder = new ArrayList<String>() {
            {
                this.add("月度更新");
                this.add("季度更新");
                this.add("半年更新");
                this.add("年度更新");
            }
        };
        order.forEach(o -> {
            //外层排序
            List<GetStatisticAnalysisPageListDto> fistList = dataList.stream().filter(dto -> dto.getSourceName().equals(o)).collect(Collectors.toList());
            //内层排序
            if (!CollectionUtils.isEmpty(fistList) && StringUtils.isNotEmpty(fistList.get(0).getUpdateCycle())) {
                sedOrder.forEach(s -> resultList.addAll(fistList.stream().filter(d -> d.getUpdateCycle().equals(s)).collect(Collectors.toList())));
            } else if (!CollectionUtils.isEmpty(fistList) && StringUtils.isEmpty(fistList.get(0).getUpdateCycle())) {
                resultList.addAll(fistList);
            } else {
                return;
            }
        });
        dataList.forEach(data -> {
            if (!resultList.contains(data)) {
                resultList.add(data);
            }
        });

        return resultList;
    }
}
