package com.onecity.os.management.statisticanalysis.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ser.Serializers;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.management.configmanage.entity.dto.RemindIndicatorInfoDto;
import com.onecity.os.management.feign.SystemFeignService;
import com.onecity.os.system.api.RemoteUserService;
import org.apache.commons.lang3.ObjectUtils;
import com.onecity.os.management.configmanage.mapper.RemindUserConfigMapper;
import com.onecity.os.management.configmanage.mapper.SourceManageMapper;
import com.onecity.os.management.messagesReport.service.MessageIndDataService;
import com.onecity.os.management.messagesReport.service.MessageIndService;
import com.onecity.os.management.statisticanalysis.entity.IndicatorStatisticsAnalysis;
import com.onecity.os.management.statisticanalysis.entity.dto.*;
import com.onecity.os.management.statisticanalysis.mapper.IndicatorStatisticsAnalysisMapper;
import com.onecity.os.management.statisticanalysis.service.IndicatorStatisticAnalysisService;
import com.onecity.os.management.utils.BeanHelper;
import com.onecity.os.management.utils.DateThis;
import com.onecity.os.management.zhibiao.entity.GeneralIndicator;
import com.onecity.os.management.zhibiao.mapper.GeneralIndicatorMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/1/25 下午3:02
 */
@Service
@Slf4j
public class IndicatorStatisticAnalysisServiceImpl implements IndicatorStatisticAnalysisService {

    @Autowired
    IndexCensusPageHelper indexCensusPageHelper;

    @Resource
    private IndicatorStatisticsAnalysisMapper indicatorStatisticsAnalysisMapper;

    @Resource
    private RemindUserConfigMapper remindUserConfigMapper;

    @Resource
    private SystemFeignService systemFeignService;

    @Resource
    private GeneralIndicatorMapper generalIndicatorMapper;

    @Resource
    private SourceManageMapper sourceManageMapper;

    @Autowired
    private MessageIndService messageIndService;

    @Autowired
    private MessageIndDataService messageIndDataService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Override
    public List<GetStatisticAnalysisPageListDto> getStatisticAnalysisPageList(String updateCycle, String startUpdateTime,
                                                                              String endUpdateTime, String sourceName,
                                                                              String isHistory, String isOverDue, Byte version) {
        List<GetStatisticAnalysisPageListDto> resultDtoList = indicatorStatisticsAnalysisMapper
                .getStatisticAnalysisHistoryPageList(updateCycle, startUpdateTime, endUpdateTime, sourceName, isHistory,
                        isOverDue, version);
        resultDtoList.forEach(dto -> {
            // 根据PC人员ids,查找指标更新PC提醒人
            if (StringUtils.isNotEmpty(dto.getPcUserIds())) {
                BaseResult userResult = remoteUserService.getPcUserNamesByPcUserIds(dto.getPcUserIds().split(","));
                if(null != userResult && null != userResult.getData() && StringUtils.isNotEmpty(userResult.getData().toString())) {
                    String pcNames = userResult.getData().toString();
                    dto.setPcUserNames(pcNames);
                    // 根据人员id,查找部门
                    BaseResult departName = remoteUserService.getDeptNamesById(dto.getPcUserIds().split(","));
                    if (null != departName) {
                        //去除重复部门
                        String[] departNameStr = String.valueOf(departName.getData()).split(",");
                        List<String> departNameList = Arrays.stream(departNameStr).distinct().collect(Collectors.toList());
                        StringBuilder stringBuilder = new StringBuilder();
                        departNameList.forEach(depart -> stringBuilder.append(depart).append(","));
                        //去除最后一个,
                        String departResult = stringBuilder.substring(0, stringBuilder.length() - 1);
                        dto.setDepartName(departResult);
                    }
                }
            }
            // 根据APP人员ids,查找指标更新APP提醒人
            if (StringUtils.isNotEmpty(dto.getAppUserIds())) {
                String appNames = remindUserConfigMapper.getAppUserNamesByPcUserIds(dto.getAppUserIds().split(","));
                dto.setAppUserNames(appNames);
            }
        });
        return resultDtoList;
    }

    @Override
    public GetStatisticInfoByIdDto getStatisticInfoById(Long id, String dateTime) {
        GetStatisticInfoByIdDto dto = indicatorStatisticsAnalysisMapper.getStatisticInfoById(id);
        if (ObjectUtils.isNotEmpty(dto)) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss");
            dto.setDate(dateFormat.format(new Date()));
        }
        //补充更新率字段数值
        if (StringUtils.isNotEmpty(dto.getShouldUpdate()) && StringUtils.isNotEmpty(dto.getActualUpdate())) {
            NumberFormat percentFormat = NumberFormat.getPercentInstance();
            BigDecimal shouldUpdate = new BigDecimal(dto.getShouldUpdate().substring(0, dto.getShouldUpdate().length() - 1));
            BigDecimal actualUpdate = new BigDecimal(dto.getActualUpdate().substring(0, dto.getActualUpdate().length() - 1));
            if(BigDecimal.ZERO.equals(shouldUpdate) || BigDecimal.ZERO.equals(actualUpdate)){
                dto.setRenewalRate("0%");
            } else {
                String renewalRate = percentFormat.format(actualUpdate.divide(shouldUpdate, 2, RoundingMode.HALF_UP));
                dto.setRenewalRate(renewalRate);
            }
        }
        return dto;
    }

    @Override
    public List<GetStatisticInfoByIdDto> getStatisticInfoBySourceId(List<String> indicatorParentIds, List<String> allIndicatorIds, Long sourceId) {
        if (CollectionUtils.isNotEmpty(indicatorParentIds)) {
            // 所有指标和分组
            List<GetStatisticInfoByIdDto> pageList = indicatorStatisticsAnalysisMapper.getStatisticInfoBySourceId(allIndicatorIds);
            if (CollectionUtils.isNotEmpty(pageList)) {
                // 根据厅局id,查找审核人,审核时间,标题
                GetStatisticInfoByIdDto dto1 = indicatorStatisticsAnalysisMapper.getAuditNameBySourceId(sourceId);
                String sourceName = sourceManageMapper.getSourceNameBySourceId(sourceId);
                SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy年MM月");
                String title = sourceName + dateFormat2.format(new Date()) + "指标更新统计分析详情";
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                // 获取格式化的日期
                SimpleDateFormat monthFormat = new SimpleDateFormat("MM");
                SimpleDateFormat dayFormat = new SimpleDateFormat("dd");
                // 获取当前月月初,月底
                DateThis dateThis = new DateThis();
                String thisMonthStart = dateThis.thisMonth();
                // 获取当前月(如: 1,2,3...)
                Calendar cal = Calendar.getInstance();
                int currentMonth = cal.get(Calendar.MONTH) + 1;
                // 获取季度的第几个月
                int seasonMonth = 1;
                if (currentMonth <= 3) {
                    seasonMonth = currentMonth;
                }
                if (4 <= currentMonth && currentMonth <= 6) {
                    seasonMonth = currentMonth - 3;
                }
                if (7 <= currentMonth && currentMonth <= 9) {
                    seasonMonth = currentMonth - 6;
                }
                if (10 <= currentMonth && currentMonth <= 12) {
                    seasonMonth = currentMonth - 9;
                }
                // 获取半年的第几个月
                int halfYearMonth = 1;
                if (currentMonth <= 6) {
                    halfYearMonth = currentMonth;
                }
                if (7 <= currentMonth && currentMonth <= 12) {
                    halfYearMonth = currentMonth - 6;
                }
                for (GetStatisticInfoByIdDto dto : pageList) {
                    if (ObjectUtils.isNotEmpty(dto1)) {
                        dto.setAuditUserName(dto1.getAuditUserName());
                        dto.setAuditTime(dto1.getAuditTime());
                    }
                    dto.setTitle(title);
                    //补充拼写父类指标名称
                    if (StringUtils.isNotEmpty(dto.getParentName())){
                        dto.setIndicatorName(dto.getParentName() + "-" +dto.getIndicatorName());
                    }
                    // 判断本月是否需要更新
                    if ("月度更新".equals(dto.getUpdateCycle())) {
                        if (StringUtils.isEmpty(dto.getPlanUpdateDate())) {
                            dto.setPlanUpdateDate("10");
                        }
                        dto.setIsShouldUpdate("1");
                        // 判断更新情况和逾期情况
                        if (dateFormat.format(dto.getUpdateTime()).compareTo(thisMonthStart) >= 0) {
                            dto.setIsUpdate("1");
                            // 判断是否逾期
                            if (Integer.parseInt(dayFormat.format(dto.getUpdateTime()))
                                    - Integer.parseInt(dto.getPlanUpdateDate()) > 0) {
                                dto.setIsOverDue("1");
                            } else {
                                dto.setIsOverDue("0");
                            }
                        } else {
                            dto.setIsUpdate("0");
                            dto.setIsOverDue("1");
                            // 未更新的,在未更新的里面,约定更新的日,大于当前日,则为未更,且不逾期的
                            if (Integer.parseInt(dto.getPlanUpdateDate()) - Integer.parseInt(dayFormat.format(new Date())) > 0) {
                                dto.setIsOverDue("0");
                            }
                        }
                    }
                    if ("季度更新".equals(dto.getUpdateCycle())) {
                        // 获取约定更新时间的月和日
                        if (StringUtils.isEmpty(dto.getPlanUpdateDate())) {
                            dto.setPlanUpdateDate("1-15");
                        }
                        if (!dto.getPlanUpdateDate().contains("-")) {
                            dto.setPlanUpdateDate("1-15");
                        }
                        Integer monthPlanUpdateDate = Integer.valueOf(dto.getPlanUpdateDate().split("-")[0]);
                        Integer dayPlanUpdateDate = Integer.valueOf(dto.getPlanUpdateDate().split("-")[1]);
                        // 获取该月应该更新的季度指标
                        if (monthPlanUpdateDate.equals(seasonMonth)) {
                            dto.setIsShouldUpdate("1");
                            //判断是否已经更新过
                        } else {
                            //处理提前更新逻辑
                            dto.setIsShouldUpdate("0");
                        }
                        //判断是否已经更新过
                        if (checkUpdate(dto.getUpdateTime(), dto.getUpdateCycle())) {
                            dto.setIsUpdate("1");
                        } else {
                            dto.setIsUpdate("0");
                        }
                        //判断是否逾期
                        if (checkOverDue(dto.getUpdateTime(), dto.getPlanUpdateDate(), dto.getUpdateCycle())) {
                            dto.setIsOverDue("1");
                        } else {
                            dto.setIsOverDue("0");
                        }
                    }
                    if ("半年更新".equals(dto.getUpdateCycle())) {
                        // 获取约定更新时间的月和日
                        if (StringUtils.isEmpty(dto.getPlanUpdateDate())) {
                            dto.setPlanUpdateDate("1-25");
                        }
                        if (!dto.getPlanUpdateDate().contains("-")) {
                            dto.setPlanUpdateDate("1-25");
                        }
                        Integer monthPlanUpdateDate = Integer.valueOf(dto.getPlanUpdateDate().split("-")[0]);
                        Integer dayPlanUpdateDate = Integer.valueOf(dto.getPlanUpdateDate().split("-")[1]);
                        // 获取该月应该更新的半年指标
                        if (monthPlanUpdateDate.equals(halfYearMonth)) {
                            dto.setIsShouldUpdate("1");
                            //判断是否已经更新过
                        } else {
                            //处理提前更新逻辑
                            dto.setIsShouldUpdate("0");
                        }
                        //判断是否已经更新过
                        if (checkUpdate(dto.getUpdateTime(), dto.getUpdateCycle())) {
                            dto.setIsUpdate("1");
                        } else {
                            dto.setIsUpdate("0");
                        }
                        //判断是否逾期
                        if (checkOverDue(dto.getUpdateTime(),dto.getPlanUpdateDate(), dto.getUpdateCycle())){
                            dto.setIsOverDue("1");
                        } else {
                            dto.setIsOverDue("0");
                        }
                    }
                    if ("年度更新".equals(dto.getUpdateCycle())) {
                        // 获取约定更新时间的月和日
                        if (StringUtils.isEmpty(dto.getPlanUpdateDate())) {
                            dto.setPlanUpdateDate("1-30");
                        }
                        if (!dto.getPlanUpdateDate().contains("-")) {
                            dto.setPlanUpdateDate("1-30");
                        }
                        Integer monthPlanUpdateDate = Integer.valueOf(dto.getPlanUpdateDate().split("-")[0]);
                        Integer dayPlanUpdateDate = Integer.valueOf(dto.getPlanUpdateDate().split("-")[1]);
                        // 获取该月应该更新的年度指标
                        if (monthPlanUpdateDate.equals(currentMonth)) {
                            dto.setIsShouldUpdate("1");
                            //判断是否已经更新过
                        } else {
                            //处理提前更新逻辑
                            dto.setIsShouldUpdate("0");
                        }
                        //判断是否已经更新过
                        if (checkUpdate(dto.getUpdateTime(), dto.getUpdateCycle())) {
                            dto.setIsUpdate("1");
                        } else {
                            dto.setIsUpdate("0");
                        }
                        //判断是否逾期
                        if (checkOverDue(dto.getUpdateTime(),dto.getPlanUpdateDate(), dto.getUpdateCycle())){
                            dto.setIsOverDue("1");
                        } else {
                            dto.setIsOverDue("0");
                        }
                    }
                    //更新人匹配
                    if (StringUtils.isNotEmpty(dto.getUpdater())) {
                        BaseResult<String> nickName = remoteUserService.getNickNameByName(dto.getUpdater());
                        dto.setUpdater(nickName.getData());
                    }

                }
            }
//            return new PageResult<>(pageList.getRecords(), pageList.getTotal(), pageList.getPages());
            return pageList;
        }
        return new ArrayList<>();
    }

    /**
     * 根据父指标ids,查找所有子集指标
     *
     * @param indicatorParentIds
     * @param allIndicatorIds
     * @return
     */
    @Override
    public List<String> getIndicatorIdsByParentIds(List<String> indicatorParentIds, List<String> allIndicatorIds) {
        if (!org.springframework.util.CollectionUtils.isEmpty(indicatorParentIds)) {
            List<String> indicatorIds = generalIndicatorMapper.getIndicatorIdsByParentIds(indicatorParentIds);
            if (!org.springframework.util.CollectionUtils.isEmpty(indicatorIds)) {
                allIndicatorIds.addAll(indicatorIds);
                // 循环查询
                getIndicatorIdsByParentIds(indicatorIds, allIndicatorIds);
            }
        }
        return allIndicatorIds;
    }

    @Override
    public List<ExportStatisticInfoXlsDto> getExportStatisticInfoXls(String isHistory, String isOverDue, String updateCycle,
                                                                     String startUpdateTime, String endUpdateTime,
                                                                     String sourceName) {
        return indicatorStatisticsAnalysisMapper.getExportStatisticListByIsHistory(isHistory, isOverDue, updateCycle,
                startUpdateTime, endUpdateTime, sourceName);
    }

    @Override
    public List<GetStatisticAnalysisPageListDto> getNewVerStatisticAnalysisPageList(String sourceName) {
        //查询出所有的部门
        List<GetStatisticAnalysisPageListDto> resultDtoList = sourceManageMapper.getSourceManagePageList(sourceName);
        resultDtoList.forEach(dto -> {
            // 根据PC人员ids,查找指标更新PC提醒人
            if (StringUtils.isNotEmpty(dto.getPcUserIds())) {
                String pcNames = remindUserConfigMapper.getPcUserNamesByPcUserIds(dto.getPcUserIds().split(","));
                dto.setPcUserNames(pcNames);
                // 根据人员id,查找部门
                BaseResult departName = remoteUserService.getDeptNamesById(dto.getPcUserIds().split(","));
                if (null != departName) {
                    dto.setDepartName(String.valueOf(departName.getData()));
                }
            }
            // 根据APP人员ids,查找指标更新APP提醒人
            if (StringUtils.isNotEmpty(dto.getAppUserIds())) {
                String appNames = remindUserConfigMapper.getAppUserNamesByPcUserIds(dto.getAppUserIds().split(","));
                dto.setAppUserNames(appNames);
            }
            // 查找指标总数
            List<GeneralIndicator> indicators = new ArrayList<>();
            // 根据厅局简称,查找指标表的父级指标id
            List<String> indicatorParentIds = generalIndicatorMapper.getParentIndicatorIdBySourceIds(
                    Collections.singletonList(dto.getSourceSimpleName()));
            // 根据父级指标id,查找所有子集指标信息
            if (CollectionUtils.isNotEmpty(indicatorParentIds)) {
                // 所有指标和分组
                List<GeneralIndicator> allIndicators = new ArrayList<>();
                getIndicatorsByParentIds(indicatorParentIds, allIndicators);
                // 将是指标的数据进行筛选出来
                if (CollectionUtils.isNotEmpty(allIndicators)) {
                    for (GeneralIndicator allIndicator : allIndicators) {
                        if (0 == allIndicator.getIndicatorType()) {
                            if ("月度更新".equals(allIndicator.getUpdateCycle()) || "季度更新".equals(allIndicator.getUpdateCycle())
                                    || "半年更新".equals(allIndicator.getUpdateCycle()) || "年度更新".equals(allIndicator.getUpdateCycle())) {
                                indicators.add(allIndicator);
                            }
                        }
                    }
                }
            }
            dto.setTotalCount(indicators.size());
            if (CollectionUtils.isNotEmpty(indicators)) {
                // 判断本月应更新指标,实际更新指标
                IndicatorListDto indicatorListDto = null;
                try {
                    indicatorListDto = checkIndicatorsIsUpdateAndOverDue(indicators);
                } catch (ParseException e) {
                    log.info("checkIndicatorsIsUpdateAndOverDue error :{}", e.getMessage());
                }
                if (Objects.nonNull(indicatorListDto)) {
                    // 当月所有应该更新的指标
                    List<String> indicatorList = new ArrayList<>();
                    indicatorList.addAll(indicatorListDto.getMonthIndicators());
                    indicatorList.addAll(indicatorListDto.getSeasonShouldIndicators());
                    indicatorList.addAll(indicatorListDto.getHalfYearShouldIndicators());
                    indicatorList.addAll(indicatorListDto.getYearShouldIndicators());
                    // 当月所有未更新的指标
                    List<String> notUpdate = new ArrayList<>();
                    notUpdate.addAll(indicatorListDto.getMonthNotUpdate());
                    notUpdate.addAll(indicatorListDto.getSeasonNotUpdate());
                    notUpdate.addAll(indicatorListDto.getHalfYearNotUpdate());
                    notUpdate.addAll(indicatorListDto.getYearNotUpdate());
                    // 设置出参
                    dto.setShouldCount(indicatorList.size());
                    dto.setActualCount((indicatorList.size() - notUpdate.size()));
                } else {
                    dto.setShouldCount(0);
                    dto.setActualCount(0);
                }

            } else {
                dto.setShouldCount(0);
                dto.setActualCount(0);
            }
        });
        return resultDtoList;
    }


    /**
     * 新版本-- 指标更新统计
     */

    @Override
    public List<IndexCensusDto> getIndexCensusPageList(String startTime,
                                                             String endTime,
                                                             String sourceName,
                                                             Integer pageNo,
                                                             Integer pageSize) throws ParseException {
        Date start = IndicatorDateHelper.strToDate(startTime);
        Date end = IndicatorDateHelper.strToDate(endTime);
        if (start == null || end == null) {
            throw new ParseException("时间串转换错误", -1000);
        }

        //查询出所有的部门
//        Page<GetStatisticAnalysisPageListDto> iPage = new Page<>(1, Integer.MAX_VALUE);
        List<GetStatisticAnalysisPageListDto> pageListDtoIPage = sourceManageMapper.getSourceManagePageList(sourceName);

        //按照客户要求，对部门排序
        List<GetStatisticAnalysisPageListDto> dtoList = IndicatorOrderHelper.orderIndicator(pageListDtoIPage);
        int indexA = (pageNo - 1) * pageSize;
        int indexB = Math.min(pageNo * pageSize, dtoList.size());
        List<GetStatisticAnalysisPageListDto> resultDtoList = dtoList.subList(indexA, indexB);

//        Page<IndexCensusDto> result = new Page<>(pageNo, pageSize);
//        result.setTotal(pageListDtoIPage.getTotal());
//        long pages = pageListDtoIPage.getTotal() % pageSize == 0 ? pageListDtoIPage.getTotal() / pageSize : pageListDtoIPage.getTotal() / pageSize + 1;
//        result.setPages(pages);
//        result.setRecords(new ArrayList<>());
        List<IndexCensusDto> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(resultDtoList)) {
            for (GetStatisticAnalysisPageListDto dto : resultDtoList) {
                // 根据PC人员ids,查找指标更新PC提醒人
                if (StringUtils.isNotEmpty(dto.getPcUserIds())) {
                    String pcNames = remindUserConfigMapper.getPcUserNamesByPcUserIds(dto.getPcUserIds().split(","));
                    dto.setPcUserNames(pcNames);
                    // 根据人员id,查找部门
                    BaseResult departName = remoteUserService.getDeptNamesById(dto.getPcUserIds().split(","));
                    if (null != departName) {
                        dto.setDepartName(String.valueOf(departName.getData()));
                    }
                }
                // 根据APP人员ids,查找指标更新APP提醒人
                if (StringUtils.isNotEmpty(dto.getAppUserIds())) {
                    String appNames = remindUserConfigMapper.getAppUserNamesByPcUserIds(dto.getAppUserIds().split(","));
                    dto.setAppUserNames(appNames);
                }
                // 查找指标总数
                List<GeneralIndicator> indicatorList = new ArrayList<>();
                // 根据厅局简称,查找指标表的父级指标id
                List<String> indicatorParentIds = generalIndicatorMapper.getParentIndicatorIdBySourceIds(Collections.singletonList(dto.getSourceSimpleName()));
                // 根据父级指标id,查找所有子集指标信息
                if (CollectionUtils.isNotEmpty(indicatorParentIds)) {
                    // 所有指标和分组
                    List<GeneralIndicator> allIndicators = new ArrayList<>();
                    getIndicatorsByParentIds(indicatorParentIds, allIndicators);
                    // 将是指标的数据进行筛选出来
                    if (CollectionUtils.isNotEmpty(allIndicators)) {
                        for (GeneralIndicator indicator : allIndicators) {
                            if (0 == indicator.getIndicatorType()) {
                                if ("月度更新".equals(indicator.getUpdateCycle()) || "季度更新".equals(indicator.getUpdateCycle())
                                        || "半年更新".equals(indicator.getUpdateCycle()) || "年度更新".equals(indicator.getUpdateCycle())) {
                                    indicatorList.add(indicator);
                                }
                            }
                        }
                    }
                }
                IndexCensusDto indexCensusDto = BeanHelper.copyProperties(dto, IndexCensusDto.class);
                indexCensusDto.setTotalCount(indicatorList.size());
                if (CollectionUtils.isNotEmpty(indicatorList)) {
                    // 统计更新的指标数据
                    IndicatorCensusBean indicatorCensusBean = indexCensusPageHelper.checkIndicatorsIsUpdateAndOverDue(indicatorList, startTime, endTime);
                    if (Objects.nonNull(indicatorCensusBean)) {
                        indexCensusPageHelper.initIndexCensusDto(indexCensusDto, indicatorCensusBean);
                    }
                } else {
                    indexCensusPageHelper.initIndexCensusDto(indexCensusDto);
                }
                //查询信息简报发布条数
                int count = checkLittleMessageCount(indexCensusDto.getSourceSimpleName(), startTime, endTime);
                indexCensusDto.setLittleMessageCount(count);
                result.add(indexCensusDto);
            }
        }
//        return new PageResult<>(result.getRecords(), result.getTotal(), result.getPages());
        return result;
    }


    /**
     * 查询信息简报发布条数
     *
     * @param startTime
     * @param endTime
     */
    private int checkLittleMessageCount(String sourceSimpleName, String startTime, String endTime) {
        //20220401 逻辑调整为该sourceID下所有 MessageIndicatorDetail 当前月数量
        //20220413 业务又让调整为根据入参查询
        //20220424 业务又让调整为根据当前月数量查询
        //20220505 业务又让调整为根据入参查询
        //20220511 业务又让调整为根据当前月数量查询
        //20220701 业务又让调整为根据入参查询
        int count = messageIndDataService.selectMonDetail(sourceSimpleName, startTime, endTime);
        return count;
    }

    /**
     * 判断某个指定厅局下,指标是已更新,未更新,还是部分更新;是否逾期,更新数量等信息
     */
    private IndicatorListDto checkIndicatorsIsUpdateAndOverDue(List<GeneralIndicator> indicators) throws ParseException {
        if (CollectionUtils.isNotEmpty(indicators)) {
            Calendar cal = Calendar.getInstance();
            // 获取当前月(如: 1,2,3...)
            int currentMonth = cal.get(Calendar.MONTH) + 1;
            // 获取季度的第几个月
            int seasonMonth = 1;
            if (currentMonth <= 3) {
                seasonMonth = currentMonth;
            }
            if (4 <= currentMonth && currentMonth <= 6) {
                seasonMonth = currentMonth - 3;
            }
            if (7 <= currentMonth && currentMonth <= 9) {
                seasonMonth = currentMonth - 6;
            }
            if (10 <= currentMonth && currentMonth <= 12) {
                seasonMonth = currentMonth - 9;
            }
            // 获取半年的第几个月
            int halfYearMonth = 1;
            if (currentMonth <= 6) {
                halfYearMonth = currentMonth;
            }
            if (7 <= currentMonth && currentMonth <= 12) {
                halfYearMonth = currentMonth - 6;
            }
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 获取格式化的日期
            SimpleDateFormat monthFormat = new SimpleDateFormat("MM");
            SimpleDateFormat dayFormat = new SimpleDateFormat("dd");
            // 获取当前月月初,月底
            DateThis dateThis = new DateThis();
            String thisMonthStart = dateThis.thisMonth();
            // 获取当前季度初,季度末
            String thisSeasonFirst = dateThis.thisSeason();
            String thisSeasonEnd = dateThis.thisSeasonEnd() + " 23:59:59";
            // 获取半年初,半年末
            String halfYearFirst = dateThis.halfYearFirst();
            String halfYearEnd = dateThis.halfYearEnd() + " 23:59:59";
            // 获取本年初,本年末
            String yearFirst = dateThis.thisYear();
            String yearEnd = dateThis.thisYearEnd() + " 23:59:59";
            // 月度最后更新时间
            String time = "1990-01-01";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date monthLastUpdateTime = simpleDateFormat.parse(time);
            // 季度最后更新时间
            Date seasonLastUpdateTime = simpleDateFormat.parse(time);
            // 半年度最后更新时间
            Date halfYearLastUpdateTime = simpleDateFormat.parse(time);
            // 年度最后更新时间
            Date yearLastUpdateTime = simpleDateFormat.parse(time);
            // 月度逾期,未更新,准时更新
            List<String> monthOverdueUpdate = new ArrayList<>();
            List<String> monthNotUpdate = new ArrayList<>();
            List<String> monthOnTimeUpdate = new ArrayList<>();
            List<String> monthNotOverdueUpdate = new ArrayList<>();
            // 季度逾期,未更新,准时更新
            List<String> seasonOverdueUpdate = new ArrayList<>();
            List<String> seasonNotUpdate = new ArrayList<>();
            List<String> seasonOnTimeUpdate = new ArrayList<>();
            // 半年逾期,未更新,准时更新,未更新且未逾期的
            List<String> halfYearOverdueUpdate = new ArrayList<>();
            List<String> halfYearNotUpdate = new ArrayList<>();
            List<String> halfYearOnTimeUpdate = new ArrayList<>();
            // 年逾期,未更新,准时更新
            List<String> yearOverdueUpdate = new ArrayList<>();
            List<String> yearNotUpdate = new ArrayList<>();
            List<String> yearOnTimeUpdate = new ArrayList<>();
            List<String> yearNotOverdueUpdate = new ArrayList<>();
            // 月度,季度,半年,年度的指标
            List<String> monthIndicators = new ArrayList<>();
            List<String> seasonIndicators = new ArrayList<>();
            List<String> halfYearIndicators = new ArrayList<>();
            List<String> yearIndicators = new ArrayList<>();
            // 季度,半年,年度的指标
            List<String> seasonShouldIndicators = new ArrayList<>();
            List<String> halfYearShouldIndicators = new ArrayList<>();
            List<String> yearShouldIndicators = new ArrayList<>();

            for (GeneralIndicator indicator : indicators) {
                // 判断月度更新的指标
                monthLastUpdateTime = monthUpdateIndicator(indicator, thisMonthStart, dateFormat, dayFormat, monthOverdueUpdate,
                        monthNotUpdate, monthOnTimeUpdate, monthLastUpdateTime, monthIndicators, monthNotOverdueUpdate);
                // 判断季度更新的指标
                seasonLastUpdateTime = seasonUpdateIndicator(indicator, thisSeasonFirst, thisSeasonEnd, dateFormat, dayFormat, monthFormat,
                        seasonOverdueUpdate, seasonNotUpdate, seasonOnTimeUpdate, seasonLastUpdateTime,
                        seasonIndicators, seasonMonth, seasonShouldIndicators);
                // 判断半年更新的指标
                halfYearLastUpdateTime = halfYearUpdateIndicator(indicator, halfYearFirst, halfYearEnd, dateFormat, dayFormat, monthFormat,
                        halfYearOverdueUpdate, halfYearNotUpdate, halfYearOnTimeUpdate, halfYearLastUpdateTime,
                        halfYearIndicators, halfYearMonth, halfYearShouldIndicators);
                // 判断年度更新的指标
                yearLastUpdateTime = yearUpdateIndicator(indicator, yearFirst, yearEnd, dateFormat, dayFormat, monthFormat, yearOverdueUpdate,
                        yearNotUpdate, yearOnTimeUpdate, yearLastUpdateTime, yearIndicators, yearNotOverdueUpdate, currentMonth, yearShouldIndicators);
            }

            IndicatorListDto indicatorListDto = new IndicatorListDto();
            // 月度逾期,未更新,准时更新
            indicatorListDto.setMonthOverdueUpdate(monthOverdueUpdate);
            indicatorListDto.setMonthNotUpdate(monthNotUpdate);
            indicatorListDto.setMonthOnTimeUpdate(monthOnTimeUpdate);
            indicatorListDto.setMonthNotOverdueUpdate(monthNotOverdueUpdate);
            // 季度逾期,未更新,准时更新
            indicatorListDto.setSeasonOverdueUpdate(seasonOverdueUpdate);
            indicatorListDto.setSeasonNotUpdate(seasonNotUpdate);
            indicatorListDto.setSeasonOnTimeUpdate(seasonOnTimeUpdate);
            // 半年逾期,未更新,准时更新,未更新且未逾期的
            indicatorListDto.setHalfYearOverdueUpdate(halfYearOverdueUpdate);
            indicatorListDto.setHalfYearNotUpdate(halfYearNotUpdate);
            indicatorListDto.setHalfYearOnTimeUpdate(halfYearOnTimeUpdate);
            // 年逾期,未更新,准时更新
            indicatorListDto.setYearOverdueUpdate(yearOverdueUpdate);
            indicatorListDto.setYearNotUpdate(yearNotUpdate);
            indicatorListDto.setYearOnTimeUpdate(yearOnTimeUpdate);
            indicatorListDto.setYearNotOverdueUpdate(yearNotOverdueUpdate);
            // 月度,季度,半年,年度的指标
            indicatorListDto.setMonthIndicators(monthIndicators);
            indicatorListDto.setSeasonIndicators(seasonIndicators);
            indicatorListDto.setHalfYearIndicators(halfYearIndicators);
            indicatorListDto.setYearIndicators(yearIndicators);
            // 季度,半年,年度的指标
            indicatorListDto.setSeasonShouldIndicators(seasonShouldIndicators);
            indicatorListDto.setHalfYearShouldIndicators(halfYearShouldIndicators);
            indicatorListDto.setYearShouldIndicators(yearShouldIndicators);

            return indicatorListDto;

        }
        return null;
    }

    /**
     * 判断年度更新的指标
     *
     * @param indicator
     * @param yearFirst
     * @param yearEnd
     * @param dateFormat
     * @param dayFormat
     * @param monthFormat
     * @param yearOverdueUpdate
     * @param yearNotUpdate
     * @param yearOnTimeUpdate
     * @param yearLastUpdateTime
     * @param yearIndicators
     * @param yearNotOverdueUpdate
     * @param currentMonth
     * @param yearShouldIndicators
     * @return
     */
    private Date yearUpdateIndicator(GeneralIndicator indicator, String yearFirst, String yearEnd,
                                     SimpleDateFormat dateFormat, SimpleDateFormat dayFormat, SimpleDateFormat monthFormat,
                                     List<String> yearOverdueUpdate, List<String> yearNotUpdate, List<String> yearOnTimeUpdate,
                                     Date yearLastUpdateTime, List<String> yearIndicators, List<String> yearNotOverdueUpdate,
                                     int currentMonth, List<String> yearShouldIndicators) {
        if ("年度更新".equals(indicator.getUpdateCycle())) {
            yearIndicators.add(indicator.getIndicatorName());
            // 获取最后的更新时间
            if (indicator.getUpdateTime().compareTo(yearLastUpdateTime) > 0) {
                yearLastUpdateTime = indicator.getUpdateTime();
            }
            if (StringUtils.isEmpty(indicator.getPlanUpdateDate())) {
                indicator.setPlanUpdateDate("1-30");
            }
            if (!indicator.getPlanUpdateDate().contains("-")) {
                indicator.setPlanUpdateDate("1-30");
            }
            Integer monthPlanUpdateDate = Integer.valueOf(indicator.getPlanUpdateDate().split("-")[0]);
            Integer dayPlanUpdateDate = Integer.valueOf(indicator.getPlanUpdateDate().split("-")[1]);
            // 获取该月应该更新的年度指标,只判断这个月该更新的年度指标
            if (currentMonth == monthPlanUpdateDate) {
                yearShouldIndicators.add(indicator.getIndicatorName());
                // 用年起始时间进行筛选,查询的年更新的指标
                if (dateFormat.format(indicator.getUpdateTime()).compareTo(yearFirst) >= 0
                        && dateFormat.format(indicator.getUpdateTime()).compareTo(yearEnd) <= 0) {
                    // 筛选年更新了的指标(预期和非逾期的)
                    Integer monthUpdate = Integer.valueOf(monthFormat.format(indicator.getUpdateTime()));
                    Integer dayUpdate = Integer.valueOf(dayFormat.format(indicator.getUpdateTime()));
                    // 判断是否逾期,逾期,实际更新的月,大于约定更新的月
                    if (monthUpdate - monthPlanUpdateDate > 0) {
                        yearOverdueUpdate.add(indicator.getIndicatorName());
                    }
                    // 逾期,实际更新的月,等于约定更新的月,实际更新的日大于约定的日
                    if (monthUpdate - monthPlanUpdateDate == 0 && dayUpdate - dayPlanUpdateDate > 0) {
                        yearOverdueUpdate.add(indicator.getIndicatorName());
                    }
                    // 未更新,实际更新的月小于约定更新的月
                    if (monthUpdate - monthPlanUpdateDate < 0) {
                        yearNotUpdate.add(indicator.getIndicatorName());
                    }
                    // 及时更新的
                    if (monthUpdate - monthPlanUpdateDate == 0 && dayUpdate - dayPlanUpdateDate < 0) {
                        yearOnTimeUpdate.add(indicator.getIndicatorName());
                    }
                } else {
                    // 未更新的
                    yearNotUpdate.add(indicator.getIndicatorName());
                    // 在未更新的里面,判断今天的月份时间,是否超过了约定更新时间,没有超过则为未更新的,且不逾期的
                    if (monthPlanUpdateDate - currentMonth > 0) {
                        yearNotOverdueUpdate.add(indicator.getIndicatorName());
                    }
                    // 如果月份没有超过,判断约定更新的日是否超过了今天的日,没有超过,则为未更新,且不逾期的
                    if (monthPlanUpdateDate - currentMonth == 0
                            && dayPlanUpdateDate - Integer.parseInt(dayFormat.format(new Date())) > 0) {
                        yearNotOverdueUpdate.add(indicator.getIndicatorName());
                    }
                }
            }
        }
        return yearLastUpdateTime;
    }

    /**
     * 判断半年更新的指标
     *
     * @param indicator
     * @param halfYearFirst
     * @param halfYearEnd
     * @param dateFormat
     * @param dayFormat
     * @param monthFormat
     * @param halfYearOverdueUpdate
     * @param halfYearNotUpdate
     * @param halfYearOnTimeUpdate
     * @param halfYearLastUpdateTime
     * @param halfYearIndicators
     * @param halfYearMonth
     * @param halfYearShouldIndicators
     * @return
     */
    private Date halfYearUpdateIndicator(GeneralIndicator indicator, String halfYearFirst, String halfYearEnd,
                                         SimpleDateFormat dateFormat, SimpleDateFormat dayFormat,
                                         SimpleDateFormat monthFormat, List<String> halfYearOverdueUpdate,
                                         List<String> halfYearNotUpdate, List<String> halfYearOnTimeUpdate,
                                         Date halfYearLastUpdateTime, List<String> halfYearIndicators,
                                         int halfYearMonth, List<String> halfYearShouldIndicators) {
        if ("半年更新".equals(indicator.getUpdateCycle())) {
            halfYearIndicators.add(indicator.getIndicatorName());
            // 获取最后的更新时间
            if (indicator.getUpdateTime().compareTo(halfYearLastUpdateTime) > 0) {
                halfYearLastUpdateTime = indicator.getUpdateTime();
            }
            if (StringUtils.isEmpty(indicator.getPlanUpdateDate())) {
                indicator.setPlanUpdateDate("1-25");
            }
            if (!indicator.getPlanUpdateDate().contains("-")) {
                indicator.setPlanUpdateDate("1-25");
            }
            Integer monthPlanUpdateDate = Integer.valueOf(indicator.getPlanUpdateDate().split("-")[0]);
            Integer dayPlanUpdateDate = Integer.valueOf(indicator.getPlanUpdateDate().split("-")[1]);
            // 获取该月应该更新的半年指标,只判断该月要更新的半年指标
            if (monthPlanUpdateDate.equals(halfYearMonth)) {
                halfYearShouldIndicators.add(indicator.getIndicatorName());
                // 格式化指标的更新时间
                Integer monthUpdate = Integer.valueOf(monthFormat.format(indicator.getUpdateTime()));
                Integer dayUpdate = Integer.valueOf(dayFormat.format(indicator.getUpdateTime()));
                // 用半年起始时间进行筛选,查询的半年更新的指标
                if (dateFormat.format(indicator.getUpdateTime()).compareTo(halfYearFirst) >= 0
                        && dateFormat.format(indicator.getUpdateTime()).compareTo(halfYearEnd) <= 0) {
                    if (7 <= monthUpdate) {
                        monthUpdate = monthUpdate - 6;
                    }
                    // 判断是否逾期,逾期,实际更新的月,大于约定更新的月
                    if (monthUpdate - monthPlanUpdateDate > 0) {
                        halfYearOverdueUpdate.add(indicator.getIndicatorName());
                    }
                    // 逾期,实际更新的月,等于约定更新的月,再判断日
                    if (monthUpdate - monthPlanUpdateDate == 0 && dayUpdate - dayPlanUpdateDate > 0) {
                        halfYearOverdueUpdate.add(indicator.getIndicatorName());
                    }
                    // 未更新,实际更新的月小于约定更新的月
                    if (monthUpdate - monthPlanUpdateDate < 0) {
                        halfYearNotUpdate.add(indicator.getIndicatorName());
                    }
                    // 及时更新的
                    if (monthUpdate - monthPlanUpdateDate == 0 && dayUpdate - dayPlanUpdateDate < 0) {
                        halfYearOnTimeUpdate.add(indicator.getIndicatorName());
                    }
                } else {
                    // 未更新的
                    halfYearNotUpdate.add(indicator.getIndicatorName());
//                // 在未更新的里面,判断今天的月份时间,是否超过了约定更新时间,没有超过则为未更新的,且不逾期的
//                if (monthPlanUpdateDate - Integer.parseInt(monthFormat.format(new Date())) > 0) {
//                    halfYearNotOverdueUpdate.add(indicator.getIndicatorName());
//                }
//                // 如果月份没有超过,判断约定更新的日是否超过了今天的日,没有超过,则为未更新,且不逾期的
//                if (monthPlanUpdateDate - Integer.parseInt(monthFormat.format(new Date())) == 0
//                        && dayPlanUpdateDate - Integer.parseInt(dayFormat.format(new Date())) > 0) {
//                    halfYearNotOverdueUpdate.add(indicator.getIndicatorName());
//                }
//                    // 在未更新的里面,判断今天的月份时间,是否超过了约定更新时间,没有超过则为未更新的,且不逾期的
//                    if (monthPlanUpdateDate - halfYearMonth > 0) {
//                        halfYearNotOverdueUpdate.add(indicator.getIndicatorName());
//                    }
//                    // 如果月份没有超过,判断约定更新的日是否超过了今天的日,没有超过,则为未更新,且不逾期的
//                    if (monthPlanUpdateDate - halfYearMonth == 0
//                            && dayPlanUpdateDate - Integer.parseInt(dayFormat.format(new Date())) > 0) {
//                        halfYearNotOverdueUpdate.add(indicator.getIndicatorName());
//                    }
                }
            }

        }
        return halfYearLastUpdateTime;
    }

    /**
     * 判断季度更新的指标
     *
     * @param indicator
     * @param thisSeasonFirst
     * @param thisSeasonEnd
     * @param dateFormat
     * @param dayFormat
     * @param monthFormat
     * @param seasonOverdueUpdate
     * @param seasonNotUpdate
     * @param seasonOnTimeUpdate
     * @param seasonLastUpdateTime
     * @param seasonIndicators
     * @param seasonMonth
     * @param seasonShouldIndicators
     * @return
     */
    private Date seasonUpdateIndicator(GeneralIndicator indicator, String thisSeasonFirst, String thisSeasonEnd,
                                       SimpleDateFormat dateFormat, SimpleDateFormat dayFormat, SimpleDateFormat monthFormat,
                                       List<String> seasonOverdueUpdate, List<String> seasonNotUpdate,
                                       List<String> seasonOnTimeUpdate, Date seasonLastUpdateTime,
                                       List<String> seasonIndicators, int seasonMonth,
                                       List<String> seasonShouldIndicators) {
        if ("季度更新".equals(indicator.getUpdateCycle())) {
            seasonIndicators.add(indicator.getIndicatorName());
            // 获取最后的更新时间
            if (indicator.getUpdateTime().compareTo(seasonLastUpdateTime) > 0) {
                seasonLastUpdateTime = indicator.getUpdateTime();
            }
            if (StringUtils.isEmpty(indicator.getPlanUpdateDate())) {
                indicator.setPlanUpdateDate("1-15");
            }
            if (!indicator.getPlanUpdateDate().contains("-")) {
                indicator.setPlanUpdateDate("1-15");
            }
            // 获取约定更新时间的月和日
            Integer monthPlanUpdateDate = Integer.valueOf(indicator.getPlanUpdateDate().split("-")[0]);
            Integer dayPlanUpdateDate = Integer.valueOf(indicator.getPlanUpdateDate().split("-")[1]);
            // 获取该月应该更新的季度指标,只判断本月该更新的季度指标
            if (monthPlanUpdateDate.equals(seasonMonth)) {
                seasonShouldIndicators.add(indicator.getIndicatorName());
                // 用季度起始时间进行筛选,查询的季度更新的指标
                if (dateFormat.format(indicator.getUpdateTime()).compareTo(thisSeasonFirst) >= 0
                        && dateFormat.format(indicator.getUpdateTime()).compareTo(thisSeasonEnd) <= 0) {
                    // 指标更新时间,比上个季度初的时间大,则表示已更新了
                    Integer monthUpdate = Integer.valueOf(monthFormat.format(indicator.getUpdateTime()));
                    Integer dayUpdate = Integer.valueOf(dayFormat.format(indicator.getUpdateTime()));
                    if (4 <= monthUpdate && 6 >= monthUpdate) {
                        monthUpdate = monthUpdate - 3;
                    }
                    if (7 <= monthUpdate && 9 >= monthUpdate) {
                        monthUpdate = monthUpdate - 6;
                    }
                    if (10 <= monthUpdate) {
                        monthUpdate = monthUpdate - 9;
                    }
                    // 判断是否逾期,逾期,实际更新的月,大于约定更新的月
                    if (monthUpdate - monthPlanUpdateDate > 0) {
                        seasonOverdueUpdate.add(indicator.getIndicatorName());
                    }
                    // 逾期,实际更新的月,等于约定更新的月,再判断日
                    if (monthUpdate - monthPlanUpdateDate == 0 && dayUpdate - dayPlanUpdateDate > 0) {
                        seasonOverdueUpdate.add(indicator.getIndicatorName());
                    }
                    // 未更新,实际更新的月小于约定更新的月
                    if (monthUpdate - monthPlanUpdateDate < 0) {
                        seasonNotUpdate.add(indicator.getIndicatorName());
                    }
                    // 及时更新的
                    if (monthUpdate - monthPlanUpdateDate == 0 && dayUpdate - dayPlanUpdateDate <= 0) {
                        seasonOnTimeUpdate.add(indicator.getIndicatorName());
                    }
                } else {
                    // 季度未更新的
                    seasonNotUpdate.add(indicator.getIndicatorName());
//                // 在未更新的里面,判断今天的月份时间,是否超过了约定更新时间,没有超过则为未更新的,且不逾期的
//                if (monthPlanUpdateDate - Integer.parseInt(monthFormat.format(new Date())) > 0) {
//                    seasonNotOverdueUpdate.add(indicator.getIndicatorName());
//                }
//                // 如果月份没有超过,判断约定更新的日是否超过了今天的日,没有超过,则为未更新,且不逾期的
//                if (monthPlanUpdateDate - Integer.parseInt(monthFormat.format(new Date())) == 0
//                        && dayPlanUpdateDate - Integer.parseInt(dayFormat.format(new Date())) > 0) {
//                    seasonNotOverdueUpdate.add(indicator.getIndicatorName());
//                }
//                    // 在未更新的里面,判断今天的月份时间,是否超过了约定更新时间,没有超过则为未更新的,且不逾期的
//                    if (monthPlanUpdateDate - seasonMonth > 0) {
//                        seasonNotOverdueUpdate.add(indicator.getIndicatorName());
//                    }
//                    // 如果月份没有超过,判断约定更新的日是否超过了今天的日,没有超过,则为未更新,且不逾期的
//                    if (monthPlanUpdateDate - seasonMonth == 0
//                            && dayPlanUpdateDate - Integer.parseInt(dayFormat.format(new Date())) > 0) {
//                        seasonNotOverdueUpdate.add(indicator.getIndicatorName());
//                    }
                }
            }
        }
        return seasonLastUpdateTime;
    }

    /**
     * 判断月度更新的指标
     *
     * @param indicator
     * @param thisMonthStart
     * @param dateFormat
     * @param dayFormat
     * @param monthOverdueUpdate
     * @param monthNotUpdate
     * @param monthOnTimeUpdate
     * @param lastUpdateTime
     * @param monthIndicators
     * @param monthNotOverdueUpdate
     * @return
     */
    private Date monthUpdateIndicator(GeneralIndicator indicator, String thisMonthStart, SimpleDateFormat dateFormat,
                                      SimpleDateFormat dayFormat, List<String> monthOverdueUpdate,
                                      List<String> monthNotUpdate, List<String> monthOnTimeUpdate, Date lastUpdateTime,
                                      List<String> monthIndicators, List<String> monthNotOverdueUpdate) {
        if ("月度更新".equals(indicator.getUpdateCycle())) {
            monthIndicators.add(indicator.getIndicatorName());
            // 获取最后的更新时间
            if (indicator.getUpdateTime().compareTo(lastUpdateTime) > 0) {
                lastUpdateTime = indicator.getUpdateTime();
            }
            if (StringUtils.isEmpty(indicator.getPlanUpdateDate())) {
                indicator.setPlanUpdateDate("10");
            }
            // 筛选这个月更新了的指标(逾期和非逾期的)
            if (dateFormat.format(indicator.getUpdateTime()).compareTo(thisMonthStart) >= 0) {
                // 判断是否逾期
                if (Integer.parseInt(dayFormat.format(indicator.getUpdateTime()))
                        - Integer.parseInt(indicator.getPlanUpdateDate()) > 0) {
                    monthOverdueUpdate.add(indicator.getIndicatorName());
                } else {
                    monthOnTimeUpdate.add(indicator.getIndicatorName());
                }
            } else {
                // 未更新的
                monthNotUpdate.add(indicator.getIndicatorName());
                // 在未更新的里面,约定更新的日,大于当前日,则为未更,且不逾期的
                if (Integer.parseInt(indicator.getPlanUpdateDate()) - Integer.parseInt(dayFormat.format(new Date())) > 0) {
                    monthNotOverdueUpdate.add(indicator.getIndicatorName());
                }
            }
        }
        return lastUpdateTime;
    }

    /**
     * 根据父级指标id,查找所有子集指标全部信息
     *
     * @param indicatorParentIds
     * @param indicators
     */
    private List<GeneralIndicator> getIndicatorsByParentIds(List<String> indicatorParentIds, List<GeneralIndicator> indicators) {
        if (!org.springframework.util.CollectionUtils.isEmpty(indicatorParentIds)) {
            List<GeneralIndicator> indicatorInfos = generalIndicatorMapper.getIndicatorsByParentIds(indicatorParentIds);
            if (!org.springframework.util.CollectionUtils.isEmpty(indicatorInfos)) {
                indicators.addAll(indicatorInfos);
                List<String> ids = new ArrayList<>();
                for (GeneralIndicator indicatorInfo : indicatorInfos) {
                    ids.add(indicatorInfo.getId());
                }
                // 循环查询
                getIndicatorsByParentIds(ids, indicators);
            }
        }
        return indicators;
    }

    @Override
    public List<GetStatisticInfoByIdDto> getExportStatisticDetailXls(String sourceId) {
        String sourceSimpleName = sourceManageMapper.getSourceSimpleNameBySourceId(Long.valueOf(sourceId));
        List<String> indicatorParentIds = generalIndicatorMapper.getParentIndicatorIdsBySourceId(sourceSimpleName);
        if (CollectionUtils.isNotEmpty(indicatorParentIds)) {
            // 所有指标和分组
            List<String> allIndicatorIds = new ArrayList<>();
            getIndicatorIdsByParentIds(indicatorParentIds, allIndicatorIds);
            List<GetStatisticInfoByIdDto> listDto = indicatorStatisticsAnalysisMapper.getAllIndicatorByIndicatorIds(allIndicatorIds);
            if (CollectionUtils.isNotEmpty(listDto)) {
                // 根据厅局id,查找审核人,审核时间,标题
                GetStatisticInfoByIdDto dto1 = indicatorStatisticsAnalysisMapper.getAuditNameBySourceId(Long.valueOf(sourceId));
                String sourceName = sourceManageMapper.getSourceNameBySourceId(Long.valueOf(sourceId));
                SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy年MM月");
                String title = sourceName + dateFormat2.format(new Date()) + "指标更新统计分析详情";
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                // 获取格式化的日期
                SimpleDateFormat monthFormat = new SimpleDateFormat("MM");
                SimpleDateFormat dayFormat = new SimpleDateFormat("dd");
                // 获取当前月月初,月底
                DateThis dateThis = new DateThis();
                String thisMonthStart = dateThis.thisMonth();
                Calendar cal = Calendar.getInstance();
                int currentMonth = cal.get(Calendar.MONTH) + 1;
                // 获取季度的第几个月
                int seasonMonth = 1;
                if (currentMonth <= 3) {
                    seasonMonth = currentMonth;
                }
                if (4 <= currentMonth && currentMonth <= 6) {
                    seasonMonth = currentMonth - 3;
                }
                if (7 <= currentMonth && currentMonth <= 9) {
                    seasonMonth = currentMonth - 6;
                }
                if (10 <= currentMonth && currentMonth <= 12) {
                    seasonMonth = currentMonth - 9;
                }
                // 获取半年的第几个月
                int halfYearMonth = 1;
                if (currentMonth <= 6) {
                    halfYearMonth = currentMonth;
                }
                if (7 <= currentMonth && currentMonth <= 12) {
                    halfYearMonth = currentMonth - 6;
                }
                for (GetStatisticInfoByIdDto dto : listDto) {
                    if (ObjectUtils.isNotEmpty(dto1)) {
                        dto.setAuditUserName(dto1.getAuditUserName());
                        dto.setAuditTime(dto1.getAuditTime());
                    }
                    dto.setTitle(title);
                    //补充拼写父类指标名称
                    if (StringUtils.isNotEmpty(dto.getParentName())){
                        dto.setIndicatorName(dto.getParentName() + "-" +dto.getIndicatorName());
                    }
                    // 判断本月是否需要更新
                    if ("月度更新".equals(dto.getUpdateCycle())) {
                        if (StringUtils.isEmpty(dto.getPlanUpdateDate())) {
                            dto.setPlanUpdateDate("10");
                        }
                        dto.setIsShouldUpdate("1");
                        // 判断更新情况和逾期情况
                        if (dateFormat.format(dto.getUpdateTime()).compareTo(thisMonthStart) >= 0) {
                            dto.setIsUpdate("1");
                            // 判断是否逾期
                            if (Integer.parseInt(dayFormat.format(dto.getUpdateTime()))
                                    - Integer.parseInt(dto.getPlanUpdateDate()) > 0) {
                                dto.setIsOverDue("1");
                            } else {
                                dto.setIsOverDue("0");
                            }
                        } else {
                            dto.setIsUpdate("0");
                            dto.setIsOverDue("1");
                            // 未更新的,在未更新的里面,约定更新的日,大于当前日,则为未更,且不逾期的
                            if (Integer.parseInt(dto.getPlanUpdateDate()) - Integer.parseInt(dayFormat.format(new Date())) > 0) {
                                dto.setIsOverDue("0");
                            }
                        }
                    }
                    if ("季度更新".equals(dto.getUpdateCycle())) {
                        // 获取约定更新时间的月和日
                        if (StringUtils.isEmpty(dto.getPlanUpdateDate())) {
                            dto.setPlanUpdateDate("1-15");
                        }
                        if (!dto.getPlanUpdateDate().contains("-")) {
                            dto.setPlanUpdateDate("1-15");
                        }
                        Integer monthPlanUpdateDate = Integer.valueOf(dto.getPlanUpdateDate().split("-")[0]);
                        Integer dayPlanUpdateDate = Integer.valueOf(dto.getPlanUpdateDate().split("-")[1]);
                        // 获取该月应该更新的季度指标
                        if (monthPlanUpdateDate.equals(seasonMonth)) {
                            dto.setIsShouldUpdate("1");
                            // 用季度起始时间进行筛选,查询的季度更新的指标;指标更新时间,比上个季度初的时间大,则表示已更新了
                            Integer monthUpdate = Integer.valueOf(monthFormat.format(dto.getUpdateTime()));
                            Integer dayUpdate = Integer.valueOf(dayFormat.format(dto.getUpdateTime()));
                            if (4 <= monthUpdate && 6 >= monthUpdate) {
                                monthUpdate = monthUpdate - 3;
                            }
                            if (7 <= monthUpdate && 9 >= monthUpdate) {
                                monthUpdate = monthUpdate - 6;
                            }
                            if (10 <= monthUpdate) {
                                monthUpdate = monthUpdate - 9;
                            }
                            // 判断本月更新的指标,是否逾期
                            if (0 <= dateFormat.format(dto.getUpdateTime()).compareTo(thisMonthStart)) {
                                // 逾期,实际更新的月,等于约定更新的月,判断日
                                if (dayUpdate - dayPlanUpdateDate > 0) {
                                    dto.setIsUpdate("1");
                                    dto.setIsOverDue("1");
                                }
                                if (dayUpdate - dayPlanUpdateDate <= 0) {
                                    dto.setIsUpdate("1");
                                    dto.setIsOverDue("0");
                                }
                            }
                            // 未在本月更新的指标,判断更新日期,是否比当月月初小,小的话为逾期
                            if (0 > dateFormat.format(dto.getUpdateTime()).compareTo(thisMonthStart)) {
                                // 未更新,逾期
                                dto.setIsUpdate("0");
                                dto.setIsOverDue("1");
                            }
                        } else {
                            dto.setIsShouldUpdate("0");
                            dto.setIsUpdate("0");
                            dto.setIsOverDue("0");
                        }
                    }
                    if ("半年更新".equals(dto.getUpdateCycle())) {
                        // 获取约定更新时间的月和日
                        if (StringUtils.isEmpty(dto.getPlanUpdateDate())) {
                            dto.setPlanUpdateDate("1-25");
                        }
                        if (!dto.getPlanUpdateDate().contains("-")) {
                            dto.setPlanUpdateDate("1-25");
                        }
                        Integer monthPlanUpdateDate = Integer.valueOf(dto.getPlanUpdateDate().split("-")[0]);
                        Integer dayPlanUpdateDate = Integer.valueOf(dto.getPlanUpdateDate().split("-")[1]);
                        // 获取该月应该更新的季度指标
                        if (monthPlanUpdateDate.equals(halfYearMonth)) {
                            dto.setIsShouldUpdate("1");
                            // 格式化指标的更新时间
                            Integer monthUpdate = Integer.valueOf(monthFormat.format(dto.getUpdateTime()));
                            Integer dayUpdate = Integer.valueOf(dayFormat.format(dto.getUpdateTime()));
                            if (7 <= monthUpdate) {
                                monthUpdate = monthUpdate - 6;
                            }
                            // 判断本月更新的指标,是否逾期
                            if (0 <= dateFormat.format(dto.getUpdateTime()).compareTo(thisMonthStart)) {
                                // 逾期,实际更新的月,等于约定更新的月,判断日
                                if (dayUpdate - dayPlanUpdateDate > 0) {
                                    dto.setIsUpdate("1");
                                    dto.setIsOverDue("1");
                                }
                                if (dayUpdate - dayPlanUpdateDate <= 0) {
                                    dto.setIsUpdate("1");
                                    dto.setIsOverDue("0");
                                }
                            }
                            // 未在本月更新的指标,判断更新日期,是否比当月月初小,小的话为逾期
                            if (0 > dateFormat.format(dto.getUpdateTime()).compareTo(thisMonthStart)) {
                                // 未更新,逾期
                                dto.setIsUpdate("0");
                                dto.setIsOverDue("1");
                            }
                        } else {
                            dto.setIsShouldUpdate("0");
                            dto.setIsUpdate("0");
                            dto.setIsOverDue("0");
                        }
                    }
                    if ("年度更新".equals(dto.getUpdateCycle())) {
                        // 获取约定更新时间的月和日
                        if (StringUtils.isEmpty(dto.getPlanUpdateDate())) {
                            dto.setPlanUpdateDate("1-30");
                        }
                        if (!dto.getPlanUpdateDate().contains("-")) {
                            dto.setPlanUpdateDate("1-30");
                        }
                        Integer monthPlanUpdateDate = Integer.valueOf(dto.getPlanUpdateDate().split("-")[0]);
                        Integer dayPlanUpdateDate = Integer.valueOf(dto.getPlanUpdateDate().split("-")[1]);
                        // 获取该月应该更新的年度指标
                        if (monthPlanUpdateDate.equals(currentMonth)) {
                            dto.setIsShouldUpdate("1");
                            // 用年起始时间进行筛选,查询的年更新的指标
                            // 筛选年更新了的指标(预期和非逾期的)
                            Integer monthUpdate = Integer.valueOf(monthFormat.format(dto.getUpdateTime()));
                            Integer dayUpdate = Integer.valueOf(dayFormat.format(dto.getUpdateTime()));
                            // 判断本月更新的指标,是否逾期
                            if (0 <= dateFormat.format(dto.getUpdateTime()).compareTo(thisMonthStart)) {
                                // 逾期,实际更新的月,等于约定更新的月,判断日
                                if (dayUpdate - dayPlanUpdateDate > 0) {
                                    dto.setIsUpdate("1");
                                    dto.setIsOverDue("1");
                                }
                                if (dayUpdate - dayPlanUpdateDate <= 0) {
                                    dto.setIsUpdate("1");
                                    dto.setIsOverDue("0");
                                }
                            }
                            // 未在本月更新的指标,判断更新日期,是否比当月月初小,小的话为逾期
                            if (0 > dateFormat.format(dto.getUpdateTime()).compareTo(thisMonthStart)) {
                                // 未更新,逾期
                                dto.setIsUpdate("0");
                                dto.setIsOverDue("1");
                            }
                        } else {
                            dto.setIsShouldUpdate("0");
                            dto.setIsUpdate("0");
                            dto.setIsOverDue("0");
                        }
                    }
                }
            }
            return listDto;
        }
        return null;
    }

    @Override
    public List<GetStatisticAnalysisPageListDto> getUpdateStatistics(String date) {
//        List<IndexCensusDto> result = new ArrayList<>();
        //查询出所有的部门
        List<GetStatisticAnalysisPageListDto> sourceManages = sourceManageMapper.getSourceForAnalysis();
        //查询出历史数据
        Set<Integer> sourceIds = sourceManages.stream().map(GetStatisticAnalysisPageListDto::getSourceId).collect(Collectors.toSet());
        //根据sourceId和查询时间获取统计数据
        List<IndicatorStatisticsAnalysis> analyses = indicatorStatisticsAnalysisMapper.getAnalysisBysourceId(sourceIds, date);
        //填写数据
        //每一项处理/年度更新/半年更新/季度更新/月度更新，数据统计
        sourceManages.forEach(dto -> {
//            IndexCensusDto indexCensusDto = BeanHelper.copyProperties(dto, IndexCensusDto.class);

            List<IndicatorStatisticsAnalysis> sourceAnalyses = analyses.stream()
                    .filter(analyse -> analyse.getSourceId().equals(dto.getSourceId())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(sourceAnalyses)) {
                //指标总数
                int totalCount = 0;
                //应更指标总数
                int shouldCount = 0;
                //已更指标总数
                int changedCount = 0;
                //年度指标
                IndicatorStatisticsAnalysis yAnalyse = sourceAnalyses.stream().filter(yA -> "年度更新".equals(yA.getUpdateCycle()))
                        .max(Comparator.comparing(IndicatorStatisticsAnalysis::getCreateTime))
                        .orElse(null);
                IndexCensusBean yearIndex = new IndexCensusBean();
                if (null != yAnalyse) {
                    yearIndex.setShouldCount(StringUtils.isNotEmpty(yAnalyse.getShouldUpdate()) ?
                            Integer.parseInt(yAnalyse.getShouldUpdate().substring(0, yAnalyse.getShouldUpdate().length() - 1)) : 0);
                    yearIndex.setActualCount(StringUtils.isNotEmpty(yAnalyse.getActualUpdate()) ?
                            Integer.parseInt(yAnalyse.getActualUpdate().substring(0, yAnalyse.getActualUpdate().length() - 1)) : 0);
                    yearIndex.setUnUpdateCount(StringUtils.isNotEmpty(yAnalyse.getNotUpdate()) ?
                            Integer.parseInt(yAnalyse.getNotUpdate().substring(0, yAnalyse.getNotUpdate().length() - 1)) : 0);
                    yearIndex.setOverdueCount(StringUtils.isNotEmpty(yAnalyse.getOverDueUpdate()) ?
                            Integer.parseInt(yAnalyse.getOverDueUpdate().substring(0, yAnalyse.getOverDueUpdate().length() - 1)) : 0);
                    yearIndex.setTotalCount(!"".equals(String.valueOf(yAnalyse.getTotalCount())) ? yAnalyse.getTotalCount() : 0);
                    totalCount = totalCount + yearIndex.getTotalCount();
                    shouldCount = shouldCount + yearIndex.getShouldCount();
                    changedCount = changedCount + yearIndex.getActualCount();
                } else {
                    yearIndex.setShouldCount(0);
                    yearIndex.setActualCount(0);
                    yearIndex.setUnUpdateCount(0);
                    yearIndex.setOverdueCount(0);
                    yearIndex.setTotalCount(0);
                }
                assert dto != null;
                dto.setYearIndex(yearIndex);

                //半年更新
                IndicatorStatisticsAnalysis hYAnalyse = sourceAnalyses.stream().filter(yA -> "半年更新".equals(yA.getUpdateCycle()))
                        .max(Comparator.comparing(IndicatorStatisticsAnalysis::getCreateTime))
                        .orElse(null);
                IndexCensusBean hYearIndex = new IndexCensusBean();
                if (null != hYAnalyse) {
                    hYearIndex.setShouldCount(StringUtils.isNotEmpty(hYAnalyse.getShouldUpdate()) ?
                            Integer.parseInt(hYAnalyse.getShouldUpdate().substring(0, hYAnalyse.getShouldUpdate().length() - 1)) : 0);
                    hYearIndex.setActualCount(StringUtils.isNotEmpty(hYAnalyse.getActualUpdate()) ?
                            Integer.parseInt(hYAnalyse.getActualUpdate().substring(0, hYAnalyse.getActualUpdate().length() - 1)) : 0);
                    hYearIndex.setUnUpdateCount(StringUtils.isNotEmpty(hYAnalyse.getNotUpdate()) ?
                            Integer.parseInt(hYAnalyse.getNotUpdate().substring(0, hYAnalyse.getNotUpdate().length() - 1)) : 0);
                    hYearIndex.setOverdueCount(StringUtils.isNotEmpty(hYAnalyse.getOverDueUpdate()) ?
                            Integer.parseInt(hYAnalyse.getOverDueUpdate().substring(0, hYAnalyse.getOverDueUpdate().length() - 1)) : 0);
                    hYearIndex.setTotalCount(!"".equals(String.valueOf(hYAnalyse.getTotalCount())) ? hYAnalyse.getTotalCount() : 0);
                    totalCount = totalCount + hYearIndex.getTotalCount();
                    shouldCount = shouldCount + hYearIndex.getShouldCount();
                    changedCount = changedCount + hYearIndex.getActualCount();
                } else {
                    hYearIndex.setShouldCount(0);
                    hYearIndex.setActualCount(0);
                    hYearIndex.setUnUpdateCount(0);
                    hYearIndex.setOverdueCount(0);
                    hYearIndex.setTotalCount(0);
                }
                dto.setHalfYearIndex(hYearIndex);

                //季度更新
                IndicatorStatisticsAnalysis sAnalyse = sourceAnalyses.stream().filter(yA -> "季度更新".equals(yA.getUpdateCycle()))
                        .max(Comparator.comparing(IndicatorStatisticsAnalysis::getCreateTime))
                        .orElse(null);
                IndexCensusBean seasonIndex = new IndexCensusBean();
                if (null != sAnalyse) {
                    seasonIndex.setShouldCount(StringUtils.isNotEmpty(sAnalyse.getShouldUpdate()) ?
                            Integer.parseInt(sAnalyse.getShouldUpdate().substring(0, sAnalyse.getShouldUpdate().length() - 1)) : 0);
                    seasonIndex.setActualCount(StringUtils.isNotEmpty(sAnalyse.getActualUpdate()) ?
                            Integer.parseInt(sAnalyse.getActualUpdate().substring(0, sAnalyse.getActualUpdate().length() - 1)) : 0);
                    seasonIndex.setUnUpdateCount(StringUtils.isNotEmpty(sAnalyse.getNotUpdate()) ?
                            Integer.parseInt(sAnalyse.getNotUpdate().substring(0, sAnalyse.getNotUpdate().length() - 1)) : 0);
                    seasonIndex.setOverdueCount(StringUtils.isNotEmpty(sAnalyse.getOverDueUpdate()) ?
                            Integer.parseInt(sAnalyse.getOverDueUpdate().substring(0, sAnalyse.getOverDueUpdate().length() - 1)) : 0);
                    seasonIndex.setTotalCount(!"".equals(String.valueOf(sAnalyse.getTotalCount())) ? sAnalyse.getTotalCount() : 0);
                    totalCount = totalCount + seasonIndex.getTotalCount();
                    shouldCount = shouldCount + seasonIndex.getShouldCount();
                    changedCount = changedCount + seasonIndex.getActualCount();
                } else {
                    seasonIndex.setShouldCount(0);
                    seasonIndex.setActualCount(0);
                    seasonIndex.setUnUpdateCount(0);
                    seasonIndex.setOverdueCount(0);
                    seasonIndex.setTotalCount(0);
                }
                dto.setSeasonIndex(seasonIndex);

                //月度更新
                IndicatorStatisticsAnalysis mAnalyse = sourceAnalyses.stream().filter(yA -> "月度更新".equals(yA.getUpdateCycle()))
                        .max(Comparator.comparing(IndicatorStatisticsAnalysis::getCreateTime))
                        .orElse(null);
                IndexCensusBean monthIndex = new IndexCensusBean();
                if (null != mAnalyse) {
                    monthIndex.setShouldCount(StringUtils.isNotEmpty(mAnalyse.getShouldUpdate()) ?
                            Integer.parseInt(mAnalyse.getShouldUpdate().substring(0, mAnalyse.getShouldUpdate().length() - 1)) : 0);
                    monthIndex.setActualCount(StringUtils.isNotEmpty(mAnalyse.getActualUpdate()) ?
                            Integer.parseInt(mAnalyse.getActualUpdate().substring(0, mAnalyse.getActualUpdate().length() - 1)) : 0);
                    monthIndex.setUnUpdateCount(StringUtils.isNotEmpty(mAnalyse.getNotUpdate()) ?
                            Integer.parseInt(mAnalyse.getNotUpdate().substring(0, mAnalyse.getNotUpdate().length() - 1)) : 0);
                    monthIndex.setOverdueCount(StringUtils.isNotEmpty(mAnalyse.getOverDueUpdate()) ?
                            Integer.parseInt(mAnalyse.getOverDueUpdate().substring(0, mAnalyse.getOverDueUpdate().length() - 1)) : 0);
                    monthIndex.setTotalCount(!"".equals(String.valueOf(mAnalyse.getTotalCount())) ? mAnalyse.getTotalCount() : 0);
                    totalCount = totalCount + monthIndex.getTotalCount();
                    shouldCount = shouldCount + monthIndex.getShouldCount();
                    changedCount = changedCount + monthIndex.getActualCount();
                } else {
                    monthIndex.setShouldCount(0);
                    monthIndex.setActualCount(0);
                    monthIndex.setUnUpdateCount(0);
                    monthIndex.setOverdueCount(0);
                    monthIndex.setTotalCount(0);
                }
                dto.setMonthIndex(monthIndex);

                //指标总数
                dto.setTotalCount(totalCount);
                //应更指标总数
                dto.setShouldCount(shouldCount);
                //已更指标总数
                dto.setChangedCount(changedCount);
            } else {
                //从未使用过的厅局直接补0
                IndexCensusBean yearIndex = new IndexCensusBean();
                yearIndex.setShouldCount(0);
                yearIndex.setActualCount(0);
                yearIndex.setUnUpdateCount(0);
                yearIndex.setOverdueCount(0);
                yearIndex.setTotalCount(0);
                dto.setYearIndex(yearIndex);

                IndexCensusBean hYearIndex = new IndexCensusBean();
                hYearIndex.setShouldCount(0);
                hYearIndex.setActualCount(0);
                hYearIndex.setUnUpdateCount(0);
                hYearIndex.setOverdueCount(0);
                hYearIndex.setTotalCount(0);
                dto.setHalfYearIndex(hYearIndex);

                IndexCensusBean seasonIndex = new IndexCensusBean();
                seasonIndex.setShouldCount(0);
                seasonIndex.setActualCount(0);
                seasonIndex.setUnUpdateCount(0);
                seasonIndex.setOverdueCount(0);
                seasonIndex.setTotalCount(0);
                dto.setSeasonIndex(seasonIndex);

                IndexCensusBean monthIndex = new IndexCensusBean();
                monthIndex.setShouldCount(0);
                monthIndex.setActualCount(0);
                monthIndex.setUnUpdateCount(0);
                monthIndex.setOverdueCount(0);
                monthIndex.setTotalCount(0);
                dto.setMonthIndex(monthIndex);

                //指标总数
                dto.setTotalCount(0);
                //应更指标总数
                dto.setShouldCount(0);
                //已更指标总数
                dto.setChangedCount(0);
            }

            //查询信息简报发布条数
            //date -- 202206 计算出对应的开始时间，和结束时间
            int count = checkLittleMessageCount(dto.getSourceSimpleName(), monthFirst(date), monthLast(date));
            dto.setLittleMessageCount(count);
        });

//        return new PageResult<>(result.getRecords(), result.getTotal(), result.getPages());
        return sourceManages;
    }

    /**
     * 月初
     *
     * @return
     */
    private String monthFirst(String strDate) {
        //strData 202105
        LocalDate first = LocalDate.of(Integer.parseInt(strDate.substring(0, 4)), Integer.parseInt(strDate.substring(4, 6)), 1);
        Timestamp firstTimestamp = Timestamp.valueOf(first.atTime(LocalTime.MIDNIGHT));
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return format.format(new Date(firstTimestamp.getTime()));
    }

    /**
     * 月末
     *
     * @return
     */
    private String monthLast(String strDate) {
        //strData 202105
        LocalDate first = LocalDate.of(Integer.parseInt(strDate.substring(0, 4)), Integer.parseInt(strDate.substring(4, 6)), 1);
        LocalDate last = first.with(TemporalAdjusters.lastDayOfMonth());
        Timestamp lastTimestamp = Timestamp.valueOf(last.atTime(LocalTime.MIDNIGHT));
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return (format.format(new Date(lastTimestamp.getTime()))).substring(0, 11) + "23:59:59";
    }

    /**
     * 判断当前指标是否在对应周期内更新过,更新过返回true，未更新返回false
     * @param updateTime
     * @param updateCycle
     * @return
     */
    private boolean checkUpdate(Date updateTime, String updateCycle) {
        Calendar cal = Calendar.getInstance();
        int mon = cal.get(Calendar.MONTH) + 1;
        try {
            switch (updateCycle) {
                case "年度更新":
                    //年初
                    Date yDate = null;
                    yDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                            .parse(new SimpleDateFormat("yyyy").format(new Date()) + "-01-01 00:00:00");
                    return updateTime.after(yDate);
                case "半年更新":
                    //当前半年初
                    if (mon >= 6) {
                        mon = 6;
                    } else {
                        mon = 1;
                    }
                    Date hyDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                            .parse(new SimpleDateFormat("yyyy").format(new Date()) + "-" + mon + "-01 00:00:00");
                    return updateTime.after(hyDate);
                case "季度更新":
                    //当前季度初
                    if (mon <= 3) {
                        mon = 1;
                    } else if (mon <= 6) {
                        mon = 3;
                    } else if (mon <= 9) {
                        mon = 6;
                    } else {
                        mon = 9;
                    }
                    Date sDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                            .parse(new SimpleDateFormat("yyyy").format(new Date()) + "-" + mon + "-01 00:00:00");
                    return updateTime.after(sDate);
                default:
                    return false;
            }
        } catch (ParseException e) {
            log.error("checkUpdate error :{}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断当前指标是否在对应周期内逾期,逾期返回true，未逾期返回false
     * @param updateTime
     * @param planTime
     * @param updateCycle
     * @return
     */
    private boolean checkOverDue(Date updateTime, String planTime, String updateCycle) {
        Calendar cal = Calendar.getInstance();
        int mon = cal.get(Calendar.MONTH) + 1;
        try {
            //本期计划更新时间
            Integer monPlan = Integer.valueOf(planTime.split("-")[0]);
            Integer dayPlan = Integer.valueOf(planTime.split("-")[1]);
            switch (updateCycle) {
                case "年度更新":
                    Date yDate = null;
                    yDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                            .parse(new SimpleDateFormat("yyyy")
                                    .format(new Date()) + "-" + String.format("%02d", monPlan) + "-" + String.format("%02d", dayPlan)
                                    + " 23:59:59");
                    return updateTime.after(yDate);
                case "半年更新":
                    //当前半年初
                    if (mon >= 6) {
                        mon = 6;
                    } else {
                        mon = 1;
                    }
                    Date hyDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                            .parse(new SimpleDateFormat("yyyy")
                                    .format(new Date()) + "-" + String.format("%02d", mon + monPlan) + "-" + String.format("%02d", dayPlan)
                                    + " 23:59:59");
                    return updateTime.after(hyDate);
                case "季度更新":
                    //当前季度初
                    if (mon <= 3) {
                        mon = 1;
                    } else if (mon <= 6) {
                        mon = 3;
                    } else if (mon <= 9) {
                        mon = 6;
                    } else {
                        mon = 9;
                    }
                    Date sDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                            .parse(new SimpleDateFormat("yyyy")
                                    .format(new Date()) + "-" + String.format("%02d", mon + monPlan) + "-" + String.format("%02d", dayPlan)
                                    + " 23:59:59");
                    return updateTime.after(sDate);
                default:
                    return false;
            }
        } catch (ParseException e) {
            log.error("checkOverDue error :{}", e.getMessage());
            return false;
        }
    }
}






















