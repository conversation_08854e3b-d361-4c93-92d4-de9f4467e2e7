package com.onecity.os.management.synchronous.controller;

import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.management.synchronous.dto.CityToProv;
import com.onecity.os.management.synchronous.dto.TjAndIndicator;
import com.onecity.os.management.synchronous.dto.param.SynchronizeDataParam;
import com.onecity.os.management.synchronous.service.CityToProvService;
import com.onecity.os.management.utils.StringUtil;
import com.onecity.os.management.zhibiao.feign.BaseResult;
import com.onecity.os.management.zhibiao.model.dto.IndicatorAuditNotesRemarks;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Api(tags = "指标同步")
@Slf4j
@RestController
@RequestMapping("/cityToProv")
@Transactional
public class CityToProvController {

    @Resource
    private CityToProvService cityToProvService;

    /**
     * 省舱获取地市中已经同步的一级指标
     */
    @ApiOperation(value = "省舱获取地市中已经同步的一级指标")
//    @AutoLog(value = "指标管理-查询指标树")
    @GetMapping("/getAllCityFirstLevelList")
    public BaseResult getAllCityFirstLevelList(@RequestParam(name = "source") String source){
        List<String> cityToProvList = new ArrayList<>();
        cityToProvList = cityToProvService.getAllCityFirstLevelList(source);
        return BaseResult.ok(cityToProvList);
    }


    @ApiOperation(value = "同步市舱指标到省舱")
    @PostMapping("/synchronizeData")
    public BaseResult synchronizeData(@RequestBody SynchronizeDataParam synchronizeDataParam) {
        if(StringUtils.isEmpty(synchronizeDataParam.getSource())){
            return BaseResult.fail("省舱的版块不能为空！");
        }
        return cityToProvService.synchronizeData(synchronizeDataParam);
}
}
