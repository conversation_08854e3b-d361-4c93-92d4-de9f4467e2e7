package com.onecity.os.management.synchronous.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.enums.ResultInfoEnum;
import com.onecity.os.common.core.exception.CockpitBusinessException;
import com.onecity.os.common.core.utils.poi.ExcelUtil;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.management.configmanage.entity.SourceManage;
import com.onecity.os.management.configmanage.service.SourceManageService;
import com.onecity.os.management.synchronous.dto.IndicatorAndData;
import com.onecity.os.management.synchronous.dto.IndicatorTreeSyncDto;
import com.onecity.os.management.synchronous.dto.SourceManageAndIndicatorTree;
import com.onecity.os.management.synchronous.dto.TjAndIndicator;
import com.onecity.os.management.synchronous.service.IndicatorSyncService;
import com.onecity.os.management.utils.ExcelUtilSelf;
import com.onecity.os.management.zhibiao.entity.*;
import com.onecity.os.management.zhibiao.mapper.GeneralIndicatorMapper;
import com.onecity.os.management.zhibiao.model.dto.*;
import com.onecity.os.management.zhibiao.model.vo.*;
import com.onecity.os.management.zhibiao.service.DataConfigService;
import com.onecity.os.management.zhibiao.service.IndicatorService;
import com.onecity.os.management.zhibiao.service.IndicatorServiceAsync;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


/**
 * 指标管理和指标数据控制层
 */
@Api(tags = "指标")
@Slf4j
@RestController
@RequestMapping("/sync/indicator")
@Transactional
public class IndicatorSyncController extends BaseController {

    @Autowired
    private IndicatorService indicatorService;

    @Autowired
    private SourceManageService sourceManageService;

    @Resource
    private IndicatorSyncService indicatorSyncService;

    @Value("${city.url1}")
    private String baseUrl1;

    @Value("${city.url2}")
    private String baseUrl2;

    @Resource
    private DataConfigService dataConfigService;
    @Resource
    private GeneralIndicatorMapper generalIndicatorMapper;


    /**
     * 1 指标管理-指标树   获取市舱厅局指标树--市舱
     *
     * @RequestParam(name = "tabid") String tabId,
     */
    @ApiOperation(value = "指标同步-获取厅局及其指标树")
//    @AutoLog(value = "指标管理-查询指标树")
    @GetMapping("/getTargetTreeList")
    public BaseResult<?> getTargetTreeList() {
        List<SourceManageAndIndicatorTree> treeVoList = new ArrayList<>();
        treeVoList = indicatorSyncService.getTargetTreeList();
        if (CollectionUtils.isEmpty(treeVoList)) {
            return BaseResult.fail("数据不存在！");
        }
        return BaseResult.ok(treeVoList);
    }

    @ApiOperation(value = "指标同步-获取市舱厅局及其指标树")
//    @AutoLog(value = "指标管理-查询指标树")
    //省舱接口
    @GetMapping("/getTargetTreeListProv")
    public BaseResult<?> getTargetTreeListProv() {
        List<SourceManageAndIndicatorTree> treeVoList = new ArrayList<>();
        String result = HttpRequest.get(baseUrl1).header("Content-Type", "application/json").timeout(20000).execute().body();
        log.info("调用市舱接口获取指标数据返回结果：" + result);
        com.alibaba.fastjson.JSONObject jsonObjects = JSON.parseObject(result);
        if(jsonObjects.getIntValue("code") == 200){
            String listJSONString = jsonObjects.getJSONArray("data").toJSONString();
            treeVoList = JSONArray.parseArray(listJSONString, SourceManageAndIndicatorTree.class);
        }else {
            return BaseResult.fail("获取市舱指标数据失败！");
        }
        return BaseResult.ok(treeVoList);
    }


    /**
     * 2 指标管理-指标列表查询
     *
     * @param tabId
     * @return
     */
    @ApiOperation(value = "指标管理-根据tabid,tj,查询指标列表")
//    @AutoLog(value = "指标管理-指标列表查询")
    @GetMapping("/getTargetList")
    public BaseResult<?> getTargetList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   @RequestParam(name = "tabid", required = true) String tabId,
                                   @RequestParam(name = "tj", required = true) String tj) {
        startPage();
//        PageInfo<IndicatorDto> pageInfo = new PageInfo<>();
        List<IndicatorDto> pageInfo = indicatorService.getTargetList(tabId, tj);
        if (ObjectUtil.isNotNull(pageInfo) && 0 != pageInfo.size()) {
            return BaseResult.ok(getDataTable(pageInfo));
        }
        if (null == pageInfo) {
            return BaseResult.fail("不存在此tab！");
        }
        return BaseResult.ok();
    }

    /**
     * 获取市舱板块需要同步的指标及其数据
     */
    @ApiOperation(value = "指标同步-获取市舱需要同步的指标及其数据")
    @PostMapping("/getIndicatorAndDataList")
    public BaseResult getIndicatorAndDataList(@RequestBody List<TjAndIndicator> tjAndIndicatorList){
        List<IndicatorAndData> indicatorAndDataList = new ArrayList<>();
        if(CollectionUtils.isEmpty(tjAndIndicatorList)){
            return BaseResult.ok("暂无需要同步的指标");
        }
        indicatorAndDataList = indicatorSyncService.getIndicatorAndData(tjAndIndicatorList);
        return BaseResult.ok(indicatorAndDataList);
    }

    /**
     * 指标数据管理-通过id获取数据配置详细信息
     * @param id
     */
    @ApiOperation(value ="指标数据管理-通过id获取数据配置详细信息")
    @GetMapping("/getDataConfigById")
    public BaseResult<?> getDataConfigById(@RequestParam String id){
        return BaseResult.ok(dataConfigService.getDataConfigById(id));
    }

    /**
     * 1 指标数据管理-查询指标数据列表-省舱
     *
     * @param indicatorId
     * @return
     */
    @ApiOperation(value = "指标数据管理-查询指标数据列表")
//    @AutoLog(value = "指标数据管理-查询指标数据列表")
    @GetMapping("/getIndicatorDetailListProv")
    public BaseResult<?> getTargetDetailListProv(@RequestParam(name = "indicatorId", required = true) String indicatorId,
                                             @RequestParam(name = "tj", required = true) String tj,
                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        IndicatorDataDto dto = new IndicatorDataDto();
        String dataUrl = baseUrl2+"?indicator="+indicatorId+"&tj="+tj+"&pageNo="+pageNo+"&pageSize="+pageSize;

        String result = HttpRequest.get(dataUrl).form(indicatorId,indicatorId).header("Content-Type", "application/json").timeout(20000).execute().body();
        log.info("调用市舱接口获取指标数据返回结果：" + result);
        com.alibaba.fastjson.JSONObject jsonObjects = JSON.parseObject(result);
        if(jsonObjects.getIntValue("code") == 200){
            String listJSONString = jsonObjects.getJSONArray("data").toJSONString();
            dto = JSON.parseObject(listJSONString, IndicatorDataDto.class);
        }else {
            return BaseResult.fail("获取市舱指标数据失败！");
        }
        return BaseResult.ok(dto);
    }

    /**
     * 1 指标数据管理-查询指标数据列表
     *
     * @param indicatorId
     * @return
     */
    @ApiOperation(value = "指标数据管理-查询指标数据列表")
//    @AutoLog(value = "指标数据管理-查询指标数据列表")
    @GetMapping("/getIndicatorDetailList")
    public BaseResult<?> getTargetDetailList(@RequestParam(name = "indicatorId", required = true) String indicatorId,
                                             @RequestParam(name = "tj", required = true) String tj,
                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        if (org.springframework.util.StringUtils.isEmpty(indicatorId)) {
            return BaseResult.fail("查询id不能为空");
        }
        IndicatorDataDto dto = new IndicatorDataDto();
        // 查询指标信息
        GeneralIndicatorTianbao generalIndicator = new GeneralIndicatorTianbao();
        generalIndicator = indicatorService.getIndicatorById(indicatorId, tj);
        if (ObjectUtils.isEmpty(generalIndicator)) {
            return BaseResult.ok(null,"分组下无数据！");
        }
        if (1 == generalIndicator.getIndicatorType()) {
            return BaseResult.ok(null,"分组下无数据！");
        }
        dto.setTreeId(generalIndicator.getId());
        dto.setSource(generalIndicator.getSourceName());
        dto.setUpdateDate(generalIndicator.getUpdateDate());
        dto.setType(generalIndicator.getIndicatorExhibitType());
        dto.setUpdateCycle(generalIndicator.getUpdateCycle());
        startPage();
        PageInfo<IndicatorDataReBean> pageInfo = new PageInfo<>();
        pageInfo = indicatorService.getTargetDetailListCity(indicatorId, tj, pageNo, pageSize);
        if (ObjectUtil.isNotNull(pageInfo) && 0 != pageInfo.getSize()) {
            dto.setData(pageInfo.getList());
            dto.setTotal(pageInfo.getTotal());
            dto.setSize(pageInfo.getSize());
            dto.setCurrent(pageInfo.getPageNum());
            dto.setPages(pageInfo.getPages());
            return BaseResult.ok(dto);
        }
        if (null == pageInfo) {
            return BaseResult.ok(null,"分组下无数据！");
        }
        return BaseResult.ok(dto);
    }


    /**
     * 指标数据审核-根据用户id,厅局简称,查找是否有审核的指标
     *
     * @param userId
     * @param sourceId
     * @return 0-无审核;1-有审核
     * @throws Exception
     */
    @ApiOperation(value = "指标数据审核-根据用户id,厅局简称,查找是否有审核的指标")
    @GetMapping("/getAuditByUserId")
    public BaseResult getAuditByUserId(@RequestParam(name = "userId") String userId, @RequestParam(name = "sourceId") String sourceId) throws Exception {
        IndicatorAuditNotesRemarks result = indicatorService.getAuditAndRemarksByUserId(userId, sourceId);
        return BaseResult.ok(result);
    }
}


























