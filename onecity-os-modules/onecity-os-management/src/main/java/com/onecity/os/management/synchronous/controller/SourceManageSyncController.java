package com.onecity.os.management.synchronous.controller;

import com.fasterxml.jackson.databind.ser.Serializers;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.enums.ResultInfoEnum;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.management.configmanage.check.SourceInsert;
import com.onecity.os.management.configmanage.check.SourceUpdate;
import com.onecity.os.management.configmanage.entity.SourceManage;
import com.onecity.os.management.configmanage.entity.vo.SourceManageReqVO;
import com.onecity.os.management.configmanage.entity.vo.SourceManageResVO;
import com.onecity.os.management.configmanage.entity.vo.SourceManageTypeVO;
import com.onecity.os.management.configmanage.enums.TypeEnums;
import com.onecity.os.management.configmanage.service.SourceManageService;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 配置管理-板块管理
 *
 * <AUTHOR>
 * @since 2020-05-21 10:11:56
 */
@Api(tags = "配置管理-板块管理")
@RestController
@RequestMapping("/sync/sourceManage")
public class SourceManageSyncController extends BaseController {

    @Autowired
    private SourceManageService sourceManageService;


    private String checkParams(SourceManageReqVO requestVO) {

        String sourceSimpleName = requestVO.getSourceSimpleName();
        //存在重名则往上加数字如wjw1,wjw2....
        int count = sourceManageService.countBySourceSimpleName(sourceSimpleName);
        if (count == 0) {
            return sourceSimpleName;
        }
        String result = sourceSimpleName + count;
        return result;
    }

    /**
     * 获取厅局列表--分页
     *
     * @return
     */
    @ApiOperation(value = "版块管理-获取厅局列表")
    @ResponseBody
    @GetMapping("/getList")
    public BaseResult getList(@RequestParam(name = "type", required = false) String type,
                                 @RequestParam(name = "sourceName", required = false) String sourceName,
                                 @RequestParam(name = "isStart", required = false) Integer isStart,
                                 @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
//        startPage();
        List<SourceManageResVO> sourceManageResVOS = sourceManageService.listSource(type, sourceName,isStart);
//        return getDataTable(sourceManageResVOS);
        return BaseResult.ok(sourceManageResVOS);
    }

    /**
     * 获取指标板块列表
     *
     * @return
     */
    @ApiOperation(value = "版块管理-获取指标板块列表")
    @ResponseBody
    @GetMapping("/getAllIndicatorsSourceManageList")
    public BaseResult getAllIndicatorsSourceManageList(){
        List<SourceManage> sourceManageList = sourceManageService.getAllIndicatorsSourceManage();

        return BaseResult.ok(sourceManageList);
    }


    /**
     *
     * @return
     */
    @ApiOperation(value = "版块管理-获取板块分组列表")
    @ResponseBody
    @GetMapping("/getTypeList")
    public BaseResult getTypeList() {
        List<SourceManageTypeVO> result = sourceManageService.getTypeList(null);
        return BaseResult.ok(result);
    }
}