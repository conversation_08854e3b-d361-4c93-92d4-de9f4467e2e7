package com.onecity.os.management.synchronous.dto;

import lombok.Data;

import java.util.List;

/**
 * 返回指标数据,指标树形结构
 *
 */
@Data
public class IndicatorTreeSyncDto {


    //{ label: '一级 1', children: [{label: '二级 1-1',children: [{label: '三级 1-1-1'}]}
    /**
     * id
     */
    private String id;

    /**
     * 名称
     */
    private String label;

    /**
     * 父指标ID,如果为一级指标, 该字段为空
     **/
    private String parentId;

    /**
     * 指标类型，0：指标，1：tab类型
     **/
    private Integer indicatorType;

    /**
     * 图标
     */
    private String iconUrl;
    /**
     * 图标
     */
    private String sequence;

    /**
     * 分组类型 0指标 1网页
     */
    private Integer groupType;

    /**
     * 分组网页
     */
    private String groupUrl;

    /**
     * 数据来源id
     */
    private String sourceId;

    /**
     * 数据来源
     */
    private String sourceName;

    /**
     * 是否展示
     */
    private Integer isShow;

    /**
     * 是否展示筛选框
     */
    private Integer isScreen;

    /**
     * 是否展示筛选框 0-不展示1展示
     */
    private Integer isLegend;

    /**
     * 数据更新方式1-手动填报2数据对接
     */
    private Integer dataUpdateMode;

    /**
     * 数据配置id
     */
    private String dataConfigId;

    /**
     * 距离上一次审核后是否有更新
     */
    private Integer isUpdate;

    /**
     * 子集
     */
    private List<IndicatorTreeSyncDto> children;


}
