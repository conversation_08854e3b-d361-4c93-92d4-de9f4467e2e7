package com.onecity.os.management.synchronous.service.impl;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.utils.BeanHelper;
import com.onecity.os.management.feign.SuperviseFeignService;
import com.onecity.os.management.statisticanalysis.entity.IndicatorUpdateRecord;
import com.onecity.os.management.synchronous.dto.CityToProv;
import com.onecity.os.management.synchronous.dto.IndicatorAndData;
import com.onecity.os.management.synchronous.dto.TjAndIndicator;
import com.onecity.os.management.synchronous.dto.param.SynchronizeDataParam;
import com.onecity.os.management.synchronous.mapper.CityToProvMapper;
import com.onecity.os.management.synchronous.service.CityToProvService;
import com.onecity.os.management.utils.DateUtil;
import com.onecity.os.management.utils.DateUtils;
import com.onecity.os.management.zhibiao.entity.GeneralIndicatorData;
import com.onecity.os.management.zhibiao.feign.BaseResult;
import com.onecity.os.management.zhibiao.mapper.IndicatorMapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class CityToProvServiceImpl implements CityToProvService {

    @Value("${city.url}")
    private String baseUrl;
    @Resource
    private CityToProvMapper cityToProvMapper;
    @Resource
    private IndicatorMapper indicatorMapper;
    @Resource
    private SuperviseFeignService superviseFeignService;


    @Override
    public List<String> getAllCityFirstLevelList(String source) {
        return cityToProvMapper.getAllCityFirstLevelList(source);
    }

    @Override
    @Transactional
    public BaseResult synchronizeData(SynchronizeDataParam synchronizeDataParam) {
        //http请求市舱的指标数据
        List<IndicatorAndData> indicatorAndDataList = new ArrayList<>();
        List<TjAndIndicator> tjAndIndicatorList = synchronizeDataParam.getTjAndIndicatorList();
        log.info("调用市舱接口获取指标数据：" + JSONObject.toJSONString(baseUrl));
        String result = HttpRequest.post(baseUrl).body(JSON.toJSONString(tjAndIndicatorList)).header("Content-Type", "application/json").timeout(20000).execute().body();
        log.info("调用市舱接口获取指标数据返回结果：" + result);
        com.alibaba.fastjson.JSONObject jsonObjects = JSON.parseObject(result);
        if(jsonObjects.getIntValue("code") == 200){
            String listJSONString = jsonObjects.getJSONArray("data").toJSONString();
            indicatorAndDataList = JSONArray.parseArray(listJSONString, IndicatorAndData.class);
        }else {
            return BaseResult.fail("获取市舱指标数据失败！");
        }
        //获取之前该板块下已同步的记录
        List<CityToProv> cityToProvs = cityToProvMapper.getAllLevelListBySource(synchronizeDataParam.getSource());
        //先将之前的记录置为已删除
        cityToProvMapper.deleteRecordBySource(synchronizeDataParam.getSource());
        //筛选出哪些为之前同步过的哪些是新同步的
        //先获取之前同步过的所有市舱指标id
        List<String> synchronizedIdList = cityToProvs.stream().map(CityToProv::getIndicatorIdCity).collect(Collectors.toList());
        //过滤出已同步过的指标
        log.info("indicatorAndDataList-----1"+indicatorAndDataList.stream().map(IndicatorAndData::getId).collect(Collectors.toList()).toString());
        List<IndicatorAndData> synchronizedList = new ArrayList<>();
        synchronizedList = indicatorAndDataList.stream().filter((IndicatorAndData s) -> synchronizedIdList.contains(s.getId())).collect(Collectors.toList());
        if(synchronizedList.size() > 0){
            this.updateIndicatorAndData(synchronizedList,synchronizeDataParam.getSource(),cityToProvs);
        }
        //新同步的指标
        List<IndicatorAndData> newSynchronizedList = new ArrayList<>();
        log.info("indicatorAndDataList-----2"+indicatorAndDataList.stream().map(IndicatorAndData::getId).collect(Collectors.toList()).toString());
        newSynchronizedList = indicatorAndDataList.stream().filter((IndicatorAndData s) -> !synchronizedIdList.contains(s.getId())).collect(Collectors.toList());
        log.info("newSynchronizedList-----2"+newSynchronizedList.stream().map(IndicatorAndData::getId).collect(Collectors.toList()).toString());
        if(newSynchronizedList.size() > 0) {
            this.insertIndicatorAndData(newSynchronizedList,synchronizeDataParam.getSource());
        }
        return BaseResult.ok("同步成功！");
    }

    private void updateIndicatorAndData(List<IndicatorAndData> synchronizedList,String source,List<CityToProv> cityToProvs) {
        String userName = SecurityUtils.getUsername();
        //将之前的同步记录置为已选择
        List<String> synchronizedIdList = synchronizedList.stream().map(IndicatorAndData::getId).collect(Collectors.toList());
        cityToProvMapper.updateRecordBySourceAndId(source, synchronizedIdList);
        //删除需要删除的指标
        //找到同步的一级指标
        List<IndicatorAndData> indicatorFirstList = synchronizedList.stream().filter((IndicatorAndData s) -> "0".equals(s.getParentId())).collect(Collectors.toList());
        List<String> firstIdList = indicatorFirstList.stream().map(IndicatorAndData::getId).collect(Collectors.toList());
        //找到这些一级指标之前同步过的记录
        List<CityToProv> cityToProvsBefor = cityToProvs.stream().filter((CityToProv s) -> firstIdList.contains(s.getFirstLevelIndicatorIdCity())).collect(Collectors.toList());
        //根据本次同步的指标id和之前的同步指标记录找出本次没有跟新的指标
        List<CityToProv> cityToProvsNotHave = cityToProvsBefor.stream().filter((CityToProv s) -> !synchronizedIdList.contains(s.getIndicatorIdCity())).collect(Collectors.toList());
        //取出指标id
        List<String> deleteList = cityToProvsNotHave.stream().map(CityToProv::getIndicatorIdProv).collect(Collectors.toList());
        if(deleteList.size() > 0){
            indicatorMapper.deleteTargets(deleteList, userName, source);
        }
        //找出真正修改过的指标
        List<IndicatorAndData> updateList = new ArrayList<>();
        for (IndicatorAndData indicatorAndData : synchronizedList) {
            //找出之前同步记录对比指标的更新时间，若时间改变则认为该指标已修改
            CityToProv cityToProv = cityToProvs.stream().filter(record -> indicatorAndData.getId().equals(record.getIndicatorIdCity())).findAny().orElse(null);
            if (null != cityToProv && !indicatorAndData.getUpdateTime().equals(cityToProv.getUpdateTimeCity())) {
                IndicatorAndData indicatorAndDataUp = new IndicatorAndData();
                indicatorAndDataUp = BeanHelper.copyProperties(indicatorAndData,IndicatorAndData.class);
                updateList.add(indicatorAndDataUp);
                //更新同步记录中的指标更新时间
                cityToProvMapper.updateRecordTimeBySourceAndId(source,cityToProv.getIndicatorIdCity(),indicatorAndData.getUpdateTime());
            }
        }
            //修改指标
            //新增指标数据表
            List<GeneralIndicatorData> indicatorDataList = new ArrayList<>();
            for (IndicatorAndData indicatorAndData : updateList) {
                indicatorAndData.setUpdater(userName);
                indicatorAndData.setUpdateTime(new Date());
                //处理成省舱指标id
                indicatorAndData.setId(indicatorAndData.getId() + "_" + source);
                if (!"0".equals(indicatorAndData.getParentId())) {
                    indicatorAndData.setParentId(indicatorAndData.getParentId() + "_" + source);
                }
                //指标数据
                if (null != indicatorAndData.getIndicatorDataList() && indicatorAndData.getIndicatorDataList().size() > 0) {
                    indicatorDataList.addAll(indicatorAndData.getIndicatorDataList());
                }
            }
            //批量修改指标
            if(updateList.size() > 0){
                cityToProvMapper.updateIndicatorBatch(updateList);
            }
            //批量删除指标数据
            List<String> deleteIdList = updateList.stream().map(IndicatorAndData::getId).collect(Collectors.toList());
            if(deleteIdList.size() > 0){
                cityToProvMapper.deleteIndicatorDataBatchById(deleteIdList);
                superviseFeignService.cancelIndicatorHistory(deleteIdList);
                superviseFeignService.cancelRemindIndicatorHistory(deleteIdList);
            }
            //批量新增指标数据
            //修改省舱指标数据中指标id
            for (GeneralIndicatorData generalIndicatorData : indicatorDataList) {
                generalIndicatorData.setIndicatorId(generalIndicatorData.getIndicatorId() + "_" + source);
                generalIndicatorData.setCreater(userName);
                generalIndicatorData.setCreateTime(new Date());
                generalIndicatorData.setUpdater(userName);
                generalIndicatorData.setUpdateTime(new Date());
            }
            if(indicatorDataList.size() > 0){
                cityToProvMapper.insertIndicatorDataBatch(indicatorDataList);
            }
            //指标更新记录
            List<IndicatorUpdateRecord> indicatorUpdateRecords = new ArrayList<>();
            for (IndicatorAndData indicatorAndData : updateList) {
                IndicatorUpdateRecord record = new IndicatorUpdateRecord();
                record.setIndicatorId(indicatorAndData.getId());
                record.setCreateTime(new Date());
                record.setIndicatorName(indicatorAndData.getIndicatorName());
                record.setDataFlag(0);
                indicatorUpdateRecords.add(record);
            }
            if(indicatorUpdateRecords.size() > 0){
                cityToProvMapper.insertRecordBatch(indicatorUpdateRecords);
            }
        }
    private void insertIndicatorAndData(List<IndicatorAndData> newSynchronizedList,String source){
        String userName = SecurityUtils.getUsername();
        //新增同步记录表
        List<CityToProv> newCityToProvs = new ArrayList<>();
        //新增指标更新记录
        List<IndicatorUpdateRecord> indicatorUpdateRecords = new ArrayList<>();
        //新增指标数据表
        List<GeneralIndicatorData> indicatorDataList = new ArrayList<>();
        for(IndicatorAndData indicatorAndData : newSynchronizedList){
            //同步记录
            CityToProv cityToProv = new CityToProv();
            cityToProv.setCreater(userName);
            cityToProv.setCreateTime(new Date());
            cityToProv.setIndicatorIdCity(indicatorAndData.getId());
            cityToProv.setIndicatorIdProv(indicatorAndData.getId()+"_"+source);
            cityToProv.setFirstLevelIndicatorIdCity(indicatorAndData.getFirstLevelIndicatorId());
            cityToProv.setFirstLevelIndicatorIdProv(indicatorAndData.getFirstLevelIndicatorId()+"_"+source);
            cityToProv.setSourceSimpleNameProv(source);
            cityToProv.setSourceSimpleNameCity(indicatorAndData.getSourceId());
            cityToProv.setIsDelete(0);
            cityToProv.setUpdater(userName);
            cityToProv.setUpdateTime(new Date());
            cityToProv.setUpdateTimeCity(indicatorAndData.getUpdateTime());
            newCityToProvs.add(cityToProv);
            //指标更新记录
            IndicatorUpdateRecord record = new IndicatorUpdateRecord();
            record.setIndicatorId(indicatorAndData.getId()+"_"+source);
            record.setCreateTime(new Date());
            record.setIndicatorName(indicatorAndData.getIndicatorName());
            record.setDataFlag(0);
            indicatorUpdateRecords.add(record);
            //指标数据
            if(null != indicatorAndData.getIndicatorDataList() && indicatorAndData.getIndicatorDataList().size() > 0){
                indicatorDataList.addAll(indicatorAndData.getIndicatorDataList());
            }
            //指标
            indicatorAndData.setCreater(userName);
            indicatorAndData.setCreateTime(new Date());
            indicatorAndData.setUpdater(userName);
            indicatorAndData.setUpdateTime(new Date());
            indicatorAndData.setSourceId(source);
            //修改省舱指标id
            indicatorAndData.setId(indicatorAndData.getId()+"_"+source);
            if(!indicatorAndData.getParentId().equals("0")){
                indicatorAndData.setParentId(indicatorAndData.getParentId()+"_"+source);
            }
        }
        //修改省舱指标数据中指标id
        for(GeneralIndicatorData generalIndicatorData : indicatorDataList){
            generalIndicatorData.setIndicatorId(generalIndicatorData.getIndicatorId()+"_"+source);
            generalIndicatorData.setCreater(userName);
            generalIndicatorData.setCreateTime(new Date());
            generalIndicatorData.setUpdater(userName);
            generalIndicatorData.setUpdateTime(new Date());
        }
        //批量新增上面那些数据
        cityToProvMapper.insertBatch(newCityToProvs);
        cityToProvMapper.insertRecordBatch(indicatorUpdateRecords);
        cityToProvMapper.insertIndicatorBatch(newSynchronizedList);
        if(indicatorDataList.size() > 0){
            cityToProvMapper.insertIndicatorDataBatch(indicatorDataList);
        }
    }

}
