package com.onecity.os.management.synchronous.service.impl;

import com.onecity.os.common.core.utils.BeanHelper;
import com.onecity.os.management.configmanage.entity.SourceManage;
import com.onecity.os.management.configmanage.mapper.SourceManageMapper;
import com.onecity.os.management.synchronous.dto.IndicatorAndData;
import com.onecity.os.management.synchronous.dto.IndicatorTreeSyncDto;
import com.onecity.os.management.synchronous.dto.SourceManageAndIndicatorTree;
import com.onecity.os.management.synchronous.dto.TjAndIndicator;
import com.onecity.os.management.synchronous.service.IndicatorSyncService;
import com.onecity.os.management.zhibiao.entity.GeneralIndicator;
import com.onecity.os.management.zhibiao.entity.GeneralIndicatorData;
import com.onecity.os.management.zhibiao.entity.GeneralIndicatorTianbao;
import com.onecity.os.management.zhibiao.mapper.GeneralIndicatorDataMapper;
import com.onecity.os.management.zhibiao.mapper.GeneralIndicatorMapper;
import com.onecity.os.management.zhibiao.mapper.IndicatorMapper;
import com.onecity.os.management.zhibiao.model.dto.IndicatorTreeDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class IndicatorSyncServiceImpl implements IndicatorSyncService {

    @Resource
    private SourceManageMapper sourceManageMapper;

    @Resource
    private IndicatorMapper indicatorMapper;

    @Resource
    private GeneralIndicatorMapper generalIndicatorMapper;

    @Resource
    private GeneralIndicatorDataMapper generalIndicatorDataMapper;

    @Override
    public List<SourceManageAndIndicatorTree> getTargetTreeList() {
        String tabId = "0";
        GeneralIndicatorTianbao generalIndicator = new GeneralIndicatorTianbao();
        generalIndicator.setId("0");
        List<SourceManageAndIndicatorTree> sourceManageAndIndicatorTrees = new ArrayList<>();
        //获取所有的指标板块
        List<SourceManage> sourceManageList = sourceManageMapper.getAllSourceManage1();
//        SourceManage sourceManage = sourceManageMapper.getSourceInfoBySourceSimpleName(tj);
        for(SourceManage sourceManage: sourceManageList) {

            SourceManageAndIndicatorTree sourceManageAndIndicatorTree = new SourceManageAndIndicatorTree();
            sourceManageAndIndicatorTree.setSourceManage(sourceManage);
            String tj = sourceManage.getSourceSimpleName();
            List<String> indicatorList = new ArrayList<>();
            indicatorList.add(generalIndicator.getId());
            // 查询 tab 下的 指标list数据
            List<GeneralIndicator> dtoList = new ArrayList<>();
            dtoList = generalIndicatorMapper.getIndicatorsByParentIdsAndTJ(indicatorList,sourceManage.getSourceSimpleName());
            List<IndicatorTreeSyncDto> treeDataVoList = new ArrayList<IndicatorTreeSyncDto>();
            treeDataVoList = getTreeList(dtoList, sourceManage);
            sourceManageAndIndicatorTree.setIndicatorTreeSyncDtoList(treeDataVoList);
            sourceManageAndIndicatorTrees.add(sourceManageAndIndicatorTree);
        }
        return sourceManageAndIndicatorTrees;

    }

    /**
     * 指标管理-递归查询下级指标列表
     *
     * @param dtoList
     * @param sourceManage
     * @return
     */
    public List<IndicatorTreeSyncDto> getTreeList(List<GeneralIndicator> dtoList, SourceManage sourceManage) {
        List<IndicatorTreeSyncDto> treeDataDtoList = new ArrayList<>();
        for (GeneralIndicator entity : dtoList) {
            IndicatorTreeSyncDto treeDto = new IndicatorTreeSyncDto();
            treeDto.setId(entity.getId());
            treeDto.setLabel(entity.getIndicatorName());
            treeDto.setParentId(entity.getParentId());
            treeDto.setIconUrl(entity.getIconUrl());
            treeDto.setIndicatorType(entity.getIndicatorType());
            treeDto.setSequence(entity.getSequence().toString());
            treeDto.setGroupType(entity.getGroupType());
            treeDto.setGroupUrl(entity.getGroupUrl());
            treeDto.setIsShow(entity.getIsShow());
            treeDto.setIsScreen(entity.getIsScreen());
            treeDto.setIsLegend(entity.getIsLegend());
            treeDto.setSourceId(entity.getSourceId());
            treeDto.setSourceName(entity.getSourceName());
            treeDto.setChildren(new ArrayList<>());
            treeDto.setDataUpdateMode(entity.getDataUpdateMode());
            treeDto.setDataConfigId(entity.getDataConfigId());
            List<String> indicatorList = new ArrayList<>();
            indicatorList.add(entity.getId());
            //下级指标
            List<GeneralIndicator> erjiList = generalIndicatorMapper.getIndicatorsByParentIdsAndTJ(indicatorList,sourceManage.getSourceSimpleName());
            if (erjiList.size() > 0) {
                treeDto.getChildren().addAll(getTreeList(erjiList, sourceManage));
            }
            treeDataDtoList.add(treeDto);
        }
        return treeDataDtoList;
    }


    @Override
    public List<IndicatorAndData> getIndicatorAndData(List<TjAndIndicator> tjAndIndicatorsList) {
        List<IndicatorAndData> indicatorAndDataList = new ArrayList<>();
        for(TjAndIndicator tjAndIndicator:tjAndIndicatorsList){
            List<IndicatorAndData> indicatorAndDataList1 = new ArrayList<>();
            IndicatorAndData indicatorAndData = new IndicatorAndData();
            for(String indicatorId:tjAndIndicator.getIndicatorList()) {
                //通过指标id查询审核后的指标
                GeneralIndicator generalIndicator = generalIndicatorMapper.getIndicatorInfoById(indicatorId);
                indicatorAndData = BeanHelper.copyProperties(generalIndicator,IndicatorAndData.class);
                indicatorAndData.setFirstLevelIndicatorId(indicatorId);
                //通过指标id查询其指标数据
                if(generalIndicator.getIsDelete() == 0){
                    List<GeneralIndicatorData> generalIndicatorDataList = generalIndicatorDataMapper.getGeneralIndicatorDataByIndicatorId(generalIndicator.getId());
                    indicatorAndData.setIndicatorDataList(generalIndicatorDataList);
                }
                indicatorAndDataList.add(indicatorAndData);
                List<String> indicatorIdList = new ArrayList<>();
                indicatorIdList.add(generalIndicator.getId());
                //递归查询其下级指标
                List<GeneralIndicator> generalIndicatorList = generalIndicatorMapper.getAllIndicatorsByParentIdsAndTJ(indicatorIdList,tjAndIndicator.getTj());
                this.getIndicatorTree(generalIndicatorList,indicatorAndDataList,indicatorId,tjAndIndicator.getTj());
            }
        }
        return indicatorAndDataList;
    }

    public void getIndicatorTree(List<GeneralIndicator> generalIndicators,List<IndicatorAndData> indicatorAndDataList,String indicatorId,String tj){
        if(null != generalIndicators && generalIndicators.size() > 0){
            List<String> indicatorList = new ArrayList<>();
            for(GeneralIndicator generalIndicator:generalIndicators){
                IndicatorAndData indicatorAndData = BeanHelper.copyProperties(generalIndicator,IndicatorAndData.class);
                //查询指标数据
                if(generalIndicator.getIsDelete() == 0){
                    List<GeneralIndicatorData> generalIndicatorDataList = generalIndicatorDataMapper.getGeneralIndicatorDataByIndicatorId(generalIndicator.getId());
                    indicatorAndData.setIndicatorDataList(generalIndicatorDataList);
                }
                indicatorAndData.setFirstLevelIndicatorId(indicatorId);
                //将指标及数据加到结果集中
                indicatorAndDataList.add(indicatorAndData);
                indicatorList.add(generalIndicator.getId());
            }
            List<GeneralIndicator> nextLevelIndicadtorList = generalIndicatorMapper.getAllIndicatorsByParentIdsAndTJ(indicatorList,tj);
            //递归查询下级指标及数据
            this.getIndicatorTree(nextLevelIndicadtorList,indicatorAndDataList,indicatorId,tj);
        }
    }
}
