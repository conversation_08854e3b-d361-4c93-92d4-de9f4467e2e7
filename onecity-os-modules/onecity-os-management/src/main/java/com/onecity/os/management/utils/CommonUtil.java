package com.onecity.os.management.utils;

import com.onecity.os.management.constant.Constant;

/**
 * <AUTHOR>
 * @Date 2020/2/27 12:25
 */
public class CommonUtil {

    /**
     * 字符串是否全是数字
     * @param str
     * @return
     */
    public static boolean isNumeric(String str){
        String regex = "-?[0-9]+.*[0-9]*";
        Boolean strResult = str.matches(regex);
        return strResult;
    }

    public static Double strToDou(String str){
        boolean numeric = isNumeric(str);
        Double result = null;
        if(numeric){
            result = Double.parseDouble(str);
        }
        return result;
    }

    public static Integer getLastChar(String source){
        return Integer.valueOf(source.substring(source.length()-1));
    }

    public static String getParentId(String source){
        return source.substring(0,source.lastIndexOf("_"));

    }

    /**
     * 字符串是否全是小写字母
     * @param str
     * @return
     */
    public static boolean isLowercase(String str){
        String regex = "^[a-z]+$";
        Boolean strResult = str.matches(regex);
        return strResult;
    }

    /**
     * 字符串长度校验
     * @param str
     * @return
     */
    public static boolean checkLength(String str){
        int len = Constant.SOURCE_SIMPLE_NAME_LENGTH;
        int length = str.length();
        return length<=len;
    }

}
