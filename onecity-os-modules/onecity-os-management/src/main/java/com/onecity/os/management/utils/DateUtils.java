package com.onecity.os.management.utils;

import com.onecity.os.management.constant.CycleConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import java.lang.management.ManagementFactory;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYYMM = "yyyyMM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期路径 即年/月/日 如20180808 完整
     */
    public static String dateTimeIntact() {
        Date now = new Date();
        return DateFormatUtils.format(now, YYYY_MM_DD_HH_MM_SS);
    }


    /**
     *  时间转化
     * @param date
     * @return
     */
    public static String dateTimeChange(Date date) {
        return DateFormatUtils.format(date, YYYY_MM_DD_HH_MM_SS);
    }

    /**
     *  时间转化 YYYYMM
     * @param date
     * @return
     */
    public static String dateTimeChangeYYYYMM(Date date) {
        return DateFormatUtils.format(date, YYYYMM);
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 校验日期是否是符合指定格式的合法日期
     * <p>
     *    此方法是为了解决接口入参的日期格式校验，我们需要接口入参是日期是一个合法的而且是指定格式的日期
     * </p>
     *
     * @param date 日期
     * @param length 日期的长度,必须是date参数的长度，这样可以兼容多种格式的校验
     * @param format 日期的格式，需要与日期格式保持一致
     * @return
     */
    public static boolean isLegalDate(String date,int length,String format) {
        if (date == null || date.length() != length) {
            return false;
        }
        try {
            DateFormat formatter = new SimpleDateFormat(format);
            // 设置lenient为false. 否则SimpleDateFormat会比较宽松地验证日期，比如2018-02-29会被接受，并转换成2018-03-01
            //"2019022 "|"201902 2" 这两种也能被Date转化，所以最后需要用date.equals(formatter.format(date1));
            formatter.setLenient(false);
            Date date1 = formatter.parse(date);
            log.info("入参：" + date + ";转换后日期：" + formatter.format(date1));
            return date.equals(formatter.format(date1));
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            return false;
        }
    }

    public static void main(String[] args) {
        String date = "哈哈哈";
        // 调用isLegalDate方法的时候，length参数一定要是要校验日期的length
        System.out.println(isLegalDate(date,date.length(),"yyyy")); //false

        System.out.println(date.length()); //false

        System.out.println(!CycleConstants.HALF_ANNUAL_ONE.equals("2018") && !CycleConstants.HALF_ANNUAL_TWO.equals("2018")); //false
    }
}
