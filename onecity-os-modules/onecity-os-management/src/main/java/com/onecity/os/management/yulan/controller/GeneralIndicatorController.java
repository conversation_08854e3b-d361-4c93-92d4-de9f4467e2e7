package com.onecity.os.management.yulan.controller;


import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.management.yulan.check.IndicatorCommon;
import com.onecity.os.management.yulan.check.IndicatorOther;
import com.onecity.os.management.yulan.po.IndicatorYuLanData;
import com.onecity.os.management.yulan.service.GeneralIndicatorDataService;
import com.onecity.os.management.yulan.service.GeneralIndicatorService;
import com.onecity.os.management.yulan.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-02-27 15:51:52
 */
@Controller
@RequestMapping("/yulan")
@Slf4j
@Api(tags = "指标相关接口")
public class GeneralIndicatorController extends BaseController {

    @Resource
    private GeneralIndicatorService generalIndicatorService;

    @Resource
    private GeneralIndicatorDataService generalIndicatorDataService;


    /**
     * 根据sourceSimpleName获取ICON列表
     *
     */
    @ResponseBody
    @PostMapping("/getIconList")
	@ApiOperation(value = "根据厅局,获取ICON列表")
    public BaseResult<?> getIconList(@Validated(IndicatorCommon.class) @RequestBody IndicatorYuLanReqVO req) {
        List<IndicatorYuLanIconResVO> generalIndicatorIconResVOS = generalIndicatorService.listIcon(req);
        return BaseResult.ok(generalIndicatorIconResVOS);
    }

    /**
     * 根据sourceSimpleName指标列表首页
     *
     * @return
     */
    @ResponseBody
    @GetMapping("/getFirstIndicatorList")
    @ApiOperation(value = "根据厅局,获取ICON列表")
    public BaseResult getFirstIndicatorList(@RequestParam(name = "sourceSimpleName") String sourceSimpleName,
                                            @RequestParam(name = "first") String first,
                                            @RequestParam(name = "second") String second,
                                            @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        GeneralIndicatorVos firstGeneralIndicatorVOS = generalIndicatorService.getFirstIndicatorList(sourceSimpleName,first,second,pageNum,pageSize);
        return BaseResult.ok(firstGeneralIndicatorVOS);
    }

    /**
     * 根据ICON的id查询栏目id
     *
     * @return
     */
    @ResponseBody
    @PostMapping("/getTabList")
	@ApiOperation(value = "根据ICON的id查询栏目id")
    public BaseResult<?> getTabList(@Validated(IndicatorOther.class) @RequestBody IndicatorYuLanReqVO req) {
        List<IndicatorYuLanIconResVO> generalIndicatorIconResVOS = generalIndicatorService.listTab(req);
        return BaseResult.ok(generalIndicatorIconResVOS);
    }
    /**
     * 根据id查询其对应数据--增加历史数据
     * 返回前端数据增加选择list
     */
    @ResponseBody
    @GetMapping("/getListByReq")
    @ApiOperation(value = "指标查询--增加历史数据;返回前端数据增加选择list")
    public BaseResult getListByReq(@RequestParam(name = "req") String req) {
        IndicatorYuLanCoreVo generalIndicatorCoreVos = generalIndicatorService.listIndicatorDataById(req);
        log.info("-----------------getListByTabId  test for generalIndicatorCoreVos : {}", JSONObject.toJSONString(generalIndicatorCoreVos));
        return BaseResult.ok(generalIndicatorCoreVos);
    }
    /**
     * 指标查询--增加历史数据
     * 返回前端数据增加选择list
     */
    @ResponseBody
    @PostMapping("/getList")
	@ApiOperation(value = "指标查询--增加历史数据;返回前端数据增加选择list")
    public BaseResult<?> getList(@Validated(IndicatorOther.class) @RequestBody IndicatorYuLanReqVO req) {
        IndicatorYuLanResVo indicatorYuLanResVo = generalIndicatorService.listIndicator(req);
        return BaseResult.ok(indicatorYuLanResVo);
    }

    /**
     * 根据 tabid 查询其子指标 分页--增加历史数据
     * 返回前端数据增加选择list
     */
    @ResponseBody
    @PostMapping("/getListByTabId")
    @ApiOperation(value = "指标查询--增加历史数据;返回前端数据增加选择list")
    public BaseResult getListByTabId(@Validated(IndicatorOther.class) @RequestBody IndicatorYuLanReqVO req) {
        startPage(req.getPageNum(), req.getPageSize());
        PageInfo<IndicatorYuLanCoreVo> indicatorYuLanCoreVos = generalIndicatorService.listIndicatorByTabId(req);
        log.info("-----------------getListByTabId  test for indicatorYuLanCoreVos : {}", JSONObject.toJSONString(indicatorYuLanCoreVos));
        return BaseResult.ok(indicatorYuLanCoreVos);
    }

    /**
     * 指标查询--增加历史数据
     * 返回前端数据增加选择list
     */
    @ResponseBody
    @PostMapping("/getDataList")
    @ApiOperation(value = "指标数据查询--根据指标周期和时间进行指标数据查询")
    public BaseResult<?> getDataList(@Validated(IndicatorOther.class) @RequestBody GeneralIndicatorYearDataReqVO req) {
        List<IndicatorYuLanData> generalIndicatorDataList = generalIndicatorDataService.getDataList(req);
        return BaseResult.ok(generalIndicatorDataList);
    }

}