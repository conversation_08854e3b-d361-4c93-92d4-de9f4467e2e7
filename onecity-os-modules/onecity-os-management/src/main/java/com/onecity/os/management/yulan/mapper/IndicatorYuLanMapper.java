package com.onecity.os.management.yulan.mapper;

import com.onecity.os.management.yulan.po.IndicatorYuLan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 *
 * <AUTHOR>
 * @since 2020-03-02 15:26:33
 */
@Mapper
public interface IndicatorYuLanMapper{

    List<IndicatorYuLan> listIcon(@Param("record") IndicatorYuLan record);

    List<IndicatorYuLan> listTab(@Param("record") IndicatorYuLan record);

    List<IndicatorYuLan> listCoreIndicatorByParentId(@Param("record") IndicatorYuLan record);

    List<IndicatorYuLan> getIndicatorListByParentId(@Param("indicatorId") String indicatorId);

    IndicatorYuLan getInfoByIndicatorId(@Param("indicatorId") String indicatorId);

    IndicatorYuLan getIndicatorInfoByIndicatorId(@Param("indicatorId") String indicatorId);
}