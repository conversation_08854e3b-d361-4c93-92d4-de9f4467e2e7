package com.onecity.os.management.yulan.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2020-05-12 16:41:34
 */
@Data
public class IndicatorYuLanData {
    //主键自增
    private Long id;
    //指标Id
    private String indicatorId;
    //指标名称
    private String indicatorName;
    //排序
    private Integer sequence;
    //指标项目
    private String itemName;
    //指标项目值
    private String itemValue;

    private String itemValue1;

    private String itemValue2;

    private String itemValue3;

    private String itemValue4;

    private String itemValue5;

    private String itemValue6;

    private String itemUnit2nd;
    //单位
    private String itemUnit;
    //用来表示指标数据是增加还是减少，前端显示不同的颜色
    private String identify;
    //指标文字展示方式，0：平铺；1：加横线
    private Integer style;
    //是否折行0：否1：是
    private Integer fold;
    //数据年份
    private String year;
    //更新日期
    private String updateDate;
    //是否删除0:否1:是
    private Integer delete;
    //创建时间
    private LocalDateTime createTime;
    //创建人
    private String creater;
    //更新时间
    private LocalDateTime updateTime;
    //更新人
    private String updater;
    //是否当前展示0：是1：否
    private Integer currentFlag;

}