package com.onecity.os.management.yulan.service;

import com.onecity.os.management.yulan.po.IndicatorYuLanData;
import com.onecity.os.management.yulan.vo.GeneralIndicatorYearDataReqVO;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2020-03-02 15:28:17
 */
public interface GeneralIndicatorDataService {

    List<IndicatorYuLanData> listByIndicatorId(String sourceSimpleName, String indicatorId);

    List<IndicatorYuLanData> list(String sourceSimpleName, IndicatorYuLanData params);

    List<String> listShowDate(String sourceSimpleName,String indicatorId);



    //根据最大数据期年份以及指标id获取展示的不删除的非年度指标数据
    List<IndicatorYuLanData> listIndicatorDatas(IndicatorYuLanData params);

    //根据最大数据期年份以及指标id获取展示的不删除的年度指标数据
    List<IndicatorYuLanData> listIndicatorYearDatas(IndicatorYuLanData params);


    String getMaxUpdateDateYear(String indicatorId);

    List<IndicatorYuLanData> getDataList(GeneralIndicatorYearDataReqVO req);
}