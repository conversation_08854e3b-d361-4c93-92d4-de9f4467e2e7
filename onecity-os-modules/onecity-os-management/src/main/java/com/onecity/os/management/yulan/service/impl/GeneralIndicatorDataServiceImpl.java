package com.onecity.os.management.yulan.service.impl;

import com.onecity.os.management.constant.Constant;
import com.onecity.os.management.yulan.mapper.IndicatorYuLanDataMapper;
import com.onecity.os.management.yulan.po.IndicatorYuLanData;
import com.onecity.os.management.yulan.service.GeneralIndicatorDataService;
import com.onecity.os.management.yulan.vo.GeneralIndicatorYearDataReqVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2020-03-02 15:28:17
 */
@Service
public class GeneralIndicatorDataServiceImpl implements GeneralIndicatorDataService {
    @Autowired(required = false)
    private IndicatorYuLanDataMapper indicatorDataMapper;

    /**
     * 根据指标id查询指标数据
     *
     * @param indicatorId
     * @return
     */
    @Override
    public List<IndicatorYuLanData> listByIndicatorId(String sourceSimpleName, String indicatorId) {
        if (StringUtils.isBlank(indicatorId)) {
            return null;
        }
        IndicatorYuLanData params = new IndicatorYuLanData();
        params.setDelete(Constant.DELETE_NO);
        params.setIndicatorId(indicatorId);
        params.setCurrentFlag(Constant.CURRENT_DATA_YES);
        List<IndicatorYuLanData> list = indicatorDataMapper.list(params);
        return list;
    }

    @Override
    public List<IndicatorYuLanData> list(String sourceSimpleName, IndicatorYuLanData params) {
        params.setDelete(Constant.DELETE_NO);
        List<IndicatorYuLanData> list = indicatorDataMapper.list(params);
        return list;
    }

    /**
     * 根据指标id查询历史年份集合
     *
     * @param indicatorId
     * @return
     */
    @Override
    public List<String> listShowDate(String sourceSimpleName, String indicatorId) {
        IndicatorYuLanData params = new IndicatorYuLanData();
        params.setDelete(Constant.DELETE_NO);
        params.setIndicatorId(indicatorId);
        List<String> result = indicatorDataMapper.listShowDate(params);
        return result;
    }

    //根据最大数据期年份以及指标id获取展示的不删除的非年度更新指标数据
    @Override
    public List<IndicatorYuLanData> listIndicatorDatas(IndicatorYuLanData params) {
        return indicatorDataMapper.listIndicatorDatas(params);
    }

    //根据最大数据期年份以及指标id获取展示的不删除的年度更新指标数据
    @Override
    public List<IndicatorYuLanData> listIndicatorYearDatas(IndicatorYuLanData params) {
        return indicatorDataMapper.listIndicatorYearDatas(params);
    }

    @Override
    public String getMaxUpdateDateYear(String indicatorId) {
        return indicatorDataMapper.getMaxUpdateDateYear(indicatorId);
    }

    /**
     * 根据前端传的年份查询指标数据
     *
     * @param req
     * @return
     */
    @Override
    public List<IndicatorYuLanData> getDataList(GeneralIndicatorYearDataReqVO req) {
        //确定查询范围
        String startYear = null;
        String endYear = null;
        List<IndicatorYuLanData> generalIndicatorDataList = new ArrayList<>();
        if (req.getYearDate().length() > 4) {
            startYear = req.getYearDate().split("-")[0];
            endYear = req.getYearDate().split("-")[1];
        } else if (req.getYearDate().length() == 4) {
            startYear = req.getYearDate();
        }
        generalIndicatorDataList = indicatorDataMapper.getYearDataList(req.getId(), startYear, endYear);
        return generalIndicatorDataList;
    }

}