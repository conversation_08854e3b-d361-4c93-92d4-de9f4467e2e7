package com.onecity.os.management.yulan.vo;

import com.onecity.os.management.zhibiao.entity.GeneralIndicatorDataTitle;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 核心指标
 * <AUTHOR>
 * @date 2020-05-022 11:02
 */
@Data
public class IndicatorYuLanCoreVo implements Serializable {


    private static final long serialVersionUID = -5734510870809269187L;
    /**
     * 指标id
     */
    private String id;
    /**
     * 指标名称-对应前端标题名称
     */
    private String indicatorName;
    /**
     * 更新日期
     */
    private String updateDate;
    /**
     * 更新周期
     */
    private String updateCycle;
    /**
     * 数据来源
     */
    private String sourceName;
    /**
     * 展示类型
     */
    private String indicatorExhibitType;
    /**
     * 数据所属日期
     */
    private String showDate;


    /**
     * 是否展示筛选框0展示1展示
     */
    private Integer isScreen;

    /**
     * 是否展示筛选框 0-不展示1展示
     */
    private Integer isLegend;

    // 是否展示0不展示1展示
    private Integer isShow;

    /**
     * 数据所对应的年份
     */
    private String yearDate;

    /**
     * 数据更新方式1-手动填报2数据对接
     */
    private Integer dataUpdateMode;

    /**
     * 数据配置id
     */
    private String dataConfigId;

    /**
     * 数据配置id
     */
    private String urlIds;
    /**
     * 链接名称
     */
    private String urlName;
    /**
     * 链接类型
     */
    private String urlType;

    /**
     * 指标下的指标排列方式
     */
    private String sortType;
    /**
     * 指标名称展示标识0-不展示1-展示
     */
    private String nameShowFlag;
    /**
     * 释义链接
     */
    private String paraUrl;
    /**
     * 数据集id
     */
    private Long dataSetId;

    /**
     * 数据集名称
     */
    private String dataSetName;

    /**
     * 数值单位
     */
    private String dataUnit;

    /**
     * 数据值
     */
    private String dataValue;

    /**
     * X轴自动或者指标项
     */
    private String dataKey;


    /**
     * 历史年份集合
     */
    private List<String> showDateList;


    /**
     * 核心指标数据列表
     */
    private List<IndicatorYuLanDataVO> list;

    /**
     * 核心数据-子指标数据
     */
    private List<IndicatorYuLanCoreVo> childList;

    /**
     * 指标头数据
     */
    private List<GeneralIndicatorDataTitle> tableHeaderList;


}
