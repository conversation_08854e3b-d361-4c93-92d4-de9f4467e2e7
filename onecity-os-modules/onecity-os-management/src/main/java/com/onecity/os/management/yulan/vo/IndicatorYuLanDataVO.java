package com.onecity.os.management.yulan.vo;

import com.onecity.os.management.yulan.po.ItemValues;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 具体指标数据
 *
 * <AUTHOR>
 * @Date 2020/2/26 13:53
 */
@Data
public class IndicatorYuLanDataVO implements Serializable {

    private static final long serialVersionUID = 8512157390406909121L;
    /**
     * 指标数据id
     */
    private Long id;
    /**
     * 指标名称
     */
    private String itemName;
    /**
     * 指标数据
     */
    private String itemValue;

    private String itemValue1;

    private String itemValue2;

    private String itemValue3;

    private String itemValue4;

    private String itemValue5;

    private String itemValue6;

    private String itemUnit2nd;
    /**
     * 指标单位
     */
    private String itemUnit;
    /**
     * 指标展示颜色--positive 红色 title标题 不需要冒号 negative--绿色 text--文本
     */
    private String identify;
    /**
     * 指标展示是否加横线 0：否1：是
     */
    private Integer style;

    /**
     * 是否折行显示0：否1：是
     */
    private Integer fold;

    /**
     * x轴或指标项名称
     */
    private String dataKeyName;

    /**
     * 副值单位
     */
    private String secondaryUnit;

    /**
     * 数据对接数据值列表接收
     */
    private List<ItemValues> itemValuesList;
}
