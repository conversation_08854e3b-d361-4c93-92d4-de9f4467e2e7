package com.onecity.os.management.yulan.vo;

import com.onecity.os.management.zhibiao.entity.GeneralIndicatorDataTitle;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 解读区数据
 * <AUTHOR>
 */
@Data
public class IndicatorYuLanDetailVO implements Serializable {

    private static final long serialVersionUID = 853020813359534L;

    private String id;

    private String indicatorName;

    /**
     * 指标展现类型
     **/
    private String indicatorExhibitType;

    /**
     * 更新周期
     */
    private String updateCycle;

    /**
     * 是否展示筛选框0展示1展示
     */
    private Integer isScreen;

    /**
     * 是否展示筛选框 0-不展示1展示
     */
    private Integer isLegend;

    // 是否展示0不展示1展示
    private Integer isShow;

    /**
     * 数据所对应的年份
     */
    private String yearDate;

    /**
     * 数据更新方式1-手动填报2数据对接
     */
    private Integer dataUpdateMode;

    /**
     * 数据配置id
     */
    private String dataConfigId;

    /**
     * 数据集id
     */
    private Long dataSetId;

    /**
     * 数据集名称
     */
    private String dataSetName;

    /**
     * 数值单位
     */
    private String dataUnit;

    /**
     * 数据值
     */
    private String dataValue;

    /**
     * X轴自动或者指标项
     */
    private String dataKey;

    /**
     * 指标数据
     */
    private List<IndicatorYuLanDataVO> list;

    /**
     * 指标数据
     */
    private List<IndicatorYuLanDetailVO> childList;

    /**
     * 指标头数据
     */
    private List<GeneralIndicatorDataTitle> tableHeaderList;

}
