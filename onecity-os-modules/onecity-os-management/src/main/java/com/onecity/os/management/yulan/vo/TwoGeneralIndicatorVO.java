package com.onecity.os.management.yulan.vo;

import com.github.pagehelper.PageInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * 实体（二级指标）
 * <AUTHOR>
 */
@Data
public class TwoGeneralIndicatorVO implements Serializable {

    private static final long serialVersionUID = 4983346127581497177L;

    private String id;

    private String indicatorName;
    //排序
    private Integer sequence;

    /**
     * 核心指标列表
     */
    private PageInfo<IndicatorYuLanCoreVo> indicatorCoreVoPageInfo;
}
