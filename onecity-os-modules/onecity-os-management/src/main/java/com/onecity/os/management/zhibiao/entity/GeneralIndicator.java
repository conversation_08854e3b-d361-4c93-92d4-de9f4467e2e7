package com.onecity.os.management.zhibiao.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import javax.persistence.*;

@Data
@Table(name = "general_indicator")
public class GeneralIndicator {
    /**
     * 指标ID
     */
    @Id
    private String id;

    /**
     * 指标名称
     */
    @Column(name = "indicator_name")
    private String indicatorName;

    /**
     * 指标展现类型
     */
    @Column(name = "indicator_exhibit_type")
    private String indicatorExhibitType;

    /**
     * 父指标ID,如果为一级指标, 该字段为空
     */
    @Column(name = "parent_id")
    private String parentId;

    /**
     * 父指标名称,如果为一级指标, 该字段为空
     */
    @Column(name = "parent_name")
    private String parentName;

    /**
     * 图标地址
     */
    @Column(name = "icon_url")
    private String iconUrl;

    /**
     * 数据来源id
     */
    @Column(name = "source_id")
    private String sourceId;

    /**
     * 数据来源
     */
    @Column(name = "source_name")
    private String sourceName;

    /**
     * 排序
     */
    private Integer sequence;

    /**
     * 指标类型，0：指标，1：tab类型
     */
    @Column(name = "indicator_type")
    private Integer indicatorType;

    /**
     * 更新日期文本类型
     */
    @Column(name = "update_date")
    private String updateDate;

    /**
     * 更新周期
     */
    @Column(name = "update_cycle")
    private String updateCycle;

    /**
     * 面向领导
     */
    private String leader;

    /**
     * 是否删除0:否1:是
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人
     */
    private String creater;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 分组类型 0指标 1网有
     */
    @Column(name = "group_type")
    private Integer groupType;

    /**
     * 分组网页
     */
    @Column(name = "group_url")
    private String groupUrl;

    /**
     * 约定更新日期
     */
    @Column(name = "plan_update_date")
    private String planUpdateDate;

    /**
     * 是否展示 0-不展示1展示
     */
    @Column(name = "is_show")
    private Integer isShow;

    /**
     * 是否展示筛选框 0-不展示1展示
     */
    @Column(name = "is_screen")
    private Integer isScreen;

    /**
     * 是否展示筛选框 0-不展示1展示
     */
    @Column(name = "is_legend")
    private Integer isLegend;

    /**
     * 数据更新方式1-手动填报2数据对接
     */
    @Column(name = "data_update_mode")
    private Integer dataUpdateMode;

    /**
     * 数据配置id
     */
    @Column(name = "data_config_id")
    private String dataConfigId;

    /**
     * 关联指标id
     */
    @Column(name = "url_ids")
    private String urlIds;
    /**
     * 链接名称
     */
    @Column(name = "url_name")
    private String urlName;
    /**
     * 链接类型
     */
    @Column(name = "url_type")
    private String urlType;

    /**
     * 指标下的指标排列方式
     */
    @Column(name = "sort_type")
    private String sortType;
    /**
     * 指标名称展示标识0-不展示1-展示
     */
    @Column(name = "name_show_flag")
    private String nameShowFlag;
    /**
     * 释义链接
     */
    @Column(name = "para_url")
    private String paraUrl;

    /**
     * 获取指标ID
     *
     * @return id - 指标ID
     */
    public String getId() {
        return id;
    }

    /**
     * 设置指标ID
     *
     * @param id 指标ID
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取指标名称
     *
     * @return indicator_name - 指标名称
     */
    public String getIndicatorName() {
        return indicatorName;
    }

    /**
     * 设置指标名称
     *
     * @param indicatorName 指标名称
     */
    public void setIndicatorName(String indicatorName) {
        this.indicatorName = indicatorName;
    }

    /**
     * 获取指标展现类型
     *
     * @return indicator_exhibit_type - 指标展现类型
     */
    public String getIndicatorExhibitType() {
        return indicatorExhibitType;
    }

    /**
     * 设置指标展现类型
     *
     * @param indicatorExhibitType 指标展现类型
     */
    public void setIndicatorExhibitType(String indicatorExhibitType) {
        this.indicatorExhibitType = indicatorExhibitType;
    }

    /**
     * 获取父指标ID,如果为一级指标, 该字段为空
     *
     * @return parent_id - 父指标ID,如果为一级指标, 该字段为空
     */
    public String getParentId() {
        return parentId;
    }

    /**
     * 设置父指标ID,如果为一级指标, 该字段为空
     *
     * @param parentId 父指标ID,如果为一级指标, 该字段为空
     */
    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    /**
     * 获取父指标名称,如果为一级指标, 该字段为空
     *
     * @return parent_name - 父指标名称,如果为一级指标, 该字段为空
     */
    public String getParentName() {
        return parentName;
    }

    /**
     * 设置父指标名称,如果为一级指标, 该字段为空
     *
     * @param parentName 父指标名称,如果为一级指标, 该字段为空
     */
    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    /**
     * 获取图标地址
     *
     * @return icon_url - 图标地址
     */
    public String getIconUrl() {
        return iconUrl;
    }

    /**
     * 设置图标地址
     *
     * @param iconUrl 图标地址
     */
    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    /**
     * 获取数据来源id
     *
     * @return source_id - 数据来源id
     */
    public String getSourceId() {
        return sourceId;
    }

    /**
     * 设置数据来源id
     *
     * @param sourceId 数据来源id
     */
    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    /**
     * 获取数据来源
     *
     * @return source_name - 数据来源
     */
    public String getSourceName() {
        return sourceName;
    }

    /**
     * 设置数据来源
     *
     * @param sourceName 数据来源
     */
    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    /**
     * 获取排序
     *
     * @return sequence - 排序
     */
    public Integer getSequence() {
        return sequence;
    }

    /**
     * 设置排序
     *
     * @param sequence 排序
     */
    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    /**
     * 获取指标类型，0：指标，1：tab类型
     *
     * @return indicator_type - 指标类型，0：指标，1：tab类型
     */
    public Integer getIndicatorType() {
        return indicatorType;
    }

    /**
     * 设置指标类型，0：指标，1：tab类型
     *
     * @param indicatorType 指标类型，0：指标，1：tab类型
     */
    public void setIndicatorType(Integer indicatorType) {
        this.indicatorType = indicatorType;
    }

    /**
     * 获取更新日期文本类型
     *
     * @return update_date - 更新日期文本类型
     */
    public String getUpdateDate() {
        return updateDate;
    }

    /**
     * 设置更新日期文本类型
     *
     * @param updateDate 更新日期文本类型
     */
    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    /**
     * 获取更新周期
     *
     * @return update_cycle - 更新周期
     */
    public String getUpdateCycle() {
        return updateCycle;
    }

    /**
     * 设置更新周期
     *
     * @param updateCycle 更新周期
     */
    public void setUpdateCycle(String updateCycle) {
        this.updateCycle = updateCycle;
    }

    /**
     * 获取面向领导
     *
     * @return leader - 面向领导
     */
    public String getLeader() {
        return leader;
    }

    /**
     * 设置面向领导
     *
     * @param leader 面向领导
     */
    public void setLeader(String leader) {
        this.leader = leader;
    }

    /**
     * 获取是否删除0:否1:是
     *
     * @return is_delete - 是否删除0:否1:是
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    /**
     * 设置是否删除0:否1:是
     *
     * @param isDelete 是否删除0:否1:是
     */
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取创建人
     *
     * @return creater - 创建人
     */
    public String getCreater() {
        return creater;
    }

    /**
     * 设置创建人
     *
     * @param creater 创建人
     */
    public void setCreater(String creater) {
        this.creater = creater;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新人
     *
     * @return updater - 更新人
     */
    public String getUpdater() {
        return updater;
    }

    /**
     * 设置更新人
     *
     * @param updater 更新人
     */
    public void setUpdater(String updater) {
        this.updater = updater;
    }

    /**
     * 获取分组类型 0指标 1网有
     *
     * @return group_type - 分组类型 0指标 1网有
     */
    public Integer getGroupType() {
        return groupType;
    }

    /**
     * 设置分组类型 0指标 1网有
     *
     * @param groupType 分组类型 0指标 1网有
     */
    public void setGroupType(Integer groupType) {
        this.groupType = groupType;
    }

    /**
     * 获取分组网页
     *
     * @return group_url - 分组网页
     */
    public String getGroupUrl() {
        return groupUrl;
    }

    /**
     * 设置分组网页
     *
     * @param groupUrl 分组网页
     */
    public void setGroupUrl(String groupUrl) {
        this.groupUrl = groupUrl;
    }

    public Integer getIsShow() {
        return isShow;
    }

    public void setIsShow(Integer isShow) {
        this.isShow = isShow;
    }

    public Integer getIsScreen() {
        return isScreen;
    }

    public void setIsScreen(Integer isScreen) {
        this.isScreen = isScreen;
    }

    public Integer getIsLegend() {
        return isLegend;
    }

    public void setIsLegend(Integer isLegend) {
        this.isLegend = isLegend;
    }

    public Integer getDataUpdateMode() {
        return dataUpdateMode;
    }

    public void setDataUpdateMode(Integer dataUpdateMode) {
        this.dataUpdateMode = dataUpdateMode;
    }

    public String getDataConfigId() {
        return dataConfigId;
    }

    public void setDataConfigId(String dataConfigId) {
        this.dataConfigId = dataConfigId;
    }

    public String getPlanUpdateDate() {
        return planUpdateDate;
    }

    public void setPlanUpdateDate(String planUpdateDate) {
        this.planUpdateDate = planUpdateDate;
    }

    public String getUrlIds() {
        return urlIds;
    }

    public void setUrlIds(String urlIds) {
        this.urlIds = urlIds;
    }
    public String getUrlName() {
        return urlName;
    }

    public void setUrlName(String urlName) {
        this.urlName = urlName;
    }
    public String getUrlType() {
        return urlType;
    }

    public void setUrlType(String urlType) {
        this.urlType = urlType;
    }

    public String getSortType() {
        return sortType;
    }

    public void setSortType(String sortType) {
        this.sortType = sortType;
    }

    public String getNameShowFlag() {
        return nameShowFlag;
    }

    public void setNameShowFlag(String nameShowFlag) {
        this.nameShowFlag = nameShowFlag;
    }

    public String getParaUrl() {
        return paraUrl;
    }

    public void setParaUrl(String paraUrl) {
        this.paraUrl = paraUrl;
    }
}