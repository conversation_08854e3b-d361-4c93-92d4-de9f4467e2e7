package com.onecity.os.management.zhibiao.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 普通指标
 *
 */
@Data
@Table(name = "general_indicator_tianbao")
public class GeneralIndicatorTianbao {
    /**
     * 主键
     **/
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    //@TableId(value = "id", type = IdType.UUID)
    @Id
    private String id;

    /**
     * 指标名称
     **/
    private String indicatorName;
    /**
     * 指标展现类型
     **/
    private String indicatorExhibitType;

    /**
     * 父指标ID,如果为一级指标, 该字段为空
     **/
    private String parentId;

    /**
     * 父指标名称,如果为一级指标, 该字段为空
     **/
    private String parentName;

    /**
     * 图标地址
     */
    private String iconUrl;

    /**
     * 数据来源id
     */
    private String sourceId;

    /**
     * 数据来源
     */
    private String sourceName;

    /**
     * 排序
     **/
    private Integer sequence;

    /**
     * 指标类型，0：指标，1：tab类型
     **/
    private Integer indicatorType;

    /**
     * 更新日期文本类型
     **/
    private String updateDate;

    /**
     * 更新周期
     **/
    private String updateCycle;
    /**
     * 面向领导
     */
    private String leader;

    /**
     * 是否删除0:否1:是
     */
    private Integer isDelete;

    /**
     * 创建时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建人
     **/
    private String creater;

    /**
     * 更新时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新人
     **/
    private String updater;

    /**
     * 分组类型 0指标 1网页
     */
    private Integer groupType;

    /**
     * 分组网页
     */
    private String groupUrl;

    /**
     * 约定更新时间
     */
    private String planUpdateDate;

    /**
     * 是否展示 0-不展示1展示
     */
    @Column(name = "is_show")
    private Integer isShow;

    /**
     * 是否展示筛选框 0-不展示1展示
     */
    @Column(name = "is_screen")
    private Integer isScreen;

    /**
     * 是否展示筛选框 0-不展示1展示
     */
    @Column(name = "is_legend")
    private Integer isLegend;

    /**
     * 数据更新方式1-手动填报2数据对接
     */
    @Column(name = "data_update_mode")
    private Integer dataUpdateMode;

    /**
     * 数据配置id
     */
    @Column(name = "data_config_id")
    private String dataConfigId;

    /**
     * 关联指标id
     */
    @Column(name = "url_ids")
    private String urlIds;
    /**
     * 链接名称
     */
    @Column(name = "url_name")
    private String urlName;
    /**
     * 链接类型
     */
    @Column(name = "url_type")
    private String urlType;
    /**
     * 指标下的指标排列方式
     */
    @Column(name = "sort_type")
    private String sortType;
    /**
     * 释义链接
     */
    @Column(name = "para_url")
    private String paraUrl;

    /**
     * 指标名称展示标识0-不展示1-展示
     */
    @Column(name = "name_show_flag")
    private String nameShowFlag;
}
