package com.onecity.os.management.zhibiao.entity.vo;

import com.onecity.os.management.zhibiao.entity.GeneralIndicatorDataTitle;
import lombok.Data;
import java.util.List;

/**
 * 指标标头标
 * <AUTHOR>
 */

@Data
public class IndicatorTitleVo {

    /**
     * 主值项
     * 前端传值   itemValue itemValue1 itemValue2
     * 对应数据库 item_value item_value1 item_value2
     */
    private List<GeneralIndicatorDataTitle> mainValues;

    /**
     * 副值项
     * 前端传值   itemValue itemValue1 itemValue2
     * 对应数据库 item_value item_value1 item_value2
     */
    private List<GeneralIndicatorDataTitle> subValues;

    /**
     * 第一单位
     */
    private String itemUnit;

    /**
     * 第二单位
     */
    private String itemUnit2nd;

    /**
     * 指标id
     */
    private String indicatorId;

}