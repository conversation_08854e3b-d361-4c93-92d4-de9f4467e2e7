package com.onecity.os.management.zhibiao.mapper;


import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.management.zhibiao.entity.GeneralIndicatorData;
import com.onecity.os.management.zhibiao.entity.GeneralIndicatorDataTianbao;
import com.onecity.os.management.zhibiao.model.vo.IndicatorDataExcel;
import com.onecity.os.management.zhibiaowarning.entity.vo.IndicatorItemValueVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.List;

/**
 * 预览页用的数据表
 */
@EnableAsync
public interface GeneralIndicatorDataMapper extends BaseMapper<GeneralIndicatorData> {
    /**
     * 根据厅局来源,查找指标数据
     *
     * @param sources
     * @return
     */
    List<GeneralIndicatorDataTianbao> getIndicatorDataListBySourceIds(@Param("sources") List<String> sources);

    /**
     * 插入数据
     *
     * @param generalIndicatorData
     */
    void insertData(@Param("vo") GeneralIndicatorData generalIndicatorData);

    /**
     * 根据指标id,查找指标数据
     *
     * @param indicatorId
     * @return
     */
    List<IndicatorDataExcel> getExportIndicatorDataXlsByIndicatorId(@Param("indicatorId") String indicatorId);

    /**
     * 根据指标id,查找指标数据项名
     *
     * @param indicatorId
     * @return
     */
    List<String> getIndicatorDataNameListByIndicatorId(@Param("indicatorId") String indicatorId);

    /**
     * 插入指标数据
     *
     * @param generalIndicatorData
     */
    void insertTianBaoIndicatorData(@Param("vo") GeneralIndicatorData generalIndicatorData);

    /**
     * 根据指标id,删除指标数据
     *
     * @param indicatorId
     */
    void deleteDataByIndicatorId(@Param("indicatorId") String indicatorId);

    /**
     * 根据指标id,批量删除指标数据
     *
     * @param indicatorIds
     */
    @Async
    void deleteDataByIndicatorIds(@Param("indicatorIds") List<String> indicatorIds);

    /**
     * 根据指标id,和指标数据项名称,更新指标数据
     *
     * @param generalIndicatorData
     */
    void updateByIndicatorIdAndItemName(@Param("vo") GeneralIndicatorData generalIndicatorData);

    /**
     * 根据指标id，查询指标数据
     */
    List<GeneralIndicatorData> getGeneralIndicatorDataByIndicatorId(@Param("indicatorId") String indicatorId);

    /**
     * 批量更新
     * @param dataList
     * @return
     */
    @Async
    int updateByList(@Param("dataList") List<GeneralIndicatorData> dataList);

    /**
     * 批量插入
     * @param dataList
     * @return
     */
    @Async
    int insertByList(@Param("dataList") List<GeneralIndicatorData> dataList);



    /**
     * 获取指标数据id
     * @param sources
     * @return
     */
    List<Long> getIndicatorDataIdListBySourceIds(@Param("sources") List<String> sources);

    /**
     * 根据指标id,指标数据项名、查询指定的指标数据
     * 固定值比较
     * @param indicatorId
     * @return
     */
    List<IndicatorItemValueVO> queryItemValueByNameID(@Param("indicatorId") String indicatorId, @Param("itemName") String itemName, @Param("itemValue") String itemValue);
    /**
     * 根据指标id,指标数据项名、查询指定的指标数据
     * 环比
     * @param indicatorId
     * @return
     */
    List<IndicatorItemValueVO> queryLastItemValueByNameID(@Param("indicatorId") String indicatorId, @Param("itemName") String itemName, @Param("itemValue") String itemValue);
    /**
     * 根据指标id,指标数据项名、查询指定的指标数据
     * 同比
     * @param indicatorId
     * @return
     */
    List<IndicatorItemValueVO> queryLastYearItemValueByNameID(@Param("indicatorId") String indicatorId, @Param("itemName") String itemName, @Param("itemValue") String itemValue);
    /**
     * 根据指标id,指标数据项名、查询指定的指标数据
     * 同比
     * @param indicatorId
     * @return
     */
    List<IndicatorItemValueVO> queryAllForWarnRule(@Param("indicatorId") String indicatorId, @Param("itemName") String itemName,  @Param("itemValue") String itemValue);
}















