package com.onecity.os.management.zhibiao.model.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;

@Data
public class IndicatorAuditNotesDto {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 指标来源简称
     */
    private String sourceId;

    /**
     * 提交人id
     */
    private String commitUserId;

    /**
     * 提交人姓名
     */
    private String commitUserName;

    /**
     * 审核人id
     */
    private String auditUserId;

    /**
     * 审核人姓名
     */
    private String auditUserName;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 审核结果 0-不通过;1-通过
     */
    private Byte auditResult;

    /**
     * 审核意见
     */
    private String auditViews;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date commitTime;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date auditTime;

    /**
     * 是否删除0-否;1-是
     */
    private Byte isDelete;

    /**
     * 提交开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date startTime;

    /**
     * 提交结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date endTime;

    /**
     * 审核开始时间
     */
    private Date auditTimeStart;

    /**
     * 审核结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date auditTimeEnd;

    /**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取指标来源简称
     *
     * @return source_id - 指标来源简称
     */
    public String getSourceId() {
        return sourceId;
    }

    /**
     * 设置指标来源简称
     *
     * @param sourceId 指标来源简称
     */
    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    /**
     * 获取提交人id
     *
     * @return commit_user_id - 提交人id
     */
    public String getCommitUserId() {
        return commitUserId;
    }

    /**
     * 设置提交人id
     *
     * @param commitUserId 提交人id
     */
    public void setCommitUserId(String commitUserId) {
        this.commitUserId = commitUserId;
    }

    /**
     * 获取审核人id
     *
     * @return audit_user_id - 审核人id
     */
    public String getAuditUserId() {
        return auditUserId;
    }

    /**
     * 设置审核人id
     *
     * @param auditUserId 审核人id
     */
    public void setAuditUserId(String auditUserId) {
        this.auditUserId = auditUserId;
    }

    /**
     * 获取备注
     *
     * @return remarks - 备注
     */
    public String getRemarks() {
        return remarks;
    }

    /**
     * 设置备注
     *
     * @param remarks 备注
     */
    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    /**
     * 获取审核结果 0-不通过;1-通过
     *
     * @return audit_ result - 审核结果 0-不通过;1-通过
     */
    public Byte getAuditResult() {
        return auditResult;
    }

    /**
     * 设置审核结果 0-不通过;1-通过
     *
     * @param auditResult 审核结果 0-不通过;1-通过
     */
    public void setAuditResult(Byte auditResult) {
        this.auditResult = auditResult;
    }

    /**
     * 获取审核意见
     *
     * @return audit_ views - 审核意见
     */
    public String getAuditViews() {
        return auditViews;
    }

    /**
     * 设置审核意见
     *
     * @param auditViews 审核意见
     */
    public void setAuditViews(String auditViews) {
        this.auditViews = auditViews;
    }

    /**
     * 获取提交时间
     *
     * @return commit_time - 提交时间
     */
    public Date getCommitTime() {
        return commitTime;
    }

    /**
     * 设置提交时间
     *
     * @param commitTime 提交时间
     */
    public void setCommitTime(Date commitTime) {
        this.commitTime = commitTime;
    }

    /**
     * 获取审核时间
     *
     * @return audit_time - 审核时间
     */
    public Date getAuditTime() {
        return auditTime;
    }

    /**
     * 设置审核时间
     *
     * @param auditTime 审核时间
     */
    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    /**
     * 获取是否删除0-否;1-是
     *
     * @return is_delete - 是否删除0-否;1-是
     */
    public Byte getIsDelete() {
        return isDelete;
    }

    /**
     * 设置是否删除0-否;1-是
     *
     * @param isDelete 是否删除0-否;1-是
     */
    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }
}