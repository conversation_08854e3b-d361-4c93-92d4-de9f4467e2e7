package com.onecity.os.management.zhibiao.model.vo;

import com.onecity.os.common.core.annotation.Dict;
import com.onecity.os.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 普通指标
 *
 */
@Data
public class GeneralIndicatorVO implements Serializable {
    private static final long serialVersionUID = -993688119686392062L;


    /**
     * 一级指标名称
     **/
    @Excel(name="一级指标")
    private String fistIndicatorName;
    /**
     * 二级指标名称
     **/
    @Excel(name="二级指标")
    private String secondIndicatorName;
    @Excel(name="指标项")
    private String itemName;
    @Excel(name="指标值")
    private String itemValue;
    /**
     * 单位
     **/
    @Excel(name="单位")
    private String itemUnit;
    /**
     * 指标展现类型
     **/
    @Excel(name="展现方式")
    @Dict(dicCode = "indicator_exhibit_type")
    private String indicatorExhibitType;
    /**
     * 更新周期
     **/
    @Excel(name="数据更新周期")
    private String updateCycle;
    /**
     * 更新日期文本类型
     **/
    @Excel(name="数据统计时间段")
    private String updateDate;
    @Excel(name="面向领导")
    private String leader;
    /**
     * 数据来源
     */
    @Excel(name="数据来源部门")
    private String sourceName;

}
