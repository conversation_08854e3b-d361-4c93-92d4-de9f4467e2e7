package com.onecity.os.management.zhibiao.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.onecity.os.management.zhibiao.entity.GeneralIndicatorDataTitle;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestBody;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/3 14:35
 */
@Data
public class IndicatorVo {
    /**
     * 主键
     **/
    private String id;

    /**
     * 指标名称/分组名称
     **/
    private String indicatorName;

    /**
     * 排序
     **/
    private Integer sequence;

    /**
     * 父指标ID,如果为一级指标, 该字段为空
     **/
    private String parentId;

    /**
     * 图标
     **/
    private String iconUrl;

    /**
     * 指标展现类型
     **/
    private String indicatorExhibitType;

    /**
     * 指标类型，0：指标，1：tab类型(指标分组)
     */
    private Integer indicatorType;

    /**
     * 更新周期
     */
    private String updateCycle;

    /**
     * 创建人
     **/
    private String creater;

    /**
     * 创建时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     **/
    private String updater;

    /**
     * 更新时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    /**
     * 类别名称
     **/
    private String categoryName;

    /**
     * 数据来源id
     */
    private String sourceId;

    /**
     * 数据来源
     */
    private String sourceName;

    /**
     * 更新日期文本类型
     */
    private String updateDate;

    /**
     * 分组类型 0指标 1网页
     */
    private Integer groupType;

    /**
     * 分组网页
     */
    private String groupUrl;

    /**
     * 是否展示
     */
    private Integer isShow;

    /**
     * 是否展示筛选框
     */
    private Integer isScreen;

    /**
     * 是否展示图例
     */
    private Integer isLegend;

    /**
     * 数据更新方式1-手动填报2数据对接
     */
    private Integer dataUpdateMode;

    /**
     * 数据配置id
     */
    private String dataConfigId;

    /**
     * 关联指标id
     */
    private String urlIds;
    /**
     * 链接名称
     */
    private String urlName;

    /**
     * 链接类型
     */
    private String urlType;
    /**
     * 指标下的指标排列方式
     */
    private String sortType;
    /**
     * 指标名称展示标识0-不展示1-展示
     */
    private String nameShowFlag;
    /**
     * 释义链接
     */
    private String paraUrl;


    /**
     * 指标头数据
     */
    List<GeneralIndicatorDataTitle> titles;

}
