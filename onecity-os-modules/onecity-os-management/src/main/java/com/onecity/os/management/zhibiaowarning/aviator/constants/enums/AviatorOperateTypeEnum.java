package com.onecity.os.management.zhibiaowarning.aviator.constants.enums;

import com.onecity.os.common.core.utils.StringUtils;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public enum AviatorOperateTypeEnum {

    EQU("=", "等于"),
    NOT_EQU("!=", "不等于"),
    GT(">", "大于"),
    GTE(">=", "大于等于"),
    LT("<", "小于"),
    LTE("<=", "小于等于"),
    INCLUDE("IN", "包含"),
    NOT_INCLUDE("NOT IN", "不包含"),

    ;

    private String value;

    private String desc;

    AviatorOperateTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 自定义 valueOf()方法
     *
     * @param desc
     * @return
     */
    public static AviatorOperateTypeEnum valueOfCustom(String desc) {
        for (AviatorOperateTypeEnum anEnum : values()) {
            if (Objects.equals(anEnum.getDesc(), desc)) {
                return anEnum;
            }
        }
        return null;
    }


    /**
     * 比较两个值 注意可能会抛出异常，转换为BigDecimal 异常，需要处理
     * 相等：转换为数字比较大小 其它数字比较同理
     * 包含：此为数据项值列表中包含此值 即 left 为数组列表其中一个值，所以此处用equals 示例 主要景点分布为 5A 4A，预警条件包含 5A ，则返回true
     * 不包含 同包含的逻辑 !包含
     * @param left 文本 1
     * @param right 文本 2
     * @param enmu AviatorOperateTypeEnum
     * @return
     */
    public static Boolean compareValue(String left, String right, AviatorOperateTypeEnum enmu) {
        if (StringUtils.isAllBlank(left, right)) {
            return false;
        }
        BigDecimal leftb = new BigDecimal(left.trim()).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal rightb = new BigDecimal(right.trim()).setScale(2, BigDecimal.ROUND_HALF_UP);
        switch (enmu) {
            case EQU:
                return leftb.compareTo(rightb) == 0;
            case NOT_EQU:
                return leftb.compareTo(rightb) != 0;
            case GT:
                return leftb.compareTo(rightb) > 0;
            case GTE:
                return leftb.compareTo(rightb) >= 0;
            case LT:
                return leftb.compareTo(rightb) < 0;
            case LTE:
                return leftb.compareTo(rightb) <= 0;
            case INCLUDE:
                return left.equals(right);
            case NOT_INCLUDE:
                return !left.equals(right);
            default:
                return false;
        }
    }


}
