package com.onecity.os.management.zhibiaowarning.aviator.constants.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public enum UpdateCycleEnum {
    YEARLY("yearly", "年度更新"),
    SEMI_ANNUAL("semiAnnual", "半年更新"),
    QUARTERLY("quarterly", "季度更新"),
    MONTHLY("monthly", "月度更新"),

    ;

    private String value;

    private String desc;

    UpdateCycleEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 自定义 valueOf()方法
     *
     * @param desc
     * @return
     */
    public static UpdateCycleEnum valueOfCustom(String desc) {
        for (UpdateCycleEnum anEnum : values()) {
            if (Objects.equals(anEnum.getDesc(), desc)) {
                return anEnum;
            }
        }
        return null;
    }

    /**
     * 所有季度
     *
     * @return
     */
    public static Map<String, String> quarterMap() {

        Map<String, String> quarterMap = new HashMap<>();
        quarterMap.put("1", "第一季度");
        quarterMap.put("2", "第二季度");
        quarterMap.put("3", "第三季度");
        quarterMap.put("4", "第四季度");
        quarterMap.put("第一季度", "1");
        quarterMap.put("第二季度", "2");
        quarterMap.put("第三季度", "3");
        quarterMap.put("第四季度", "4");
        return quarterMap;
    }

    /**
     * 所有半年
     *
     * @return
     */
    public static Map<String, String> semiAnnualMap() {

        Map<String, String> semiAnnualMap = new HashMap<>();
        semiAnnualMap.put("1", "上半年");
        semiAnnualMap.put("2", "下半年");
        semiAnnualMap.put("上半年", "1");
        semiAnnualMap.put("下半年", "2");
        return semiAnnualMap;
    }

}
