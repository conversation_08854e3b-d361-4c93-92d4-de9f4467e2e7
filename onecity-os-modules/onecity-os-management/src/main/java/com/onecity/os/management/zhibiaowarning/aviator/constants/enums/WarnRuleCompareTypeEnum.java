package com.onecity.os.management.zhibiaowarning.aviator.constants.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 预警规则 比较方式枚举
 * 1-固定值对比；2-环比；3-同比
 * <AUTHOR>
 */
@Getter
public enum WarnRuleCompareTypeEnum {

    FIXED_VALUE(1, "固定值对比"),
    QOQ(2, "环比"),
    YOY(3, "同比"),

    ;

    private int value;

    private String desc;

    WarnRuleCompareTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 自定义 valueOf()方法
     *
     * @param value
     * @return
     */
    public static WarnRuleCompareTypeEnum valueOfCustom(Integer value) {
        if (Objects.isNull(value)) {
            return null;
        }
        for (WarnRuleCompareTypeEnum anEnum : values()) {
            if (anEnum.getValue() == value) {
                return anEnum;
            }
        }
        return null;
    }


}
