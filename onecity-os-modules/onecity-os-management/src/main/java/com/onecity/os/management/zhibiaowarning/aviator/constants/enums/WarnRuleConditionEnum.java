package com.onecity.os.management.zhibiaowarning.aviator.constants.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 预警规则满足条件枚举
 * <AUTHOR>
 */
@Getter
public enum WarnRuleConditionEnum {

    ALL("1", "满足全部条件"),
    ANY("2", "满足任意条件"),
    DIY("3", "自定义"),

    ;

    private String value;

    private String desc;

    WarnRuleConditionEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 自定义 valueOf()方法
     *
     * @param value
     * @return
     */
    public static WarnRuleConditionEnum valueOfCustom(String value) {
        for (WarnRuleConditionEnum anEnum : values()) {
            if (Objects.equals(anEnum.getValue(), value)) {
                return anEnum;
            }
        }
        return null;
    }


}
