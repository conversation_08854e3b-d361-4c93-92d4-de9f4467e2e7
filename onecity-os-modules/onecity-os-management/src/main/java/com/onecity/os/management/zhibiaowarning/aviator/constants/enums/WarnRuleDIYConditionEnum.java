package com.onecity.os.management.zhibiaowarning.aviator.constants.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 预警规则满足条件枚举
 * <AUTHOR>
 */
@Getter
public enum WarnRuleDIYConditionEnum {

    ONE("A", 0, 1, "满足一个条件"),
    ALLOF2("A&&B", 1, 2, "同时满足两个条件"),
    ANYOF2("A||B", 2, 2, "两个条件满足任意一个条件"),
    ALLOF3("A&&B&&C", 3, 3, "同时满足三个条件"),
    ANYOF3("A||B||C", 4, 3, "三个条件满足任意一个"),
    AANDB1ORC2("(A&&B)||C", 5, 3, "前两个条件同时满足或者第三个条件满足"),
    AAND2BORC1("A&&(B||C)", 6, 3, "第一个条件满足后两个条件任意一个满足"),
    AORB1ANDC2("(A||B)&&C", 7, 3, "第三个条件满足，前两个条件任意一个满足"),
    AOR2BANDC1("A||(B&&C)", 8, 3, "后两个条件同时满足或第一个条件满足"),
    AORC1ANDB2("(A||C)&&B", 9, 3, "中间条件满足后两边条件任意满足"),
    AANDC1ORB2("(A&&C)||B", 10, 3, "两边条件同时满足或中间条件满足"),

    ;

    /**
     * 条件表达式
     */
    private String value;
    /**
     * 条件排序
     */
    private int sort;

    /**
     * 条件元素个数
     */
    private int count;
    /**
     * 条件说明
     */
    private String desc;

    WarnRuleDIYConditionEnum(String value, int sort, int count, String desc) {
        this.value = value;
        this.sort = sort;
        this.count = count;
        this.desc = desc;
    }

    /**
     * 自定义 valueOf()方法
     *
     * @param value
     * @return
     */
    public static WarnRuleDIYConditionEnum valueOfCustom(String value) {
        for (WarnRuleDIYConditionEnum anEnum : values()) {
            if (Objects.equals(anEnum.getValue(), value)) {
                return anEnum;
            }
        }
        return null;
    }


}
