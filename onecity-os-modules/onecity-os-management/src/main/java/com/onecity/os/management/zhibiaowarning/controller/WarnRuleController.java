package com.onecity.os.management.zhibiaowarning.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.BeanHelper;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.management.configmanage.service.SourceManageService;
import com.onecity.os.management.zhibiaowarning.entity.WarnResult;
import com.onecity.os.management.zhibiaowarning.entity.WarnRule;
import com.onecity.os.management.zhibiaowarning.entity.WarnRuleDetail;
import com.onecity.os.management.zhibiaowarning.entity.dto.*;
import com.onecity.os.management.zhibiaowarning.entity.po.ItemTitlePo;
import com.onecity.os.management.zhibiaowarning.entity.po.RemindUserPo;
import com.onecity.os.management.zhibiaowarning.entity.po.WarnInfoPo;
import com.onecity.os.management.zhibiaowarning.entity.po.WarnResultPo1;
import com.onecity.os.management.zhibiaowarning.entity.vo.*;
import com.onecity.os.management.zhibiaowarning.service.WarnResultService;
import com.onecity.os.management.zhibiaowarning.service.WarnRuleService;
import com.onecity.os.system.api.RemoteUserService;
import com.onecity.os.system.api.domain.SysMenu;
import com.onecity.os.system.api.domain.SysRole;
import com.onecity.os.system.api.domain.SysUser;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.mail.search.SearchTerm;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.onecity.os.management.zhibiaowarning.entity.WarnResultHandleLog;
import com.onecity.os.management.zhibiaowarning.entity.dto.WarnResultHandleLogDto;
import com.onecity.os.management.zhibiaowarning.service.WarnResultHandleLogService;
import com.onecity.os.common.core.utils.poi.ExcelUtil;
import com.onecity.os.management.zhibiaowarning.entity.vo.WarnRuleAndResultExportVo;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;

@Slf4j
@RestController
@RequestMapping("/zhibiaowarning")
@Api(tags = "版块管理接口")
public class WarnRuleController extends BaseController {
    @Autowired
    private SourceManageService sourceManageService;
    
    @Autowired
    private WarnRuleService warnRuleService;

    @Autowired
    private RemoteUserService remoteUserService;
    @Autowired
    private WarnResultService warnResultService;

    @Autowired
    private WarnResultHandleLogService warnResultHandleLogService;

    /**
     * 根据用户权限获取用户拥有的指标版块和项目版块
     */
    @GetMapping("/getSourceManageByUser")
    @ApiOperation(value = "获取用户有权限的版块列表", notes = "根据当前登录用户权限获取可访问的指标版块和项目版块")
    public BaseResult getSourceManageByUser() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        BaseResult<List<SysMenu>> menuResult = remoteUserService.getMenuByUserId(loginUser.getUserid());
        if (menuResult.getCode() != 200 || ObjectUtils.isEmpty(menuResult.getData())){
            return BaseResult.fail("未获取到用户权限，请联系管理员");
        }
        List<SysMenu> menuList = menuResult.getData();
        Set<String> sourceSimpleNameList = new HashSet<String>();
        for (SysMenu sysMenu : menuList) {
            //分割path获取最后一个值作为sourceSimpleName
            String[] pathArray = sysMenu.getPath().split("/");
            String sourceSimpleName = pathArray[pathArray.length - 1];
            sourceSimpleNameList.add(sourceSimpleName);
        }
        List<SourceManageVo> sourceManageList = sourceManageService.getSourceManageBySourceSimpleNameList(new ArrayList<String>(sourceSimpleNameList));
        return BaseResult.ok(sourceManageList);
    }
    
    /**
     * 根据指标id获取其下面的子指标信息以及预警规则信息
     */
    @GetMapping("/getWarnAndInfoByParentId")
    @ApiOperation(value = "获取子指标及预警规则信息", notes = "根据指标ID获取其下面的子指标信息以及预警规则信息")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "indicatorId", value = "指标ID", required = true, paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "pageNum", value = "页码", required = true, paramType = "query", dataType = "Integer"),
        @ApiImplicitParam(name = "pageSize", value = "页大小", required = true, paramType = "query", dataType = "Integer")
    })
    public TableDataInfo getWarnAndInfoByParentId(
            @RequestParam("indicatorId") String indicatorId,
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize) {
        startPage();
        List<IndicatorWarn> pageInfo = warnRuleService.getWarnAndInfoByParentId(indicatorId);
        return getDataTable(pageInfo);
    }
    
    /**
     * 根据指标id获取其配置的预警规则列表
     */
    @GetMapping("/getWarnInfoListByIndicatorId")
    @ApiOperation(value = "获取指标预警规则列表", notes = "根据指标ID获取其配置的预警规则列表")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "indicatorId", value = "指标ID", required = true, paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "pageNum", value = "页码", required = true, paramType = "query", dataType = "Integer"),
        @ApiImplicitParam(name = "pageSize", value = "页大小", required = true, paramType = "query", dataType = "Integer")
    })
    public TableDataInfo getWarnInfoListByIndicatorId(
            @RequestParam("indicatorId") String indicatorId,
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize) {
        startPage();
        List<WarnInfoPo> pageInfo = warnRuleService.getWarnInfoListByIndicatorId(indicatorId);
        return getDataTable(pageInfo);
    }
    
    /**
     * 根据指标id和预警规则id设置其状态
     */
    @PostMapping("/startOrStopWarnRule")
    @ApiOperation(value = "启用或停用预警规则", notes = "根据指标ID和预警规则ID设置其状态")
    public BaseResult startOrStopWarnRule(@Valid @RequestBody WarnRuleStatus warnRuleStatus) {
        int result = warnRuleService.updateWarnRuleStatus(warnRuleStatus.getWarnRuleId(), warnRuleStatus.getWarnStatus());
        if (result > 0) {
            return BaseResult.ok("操作成功");
        } else {
            return BaseResult.fail("操作失败");
        }
    }
    
    /**
     * 根据指标id和预警规则id删除预警规则
     */
    @PostMapping("/deleteWarnRule")
    @Log(title = "删除预警规则", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除预警规则", notes = "根据指标ID和预警规则ID删除预警规则")
    public BaseResult deleteWarnRule(@Valid @RequestBody WarnRuleVo warnRuleVo) {
        int result = warnRuleService.deleteWarnRuleById(warnRuleVo.getWarnRuleId());
        if (result > 0) {
            return BaseResult.ok("删除成功");
        } else {
            return BaseResult.fail("删除失败");
        }
    }
    
    /**
     * 新建预警规则
     */
    @PostMapping("/addWarnRule")
    @Log(title = "新建预警规则", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新建预警规则", notes = "新建预警规则及其详情")
    public BaseResult addWarnRule(@Valid @RequestBody WarnRuleDto warnRuleDto) {
        // 转换为实体对象
        WarnRule warnRule = new WarnRule();
        //判断规则名称是否重复
        List<WarnRule> warnRule1 = warnRuleService.selectWarnRuleByName(warnRuleDto.getRuleName());
        if(warnRule1.size()>0){
            return BaseResult.fail("规则名称已存在");
        }
        BeanUtils.copyProperties(warnRuleDto, warnRule);
        
        // 设置默认状态为启用
        if (warnRule.getWarnStatus() == null) {
            warnRule.setWarnStatus(1);
        }
        
        // 处理预警规则详情
        List<WarnRuleDetail> detailList = new ArrayList<>();
        if (warnRuleDto.getWarnRuleDetailList() != null && !warnRuleDto.getWarnRuleDetailList().isEmpty()) {
            for (WarnRuleDetailDto detailDto : warnRuleDto.getWarnRuleDetailList()) {
                WarnRuleDetail detail = new WarnRuleDetail();
                if("条件1".equals(detailDto.getDetailName())||"条件2".equals(detailDto.getDetailName())||"条件3".equals(detailDto.getDetailName())){
                    BeanUtils.copyProperties(detailDto, detail);
                    detail.setDetailId(UUID.randomUUID().toString().replaceAll("-", ""));
                    detailList.add(detail);
                }else {
                    return BaseResult.fail("条件名称只能为条件1、条件2、条件3");
                }

            }
        }
        
        // 保存预警规则及其详情
        int result = warnRuleService.insertWarnRuleWithDetails(warnRule, detailList);
        if (result > 0) {
            return BaseResult.ok("新建成功");
        } else {
            return BaseResult.fail("新建失败");
        }
    }

    /**
     * 编辑预警规则
     */
    @PostMapping("/editWarnRule")
    @Log(title = "编辑预警规则", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "编辑预警规则", notes = "编辑预警规则及其详情")
    public BaseResult editWarnRule(@Valid @RequestBody WarnRuleDto1 warnRuleDto1) {
        // 检查预警规则是否存在
        WarnRule existingRule = warnRuleService.selectWarnRuleById(warnRuleDto1.getWarnRuleId());
        if (existingRule == null) {
            return BaseResult.fail("预警规则不存在");
        }
        //判断规则名称是否重复
        List<WarnRule> warnRule1 = warnRuleService.selectWarnRuleByNameAndId(warnRuleDto1.getRuleName(),warnRuleDto1.getWarnRuleId());
        if(warnRule1.size()>=1){
            return BaseResult.fail("规则名称已存在");
        }
        // 转换为实体对象
        WarnRule warnRule = new WarnRule();
        BeanUtils.copyProperties(warnRuleDto1, warnRule);
        
        // 处理预警规则详情
        List<WarnRuleDetail> detailList = new ArrayList<>();
        if (warnRuleDto1.getWarnRuleDetailList() != null && !warnRuleDto1.getWarnRuleDetailList().isEmpty()) {
            for (WarnRuleDetailDto1 detailDto : warnRuleDto1.getWarnRuleDetailList()) {
                if("条件1".equals(detailDto.getDetailName())||"条件2".equals(detailDto.getDetailName())||"条件3".equals(detailDto.getDetailName())) {
                    WarnRuleDetail detail = new WarnRuleDetail();
                    BeanUtils.copyProperties(detailDto, detail);
                    detailList.add(detail);
                }else {
                    return BaseResult.fail("条件名称只能为条件1、条件2、条件3");
                }
            }
        }
        
        // 更新预警规则及其详情
        int result = warnRuleService.updateWarnRuleWithDetails(warnRule, detailList);
        if (result > 0) {
            return BaseResult.ok("更新成功");
        } else {
            return BaseResult.fail("更新失败");
        }
    }

    /**
     * 根据版块编码、指标ID和预警规则ID获取预警规则详情
     */
    @GetMapping("/getWarnRuleById")
    @ApiOperation(value = "获取预警规则详情", notes = "根据版块编码、指标ID和预警规则ID获取预警规则及其详情")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "sourceId", value = "版块编码", required = true, paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "indicatorId", value = "指标ID", required = true, paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "warnRuleId", value = "预警规则ID", required = true, paramType = "query", dataType = "String")
    })
    public BaseResult getWarnRuleById(
            @RequestParam("sourceId") String sourceId,
            @RequestParam("indicatorId") String indicatorId,
            @RequestParam("warnRuleId") String warnRuleId) {
        // 获取预警规则
        WarnRule warnRule = warnRuleService.selectWarnRuleByIdAndSourceId(warnRuleId, sourceId, indicatorId);
        if (warnRule == null) {
            return BaseResult.fail("预警规则不存在");
        }
        
        // 获取预警规则详情
        List<WarnRuleDetail> detailList = warnRuleService.selectWarnRuleDetailsByWarnRuleId(warnRuleId);
        
        // 构建返回VO对象
        WarnRuleVo1 warnRuleVo1 = new WarnRuleVo1();
        BeanUtils.copyProperties(warnRule, warnRuleVo1);
        // 处理用户Id
        if (1==warnRule.getRemindType()&&StringUtils.isNotBlank(warnRule.getRemindUserIds())) {
            String[] userIds = warnRule.getRemindUserIds().split(",");
            StringBuilder userNames = new StringBuilder();
            BaseResult<List<SysUser>> userList = remoteUserService.getUserByPcUserIds(userIds);
            if (userList.getCode() == 200 && userList.getData() != null) {
                warnRuleVo1.setSysUserList(userList.getData());
                for (SysUser sysUser : userList.getData()) {
                    userNames.append(sysUser.getNickName()).append("、");
                }
                warnRuleVo1.setRemindUsers(userNames.toString().substring(0,userNames.length() - 1));
            }
        }else if (2==warnRule.getRemindType()&&StringUtils.isNotBlank(warnRule.getRemindRoleId())) {
            String[] roleIds = warnRule.getRemindRoleId().split(",");
            StringBuilder roleNames = new StringBuilder();
            BaseResult<List<SysRole>> roleList = remoteUserService.getRoleNameByRoleIds(roleIds);
            if (roleList.getCode() == 200 && roleList.getData()!= null) {
                warnRuleVo1.setSysRoleList(roleList.getData());
                for (SysRole sysRole : roleList.getData()) {
                    roleNames.append(sysRole.getRoleName()).append("、");
                }
                warnRuleVo1.setRemindRoles(roleNames.toString().substring(0,roleNames.length() - 1));
            }
        }
        // 转换详情列表
        List<WarnRuleDetailVo> detailVOList = new ArrayList<>();
        if (detailList != null && !detailList.isEmpty()) {
            for (WarnRuleDetail detail : detailList) {
                WarnRuleDetailVo detailVO = new WarnRuleDetailVo();
                BeanUtils.copyProperties(detail, detailVO);
                detailVOList.add(detailVO);
            }
        }
        warnRuleVo1.setDetailList(detailVOList);
        
        return BaseResult.ok(warnRuleVo1);
    }
    /**
     * 根据指标id查询指标的数据项
     */
    @GetMapping("/getItemByIndicatorId")
    @ApiOperation(value = "查询指标数据项", notes = "根据指标ID查询该指标关联的数据项列表")
    @ApiImplicitParam(name = "indicatorId", value = "指标ID", required = true, paramType = "query", dataType = "String")
    public BaseResult<List<String>> getItemByIndicatorId(@RequestParam("indicatorId") String indicatorId) {
        List<String> itemList = warnRuleService.getItemByIndicatorId(indicatorId); // 调用Service层方法
        return BaseResult.ok(itemList);
    }

    /**
     * 根据指标id查询正在使用的表头
     */
    @GetMapping("/getItemTitleByIndicatorId")
    @ApiOperation(value = "查询指标表头信息", notes = "根据指标ID查询该指标正在使用的表头列表")
    @ApiImplicitParam(name = "indicatorId", value = "指标ID", required = true, paramType = "query", dataType = "String")
    public BaseResult<List<ItemTitlePo>> getItemTitleByIndicatorId(@RequestParam("indicatorId") String indicatorId) {
        List<ItemTitlePo> itemTitlePoList = warnRuleService.getItemTitleByIndicatorId(indicatorId); // 调用Service层方法
        return BaseResult.ok(itemTitlePoList);
    }

    /**
     * 根据时间周期查询预警总数
     */
    @ApiOperation("预警结果统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "beginTime", value = "开始时间", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", dataType = "date", paramType = "query")
    })
    @GetMapping("/countWarnResult")
    public BaseResult<WarnResultCountVo> countWarnResult(@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date beginTime,
                                                       @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        WarnResultCountVo resultVo = warnResultService.countWarnResult(beginTime, endTime);
        return BaseResult.ok(resultVo);
    }

    /**
     * 获取全部的预警规则及相应的预警结果列表
     */
    @GetMapping("/getWarnRuleAndResultList")
    @ApiOperation(value = "获取预警规则及结果列表", notes = "获取全部的预警规则及相应的预警结果列表（分页）可带查询条件")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNum", value = "页码", required = true, paramType = "query", dataType = "Integer"),
        @ApiImplicitParam(name = "pageSize", value = "页大小", required = true, paramType = "query", dataType = "Integer"),
        @ApiImplicitParam(name = "sourceName", value = "版块名称", paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "indicatorName", value = "指标名称", paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "warnRuleName", value = "预警规则名称", paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "resultBeginTime", value = "触发预警查询开始时间", paramType = "query", dataType = "date"),
        @ApiImplicitParam(name = "resultEndTime", value = "触发预警查询结束时间", paramType = "query", dataType = "date")
    })
    public TableDataInfo getWarnRuleAndResultList(
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize,
            @RequestParam(value = "sourceName", required = false) String sourceName,
            @RequestParam(value = "indicatorName", required = false) String indicatorName,
            @RequestParam(value = "warnRuleName", required = false) String warnRuleName,
            @RequestParam(value = "resultBeginTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date resultBeginTime,
            @RequestParam(value = "resultEndTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date resultEndTime) {

        // 构建查询条件
        WarnRuleAndResultQueryDto queryDto = new WarnRuleAndResultQueryDto();
        queryDto.setSourceName(sourceName);
        queryDto.setIndicatorName(indicatorName);
        queryDto.setWarnRuleName(warnRuleName);
        queryDto.setResultBeginTime(resultBeginTime);
        queryDto.setResultEndTime(resultEndTime);

        // 分页查询
        startPage();
        List<WarnRuleAndResult> list = warnRuleService.getWarnRuleAndResultList(queryDto);

        return getDataTable(list);
    }

    /**
     * 根据预警规则ID获取预警历史
     */
    @GetMapping("/getWarnResultList")
    @ApiOperation(value = "获取预警历史", notes = "根据预警规则ID获取预警历史（分页）")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNum", value = "页码", required = true, paramType = "query", dataType = "Integer"),
        @ApiImplicitParam(name = "pageSize", value = "页大小", required = true, paramType = "query", dataType = "Integer"),
        @ApiImplicitParam(name = "warnRuleId", value = "预警规则ID", required = true, paramType = "query", dataType = "String")
    })
    public TableDataInfo getWarnResultList(
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize,
            @RequestParam("warnRuleId") String warnRuleId) {

        // 验证参数
        if (StringUtils.isEmpty(warnRuleId)) {
            return getDataTable(new ArrayList<>());
        }

        // 查询预警规则是否存在
        WarnRule warnRule = warnRuleService.selectWarnRuleById(warnRuleId);
        if (warnRule == null) {
            return getDataTable(new ArrayList<>());
        }

        // 分页查询
        startPage();
        List<WarnResultPo1> list = warnResultService.getWarnResultListByWarnRuleId(warnRuleId);

        return getDataTable(list);
    }

    /**
     * 获取预警结果通知用户列表
     */
    @GetMapping("/getRemindUserList")
    @ApiOperation(value = "获取预警结果通知用户列表", notes = "根据预警结果ID获取通知用户列表（分页）")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNum", value = "页码", required = true, paramType = "query", dataType = "Integer"),
        @ApiImplicitParam(name = "pageSize", value = "页大小", required = true, paramType = "query", dataType = "Integer"),
        @ApiImplicitParam(name = "warnResultId", value = "预警结果ID", required = true, paramType = "query", dataType = "String")
    })
    public TableDataInfo getRemindUserList(
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize,
            @RequestParam("warnResultId") String warnResultId) {

        // 验证参数
        if (StringUtils.isEmpty(warnResultId)) {
            return getDataTable(new ArrayList<>());
        }

        // 查询预警结果是否存在
        WarnResult warnResult = warnResultService.selectByPrimaryKey(warnResultId);
        if (warnResult == null) {
            return getDataTable(new ArrayList<>());
        }

        // 分页查询
        // 使用新的分页方法
        PageInfo<RemindUserPo> pageInfo = warnResultService.getRemindUserListPage(warnResultId, pageNum, pageSize);

        // 返回分页数据
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(200);
        rspData.setRows(pageInfo.getList());
        rspData.setTotal(pageInfo.getTotal());
        return rspData;
    }

    /**
     * 根据预警结果ID获取预警结果处理记录
     */
    @GetMapping("/getHandleLogList")
    @ApiOperation(value = "获取预警结果处理记录", notes = "根据预警结果ID获取预警结果处理记录（分页）")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNum", value = "页码", required = true, paramType = "query", dataType = "Integer"),
        @ApiImplicitParam(name = "pageSize", value = "页大小", required = true, paramType = "query", dataType = "Integer"),
        @ApiImplicitParam(name = "warnResultId", value = "预警结果ID", required = true, paramType = "query", dataType = "String")
    })
    public TableDataInfo getHandleLogList(
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize,
            @RequestParam("warnResultId") String warnResultId) {

        // 验证参数
        if (StringUtils.isEmpty(warnResultId)) {
            return getDataTable(new ArrayList<>());
        }

        // 查询预警结果是否存在
        WarnResult warnResult = warnResultService.selectByPrimaryKey(warnResultId);
        if (warnResult == null) {
            return getDataTable(new ArrayList<>());
        }

        // 分页查询
        startPage();
        List<WarnResultHandleLog> list = warnResultHandleLogService.getHandleLogListByWarnResultId(warnResultId);
        List<WarnResultHandleLogDto> resultHandleLogDtos = BeanHelper.copyWithCollection(list, WarnResultHandleLogDto.class);
        return getDataTable(resultHandleLogDtos);
    }

    /**
     * 导出查询的预警管理列表
     */
    @PostMapping("/exportWarnRuleAndResultList")
    @ApiOperation(value = "导出预警管理列表", notes = "根据条件导出预警规则及结果列表")
    @Log(title = "导出预警管理列表", businessType = BusinessType.EXPORT)
    public void exportWarnRuleAndResultList(
            @RequestBody WarnRuleAndResultQueryDto queryDto,HttpServletResponse response) {

        // 查询数据
        List<WarnRuleAndResult> list = warnRuleService.getWarnRuleAndResultList(queryDto);
        log.info("导出前查询到的数据为："+ JSONObject.toJSONString(list));
        // 转换为导出VO
        List<WarnRuleAndResultExportVo> exportList = BeanHelper.copyWithCollection(list, WarnRuleAndResultExportVo.class);
        log.info("导出前查询到的excel数据为："+ JSONObject.toJSONString(exportList));
        // 导出Excel
        ExcelUtil<WarnRuleAndResultExportVo> util = new ExcelUtil<>(WarnRuleAndResultExportVo.class);
        try {
            util.exportExcel(response, exportList, "预警管理列表");
        } catch (IOException e) {
            log.error("导出失败：{}", e.getMessage());
        }
    }
}
