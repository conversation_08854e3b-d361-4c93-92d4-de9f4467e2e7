package com.onecity.os.management.zhibiaowarning.controller.app;

import com.github.pagehelper.PageInfo;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.management.zhibiaowarning.entity.vo.WarnInfoDetail;
import com.onecity.os.system.api.domain.SysUser;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.onecity.os.management.zhibiaowarning.entity.vo.WarnResultVo;
import com.onecity.os.management.zhibiaowarning.entity.vo.ResultStatus;
import com.onecity.os.management.zhibiaowarning.service.WarnResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import com.onecity.os.management.zhibiaowarning.entity.WarnResultHandleLog;
import com.onecity.os.management.zhibiaowarning.entity.dto.WarnResultHandleLogDto;
import com.onecity.os.management.zhibiaowarning.service.WarnResultHandleLogService;
import java.util.Date;

/**
 * App预警控制器
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@RestController
@RequestMapping("/app/zhibiaowarning")
@Api(tags = "App预警管理接口")
public class AppWarnController extends BaseController {

    @Autowired
    private WarnResultService warnResultService;

    /**
     * 获取当前用户收到的预警列表
     */
    @GetMapping("/getWarnResultListByUser")
    @ApiOperation(value = "获取当前用户收到的预警列表", notes = "分页查询当前用户收到的预警列表，支持按规则名称或指标名称模糊查询")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "name", value = "规则名称或指标名称", required = false, paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "pageNum", value = "页码", required = true, paramType = "query", dataType = "Integer"),
        @ApiImplicitParam(name = "pageSize", value = "页大小", required = true, paramType = "query", dataType = "Integer")
    })
    public TableDataInfo getWarnResultListByUser(
            @RequestParam(value = "name", required = false) String name,
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize) {
        startPage();
        // 获取当前登录用户ID
        LoginUser sysUser = SecurityUtils.getLoginUser();
        if (ObjectUtils.isEmpty(sysUser)) {
            return getDataTable(new ArrayList<WarnResultVo>());
        }
        List<String> roleIds = Arrays.stream(sysUser.getSysUser().getRoleIds())
                .map(String::valueOf)
                .collect(Collectors.toList());
        // 调用服务查询预警列表
        List<WarnResultVo> pageInfo = warnResultService.getWarnResultListByUser(sysUser.getSysUser().getUserId().toString(),
                name, roleIds);
        
        return getDataTable(pageInfo);
    }

    /**
     * 根据预警结果ID查询指标预警详情及预警历史
     */
    @GetMapping("/getWarnInfoDetailByResultId")
    @ApiOperation(value = "获取预警详情及历史", notes = "根据预警结果ID查询指标预警详情及预警历史")
    @ApiImplicitParam(name = "warnResultId", value = "预警结果ID", required = true, paramType = "query", dataType = "String")
    public BaseResult getWarnInfoDetailByResultId(@RequestParam("warnResultId") String warnResultId) {
        if (StringUtils.isEmpty(warnResultId)) {
            return BaseResult.fail("预警结果ID不能为空");
        }
        
        WarnInfoDetail warnInfoDetail = warnResultService.getWarnInfoDetailByResultId(warnResultId);
        if (warnInfoDetail == null) {
            return BaseResult.fail("未找到相关预警信息");
        }
        
        return BaseResult.ok(warnInfoDetail);
    }

    /**
     * 根据预警结果ID设置预警结果状态
     */
    @PostMapping("/setWarnResultStatusByResultId")
    @ApiOperation(value = "设置预警结果状态", notes = "根据预警结果ID设置预警结果状态")
    public BaseResult setWarnResultStatusByResultId(@Validated @RequestBody ResultStatus resultStatus) {
        // 获取当前登录用户ID
        Long userId = SecurityUtils.getLoginUser().getUserid();
        if (userId == null) {
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        
        // 调用服务更新预警结果状态
        int result = warnResultService.updateWarnResultStatus(
                resultStatus.getWarnResultId(), 
                resultStatus.getStatus(), 
                userId,resultStatus.getHandleRemark());
        
        if (result > 0) {
            return BaseResult.ok("操作成功");
        } else {
            return BaseResult.fail("操作失败，请检查预警结果ID是否存在");
        }
    }
}
