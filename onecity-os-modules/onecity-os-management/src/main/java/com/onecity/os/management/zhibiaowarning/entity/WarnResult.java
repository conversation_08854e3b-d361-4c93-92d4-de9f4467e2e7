package com.onecity.os.management.zhibiaowarning.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 预警结果实体类
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
@Table(name = "warn_result")
public class WarnResult {
    
    /**
     * 预警结果id
     */
    @Id
    @Column(name = "warn_result_id")
    private String warnResultId;
    
    /**
     * 板块编码
     */
    @Column(name = "source_id")
    private String sourceId;
    
    /**
     * 预警规则id
     */
    @Column(name = "warn_rule_id")
    private String warnRuleId;
    
    /**
     * 指标id
     */
    @Column(name = "indicator_id")
    private String indicatorId;
    
    /**
     * 指标名称
     */
    @Column(name = "indicator_name")
    private String indicatorName;
    
    /**
     * 指标展示方式
     */
    @Column(name = "indicator_exhibit_type")
    private String indicatorExhibitType;
    
    /**
     * 预警时指标数据JSON字符串存储
     */
    @Column(name = "indicator_data")
    private String indicatorData;
    
    /**
     * 预警结果状态
     */
    @Column(name = "result_status")
    private Integer resultStatus;
    
    /**
     * 比较目标值
     */
    @Column(name = "compare_value")
    private String compareValue;
    
    /**
     * 触发预警时的实际值
     */
    @Column(name = "real_value")
    private String realValue;
    
    /**
     * 触发预警的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time")
    private Date createTime;
    
    /**
     * 触发预警的原因拼接成一句话，多个条件用；分割
     * 例如：数据项为item_name时，item_value的固定值大于3
     */
    @Column(name = "warn_conut_rule_sub")
    private String warnConutRuleSub;

    @Column(name = "remind_user_ids")
    private String remindUserIds;

}