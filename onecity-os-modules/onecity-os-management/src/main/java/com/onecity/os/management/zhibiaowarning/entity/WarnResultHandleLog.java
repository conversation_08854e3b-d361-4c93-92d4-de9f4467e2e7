package com.onecity.os.management.zhibiaowarning.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 预警结果处理记录实体类
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
@Table(name = "warn_result_handle_log")
public class WarnResultHandleLog {
    
    /**
     * 预警结果处理记录id
     */
    @Id
    @Column(name = "handle_id")
    private String handleId;
    
    /**
     * 预警结果id
     */
    @Column(name = "warn_result_id")
    private String warnResultId;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String creater;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time")
    private Date createTime;
    
    /**
     * 处理内容
     */
    @Column(name = "handle_content")
    private String handleContent;
    
    /**
     * 处理结果
     */
    @Column(name = "handle_result")
    private String handleResult;

    /**
     * 处理备注
     */
    @Column(name = "handle_remark")
    private String handleRemark;

    /**
     * 处理人id
     */
    @Column(name = "handle_user_id")
    private Long handleUserId;
}