package com.onecity.os.management.zhibiaowarning.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 预警规则实体类
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
@Table(name = "warn_rule")
public class WarnRule {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "warn_rule_id")
    private String warnRuleId;

    /**
     * 板块编码
     */
    @Column(name = "source_id")
    private String sourceId;

    /**
     * 指标ID，显示出层级结构
     */
    @Column(name = "indicator_id")
    private String indicatorId;

    /**
     * 数据更新方式：1-手动填报，2-数据对接
     */
    @Column(name = "data_update_mode")
    private Integer dataUpdateMode;

    /**
     * 指标名称
     */
    @Column(name = "indicator_name")
    private String indicatorName;

    /**
     * 指标更新周期
     */
    @Column(name = "update_cycle")
    private String updateCycle;

    /**
     * 当前指标预警统计过的最新手动填报的数据期
     */
    @Column(name = "last_update_date")
    private String lastUpdateDate;

    /**
     * 规则名称
     */
    @Column(name = "rule_name")
    private String ruleName;

    /**
     * 预警级别：1-预警，2-告警
     */
    @Column(name = "warn_level")
    private Integer warnLevel;

    /**
     * 状态：0-停用，1-启用
     */
    @Column(name = "warn_status")
    private Integer warnStatus;

    /**
     * 通知人IDs
     */
    @Column(name = "remind_user_ids")
    private String remindUserIds;

    /**
     * 通知人角色ID
     */
    @Column(name = "renmind_role_id")
    private String remindRoleId;

    /**
     * 通知人类型：1-用户，2-角色
     */
    @Column(name = "remind_type")
    private Integer remindType;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    private String creater;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 预警周期/监测频率
     */
    @Column(name = "warn_interval")
    private String warnInterval;

    /**
     * 预警计算规则：1-满足全部条件，2-满足任意条件，3-自定义
     */
    @Column(name = "warn_count_rule")
    private String warnCountRule;

    /**
     * 自定义计算规则表达式
     */
    @Column(name = "udf_rule")
    private String udfRule;
}