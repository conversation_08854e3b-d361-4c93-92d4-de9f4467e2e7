package com.onecity.os.management.zhibiaowarning.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 预警规则详情实体类
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
@Table(name = "warn_rule_detail")
public class WarnRuleDetail implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 详情ID
     */
    @Id
    @Column(name = "detail_id")
    private String detailId;
    
    /**
     * 预警规则ID
     */
    @Column(name = "warn_rule_id")
    private String warnRuleId;
    
    /**
     * 详情名称
     */
    @Column(name = "detail_name")
    private String detailName;
    
    /**
     * 数据项，为空时，预警数据为item_value一列的数据
     */
    @Column(name = "item_name")
    private String itemName;
    
    /**
     * 数据值
     */
    @Column(name = "item_value")
    private String itemValue;
    
    /**
     * 1-为对比某一个值，2-为对比某一列值
     */
    @Column(name = "compare_type")
    private Integer compareType;
    
    /**
     * 1-固定值对比；2-环比；3-同比
     */
    @Column(name = "rule_type")
    private Integer ruleType;
    
    /**
     * 比较方式
     */
    @Column(name = "compare_rule")
    private String compareRule;
    
    /**
     * 比较目标值
     */
    @Column(name = "compare_value")
    private String compareValue;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String creater;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time")
    private Date createTime;
}