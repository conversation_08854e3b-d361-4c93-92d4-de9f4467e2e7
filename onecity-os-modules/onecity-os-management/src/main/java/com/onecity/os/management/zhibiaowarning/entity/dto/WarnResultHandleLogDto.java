package com.onecity.os.management.zhibiaowarning.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 预警结果处理记录DTO
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
@ApiModel(value = "预警结果处理记录DTO")
public class WarnResultHandleLogDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 预警结果ID
     */
    @NotBlank(message = "预警结果ID不能为空")
    @ApiModelProperty(value = "预警结果ID", required = true)
    private String warnResultId;

    /**
     * 处理内容
     */
    @NotBlank(message = "处理内容不能为空")
    @ApiModelProperty(value = "处理内容", required = true)
    private String handleContent;

    /**
     * 处理结果
     */
    @ApiModelProperty(value = "处理结果")
    private String handleResult;

    /**
     * 处理备注
     */
    @ApiModelProperty(value = "处理备注")
    private String handleRemark;

    /**
     * 创建人
     */
    private String creater;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time")
    private Date createTime;
}