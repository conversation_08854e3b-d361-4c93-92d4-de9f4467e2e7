package com.onecity.os.management.zhibiaowarning.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 预警规则及结果查询条件
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
@ApiModel(value = "预警规则及结果查询条件")
public class WarnRuleAndResultQueryDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 版块名称
     */
    @ApiModelProperty(value = "版块名称")
    private String sourceName;

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称")
    private String indicatorName;

    /**
     * 预警规则名称
     */
    @ApiModelProperty(value = "预警规则名称")
    private String warnRuleName;

    /**
     * 触发预警查询开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "触发预警查询开始时间")
    private Date resultBeginTime;

    /**
     * 触发预警查询结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "触发预警查询结束时间")
    private Date resultEndTime;
}