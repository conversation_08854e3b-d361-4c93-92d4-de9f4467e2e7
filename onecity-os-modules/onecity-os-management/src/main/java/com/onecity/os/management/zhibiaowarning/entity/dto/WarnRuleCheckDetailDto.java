package com.onecity.os.management.zhibiaowarning.entity.dto;

import com.onecity.os.management.zhibiaowarning.entity.WarnRuleDetail;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/26
 * @ClassName: WarnRuleCheckDetailDto
 * @Description:
 * @Version 1.0
 */
@Data
@Builder
public class WarnRuleCheckDetailDto {
    /**
     * 指标id
     */
    String indicatorId;
    /**
     * 数据源id
     */
    String sourceId;
    /**
     * 预警规则id
     */
    String warnRuleId;
    /**
     * 命中预警详情
     */
    private List<WarnRuleDetail> details = new ArrayList<>();
    /**
     * 记录命中的详情数值
     */
    Map<String, String> valueMap = new HashMap<>();
    /**
     * 记录指标值名称
     */
    Map<String, String> valueNameMap = new HashMap<>();
}
