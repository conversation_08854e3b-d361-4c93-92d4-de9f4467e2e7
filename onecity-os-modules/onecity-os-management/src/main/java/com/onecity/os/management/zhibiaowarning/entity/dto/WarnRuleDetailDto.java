package com.onecity.os.management.zhibiaowarning.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 预警规则详情DTO
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
@ApiModel(value = "预警规则详情DTO")
public class WarnRuleDetailDto implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 规则详情名称
     */
    @ApiModelProperty(value = "规则详情名称")
    private String detailName;

    /**
     * 数据项，为空时，预警数据为item_value一列的数据
     */
    @ApiModelProperty(value = "数据项，为空时，预警数据为item_value一列的数据")
    private String itemName;
    
    /**
     * 数据值
     */
    @ApiModelProperty(value = "数据值")
    private String itemValue;
    
    /**
     * 1-为对比某一个值，2-为对比某一列值
     */
    @ApiModelProperty(value = "1-为对比某一个值，2-为对比某一列值")
    private Integer compareType;
    
    /**
     * 1-固定值对比；2-环比；3-同比
     */
    @ApiModelProperty(value = "1-固定值对比；2-环比；3-同比")
    private Integer ruleType;
    
    /**
     * 比较方式
     */
    @ApiModelProperty(value = "比较方式")
    private String compareRule;
    
    /**
     * 比较目标值
     */
    @ApiModelProperty(value = "比较目标值")
    private String compareValue;
}