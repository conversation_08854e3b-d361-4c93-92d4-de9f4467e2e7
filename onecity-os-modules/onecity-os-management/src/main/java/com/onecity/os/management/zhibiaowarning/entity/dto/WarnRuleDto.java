package com.onecity.os.management.zhibiaowarning.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 预警规则DTO
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
@ApiModel(value = "预警规则DTO")
public class WarnRuleDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 板块编码
     */
    @NotBlank(message = "板块编码不能为空")
    @ApiModelProperty(value = "板块编码", required = true)
    private String sourceId;
    
    /**
     * 指标ID，显示出层级结构
     */
    @NotBlank(message = "指标ID不能为空")
    @ApiModelProperty(value = "指标ID，显示出层级结构", required = true)
    private String indicatorId;
    
    /**
     * 指标名称显示层级结构
     */
    @NotBlank(message = "指标名称不能为空")
    @ApiModelProperty(value = "指标名称显示层级结构", required = true)
    private String indicatorName;
    
    /**
     * 数据更新方式1-手动填报2数据对接
     */
    @NotNull(message = "数据更新方式不能为空")
    @ApiModelProperty(value = "数据更新方式1-手动填报2数据对接", required = true)
    private Integer dataUpdateMode;
    
    /**
     * 规则名称
     */
    @NotBlank(message = "规则名称不能为空")
    @ApiModelProperty(value = "规则名称", required = true)
    private String ruleName;
    
    /**
     * 预警级别
     */
    @NotNull(message = "预警级别不能为空")
    @ApiModelProperty(value = "预警级别", required = true)
    private Integer warnLevel;
    
    /**
     * 状态0-停用，1-启用
     */
    @ApiModelProperty(value = "状态0-停用，1-启用")
    private Integer warnStatus;
    
    /**
     * 通知人ids
     */
    @ApiModelProperty(value = "通知人ids")
    private String remindUserIds;
    
    /**
     * 通知人角色id
     */
    @ApiModelProperty(value = "通知人角色id")
    private String remindRoleId;
    
    /**
     * 通知人类型1-用户，2-角色
     */
    @ApiModelProperty(value = "通知人类型1-用户，2-角色")
    private Integer remindType;
    
    /**
     * 预警周期/监测频率
     */
    @ApiModelProperty(value = "预警周期/监测频率")
    private String warnInterval;
    
    /**
     * 预警计算规则1-满足全部条件，2-满足任意条件，3-自定义
     */
    @NotBlank(message = "预警计算规则不能为空")
    @ApiModelProperty(value = "预警计算规则1-满足全部条件，2-满足任意条件，3-自定义", required = true)
    private String warnCountRule;
    
    /**
     * 自定义计算规则表达式
     */
    @ApiModelProperty(value = "自定义计算规则表达式")
    private String udfRule;
    
    /**
     * 预警规则详情列表
     */
    @ApiModelProperty(value = "预警规则详情列表")
    private List<WarnRuleDetailDto> warnRuleDetailList;
}