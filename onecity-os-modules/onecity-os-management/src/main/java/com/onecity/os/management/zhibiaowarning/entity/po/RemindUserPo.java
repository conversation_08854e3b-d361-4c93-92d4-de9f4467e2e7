package com.onecity.os.management.zhibiaowarning.entity.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 预警通知用户PO
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
@ApiModel(value = "预警通知用户PO")
public class RemindUserPo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 用户真实姓名
     */
    @ApiModelProperty(value = "用户真实姓名")
    private String nickName;

    /**
     * 用户账号
     */
    @ApiModelProperty(value = "用户账号")
    private String userName;

    /**
     * 处理结果
     */
    @ApiModelProperty(value = "处理结果")
    private String handleResult;

    /**
     * 处理时间
     */
    @ApiModelProperty(value = "处理时间")
    private Date handleTime;

}