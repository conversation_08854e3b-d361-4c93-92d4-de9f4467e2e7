package com.onecity.os.management.zhibiaowarning.entity.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.onecity.os.system.api.domain.SysRole;
import com.onecity.os.system.api.domain.SysUser;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 预警规则信息PO
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
public class WarnInfoPo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 预警规则ID
     */
    private String warnRuleId;
    
    /**
     * 预警规则名称
     */
    private String warnRuleName;
    
    /**
     * 预警级别：1-预警，2-告警
     */
    private Integer warnLevel;
    
    /**
     * 状态：0-停用，1-启用
     */
    private Integer warnStatus;

    private String indicatorId;

    private String remindUsers;

    private String remindUserIds;

    private String remindRoleId;

    private String remindRoles;
    private Integer remindType;

    private List<SysRole> sysRoleList;
    /**
     * 通知人
     */
    private List<SysUser> sysUserList;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}