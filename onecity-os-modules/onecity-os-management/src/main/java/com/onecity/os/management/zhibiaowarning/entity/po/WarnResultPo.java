package com.onecity.os.management.zhibiaowarning.entity.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 预警结果历史PO
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
@ApiModel(value = "预警结果历史PO")
public class WarnResultPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 触发预警的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "触发预警的时间")
    private Date createTime;

    /**
     * 触发预警原因
     */
    @ApiModelProperty(value = "触发预警原因")
    private String warnCountRuleSubs;
}