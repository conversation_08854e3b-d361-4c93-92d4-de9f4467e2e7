package com.onecity.os.management.zhibiaowarning.entity.vo;

import lombok.Data;
import java.io.Serializable;

/**
 * 指标预警信息VO
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
public class IndicatorWarn implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 指标ID
     */
    private String indicatorId;
    
    /**
     * 指标名称
     */
    private String indicatorName;
    
    /**
     * 数据更新方式：1-手动填报，2-数据对接
     */
    private Integer updateMode;
    
    /**
     * 数据更新周期
     */
    private String updateCycle;
    
    /**
     * 启用预警规则数量
     */
    private Integer startWarnRuleNum;
}