package com.onecity.os.management.zhibiaowarning.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 预警结果状态更新请求参数
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
@ApiModel(value = "预警结果状态更新请求参数")
public class ResultStatus implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 预警结果ID
     */
    @NotBlank(message = "预警结果ID不能为空")
    @ApiModelProperty(value = "预警结果ID", required = true)
    private String warnResultId;
    
    /**
     * 状态：0-未处理，1-已处理，2-已忽略
     */
    @NotNull(message = "状态不能为空")
    @ApiModelProperty(value = "状态：1-待处理，2-处理中，3-已处理，4-已终止", required = true)
    private Integer status;

    /**
     * 处理备注
     */
    @ApiModelProperty(value = "处理备注")
    private String handleRemark;
}