package com.onecity.os.management.zhibiaowarning.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "TopSource", description = "预警来源统计对象")
public class TopSource {

    @ApiModelProperty(value = "某个版块总的预警结果数")
    private Integer resultCount;

    @ApiModelProperty(value = "版块名称")
    private String sourceName;

    public Integer getResultCount() {
        return resultCount;
    }

    public void setResultCount(Integer resultCount) {
        this.resultCount = resultCount;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    @Override
    public String toString() {
        return "TopSource{" +
                "resultCount=" + resultCount +
                ", sourceName='" + sourceName + '\'' +
                '}';
    }
}