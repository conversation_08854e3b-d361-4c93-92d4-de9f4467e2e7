package com.onecity.os.management.zhibiaowarning.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.onecity.os.management.zhibiaowarning.entity.WarnResultHandleLog;
import com.onecity.os.management.zhibiaowarning.entity.po.WarnResultPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 预警详情VO
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
@ApiModel(value = "预警详情VO")
public class WarnInfoDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 预警规则ID
     */
    @ApiModelProperty(value = "预警规则ID")
    private String warnRuleId;

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称")
    private String indicatorName;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    /**
     * 板块编码
     */
    @ApiModelProperty(value = "板块编码")
    private String sourceId;

    /**
     * 板块名称
     */
    @ApiModelProperty(value = "板块名称")
    private String sourceName;

    /**
     * 触发预警的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "触发预警的时间")
    private Date createTime;

    /**
     * 预警级别
     */
    @ApiModelProperty(value = "预警级别")
    private Integer warnLevel;

    /**
     * 触发预警原因
     */
    @ApiModelProperty(value = "触发预警原因")
    private String warnCountRuleSubs;

    /**
     * 比较目标值
     */
    @ApiModelProperty(value = "比较目标值")
    private String compareValue;

    /**
     * 触发预警时的实际值
     */
    @ApiModelProperty(value = "触发预警时的实际值")
    private String realValue;

    /**
     * 预警时指标数据JSON字符串存储
     */
    @ApiModelProperty(value = "预警时指标数据JSON字符串存储")
    private String indicatorData;

    /**
     * 指标展示方式
     */
    @ApiModelProperty(value = "指标展示方式")
    private String indicatorExhibitType;

    /**
     * 预警结果状态
     */
    @ApiModelProperty(value = "预警结果状态")
    private Integer resultStatus;

    /**
     * 预警结果处理记录列表
     */
    @ApiModelProperty(value = "预警结果处理记录列表")
    private List<WarnResultHandleLog> warnResultHandleLogList;

    /**
     * 预警历史列表
     */
    @ApiModelProperty(value = "预警历史列表")
    private List<WarnResultPo> warnResultHistoryList;
}