package com.onecity.os.management.zhibiaowarning.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "WarnResultCountVo", description = "预警统计结果对象")
public class WarnResultCountVo {

    @ApiModelProperty(value = "总预警数")
    private Long resultTotal;

    @ApiModelProperty(value = "待处理预警数")
    private Long toDoTotal;

    @ApiModelProperty(value = "处理中预警数")
    private Long doingTotal;

    @ApiModelProperty(value = "已处理预警数")
    private Long doneTotal;

    @ApiModelProperty(value = "已终止预警数")
    private Long endTotal;

    @ApiModelProperty(value = "预警来源排行列表")
    private List<TopSource> topSourceList;

    @Override
    public String toString() {
        return "WarnResultCountVo{" +
                "resultTotal=" + resultTotal +
                ", toDoTotal=" + toDoTotal +
                ", doingTotal=" + doingTotal +
                ", doneTotal=" + doneTotal +
                ", endTotal=" + endTotal +
                ", topSourceList=" + topSourceList +
                '}';
    }
}