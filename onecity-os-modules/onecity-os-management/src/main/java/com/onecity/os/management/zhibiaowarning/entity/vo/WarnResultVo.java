package com.onecity.os.management.zhibiaowarning.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 预警结果VO
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
@ApiModel(value = "预警结果VO")
public class WarnResultVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 预警结果ID
     */
    @ApiModelProperty(value = "预警结果ID")
    private String warnResultId;

    /**
     * 预警规则名称
     */
    @ApiModelProperty(value = "预警规则名称")
    private String warnRuleName;

    /**
     * 板块编码
     */
    @ApiModelProperty(value = "板块编码")
    private String sourceId;

    /**
     * 预警规则ID
     */
    @ApiModelProperty(value = "预警规则ID")
    private String warnRuleId;

    /**
     * 指标ID
     */
    @ApiModelProperty(value = "指标ID")
    private String indicatorId;

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称")
    private String indicatorName;

    /**
     * 预警结果状态
     */
    @ApiModelProperty(value = "预警结果状态")
    private Integer resultStatus;

    /**
     * 触发预警原因
     */
    @ApiModelProperty(value = "触发预警原因")
    private String warnCountRuleSubs;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 预警级别
     */
    @ApiModelProperty(value = "预警级别")
    private Integer warnLevel;
}