package com.onecity.os.management.zhibiaowarning.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 预警规则及结果VO
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
@ApiModel(value = "预警规则及结果VO")
public class WarnRuleAndResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 板块编码
     */
    @ApiModelProperty(value = "板块编码")
    private String sourceId;

    /**
     * 板块名称
     */
    @ApiModelProperty(value = "板块名称")
    private String sourceName;

    /**
     * 预警规则ID
     */
    @ApiModelProperty(value = "预警规则ID")
    private String warnRuleId;

    /**
     * 指标ID
     */
    @ApiModelProperty(value = "指标ID")
    private String indicatorId;

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称")
    private String indicatorName;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    /**
     * 预警级别：1-预警，2-告警
     */
    @ApiModelProperty(value = "预警级别：1-预警，2-告警")
    private Integer warnLevel;

    /**
     * 预警状态：0-停用，1-启用
     */
    @ApiModelProperty(value = "预警状态：0-停用，1-启用")
    private Integer warnStatus;

    /**
     * 触发预警原因
     */
    @ApiModelProperty(value = "触发预警原因")
    private String warnCountRuleSubs;

    /**
     * 触发预警的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "触发预警的时间")
    private Date createTime;

    /**
     * 预警结果ID
     */
    @ApiModelProperty(value = "预警结果ID")
    private String warnResultId;

    /**
     * 预警结果状态：0-未处理，1-已处理，2-已忽略
     */
    @ApiModelProperty(value = "预警结果状态：0-未处理，1-已处理，2-已忽略")
    private Integer resultStatus;
}