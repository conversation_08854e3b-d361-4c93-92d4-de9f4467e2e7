package com.onecity.os.management.zhibiaowarning.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.onecity.os.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 预警规则及结果导出VO
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
public class WarnRuleAndResultExportVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 板块编码
     */
    private String sourceId;

    /**
     * 板块名称
     */
    @Excel(name = "所属版块")
    private String sourceName;

    /**
     * 预警规则ID
     */
    private String warnRuleId;

    /**
     * 指标ID
     */
    private String indicatorId;

    /**
     * 指标名称
     */
    @Excel(name = "监测对象")
    private String indicatorName;

    /**
     * 规则名称
     */
    @Excel(name = "预警规则名称")
    private String ruleName;

    /**
     * 预警级别：1-预警，2-告警
     */
    @Excel(name = "预警级别", readConverterExp = "1=预警,2=告警", type = Excel.Type.EXPORT)
    private Integer warnLevel;


    /**
     * 预警状态：0-停用，1-启用
     */
    private Integer warnStatus;

    /**
     * 触发预警原因
     */
    @Excel(name = "预警原因")
    private String warnCountRuleSubs;

    /**
     * 触发预警的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "触发时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 预警结果ID
     */
    private String warnResultId;

    /**
     * 预警结果状态：0-未处理，1-已处理，2-已忽略
     */
    @Excel(name = "状态", readConverterExp = "1=待处理,2=处理中,3=已处理,4=已终止", type = Excel.Type.EXPORT)
    private Integer resultStatus;
}