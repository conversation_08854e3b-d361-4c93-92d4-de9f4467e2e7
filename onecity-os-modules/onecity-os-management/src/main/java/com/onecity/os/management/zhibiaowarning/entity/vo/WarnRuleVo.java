package com.onecity.os.management.zhibiaowarning.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 预警规则请求参数
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
@ApiModel(value = "预警规则请求参数")
public class WarnRuleVo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 预警规则ID
     */
    @NotBlank(message = "预警规则ID不能为空")
    @ApiModelProperty(value = "预警规则ID", required = true)
    private String warnRuleId;
    
    /**
     * 指标ID
     */
    @NotBlank(message = "指标ID不能为空")
    @ApiModelProperty(value = "指标ID", required = true)
    private String indicatorId;
}