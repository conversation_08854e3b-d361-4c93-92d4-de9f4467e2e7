package com.onecity.os.management.zhibiaowarning.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.onecity.os.system.api.domain.SysRole;
import com.onecity.os.system.api.domain.SysUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 预警规则VO
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
@ApiModel(value = "预警规则VO")
public class WarnRuleVo1 implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 预警规则ID
     */
    @ApiModelProperty(value = "预警规则ID")
    private String warnRuleId;

    /**
     * 板块编码
     */
    @ApiModelProperty(value = "板块编码")
    private String sourceId;

    /**
     * 指标ID，显示出层级结构
     */
    @ApiModelProperty(value = "指标ID，显示出层级结构")
    private String indicatorId;

    /**
     * 指标名称显示层级结构
     */
    @ApiModelProperty(value = "指标名称显示层级结构")
    private String indicatorName;

    /**
     * 数据更新方式1-手动填报2数据对接
     */
    @ApiModelProperty(value = "数据更新方式1-手动填报2数据对接")
    private Integer dataUpdateMode;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    /**
     * 预警级别
     */
    @ApiModelProperty(value = "预警级别")
    private Integer warnLevel;

    /**
     * 状态0-停用，1-启用
     */
    @ApiModelProperty(value = "状态0-停用，1-启用")
    private Integer warnStatus;

    /**
     * 通知人ids
     */
    @ApiModelProperty(value = "通知人ids")
    private String remindUserIds;

    /**
     * 通知人角色id
     */
    @ApiModelProperty(value = "通知人角色id")
    private String remindRoleId;
    @ApiModelProperty(value = "通知人")
    private String remindUsers;
    @ApiModelProperty(value = "通知人角色")
    private String remindRoles;

    private List<SysRole> sysRoleList;
    /**
     * 通知人
     */
    private List<SysUser> sysUserList;
    /**
     * 通知人类型1-用户，2-角色
     */
    @ApiModelProperty(value = "通知人类型1-用户，2-角色")
    private Integer remindType;

    /**
     * 预警周期/监测频率
     */
    @ApiModelProperty(value = "预警周期/监测频率")
    private String warnInterval;

    /**
     * 预警计算规则1-满足全部条件，2-满足任意条件，3-自定义
     */
    @ApiModelProperty(value = "预警计算规则1-满足全部条件，2-满足任意条件，3-自定义")
    private String warnCountRule;

    /**
     * 自定义计算规则表达式
     */
    @ApiModelProperty(value = "自定义计算规则表达式")
    private String udfRule;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updater;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creater;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 预警规则详情列表
     */
    @ApiModelProperty(value = "预警规则详情列表")
    private List<WarnRuleDetailVo> detailList;
}
