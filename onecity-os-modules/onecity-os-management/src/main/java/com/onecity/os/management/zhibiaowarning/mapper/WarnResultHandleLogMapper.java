package com.onecity.os.management.zhibiaowarning.mapper;

import com.onecity.os.management.zhibiaowarning.entity.WarnResultHandleLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预警结果处理记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2023/10/25
 */
@Mapper
public interface WarnResultHandleLogMapper {
    
    /**
     * 根据主键查询预警结果处理记录
     * 
     * @param handleId 预警结果处理记录id
     * @return 预警结果处理记录
     */
    WarnResultHandleLog selectByPrimaryKey(String handleId);
    
    /**
     * 根据预警结果ID查询预警结果处理记录列表
     * 
     * @param warnResultId 预警结果id
     * @return 预警结果处理记录列表
     */
    List<WarnResultHandleLog> selectByWarnResultId(String warnResultId);

    /**
     * 根据预警结果ID查询预警结果处理记录是督办的列表
     *
     * @param warnResultId 预警结果id
     * @return 预警结果处理记录列表
     */
    List<WarnResultHandleLog> selectSuperviseByWarnResultId(String warnResultId);


    /**
     * 新增预警结果处理记录
     * 
     * @param warnResultHandleLog 预警结果处理记录
     * @return 影响行数
     */
    int insert(WarnResultHandleLog warnResultHandleLog);
    
    /**
     * 修改预警结果处理记录
     * 
     * @param warnResultHandleLog 预警结果处理记录
     * @return 影响行数
     */
    int updateByPrimaryKey(WarnResultHandleLog warnResultHandleLog);
    
    /**
     * 删除预警结果处理记录
     * 
     * @param handleId 预警结果处理记录id
     * @return 影响行数
     */
    int deleteByPrimaryKey(String handleId);
    
    /**
     * 根据预警结果ID删除预警结果处理记录
     * 
     * @param warnResultId 预警结果id
     * @return 影响行数
     */
    int deleteByWarnResultId(String warnResultId);

    /**
     * 根据预警规则ID删除预警结果处理记录
     *
     * @param warnRuleId 预警规则id
     * @return 影响行数
     */
    int deleteByWarnRuleId(String warnRuleId);

    /**
     * 根据预警结果ID统计处理记录数量
     * 
     * @param warnResultId 预警结果id
     * @return 处理记录数量
     */
    int countByWarnResultId(String warnResultId);

    /**
     * 根据处理人ID查询处理记录列表
     *
     * @param handleUserId 处理人ID
     * @return 处理记录列表
     */
    List<WarnResultHandleLog> selectByHandleUserId(@Param("handleUserId") Long handleUserId);
}