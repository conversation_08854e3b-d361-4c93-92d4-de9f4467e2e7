package com.onecity.os.management.zhibiaowarning.mapper;

import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.management.zhibiaowarning.entity.WarnResult;
import com.onecity.os.management.zhibiaowarning.entity.po.RemindUserPo;
import com.onecity.os.management.zhibiaowarning.entity.po.WarnResultPo;
import com.onecity.os.management.zhibiaowarning.entity.po.WarnResultPo1;
import com.onecity.os.management.zhibiaowarning.entity.vo.CountStatusVo;
import com.onecity.os.management.zhibiaowarning.entity.vo.WarnResultVo;
import com.onecity.os.management.zhibiaowarning.entity.vo.TopSource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 预警结果Mapper接口
 * 
 * <AUTHOR>
 * @date 2023/10/25
 */
@Mapper
public interface WarnResultMapper {
    
    /**
     * 根据主键查询预警结果
     * 
     * @param warnResultId 预警结果id
     * @return 预警结果
     */
    WarnResult selectByPrimaryKey(String warnResultId);
    
    /**
     * 根据预警规则ID查询预警结果列表
     * 
     * @param warnRuleId 预警规则id
     * @return 预警结果列表
     */
    List<WarnResult> selectByWarnRuleId(String warnRuleId);
    
    /**
     * 根据指标ID查询预警结果列表
     * 
     * @param indicatorId 指标id
     * @return 预警结果列表
     */
    List<WarnResult> selectByIndicatorId(String indicatorId);

    /**
     * 根据指标ID列表查询预警结果状态为未终止的结果列表
     *
     * @param indicatorIds 指标id
     * @return 预警结果列表
     */
    List<String> selectByIndicatorIds(@Param("indicatorIds") List<String> indicatorIds);

    /**
     * 根据规则ID列表查询预警结果状态为未终止的结果列表
     *
     * @param warnRuleId 指标id
     * @return 预警结果列表
     */
    List<String> checkByWarnRuleId(@Param("warnRuleId") String warnRuleId);

    /**
     * 新增预警结果
     * 
     * @param warnResult 预警结果
     * @return 影响行数
     */
    int insert(WarnResult warnResult);
    
    /**
     * 修改预警结果
     * 
     * @param warnResult 预警结果
     * @return 影响行数
     */
    int updateByPrimaryKey(WarnResult warnResult);
    
    /**
     * 删除预警结果
     * 
     * @param warnResultId 预警结果id
     * @return 影响行数
     */
    int deleteByPrimaryKey(String warnResultId);

    /**
     * 删除预警结果
     *
     * @param warnRuleId 预警结果id
     * @return 影响行数
     */
    int deleteByWarnRuleId(String warnRuleId);

    /**
     * 根据用户ID查询预警结果列表
     *
     * @param userId 用户ID
     * @param name 规则名称或指标名称
     * @return 预警结果列表
     */
    List<WarnResultVo> selectWarnResultListByUser(@Param("userId") String userId, @Param("name") String name,@Param("roleIds") List<String> roleIds);

    /**
     * 查询预警历史（当前预警结果之前的记录）
     *
     * @param warnRuleId 预警规则ID
     * @param warnResultId 当前预警结果ID
     * @param limit 限制条数
     * @return 预警历史列表
     */
    List<WarnResultPo> selectHistoryByWarnRuleId(@Param("warnRuleId") String warnRuleId,
                                                 @Param("warnResultId") String warnResultId,
                                                 @Param("limit") Integer limit);

    /**
     * 更新预警结果处理状态
     */
    int updateWarnResultStatus(@Param("wr") WarnResult wr);

    /**
     * 根据状态统计预警结果数量
     *
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 数量
     */
    List<CountStatusVo> countWarnResultByStatus(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 获取预警结果总数
     *
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 总数
     */
    long countTotalWarnResult(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 查询预警来源排行
     *
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @param limit 查询数量
     * @return 预警来源排行列表
     */
    List<TopSource> getTopSourceWarnResults(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime, @Param("limit") int limit);

    /**
     * 根据预警规则ID查询预警历史列表
     *
     * @param warnRuleId 预警规则ID
     * @return 预警历史列表
     */
    List<WarnResultPo1> getWarnResultListByWarnRuleId(@Param("warnRuleId") String warnRuleId);
    /**
     * 根据预警结果ID查询通知用户列表
     *
     * @param warnResultId 预警结果ID
     * @return 通知用户列表
     */
//    List<RemindUserPo> getRemindUserList(@Param("warnResultId") String warnResultId);
}
