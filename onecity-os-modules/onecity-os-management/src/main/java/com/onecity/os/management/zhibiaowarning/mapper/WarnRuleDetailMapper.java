package com.onecity.os.management.zhibiaowarning.mapper;

import com.onecity.os.management.zhibiaowarning.entity.WarnRuleDetail;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 预警规则详情Mapper接口
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
public interface WarnRuleDetailMapper extends Mapper<WarnRuleDetail> {

    /**
     * 批量新增预警规则详情
     *
     * @param warnRuleDetails 预警规则详情列表
     * @return 结果
     */
    int inserBatch(@Param("warnRuleDetails") List<WarnRuleDetail> warnRuleDetails);
    /**
     * 根据预警规则ID查询预警规则详情列表
     *
     * @param warnRuleId 预警规则ID
     * @return 预警规则详情列表
     */
    List<WarnRuleDetail> selectByWarnRuleId(@Param("warnRuleId") String warnRuleId);
    
    /**
     * 根据预警规则ID删除预警规则详情
     *
     * @param warnRuleId 预警规则ID
     * @return 结果
     */
    int deleteByWarnRuleId(@Param("warnRuleId") String warnRuleId);
}