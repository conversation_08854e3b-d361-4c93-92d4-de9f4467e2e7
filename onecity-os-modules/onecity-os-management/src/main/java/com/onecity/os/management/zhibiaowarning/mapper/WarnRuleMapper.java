package com.onecity.os.management.zhibiaowarning.mapper;

import com.onecity.os.common.core.dao.BaseMapper;
import com.onecity.os.management.zhibiaowarning.entity.WarnRule;
import com.onecity.os.management.zhibiaowarning.entity.dto.WarnRuleAndResultQueryDto;
import com.onecity.os.management.zhibiaowarning.entity.po.WarnInfoPo;
import com.onecity.os.management.zhibiaowarning.entity.vo.IndicatorWarn;
import com.onecity.os.management.zhibiaowarning.entity.vo.WarnRuleAndResult;
import com.onecity.os.management.zhibiaowarning.entity.vo.WarnRuleVo;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 预警规则Mapper接口
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
public interface WarnRuleMapper{
    WarnRule selectByPrimaryKey(@Param("warnRuleId") String warnRuleId);

    /**
     * 根据主键更新预警规则
     *
     * @param warnRule 预警规则
     */
    int updateByPrimaryKey(@Param("warnRule") WarnRule warnRule);

    /**
     * 根据预警规则名称查询预警规则
     *
     * @param warnRuleName 预警规则名称
     * @return 预警规则
     */
    List<WarnRule> selectByRuleName(@Param("ruleName") String warnRuleName);

    /**
     * 根据预警规则名称和规则id查询预警规则
     *
     * @param warnRuleName 预警规则名称
     * @return 预警规则
     */
    List<WarnRule> selectByRuleNameAndId(@Param("ruleName") String warnRuleName,@Param("warnRuleId") String warnRuleId);

    /**
     * 根据主键删除预警规则
     *
     * @param warnRuleId 预警规则ID
     * @return 结果
     */
    int deleteByPrimaryKey(@Param("warnRuleId") String warnRuleId);
    /**
     * 根据指标id获取其下面的子指标信息以及预警规则信息
     *
     * @param indicatorId 指标ID
     * @return 子指标及预警规则信息
     */
    List<IndicatorWarn> getWarnAndInfoByParentId(@Param("indicatorId") String indicatorId);

    /**
     * 新增预警规则
     *
     * @param warnRule 预警规则
     * @return 结果
     */
    int insertWarnRule(@Param("warnRule") WarnRule warnRule);
    /**
     * 根据指标id获取其配置的预警规则列表
     *
     * @param indicatorId 指标ID
     * @return 预警规则信息列表
     */
    List<WarnInfoPo> getWarnInfoListByIndicatorId(@Param("indicatorId") String indicatorId);

    /**
     * 根据预警规则ID获取其配置的预警规则列表
     * @param warnRuleId
     * @return
     */
    WarnInfoPo getWarnInfoListByWarnRuleId(@Param("warnRuleId") String warnRuleId);
    
    /**
     * 查询预警规则列表
     *
     * @param warnRule 预警规则
     * @return 预警规则集合
     */
    List<WarnRule> selectWarnRuleList(WarnRule warnRule);
    
    /**
     * 批量删除预警规则
     *
     * @param warnRuleIds 需要删除的预警规则ID数组
     * @return 结果
     */
    int deleteWarnRuleByIds(String[] warnRuleIds);
    
    /**
     * 更新预警规则状态
     *
     * @param warnRuleId 预警规则ID
     * @param warnStatus 状态
     * @return 结果
     */
    int updateStatus(@Param("warnRuleId") String warnRuleId, @Param("warnStatus") Integer warnStatus);

    /**
     * 根据预警规则ID、版块编码和指标ID查询预警规则
     *
     * @param warnRuleId 预警规则ID
     * @param sourceId 版块编码
     * @param indicatorId 指标ID
     * @return 预警规则
     */
    WarnRule selectWarnRuleByIdAndSourceId(@Param("warnRuleId") String warnRuleId, @Param("sourceId") String sourceId, @Param("indicatorId") String indicatorId);

    int updateByPrimaryKeySelective(WarnRule warnRule);

    /**
     * 获取全部的预警规则及相应的预警结果列表
     *
     * @param queryDto 查询条件
     * @return 预警规则及结果列表
     */
    List<WarnRuleAndResult> getWarnRuleAndResultList(WarnRuleAndResultQueryDto queryDto);

    /**
     * 根据预警结果ID查询预警规则
     *
     * @param warnResultId 预警结果ID
     * @return 预警规则
     */
    WarnRule getWarnRuleByResultId(@Param("warnResultId") String warnResultId);

    /**
     * 根据更新周期获取预警规则ID
     * @param warnInterval
     * @return
     */
    List<String> getWarnRuleIdsByWarnInterval(@Param("warnInterval") String warnInterval);
}