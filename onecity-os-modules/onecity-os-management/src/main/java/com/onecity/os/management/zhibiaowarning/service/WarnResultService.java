package com.onecity.os.management.zhibiaowarning.service;

import com.github.pagehelper.PageInfo;
import com.onecity.os.management.zhibiaowarning.entity.WarnResult;
import com.onecity.os.management.zhibiaowarning.entity.po.RemindUserPo;
import com.onecity.os.management.zhibiaowarning.entity.po.WarnResultPo1;
import com.onecity.os.management.zhibiaowarning.entity.vo.WarnInfoDetail;
import com.onecity.os.management.zhibiaowarning.entity.vo.WarnResultVo;
import com.onecity.os.management.zhibiaowarning.entity.vo.WarnResultCountVo;

import java.util.Date;
import java.util.List;

/**
 * 预警结果Service接口
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
public interface WarnResultService {
    
    /**
     * 根据用户ID获取预警结果列表
     *
     * @param userId 用户ID
     * @param name 规则名称或指标名称
     * @param roleIds 角色ID
     * @return 预警结果列表
     */
    List<WarnResultVo> getWarnResultListByUser(String userId, String name,List<String> roleIds);
    
    /**
     * 根据主键查询预警结果
     *
     * @param warnResultId 预警结果ID
     * @return 预警结果
     */
    WarnResult selectByPrimaryKey(String warnResultId);
    /**
     * 保存预警结果
     *
     * @param warnResult 预警结果ID
     * @return 预警结果
     */
    int saveWarnResult(WarnResult warnResult) ;

    /**
     * 根据预警规则ID查询预警结果列表
     *
     * @param warnRuleId 预警规则ID
     * @return 预警结果列表
     */
    List<WarnResult> selectByWarnRuleId(String warnRuleId);
    
    /**
     * 根据指标ID查询预警结果列表
     *
     * @param indicatorId 指标ID
     * @return 预警结果列表
     */
    List<WarnResult> selectByIndicatorId(String indicatorId);
    
    /**
     * 根据预警结果ID查询指标预警详情及预警历史
     *
     * @param warnResultId 预警结果ID
     * @return 预警详情及历史
     */
    WarnInfoDetail getWarnInfoDetailByResultId(String warnResultId);
    
    /**
     * 更新预警结果状态
     *
     * @param warnResultId 预警结果ID
     * @param status 状态：0-未处理，1-已处理，2-已忽略
     * @param userId 操作用户ID
     * @return 影响行数
     */
    int updateWarnResultStatus(String warnResultId, Integer status, Long userId,String handleRemark);

    /**
     * 统计预警结果
     *
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    WarnResultCountVo countWarnResult(Date beginTime, Date endTime);

    /**
     * 根据预警规则ID查询预警历史列表
     *
     * @param warnRuleId 预警规则ID
     * @return 预警历史列表
     */
    List<WarnResultPo1> getWarnResultListByWarnRuleId(String warnRuleId);

    /**
     * 根据预警结果ID查询通知用户列表
     *
     * @param warnResultId 预警结果ID
     * @return 通知用户列表
     */
    List<RemindUserPo> getRemindUserList(String warnResultId);

    /**
     * 根据预警结果ID分页查询通知用户列表
     *
     * @param warnResultId 预警结果ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页后的通知用户列表
     */
    PageInfo<RemindUserPo> getRemindUserListPage(String warnResultId, Integer pageNum, Integer pageSize);

}
