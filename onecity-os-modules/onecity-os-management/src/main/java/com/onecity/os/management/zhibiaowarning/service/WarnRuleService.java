package com.onecity.os.management.zhibiaowarning.service;

import com.onecity.os.management.zhibiaowarning.entity.WarnRule;
import com.onecity.os.management.zhibiaowarning.entity.WarnRuleDetail;
import com.onecity.os.management.zhibiaowarning.entity.dto.WarnRuleAndResultQueryDto;
import com.onecity.os.management.zhibiaowarning.entity.dto.WarnRuleCheckDetailDto;
import com.onecity.os.management.zhibiaowarning.entity.po.ItemTitlePo;
import com.onecity.os.management.zhibiaowarning.entity.po.WarnInfoPo;
import com.onecity.os.management.zhibiaowarning.entity.vo.IndicatorWarn;
import com.onecity.os.management.zhibiaowarning.entity.vo.WarnRuleAndResult;

import java.util.Date;
import java.util.List;

/**
 * 预警规则Service接口
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
public interface WarnRuleService {
    
    /**
     * 根据指标id获取其下面的子指标信息以及预警规则信息
     *
     * @param indicatorId 指标ID
     * @return 子指标及预警规则信息
     */
    List<IndicatorWarn> getWarnAndInfoByParentId(String indicatorId);

    /**
     * 根据指标id获取其配置的预警规则列表
     *
     * @param indicatorId 指标ID
     * @return 预警规则信息列表
     */
    List<WarnInfoPo> getWarnInfoListByIndicatorId(String indicatorId);

    /**
     * 查询预警规则列表
     *
     * @param warnRule 预警规则
     * @return 预警规则集合
     */
    List<WarnRule> selectWarnRuleList(WarnRule warnRule);
    
    /**
     * 根据ID查询预警规则
     *
     * @param warnRuleId 预警规则ID
     * @return 预警规则
     */
    WarnRule selectWarnRuleById(String warnRuleId);
    
    /**
     * 新增预警规则
     *
     * @param warnRule 预警规则
     * @return 结果
     */
    int insertWarnRule(WarnRule warnRule);
    
    /**
     * 新增预警规则及其详情
     *
     * @param warnRule 预警规则
     * @param detailList 预警规则详情列表
     * @return 结果
     */
    int insertWarnRuleWithDetails(WarnRule warnRule, List<WarnRuleDetail> detailList);

    /**
     * 根据预警规则名称查询预警规则
     *
     * @param warnRuleName 预警规则名称
     * @return 预警规则
     */
    List<WarnRule> selectWarnRuleByName(String warnRuleName);

    /**
     * 根据预警规则名称和规则id查询预警规则
     *
     * @param warnRuleName 预警规则名称
     * @return 预警规则
     */
    List<WarnRule> selectWarnRuleByNameAndId(String warnRuleName,String wranRuleId);

    /**
     * 修改预警规则
     *
     * @param warnRule 预警规则
     * @return 结果
     */
    int updateWarnRule(WarnRule warnRule);
    
    /**
     * 删除预警规则
     *
     * @param warnRuleId 预警规则ID
     * @return 结果
     */
    int deleteWarnRuleById(String warnRuleId);
    
    /**
     * 批量删除预警规则
     *
     * @param warnRuleIds 需要删除的预警规则ID数组
     * @return 结果
     */
    int deleteWarnRuleByIds(String[] warnRuleIds);
    
    /**
     * 更新预警规则状态
     *
     * @param warnRuleId 预警规则ID
     * @param warnStatus 状态
     * @return 结果
     */
    int updateWarnRuleStatus(String warnRuleId, Integer warnStatus);

    /**
     * 修改预警规则及其详情
     *
     * @param warnRule 预警规则
     * @param detailList 预警规则详情列表
     * @return 结果
     */
    int updateWarnRuleWithDetails(WarnRule warnRule, List<WarnRuleDetail> detailList);

    /**
     * 根据预警规则ID查询预警规则详情列表
     *
     * @param warnRuleId 预警规则ID
     * @return 预警规则详情列表
     */
    List<WarnRuleDetail> selectWarnRuleDetailsByWarnRuleId(String warnRuleId);

    /**
     * 根据预警规则ID、版块编码和指标ID查询预警规则
     *
     * @param warnRuleId 预警规则ID
     * @param sourceId 版块编码
     * @param indicatorId 指标ID
     * @return 预警规则
     */
    WarnRule selectWarnRuleByIdAndSourceId(String warnRuleId, String sourceId, String indicatorId);

    /**
     * 根据预警规则ID、数据源ID和指标ID获取预警规则详情列表
     *
     * @param warnRuleId 预警规则ID
     * @param sourceId 数据源ID
     * @param indicatorId 指标ID
     * @return 预警规则详情列表，如果没有找到相关的预警规则或详情，则返回空列表
     */
    WarnRuleCheckDetailDto getResultWarnRuleByIdAndSourceId(String warnRuleId, String sourceId, String indicatorId);

    /**
     * 根据指标 id 和 source 检查是否有对应的预警规则及是否触发预警
     * @param sources
     * @param sourceId
     */
    void checkResultWarnRuleByIdAndSourceId(List<String> sources, String sourceId,Date date);

    /**
     * 根据指标 id 和 source 检查是否有对应的预警规则及是否触发预警
     * 异步执行
     * @param sources
     */
    void checkResultWarnRuleByIdAndSourceIdAsync(List<String> sources, String sourceId, Date date);

    /**
     * 根据预警间隔检查是否有对应的预警规则及是否触发预警
     * 异步执行
     * @param warnInterval
     */
    void checkResultWarnRuleByWarnIntervalAsync(String warnInterval);

    /**
     * 根据指标ID查询数据项列表
     * @param indicatorId 指标ID
     * @return 数据项列表
     */
    List<String> getItemByIndicatorId(String indicatorId);

    /**
     * 根据指标ID查询正在使用的表头列表
     * @param indicatorId 指标ID
     * @return 表头信息列表（包含数据值名称和对应字段名）
     */
    List<ItemTitlePo> getItemTitleByIndicatorId(String indicatorId);

    /**
     * 获取全部的预警规则及相应的预警结果列表
     *
     * @param queryDto 查询条件
     * @return 预警规则及结果列表
     */
    List<WarnRuleAndResult> getWarnRuleAndResultList(WarnRuleAndResultQueryDto queryDto);

}