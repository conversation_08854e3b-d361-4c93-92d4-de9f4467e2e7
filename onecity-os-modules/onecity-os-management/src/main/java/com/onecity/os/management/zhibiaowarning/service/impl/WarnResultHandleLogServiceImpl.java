package com.onecity.os.management.zhibiaowarning.service.impl;

import com.onecity.os.management.zhibiaowarning.entity.WarnResultHandleLog;
import com.onecity.os.management.zhibiaowarning.mapper.WarnResultHandleLogMapper;
import com.onecity.os.management.zhibiaowarning.service.WarnResultHandleLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;
import java.util.Date;

/**
 * 预警结果处理记录Service实现
 * 
 * <AUTHOR>
 * @date 2023/10/25
 */
@Service
public class WarnResultHandleLogServiceImpl implements WarnResultHandleLogService {
    
    @Autowired
    private WarnResultHandleLogMapper warnResultHandleLogMapper;
    
    /**
     * 根据预警结果ID查询预警结果处理记录列表
     * 
     * @param warnResultId 预警结果id
     * @return 预警结果处理记录列表
     */
    @Override
    public List<WarnResultHandleLog> getHandleLogListByWarnResultId(String warnResultId) {
        return warnResultHandleLogMapper.selectByWarnResultId(warnResultId);
    }

}