package com.onecity.os.management.zhibiaowarning.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.DateUtils;
import com.onecity.os.common.core.utils.IdGen;
import com.onecity.os.management.configmanage.entity.SourceManage;
import com.onecity.os.management.configmanage.mapper.SourceManageMapper;
import com.onecity.os.management.zhibiaowarning.entity.WarnResult;
import com.onecity.os.management.zhibiaowarning.entity.WarnResultHandleLog;
import com.onecity.os.management.zhibiaowarning.entity.WarnRule;
import com.onecity.os.management.zhibiaowarning.entity.po.RemindUserPo;
import com.onecity.os.management.zhibiaowarning.entity.po.WarnResultPo;
import com.onecity.os.management.zhibiaowarning.entity.po.WarnResultPo1;
import com.onecity.os.management.zhibiaowarning.entity.vo.*;
import com.onecity.os.management.zhibiaowarning.mapper.WarnResultHandleLogMapper;
import com.onecity.os.management.zhibiaowarning.mapper.WarnResultMapper;
import com.onecity.os.management.zhibiaowarning.mapper.WarnRuleMapper;
import com.onecity.os.management.zhibiaowarning.service.WarnResultService;
import com.onecity.os.system.api.RemoteUserService;
import com.onecity.os.system.api.domain.SysRole;
import com.onecity.os.system.api.domain.SysUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import static com.github.pagehelper.page.PageMethod.startPage;

/**
 * 预警结果Service实现
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Slf4j
@Service
public class WarnResultServiceImpl implements WarnResultService {
    
    @Autowired
    private WarnResultMapper warnResultMapper;
    
    @Autowired
    private WarnResultHandleLogMapper warnResultHandleLogMapper;

    @Resource
    private WarnRuleMapper warnRuleMapper;

    @Autowired
    private SourceManageMapper sourceManageMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 根据用户ID获取预警结果列表
     *
     * @param userId 用户ID
     * @param name 规则名称或指标名称
     * @param roleIds 用户的角色列表
     * @return 预警结果列表
     */
    @Override
    public List<WarnResultVo> getWarnResultListByUser(String userId, String name, List<String> roleIds) {
        List<WarnResultVo> list = warnResultMapper.selectWarnResultListByUser(userId, name, roleIds);
        return list;
    }
    
    /**
     * 根据主键查询预警结果
     *
     * @param warnResultId 预警结果ID
     * @return 预警结果
     */
    @Override
    public WarnResult selectByPrimaryKey(String warnResultId) {
        return warnResultMapper.selectByPrimaryKey(warnResultId);
    }
    /**
     * 根据主键查询预警结果
     *
     * @param warnResult 预警结果
     * @return 预警结果
     */
    @Override
    public int saveWarnResult(WarnResult warnResult) {
        warnResult.setWarnResultId(IdGen.uuid());
        // 状态：0-未处理，1-已处理，2-已忽略
        warnResult.setResultStatus(1);
        warnResult.setCreateTime(DateUtils.getNowDate());
        return warnResultMapper.insert(warnResult);
    }

    /**
     * 根据预警规则ID查询预警结果列表
     *
     * @param warnRuleId 预警规则ID
     * @return 预警结果列表
     */
    @Override
    public List<WarnResult> selectByWarnRuleId(String warnRuleId) {
        return warnResultMapper.selectByWarnRuleId(warnRuleId);
    }
    
    /**
     * 根据指标ID查询预警结果列表
     *
     * @param indicatorId 指标ID
     * @return 预警结果列表
     */
    @Override
    public List<WarnResult> selectByIndicatorId(String indicatorId) {
        return warnResultMapper.selectByIndicatorId(indicatorId);
    }

    /**
     * 根据预警结果ID查询指标预警详情及预警历史
     *
     * @param warnResultId 预警结果ID
     * @return 预警详情及历史
     */
    @Override
    public WarnInfoDetail getWarnInfoDetailByResultId(String warnResultId) {
        // 查询预警结果
        WarnResult warnResult = warnResultMapper.selectByPrimaryKey(warnResultId);
        if (warnResult == null) {
            return null;
        }
        
        // 查询预警规则
        WarnRule warnRule = warnRuleMapper.selectByPrimaryKey(warnResult.getWarnRuleId());
        if (warnRule == null) {
            return null;
        }
        
        // 查询板块信息
        String sourceName = "";
        if (StringUtils.isNotEmpty(warnResult.getSourceId())) {
            SourceManage sourceInfo = sourceManageMapper.getSourceInfoBySourceSimpleName(warnResult.getSourceId());
            if (sourceInfo != null) {
                sourceName = sourceInfo.getSourceName();
            }
        }
        
        // 查询预警结果处理记录
        List<WarnResultHandleLog> handleLogList = warnResultHandleLogMapper.selectByWarnResultId(warnResultId);
        
        // 查询预警历史（当前预警结果之前的10条）
        List<WarnResultPo> historyList = warnResultMapper.selectHistoryByWarnRuleId(
                warnResult.getWarnRuleId(), warnResultId, 10);
        
        // 构建返回结果
        WarnInfoDetail warnInfoDetail = new WarnInfoDetail();
        warnInfoDetail.setIndicatorName(warnRule.getIndicatorName());
        warnInfoDetail.setResultStatus(warnResult.getResultStatus());
        warnInfoDetail.setWarnRuleId(warnRule.getWarnRuleId());
        warnInfoDetail.setRuleName(warnRule.getRuleName());
        warnInfoDetail.setSourceId(warnResult.getSourceId());
        warnInfoDetail.setSourceName(sourceName);
        warnInfoDetail.setCreateTime(warnResult.getCreateTime());
        warnInfoDetail.setWarnLevel(warnRule.getWarnLevel());
        warnInfoDetail.setWarnCountRuleSubs(warnResult.getWarnConutRuleSub());
        warnInfoDetail.setCompareValue(warnResult.getCompareValue());
        warnInfoDetail.setRealValue(warnResult.getRealValue());
        warnInfoDetail.setIndicatorData(warnResult.getIndicatorData());
        warnInfoDetail.setIndicatorExhibitType(warnResult.getIndicatorExhibitType());
        warnInfoDetail.setWarnResultHandleLogList(handleLogList);
        warnInfoDetail.setWarnResultHistoryList(historyList);
        
        return warnInfoDetail;
    }

    /**
     * 更新预警结果状态
     *
     * @param warnResultId 预警结果ID
     * @param status 状态：0-未处理，1-已处理，2-已忽略
     * @param userId 操作用户ID
     * @return 影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateWarnResultStatus(String warnResultId, Integer status, Long userId,String handleRemark) {
        // 查询预警结果是否存在
        WarnResult warnResult = warnResultMapper.selectByPrimaryKey(warnResultId);
        if (warnResult == null) {
            return 0;
        }
        
//        // 如果状态没有变化，直接返回成功
//        if (warnResult.getResultStatus() != null && warnResult.getResultStatus().equals(status)) {
//            return 1;
//        }
        
        // 更新预警结果状态
        WarnResult updateResult = new WarnResult();
        updateResult.setWarnResultId(warnResultId);
        updateResult.setResultStatus(status);
        
        // 添加处理记录
        WarnResultHandleLog handleLog = new WarnResultHandleLog();
        handleLog.setHandleId(UUID.randomUUID().toString().replaceAll("-", ""));
        handleLog.setWarnResultId(warnResultId);
        handleLog.setHandleUserId(userId);
        handleLog.setCreateTime(new Date());
        
        // 根据不同状态设置不同的处理内容和结果
        String username = SecurityUtils.getLoginUser().getSysUser().getNickName();
        handleLog.setCreater(username);
        
        if (status == 1) {
            // 待处理
            handleLog.setHandleContent("设置状态为待处理");
            handleLog.setHandleResult("待处理");
        } else if (status == 2) {
            // 已忽略
            handleLog.setHandleContent("设置状态为处理中");
            handleLog.setHandleResult("处理中");
        } else if(status == 3){
            // 未处理（一般不会有这种操作，但为了完整性添加）
            handleLog.setHandleContent("设置状态为已解决");
            handleLog.setHandleResult("已解决");
        }else if(status == 4){
            // 未处理（一般不会有这种操作，但为了完整性添加）
            handleLog.setHandleContent("设置状态为终止");
            handleLog.setHandleResult("终止");
            handleLog.setHandleRemark(handleRemark);
        }else if(status == 5){
            // 未处理（一般不会有这种操作，但为了完整性添加）
            handleLog.setHandleContent("进行督办");
            handleLog.setHandleResult("督办");
            handleLog.setHandleRemark(handleRemark);
            updateResult.setResultStatus(warnResult.getResultStatus());
        }
        
        // 保存处理记录
        warnResultHandleLogMapper.insert(handleLog);
        
        // 更新预警结果状态
        return warnResultMapper.updateWarnResultStatus(updateResult);
    }

    @Override
    public WarnResultCountVo countWarnResult(Date beginTime, Date endTime) {
        WarnResultCountVo resultVo = new WarnResultCountVo();
        // 根据状态统计预警结果数量
        List<CountStatusVo> countStatusVo = warnResultMapper.countWarnResultByStatus(beginTime, endTime);
        for (CountStatusVo count : countStatusVo) {
            switch (count.getResultStatus()) {
                case 1:
                    resultVo.setToDoTotal(count.getResultCount());
                    break;
                case 2:
                    resultVo.setDoingTotal(count.getResultCount());
                    break;
                case 3:
                    resultVo.setDoneTotal(count.getResultCount());
                    break;
                case 4:
                    resultVo.setEndTotal(count.getResultCount());
                    break;
                default:
                    break;
            }
        }
        resultVo.setResultTotal(warnResultMapper.countTotalWarnResult(beginTime, endTime));

        List<TopSource> topSources = warnResultMapper.getTopSourceWarnResults(beginTime, endTime, 5); // 获取TOP 5
        resultVo.setTopSourceList(topSources);

        return resultVo;
    }

    /**
     * 根据预警规则ID查询预警历史列表
     *
     * @param warnRuleId 预警规则ID
     * @return 预警历史列表
     */
    @Override
    public List<WarnResultPo1> getWarnResultListByWarnRuleId(String warnRuleId) {
        return warnResultMapper.getWarnResultListByWarnRuleId(warnRuleId);
    }

    /**
     * 根据预警结果ID查询通知用户列表
     *
     * @param warnResultId 预警结果ID
     * @return 通知用户列表
     */
    @Override
    public List<RemindUserPo> getRemindUserList(String warnResultId) {
        List<RemindUserPo> remindUserList = new ArrayList<>();
        // 根据预警结果ID查询通知用户
        WarnResult warnResult = warnResultMapper.selectByPrimaryKey(warnResultId);
        if (warnResult == null) {
            return new ArrayList<>();
        }
        //根据预警结果id查询督办处理日志
        List<WarnResultHandleLog> warnResultHandleLogList = warnResultHandleLogMapper.selectByWarnResultId(warnResultId);
        // 处理用户Id,分页处理
//        if (1==warnRule.getRemindType()&&StringUtils.isNotBlank(warnRule.getRemindUserIds())) {
        if(StringUtils.isNotBlank(warnResult.getRemindUserIds())) {
            String[] userIds = warnResult.getRemindUserIds().split(",");
            BaseResult<List<SysUser>> userList = remoteUserService.getUserByPcUserIds(userIds);
            if (userList.getCode() == 200 && userList.getData() != null) {
                for (SysUser sysUser : userList.getData()) {
                    RemindUserPo remindUserPo = new RemindUserPo();
                    remindUserPo.setUserName(sysUser.getUserName());
                    remindUserPo.setNickName(sysUser.getNickName());
                    Boolean flag = true;
                    for (WarnResultHandleLog handleLog : warnResultHandleLogList) {
                        if (handleLog.getHandleUserId().equals(sysUser.getUserId())) {
                            remindUserPo.setHandleResult(handleLog.getHandleResult());
                            remindUserPo.setHandleTime(handleLog.getCreateTime());
                            flag = false;
                        }
                    }
                    if (flag) {
                        remindUserPo.setHandleResult("未处理");
                    }
                    remindUserList.add(remindUserPo);
                }
            }
        }
            //通知人列表在预警结果中存的有，不再使用预警规则里的通知人
//        }else if (2==warnRule.getRemindType()&&StringUtils.isNotBlank(warnRule.getRemindRoleId())) {
//            String[] roleIds = warnRule.getRemindRoleId().split(",");
//            BaseResult<List<SysUser>> userList = remoteUserService.getUsersByRoleIds(roleIds);
//            if (userList.getCode() == 200 && userList.getData()!= null) {
//                for (SysUser sysUser : userList.getData()) {
//                    log.info("获取的角色对应的用户为："+ JSONObject.toJSONString(sysUser));
//                    RemindUserPo remindUserPo = new RemindUserPo();
//                    remindUserPo.setUserName(sysUser.getUserName());
//                    remindUserPo.setNickName(sysUser.getNickName());
//                    Boolean flag = true;
//                    for(WarnResultHandleLog handleLog : warnResultHandleLogList) {
//                        if (handleLog.getHandleUserId().equals(sysUser.getUserId())) {
//                            remindUserPo.setHandleResult(handleLog.getHandleResult());
//                            remindUserPo.setHandleTime(handleLog.getCreateTime());
//                            flag = false;
//                        }
//                    }
//                    if (flag) {
//                        remindUserPo.setHandleResult("未处理");
//                    }
//                    remindUserList.add(remindUserPo);
//                }
//            }
//        }
        return remindUserList;
    }

    /**
     * 根据预警结果ID分页查询通知用户列表
     *
     * @param warnResultId 预警结果ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页后的通知用户列表
     */
    @Override
    public PageInfo<RemindUserPo> getRemindUserListPage(String warnResultId, Integer pageNum, Integer pageSize) {
        // 获取完整的通知用户列表
        List<RemindUserPo> allRemindUserList = getRemindUserList(warnResultId);

        // 手动实现分页逻辑
        int total = allRemindUserList.size();

        // 计算起始索引和结束索引
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);

        // 防止索引越界
        if (startIndex >= total) {
            // 如果起始索引超出范围，返回空列表
            if (pageNum > 1) {
                // 如果不是第一页，则返回最后一页数据
                int lastPageNum = (total + pageSize - 1) / pageSize;
                startIndex = (lastPageNum - 1) * pageSize;
                endIndex = total;
            } else {
                return new PageInfo<>(new ArrayList<>());
            }
        }

        // 截取当前页的数据
        List<RemindUserPo> pageList = allRemindUserList.subList(startIndex, endIndex);

        // 创建PageInfo对象
        PageInfo<RemindUserPo> pageInfo = new PageInfo<>(pageList);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages((total + pageSize - 1) / pageSize);
        pageInfo.setSize(pageList.size());

        return pageInfo;
    }
}
