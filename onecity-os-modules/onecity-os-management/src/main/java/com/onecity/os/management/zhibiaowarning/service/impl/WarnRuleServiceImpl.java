package com.onecity.os.management.zhibiaowarning.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.googlecode.aviator.AviatorEvaluator;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.DateUtils;
import com.onecity.os.management.constant.UtilConstants;
import com.onecity.os.management.dingding.utils.AppManageUtil;
import com.onecity.os.management.statisticanalysis.entity.IndicatorUpdateInfo;
import com.onecity.os.management.statisticanalysis.entity.IndicatorUpdateRecord;
import com.onecity.os.management.statisticanalysis.mapper.IndicatorUpdateRecordMapper;
import com.onecity.os.management.yulan.feign.ReportFeignService;
import com.onecity.os.management.yulan.feign.ResponseBean;
import com.onecity.os.management.yulan.service.GeneralIndicatorService;
import com.onecity.os.management.yulan.vo.IndicatorYuLanCoreVo;
import com.onecity.os.management.yulan.vo.IndicatorYuLanReqVO;
import com.onecity.os.management.zhibiao.entity.DataConfig;
import com.onecity.os.management.zhibiao.entity.GeneralIndicator;
import com.onecity.os.management.zhibiao.entity.GeneralIndicatorDataTitle;
import com.onecity.os.management.zhibiao.entity.GeneralIndicatorTianbao;
import com.onecity.os.management.zhibiao.mapper.*;
import com.onecity.os.management.zhibiao.service.IndicatorService;
import com.onecity.os.management.zhibiao.service.IndicatorTitleService;
import com.onecity.os.management.zhibiaowarning.aviator.constants.enums.*;
import com.onecity.os.management.zhibiaowarning.entity.WarnResult;
import com.onecity.os.management.zhibiaowarning.entity.WarnRule;
import com.onecity.os.management.zhibiaowarning.entity.WarnRuleDetail;
import com.onecity.os.management.zhibiaowarning.entity.dto.WarnRuleAndResultQueryDto;
import com.onecity.os.management.zhibiaowarning.entity.dto.WarnRuleCheckDetailDto;
import com.onecity.os.management.zhibiaowarning.entity.po.ItemTitlePo;
import com.onecity.os.management.zhibiaowarning.entity.po.WarnInfoPo;
import com.onecity.os.management.zhibiaowarning.entity.vo.IndicatorItemValueVO;
import com.onecity.os.management.zhibiaowarning.entity.vo.IndicatorWarn;
import com.onecity.os.management.zhibiaowarning.entity.vo.WarnRuleAndResult;
import com.onecity.os.management.zhibiaowarning.mapper.WarnResultHandleLogMapper;
import com.onecity.os.management.zhibiaowarning.mapper.WarnResultMapper;
import com.onecity.os.management.zhibiaowarning.mapper.WarnRuleDetailMapper;
import com.onecity.os.management.zhibiaowarning.mapper.WarnRuleMapper;
import com.onecity.os.management.zhibiaowarning.service.WarnResultService;
import com.onecity.os.management.zhibiaowarning.service.WarnRuleService;
import com.onecity.os.management.zhibiaowarning.utils.UpdateCycleUtils;
import com.onecity.os.system.api.RemoteUserService;
import com.onecity.os.system.api.domain.SysRole;
import com.onecity.os.system.api.domain.SysUser;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 预警规则Service实现
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Slf4j
@Service
public class WarnRuleServiceImpl implements WarnRuleService {

    @Resource
    private WarnRuleMapper warnRuleMapper;

    @Resource
    private WarnRuleDetailMapper warnRuleDetailMapper;


    @Resource
    private IndicatorService indicatorService;

    @Resource
    private IndicatorTitleService indicatorTitleService;

    @Resource
    private IndicatorMapper indicatorMapper;

    @Resource
    private GeneralIndicatorService generalIndicatorService;

    @Resource
    private WarnResultService warnResultService;

    @Resource
    private IndicatorUpdateRecordMapper indicatorUpdateRecordMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    @Resource
    private GeneralIndicatorDataMapper generalIndicatorDataMapper;

    @Autowired
    private IndicatorTitleMapper indicatorTitleMapper;


    @Resource
    private GeneralIndicatorMapper generalIndicatorMapper;

    @Resource
    private DataConfigMapper dataConfigMapper;

    @Resource
    private IndicatorAuditNotesMapper indicatorAuditNotesMapper;

    @Autowired
    private WarnResultMapper warnResultMapper;

    @Autowired
    private WarnResultHandleLogMapper warnResultHandleLogMapper;

    @Resource
    private AppManageUtil appManageUtil;
    @Resource
    private ReportFeignService reportFeignService;


    // 共享的线程池 核心线程数10 最大线程数20 空闲线程存活时间60 任务队列100
    private static final ExecutorService executorService = new ThreadPoolExecutor(10, 20, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(100), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 预警规则阈值 key 字段
     * 用来存储同环比计算后的预警值
     */
    private static final String RULE_VALUE = "ruleValue";
    /**
     * 预警规则指标项名 itemName  key 字段
     * 用来存储同环比计算后的预警值
     */
    private static final String ITEM_NAME = "itemName";

    /**
     * 根据指标id获取其下面的子指标信息以及预警规则信息
     *
     * @param indicatorId 指标ID
     * @return 子指标及预警规则信息
     */
    @Override
    public List<IndicatorWarn> getWarnAndInfoByParentId(String indicatorId) {
        List<IndicatorWarn> list = warnRuleMapper.getWarnAndInfoByParentId(indicatorId);
        return list;
    }
    
    /**
     * 根据指标id获取其配置的预警规则列表
     *
     * @param indicatorId 指标ID
     * @return 预警规则信息列表
     */
    @Override
    public List<WarnInfoPo> getWarnInfoListByIndicatorId(String indicatorId) {
        List<WarnInfoPo> list = warnRuleMapper.getWarnInfoListByIndicatorId(indicatorId);
        //将用户Id和角色转换为对应的名称以及Id
        for (WarnInfoPo warnInfoPo : list) {
            // 处理用户Id
            if (1==warnInfoPo.getRemindType()&&StringUtils.isNotBlank(warnInfoPo.getRemindUserIds())) {
                String[] userIds = warnInfoPo.getRemindUserIds().split(",");
                StringBuilder userNames = new StringBuilder();
                BaseResult<List<SysUser>> userList = remoteUserService.getUserByPcUserIds(userIds);
                if (userList.getCode() == 200 && userList.getData() != null) {
//                    warnInfoPo.setSysUserList(userList.getData());
                    for (SysUser sysUser : userList.getData()) {
                        userNames.append(sysUser.getNickName()).append("、");
                    }
                    warnInfoPo.setRemindUsers(userNames.toString().substring(0,userNames.length() - 1));
                }
            }else if (2==warnInfoPo.getRemindType()&&StringUtils.isNotBlank(warnInfoPo.getRemindRoleId())) {
                String[] roleIds = warnInfoPo.getRemindRoleId().split(",");
                StringBuilder roleNames = new StringBuilder();
                BaseResult<List<SysRole>> roleList = remoteUserService.getRoleNameByRoleIds(roleIds);
                if (roleList.getCode() == 200 && roleList.getData()!= null) {
//                    warnInfoPo.setSysRoleList(roleList.getData());
                    for (SysRole sysRole : roleList.getData()) {
                        roleNames.append(sysRole.getRoleName()).append("、");
                    }
                    warnInfoPo.setRemindRoles(roleNames.toString().substring(0,roleNames.length() - 1));
                }
            }
        }
        return list;
    }
    
    /**
     * 查询预警规则列表
     *
     * @param warnRule 预警规则
     * @return 预警规则集合
     */
    @Override
    public List<WarnRule> selectWarnRuleList(WarnRule warnRule) {
        return warnRuleMapper.selectWarnRuleList(warnRule);
    }
    
    /**
     * 根据ID查询预警规则
     *
     * @param warnRuleId 预警规则ID
     * @return 预警规则
     */
    @Override
    public WarnRule selectWarnRuleById(String warnRuleId) {
        return warnRuleMapper.selectByPrimaryKey(warnRuleId);
    }
    
    /**
     * 新增预警规则
     *
     * @param warnRule 预警规则
     * @return 结果
     */
    @Override
    @Transactional
    public int insertWarnRule(WarnRule warnRule) {
        warnRule.setWarnRuleId(UUID.randomUUID().toString().replaceAll("-", ""));
        warnRule.setCreateTime(DateUtils.getNowDate());
        warnRule.setCreater(SecurityUtils.getLoginUser().getSysUser().getNickName());
        return warnRuleMapper.insertWarnRule(warnRule);
    }

    /**
     * 新增预警规则及其详情
     *
     * @param warnRule 预警规则
     * @param detailList 预警规则详情列表
     * @return 结果
     */
    @Override
    @Transactional
    public int insertWarnRuleWithDetails(WarnRule warnRule, List<WarnRuleDetail> detailList) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return 0;
        }
        log.info("当前登录用户信息为：{}", JSONObject.toJSONString(loginUser));
        // 生成预警规则ID
        String warnRuleId = UUID.randomUUID().toString().replaceAll("-", "");
        warnRule.setWarnRuleId(warnRuleId);
        warnRule.setCreateTime(DateUtils.getNowDate());
        warnRule.setCreater(loginUser.getSysUser().getNickName());
        warnRule.setUpdateTime(DateUtils.getNowDate());
        warnRule.setUpdater(loginUser.getSysUser().getNickName());

        log.info("预警规则为：{}", JSONObject.toJSONString(warnRule));
        // 插入预警规则
        int rows = warnRuleMapper.insertWarnRule(warnRule);

        // 插入预警规则详情
        if (detailList != null && !detailList.isEmpty()) {
            for (WarnRuleDetail detail : detailList) {
                detail.setWarnRuleId(warnRuleId);
                detail.setCreateTime(DateUtils.getNowDate());
                detail.setCreater(loginUser.getSysUser().getNickName());
                detail.setUpdateTime(DateUtils.getNowDate());
                detail.setUpdater(loginUser.getSysUser().getNickName());
            }
        }
        if (detailList != null && !detailList.isEmpty()) {
            log.info("预警规则详情为：{}", JSONObject.toJSONString(detailList));
            warnRuleDetailMapper.inserBatch(detailList);
        }
        return rows;
    }

    /**
     * 根据预警规则名称查询预警规则
     *
     * @param warnRuleName 预警规则名称
     * @return 预警规则
     */
    @Override
    public List<WarnRule> selectWarnRuleByName(String warnRuleName) {
        return warnRuleMapper.selectByRuleName(warnRuleName);
    }

    @Override
    public List<WarnRule> selectWarnRuleByNameAndId(String warnRuleName, String wranRuleId) {
        return warnRuleMapper.selectByRuleNameAndId(warnRuleName,wranRuleId);
    }

    /**
     * 修改预警规则
     *
     * @param warnRule 预警规则
     * @return 结果
     */
    @Override
    @Transactional
    public int updateWarnRule(WarnRule warnRule) {
        warnRule.setUpdateTime(DateUtils.getNowDate());
        warnRule.setUpdater(SecurityUtils.getLoginUser().getSysUser().getNickName());
        return warnRuleMapper.updateByPrimaryKey(warnRule);
    }
    
    /**
     * 删除预警规则
     *
     * @param warnRuleId 预警规则ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWarnRuleById(String warnRuleId) {
        // 删除预警规则详情
        warnRuleDetailMapper.deleteByWarnRuleId(warnRuleId);
        // 产品经理沟通不需要删除预警结果
        // 注意：如果有预警结果表，需要删除相关的预警结果
        warnResultMapper.deleteByWarnRuleId(warnRuleId);
        //删除预警结果处理记录
        warnResultHandleLogMapper.deleteByWarnRuleId(warnRuleId);
        // 删除预警规则
        return warnRuleMapper.deleteByPrimaryKey(warnRuleId);
    }
    
    /**
     * 批量删除预警规则
     *
     * @param warnRuleIds 需要删除的预警规则ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWarnRuleByIds(String[] warnRuleIds) {
        // 批量删除预警规则详情和预警结果
        for (String warnRuleId : warnRuleIds) {
            // 删除预警规则详情
            warnRuleDetailMapper.deleteByWarnRuleId(warnRuleId);
            // 删除预警结果
            // 注意：如果有预警结果表，需要删除相关的预警结果
            // warnResultMapper.deleteByWarnRuleId(warnRuleId);
        }
        // 批量删除预警规则
        return warnRuleMapper.deleteWarnRuleByIds(warnRuleIds);
    }
    
    /**
     * 更新预警规则状态
     *
     * @param warnRuleId 预警规则ID
     * @param warnStatus 状态
     * @return 结果
     */
    @Override
    @Transactional
    public int updateWarnRuleStatus(String warnRuleId, Integer warnStatus) {
        return warnRuleMapper.updateStatus(warnRuleId, warnStatus);
    }
    
    /**
     * 修改预警规则及其详情
     *
     * @param warnRule 预警规则
     * @param detailList 预警规则详情列表
     * @return 结果
     */
    @Override
    @Transactional
    public int updateWarnRuleWithDetails(WarnRule warnRule, List<WarnRuleDetail> detailList) {
        // 更新预警规则
        warnRule.setUpdateTime(DateUtils.getNowDate());
        warnRule.setUpdater(SecurityUtils.getLoginUser().getSysUser().getNickName());
        int rows = warnRuleMapper.updateByPrimaryKeySelective(warnRule);
        
        if (detailList != null && !detailList.isEmpty()) {
            // 删除原有的预警规则详情
            warnRuleDetailMapper.deleteByWarnRuleId(warnRule.getWarnRuleId());
            
            // 插入新的预警规则详情
            for (WarnRuleDetail detail : detailList) {
                // 如果detailId为空，则生成新的ID
                if (StringUtils.isEmpty(detail.getDetailId())) {
                    detail.setDetailId(UUID.randomUUID().toString().replaceAll("-", ""));
                }
                detail.setWarnRuleId(warnRule.getWarnRuleId());
                detail.setUpdateTime(DateUtils.getNowDate());
                detail.setUpdater(SecurityUtils.getLoginUser().getSysUser().getNickName());
                // 如果是新增的详情，设置创建人和创建时间
                if (detail.getCreateTime() == null) {
                    detail.setCreateTime(DateUtils.getNowDate());
                    detail.setCreater(SecurityUtils.getLoginUser().getSysUser().getNickName());
                }
                warnRuleDetailMapper.insert(detail);
            }
        }
        
        return rows;
    }
    
    /**
     * 根据预警规则ID查询预警规则详情列表
     *
     * @param warnRuleId 预警规则ID
     * @return 预警规则详情列表
     */
    @Override
    public List<WarnRuleDetail> selectWarnRuleDetailsByWarnRuleId(String warnRuleId) {
        return warnRuleDetailMapper.selectByWarnRuleId(warnRuleId);
    }

    /**
     * 根据预警规则ID、版块编码和指标ID查询预警规则
     *
     * @param warnRuleId 预警规则ID
     * @param sourceId 版块编码
     * @param indicatorId 指标ID
     * @return 预警规则
     */
    @Override
    public WarnRule selectWarnRuleByIdAndSourceId(String warnRuleId, String sourceId, String indicatorId) {
        return warnRuleMapper.selectWarnRuleByIdAndSourceId(warnRuleId, sourceId, indicatorId);
    }

    /**
     * 根据指标ID查询数据项列表
     * @param indicatorId 指标ID
     * @return 数据项列表
     */
    @Override
    public List<String> getItemByIndicatorId(String indicatorId) {
        List<String> itemList = new ArrayList<>();
        GeneralIndicator generalIndicator = generalIndicatorMapper.getIndicatorInfoById(indicatorId);
        if(1==generalIndicator.getDataUpdateMode()) {
            itemList = generalIndicatorDataMapper.getIndicatorDataNameListByIndicatorId(indicatorId);
        }else if(2==generalIndicator.getDataUpdateMode()) {
            List<DataConfig> dataConfigList = dataConfigMapper.getDataConfigByIndicatorId(indicatorId);
            //查询数据集信息
            Long dataSetId = dataConfigList.get(0).getDataSetId();
            //数据项key
            String dataKey = dataConfigList.get(0).getDataKey();
            try {
            ResponseBean responseBean = reportFeignService.detailById(dataSetId);
            log.info("获取到的数据集数据为data："+responseBean.getData().toString());
            LinkedHashMap dataDto = (LinkedHashMap)responseBean.getData();
            List<Map> dataArray = (List<Map>)dataDto.get("data");
            if (dataArray.size() > 0) {
                for (Map object : dataArray) {
                    itemList.add(object.get(dataKey).toString());
                    }
                }
            } catch (Exception e) {
                log.error("请求数据集接口获取数据异常！" + e.getMessage());
            }
        }
        return itemList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 根据指标ID查询正在使用的表头列表
     *
     * @param indicatorId 指标ID
     * @return 表头信息列表（包含数据值名称和对应字段名）
     */
    @Override
    public List<ItemTitlePo> getItemTitleByIndicatorId(String indicatorId){
        GeneralIndicator generalIndicator = generalIndicatorMapper.getIndicatorInfoById(indicatorId);
        List<ItemTitlePo> itemTitlePoList = new ArrayList<>();
        if(1==generalIndicator.getDataUpdateMode()) {
            List<GeneralIndicatorDataTitle> titles = indicatorTitleMapper.queryById(indicatorId);
            for (GeneralIndicatorDataTitle title : titles) {
                ItemTitlePo itemTitlePo = new ItemTitlePo();
                itemTitlePo.setItemValueName(title.getItemValueName());
                itemTitlePo.setColumnName(title.getColumnName());
                itemTitlePoList.add(itemTitlePo);
            }
        }else if(2==generalIndicator.getDataUpdateMode()) {
            List<DataConfig> dataConfigList = dataConfigMapper.getDataConfigByIndicatorId(indicatorId);
            for (DataConfig dataConfig : dataConfigList) {
                ItemTitlePo itemTitlePo = new ItemTitlePo();
                itemTitlePo.setItemValueName(dataConfig.getDataValueName());
                itemTitlePo.setColumnName(dataConfig.getDataValue());
                itemTitlePoList.add(itemTitlePo);
            }
        }
        return itemTitlePoList;
    }

    /**
     * 异步校验指标预警
     * @param sources
     * @param sourceId
     * @return
     */
    private CompletableFuture<Void> checkResultWarnRuleAsync(List<String> sources, String sourceId,Date date) {
        if (CollectionUtils.isEmpty(sources)) {
            return null;
        }
        return CompletableFuture.runAsync(() -> {
            try {
                checkResultWarnRuleByIdAndSourceId(sources, sourceId,date);
            } catch (Exception e) {
                log.error("checkResultWarnRuleAsync异步校验指标预警异常", e);
                // 可以选择重新抛出运行时异常
                throw new RuntimeException(e);
            }
        }, executorService).whenComplete((result, ex) -> {
            if (ex != null) {
                log.error("checkResultWarnRuleAsync异步校验指标预警调用完成时发生异常", ex);
            } else {
                log.info("checkResultWarnRuleAsync异步校验指标预警完成");
            }
        });
    }

    /**
     * 异步校验待更新的指标是否满足指标预警
     *
     * @param sources
     */
    @Override
    public void checkResultWarnRuleByIdAndSourceIdAsync(List<String> sources, String sourceId,Date date) {
        checkResultWarnRuleAsync(sources, sourceId,date);
    }

    @Override
    public void checkResultWarnRuleByWarnIntervalAsync(String warnInterval) {
        //查询指定时间间隔的数据对接方式的指标预警的指标id
        List<String> warnRuleIds = warnRuleMapper.getWarnRuleIdsByWarnInterval(warnInterval);
        //遍历
        for (String id : warnRuleIds) {
            //查询预警结果表，如果该指标有未终止状态的预警结果，则不触发预警
            List<String> warnResultIds = warnResultMapper.checkByWarnRuleId(id);
            if (warnResultIds.size() > 0) {
                continue;
            }
            // 遍历指标的每个预警
            WarnInfoPo warnInfoPo = warnRuleMapper.getWarnInfoListByWarnRuleId(id);
            String warnRuleId = warnInfoPo.getWarnRuleId();
            //查询指标详情
            GeneralIndicator indicator = generalIndicatorMapper.getIndicatorInfoById(warnInfoPo.getIndicatorId());
            WarnRuleCheckDetailDto warnRuleCheckDetailDto = getResultWarnRuleByIdAndSourceId(warnRuleId, indicator.getSourceId(), warnInfoPo.getIndicatorId());
            // 触发了预警
            if (Objects.nonNull(warnRuleCheckDetailDto) && CollectionUtils.isNotEmpty(warnRuleCheckDetailDto.getDetails())) {
                List<WarnRuleDetail> detailList = warnRuleCheckDetailDto.getDetails();
                // 存预警结果
                String warnConutRuleSub = dealWarnRuleMsgByDetails(detailList, warnRuleCheckDetailDto.getValueMap(), warnRuleCheckDetailDto.getValueNameMap());
                WarnResult warnResult = new WarnResult();
                warnResult.setSourceId(indicator.getSourceId());
                warnResult.setWarnRuleId(warnRuleId);
                warnResult.setIndicatorId(warnInfoPo.getIndicatorId());
                warnResult.setIndicatorName(indicator.getIndicatorName());
                warnResult.setResultStatus(1);
                // 获取指标数据
                warnResult.setIndicatorData(getIndicatorDataById(warnInfoPo.getIndicatorId(),indicator.getSourceId()));
                // 比较值,拼接
                warnResult.setCompareValue(getCompareValueByDetails(detailList));
                // 实际值值,拼接
                warnResult.setRealValue(getRealValueByDetails(detailList, warnRuleCheckDetailDto.getValueMap()));
                warnResult.setWarnConutRuleSub(warnConutRuleSub);
                // 处理用户Id
                if (1==warnInfoPo.getRemindType()&&StringUtils.isNotBlank(warnInfoPo.getRemindUserIds())) {
                    warnResult.setRemindUserIds(warnInfoPo.getRemindUserIds());
                }else if (2==warnInfoPo.getRemindType()&&StringUtils.isNotBlank(warnInfoPo.getRemindRoleId())) {
                    String[] roleIds = warnInfoPo.getRemindRoleId().split(",");
                    StringBuilder userIds = new StringBuilder();
                    BaseResult<List<SysUser>> userList = remoteUserService.getUsersByRoleIds(roleIds);
                    log.info("根据角色id远程获取用户列表返回数据：", JSONObject.toJSONString(userList));
                    if (userList.getCode() == 200 && userList.getData()!= null) {
                        for (SysUser sysUser : userList.getData()) {
                            userIds.append(sysUser.getUserId()).append(",");
                        }
                    }
                    log.info("拼接完的用户id数据为：", userIds);
                    warnResult.setRemindUserIds(userIds.toString().substring(0,userIds.length() - 1));
                }
                warnResultService.saveWarnResult(warnResult);
                // 发出通知
                sendWarnResultMsgToUsers(warnInfoPo, indicator.getSourceName(), indicator.getIndicatorName(), indicator.getSourceId());
            }
        }
    }

    /**
     * 校验待更新的指标是否满足指标预警
     *
     * @param sources
     * @param sourceId
     */
    @Override
    public void checkResultWarnRuleByIdAndSourceId(List<String> sources, String sourceId ,Date date) {
        if (CollectionUtils.isEmpty(sources)) {
            return;
        }
        //获取版块上次审核通过的时间,审核时是按版块提交的，故sources里只有一个元素,传入的时间为这次审核触发预警的时间
        //格式化时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            date = dateFormat.parse(dateFormat.format(date));
        } catch (ParseException e) {
            log.error("日期转换失败："+e.getMessage());
        }
        Date lastAuditTime = indicatorAuditNotesMapper.getAuditTimeBySourceSimpleName(sources.get(0),date);
        // 异步调用指标监控，符合条件的指标则触发预警
        List<IndicatorUpdateInfo> indicatorUpdateAllRecord = generalIndicatorMapper.selectTodayUpdateIndicatorsBySourceForWarn(sources,lastAuditTime);
        // 获取指标 id
        Set<String> indicatorIds = indicatorUpdateAllRecord.stream().map(info -> info.getIndicatorId()).collect(Collectors.toSet());
        //去重指标更新记录中的重复记录
        List<IndicatorUpdateInfo> indicatorUpdateSetRecord = new ArrayList<>();
        for (String indicatorId : indicatorIds) {
            for (IndicatorUpdateInfo indicatorUpdateInfo : indicatorUpdateAllRecord) {
                if (indicatorId.equals(indicatorUpdateInfo.getIndicatorId())) {
                    indicatorUpdateSetRecord.add(indicatorUpdateInfo);
                }
            }
        }
        // 遍历每个指标
        for (IndicatorUpdateInfo indicatorUpdateInfo : indicatorUpdateSetRecord) {
            String indicatorId = indicatorUpdateInfo.getIndicatorId();
            // 如果没有数据更新则跳过
            if ("0".equals(indicatorUpdateInfo.getDataFlag())) {
                continue;
            }
            //查询预警结果表，如果该指标有未终止状态的预警结果，则不触发预警
            List<String> indicatorIdList = new ArrayList<>();
            indicatorIdList.add(indicatorId);
            List<String> warnResultIds = warnResultMapper.selectByIndicatorIds(indicatorIdList);
            if (warnResultIds.size() > 0) {
                continue;
            }
            String indicatorName = indicatorUpdateInfo.getIndicatorName();
            String sourceName = indicatorUpdateInfo.getSourceName();
            List<WarnInfoPo> list = Optional.ofNullable(getWarnInfoListByIndicatorId(indicatorId)).orElse(new ArrayList<>());
            // 过来启用的指标预警
            list = list.stream().filter(warnInfoPo -> warnInfoPo.getWarnStatus() == 1).collect(Collectors.toList());
            // 遍历指标的每个预警
            for (WarnInfoPo warnInfoPo : list) {
                String warnRuleId = warnInfoPo.getWarnRuleId();
                WarnRuleCheckDetailDto warnRuleCheckDetailDto = getResultWarnRuleByIdAndSourceId(warnRuleId, sourceId, indicatorId);
                // 触发了预警
                if (Objects.nonNull(warnRuleCheckDetailDto) && CollectionUtils.isNotEmpty(warnRuleCheckDetailDto.getDetails())) {
                    List<WarnRuleDetail> detailList = warnRuleCheckDetailDto.getDetails();
                    // 存预警结果
                    String warnConutRuleSub = dealWarnRuleMsgByDetails(detailList, warnRuleCheckDetailDto.getValueMap(), warnRuleCheckDetailDto.getValueNameMap());
                    WarnResult warnResult = new WarnResult();
                    warnResult.setSourceId(sourceId);
                    warnResult.setWarnRuleId(warnRuleId);
                    warnResult.setIndicatorId(indicatorId);
                    warnResult.setIndicatorName(indicatorName);
                    warnResult.setResultStatus(1);
                    // 获取指标数据
                    warnResult.setIndicatorData(getIndicatorDataById(indicatorId,sources.get(0)));
                    // 比较值,拼接
                    warnResult.setCompareValue(getCompareValueByDetails(detailList));
                    // 实际值值,拼接
                    warnResult.setRealValue(getRealValueByDetails(detailList, warnRuleCheckDetailDto.getValueMap()));
                    warnResult.setWarnConutRuleSub(warnConutRuleSub);
                    // 处理用户Id
                    if (1==warnInfoPo.getRemindType()&&StringUtils.isNotBlank(warnInfoPo.getRemindUserIds())) {
                        warnResult.setRemindUserIds(warnInfoPo.getRemindUserIds());
                    }else if (2==warnInfoPo.getRemindType()&&StringUtils.isNotBlank(warnInfoPo.getRemindRoleId())) {
                        String[] roleIds = warnInfoPo.getRemindRoleId().split(",");
                        StringBuilder userIds = new StringBuilder();
                        BaseResult<List<SysUser>> userList = remoteUserService.getUsersByRoleIds(roleIds);
                        log.info("根据角色id远程获取用户列表返回数据：", JSONObject.toJSONString(userList));
                        if (userList.getCode() == 200 && userList.getData()!= null) {
                            for (SysUser sysUser : userList.getData()) {
                                userIds.append(sysUser.getUserId()).append(",");
                            }
                        }
                        log.info("拼接完的用户id数据为：", userIds);
                        warnResult.setRemindUserIds(userIds.toString().substring(0,userIds.length() - 1));
                    }
                    warnResultService.saveWarnResult(warnResult);
                    // 发出通知
                    sendWarnResultMsgToUsers(warnInfoPo, sourceName, indicatorName, sourceId);
                }
            }
        }
    }

    /**
     * 根据指标 id 获取上次审核通过后指标更新的数据
     * @param indicatorIds
     * @return
     */
    private List<IndicatorUpdateRecord> getIndicatorUpdateRecord(List<String> indicatorIds) {
        if (CollectionUtils.isEmpty(indicatorIds)) {
            return new ArrayList<>();
        }
        // 所有指标和分组
        DateTime dateTime = DateUtil.beginOfDay(new Date());
        //查询出所有指标数据更新的记录
        List<IndicatorUpdateRecord> recordList = indicatorUpdateRecordMapper.getUpdateRecordList(dateTime.toStringDefaultTimeZone(), null, indicatorIds,1);
        return recordList;
    }

    /**
     * 根据指标IDSet获取指标数据
     * @param indicatorIdSet
     * @return
     */
    private List<GeneralIndicator> getIndicatorBySet(HashSet<String> indicatorIdSet) {
        if (CollectionUtils.isEmpty(indicatorIdSet)) {
            return new ArrayList<>();
        }
        // 所有指标和分组
        List<GeneralIndicator> allIndicators = indicatorService.getIndicatorsByParentIds(new ArrayList<>(indicatorIdSet));
        // 指标
        List<GeneralIndicator> indicators = new ArrayList<>();
        // 将是指标的数据进行筛选出来
        if (!org.springframework.util.CollectionUtils.isEmpty(allIndicators)) {
            for (GeneralIndicator allIndicator : allIndicators) {
                if (0 == allIndicator.getIndicatorType()) {
                    indicators.add(allIndicator);
                }
            }
        }
        return indicators;
    }

    /**
     * 获取指标数据
     * childList    urlIds   urlName   urlType 置为空
     * @param indicatorId
     * @return
     */
    private String getIndicatorDataById(String indicatorId,String sourceSimpleName) {
        if (StringUtils.isBlank(indicatorId)) {
            return Strings.EMPTY;
        }
        IndicatorYuLanReqVO req = new IndicatorYuLanReqVO();
        GeneralIndicatorTianbao indicator = indicatorService.getIndicatorById(indicatorId,sourceSimpleName);
        req.setId(indicator.getParentId());
        req.setPageNum(1);
        req.setPageSize(10000);
        PageInfo<IndicatorYuLanCoreVo> indicatorYuLanCoreVos = generalIndicatorService.listIndicatorByTabId(req);
        List<IndicatorYuLanCoreVo> result = new ArrayList<>();
        if (Objects.nonNull(indicatorYuLanCoreVos)) {
            for (IndicatorYuLanCoreVo indicatorYuLanCoreVo : indicatorYuLanCoreVos.getList()) {
                if (indicatorId.equals(indicatorYuLanCoreVo.getId())) {
                    indicatorYuLanCoreVo.setChildList(new ArrayList<>());
                    indicatorYuLanCoreVo.setUrlIds(Strings.EMPTY);
                    indicatorYuLanCoreVo.setUrlName(Strings.EMPTY);
                    indicatorYuLanCoreVo.setUrlType(Strings.EMPTY);
                    result.add(indicatorYuLanCoreVo);
                }
            }
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 根据预警详情列表获取比较值
     * @param detailList
     * @return
     */
    private String getRealValueByDetails(List<WarnRuleDetail> detailList, Map<String, String> valueMap) {
        StringBuilder result = new StringBuilder();
        for (WarnRuleDetail detail : detailList) {
            String detailId = detail.getDetailId();
            String realValue = valueMap.getOrDefault(detailId, "");
            if (StringUtils.isNotBlank(realValue)) {
                result.append(realValue);
            }
        }
        return result.toString();
    }

    /**
     * 根据预警详情列表获取比较值
     * @param detailList
     * @return
     */
    private String getCompareValueByDetails(List<WarnRuleDetail> detailList) {
        return detailList.stream().map(detail -> detail.getCompareRule()).collect(Collectors.joining(","));
    }

    /**
     * 处理预警结果信息
     * @param detailList 详情列表
     * @param valueMap 实际指标值 map <detailId value>
     * @param valueNameMap 指标值名称 map <warnRuleId + itemValue valueName>
     * @return
     */
    private String dealWarnRuleMsgByDetails(List<WarnRuleDetail> detailList, Map<String, String> valueMap, Map<String, String> valueNameMap) {
        if (CollectionUtils.isEmpty(detailList)) {
            return Strings.EMPTY;
        }
        StringBuilder result = new StringBuilder();
        for (WarnRuleDetail detail : detailList) {
            if (Objects.isNull(detail)) {
                continue;
            }
            // 获取实际指标名、不存在则获取指标名
            String itemName = valueMap.getOrDefault(detail.getDetailId() + ITEM_NAME, detail.getItemName());
            itemName = itemName == null ? "未知项" : itemName;
            String itemValue = detail.getItemValue() == null ? "" : detail.getItemValue().toString();
            String compareRule = detail.getCompareRule() == null ? "" : detail.getCompareRule();
            String compareValue = detail.getCompareValue() == null ? "" : detail.getCompareValue().toString();
            // 获取同环比阈值，默认为对比值
            String ruleValue = valueMap.getOrDefault(detail.getDetailId() + RULE_VALUE, compareValue);
            // 获取指标值名称 key为预警规则id+ 指标值字段名
            String key = detail.getWarnRuleId() + itemValue;
            String itemValueName = valueNameMap.getOrDefault(key, "");
            // 获取实际指标值
            String realvalue = valueMap.getOrDefault(detail.getDetailId(), "");
            if (result.length() > 0) {
                result.append(" ");
            }
            if(ObjectUtils.isEmpty(detail.getItemName())){
                result.append("【")
                        .append(itemValueName)
                        .append("】，指标值为")
                        .append(realvalue)
                        .append("，阈值为")
                        .append(compareRule)
                        .append(ruleValue)
                        .append("；");
            }else{
                result.append("【")
                        .append(itemName)
                        .append("】的【")
                        .append(itemValueName)
                        .append("】，指标值为")
                        .append(realvalue)
                        .append("，阈值为")
                        .append(compareRule)
                        .append(ruleValue)
                        .append("；");
            }
        }
        return result.toString();
    }

    /**
     * 发送预警结果消息给用户
     * @param warnInfoPo 预警信息
     * @param indicatorName 指标名称
     * @param sourceId 版块Id
     */
    private void sendWarnResultMsgToUsers(WarnInfoPo warnInfoPo, String sourceName,String indicatorName, String sourceId) {
        if (Objects.isNull(warnInfoPo) || StringUtils.isBlank(sourceId)) {
            return;
        }
        String userIdsStr = warnInfoPo.getRemindUserIds();
        // 处理用户Id
        if  (2 == warnInfoPo.getRemindType() && StringUtils.isNotBlank(warnInfoPo.getRemindRoleId())) {
            String[] roleIds = warnInfoPo.getRemindRoleId().split(",");
            StringBuilder userIds = new StringBuilder();
            BaseResult<List<SysUser>> roleList = remoteUserService.getUsersByRoleIds(roleIds);
            if (roleList.getCode() == 200 && roleList.getData() != null) {
                warnInfoPo.setSysUserList(roleList.getData());
                for (SysUser sysUser : roleList.getData()) {
                    userIds.append(sysUser.getUserId()).append(",");
                }
            }
            userIdsStr = userIds.toString().substring(0, userIds.length() - 1);
        }
        // 判断是否为空以及是否需要处理
        if (StringUtils.isNotBlank(userIdsStr)) {
            // 将字符串按逗号分割成数组并去重
            Set<String> userIdSet = new HashSet<>(Arrays.asList(userIdsStr.split(",")));
            // 重新拼接为字符串
            userIdsStr = String.join(",", userIdSet);
        }

        Integer warnLevel = warnInfoPo.getWarnLevel();
        String warnLevelDesc = Objects.isNull(warnLevel)|| warnLevel == 1 ? "指标预警" : "指标告警";
        String msg = "收到" + indicatorName + "一条" + warnLevelDesc + "，请及时处理。";
        //发送消息
        appManageUtil.sendAppRemindMsg(sourceName + " " + warnLevelDesc, msg, (byte) 0, "admin",
                userIdsStr, "3", "指标预警", null, "1", null);
    }


    /**
     * 根据预警规则ID、数据源ID和指标ID获取预警规则详情列表
     *
     * @param warnRuleId 预警规则ID
     * @param sourceId 数据源ID
     * @param indicatorId 指标ID
     * @return 预警规则详情列表，如果没有找到相关的预警规则或详情，则返回空列表
     */
    @Override
    public WarnRuleCheckDetailDto getResultWarnRuleByIdAndSourceId(String warnRuleId, String sourceId, String indicatorId) {
        if (StringUtils.isAnyBlank(warnRuleId, sourceId, indicatorId)) {
            return null;
        }
        // 获取指标预警规则
        WarnRule warnRule = selectWarnRuleByIdAndSourceId(warnRuleId, sourceId, indicatorId);
        // 没有设置预警规则 获取未启用，则返回空列表
        if (warnRule == null || warnRule.getWarnStatus() == 0) {
            return null;
        }
        // 获取预警规则详情
        List<WarnRuleDetail> detailList = selectWarnRuleDetailsByWarnRuleId(warnRuleId);
        // 没有设置预警规则详情，则返回空列表
        if (CollectionUtils.isEmpty(detailList)) {
            return null;
        }
        // 遍历预警规则详情
        List<WarnRuleDetail> warnResultList = new ArrayList<>();
        // 记录命中的详情数值
        Map<String, String> valueMap = new HashMap<>();
        // 记录指标值名称
        Map<String, String> valueNameMap = new HashMap<>();
        //  预警计算规则：1-满足全部条件，2-满足任意条件，3-自定义
        String warnCountRule = warnRule.getWarnCountRule();
        boolean warnFlag = false;
        Map<Integer, Boolean> resultMap = new HashMap<>();
        if(1 == warnRule.getDataUpdateMode()){
            resultMap = getWarnResultList(indicatorId, detailList, warnResultList, valueNameMap, valueMap);
        }else{
            resultMap = getWarnResultListByDataSet(indicatorId, detailList, warnResultList, valueNameMap, valueMap);
        }
        if (warnCountRule.equals(WarnRuleConditionEnum.ALL.getValue())) {
            warnFlag = warnResultList.size() > 0;
        } else if (warnCountRule.equals(WarnRuleConditionEnum.ANY.getValue())) {
            warnFlag = warnResultList.size() == detailList.size();
        } else {
            String udfRule = warnRule.getUdfRule();
            // 判断是否非法自定义表达式
            if (Objects.isNull(WarnRuleDIYConditionEnum.valueOfCustom(udfRule))) {
                return null;
            }
            // 处理自定义表达式 判断是否满足条件 满足 返回true的预警详情
            warnFlag = getWarnRuleDiyCondition(udfRule, resultMap, detailList.size());
        }
        // 是否满足预警条件，满足则返回预警现场
        if (warnFlag) {
            return WarnRuleCheckDetailDto.builder().
                    indicatorId(indicatorId).sourceId(sourceId).warnRuleId(warnRuleId).
                    details(warnResultList).valueMap(valueMap).valueNameMap(valueNameMap).build();
        } else {
            return null;
        }
    }

    /**
     * 根据自定义规则和结果映射判断预警规则的DIY条件是否满足
     *
     * @param udfRule 自定义规则字符串，不应为null
     * @param resultMap 包含结果映射的字典，不应为null
     * @param detailSize 详情大小，用于校验resultMap的大小
     * @return 如果满足自定义条件则返回true，否则返回false
     */
    private Boolean getWarnRuleDiyCondition(String udfRule, Map<Integer, Boolean> resultMap, int detailSize) {
        // 检查输入参数是否为空，如果为空则记录错误日志并返回false
        if (StringUtils.isBlank(udfRule) || MapUtils.isEmpty(resultMap)) {
            log.error("监控策略 参数为空，udfRule:{} ", JSONObject.toJSONString(udfRule));
            return false;
        }

        // 将自定义规则字符串转换为枚举类型，以便后续处理
        WarnRuleDIYConditionEnum warnRuleDIYConditionEnum = WarnRuleDIYConditionEnum.valueOfCustom(udfRule);
        // 如果转换失败，记录错误日志并返回false
        if (Objects.isNull(warnRuleDIYConditionEnum)) {
            log.error("监控策略 自定义条件 不存在，预警自定义条件  udfRule:{} ", JSONObject.toJSONString(udfRule));
            return false;
        }

        // 检查子条件数量是否符合自定义表达式数量要求，如果不符合则记录错误日志并返回false
        if (detailSize < warnRuleDIYConditionEnum.getCount()) {
            log.error("监控策略 详情数量不足 ，预警 自定义条件 udfRule:{}, ", JSONObject.toJSONString(udfRule));
            return false;
        }

        // 从resultMap中获取特定索引对应的值，如果不存在则默认为false
        // 假设 index 0=A, 1=B, 2=C
        boolean A = resultMap.getOrDefault(0, false);
        boolean B = resultMap.getOrDefault(1, false);
        boolean C = resultMap.getOrDefault(2, false);

        // 根据枚举类型和获取的值评估表达式，并返回结果
        return evaluateExpression(warnRuleDIYConditionEnum.getValue(), A, B, C);
    }



    /**
     * 计算表达式的布尔值
     * @param expression 表达式字符串
     * @param a 布尔值A
     * @param b 布尔值B
     * @param c 布尔值C
     * @return 表达式的计算结果
     */
    public static boolean evaluateExpression(String expression, boolean a, boolean b, boolean c) {
        // 替换变量
        String replacedExpression = expression.replace("A", Boolean.toString(a))
                .replace("B", Boolean.toString(b))
                .replace("C", Boolean.toString(c));
        try {
            Object execute = AviatorEvaluator.execute(replacedExpression);
            return Boolean.parseBoolean(execute.toString());
        } catch (Exception e) {
            log.error("监控策略 自定义条件逻辑计算异常 计算条件 expression:{},替换后文本:{} ", JSONObject.toJSONString(expression), JSONObject.toJSONString(replacedExpression), e);
            return false;
        }
    }



    private Map<Integer, Boolean> getWarnResultListByDataSet(String indicatorId, List<WarnRuleDetail> detailList, List<WarnRuleDetail> resultList, Map<String, String> valueNameMap, Map<String, String> valueMap) {
        Map<Integer, Boolean> resultMap = new HashMap<>();
        // 遍历预警规则详情
        for (int i = 0; i < detailList.size(); i++) {
            WarnRuleDetail detail = detailList.get(i);
            // 获取字段名称、值
            String warnRuleId = detail.getWarnRuleId();
            String itemName = detail.getItemName();
            String itemValue = detail.getItemValue();

            String compareValue = detail.getCompareValue();
            String compareRule = detail.getCompareRule();
            // 获取运算符枚举
            AviatorOperateTypeEnum aviatorOperateTypeEnum = AviatorOperateTypeEnum.valueOfCustom(compareRule);
            if (aviatorOperateTypeEnum == null) {
                log.error("监控策略比较方式 不存在，预警 id warnRuleId:{},详情 id，detailId:{},详情名称detailName:{} 比较方式compareRule:{}", detail.getWarnRuleId(), detail.getDetailId(), detail.getDetailName(), compareRule);
                continue;
            }
            // 1-为对比某一个值，2-为对比某一列值 2需将itemName置空 即查询所有的数据
            Integer compareType = detail.getCompareType();
            if (compareType == 2) {
                itemName = Strings.EMPTY;
            }
            //查询数据集配置信息
            List<DataConfig> dataConfigList = dataConfigMapper.getDataConfigByIndicatorId(indicatorId);
            if (CollectionUtils.isEmpty(dataConfigList)) {
                log.error("监控策略 数据集不存在，指标id indicatorId:{},数据集名称dataSetName:{}", indicatorId, dataConfigList.get(0).getDataSetName());
                continue;
            }
            for (DataConfig dataConfig : dataConfigList) {
                if(dataConfig.getDataValue().equals(itemValue)){
                    String key = warnRuleId + itemValue;
                    valueNameMap.put(key, dataConfig.getDataValueName());
                    break;
                }
            }
            //查询数据集信息
            Long dataSetId = dataConfigList.get(0).getDataSetId();
            //数据项key
            String dataKey = dataConfigList.get(0).getDataKey();
            try {
            ResponseBean responseBean = reportFeignService.detailById(dataSetId);
            log.info("获取到的数据集数据为data："+responseBean.getData().toString());
            LinkedHashMap dataDto = (LinkedHashMap)responseBean.getData();
            List<Map> dataArray = (List<Map>)dataDto.get("data");
            //取值初始list
            List<IndicatorItemValueVO> values = new ArrayList<>();
            if (dataArray.size() > 0) {
                for (Map object : dataArray) {
                    IndicatorItemValueVO indicatorItemValueVO = new IndicatorItemValueVO();
                    indicatorItemValueVO.setItemName(object.get(dataKey).toString());
                    indicatorItemValueVO.setItemValue(object.get(itemValue).toString());
                    if (compareType == 2) {
                        values.add(indicatorItemValueVO);
                    }else {
                        if (indicatorItemValueVO.getItemName().equals(itemName)) {
                            values.add(indicatorItemValueVO);
                            break;
                        }
                    }
                }
            }
            // 1-固定值对比
            // 遍历指标数据 compareType=1 只有一个 compareType=2 有多个，按排序依次遍历 任意命中即为 true
                Boolean warnResult = false;
                for (IndicatorItemValueVO valueVO : values) {
                    warnResult = getWarnRuleDiyCondition(detail, valueVO, compareValue, aviatorOperateTypeEnum);
                    if (warnResult) {
                        // 赋值 指标项名称 指标值 以便拼接预警结果
                        // 返回 记录指标值
                        valueMap.put(detail.getDetailId(),valueVO.getItemValue());
                        resultList.add(detail);
                        break;
                    }
                }
                resultMap.put(i, warnResult);
            } catch (Exception e) {
                log.error("监控策略 比较转换异常，预警 id warnRuleId:{},详情 id，detailId:{},详情名称detailName:{} 比较方式compareRule:{}，数据值itemValue:{},比较值compareValue:{}", detail.getWarnRuleId(), detail.getDetailId(), detail.getDetailName(), compareRule, itemValue, compareValue, e);
            }
        }
        return resultMap;
    }

    /**
     * 遍历预警详情，返回预警的详情信息
     * @param indicatorId 指标 id
     * @param detailList 预警详情列表
     * @param resultList 命中预警详情列表
     * @param valueMap 命中预警详情指标值
     * @return
     */
    private Map<Integer, Boolean> getWarnResultList(String indicatorId, List<WarnRuleDetail> detailList, List<WarnRuleDetail> resultList, Map<String, String> valueNameMap, Map<String, String> valueMap) {
        // 没有设置预警规则详情，则返回false
        if (CollectionUtils.isEmpty(detailList)) {
            return new HashMap<>();
        }
        Map<Integer, Boolean> resultMap = new HashMap<>();
        // 遍历预警规则详情
        for (int i = 0; i < detailList.size(); i++) {
            WarnRuleDetail detail = detailList.get(i);
            // 获取字段名称、值
            String warnRuleId = detail.getWarnRuleId();
            String itemName = detail.getItemName();
            String itemValue = detail.getItemValue();
            //  获取字段名称 驼峰
            String lowerItemValue = UtilConstants.getItemMap(itemValue);
            String compareValue = detail.getCompareValue();
            String compareRule = detail.getCompareRule();
            // 获取运算符枚举
            AviatorOperateTypeEnum aviatorOperateTypeEnum = AviatorOperateTypeEnum.valueOfCustom(compareRule);
            if (aviatorOperateTypeEnum == null) {
                log.error("监控策略比较方式 不存在，预警 id warnRuleId:{},详情 id，", JSONObject.toJSONString(detail.getWarnRuleId()), JSONObject.toJSONString(detail.getDetailId()));
                continue;
            }
            // 1-为对比某一个值，2-为对比某一列值 2需将itemName置空 即查询所有的数据
            Integer compareType = detail.getCompareType();
            if (compareType == 2) {
                itemName = Strings.EMPTY;
            }
            // 查询指标头信息
            List<GeneralIndicatorDataTitle> titles = indicatorTitleService.queryById(indicatorId);
            // 获取更新周期
            GeneralIndicatorTianbao generalIndicator = indicatorMapper.getInfoOnlyById(indicatorId);
            String updateCycle = Objects.isNull(generalIndicator) ? null : generalIndicator.getUpdateCycle();
            for (GeneralIndicatorDataTitle title : titles) {
                // 遍历 取  itemValue 这里是驼峰
                if (title.getColumnName().equals(lowerItemValue)) {
                    // 记录指标值名称 key为预警规则id+ 指标值字段名
                    String key = warnRuleId + itemValue;
                    valueNameMap.put(key, title.getItemValueName());
                    break;
                }
            }
            // 1-固定值对比；2-环比；3-同比  比较的值为 数值20，并不是 20% 计算时候要注意
            Integer ruleType = detail.getRuleType();
            WarnRuleCompareTypeEnum warnRuleCompareTypeEnum = WarnRuleCompareTypeEnum.valueOfCustom(ruleType);
            // 枚举为空时，默认视为无效规则，直接跳过处理（可根据业务需求调整）
            if (warnRuleCompareTypeEnum == null) {
                resultMap.put(i, false);
                continue;
            }
            List<IndicatorItemValueVO> values = Optional.ofNullable(indicatorService.queryItemValueByNameID(indicatorId, itemName, itemValue, ruleType)).orElse(new ArrayList<>());
            // 条件预警结果
            boolean warnResult = false;
            // 固定值比较逻辑
            if (warnRuleCompareTypeEnum.getValue() == WarnRuleCompareTypeEnum.FIXED_VALUE.getValue()) {
                warnResult = getWarnResultOfFixedValue(detail, compareValue, aviatorOperateTypeEnum, values, resultList, valueMap);
            } else {
                // 同环比处理
                warnResult = getWarnResultOfYoq(values, warnRuleCompareTypeEnum, itemName, compareValue, updateCycle, aviatorOperateTypeEnum, detail, resultList, valueMap);
            }
            resultMap.put(i, warnResult);
        }
        return resultMap;
    }

    /**
     * 获取同环比结果
     * @param values 指标值列表
     * @param compareValue 同环比比较值  比较的值为 数值20，并不是 20%
     * @param aviatorOperateTypeEnum 运算符枚举
     * @param detail 预警规则详情
     * @param resultList 命中预警详情列表
     * @param valueMap 命中预警详情指标值
     * @return
     */
    private boolean getWarnResultOfYoq(List<IndicatorItemValueVO> values, WarnRuleCompareTypeEnum warn, String itemName, String compareValue,String updateCycle, AviatorOperateTypeEnum aviatorOperateTypeEnum, WarnRuleDetail detail, List<WarnRuleDetail> resultList, Map<String, String> valueMap) {
        // 计算同环比 必须要能查出来两条数据
        if(CollectionUtils.isEmpty(values) || values.size() < 2){
            return false;
        }
        Map<String, Integer> valueLinkMap = new LinkedHashMap<>();
        Map<Integer, IndicatorItemValueVO> valueVoLinkMap = new LinkedHashMap<>();
        for (int i = 0; i < values.size(); i++) {
            // 以拼装 数据期和指标项 拼接 key，记录对应的顺序 并存储对应顺序的指标数据 vo
            valueLinkMap.put(values.get(i).getUpdateDate() + values.get(i).getItemName(), i);
            valueVoLinkMap.put(i, values.get(i));
        }
        // 是否选择数据项标识
        boolean itemNameBankFlag = false;
        // 未选数据项时候，过滤 同一数据期 多个数据项的 case
        if (StringUtils.isBlank(itemName)) {
            // 根据数据期 筛选 返回每个数据期对应的 value
            Map<String, List<IndicatorItemValueVO>> updateDateList = values.stream().collect(Collectors.groupingBy(IndicatorItemValueVO::getUpdateDate));
            for (Map.Entry<String, List<IndicatorItemValueVO>> newOne : updateDateList.entrySet()) {
                List<IndicatorItemValueVO> list = newOne.getValue();
                // 最新数据期有多个数据项 过滤掉
                if (list.size() > 1) {
                    return false;
                }
            }
            itemNameBankFlag = true;
            // 未选择 数据项 则默认最新一条
            itemName = values.get(0).getItemName();
        }
        String finalItemName = itemName;
        // 筛选选定的指标项
        Optional<IndicatorItemValueVO> firstOne = values.stream().filter(vo -> finalItemName.equals(vo.getItemName())).findFirst();
        if(!firstOne.isPresent()){
            return false;
        }
        IndicatorItemValueVO first = firstOne.get();
        String itemValue = first.getItemValue();
        // 或者同环比对比指标数据
        IndicatorItemValueVO next = getLastIndicatorItemValueVO(first, warn, values, updateCycle,itemNameBankFlag);
        if (Objects.isNull(next)) {
            return false;
        }
        String lastItemValue = next.getItemValue();
        try {
            // 转换为数字计算 100倍比值 方便同  比较值比较
            double now = Double.parseDouble(itemValue.trim());
            double last = Double.parseDouble(lastItemValue.trim());

            // 上期为 0 时无法计算环比
            if (last == 0) {
                return false;
            }
            // 以20 % 比较为例，实际比较值为 20 故此处*100
            double growthRate = (now - last) * 100 / Math.abs(last);
            // 保留 2 位小数且 四舍五入 转为字符串 与比较值比较
            String growthRateStr = new BigDecimal(growthRate).setScale(2, RoundingMode.HALF_UP).toPlainString();
            Boolean result = AviatorOperateTypeEnum.compareValue(growthRateStr, compareValue, aviatorOperateTypeEnum);
            if (result) {
                // 赋值 指标项名称 指标值 以便拼接预警结果
                // 返回 记录同环比值
                valueMap.put(detail.getDetailId(), itemValue);
                resultList.add(detail);
                // 触发预告警，则记录计算后的阈值 指标名
                valueMap.put(detail.getDetailId() + RULE_VALUE, getThreshold(last, compareValue));
                valueMap.put(detail.getDetailId() + ITEM_NAME, itemName);
            }
            return result;
        } catch (NumberFormatException e) {
            log.error("监控策略 同环比计算异常，预警 id warnRuleId:{},详情 id，detailId:{},详情名称detailName:{} 比较方式compareRule:{}，本期数据值:{}，上次期数据值:{},比较百分比compareValue:{}",
                    JSONObject.toJSONString(detail.getWarnRuleId()), JSONObject.toJSONString(detail.getDetailId()), JSONObject.toJSONString(detail.getDetailName()), JSONObject.toJSONString(aviatorOperateTypeEnum.getValue()),
                    JSONObject.toJSONString(itemValue), JSONObject.toJSONString(lastItemValue), JSONObject.toJSONString(compareValue), e);
            return false;
        }
    }

    /**
     * 获取同环比阈值
     * 保留两位小数但移除末尾 0 ，如 20.00 移除末尾 00
     * @param last
     * @param compareValue
     * @return
     */
    private String getThreshold(double last, String compareValue) {
        // 触发预告警，则记录计算后的阈值 以20 % 比较为例，实际阈值 为 last*1.2
        double ruleValue = last * (1 + Double.parseDouble(compareValue.trim()) / 100);
        BigDecimal number = new BigDecimal(ruleValue).setScale(2, RoundingMode.HALF_UP);
        // 判断是否为整数（即去掉末尾零后 scale <= 0）
        return number.stripTrailingZeros().toPlainString();
    }
    /**
     * 获取同环比数据项
     * @param first 当前指标项
     * @param warn 同环比
     * @param values 指标项列表
     * @param updateCycle 指标更新周期
     * @return 同环比指标项
     */

    private IndicatorItemValueVO getLastIndicatorItemValueVO(IndicatorItemValueVO first, WarnRuleCompareTypeEnum warn, List<IndicatorItemValueVO> values, String updateCycle,boolean itemNameBankFlag) {
        if (CollectionUtils.isEmpty(values) || Objects.isNull(first) || Objects.isNull(warn)) {
            return null;
        }
        // 数据项
        String itemName = first.getItemName();
        // 最新数据期
        String updateDate = first.getUpdateDate();
        // 环比处理
        if (warn.getValue() == WarnRuleCompareTypeEnum.QOQ.getValue()) {
            return getLastNextIndicatorItemValue(values, itemName, updateDate, updateCycle, itemNameBankFlag);
        } else if (warn.getValue() == WarnRuleCompareTypeEnum.YOY.getValue()) {
            // 同比处理
            return getLastYearIndicatorItemValue(values, itemName, updateDate, itemNameBankFlag);
        }

        return null;
    }

    /**
     * 获取环比数据项
     *
     * @param values 指标项列表
     * @param itemName 指标项名称
     * @param updateDate 最新数据期
     * @param updateCycle 指标更新周期
     * @return
     */
    private IndicatorItemValueVO getLastNextIndicatorItemValue(List<IndicatorItemValueVO> values, String itemName, String updateDate, String updateCycle,boolean itemNameBankFlag) {
        if (StringUtils.isAnyBlank(itemName, updateDate)) {
            return null;
        }
        List<IndicatorItemValueVO> itemNameList;
        // 未选择数据项 排序所有数据项
        if(itemNameBankFlag){
            itemNameList = values.stream()
                    .sorted(Comparator.comparing(IndicatorItemValueVO::getUpdateDate).reversed())
                    .collect(Collectors.toList());
        }else {
            // 选择数据项，则过滤指定数据项
            itemNameList = values.stream()
                    .filter(v -> v.getItemName().equals(itemName))
                    .sorted(Comparator.comparing(IndicatorItemValueVO::getUpdateDate).reversed())
                    .collect(Collectors.toList());
        }
        // 筛选 同环比目标值
        Map<String, Integer> valueLinkMap = new LinkedHashMap<>();
        Map<Integer, IndicatorItemValueVO> valueVoLinkMap = new LinkedHashMap<>();
        for (int i = 0; i < itemNameList.size(); i++) {
            // 以拼装 数据期 key，记录对应的顺序 并存储对应顺序的指标数据 vo
            valueLinkMap.put(itemNameList.get(i).getUpdateDate(), i);
            valueVoLinkMap.put(i, itemNameList.get(i));
        }
        // 获取 上一个数据期
        String next = getPreviousCycleTime(updateDate, UpdateCycleEnum.valueOfCustom(updateCycle));
        if (StringUtils.isBlank(next) || Objects.isNull(valueLinkMap.getOrDefault(next, null))) {
            return null;
        }
        // 取下一个数据期即是环比的目标数据对象
        return valueVoLinkMap.getOrDefault(valueLinkMap.get(next), null);
    }

    /**
     * 获取同比数据项
     *
     * @param values
     * @param itemName
     * @param updateDate
     * @return
     */
    private IndicatorItemValueVO getLastYearIndicatorItemValue(List<IndicatorItemValueVO> values, String itemName, String updateDate,boolean itemNameBankFlag) {
        if (StringUtils.isAnyBlank(itemName, updateDate)) {
            return null;
        }
        // 根据 指标项名称和数据期 拼接 key 若不选指标项则不选指标项
        String key = itemNameBankFlag ? updateDate : updateDate + itemName;

        // 筛选 同比目标值
        Map<String, Integer> valueLinkMap = new LinkedHashMap<>();
        Map<Integer, IndicatorItemValueVO> valueVoLinkMap = new LinkedHashMap<>();
        for (int i = 0; i < values.size(); i++) {
            String updateDateTemp = values.get(i).getUpdateDate();
            String itemNameTemp = values.get(i).getItemName();
            String keyTemp  = itemNameBankFlag?updateDateTemp:updateDateTemp+itemNameTemp;
            // 以拼装 数据期和指标项 拼接 key，记录对应的顺序 并存储对应顺序的指标数据 vo
            valueLinkMap.put(keyTemp, i);
            valueVoLinkMap.put(i, values.get(i));
        }
        Integer firstSort = valueLinkMap.getOrDefault(key, null);
        if (Objects.isNull(firstSort)) {
            return null;
        }
        // 同比处理 获取上一年的数据期
        String lastYearUpdateDate = UpdateCycleUtils.getLastYearUpdateDate(updateDate);
        if (StringUtils.isBlank(lastYearUpdateDate)) {
            return null;
        }
        // 根据 指标项名称和数据期 拼接 key 若不选指标项则不选指标项
        String lastKey = itemNameBankFlag ? lastYearUpdateDate : lastYearUpdateDate + itemName;
        // 获取所在数组 的排序值
        Integer lastYearSort = valueLinkMap.getOrDefault(lastKey, null);
        return Objects.isNull(lastYearSort) ? null : valueVoLinkMap.getOrDefault(lastYearSort, null);
    }


    /**
     * 根据更新周期类型，获取上一个周期的时间字符串
     *
     * @param dateStr 当前周期时间（如 "2025", "2025-上半年", "2025-Q2", "2025-06"）
     * @param cycleType 周期类型（"年度更新", "半年更新", "季度更新", "月度更新"）
     * @return 上一个周期时间字符串
     */
    public static String getPreviousCycleTime(String dateStr, UpdateCycleEnum cycleType) {
        if (dateStr == null || cycleType == null) return null;
        switch (cycleType) {
            case YEARLY:
                return UpdateCycleUtils.getPreviousYear(dateStr);
            case SEMI_ANNUAL:
                return UpdateCycleUtils.getPreviousSemiAnnual(dateStr);
            case QUARTERLY:
                return UpdateCycleUtils.getPreviousQuarter(dateStr);
            case MONTHLY:
                return UpdateCycleUtils.getPreviousMonth(dateStr);
            default:
                return null;
        }
    }


    /**
     * 处理固定值对比
     * @param detail 预警规则详情
     * @param compareValue 比较目标值
     * @param aviatorOperateTypeEnum 比较方式
     * @param values 指标数据
     * @param resultList 命中预警详情列表
     * @param valueMap 命中预警详情指标值
     */
    private boolean getWarnResultOfFixedValue( WarnRuleDetail detail, String compareValue, AviatorOperateTypeEnum aviatorOperateTypeEnum, List<IndicatorItemValueVO> values, List<WarnRuleDetail> resultList, Map<String, String> valueMap) {
        // 遍历指标数据 compareType=1 只有一个 compareType=2 有多个，按排序依次遍历 任意命中即为 true
        boolean warnResult = false;
        for (IndicatorItemValueVO valueVO : values) {
            warnResult = getWarnRuleDiyCondition(detail, valueVO, compareValue, aviatorOperateTypeEnum);
            if (warnResult) {
                // 赋值 指标项名称 指标值 以便拼接预警结果
                // 返回 记录指标值
                valueMap.put(detail.getDetailId(), valueVO.getItemValue());
                valueMap.put(detail.getDetailId() + ITEM_NAME, valueVO.getItemName());
                resultList.add(detail);
                break;
            }
        }
        return warnResult;
    }


    /**
     * 自定义条件 某一列比较逻辑
     *
     * @param detail
     * @param valueVO
     * @param compareValue
     * @param aviatorOperateTypeEnum
     * @return
     */
    private Boolean getWarnRuleDiyCondition(WarnRuleDetail detail, IndicatorItemValueVO valueVO, String compareValue, AviatorOperateTypeEnum aviatorOperateTypeEnum) {
        if (Objects.isNull(valueVO) || StringUtils.isBlank(valueVO.getItemValue())) {
            log.error("监控策略 数据项数据值为空,，预警 id warnRuleId:{},详情 id，detailId:{},详情名称detailName:{} 比较值compareValue:{}", JSONObject.toJSONString(detail.getWarnRuleId()), JSONObject.toJSONString(detail.getDetailId()), JSONObject.toJSONString(detail.getDetailName()), JSONObject.toJSONString(compareValue));
            return false;
        }
        return AviatorOperateTypeEnum.compareValue(valueVO.getItemValue(), compareValue, aviatorOperateTypeEnum);
    }

    /**
     * 获取全部的预警规则及相应的预警结果列表
     *
     * @param queryDto 查询条件
     * @return 预警规则及结果列表
     */
    @Override
    public List<WarnRuleAndResult> getWarnRuleAndResultList(WarnRuleAndResultQueryDto queryDto) {
        return warnRuleMapper.getWarnRuleAndResultList(queryDto);
    }
}
