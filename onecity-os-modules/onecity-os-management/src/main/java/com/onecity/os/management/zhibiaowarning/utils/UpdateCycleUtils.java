package com.onecity.os.management.zhibiaowarning.utils;

import com.onecity.os.management.zhibiaowarning.aviator.constants.enums.UpdateCycleEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/6/18
 * @ClassName: UpdateCycleUtils
 * @Description: 更新周期工具类，用于获取环比数据的时间计算
 * @Version 1.0
 */
public class UpdateCycleUtils {

    /**
     * 获取上一年
     *
     * 示例："2025" → "2024"
     */
    public static String getPreviousYear(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) return null;
        try {
            int year = Integer.parseInt(dateStr);
            return String.valueOf(year - 1);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 获取上一个半年周期
     *
     * 示例："2025-上半年" → "2024-下半年"
     * 示例："2025-下半年" → "2025-上半年"
     */
    public static String getPreviousSemiAnnual(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) return null;
        String[] parts = dateStr.split("-");
        if (parts.length != 2) return null;

        int year;
        try {
            year = Integer.parseInt(parts[0]);
        } catch (NumberFormatException e) {
            return null;
        }

        String half = parts[1];

        if ("上半年".equals(half)) {
            return (year - 1) + "-下半年";
        } else if ("下半年".equals(half)) {
            return year + "-上半年";
        }

        return null;
    }

    /**
     * 获取上一个季度
     *
     * 示例："2025-第一季度" → "2024-第四季度"
     * 示例："2025-第二季度" → "2025-第一季度"
     */
    public static String getPreviousQuarter(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) return null;
        String[] parts = dateStr.split("-");
        if (parts.length != 2) return null;

        int year;
        try {
            year = Integer.parseInt(parts[0]);
        } catch (NumberFormatException e) {
            return null;
        }

        String quarter = parts[1];
        Map<String, String> quarterMap = UpdateCycleEnum.quarterMap();

        String qNumStr = quarterMap.get(quarter);
        if (qNumStr == null) return null;

        int qNum = Integer.parseInt(qNumStr);

        if (qNum == 1) {
            year -= 1;
            qNum = 4;
        } else {
            qNum -= 1;
        }

        return year + "-" + quarterMap.get(String.valueOf(qNum));
    }

    /**
     * 获取上一个月
     *
     * 示例："2025-06" → "2025-05"
     * 示例："2025-01" → "2024-12"
     */
    public static String getPreviousMonth(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) return null;
        String[] parts = dateStr.split("-");
        if (parts.length != 2) return null;

        int year, month;
        try {
            year = Integer.parseInt(parts[0]);
            month = Integer.parseInt(parts[1]);
        } catch (NumberFormatException e) {
            return null;
        }

        if (month == 1) {
            year -= 1;
            month = 12;
        } else {
            month -= 1;
        }

        return String.format("%d-%02d", year, month);
    }
    /**
     * 获取上一年数据期
     * @param updateDate
     * @return
     */
    public static String getLastYearUpdateDate(String updateDate) {
        if (StringUtils.isBlank(updateDate)) {
            return null;
        }

        // 提取年份部分
        int year;
        try {
            if (updateDate.length() >= 4) {
                // 尝试从字符串前四位提取年份
                year = Integer.parseInt(updateDate.substring(0, 4));
            } else {
                // 如果长度小于4位，则尝试直接解析为年份
                year = Integer.parseInt(updateDate);
            }
        } catch (NumberFormatException e) {
            return null;
        }

        // 计算去年
        int lastYear = year - 1;

        // 如果原始字符串包含更多格式（如年月），保留后面的部分
        String suffix = "";
        if (updateDate.length() > 4) {
            suffix = updateDate.substring(4);
        }

        // 返回拼接结果
        return lastYear + suffix;
    }
}