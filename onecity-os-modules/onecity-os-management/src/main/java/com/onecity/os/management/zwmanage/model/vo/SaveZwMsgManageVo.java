package com.onecity.os.management.zwmanage.model.vo;

import io.swagger.models.auth.In;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/10/30 14:57
 */
@Data
@NoArgsConstructor
public class SaveZwMsgManageVo {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空")
    private String name;

    /**
     * 类型:1-长政信息; 2-长政简报
     */
    @NotNull(message = "类型不能为空")
    private String type;

    /**
     * 标签
     */
    private String label;

    /**
     * 期刊号
     */
    private String journalNum;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 图片地址
     */
    private String picPath;

    /**
     * 图片展示形式:1-大图展示;2-缩略展示
     */
    private Integer picIsBig;

    /**
     * 内容形式:1-文本;2-pdf
     */
    @NotNull(message = "内容形式:1-文本;2-pdf不能为空")
    private Integer contentFormate;

    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空")
    private String content;

}
