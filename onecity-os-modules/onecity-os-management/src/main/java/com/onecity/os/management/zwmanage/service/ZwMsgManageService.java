package com.onecity.os.management.zwmanage.service;



import com.onecity.os.management.zwmanage.model.dto.GetZwMsgManageListDto;
import com.onecity.os.management.zwmanage.model.vo.SaveZwMsgManageVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/30 14:32
 */
public interface ZwMsgManageService {
    /**
     * 根据参数查询政务信息列表(分页)
     *
     * @param name
     * @param type
     * @return
     */
    List<GetZwMsgManageListDto> getZwMsgManageList(String name, Integer type);

    /**
     * 新增/修改政务信息管理
     *
     * @param vo
     */
    int saveZwMsgManage(SaveZwMsgManageVo vo);

    /**
     * 根据政务信息id,删除政务信息
     *
     * @param id
     * @return
     */
    int deleteZwMsgManageById(Long id);
}












