server:
  port: 8080
# spring配置
spring:
  servlet:
    multipart:
      # 根据实际需求作调整
      max-file-size: 10MB
      # 默认最大请求大小为10M，总上传的数据大小
      max-request-size: 15MB
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  redis:
    host: onecity-os-redis
#    host: *************
    port: 6379
#    port: 30425
    password: DONT(qOabvXt6cR+BDQvtcBU6cbRv3KOdJFEmAPdN7Vo4b4rtkA0VqsPe5SXJrcIYMRhN4vV3+CPWcxs05elMVxPz28Akqgr408GaQRGEPXoT/2WAAz4u9ozHv+xGePp44vW7onza4utNLIzC0ymGmp2vapMuFO+6iQpX5eWm39s3TWM=)
  datasource:
    druid:
      initialSize: 5
      minIdle: 5
      maxActive: 20
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      filters: stat,slf4j
      connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      druid:
        initialSize: 5
        minIdle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j
      datasource:
          # 主库数据源
          master:
            driver-class-name: com.mysql.cj.jdbc.Driver
#            url: jdbc:mysql://*************:31286/service-disposal?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
            url: ******************************************************************************************************************************************************************************************
#            url: ****************************************************************************************************************************************************************
            username: root
            password: DONT(qOabvXt6cR+BDQvtcBU6cbRv3KOdJFEmAPdN7Vo4b4rtkA0VqsPe5SXJrcIYMRhN4vV3+CPWcxs05elMVxPz28Akqgr408GaQRGEPXoT/2WAAz4u9ozHv+xGePp44vW7onza4utNLIzC0ymGmp2vapMuFO+6iQpX5eWm39s3TWM=)
          # 从库数据源
          # slave:
            # username:
            # password:
            # url:
            # driver-class-name:
      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭
# seata配置
seata:
  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启
  enabled: false
  # Seata 应用编号，默认为 ${spring.application.name}
  application-id: ${spring.application.name}
  # Seata 事务组编号，用于 TC 集群名
  tx-service-group: ${spring.application.name}-group
  # 关闭自动代理
  enable-auto-data-source-proxy: false
  # 服务配置项
  service:
    # 虚拟组和分组的映射
    vgroup-mapping:
      ruoyi-system-group: default
  config:
    type: nacos
    nacos:
#      serverAddr: *************:30309
      serverAddr: onecity-os-nacos:8848
      group: SEATA_GROUP
      namespace:
  registry:
    type: nacos
    nacos:
      application: seata-server
#      server-addr: *************:30309
      server-addr: onecity-os-nacos:8848
      namespace:

# mybatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.disposal
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath:mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#Mybatis输出sql日志
logging:
  level:
    com.onecity.os.disposal.mapper: info
    com.onecity.os.management.buildHall.ensureHouseProject.mapper: info
    com.onecity.os.management.buildHall.realEstateDev.mapper: info
# swagger配置
swagger:
  title: management模块接口文档
  license: Powered By ruoyi
  licenseUrl: https://ruoyi.vip

#阅批呈报编码
ypcb:
  sourceSimpleName: tbcb

# fdfs配置
fdfs:
  soTimeout: 1500 #socket连接超时时长
  connectTimeout: 600 #连接tracker服务器超时时长
  thumbImage: #缩略图生成参数，可选
   width: 150
   height: 150
  web-server-url: onecity-os-fastdfs-storage:22122
#  web-server-url: *************:31968
  trackerList: #TrackerList参数,支持多个，我这里只有一个，如果有多个在下方加- x.x.x.x:port
  - onecity-os-fastdfs-tracker:22122
#  - *************:31566
springfox:
  documentation:
    # 总开关（同时设置auto-startup=false，否则/v3/api-docs等接口仍能继续访问）
    enabled: false
    auto-startup: false
    swagger-ui:
      enabled: false
#
indicator:
  template:
    file:
      path: C://模板
  manage:
    permission:
      id: 100000000
indicatorData:
  manage:
    permission:
      id: 200000000
source:
  manage:
    permission:
      id: 1095
dingding:
  #演示版
#  CorpId: dingdebdf9aead7b1adcacaaa37764f94726
#  AgentId: 1651527232
#  AppKey: dingx7c3abazl08nbtcn
#  AppSecret: 9VyGZ4OW5Muy2_zJv0nr90N6GIIsriKCApEfo34ZFBSyE_mVdzYr4xVochsvcaPi
#  DingPanAgentId: 449580876
#  SignedUrl: http://111.63.48.25:32017/app-portal/app/#/home
#  DingRoleGroupName: 驾驶舱演示版
  #  开发版
  #  CorpId: ding8a5bd4a1b56aa0baee0f45d8e4f7c288
  #  AppKey: dingplldmz0gskm4tfru
  #  AppSecret: b1NDlurw-1x4t5PK4ulZdUrNQYzg5pa_jpL9HS1f5JJhmrxbnCQe8uYnaMtgTk5c
  #  AgentId: 825067087
  #  CorpId: ding8a5bd4a1b56aa0baee0f45d8e4f7c288
  #  AgentId: 1649984656
  #  AppKey: dings4gtmlvlxwqk7wbq
  #  AppSecret: GJSPSd8v0Rc6w5YdRVUsVJyfTGsaPgSeWkPPx2qqh-oTGc4zPMv3r6aS9aqZnWHM
  #  DingPanAgentId: 449580876
  #  SignedUrl: http://111.63.48.25:61334/app/#/home
  #  DingRoleGroupName: 驾驶舱
  CorpId: ding2f6e657a3893596724f2f5cc6abecb85
  AppKey: ding0kwcm5cz3xsne64h
  AppSecret: d_YNk8D6FbIYmqKqOSXZ1oMIgu6ZlV6ucRUqDJhztX2PL9h3yTgj8xmiUhqPAYdf
  AgentId: 1651556269
  DingPanAgentId: 449580876
  SignedUrl: http://111.63.48.25:30461/app/#/home
  DingRoleGroupName: 驾驶舱
  DingRoleLevel: 1,2,3,4,5
  DingRoleLevelName: 一级职位,二级职位,三级职位,四级职位,五级职位
  #钉钉向上查看通讯录范围, 应为通讯录为五级,所有 5 就是向上看到所有的
  DingRoleLevelUp: 5
  #钉钉向下查看通讯录范围
  DingRoleLevelDown: 2
  #发送APP通知消息，messageUrl
  picUrl: http://39.100.105.105:8080/group1/M00/00/00/rBriW18nfzWAfeNbAAAMBM970cM469.png
  baseMessageUrl: http://111.63.48.25:32753/app-portal/app/#/
  superviseMessageUrl: sxdb
  indicatorWarnMessageUrl: zbyj
  indicatorMessageUrl: myInterest
  disposalMessageUrl: ldps?active=1
  noticeMessageUrl: notice
  role:
    groupid: 2855243959

#XXL-job配置
jsc:
  xxljob:
    ##是否启用xxljob
    enabled: true
    ### 调度中心部署跟地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
    adminAddresses: http://onecity-os-backend-job:8080/xxl-job-admin
    ### 执行器通讯TOKEN [选填]：非空时启用；
    accessToken: jiashicang20179aSr@Mg%df25
    ### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
    appname: ${spring.application.name}
    ### 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。
    address:
    ### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
    ip:
    ### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
    port: 0
    ### 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
    logPath: ./logs/xxl-job/jobhandler
    ### 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
    logRetentionDays: 30
city:
  url: http://**************:32018/service-management/sync/indicator/getIndicatorAndDataList
  url1: http://**************:32018/service-management/sync/indicator/getTargetTreeList
  url2: http://**************:32018/service-management/sync/indicator/getIndicatorDetailListProv
userSync:
  requestId: C81E744DBD30000188E1F7101AE747A0
  appId: zyg5f4rbapfv9q
  appSecret: XXXzyYIRGTAQ9ie06y5Mbk9YWxYKjqWfEd0XXXX
  url: http://************:32728/uip/foreign/sync
  path: /userList
  tenementId: 4c3321e2343943d98ef0785fd4e7f790
other:
  videoUrl: http://*************:8060/robot/liveStream?deviceId=34020000001310000002&playType=1&subDeviceId=34020000001310000002&type=2
feign:
  compression:
    request:
      min-request-size: 30000
