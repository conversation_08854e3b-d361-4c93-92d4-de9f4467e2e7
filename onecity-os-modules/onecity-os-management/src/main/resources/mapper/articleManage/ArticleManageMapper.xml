<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.articleManage.mapper.ArticleManageMapper">

    <select id="selectPageByNameAndType" resultType="com.onecity.os.management.articleManage.model.dto.GetArticleManageListDto">
        SELECT zmm.id as id,zmm.name as name,zmm.journal_num as journalNum,zmm.type as typeValue,
        zmm.label as label,zmm.file_name as fileName,zmm.pic_path as picPath,zmm.pic_is_big as picIsBig,
        zmm.content_formate as contentFormate,zmm.content as content,zmm.is_delete as isDelete,zmm.creater as creater,
        zmm.create_time as createTime,zmm.updater as updater,zmm.update_time as updateTime
        from article_manage zmm
        WHERE zmm.is_delete = 0
        and source_id=#{sourceId}
        <if test="name!=null and name!=''">
            and INSTR(zmm.name,#{name})
        </if>
        <if test="type!=null and type!='' or type==0">
            and zmm.type = #{type}
        </if>
        order by zmm.create_time desc
    </select>

    <select id="getListByType" resultType="com.onecity.os.management.articleManage.entity.ArticleAppManage">
    SELECT * from article_manage where type = #{type} and source_id=#{sourceId} and is_delete = 0
    order by create_time DESC
  </select>

</mapper>















