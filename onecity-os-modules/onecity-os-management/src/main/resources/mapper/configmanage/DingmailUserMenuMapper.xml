<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.onecity.os.management.configmanage.mapper.DingmailUserMenuMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.management.configmanage.entity.DingmailUserMenu">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="source_id" property="sourceId" jdbcType="VARCHAR"/>
        <result column="creater" property="creater" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="getDingDingConfigPageList"
            resultType="com.onecity.os.management.configmanage.entity.dto.GetDingDingConfigPageListDto">
        SELECT
        a.userid AS userId,
        a.`name` AS userName,
        a.mobile AS phone,
        a.position AS `position`,
        b.`name` AS depart,
        c.creater AS updater,
        c.create_time AS updateTime,
        a.departments AS departmentIds,
        a.roles
        FROM
        dingmail_user a
        LEFT JOIN dingmail_department b ON b.departmentid = a.departments
        LEFT JOIN dingmail_user_menu c ON c.user_id = a.userid
        where
        1=1
        <if test="null != name and '' != name">
            and INSTR(a.`name`,#{name})
        </if>
        <if test="null != phone and '' != phone">
            and INSTR(a.mobile,#{phone})
        </if>
        <if test="null != position and '' != position">
            and INSTR(a.position,#{position})
        </if>
        <if test="null != departIds and '' != departIds">
            AND concat(',',a.departments,',') regexp concat(#{departIds})
        </if>
        <if test="null == departIds and '' != depart">
            AND a.departments = ''
        </if>
        GROUP BY a.userid
        ORDER BY a.orderindepts
    </select>

    <select id="getDingDingMenuList"
            resultType="com.onecity.os.management.configmanage.entity.dto.GetDingDingMenuListDto">
        SELECT
        id,
        source_name,
        source_simple_name,
        `type`,
        sequence,
        is_indicators
        FROM
        source_manage
        WHERE
        is_delete = 0
        AND is_start = 1
        <if test="null != name and '' != name">
            AND INSTR(source_name,#{name})
        </if>
        ORDER BY
        sequence
    </select>

    <update id="updateDingUserUpdateTimeByUserId">
        UPDATE
        dingmail_user
        SET updater = #{loginName},
        update_time = #{date}
        WHERE
            userid = #{userId}
    </update>

    <select id="getSourceIdByUserId" resultType="com.onecity.os.management.configmanage.entity.vo.DingDingUserMenuVo">
        SELECT
            a.source_id AS id,
            a.indicators_permissions
        FROM
            dingmail_user_menu a
            LEFT JOIN source_manage b ON b.id = a.source_id
        WHERE
            a.user_id = #{userId}
            AND b.is_delete = 0
            AND b.is_start = 1
        ORDER BY b.sequence
    </select>

    <select id="getDingDingMenuSzjgOrQxzh"
            resultType="com.onecity.os.management.configmanage.entity.dto.GetDingDingMenuListDto">
        SELECT
            id,
            source_name,
            source_simple_name,
            `type`,
            sequence
        FROM
            source_manage
        WHERE
            is_delete = 0
            AND is_start = 1
            AND `type` = 'zdy'
            AND source_simple_name = #{name}
            LIMIT 1
    </select>

    <select id="getDingDingConfigByDepartIdPageList"
            resultType="com.onecity.os.management.configmanage.entity.dto.GetDingDingConfigPageListDto">
        SELECT
        a.userid AS userId,
        a.`name` AS userName,
        a.mobile AS phone,
        a.position AS `position`,
        b.`name` AS depart,
        c.creater AS updater,
        c.create_time AS updateTime,
        a.departments AS departmentIds,
        a.roles,
        json_extract_c(a.orderindepts, "$.#{departId}") AS orderNew
        FROM
        dingmail_user a
        LEFT JOIN dingmail_department b ON b.departmentid = a.departments
        LEFT JOIN dingmail_user_menu c ON c.user_id = a.userid
        where 1=1
        <if test="null != departId and  '' != departId">
            and FIND_IN_SET(#{departId}, a.departments)
        </if>
        <if test="null != name and '' != name">
            and INSTR(a.`name`,#{name})
        </if>
        <if test="null != phone and '' != phone">
            and INSTR(a.mobile,#{phone})
        </if>
        <if test="null != position and '' != position">
            and INSTR(a.position,#{position})
        </if>
        GROUP BY a.userid
        ORDER BY orderNew desc
    </select>

    <select id="exportDingConfig"
            resultType="com.onecity.os.management.configmanage.entity.dto.DingConExcel">
        SELECT
               u.userid   AS userId,
               u.`name`   AS userName,
               INSERT ( u.mobile , 4, 4, '****' ) AS mobile,
               u.position AS `position`,
               dp.`name`  AS depart,
        json_extract_c(u.orderindepts, "$.#{departId}") AS orderNew
        FROM dingmail_user u
                 LEFT JOIN dingmail_department dp ON dp.departmentid = u.departments
        where 1=1
        <if test="null != departId and  '' != departId">
            and FIND_IN_SET(#{departId}, u.departments)
        </if>
        <if test="null != name and '' != name">
            and INSTR(u.`name`,#{name})
        </if>
        <if test="null != phone and '' != phone">
            and INSTR(u.mobile,#{phone})
        </if>
        <if test="null != position and '' != position">
            and INSTR(u.position,#{position})
        </if>
        ORDER BY orderNew desc
    </select>

    <select id="getSourceByUserId" resultType="java.lang.String">
        SELECT
            m.source_name AS sourceName
        FROM
            dingmail_user_menu u
                LEFT JOIN source_manage m ON m.id = u.source_id
        WHERE
            u.user_id = #{userId}
          AND m.is_delete = 0
          AND m.is_start = 1
        ORDER BY m.sequence
    </select>

    <select id="getPermissionsByUserId" resultType="java.lang.String">
        SELECT du.indicators_permissions FROM general_indicator ge
        LEFT JOIN source_manage sm on ge.source_id = sm.source_simple_name
        LEFT JOIN role_source du on sm.id = du.source_id
        WHERE ge.id = #{indicatorId} AND du.role_id in
        <foreach collection="roleList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getSupervisePermissions" resultType="java.lang.String">
        SELECT du.supervise_permissions FROM general_indicator ge
        LEFT JOIN source_manage sm on ge.source_id = sm.source_simple_name
        LEFT JOIN role_source du on sm.id = du.source_id
        WHERE ge.id = #{indicatorId} AND du.role_id in
        <foreach collection="roleList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getInformPermissions" resultType="com.onecity.os.management.configmanage.entity.RoleSource">
        select * from role_source
        WHERE source_id = #{sourceId} and role_id in
        <foreach collection="roleList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getRedPermissions" resultType="java.lang.String">
        SELECT du.red_permissions FROM general_indicator ge
        LEFT JOIN source_manage sm on ge.source_id = sm.source_simple_name
        LEFT JOIN role_source du on sm.id = du.source_id
        WHERE ge.id = #{indicatorId} AND du.role_id in
        <foreach collection="roleList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="checkUserCircleApprovalPermission" resultType="java.lang.Long">
        select rs.id from role_source rs left join source_manage sm on rs.source_id = sm.id
        where sm.is_indicators = 4 AND sm.is_delete = 0
        AND sm.is_start = 1 and rs.role_id in
        <foreach collection="roleList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <delete id="deleteByUserId" parameterType="java.lang.String">
		delete from dingmail_user_menu where user_id = #{userId}
	</delete>
</mapper>















