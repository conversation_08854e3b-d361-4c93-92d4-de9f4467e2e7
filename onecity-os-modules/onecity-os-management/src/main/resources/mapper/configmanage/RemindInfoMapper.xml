<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.configmanage.mapper.RemindInfoMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.management.configmanage.entity.RemindInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="source_id" jdbcType="BIGINT" property="sourceId"/>
        <result column="remind_title" jdbcType="VARCHAR" property="remindTitle"/>
        <result column="is_read" jdbcType="TINYINT" property="isRead"/>
        <result column="reminded_count" jdbcType="TINYINT" property="remindedCount"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="creater" jdbcType="VARCHAR" property="creater"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="remind_content" jdbcType="LONGVARCHAR" property="remindContent"/>
        <result column="pc_user_ids" jdbcType="LONGVARCHAR" property="pcUserIds"/>
        <result column="app_user_ids" jdbcType="LONGVARCHAR" property="appUserIds"/>
    </resultMap>

    <select id="getRemindInfoPageList" resultType="com.onecity.os.management.configmanage.entity.RemindInfo">
        SELECT *
        FROM remind_info
        WHERE is_delete = 0
          AND FIND_IN_SET(#{userId}, pc_user_ids)
        ORDER BY create_time DESC,
                 is_read DESC
    </select>

    <select id="getRemindForMsg" resultType="com.onecity.os.management.configmanage.entity.RemindInfo">
        SELECT *
        FROM remind_info
        WHERE is_delete = 0
          AND type IN ("MON_REMIND", "OVERDUE_3_REMIND", "OVERDUE_1_REMIND")
          AND DATE_FORMAT(remind_date,'%Y%m%d')=DATE_FORMAT(CURDATE(),'%Y%m%d')
    </select>

    <update id="updateIsRead">
        update remind_info set is_read = 1 where id = #{id}
    </update>
</mapper>