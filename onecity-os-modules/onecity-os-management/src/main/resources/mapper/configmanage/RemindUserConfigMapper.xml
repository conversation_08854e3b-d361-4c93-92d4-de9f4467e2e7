<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.onecity.os.management.configmanage.mapper.RemindUserConfigMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.management.configmanage.entity.RemindUserConfig">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="source_id" property="sourceId" jdbcType="BIGINT"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="pc_user_ids" property="pcUserIds" jdbcType="LONGVARCHAR"/>
        <result column="app_user_ids" property="appUserIds" jdbcType="LONGVARCHAR"/>
    </resultMap>

    <select id="getRemindPageList"
            resultType="com.onecity.os.management.configmanage.entity.dto.GetRemindConfigPageListDto">
        SELECT
        a.id,
        a.source_name AS sourceName,
        a.sequence AS sequence,
        IFNULL(c.source_name,"其他") AS type,
        b.id AS zhujianid,
        b.pc_user_ids AS pcUserIds,
        b.app_user_ids AS appUserIds,
        b.updater AS updater,
        b.update_time AS updateTime
        FROM
        source_manage a
        LEFT JOIN remind_user_config b ON b.source_id = a.id AND b.is_delete = 0
        LEFT JOIN source_manage c on a.type = c.source_simple_name
        WHERE
        a.is_start = 1
        AND a.is_delete = 0
        AND a.is_indicators in (1,5)
        <if test="null != type and '' != type">
            AND a.type = #{type}
        </if>
        <if test="null != sourceName and '' != sourceName">
            AND INSTR(a.source_name, #{sourceName})
        </if>
        ORDER BY
        a.sequence
    </select>

    <select id="getPcUserNamesByPcUserIds" resultType="String">
        SELECT
        group_concat( realname )
        FROM
        sys_user
        WHERE
        del_flag = 0
        AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getPcUserIdsByPcUserNames" resultType="String">
        SELECT
        group_concat( id )
        FROM
        sys_user
        WHERE
        del_flag = 0
        AND realname IN
        <foreach collection="names" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>
    </select>

    <select id="getAppUserNamesByPcUserIds" resultType="String">
        SELECT
        group_concat( `name` )
        FROM
        dingmail_user
        WHERE
        userid IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getAppUserIdsByPcUserNames" resultType="String">
        SELECT
        group_concat( userid )
        FROM
        dingmail_user
        WHERE
        `name` IN
        <foreach collection="names" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>
    </select>

    <select id="getRemindInfoList"
            resultType="com.onecity.os.management.configmanage.entity.dto.RemindUserConfigDto">
        SELECT
            a.*,
            b.source_simple_name AS sourceSimpleName,
            b.source_name AS sourceName
        FROM
            remind_user_config a
            LEFT JOIN source_manage b ON b.id = a.source_id
        WHERE
            a.is_delete = 0
            AND b.is_delete = 0
            AND b.is_start = 1
        GROUP BY
            b.id
    </select>

    <select id="getRemindInfoBySourceId"
            resultType="com.onecity.os.management.configmanage.entity.dto.RemindUserConfigDto">
        SELECT
            a.*,
            b.source_simple_name AS sourceSimpleName,
            b.source_name AS sourceName
        FROM
            remind_user_config a
                LEFT JOIN source_manage b ON b.id = a.source_id
        WHERE
            a.is_delete = 0
          AND b.is_delete = 0
          AND b.is_start = 1
          AND a.source_id = #{sourceId}
        GROUP BY
            b.id
    </select>

    <update id="updatePcUserIdsById">
        UPDATE remind_user_config
        SET pc_user_ids = #{vo.pcUserIds}
        WHERE
            id = #{vo.id}
    </update>

    <update id="updateAppUserIdsById">
        UPDATE remind_user_config
        SET app_user_ids = #{vo.appUserIds}
        WHERE
            id = #{vo.id}
    </update>

    <select id="getAllStartedTjSourceList"
            resultType="com.onecity.os.management.configmanage.entity.dto.RemindUserConfigDto">
        SELECT
            id AS sourceId,
            source_simple_name AS sourceSimpleName,
            source_name AS sourceName
        FROM
            source_manage
        WHERE
            is_delete = 0
            AND is_start = 1
        GROUP BY
            source_simple_name
    </select>

    <select id="exportRemindConfigXls"
            resultType="com.onecity.os.management.configmanage.entity.dto.ExportRemindConfigXlsDto">
        SELECT
        b.id,
        a.source_name AS sourceName,
        a.sequence AS sequence,
        (
        CASE a.type
        WHEN 'tjzt' THEN
        '市直机关'
        WHEN 'qxzh' THEN
        '区县纵横'
        ELSE
        '自定义'
        END
        ) typeName,
        b.pc_user_ids AS pcUserIds,
        b.app_user_ids AS appUserIds,
        b.updater AS updater,
        b.update_time AS updateTime
        FROM
        source_manage a
        LEFT JOIN remind_user_config b ON b.source_id = a.id AND b.is_delete = 0
        WHERE
        a.is_start = 1
        AND a.is_delete = 0
        AND a.is_indicators = 1
        <if test="null != type and '' != type">
            AND a.type = #{type}
        </if>
        <if test="null != sourceName and '' != sourceName">
            AND INSTR(a.source_name,#{sourceName})
        </if>
        ORDER BY
        a.sequence
    </select>

    <select id="getPcUserNames"
            resultType="com.onecity.os.management.configmanage.entity.dto.ExportRemindConfigXlsDto">
        SELECT
            a.id,
            GROUP_CONCAT( b.realname ) AS pcUserNames
        FROM
            remind_user_config a
            JOIN sys_user b ON FIND_IN_SET( b.id, a.pc_user_ids )
        WHERE
            a.is_delete = 0
            AND b.del_flag = 0
        GROUP BY
            a.id
    </select>

    <select id="getAppUserNames"
            resultType="com.onecity.os.management.configmanage.entity.dto.ExportRemindConfigXlsDto">
        SELECT
            a.id,
            GROUP_CONCAT( b.`name` ) AS appUserNames
        FROM
            remind_user_config a
            JOIN dingmail_user b ON FIND_IN_SET( b.userid, a.app_user_ids )
        WHERE
            a.is_delete = 0
        GROUP BY
            a.id
    </select>

    <update id="updateBySourceId" parameterType="com.onecity.os.management.configmanage.entity.RemindUserConfig">
        update remind_user_config SET pc_user_ids = #{pcUserIds},app_user_ids = #{appUserIds},update_time = #{updateTime},
        updater = #{updater} where source_id = #{sourceId}
    </update>
</mapper>





































