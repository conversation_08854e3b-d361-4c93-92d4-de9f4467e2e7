<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.onecity.os.management.configmanage.mapper.RoleSourceMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.management.configmanage.entity.DingmailUserMenu">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="source_id" property="sourceId" jdbcType="VARCHAR"/>
        <result column="creater" property="creater" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="getSourceIdByRoleId" resultType="com.onecity.os.management.configmanage.entity.vo.DingDingUserMenuVo">
        SELECT
            a.source_id AS id,
            a.indicators_permissions,
            a.supervise_permissions,
            a.red_permissions,
            a.inform_audit_permissions,
            a.circle_batch_permissions
        FROM
            role_source a
            LEFT JOIN source_manage b ON b.id = a.source_id
        WHERE
            a.role_id = #{roleId}
            AND b.is_delete = 0
            AND b.is_start = 1
        ORDER BY b.sequence
    </select>

    <select id="getSourceByRoleId" resultType="java.lang.String">
        SELECT
            m.source_name AS sourceName
        FROM
            role_source u
                LEFT JOIN source_manage m ON m.id = u.source_id
        WHERE
            u.role_id = #{roleId}
          AND m.is_delete = 0
          AND m.is_start = 1
        ORDER BY m.sequence
    </select>

    <select id="getPermissionsByRoleId" resultType="java.lang.String">
        SELECT du.indicators_permissions FROM general_indicator ge
        LEFT JOIN source_manage sm on ge.source_id = sm.source_simple_name
        LEFT JOIN role_source du on sm.id = du.source_id
        WHERE ge.id = #{indicatorId} AND du.role_id = #{roleId}
    </select>

    <delete id="deleteByRoleId">
		delete from role_source where role_id = #{roleId}
	</delete>
</mapper>















