<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.configmanage.mapper.SourceManageMapper">

    <resultMap type="com.onecity.os.management.configmanage.entity.SourceManage" id="BaseResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="sourceId" column="source_id" jdbcType="VARCHAR"/>
        <result property="sourceName" column="source_name" jdbcType="VARCHAR"/>
        <result property="iconUrl" column="icon_url" jdbcType="VARCHAR"/>
        <result property="sourceSimpleName" column="source_simple_name" jdbcType="VARCHAR"/>
        <result property="sequence" column="sequence" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creater" column="creater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="isStart" column="is_start" jdbcType="INTEGER"/>
        <result property="isIndicators" column="is_indicators" jdbcType="INTEGER"/>
        <result property="urlIds" column="url_ids" jdbcType="VARCHAR"/>
        <result property="urlName" column="url_name" jdbcType="VARCHAR"/>
        <result property="urlType" column="url_type" jdbcType="VARCHAR"/>
        <result property="firstGroupFlag" column="first_group_flag" jdbcType="VARCHAR"/>
        <result property="secondGroupFlag" column="second_group_flag" jdbcType="VARCHAR"/>
        <result property="appReportInfoFlag" column="app_report_info_flag" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, source_id, source_name, icon_url,source_simple_name,
        sequence,create_time,creater,update_time,updater,is_delete,type
    </sql>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.onecity.os.management.configmanage.entity.SourceManage">
        update source_manage
        <set>
            <if test="sourceName != null">
                source_name = #{sourceName,jdbcType=VARCHAR},
            </if>
            <if test="iconUrl != null">
                icon_url = #{iconUrl,jdbcType=VARCHAR},
            </if>
            <if test="sequence != null">
                sequence = #{sequence,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="isStart != null">
                is_start = #{isStart,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="isIndicators != null">
                is_indicators = #{isIndicators,jdbcType=INTEGER}
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="createIndicatorTable"
            parameterType="com.onecity.os.management.configmanage.entity.dto.SourceTableDTO">
        CREATE TABLE ${generalIndicatorTableName} (
          `id` varchar(255) NOT NULL COMMENT '指标ID',
          `indicator_name` varchar(255) DEFAULT NULL COMMENT '指标名称',
          `indicator_exhibit_type` varchar(255) DEFAULT NULL COMMENT '指标展现类型',
          `parent_id` varchar(255) DEFAULT NULL COMMENT '父指标ID,如果为一级指标, 该字段为空',
          `parent_name` varchar(255) DEFAULT NULL COMMENT '父指标名称,如果为一级指标, 该字段为空',
          `icon_url` varchar(255) DEFAULT NULL COMMENT '图标地址',
          `source_id` varchar(16) DEFAULT NULL COMMENT '数据来源id',
          `source_name` varchar(255) DEFAULT NULL COMMENT '数据来源',
          `sequence` int(11) DEFAULT NULL COMMENT '排序',
          `indicator_type` int(11) DEFAULT '0' COMMENT '指标类型，0：指标，1：tab类型',
          `update_date` varchar(100) DEFAULT NULL COMMENT '更新日期文本类型',
          `update_cycle` varchar(45) DEFAULT NULL COMMENT '更新周期',
          `leader` varchar(255) DEFAULT NULL COMMENT '面向领导',
          `is_delete` int(1) DEFAULT '0' COMMENT '是否删除0:否1:是',
          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
          `creater` varchar(255) DEFAULT NULL COMMENT '创建人',
          `update_time` datetime DEFAULT NULL COMMENT '更新时间',
          `updater` varchar(255) DEFAULT NULL COMMENT '更新人',
          PRIMARY KEY (`id`)
        );
        CREATE TABLE ${generalIndicatorDataTableName} (
          `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键自增',
          `indicator_id` varchar(255) DEFAULT NULL COMMENT '指标Id',
          `indicator_name` varchar(255) DEFAULT NULL COMMENT '指标名称',
          `item_name` varchar(255) DEFAULT NULL COMMENT '指标项目',
          `item_value` varchar(1000) DEFAULT NULL COMMENT '指标项目值',
          `item_unit` varchar(10) DEFAULT NULL COMMENT '单位',
          `identify` varchar(255) DEFAULT NULL COMMENT '用来表示指标数据是增加还是减少，前端显示不同的颜色',
          `style` int(11) DEFAULT '0' COMMENT '指标文字展示方式，0：平铺；1：加横线',
          `is_fold` int(11) DEFAULT '0' COMMENT '是否折行0：否1：是',
          `sequence` int(11) DEFAULT NULL COMMENT '排序',
          `update_date` varchar(255) DEFAULT NULL COMMENT '更新日期',
          `current_flag` int(11) DEFAULT '0' COMMENT '是否当前展示0：是1：否',
          `is_delete` int(1) DEFAULT '0' COMMENT '是否删除0:否1:是',
          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
          `creater` varchar(255) DEFAULT NULL COMMENT '创建人',
          `update_time` datetime DEFAULT NULL COMMENT '更新时间',
          `updater` varchar(255) DEFAULT NULL COMMENT '更新人',
          PRIMARY KEY (`id`)
        );
    </update>

    <select id="getSourceInfoBySourceSimpleName" resultMap="BaseResultMap">
        SELECT
            *
        FROM
           source_manage
        WHERE
            is_delete = 0
         AND source_simple_name = #{sourceSimpleName}
            LIMIT 1
    </select>

    <select id="getSourceSimpleNameBySourceId" resultType="String">
        SELECT
            source_simple_name
        FROM
           source_manage
        WHERE
            is_delete = 0
         AND id = #{sourceId}
    </select>

    <select id="getSourceManagePageList" resultType="com.onecity.os.management.statisticanalysis.entity.dto.GetStatisticAnalysisPageListDto">
        SELECT
            a.id AS sourceId,
            a.source_name AS sourceName,
            a.source_simple_name AS sourceSimpleName,
            a.`type`,
            b.pc_user_ids AS pcUserIds,
            b.app_user_ids AS appUserIds
        FROM
            source_manage a
            LEFT JOIN remind_user_config b ON b.source_id = a.id
            AND b.is_delete = 0
        WHERE
            a.is_delete = 0
            AND a.is_start = 1
        <if test="null != sourceName and '' != sourceName">
            AND INSTR(a.source_name,#{sourceName})
        </if>
        ORDER BY a.sequence
    </select>

    <select id="getSourceNameBySourceId" resultType="String">
        SELECT
            source_name
        FROM
           source_manage
        WHERE
            is_delete = 0
         AND id = #{sourceId}
            LIMIT 1
    </select>

    <select id="getAllSourceManage" resultType="String">
        SELECT
            source_simple_name
        FROM
            source_manage
        WHERE
            is_delete = 0
    </select>

    <select id="getAllSourceManage1" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            source_manage
        WHERE
            is_delete = 0
            and is_indicators = 1
            and is_start = 1
    </select>

    <select id="getSourceForAnalysis" resultType="com.onecity.os.management.statisticanalysis.entity.dto.GetStatisticAnalysisPageListDto">
        SELECT
        a.id AS sourceId,
        a.source_name AS sourceName,
        a.source_simple_name AS sourceSimpleName,
        a.`type`
        FROM
        source_manage a
        WHERE
        a.is_delete = 0
        AND a.is_start = 1
        AND a.is_indicators in (1,5)
        ORDER BY a.sequence
    </select>

    <select id="countBySourceSimpleName" parameterType="String" resultType="int">
        SELECT
            count(*)
        FROM
            source_manage
        WHERE
            INSTR(source_simple_name,#{sourceSimpleName}) = 1 and is_delete = 0
    </select>

    <select id="countAll" resultType="int">
        SELECT
            count(*)
        FROM
            source_manage
        WHERE
             is_delete = 0
    </select>

    <select id="selectPage" resultType="com.onecity.os.management.configmanage.entity.vo.SourceManageResVO">
        SELECT
        a.id, a.source_id sourceId, a.source_name sourceName, a.icon_url iconUrl,a.source_simple_name sourceSimpleName,
        a.sequence,a.is_start isStart,a.create_time createTime,a.creater,a.update_time updateTime,a.updater,a.type,b.source_name typeName,a.is_indicators isIndicators
        FROM
            source_manage a
            left join source_manage b on a.type = b.source_simple_name
        WHERE
             a.is_delete = 0 and a.is_indicators != 4
        <if test="type != null and '' != type">
           and a.`type` = #{type}
        </if>
        <if test="sourceName != null and '' != sourceName">
            and INSTR(a.source_name,#{sourceName})
        </if>
        <if test="isStart != null">
            and a.is_start = #{isStart}
        </if>
        order by a.sequence
    </select>

    <select id="getSourceInfoBySourceId" resultMap="BaseResultMap">
        SELECT
            *
        FROM
           source_manage
        WHERE
            is_delete = 0 AND source_id = #{sourceId}
            LIMIT 1
    </select>

    <select id="getSourceInfoById" resultMap="BaseResultMap">
        SELECT
            *
        FROM
           source_manage
        WHERE
            is_delete = 0 AND id = #{id}
            LIMIT 1
    </select>

    <select id="getTypeList" resultType="com.onecity.os.management.configmanage.entity.vo.SourceManageTypeVO">
        SELECT
        source_name as name,source_simple_name as code
        FROM
        source_manage
        WHERE
        is_delete = 0 and is_start = 1
        and `type` = #{type}
        order by sequence
    </select>

    <select id="getIdListByType" resultType="string">
        SELECT
        id
        FROM
        source_manage
        WHERE
        is_delete = 0 and is_start = #{isStart}
        and `type` = #{type}
        order by sequence
    </select>

    <select id="getMenuListByRoleIdAndType" resultType="com.onecity.os.management.configmanage.entity.dto.GetMenuListDto">
        SELECT
        distinct(a.source_id) AS id,
        b.source_name AS sourceName,
        b.source_simple_name AS sourceSimpleName,
        b.icon_url AS iconUrl,
        b.type AS type,
        b.is_indicators AS isIndicators,
        b.url_ids as urlIds,
        b.url_name as urlName,
        b.url_type as urlType,
        b.first_group_flag as firstGroupFlag,
        b.second_group_flag as secondGroupFlag,
        b.app_report_info_flag as appReportInfoFlag
        FROM
        role_source a
        LEFT JOIN source_manage b ON b.id = a.source_id
        WHERE
        a.role_id in
        <foreach collection="roleIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND b.is_delete = 0
        AND b.is_start = 1
        AND b.type = #{type}
        AND b.is_indicators != 4
        ORDER BY
        b.sequence
    </select>

    <update id="insertOrUpdateSourceManageUrl">
        update source_manage set url_ids=#{urlIds},url_name=#{urlName},url_type=#{urlType}
        where source_simple_name = #{sourceSimpleName}
    </update>

    <select id="getSourceManageUrl" resultType="com.onecity.os.management.configmanage.entity.dto.SourceManageUrl">
        select source_simple_name,source_name,url_ids,url_name,url_type
        from source_manage
        where source_simple_name = #{sourceSimpleName}
    </select>

    <update id="insertOrUpdateSourceManageIndicatorGroup">
        update source_manage set first_group_flag=#{firstGroupFlag},
        second_group_flag=#{secondGroupFlag},
        app_report_info_flag=#{appReportInfoFlag}
        where source_simple_name = #{sourceSimpleName} and is_indicators=1
    </update>

    <select id="getSourceManageIndicatorGroup" resultType="com.onecity.os.management.configmanage.entity.dto.SourceManageIndicatorGroup">
        select source_simple_name,source_name,first_group_flag,second_group_flag,app_report_info_flag
        from source_manage
        where source_simple_name = #{sourceSimpleName} and is_indicators=1
    </select>
    <select id="getSourceManageBySourceSimpleNameList" resultType="com.onecity.os.management.zhibiaowarning.entity.vo.SourceManageVo">
        SELECT
            source_id,
            source_name,
            source_simple_name,
            icon_url,
            sequence,
            is_indicators
        FROM
            source_manage
        WHERE
            is_delete = 0
            AND is_start = 1
            and is_indicators in (1,5)
            AND source_simple_name IN
        <foreach collection="sourceSimpleNameList" item="sourceSimpleName" open="(" separator="," close=")">
            #{sourceSimpleName}
        </foreach>
    </select>

</mapper>












