<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.configmanage.mapper.SysSeqConfMapper">

    <resultMap type="com.onecity.os.management.configmanage.entity.SysSeqConf" id="BaseResultMap">
        <id column="seq_id" property="seqId" jdbcType="VARCHAR"/>
        <result column="seq_current" property="seqCurrent" jdbcType="BIGINT"/>
        <result column="seq_maximum" property="seqMaximum" jdbcType="BIGINT"/>
    </resultMap>
    <sql id="Base_Column_List">
       seq_id as seqId, seq_current as seqCurrent, seq_maximum as seqMaximum
    </sql>
    <select id="selectByPrimaryKey" resultType="com.onecity.os.management.configmanage.entity.SysSeqConf" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from sys_seq_conf
        where seq_id = #{seqId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sys_seq_conf
    where seq_id = #{seqId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="com.onecity.os.management.configmanage.entity.SysSeqConf">
    insert into sys_seq_conf (seq_id, seq_current, seq_maximum
      )
    values (#{seqId,jdbcType=VARCHAR}, #{seqCurrent,jdbcType=BIGINT}, #{seqMaximum,jdbcType=BIGINT}
      )
  </insert>
    <insert id="insertSelective" parameterType="com.onecity.os.management.configmanage.entity.SysSeqConf">
        insert into sys_seq_conf
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="seqId != null">
                seq_id,
            </if>
            <if test="seqCurrent != null">
                seq_current,
            </if>
            <if test="seqMaximum != null">
                seq_maximum,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="seqId != null">
                #{seqId,jdbcType=VARCHAR},
            </if>
            <if test="seqCurrent != null">
                #{seqCurrent,jdbcType=BIGINT},
            </if>
            <if test="seqMaximum != null">
                #{seqMaximum,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.onecity.os.management.configmanage.entity.SysSeqConf">
        update sys_seq_conf
        <set>
            <if test="seqCurrent != null">
                seq_current = #{seqCurrent,jdbcType=BIGINT},
            </if>
            <if test="seqMaximum != null">
                seq_maximum = #{seqMaximum,jdbcType=BIGINT},
            </if>
        </set>
        where seq_id = #{seqId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.onecity.os.management.configmanage.entity.SysSeqConf">
    update sys_seq_conf
    set seq_current = #{seqCurrent,jdbcType=BIGINT},
      seq_maximum = #{seqMaximum,jdbcType=BIGINT}
    where seq_id = #{seqId,jdbcType=VARCHAR}
  </update>
</mapper>