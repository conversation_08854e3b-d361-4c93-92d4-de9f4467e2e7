<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.dingding.mapper.DingMailDepartmentMapper">

    <insert id="saveDepartment" parameterType="com.onecity.os.management.dingding.model.po.Department"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO dingmail_department ( id, departmentid, name, parentid)
        VALUES
            (#{id}, #{departmentid},#{name}, #{parentid})
    </insert>

    <delete id="truncateData">
        truncate table dingmail_department
    </delete>

    <select id="getDepartIdsByName" resultType="String">
        SELECT
            GROUP_CONCAT( departmentid SEPARATOR '|' )
        FROM
            dingmail_department
        WHERE
            INSTR(`name`,#{name})
    </select>

    <select id="getDepartNamesByDeparts" resultType="String">
        SELECT
        GROUP_CONCAT(`name`)
        FROM
        dingmail_department
        WHERE
        departmentid IN
        <foreach collection="departs" item="depart" open="(" separator="," close=")">
            #{depart}
        </foreach>
    </select>

    <select id="getParentDepartByName" resultType="com.onecity.os.management.dingding.model.dto.GetDingDepartTreeListDto">
        SELECT * FROM dingmail_department WHERE parentid=1
    </select>

    <select id="getDepartByParentId" resultType="com.onecity.os.management.dingding.model.dto.GetDingDepartTreeListDto">
        SELECT * FROM dingmail_department WHERE parentid=#{parentId}
    </select>

    <select id="getDepartByName" resultType="com.onecity.os.management.dingding.model.dto.GetDingDepartTreeListDto">
        SELECT * FROM dingmail_department WHERE INSTR(`name`,#{name})
    </select>

    <select id="selectAllDepartments" resultType="com.onecity.os.management.dingding.model.po.Department">
        SELECT id,departmentid,name,parentid FROM dingmail_department
    </select>

    <select id="getDepartById" resultType="com.onecity.os.management.dingding.model.dto.GetDingDepartTreeListDto">
        SELECT * FROM dingmail_department WHERE departmentid=#{id} LIMIT 1
    </select>

</mapper>















