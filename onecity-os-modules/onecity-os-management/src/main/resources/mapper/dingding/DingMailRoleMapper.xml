<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.dingding.mapper.DingMailRoleMapper">

    <select id="selectRoleByRoleid" resultType="com.onecity.os.management.dingding.model.po.Role">
        select  * from dingmail_role where roleid=#{roleid}
    </select>

    <select id="getRoleLevelUp" resultType="java.lang.Integer">
        select min(a.level) from dingmail_role a
        <where>
            a.roleid IN
            <foreach item="roleids" index="index" collection="roleids" open="(" separator="," close=")">#{roleids}
            </foreach>
        </where>
    </select>

    <select id="selectRoleByLevel" resultType="java.lang.String">
        select a.roleid from dingmail_role a where a.level &gt;= #{levelUp} and a.level &lt;= #{levelDown}
    </select>

    <insert id="saveRole" parameterType="com.onecity.os.management.dingding.model.po.Role" useGeneratedKeys="true"
            keyProperty="id" keyColumn="id">
        INSERT INTO dingmail_role ( id, roleid, rolename, level, groupname, groupid)
        VALUES
            (#{id}, #{roleid},#{rolename}, #{level}, #{groupname}, #{groupid})
    </insert>

    <delete id="truncateData">
        truncate table dingmail_role
    </delete>

    <select id="getRoleNameByRoleIds" resultType="com.onecity.os.management.dingding.model.po.User">
        SELECT
        GROUP_CONCAT( a.rolename ) AS roles
        FROM
        dingmail_role a
        WHERE
        roleid IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </select>

</mapper>