<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.dingding.mapper.DingMailUserMapper">

    <select id="selectUserByUserId" resultType="com.onecity.os.management.dingding.model.po.User">
        select  * from dingmail_user where userid=#{userid} limit 1
    </select>

    <select id="selectUserByRoleAndDep" resultType="com.onecity.os.management.dingding.model.po.User">
        select * from dingmail_user a
        <where>
            a.roles IN
            <foreach item="roleids" index="index" collection="roleids" open="(" separator="," close=")">#{roleids}
            </foreach>
        </where>
        and a.departments=#{departmentid}
        order by a.orderindepts desc
    </select>

    <select id="selectUserByRoleIds" resultType="java.lang.String">
        select userid from dingmail_user a
        <where>
            <foreach collection="roleIdList" item="roleId" separator="+" open="and (" close=") > 0">
                find_in_set(#{roleId}, a.roles)
            </foreach>
        </where>
    </select>

    <select id="selectUsersSplitDepart" resultType="com.onecity.os.management.dingding.model.vo.DingdingUserVo">
        select
        du.id as id, du.userid,du.name,du.mobile,du.position,du.roles,
        substring_index(substring_index(du.departments, ',', ht.help_topic_id + 1) ,',', -1) as departmentIds
        from
        dingmail_user du join mysql.help_topic ht
        on
        ht.help_topic_id &lt; LENGTH(du.departments) - LENGTH(REPLACE(du.departments,',','')) + 1;
    </select>

    <insert id="saveUser" parameterType="com.onecity.os.management.dingding.model.po.User" useGeneratedKeys="true"
            keyProperty="id" keyColumn="id">
        INSERT INTO dingmail_user ( id, userid, name, mobile, position, departments, roles, rolelevel, orderindepts)
        VALUES
            (#{id}, #{userid},#{name}, #{mobile}, #{position}, #{departments}, #{roles}, #{rolelevel}, #{orderindepts})
    </insert>

    <delete id="truncateData">
        truncate table dingmail_user
    </delete>

    <select id="getDingmailUsersByPage" resultType="com.onecity.os.management.dingding.model.po.User">
        SELECT
        a.id,
        a.userid,
        a.`name`,
        a.mobile,
        a.roles,
        a.rolelevel,
        a.position,
        a.orderindepts,
        b.`name` AS departments,
        a.departments AS departmentIds
        FROM
        dingmail_user a
        LEFT JOIN dingmail_department b ON b.departmentid = a.departments
        WHERE
        1 = 1
        <if test="null != name and '' != name">
            AND INSTR(a.`name`,#{name})
        </if>
        <if test="null != mobile and '' != mobile">
            AND INSTR(a.mobile,#{mobile})
        </if>
        <if test="null != position and '' != position">
            AND INSTR(a.position,#{position})
        </if>
        <if test="null != departIds and '' != departIds">
            AND concat(',',a.departments,',') regexp concat(#{departIds})
        </if>
        ORDER BY a.orderindepts
    </select>

    <select id="getAllUserIdsString" resultType="String">
        SELECT GROUP_CONCAT(userid) FROM dingmail_user
    </select>

    <select id="getDingmailUsersByDepartIdPage" resultType="com.onecity.os.management.dingding.model.po.User">
        SELECT
        a.id,
        a.userid,
        a.`name`,
        a.mobile,
        a.roles,
        a.rolelevel,
        a.position,
        a.orderindepts,
        b.`name` AS departments,
        a.departments AS departmentIds,
        json_extract_c(a.orderindepts, "$.#{departId}") AS orderNew
        FROM
        dingmail_user a
        LEFT JOIN dingmail_department b ON b.departmentid = a.departments
        WHERE
        FIND_IN_SET(#{departId}, a.departments)
        <if test="null != name and '' != name">
            AND INSTR(a.`name`,#{name})
        </if>
        <if test="null != mobile and '' != mobile">
            AND INSTR(a.mobile,#{mobile})
        </if>
        <if test="null != position and '' != position">
            AND INSTR(a.position,#{position})
        </if>
        order by orderNew desc
    </select>

    <select id="getDingmailUsersByDepartIdNoPage" resultType="com.onecity.os.management.dingding.model.po.User">
        SELECT
        a.id,
        a.userid,
        a.`name`,
        a.mobile,
        a.roles,
        a.rolelevel,
        a.position,
        a.orderindepts,
        b.`name` AS departments,
        a.departments AS departmentIds,
        json_extract_c(a.orderindepts, "$.#{departId}") AS orderNew
        FROM
        dingmail_user a
        LEFT JOIN dingmail_department b ON b.departmentid = a.departments
        WHERE
        FIND_IN_SET(#{departId}, a.departments)
        <if test="null != name and '' != name">
            AND INSTR(a.`name`,#{name})
        </if>
        <if test="null != mobile and '' != mobile">
            AND INSTR(a.mobile,#{mobile})
        </if>
        <if test="null != position and '' != position">
            AND INSTR(a.position,#{position})
        </if>
        order by orderNew desc
    </select>

    <select id="selectAllRoles" resultType="com.onecity.os.management.dingding.model.dto.DingdingRoleDTO">
        select id,roleid as roleId,rolename as roleName from dingmail_role where groupid=#{groupId}
        <if test="null != roleName and '' != roleName">
            and INSTR(rolename,#{roleName})
        </if>
        order by level
    </select>
</mapper>
















