<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.emer.mapper.EmerPlanMapper">

    <sql id="Base_Column_List" >
        id, `name`, `type`, sequence, state, create_company, effect_date, file_name, file_path,
        is_delete, creater, create_time, updater,update_time
    </sql>
    <select id="getIdsBySequence" resultType="java.lang.Long">
        select id from emer_plan where is_delete =0 and sequence = #{sequence}
        <if test="id!=null and id !=''">
            and id != #{id}
        </if>
    </select>

    <select id="getListByPage" resultType="com.onecity.os.management.emer.entity.EmerPlan" parameterType="com.onecity.os.management.emer.entity.EmerPlanPageReq" >
        select
        <include refid="Base_Column_List" />
        from emer_plan
        <where>
            is_delete =0
            <if test="req.name != null and req.name != ''">
                AND instr(`name`,#{req.name})>0
            </if>
            <if test="req.type == 0 or req.type != null and req.type != '' ">
                AND `type`=#{req.type}
            </if>
            <if test="req.state != null and req.state != ''">
                AND state= #{req.state}
            </if>
            <if test="req.createCompany != null and req.createCompany != ''">
                AND instr(create_company,#{req.createCompany})>0
            </if>
        </where>
        order by sequence asc,create_time desc
    </select>

    <insert id="insertEmerPlan" >
        insert into emer_plan (id,`name`,`type`,sequence,state,create_company ,effect_date,file_name ,
        file_path ,creater,create_time ,updater,update_time,is_delete) values (#{id},#{name},
        #{type},#{sequence},#{state},#{createCompany},#{effectDate},#{fileName},#{filePath},#{creater},
        #{createTime},#{updater},#{updateTime},0)
    </insert>

    <update id="updateEmerPlanById">
        update emer_plan set id = id
        <if test="name!=null and name !=''">
            ,`name` = #{name}
        </if>
        <if test="type!=null and type !=''">
            ,`type` = #{type}
        </if>
        <if test="sequence!=null and sequence !=''">
            ,sequence = #{sequence}
        </if>
        <if test="state!=null and state !=''">
            ,state = #{state}
        </if>
        <if test="createCompany!=null and createCompany !=''">
            ,create_company = #{createCompany}
        </if>
        <if test="effectDate!=null">
            ,effect_date = #{effectDate}
        </if>
        <if test="fileName!=null and fileName !=''">
            ,file_name = #{fileName}
        </if>
        <if test="filePath!=null and filePath !=''">
            ,file_path = #{filePath}
        </if>
        <if test="creater!=null and creater !=''">
            ,creater = #{creater}
        </if>
        <if test="createTime!=null">
            ,create_time = #{createTime}
        </if>
        <if test="updater!=null and updater !=''">
            ,updater = #{updater}
        </if>
        <if test="updateTime!=null">
            ,update_time = #{updateTime}
        </if>
        where id = #{id}
    </update>

    <delete id="deleteByIdss">
        UPDATE emer_plan  SET is_delete=1, update_time=NOW(), updater=#{name} WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
          #{id}
        </foreach>
    </delete>
</mapper>