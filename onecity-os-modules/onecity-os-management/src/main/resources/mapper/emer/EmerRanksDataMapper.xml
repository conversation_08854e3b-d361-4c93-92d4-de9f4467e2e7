<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.emer.mapper.EmerRanksDataMapper">

    <select id="selectEmerRanksPage" resultType="com.onecity.os.management.emer.entity.EmerRanksIndicator">
        select er.id as id,er.name as Name,er.station as station,sdi.item_value as districtValue,
        case when sdi.item_text is null then er.district else sdi.item_text end as district,
        er.type as type,er.quality as quality,er.contacts as contacts,er.contacts_tel as contactsTel,
        er.nums as nums,er.equipment as equipment,er.rescueclass as rescueclass,er.is_delete as isDelete,
        er.creater as creater,er.creater_time as createrTime,er.updater as updater,er.update_time as updateTime
        from emer_ranks er
        left join sys_dict sd on sd.dict_code = 'dishiquyu'
        left join sys_dict_item sdi on sd.id = sdi.dict_id and sdi.item_value = er.district
        where er.is_delete=0
        <if test="name != null and name != ''">
            AND INSTR(er.name,#{name})
        </if>
        <if test="type != null and type != '' ">
            AND er.type=#{type}
        </if>
        <if test="district != null and district != ''">
            AND INSTR(district,#{district})
        </if>
        <if test="contacts != null and contacts != ''">
            AND INSTR(er.contacts,#{contacts})
        </if>
        order by er.creater_time desc
    </select>
</mapper>