<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.emer.mapper.EmerRanksMapper">

    <insert id="insertEmerRank" >
        insert into emer_ranks (id,`name`,district,station,`type`,quality,contacts ,contacts_tel ,
        nums ,equipment,rescueclass,creater,creater_time ,updater,update_time) values (#{id},#{name},
        #{district},#{station},#{type},#{quality},#{contacts},#{contactsTel},#{nums},
        #{equipment},#{rescueclass},#{creater},#{createrTime},#{updater},#{updateTime})
    </insert>
    <insert id="updateEmerRankById" >
        update emer_ranks set id = id
        <if test="name!=null and name !=''">
            ,`name` = #{name}
        </if>
        <if test="district!=null and district !=''">
            ,district = #{district}
        </if>
        <if test="station!=null and station !=''">
            ,station = #{station}
        </if>
        <if test="type!=null and type !=''">
            ,`type` = #{type}
        </if>
        <if test="quality!=null and quality !=''">
            ,quality = #{quality}
        </if>
        <if test="contacts!=null and contacts !=''">
            ,contacts = #{contacts}
        </if>
        <if test="contactsTel!=null and contactsTel !=''">
            ,contacts_tel = #{contactsTel}
        </if>
        <if test="nums!=null and nums !=''">
            ,nums = #{nums}
        </if>
        <if test="equipment!=null and equipment !=''">
            ,equipment = #{equipment}
        </if>
        <if test="rescueclass!=null and rescueclass !=''">
            ,rescueclass = #{rescueclass}
        </if>
        <if test="isDelete!=null and isDelete !=''">
            ,is_delete = #{isDelete}
        </if>
        <if test="updater!=null and updater !=''">
            ,updater = #{updater}
        </if>
        <if test="updateTime!=null">
            ,update_time = #{updateTime}
        </if>
        where id = #{id}
    </insert>
</mapper>