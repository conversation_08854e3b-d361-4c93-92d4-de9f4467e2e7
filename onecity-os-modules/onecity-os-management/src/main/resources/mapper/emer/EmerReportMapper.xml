<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.emer.mapper.EmerReportMapper">

    <select id="getIdsBySequence" resultType="java.lang.String">
        select id from emer_report where is_delete =0 and sequence = #{sequence}
        <if test="id!=null and id !=''">
            and id != #{id}
        </if>
    </select>


    <select id="selectList" resultType="com.onecity.os.management.emer.entity.EmerReportIndicator">
        select id,name,type,sequence,create_company createCompany,file_name fileName,
        file_path filePath,is_delete isDelete,creater,create_time createTime,updater,update_time updateTime
        from emer_report where is_delete =0
        <if test="name!=null and name !=''">
            and INSTR(name,#{name})
        </if>
        <if test="type!=null and type !=''">
            and INSTR(type,#{type})
        </if>
        <if test="createCompany!=null and createCompany !=''">
            and INSTR(create_company,#{createCompany})
        </if>
        order by sequence asc,update_time desc
    </select>

    <insert id="insertEmerReport" >
        insert into emer_report (id,`name`,`type`,sequence,create_company ,file_name ,
        file_path ,creater,create_time ,updater,update_time) values (#{id},#{name},
        #{type},#{sequence},#{createCompany},#{fileName},#{filePath},#{creater},
        #{createTime},#{updater},#{updateTime})
    </insert>

    <insert id="updateEmerReportById" >
        update emer_report set id = id
        <if test="name!=null and name !=''">
            ,`name` = #{name}
        </if>
        <if test="type!=null and type !=''">
            ,`type` = #{type}
        </if>
        <if test="sequence!=null and sequence !=''">
            ,sequence = #{sequence}
        </if>
        <if test="createCompany!=null and createCompany !=''">
            ,create_company = #{createCompany}
        </if>
        <if test="fileName!=null and fileName !=''">
            ,file_name = #{fileName}
        </if>
        <if test="filePath!=null and filePath !=''">
            ,file_path = #{filePath}
        </if>
        <if test="creater!=null and creater !=''">
            ,creater = #{creater}
        </if>
        <if test="createTime!=null and createTime !=''">
            ,create_time = #{createTime}
        </if>
        <if test="updater!=null and updater !=''">
            ,updater = #{updater}
        </if>
        <if test="updateTime!=null and updateTime !=''">
            ,update_time = #{updateTime}
        </if>
        where id = #{id}
    </insert>
</mapper>