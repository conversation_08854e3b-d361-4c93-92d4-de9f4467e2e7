<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.emer.mapper.HeadquartersMapper">

    <resultMap type="com.onecity.os.management.emer.model.entiy.GetHeadquarters" id="insertHeadquartersMap">
        <result property="hqtName" column="hqtName" jdbcType="VARCHAR"/>
        <result property="firduty" column="firduty" jdbcType="VARCHAR"/>
        <result property="secduty" column="secduty" jdbcType="VARCHAR"/>
        <result property="duty" column="duty" jdbcType="VARCHAR"/>
        <result property="station" column="station" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="INTEGER"/>
        <result property="sequence" column="sequence" jdbcType="INTEGER"/>
        <result property="userName" column="userName" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="population" column="population" jdbcType="INTEGER"/>
        <result property="creater" column="creater" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>


    <insert id="insertHeadquarters" parameterType="com.onecity.os.management.emer.model.vo.HeadquartersParam"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO emer_department (`name`,firduty,secduty,sequence,`type`,creater,create_time,updater,update_time)
        VALUES
            (#{name},
            #{firduty},
            #{secduty},
            #{sequence},
            #{type},#{creater},#{createTime},#{updater},#{updateTime})
    </insert>

    <insert id="insertHeadquartersUser"
            parameterType="com.onecity.os.management.emer.model.vo.HeadquartersUserParam" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO emer_user (`name`,duty,station,phone,depart_id,creater,create_time,updater,update_time)
        VALUES
            (#{name},#{duty},#{station},#{phone},#{departId},#{creater},#{createTime},#{updater},#{updateTime})
    </insert>

    <select id="getHeadquarters" resultMap="insertHeadquartersMap" parameterType="java.lang.String">
        SELECT a.id,b.id as userId,a.name as hqtName,a.firduty,a.secduty,b.duty,b.station,b.phone,a.sequence,b.name as
        userName, a.type,(select count(*) from emer_user where depart_id=a.id and is_delete=0 ) as population,
        a.creater,a.create_time
        from emer_department a LEFT JOIN emer_user b
        on a.id=b.depart_id where a.is_delete=0 and b.is_delete = 0 and b.station in ("指挥长","总指挥")
        <if test="hqtName != null and '' != hqtName">
            and INSTR(a.name,#{hqtName})
        </if>
        <if test="userName!= null and '' != userName">
            and INSTR(b.name,#{userName})
        </if>
        GROUP BY a.id
        order by a.sequence ASC,a.create_time DESC
    </select>


    <update id="deleteHeadquarters">
     UPDATE emer_user SET is_delete = 1 where  depart_id = #{id}
    </update>


    <update id="updateDepartment" parameterType="com.onecity.os.management.emer.model.vo.HeadquartersParam">
    update emer_department
    set `name` = #{name},firduty = #{firduty},secduty = #{secduty},sequence = #{sequence},`type`= #{type},updater = #{updater},update_time = #{updateTime}
    where id = #{id}
    </update>


    <update id="updateUser" parameterType="com.onecity.os.management.emer.model.vo.HeadquartersUserParam">
   update emer_user
   set `name` = #{name},duty = #{duty},station = #{station},phone = #{phone},updater = #{updater},
        update_time = #{updateTime}, is_delete = 0
   where id = #{id}
    </update>

    <select id="getIdExits" resultType="Integer">
   SELECT EXISTS( SELECT 1 FROM emer_department WHERE id = #{id})
</select>

    <update id="deleteDepartmentById">
        UPDATE emer_department SET is_delete = 1 where  id = #{id}
    </update>

    <select id="getHeadquarterById" resultType="com.onecity.os.management.emer.model.vo.GetHeadquarterByIdDto">
        SELECT * FROM emer_department WHERE id = #{id} AND is_delete = 0
    </select>

    <select id="getEmerUsersByDepartId" resultType="com.onecity.os.management.emer.model.vo.EmerUsersDto">
        SELECT * FROM emer_user WHERE depart_id = #{id} AND is_delete = 0 AND station=#{station}
    </select>

    <select id="getStation" resultType="String">
        SELECT
        t2.station
        FROM
        emer_department AS t1
        LEFT JOIN emer_user AS t2 ON t1.id = t2.depart_id
        WHERE
        t1.id = #{id}
        and t1.is_delete = 0 and t2.is_delete=0 GROUP BY t2.station
        <if test="null != type and 0 == type">
            ORDER BY
            CASE
            WHEN t2.station = '总指挥' THEN
            1
            WHEN t2.station = '常务副总指挥' THEN
            2
            WHEN t2.station = '副总指挥' THEN
            3
            WHEN t2.station = '成员' THEN
            4
            END ASC
        </if>
        <if test="null != type and 1 == type">
            ORDER BY
            CASE
            WHEN t2.station = '指挥长' THEN
            1
            WHEN t2.station = '副指挥长' THEN
            2
            WHEN t2.station = '成员' THEN
            3
            END ASC
        </if>
    </select>

    <update id="deleteEmerUserByDepartId">
        UPDATE emer_user SET is_delete = 1, updater = #{userName}, update_time=NOW() WHERE depart_id = #{departIdReceive}
    </update>

    <select id="getEmerZhiHuiUsersByDepartIdAndStation" resultType="String">
        SELECT
        GROUP_CONCAT( b.`name` ) as userName
        FROM
        emer_user b
        LEFT JOIN emer_department a ON a.id = b.depart_id
        WHERE
        a.is_delete = 0
        AND a.id = #{id}
        AND b.is_delete = 0
        AND b.station = #{station}
        LIMIT 1
    </select>

    <select id="countSequences" resultType="java.lang.Integer">
        SELECT count(1) FROM  emer_department WHERE
        is_delete = 0 and sequence= #{sequence}
        <if test="id!=null and id !=''">
            and id != #{id}
        </if>
    </select>
</mapper>











