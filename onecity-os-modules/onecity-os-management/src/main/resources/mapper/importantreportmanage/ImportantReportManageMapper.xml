<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.onecity.os.management.importantreportmanage.mapper.ImportantReportManageMapper">
    <resultMap id="BaseResultMap"
               type="com.onecity.os.management.importantreportmanage.model.entity.ImportantReportManage">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="public_source" property="publicSource" jdbcType="VARCHAR"/>
        <result column="file_name" property="fileName" jdbcType="LONGVARCHAR"/>
        <result column="file_path" property="filePath" jdbcType="LONGVARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="is_read" property="isRead" jdbcType="TINYINT"/>
        <result column="read_count" property="readCount" jdbcType="INTEGER"/>
        <result column="send_time" property="sendTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="creater" property="creater" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="receive_user_id" property="receiveUserId" jdbcType="LONGVARCHAR"/>
        <result column="audit_user_ids" property="auditUserIds" jdbcType="LONGVARCHAR"/>
        <result column="content" property="content" jdbcType="LONGVARCHAR"/>
    </resultMap>

    <select id="getImportantReportManagePageList"
            resultType="com.onecity.os.management.importantreportmanage.model.entity.ImportantReportManage">
        SELECT
        a.id,
        a.title,
        a.public_source publicSource,
        a.send_time as sendTime,
        a.create_time as createTime,
        a.update_time as updateTime,
        a.file_name as fileName,
        a.file_path as filePath,
        a.content,
        a.`status`,
        a.creater,
        a.updater
        FROM
        important_report_manage a
        WHERE
        a.is_delete = 0
        <if test="null != title and '' != title">
            AND INSTR(a.title,#{title})
        </if>
        <if test="null != publicDate and '' != publicDate">
            AND a.`status` = 1
            AND INSTR(a.send_time,#{publicDate})
        </if>
        ORDER BY a.create_time DESC
    </select>

    <select id="getImportantReportAuditListById"
            resultType="com.onecity.os.management.importantreportmanage.model.dto.GetImportantReportAuditListByIdDto">
      SELECT
          a.audit_time,
          a.audit_view,
          a.creater AS auditUserName
      FROM
          important_report_audit a
      WHERE
          a.important_report_id = #{id}
      ORDER BY
          a.audit_time DESC
    </select>

    <update id="updateImportantReportStatusById">
        UPDATE important_report_manage SET `status`=#{status}, updater=#{loginName}, update_time=NOW()
        <if test="1 == status">
            ,send_time=NOW()
        </if>
        <if test="2 == status">
            ,send_time=NULL,read_count=0
        </if>
        WHERE id=#{id}
    </update>

    <update id="deleteImportantReportById">
        UPDATE important_report_manage SET is_delete=1, updater=#{loginName}, update_time=NOW() WHERE id=#{id}
    </update>

    <select id="getImportantReportReceiveUsersById" resultType="String">
        SELECT
            receive_user_id
        FROM
            important_report_manage
        WHERE
            is_delete = 0
            AND id = #{id}
        LIMIT 1
    </select>

    <select id="getStatusById" resultType="java.lang.Byte">
        SELECT
        `status`
        FROM
        important_report_manage
        WHERE
        is_delete = 0
        AND id = #{id}
        LIMIT 1
    </select>

    <select id="getInfoById"
            resultType="com.onecity.os.management.importantreportmanage.model.entity.ImportantReportManage">
        SELECT
        *
        FROM
        important_report_manage
        WHERE
        is_delete = 0
        AND id = #{id}
    </select>

    <select id="getIsAuditById" resultType="Long">
        SELECT
        id
        FROM
        important_report_manage
        WHERE
        id = #{id}
        AND audit_user_ids is NOT NULL
        AND audit_user_ids != ''
        LIMIT 1
    </select>

    <select id="getUserNameByUserIds"
            resultType="com.onecity.os.management.importantreportmanage.model.dto.GetImportantReportReceiveUsersByIdDto">
        SELECT
            userid AS userId,
            `name` AS userName
        FROM
            dingmail_user
        WHERE
            userid IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="getImportantReportInfoById"
            resultType="com.onecity.os.management.importantreportmanage.model.entity.ImportantReportManage">
        SELECT
        *
        FROM
        important_report_manage
        WHERE
        is_delete = 0
        AND id = #{id}
    </select>

    <delete id="deleteReportAuditByReportId">
        DELETE FROM important_report_audit WHERE important_report_id=#{importantReportId}
    </delete>

    <update id="updateAuditUserIdById">
        UPDATE important_report_manage SET audit_user_ids = '' WHERE id=#{id}
    </update>

</mapper>


























