<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.onecity.os.management.messagesReport.mapper.MessageIndDataMapper">
    <resultMap id="BaseResultMap"
               type="com.onecity.os.management.messagesReport.entity.MessageIndicatorDetail">
        <result column="id" property="id"/>
        <result column="indicator_id" property="indicatorId"/>
        <result column="indicator_name" property="indicatorName"/>
        <result column="content" property="content"/>
        <result column="update_date" property="updateDate"/>
        <result column="file_url" property="fileUrl"/>
        <result column="file_name" property="fileName"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_time" property="createTime"/>
        <result column="creater" property="creater"/>
        <result column="update_Time" property="updateTime"/>
        <result column="updater" property="updater"/>
    </resultMap>

    <select id="countByIndicatorId" resultType="java.lang.Integer">
        SELECT count(*) FROM message_indicator_detail
        WHERE indicator_id = #{indicatorId} AND is_delete = 0
    </select>

    <select id="getPageList" resultMap="BaseResultMap">
        SELECT * FROM message_indicator_detail
        WHERE is_delete = 0 and indicator_id = #{messageIndicatorDetail.indicatorId}
        order by update_date desc
    </select>
</mapper>


























