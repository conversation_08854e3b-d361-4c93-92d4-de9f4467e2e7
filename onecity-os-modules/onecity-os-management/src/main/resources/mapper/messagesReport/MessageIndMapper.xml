<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.onecity.os.management.messagesReport.mapper.MessageIndMapper">
    <resultMap id="BaseResultMap"
               type="com.onecity.os.management.messagesReport.entity.MessageIndicator">
        <result column="id" property="id"/>
        <result column="indicator_name" property="indicatorName"/>
        <result column="sequence" property="sequence"/>
        <result column="category_name" property="categoryName"/>
        <result column="source_id" property="sourceId"/>
        <result column="source_name" property="sourceName"/>
        <result column="type" property="type"/>
        <result column="update_date" property="updateDate"/>
        <result column="update_cycle" property="updateCycle"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_time" property="createTime"/>
        <result column="creater" property="creater"/>
        <result column="update_time" property="updateTime"/>
        <result column="top_update_time" property="topUpdateTime"/>
        <result column="updater" property="updater"/>
    </resultMap>

    <sql id="Base_Column_List">
        id as id,indicator_name as indicatorName,sequence as sequence,
        category_name as categoryName,source_id as sourceId,
        source_name as sourceName,`type` as `type`,update_date as updateDate,
        update_cycle as updateCycle,is_delete as isDelete,create_time as createTime,
        creater as creater,update_time as updateTime,top_update_time as topUpdateTime,
        updater as updater
    </sql>

    <select id="getPageList" resultType="com.onecity.os.management.messagesReport.entity.MessageIndicator">
        SELECT <include refid="Base_Column_List"/> FROM message_indicator
        where is_delete = 0 and source_id = #{messageIndicator.sourceId}
        ORDER BY sequence desc ,top_update_time desc ,update_time desc
    </select>

    <update id="updateByPram"
            parameterType="com.onecity.os.management.messagesReport.entity.MessageIndicator">
        UPDATE message_indicator
        <trim prefix="SET" suffixOverrides=",">
            sequence = #{indicator.sequence},
            updater = #{indicator.updater},
            top_update_time = #{indicator.topUpdateTime},
            update_time = #{indicator.updateTime},
        </trim>
        where 1 = 1
        AND id = #{indicator.id}
    </update>
</mapper>


























