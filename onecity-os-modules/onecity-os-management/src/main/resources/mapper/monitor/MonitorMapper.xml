<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.monitor.mapper.MonitorMapper">

    <select id="getListByPage" resultType="com.onecity.os.management.monitor.entity.ProjectKeyMonitor">
        select *
        from project_key_monitor
        <where>
            is_delete =0
        </where>
        order by create_time,update_time desc
    </select>
</mapper>