<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.onecity.os.management.programme.mapper.ProgrammeMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.management.programme.model.entity.Programme">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="date_time" property="dateTime" jdbcType="TIMESTAMP"/>
        <result column="comment" property="comment" jdbcType="VARCHAR"/>
        <result column="creater_id" property="createrId" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="creater" property="creater" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="isDelete" jdbcType="INTEGER"/>
    </resultMap>

    <select id="getProgrammeList" resultType="com.onecity.os.management.programme.model.entity.Programme">
        SELECT
        id,
        date_time,
        comment,
        creater_id,
        update_time,
        updater,
        creater,
        create_time,
        is_delete
        FROM
        programme
        WHERE
        is_delete = 0
        and creater_id = #{createrId}
        and date_time = #{dateTime}
        ORDER BY create_time DESC
    </select>

    <select id="getAllDate" resultType="String">
      SELECT
          date_time
      FROM
          programme
      WHERE
          is_delete = 0
          AND creater_id = #{createrId}
          group by date_time
  </select>

    <select id="getProgrammeInfoById" resultType="com.onecity.os.management.programme.model.entity.Programme">
        SELECT
        *
        FROM
        programme
        WHERE
        is_delete = 0
        AND creater_id = #{createrId}
    </select>

</mapper>

























