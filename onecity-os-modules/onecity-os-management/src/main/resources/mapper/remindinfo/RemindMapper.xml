<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.schedule.mapper.RemindMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.management.configmanage.entity.RemindInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="remind_title" jdbcType="VARCHAR" property="remindTitle"/>
        <result column="is_read" jdbcType="TINYINT" property="isRead"/>
        <result column="reminded_count" jdbcType="TINYINT" property="remindedCount"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="creater" jdbcType="VARCHAR" property="creater"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="remind_content" jdbcType="LONGVARCHAR" property="remindContent"/>
        <result column="pc_user_ids" jdbcType="LONGVARCHAR" property="pcUserIds"/>
        <result column="app_user_ids" jdbcType="LONGVARCHAR" property="appUserIds"/>
    </resultMap>


    <select id="getOverDueRemindForMsg" resultType="com.onecity.os.management.configmanage.entity.RemindInfo">
        SELECT *
        FROM remind_info
        WHERE is_delete = 0
          AND type IN ("OVERDUE_3_REMIND", "OVERDUE_1_REMIND")
          AND DATE_FORMAT(remind_date,'%Y%m%d')=DATE_FORMAT(CURDATE(),'%Y%m%d')
    </select>

    <select id="getAnalysisRemindForMsg" resultType="com.onecity.os.management.configmanage.entity.RemindInfo">
        SELECT *
        FROM remind_info
        WHERE is_delete = 0
          AND type = "MON_REMIND"
          AND DATE_FORMAT(remind_date,'%Y%m%d')=DATE_FORMAT(CURDATE(),'%Y%m%d')
    </select>

</mapper>