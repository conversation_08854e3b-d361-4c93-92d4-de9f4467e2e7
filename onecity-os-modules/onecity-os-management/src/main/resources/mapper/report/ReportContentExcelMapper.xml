<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.report.mapper.ReportContentExcelMapper">
    
    <resultMap type="com.onecity.os.management.report.domain.ReportContentExcel" id="ReportContentExcelResult">
        <result property="reportContentId"    column="report_content_id"    />
        <result property="reportId"    column="report_id"    />
        <result property="reportContent"    column="report_content"    />
        <result property="contentType"    column="content_type"    />
        <result property="updater"    column="updater"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creater"    column="creater"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectReportContentExcelVo">
        select report_content_id, report_id, report_content, content_type, updater, update_time, creater, create_time from report_content_excel
    </sql>

    <select id="selectReportContentExcelList" parameterType="com.onecity.os.management.report.domain.ReportContentExcel" resultMap="ReportContentExcelResult">
        <include refid="selectReportContentExcelVo"/>
        <where>  
            <if test="reportId != null  and reportId != ''"> and report_id = #{reportId}</if>
            <if test="reportContent != null  and reportContent != ''"> and report_content = #{reportContent}</if>
            <if test="contentType != null  and contentType != ''"> and content_type = #{contentType}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="creater != null  and creater != ''"> and creater = #{creater}</if>
        </where>
    </select>
    
    <select id="selectReportContentExcelById" parameterType="String" resultMap="ReportContentExcelResult">
        <include refid="selectReportContentExcelVo"/>
        where report_id = #{reportId}
    </select>

    <select id="selectReportContentExcelByReportContentId" parameterType="String" resultMap="ReportContentExcelResult">
        <include refid="selectReportContentExcelVo"/>
        where report_content_id = #{reportContentId}
    </select>

    <insert id="insertReportContentExcel" parameterType="com.onecity.os.management.report.domain.ReportContentExcel">
        insert into report_content_excel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reportContentId != null">report_content_id,</if>
            <if test="reportId != null">report_id,</if>
            <if test="reportContent != null">report_content,</if>
            <if test="contentType != null">content_type,</if>
            <if test="updater != null">updater,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creater != null">creater,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reportContentId != null">#{reportContentId},</if>
            <if test="reportId != null">#{reportId},</if>
            <if test="reportContent != null">#{reportContent},</if>
            <if test="contentType != null">#{contentType},</if>
            <if test="updater != null">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creater != null">#{creater},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateReportContentExcel" parameterType="com.onecity.os.management.report.domain.ReportContentExcel">
        update report_content_excel
        <trim prefix="SET" suffixOverrides=",">
            <if test="reportContent != null">report_content = #{reportContent},</if>
            <if test="contentType != null">content_type = #{contentType},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creater != null">creater = #{creater},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where report_content_id = #{reportContentId} and report_id = #{reportId}
    </update>

    <delete id="deleteReportContentExcelByReportContentId" parameterType="String">
        delete from report_content_excel where report_content_id = #{reportContentId}
    </delete>

    <delete id="deleteReportContentExcelByReportContentIds" parameterType="String">
        delete from report_content_excel where report_content_id in 
        <foreach item="reportContentId" collection="array" open="(" separator="," close=")">
            #{reportContentId}
        </foreach>
    </delete>

</mapper>