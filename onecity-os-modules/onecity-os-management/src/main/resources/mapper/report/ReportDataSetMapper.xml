<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.report.mapper.ReportDataSetMapper">
    
    <resultMap type="com.onecity.os.management.report.domain.ReportDataSet" id="ReportResult">
        <result property="reportId"    column="report_id"    />
        <result property="dataSetId"    column="report_data_set_id"    />
        <result property="dataSetColumn"    column="data_set_id"    />
        <result property="dataSetColumn"    column="data_set_column"    />
    </resultMap>

    <sql id="selectReportDataSetVo">
        select
            report_id,
            report_data_set_id,
            data_set_id,
            data_set_column
    </sql>

    <select id="selectReportDataSetListByReportId" resultMap="ReportResult">
        <include refid="selectReportDataSetVo" />
        from report_data_set
        where report_id = #{reportId}
    </select>

    <delete id="deleteReportDataSetByReportId" parameterType="String">
        delete from report_data_set where report_id = #{reportId}
    </delete>

    <insert id="insertReportDataSetBatch" parameterType="java.util.List">
        insert into report_data_set (report_id, report_data_set_id, data_set_id, data_set_column)
        values
        <foreach collection="reportDataSetList" item="item" index="index" separator=",">
            (#{reportId}, #{item.reportDataSetId}, #{item.dataSetId}, #{item.dataSetColumn})
        </foreach>
    </insert>
</mapper>