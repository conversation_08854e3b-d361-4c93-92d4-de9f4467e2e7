<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.report.mapper.ReportModleMapper">
    
    <resultMap type="com.onecity.os.management.report.domain.ReportModle" id="ReportModleResult">
        <result property="reportModleId"    column="report_modle_id"    />
        <result property="reportModleName"    column="report_modle_name"    />
        <result property="reportModleType"    column="report_modle_type"    />
        <result property="reportModleContent"    column="report_modle_content"    />
        <result property="updater"    column="updater"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creater"    column="creater"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectReportModleVo">
        select report_modle_id, report_modle_name, report_modle_type, report_modle_content, updater, update_time, creater, create_time from report_modle
    </sql>

    <select id="selectReportModleList" resultMap="ReportModleResult">
        <include refid="selectReportModleVo"/>
        <where>  
            <if test="reportModleName != null  and reportModleName != ''"> and report_modle_name like concat('%', #{reportModleName}, '%')</if>
            <if test="reportModleType != null  and reportModleType != ''"> and report_modle_type = #{reportModleType}</if>
        </where>
    </select>
    
    <!-- 获取报告模版内容 -->
    <select id="selectReportModleContentByReportModleId" parameterType="String" resultType="String">
        select report_modle_content from report_modle where report_modle_id = #{reportModleId}
    </select>

    <!-- 保存报告模版内容 -->
    <update id="saveReportModleContent" parameterType="com.onecity.os.management.report.domain.ReportModle">
        update report_modle set report_modle_content = #{reportModleContent}, updater = #{updater}, update_time = #{updateTime} where report_modle_id = #{reportModleId}
    </update>

    <select id="selectReportModleByReportModleId" parameterType="String" resultMap="ReportModleResult">
        <include refid="selectReportModleVo"/>
        where report_modle_id = #{reportModleId}
    </select>
        
    <insert id="insertReportModle" parameterType="com.onecity.os.management.report.domain.ReportModle">
        insert into report_modle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reportModleId != null">report_modle_id,</if>
            <if test="reportModleName != null">report_modle_name,</if>
            <if test="reportModleType != null">report_modle_type,</if>
            <if test="reportModleContent != null">report_modle_content,</if>
            <if test="updater != null">updater,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creater != null">creater,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reportModleId != null">#{reportModleId},</if>
            <if test="reportModleName != null">#{reportModleName},</if>
            <if test="reportModleType != null">#{reportModleType},</if>
            <if test="reportModleContent != null">#{reportModleContent},</if>
            <if test="updater != null">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creater != null">#{creater},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateReportModle" parameterType="com.onecity.os.management.report.domain.ReportModle">
        update report_modle
        <trim prefix="SET" suffixOverrides=",">
            <if test="reportModleName != null">report_modle_name = #{reportModleName},</if>
            <if test="reportModleType != null">report_modle_type = #{reportModleType},</if>
            <if test="reportModleContent != null">report_modle_content = #{reportModleContent},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where report_modle_id = #{reportModleId}
    </update>

    <delete id="deleteReportModleByReportModleId" parameterType="String">
        delete from report_modle where report_modle_id = #{reportModleId}
    </delete>

    <delete id="deleteReportModleByReportModleIds" parameterType="String">
        delete from report_modle where report_modle_id in 
        <foreach item="reportModleId" collection="array" open="(" separator="," close=")">
            #{reportModleId}
        </foreach>
    </delete>

</mapper>