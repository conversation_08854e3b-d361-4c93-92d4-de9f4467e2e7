<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.report.mapper.WordDataMapper">
    
    <resultMap type="com.onecity.os.management.report.domain.WordData" id="WordDataResult">
        <result property="wordDataId"    column="word_data_id"    />
        <result property="reportId"    column="report_id"    />
        <result property="wordDataContent"    column="word_data_content"    />
        <result property="updater"    column="updater"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creater"    column="creater"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectWordDataVo">
        select word_data_id, report_id, word_data_content, updater, update_time, creater, create_time from word_data
    </sql>

    <select id="selectWordDataList" parameterType="com.onecity.os.management.report.domain.WordData" resultMap="WordDataResult">
        <include refid="selectWordDataVo"/>
        <where>  
            <if test="reportId != null  and reportId != ''"> and report_id = #{reportId}</if>
            <if test="wordDataContent != null  and wordDataContent != ''"> and word_data_content = #{wordDataContent}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="creater != null  and creater != ''"> and creater = #{creater}</if>
        </where>
    </select>
    
    <select id="selectWordDataById" parameterType="String" resultMap="WordDataResult">
        <include refid="selectWordDataVo"/>
        where report_id = #{reportId}
    </select>

    <select id="selectWordDataByWordDataId" parameterType="String" resultMap="WordDataResult">
        <include refid="selectWordDataVo"/>
        where word_data_id = #{wordDataId}
    </select>

    <insert id="insertWordData" parameterType="com.onecity.os.management.report.domain.po.WordDataPo">
        insert into word_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="wordDataId != null">word_data_id,</if>
            <if test="reportId != null">report_id,</if>
            <if test="wordDataContent != null">word_data_content,</if>
            <if test="updater != null">updater,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creater != null">creater,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="wordDataId != null">#{wordDataId},</if>
            <if test="reportId != null">#{reportId},</if>
            <if test="wordDataContent != null">#{wordDataContent},</if>
            <if test="updater != null">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creater != null">#{creater},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateWordData" parameterType="com.onecity.os.management.report.domain.po.WordDataPo">
        update word_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="wordDataContent != null">word_data_content = #{wordDataContent},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creater != null">creater = #{creater},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where word_data_id = #{wordDataId} and report_id = #{reportId}
    </update>

    <delete id="deleteWordDataByWordDataId" parameterType="String">
        delete from word_data where word_data_id = #{wordDataId}
    </delete>

    <delete id="deleteWordDataByWordDataIds" parameterType="String">
        delete from word_data where word_data_id in 
        <foreach item="wordDataId" collection="array" open="(" separator="," close=")">
            #{wordDataId}
        </foreach>
    </delete>

</mapper>