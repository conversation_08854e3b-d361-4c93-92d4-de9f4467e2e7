<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.onecity.os.management.statisticanalysis.mapper.IndicatorStatisticsAnalysisMapper">
    <resultMap id="BaseResultMap"
               type="com.onecity.os.management.statisticanalysis.entity.IndicatorStatisticsAnalysis">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="source_id" property="sourceId" jdbcType="INTEGER"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="update_cycle" property="updateCycle" jdbcType="VARCHAR"/>
        <result column="is_update" property="isUpdate" jdbcType="TINYINT"/>
        <result column="is_over_due" property="isOverDue" jdbcType="TINYINT"/>
        <result column="total_update" property="totalUpdate" jdbcType="VARCHAR"/>
        <result column="should_update" property="shouldUpdate" jdbcType="VARCHAR"/>
        <result column="actual_update" property="actualUpdate" jdbcType="VARCHAR"/>
        <result column="over_due_update" property="overDueUpdate" jdbcType="VARCHAR"/>
        <result column="not_update" property="notUpdate" jdbcType="VARCHAR"/>
        <result column="miss_update_ratio" property="missUpdateRatio" jdbcType="VARCHAR"/>
        <result column="over_due_update_ratio" property="overDueUpdateRatio" jdbcType="VARCHAR"/>
        <result column="last_update_time" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
        <result column="last_audit_time" property="lastAuditTime" jdbcType="TIMESTAMP"/>
        <result column="is_history" property="isHistory" jdbcType="TINYINT"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="audit_name" property="auditName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="TINYINT"/>
        <result column="total_count" property="totalCount" jdbcType="INTEGER"/>
        <result column="date_date" property="dataDate" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getStatisticAnalysisHistoryPageList"
            resultType="com.onecity.os.management.statisticanalysis.entity.dto.GetStatisticAnalysisPageListDto">
        SELECT
        a.id,
        a.update_cycle,
        b.source_name AS sourceName,
        a.is_update AS isUpdate,
        a.update_cycle AS updateCycle,
        b.`type`,
        b.id AS sourceId,
        a.last_update_time AS lastUpdateTime,
        a.last_audit_time AS lastAuditTime,
        a.is_over_due AS isOverDue,
        c.pc_user_ids AS pcUserIds,
        c.app_user_ids AS appUserIds,
        REPLACE(a.total_update, '条', '') AS totalCount,
        REPLACE(a.should_update, '条', '') AS shouldCount,
        REPLACE(a.actual_update, '条', '') AS actualCount
        FROM
        indicator_statistics_analysis a
        LEFT JOIN source_manage b ON b.id = a.source_id
        LEFT JOIN remind_user_config c ON c.source_id = b.id AND c.is_delete = 0
        WHERE
        a.is_delete = 0
        AND a.is_history = #{isHistory}
        AND a.version = #{version}
        AND b.is_delete = 0
        AND b.is_start = 1
        <if test="1 == version">
            AND (a.update_cycle = '' or a.update_cycle is null)
        </if>
        <if test="0 == version">
            AND a.update_cycle IN ( '月度更新', '季度更新', '半年更新', '年度更新' )
        </if>
        <if test="null != isOverDue and '' != isOverDue">
            AND a.is_over_due = #{isOverDue}
        </if>
        <if test="null != updateCycle and '' != updateCycle">
            AND a.update_cycle = #{updateCycle}
        </if>
        <if test="null != startUpdateTime and '' != startUpdateTime">
            AND a.last_update_time &gt;= #{startUpdateTime}
        </if>
        <if test="null != endUpdateTime and '' != endUpdateTime">
            AND a.last_update_time &lt;= #{endUpdateTime}
        </if>
        <if test="null != sourceName and '' != sourceName">
            AND INSTR(b.source_name,#{sourceName})
        </if>
        ORDER BY a.create_time DESC, a.id ASC, b.sequence ASC
    </select>

    <select id="getStatisticInfoById"
            resultType="com.onecity.os.management.statisticanalysis.entity.dto.GetStatisticInfoByIdDto">
        SELECT a.id,
               a.title AS title,
               a.should_update,
               a.actual_update,
               a.over_due_update,
               a.not_update,
               a.miss_update_ratio,
               a.over_due_update_ratio,
               a.last_update_time,
               a.update_cycle
        FROM indicator_statistics_analysis a
                 LEFT JOIN source_manage b ON b.id = a.source_id
        WHERE a.id = #{id}
          AND a.version = 0
          AND a.is_delete = 0
          AND b.is_delete = 0
          AND b.is_start = 1 LIMIT 1
    </select>

    <select id="getLastestTimebyStatisticsId" resultType="date">
        select max(update_time) as date
        from general_indicator_data
        where
            is_delete=0
          and indicator_id in (select d.id from general_indicator d
            , (SELECT
            a.update_cycle as update_cycle
            , b.source_simple_name as source_simple_name
            FROM
            indicator_statistics_analysis a
            LEFT JOIN source_manage b ON b.id = a.source_id
            WHERE
            a.id = #{id}
          AND a.version = 0
          AND a.is_delete = 0
          AND b.is_delete = 0
          AND b.is_start = 1
            LIMIT 1) c
            where d.source_id = c.source_simple_name
          and d.update_cycle= c.update_cycle
          and d.is_delete=0
          and d.is_show=1)
    </select>

    <select id="getStatisticSourceIdBySourceSimpleName" resultType="Long">
        SELECT
        id
        FROM
        indicator_statistics_analysis
        WHERE
        is_delete = 0
        AND is_history = 0
        <if test="null != updateCycle and '' != updateCycle">
            AND update_cycle = #{updateCycle}
        </if>
        <if test="null == updateCycle or '' == updateCycle">
            AND update_cycle is null
        </if>
        AND source_id = #{sourceId}
        AND data_date = #{dataDate}
        LIMIT 1
    </select>

    <select id="getExportStatisticListByIsHistory"
            resultType="com.onecity.os.management.statisticanalysis.entity.dto.ExportStatisticInfoXlsDto">
        SELECT
        b.source_name AS sourceName,
        a.title,
        a.update_cycle AS updateCycle,
        a.is_update AS isUpdate,
        a.is_over_due AS isOverDue,
        a.should_update AS shouldUpdate,
        a.actual_update AS actualUpdate,
        a.over_due_update AS overDueUpdate,
        a.not_update AS notUpdate,
        a.miss_update_ratio AS missUpdateRatio,
        a.over_due_update_ratio AS overDueUpdateRatio,
        a.is_history AS isHistory,
        a.last_update_time AS lastUpdateTime,
        a.last_audit_time AS lastAuditTime
        FROM
        indicator_statistics_analysis a
        LEFT JOIN source_manage b ON b.id = a.source_id
        WHERE
        a.is_delete = 0
        AND a.is_history = #{isHistory}
        AND a.version = 0
        AND a.update_cycle IN ( '月度更新', '季度更新', '半年更新', '年度更新' )
        AND b.is_delete = 0
        AND b.is_start = 1
        <if test="null != isOverDue and '' != isOverDue">
            AND a.is_over_due = #{isOverDue}
        </if>
        <if test="null != updateCycle and '' != updateCycle">
            AND a.update_cycle = #{updateCycle}
        </if>
        <if test="null != startUpdateTime and '' != startUpdateTime">
            AND a.last_update_time &gt;= #{startUpdateTime}
        </if>
        <if test="null != endUpdateTime and '' != endUpdateTime">
            AND a.last_update_time &lt;= #{endUpdateTime}
        </if>
        <if test="null != sourceName and '' != sourceName">
            AND INSTR(b.source_name,#{sourceName})
        </if>
        ORDER BY a.create_time DESC, a.id ASC, b.sequence ASC
    </select>

    <select id="getStatisticInfoBySourceId"
            resultType="com.onecity.os.management.statisticanalysis.entity.dto.GetStatisticInfoByIdDto">
        SELECT
        a.indicator_name,
        a.update_cycle,
        a.plan_update_date,
        a.update_time,
        a.updater updater,
        pInt.indicator_name AS parentName
        FROM
        general_indicator a
        LEFT JOIN general_indicator AS pInt ON a.parent_id = pInt.id
        WHERE
        a.update_cycle IN ( '月度更新', '季度更新', '半年更新', '年度更新' )
        AND a.is_delete = 0
        AND a.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY
        FIELD(
        a.update_cycle,
        '月度更新',
        '季度更新',
        '半年更新',
        '年度更新'
        ),a.sequence
    </select>

    <select id="getAuditNameBySourceId"
            resultType="com.onecity.os.management.statisticanalysis.entity.dto.GetStatisticInfoByIdDto">
        SELECT a.audit_user_name,
               a.audit_time
        FROM indicator_audit_notes a
                 LEFT JOIN source_manage b ON b.source_simple_name = a.source_id
        WHERE a.is_delete = 0
          AND a.audit_result = 1
          AND b.id = #{sourceId}
        ORDER BY a.audit_time DESC LIMIT 1
    </select>

    <select id="getTitleBySourceId" resultType="String">
        SELECT title
        FROM indicator_statistics_analysis
        WHERE source_id = #{sourceId}
          AND is_delete = 0
          AND version = 1
        ORDER BY create_time DESC LIMIT 1
    </select>

    <select id="getAllIndicatorByIndicatorIds"
            resultType="com.onecity.os.management.statisticanalysis.entity.dto.GetStatisticInfoByIdDto">
        SELECT
        a.indicator_name,
        a.update_cycle,
        a.plan_update_date,
        a.update_time,
        b.realname AS updater,
        pInt.indicator_name AS parentName
        FROM
        general_indicator_tianbao a
        LEFT JOIN sys_user b ON b.username = a.updater
        LEFT JOIN general_indicator_tianbao pInt ON a.parent_id = pInt.id
        WHERE
        a.update_cycle IN ( '月度更新', '季度更新', '半年更新', '年度更新' )
        AND a.is_delete = 0
        AND a.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY
        FIELD(
        a.update_cycle,
        '月度更新',
        '季度更新',
        '半年更新',
        '年度更新'
        ),a.sequence
    </select>

    <select id="getAnalysisBysourceId" resultMap="BaseResultMap">
        SELECT *
        FROM indicator_statistics_analysis AS ana
        WHERE 1=1
        AND ana.is_delete = 0
        AND ana.data_date = #{date}
        AND ana.source_id IN
        <foreach collection="sourceIds" item="sourceId" open="(" separator="," close=")">
            #{sourceId}
        </foreach>
    </select>

    <select id="getAnalysisBySource" resultMap="BaseResultMap">
        SELECT *
        FROM indicator_statistics_analysis AS ana
        WHERE ana.is_history = #{isHistory}
          AND ana.is_delete = 0
          AND ana.data_date = #{dataDate}
          AND ana.source_id = #{sourceId}
          AND ana.update_cycle = #{updateCycle}
    </select>

    <select id="getAnalysisByParameter" resultMap="BaseResultMap">
        SELECT *
        FROM indicator_statistics_analysis AS ana
        WHERE ana.is_history = #{isHistory}
          AND ana.is_delete = 0
          AND ana.data_date = #{dataDate}
          AND ana.source_id = #{sourceId}
    </select>

    <update id="updateBySourceIdAndDateDateAndIsHistory"
            parameterType="com.onecity.os.management.statisticanalysis.entity.IndicatorStatisticsAnalysis">
        update indicator_statistics_analysis
        set
        title=#{vo.title},
        is_update=#{vo.isUpdate},
        is_over_due=#{vo.isOverDue},
        total_update=#{vo.totalUpdate},
        should_update=#{vo.shouldUpdate},
        actual_update=#{vo.actualUpdate},
        over_due_update=#{vo.overDueUpdate},
        not_update=#{vo.notUpdate},
        miss_update_ratio=#{vo.missUpdateRatio},
        over_due_update_ratio=#{vo.overDueUpdateRatio},
        <if test="1 == updateLastUpdateTimeFlag">
            last_update_time=#{vo.lastUpdateTime},
        </if>
        last_audit_time=#{vo.lastAuditTime},
        is_delete=#{vo.isDelete},
        audit_name=#{vo.auditName},
        update_time=#{vo.updateTime},
        version=#{vo.version}
        where is_history = 0 and data_date=#{dateDate} and source_id=#{sourceId}
        <if test="null != updateCycle and '' != updateCycle">
            AND update_cycle = #{updateCycle}
        </if>
        <if test="null == updateCycle or '' == updateCycle">
            AND update_cycle is null
        </if>
    </update>

    <update id="updateBySourceIdAndDateDateAndIsHistoryNoFlag"
            parameterType="com.onecity.os.management.statisticanalysis.entity.IndicatorStatisticsAnalysis">
        update indicator_statistics_analysis
        set
        title=#{vo.title},
        is_update=#{vo.isUpdate},
        is_over_due=#{vo.isOverDue},
        should_update=#{vo.shouldUpdate},
        actual_update=#{vo.actualUpdate},
        over_due_update=#{vo.overDueUpdate},
        not_update=#{vo.notUpdate},
        miss_update_ratio=#{vo.missUpdateRatio},
        over_due_update_ratio=#{vo.overDueUpdateRatio},
        last_update_time=#{vo.lastUpdateTime},
        last_audit_time=#{vo.lastAuditTime},
        is_delete=#{vo.isDelete},
        total_count=#{vo.totalCount},
        update_cycle=#{vo.updateCycle},
        update_time=#{vo.updateTime},
        version=#{vo.version}
        where is_history = 0 and data_date=#{dateDate} and source_id=#{sourceId}
        <if test="null != updateCycle and '' != updateCycle">
            AND update_cycle = #{updateCycle}
        </if>
        <if test="null == updateCycle or '' == updateCycle">
            AND update_cycle is null
        </if>
    </update>

    <update id="updateHistoryFlag">
        update indicator_statistics_analysis
        set
        is_history = 1
        WHERE
        is_delete = 0
        AND is_history = 0
        <if test="null != updateCycle and '' != updateCycle">
            AND update_cycle = #{updateCycle}
        </if>
        AND source_id = #{sourceId}
        AND data_date != #{dataDate}
    </update>

    <select id="getAnalysisHistory" resultMap="BaseResultMap">
        SELECT ana.id,
               ana.source_id    AS sourceId,
               ana.title        AS title,
               ana.update_cycle AS updateCycle,
               ana.create_time  AS createTime
        FROM indicator_statistics_analysis AS ana
        WHERE DATE_FORMAT(ana.create_time, '%/Y-%m') = DATE_FORMAT(NOW(), '%/Y-%m')
          AND ana.is_delete = 0
    </select>
</mapper>
























