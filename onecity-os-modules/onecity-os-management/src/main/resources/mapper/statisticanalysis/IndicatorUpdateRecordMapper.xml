<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.onecity.os.management.statisticanalysis.mapper.IndicatorUpdateRecordMapper">
    <resultMap id="BaseResultMap"
               type="com.onecity.os.management.statisticanalysis.entity.IndicatorUpdateRecord">

        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="indicator_id" property="indicatorId" jdbcType="VARCHAR"/>
        <result column="indicator_name" property="indicatorName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="is_send_ding" property="isSendDing" jdbcType="TINYINT"/>
        <result column="data_flag" property="dataFlag" jdbcType="TINYINT"/>
    </resultMap>

    <select id="getUpdateRecordList"
            resultType="com.onecity.os.management.statisticanalysis.entity.IndicatorUpdateRecord">
        SELECT
        a.id,
        a.indicator_id AS indicatorId,
        a.indicator_name AS indicatorName,
        a.create_time AS createTime,
        a.is_send_ding as isSendDing,
        a.data_flag as dataFlag
        FROM
        general_indicator_tianbao_update_record a
        WHERE
        1 = 1
        <if test="null != startTime and '' != startTime">
            AND a.create_time &gt;= #{startTime}
        </if>
        <if test="null != endTime and '' != endTime">
            AND a.create_time &lt;= #{endTime}
        </if>
        AND a.indicator_id in
        <foreach collection="indicatorIdList" item="indicatorId" open="(" separator="," close=")">
            #{indicatorId}
        </foreach>
        <if test="null != dataFlag">
            AND a.data_flag = #{dataFlag}
        </if>
    </select>

    <insert id="insertIndicatorUpdateRecord">
        INSERT INTO general_indicator_tianbao_update_record (indicator_id,
                                                             indicator_name,
                                                             create_time,is_send_ding,data_flag)
        VALUES (#{data.indicatorId},
                #{data.indicatorName},
                #{data.createTime},0,#{data.dataFlag})
    </insert>

    <insert id="insertIndicatorUpdateRecordBatch">
        INSERT INTO general_indicator_tianbao_update_record (indicator_id,
                                                             indicator_name,
                                                             create_time,is_send_ding,data_flag)
        VALUES
        <foreach collection="dataList" item="data" separator=",">
                (#{data.indicatorId},
                #{data.indicatorName},
                #{data.createTime},0,#{data.dataFlag})
        </foreach>
    </insert>


    <insert id="updateSendByIndicatorIds">
        update general_indicator_tianbao_update_record set is_send_ding = 1
        where indicator_id in
        <foreach collection="indicatorIdList" item="indicatorId" open="(" separator="," close=")">
            #{indicatorId}
        </foreach>
    </insert>

    <select id="selectAttentionUsersByIndicatorId" resultType="java.lang.String">
        SELECT userIds from(select jaz.indicator_id indicatorId,group_concat(DISTINCT jaz.user_id) userIds,group_concat(DISTINCT source_name) sourceName
        from jz_attention_zhibiao jaz
        left join general_indicator_tianbao git on jaz.indicator_id = git.id
        where jaz.indicator_id = #{indicatorId} and jaz.status=1 group by jaz.indicator_id) A  where A.sourceName is not null
    </select>
</mapper>