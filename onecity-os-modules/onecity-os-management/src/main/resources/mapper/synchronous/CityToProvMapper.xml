<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.onecity.os.management.synchronous.mapper.CityToProvMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.management.synchronous.dto.CityToProv">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="indicator_id_city" property="indicatorIdCity" jdbcType="VARCHAR"/>
        <result column="indicator_id_prov" property="indicatorIdProv" jdbcType="VARCHAR"/>
        <result column="first_level_indicator_id_city" property="firstLevelIndicatorIdCity" jdbcType="VARCHAR"/>
        <result column="first_level_indicator_id_prov" property="firstLevelIndicatorIdProv" jdbcType="VARCHAR"/>
        <result column="source_simple_name_city" property="sourceSimpleNameCity" jdbcType="VARCHAR"/>
        <result column="source_simple_name_prov" property="sourceSimpleNameProv" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creater" property="creater" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="update_time_city" property="updateTimeCity" jdbcType="TIMESTAMP"/>
    </resultMap>


    <select id="getAllCityFirstLevelList" resultMap="BaseResultMap">
        select DISTINCT(first_level_indicator_id_city) from city_to_prov
        where is_delete = 0 and source_simple_name_prov = #{source}
    </select>

    <select id="getAllLevelListBySource" resultMap="BaseResultMap">
        select * from city_to_prov
        where source_simple_name_prov = #{sourceSimpleNameProv}
    </select>

    <update id="deleteRecordBySource">
        update city_to_prov set is_delete = 1,update_time = sysdate() where source_simple_name_prov = #{sourceSimpleNameProv}
    </update>

    <update id="updateRecordBySourceAndId">
        update city_to_prov set is_delete = 0,update_time = sysdate() where source_simple_name_prov = #{sourceSimpleNameProv}
        and indicator_id_city in
        <foreach collection="synchronizedIdList" item="indicatorId" open="(" separator="," close=")">
            #{indicatorId}
        </foreach>
    </update>

    <update id="updateRecordTimeBySourceAndId">
        update city_to_prov set update_time_city = #{time} where source_simple_name_prov = #{sourceSimpleNameProv}
        and indicator_id_city =  #{indicatorIdCity}
    </update>

    <insert id="insertBatch">
    INSERT INTO city_to_prov
    (indicator_id_city, indicator_id_prov, first_level_indicator_id_city, first_level_indicator_id_prov,
    source_simple_name_city,source_simple_name_prov,is_delete,create_time,
    creater,update_time,updater,update_time_city)
    values
        <foreach collection="cityToProvs" item="data" separator="," close=";">
            (
            #{data.indicatorIdCity},#{data.indicatorIdProv},#{data.firstLevelIndicatorIdCity},
            #{data.firstLevelIndicatorIdProv},#{data.sourceSimpleNameCity},#{data.sourceSimpleNameProv},
            #{data.isDelete},#{data.createTime},#{data.creater},
            #{data.updateTime},#{data.updater},#{data.updateTimeCity}
            )
        </foreach>
    </insert>

    <insert id="insertRecordBatch">
        INSERT INTO general_indicator_tianbao_update_record (`indicator_id`,
                                                             `indicator_name`,
                                                             `create_time`,`is_send_ding`,`data_flag`)
        VALUES
        <foreach collection="indicatorUpdateRecords" item="data" separator="," close=";">
                (#{data.indicatorId},
                #{data.indicatorName},
                #{data.createTime},0,#{data.dataFlag})
        </foreach>
    </insert>

    <insert id="insertIndicatorBatch">
        INSERT INTO general_indicator_tianbao (
        `id`,`indicator_name`,`indicator_exhibit_type`,`parent_id`,
        `icon_url`,`source_id`,`source_name`,`sequence`,`indicator_type`,`update_date`,
        `update_cycle`,`is_delete`,`create_time`,`creater`,`update_time`,
        `updater`,`group_type`,`group_url`,`plan_update_date`,`is_show`,
        `is_screen`,`is_legend`,`data_update_mode`,`data_config_id`)
        VALUES
        <foreach collection="indicatorAndDataList" item="vo" separator="," close=";">
	    (#{vo.id},#{vo.indicatorName},#{vo.indicatorExhibitType},#{vo.parentId},
        #{vo.iconUrl},#{vo.sourceId},#{vo.sourceName},
		#{vo.sequence},#{vo.indicatorType},#{vo.updateDate},#{vo.updateCycle},
		0,#{vo.createTime},#{vo.creater},#{vo.updateTime},#{vo.updater},
	    #{vo.groupType},#{vo.groupUrl},#{vo.planUpdateDate},#{vo.isShow},
	    #{vo.isScreen},#{vo.isLegend},#{vo.dataUpdateMode},#{vo.dataConfigId})
        </foreach>
    </insert>

    <insert id="insertIndicatorDataBatch">
        INSERT INTO general_indicator_data_tianbao (`id`,`indicator_id`,`indicator_name`,`item_name`,
        `item_value`,`item_unit`,
        `identify`,`style`,`is_fold`,`sequence`,`update_date`,`current_flag`,`is_delete`,
        `create_time`,`creater`,`update_time`,`updater`)
        VALUES
        <foreach collection="indicatorDataList" item="vo" separator="," close=";">
        (NULL,#{vo.indicatorId},#{vo.indicatorName},#{vo.itemName},
		#{vo.itemValue},#{vo.itemUnit},#{vo.identify},#{vo.style},
		#{vo.isFold},#{vo.sequence},#{vo.updateDate},#{vo.currentFlag},
		0,#{vo.createTime},#{vo.creater},#{vo.updateTime},#{vo.updater})
        </foreach>
    </insert>

    <update id="updateIndicatorBatch">
        <foreach collection="indicatorAndDataList" item="vo" index="index" open="" close="" separator=";">
            UPDATE
            general_indicator_tianbao
            SET
            `indicator_name` = #{vo.indicatorName},
            `indicator_exhibit_type` = #{vo.indicatorExhibitType},
            `parent_id` = #{vo.parentId},
            <if test="null != vo.iconUrl and '' != vo.iconUrl">
                `icon_url` = #{vo.iconUrl},
            </if>
            <if test="null != vo.updateDate and '' != vo.updateDate">
                `update_date` = #{vo.updateDate},
            </if>
            <if test="null != vo.updateTime">
                `update_time` = #{vo.updateTime},
            </if>
            <if test="null != vo.updater and '' != vo.updater">
                `updater` = #{vo.updater},
            </if>
            <if test="null != vo.groupType">
                `group_type` = #{vo.groupType},
            </if>
            <if test="null != vo.groupUrl and '' != vo.groupUrl">
                `group_url` = #{vo.groupUrl},
            </if>
            `update_cycle` = #{vo.updateCycle},
            `sequence` = #{vo.sequence},
            `indicator_type` = #{vo.indicatorType},
            `plan_update_date` = #{vo.planUpdateDate},
            `is_delete` = 0,
            `is_show` = #{vo.isShow},
            `is_screen` = #{vo.isScreen},
            `is_legend` = #{vo.isLegend},
            `data_config_id` = #{vo.dataConfigId},
            `data_update_mode` = #{vo.dataUpdateMode}
            WHERE
            `id` = #{vo.id}
        </foreach>
    </update>

    <delete id="deleteIndicatorDataBatchById">
        delete from general_indicator_data_tianbao where indicator_id in
        <foreach collection="idList" item="indicatorId" separator="," open="(" close=")">
            #{indicatorId}
        </foreach>
    </delete>
</mapper>