<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.systemstyle.mapper.SystemStyleMapper">
    
    <resultMap type="com.onecity.os.management.systemstyle.domain.SystemStyle" id="SystemStyleResult">
        <result property="id"    column="id"    />
        <result property="style"    column="style"    />
        <result property="pcName"    column="pc_name"    />
        <result property="appName"    column="app_name"    />
        <result property="appSourceNameFlag"    column="app_source_name_flag"    />
        <result property="appUpdateCycleFlag"    column="app_update_cycle_flag"    />
        <result property="appTopFlag"    column="app_top_flag"    />
        <result property="appFirstPageFlag"    column="app_first_page_flag"    />
        <result property="appBackFlag"    column="app_back_flag"    />
        <result property="appWaterFlag"    column="app_water_flag"    />
    </resultMap>

    <sql id="selectSystemStyleVo">
        select id, style, pc_name, app_name, app_source_name_flag, app_update_cycle_flag,
          app_top_flag,app_first_page_flag,app_back_flag,app_water_flag
          from system_style
    </sql>

    <select id="selectSystemStyleById"  resultMap="SystemStyleResult">
        <include refid="selectSystemStyleVo"/>
        LIMIT 1
    </select>
        
    <insert id="insertSystemStyle" parameterType="com.onecity.os.management.systemstyle.domain.SystemStyle" useGeneratedKeys="true" keyProperty="id">
        insert into system_style
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="style != null">style,</if>
            <if test="pcName != null">pc_name,</if>
            <if test="appName != null">app_name,</if>
            <if test="appSourceNameFlag != null">app_source_name_flag,</if>
            <if test="appUpdateCycleFlag != null">app_update_cycle_flag,</if>
            <if test="appTopFlag != null">app_top_flag,</if>
            <if test="appFirstPageFlag != null">app_first_page_flag,</if>
            <if test="appBackFlag != null">app_back_flag,</if>
            <if test="appWaterFlag != null">app_water_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="style != null">#{style},</if>
            <if test="pcName != null">#{pcName},</if>
            <if test="appName != null">#{appName},</if>
            <if test="appSourceNameFlag != null">#{appSourceNameFlag},</if>
            <if test="appUpdateCycleFlag != null">#{appUpdateCycleFlag},</if>
            <if test="appTopFlag != null">#{appTopFlag},</if>
            <if test="appFirstPageFlag != null">#{appFirstPageFlag},</if>
            <if test="appBackFlag != null">#{appBackFlag},</if>
            <if test="appWaterFlag != null">#{appWaterFlag},</if>
         </trim>
    </insert>

    <update id="updateSystemStyle" parameterType="com.onecity.os.management.systemstyle.domain.SystemStyle">
        update system_style
        <trim prefix="SET" suffixOverrides=",">
            <if test="style != null">style = #{style},</if>
            <if test="pcName != null">pc_name = #{pcName},</if>
            <if test="appName != null">app_name = #{appName},</if>
            <if test="appSourceNameFlag != null">app_source_name_flag = #{appSourceNameFlag},</if>
            <if test="appUpdateCycleFlag != null">app_update_cycle_flag = #{appUpdateCycleFlag},</if>
            <if test="appTopFlag != null">app_top_flag = #{appTopFlag},</if>
            <if test="appFirstPageFlag != null">app_first_page_flag = #{appFirstPageFlag},</if>
            <if test="appBackFlag != null">app_back_flag = #{appBackFlag},</if>
            <if test="appWaterFlag != null">app_water_flag = #{appWaterFlag},</if>
        </trim>
        where id = #{id}
    </update>

</mapper>