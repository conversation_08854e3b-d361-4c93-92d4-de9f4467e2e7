<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.yulan.mapper.IndicatorYuLanDataMapper">

    <resultMap type="com.onecity.os.management.yulan.po.IndicatorYuLanData" id="BaseResultMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="indicatorId" column="indicator_id" jdbcType="VARCHAR"/>
        <result property="indicatorName" column="indicator_name" jdbcType="VARCHAR"/>
        <result property="itemName" column="item_name" jdbcType="VARCHAR"/>
        <result property="itemValue" column="item_value" jdbcType="VARCHAR"/>
        <result property="itemUnit" column="item_unit" jdbcType="VARCHAR"/>
        <result property="identify" column="identify" jdbcType="VARCHAR"/>
        <result property="style" column="style" jdbcType="INTEGER"/>
        <result property="fold" column="is_fold" jdbcType="INTEGER"/>
        <result property="sequence" column="sequence" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creater" column="creater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="VARCHAR"/>
        <result property="currentFlag" column="current_flag" jdbcType="INTEGER"/>
        <result property="delete" column="is_delete" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, indicator_id AS indicatorId,indicator_name AS indicatorName, item_name AS itemName, item_value AS itemValue,
        item_value1 AS itemValue1, item_value2 AS itemValue2, item_value3 AS itemValue3, item_value4 AS itemValue4,
        item_value5 AS itemValue5, item_value6 AS itemValue6,item_unit AS itemUnit, item_unit_2nd AS itemUnit2nd,
        identify,style,is_fold AS fold, sequence,create_time AS createTime,creater,update_time AS updateTime,
        updater,update_date AS updateDate,is_delete,current_flag AS currentFlag
    </sql>
    <sql id="where">
        <where>
            <if test="param.indicatorId != null and param.indicatorId != '' ">
                indicator_id = #{param.indicatorId}
            </if>
            <if test="param.delete != null">
                AND is_delete = #{param.delete}
            </if>
            <if test="param.currentFlag != null">
                AND current_flag = #{param.currentFlag}
            </if>
            <if test="param.updateDate != null">
                AND update_date = #{param.updateDate}
            </if>
        </where>
    </sql>
    <select id="list" resultType="com.onecity.os.management.yulan.po.IndicatorYuLanData">
        select
        <include refid="Base_Column_List"/>
        from general_indicator_data_tianbao
        <include refid="where"/>
        order by sequence
    </select>
    <select id="listShowDate" resultType="string">
        select update_date
        from general_indicator_data_tianbao
        <include refid="where"/>
        group by update_date
    </select>

    <select id="getZhibiaoDataListByIndicatorId" resultType="com.onecity.os.management.yulan.vo.AttentionZhiBiaoDataVo">
        SELECT item_name,
               item_value,
               item_unit,
               identify
        FROM general_indicator_data_tianbao
        WHERE indicator_id = #{indicatorId}
          AND is_delete = 0
        ORDER BY sequence
    </select>

    <select id="listIndicatorDatas" resultType="com.onecity.os.management.yulan.po.IndicatorYuLanData">
        select
        <include refid="Base_Column_List"/>
        from general_indicator_data_tianbao
        where
        <if test="param.indicatorId != null and param.indicatorId != '' ">
            indicator_id = #{param.indicatorId}
        </if>
        <if test="param.delete != null">
            AND is_delete = #{param.delete}
        </if>
        <if test="param.currentFlag != null">
            AND current_flag = #{param.currentFlag}
        </if>
        <if test="param.updateDate != null">
            AND subString(update_date,1,4) = #{param.updateDate}
        </if>
        order by sequence
    </select>

    <select id="listIndicatorYearDatas" resultType="com.onecity.os.management.yulan.po.IndicatorYuLanData">
        select
        <include refid="Base_Column_List"/>
        from general_indicator_data_tianbao
        where
        <if test="param.indicatorId != null and param.indicatorId != '' ">
            indicator_id = #{param.indicatorId}
        </if>
        <if test="param.delete != null">
            AND is_delete = #{param.delete}
        </if>
        <if test="param.currentFlag != null">
            AND current_flag = #{param.currentFlag}
        </if>
        <if test="param.updateDate != null">
            AND subString(update_date,1,4) >= #{param.updateDate}-4 AND subString(update_date,1,4) &lt;= #{param.updateDate}
        </if>
        order by sequence
    </select>

    <select id="getMaxUpdateDateYear" resultType="java.lang.String">
        select MAX(subString(update_date, 1, 4)) maxUpdateDateYear
        from general_indicator_data_tianbao
        where indicator_id = #{indicatorId}
          AND is_delete = 0
          AND current_flag = 1
    </select>

    <select id="getYearDataList" resultType="com.onecity.os.management.yulan.po.IndicatorYuLanData">
        select
        <include refid="Base_Column_List"/>
        from general_indicator_data_tianbao
        where indicator_id=#{indicatorId}
        AND is_delete=0
        AND current_flag=1
        <if test="startYear != null and endYear != null">
            AND subString(update_date,1,4) >= #{startYear} AND subString(update_date,1,4) &lt;= #{endYear}
        </if>
        <if test="startYear != null and endYear == null">
            AND subString(update_date,1,4) = #{startYear}
        </if>
        ORDER BY sequence
    </select>
</mapper>