<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.yulan.mapper.IndicatorYuLanMapper">

    <resultMap type="com.onecity.os.management.yulan.po.IndicatorYuLan" id="BaseResultMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="indicatorName" column="indicator_name" jdbcType="VARCHAR"/>
        <result property="indicatorExhibitType" column="indicator_exhibit_type" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="parentName" column="parent_name" jdbcType="VARCHAR"/>
        <result property="iconUrl" column="icon_url" jdbcType="VARCHAR"/>
        <result property="sourceId" column="source_id" jdbcType="VARCHAR"/>
        <result property="sourceName" column="source_name" jdbcType="VARCHAR"/>
        <result property="sequence" column="sequence" jdbcType="INTEGER"/>
        <result property="indicatorType" column="indicator_type" jdbcType="INTEGER"/>
        <result property="updateDate" column="update_date" jdbcType="VARCHAR"/>
        <result property="updateCycle" column="update_cycle" jdbcType="VARCHAR"/>
        <result property="leader" column="leader" jdbcType="VARCHAR"/>
        <result property="delete" column="is_delete" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creater" column="creater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="groupType" column="group_type" jdbcType="INTEGER"/>
        <result property="groupUrl" column="group_url" jdbcType="VARCHAR"/>
        <result property="isShow" column="is_show" jdbcType="INTEGER"/>
        <result property="isScreen" column="is_screen" jdbcType="INTEGER"/>
        <result property="isLegend" column="is_legend" jdbcType="INTEGER"/>
        <result property="dataUpdateMode" column="data_update_mode" jdbcType="INTEGER"/>
        <result property="dataConfigId" column="data_config_id" jdbcType="VARCHAR"/>
        <result property="urlIds" column="url_ids" jdbcType="VARCHAR"/>
        <result property="urlName" column="url_name" jdbcType="VARCHAR"/>
        <result property="urlType" column="url_type" jdbcType="VARCHAR"/>
        <result property="sortType" column="sort_type" jdbcType="VARCHAR"/>
        <result property="paraUrl" column="para_url" jdbcType="VARCHAR"/>
        <result property="nameShowFlag" column="name_show_flag" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , indicator_name AS indicatorName , indicator_exhibit_type AS indicatorExhibitType,  parent_id AS parentId,
        parent_name AS parentName, icon_url AS iconUrl,source_id AS sourceId,source_name AS sourceName,sequence,
        indicator_type AS indicatorType,update_date AS updateDate,update_cycle AS updateCycle ,leader,is_delete AS `delete`,
        create_time AS createTime,creater,update_time AS updateTime,updater,group_type AS groupType,group_url AS groupUrl,
        is_show AS isShow,is_screen AS isScreen,is_legend AS isLegend,data_update_mode AS dataUpdateMode ,data_config_id AS dataConfigId,
        url_ids,url_type,url_name,
        sort_type,para_url,name_show_flag
    </sql>
    <sql id="where">
        <where>
            <if test="record.parentId != null and record.parentId != '' ">
                parent_id = #{record.parentId}
            </if>
            <if test="record.sourceId != null and record.sourceId != ''">
                AND source_id = #{record.sourceId}
            </if>
            <if test="record.delete != null">
                AND is_delete = #{record.delete}
            </if>
            <if test="record.indicatorType != null">
                AND indicator_type = #{record.indicatorType}
            </if>
            <if test="record.isShow != null">
                AND is_show = #{record.isShow}
            </if>
        </where>
    </sql>

    <select id="listIcon" resultType="com.onecity.os.management.yulan.po.IndicatorYuLan">
        select
        <include refid="Base_Column_List"/>
        from general_indicator_tianbao
        <include refid="where"/>
        order by sequence
    </select>
    <select id="listTab" resultType="com.onecity.os.management.yulan.po.IndicatorYuLan">
        select
        <include refid="Base_Column_List"/>
        from general_indicator_tianbao
        <include refid="where"/>
        order by sequence
    </select>
    <select id="listCoreIndicatorByParentId" resultType="com.onecity.os.management.yulan.po.IndicatorYuLan">
        select
        <include refid="Base_Column_List"/>
        from general_indicator_tianbao
        <include refid="where"/>
        order by sequence
    </select>

    <select id="getIndicatorListByParentId" resultType="com.onecity.os.management.yulan.po.IndicatorYuLan">
        select
        <include refid="Base_Column_List"/>
        from general_indicator_tianbao
        where parent_id = #{indicatorId}
          and is_delete = 0
    </select>

    <select id="getInfoByIndicatorId" resultType="com.onecity.os.management.yulan.po.IndicatorYuLan">
        select
        <include refid="Base_Column_List"/>
        from general_indicator_tianbao
        where id = #{indicatorId}
          and is_delete = 0
    </select>

    <select id="getIndicatorInfoByIndicatorId" resultType="com.onecity.os.management.yulan.po.IndicatorYuLan">
        select
        <include refid="Base_Column_List"/>
        from general_indicator_tianbao
        where id = #{indicatorId}
        and is_delete = 0 and is_show = 1
    </select>

</mapper>