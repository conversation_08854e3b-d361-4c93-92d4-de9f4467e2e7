<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.zhibiao.mapper.GeneralIndicatorDataMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.management.zhibiao.entity.GeneralIndicatorData">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="indicator_id" jdbcType="VARCHAR" property="indicatorId"/>
        <result column="indicator_name" jdbcType="VARCHAR" property="indicatorName"/>
        <result column="item_name" jdbcType="VARCHAR" property="itemName"/>
        <result column="item_value" jdbcType="VARCHAR" property="itemValue"/>
        <result column="item_unit" jdbcType="VARCHAR" property="itemUnit"/>
        <result column="identify" jdbcType="VARCHAR" property="identify"/>
        <result column="style" jdbcType="INTEGER" property="style"/>
        <result column="is_fold" jdbcType="INTEGER" property="isFold"/>
        <result column="sequence" jdbcType="INTEGER" property="sequence"/>
        <result column="update_date" jdbcType="VARCHAR" property="updateDate"/>
        <result column="current_flag" jdbcType="INTEGER" property="currentFlag"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="creater" jdbcType="VARCHAR" property="creater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
    </resultMap>

    <sql id="Base_Column_List">
        a.id as id,a.indicator_id as indicatorId,a.indicator_name as indicatorName,
        a.item_name as itemName,a.item_value as itemValue,a.item_value1 as itemValue1,a.item_value2 as itemValue2,
        a.item_value3 as itemValue3,a.item_value4 as itemValue4 ,a.item_value5 as itemValue5,a.item_value6 as itemValue6,
        a.item_unit as itemUnit,a.item_unit_2nd as itemUnit2nd,
        a.identify as identify,a.style as style,a.is_fold as isFold,
        a.sequence as sequence,a.update_date as updateDate,a.current_flag as currentFlag,
        a.is_delete as isDelete,a.create_time as createTime,a.creater as creater,
        a.update_time as updateTime,a.updater as updater
    </sql>

    <select id="getIndicatorDataListBySourceIds"
            resultType="com.onecity.os.management.zhibiao.entity.GeneralIndicatorDataTianbao">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        general_indicator_data_tianbao a
        LEFT JOIN general_indicator_tianbao b ON b.id = a.indicator_id
        WHERE
        b.source_id IN
        <foreach collection="sources" item="source" open="(" separator="," close=")">
            #{source}
        </foreach>
        order by a.id
    </select>

    <insert id="insertData" parameterType="com.onecity.os.management.zhibiao.entity.GeneralIndicatorData">
        INSERT INTO
        `general_indicator_data_tianbao` (
            `id`,
            `indicator_id`,
            `indicator_name`,
            `item_name`,
            `item_value`,
            `item_unit`,
            `identify`,
            `style`,
            `is_fold`,
            `sequence`,
            `update_date`,
            `current_flag`,
            `is_delete`,
            `create_time`,
            `creater`,
            `update_time`,
            `updater`
        )
        VALUES
            (
                #{vo.id},
                #{vo.indicatorId},
                #{vo.indicatorName},
                #{vo.itemName},
                #{vo.itemValue},
                #{vo.itemUnit},
                #{vo.identify},
                #{vo.style},
                #{vo.isFold},
                #{vo.sequence},
                #{vo.updateDate},
                #{vo.currentFlag},
                #{vo.isDelete},
                #{vo.createTime},
                #{vo.creater},
                #{vo.updateTime},
                #{vo.updater}
            );
    </insert>

    <select id="getExportIndicatorDataXlsByIndicatorId"
            resultType="com.onecity.os.management.zhibiao.model.vo.IndicatorDataExcel">
        SELECT
        <include refid="Base_Column_List"/>,
            b.indicator_name AS indicatorName,
            b.indicator_exhibit_type AS `type`
        FROM
            general_indicator_data_tianbao a
            LEFT JOIN general_indicator_tianbao b ON b.id = a.indicator_id
        WHERE
            a.indicator_id = #{indicatorId}
            AND a.is_delete = 0
        ORDER BY
            a.sequence,
            a.update_time
    </select>

    <select id="getIndicatorDataNameListByIndicatorId" resultType="String">
        SELECT
            item_name
        FROM
            general_indicator_data_tianbao
        WHERE
            indicator_id = #{indicatorId}
            and is_delete = 0
    </select>

    <insert id="insertTianBaoIndicatorData">
        INSERT INTO `general_indicator_data_tianbao` (
            `id`,
            `indicator_id`,
            `indicator_name`,
            `item_name`,
            `item_value`,
            `item_unit`,
            `identify`,
            `style`,
            `is_fold`,
            `sequence`,
            `update_date`,
            `current_flag`,
            `is_delete`,
            `create_time`,
            `creater`,
            `update_time`,
            `updater`
        )
        VALUES
            (
                NULL,
                #{vo.indicatorId},
                NULL,
                #{vo.itemName},
                #{vo.itemValue},
                #{vo.itemUnit},
                #{vo.identify},
                #{vo.style},
                #{vo.isFold},
                #{vo.sequence},
                #{vo.updateDate},
                #{vo.currentFlag},
                0,
                #{vo.createTime},
                #{vo.creater},
                #{vo.updateTime},
                #{vo.updater}
            );
    </insert>

    <update id="deleteDataByIndicatorId">
        UPDATE
        general_indicator_data_tianbao
        SET is_delete = 1
        WHERE
            indicator_id = #{indicatorId}
            AND is_delete = 0
    </update>

    <update id="deleteDataByIndicatorIds">
        update
        general_indicator_data_tianbao
        set is_delete = 1
        where is_delete = 0
        and indicator_id in
        <foreach collection="indicatorIds" item="indicatorId" open="(" separator="," close=")">
            #{indicatorId}
        </foreach>
    </update>

    <update id="updateByIndicatorIdAndItemName">
        UPDATE
        `general_indicator_data_tianbao`
        SET
        `item_name` = #{vo.itemName},
        `item_value` = #{vo.itemValue},
        `item_unit` = #{vo.itemUnit},
        `identify` = #{vo.identify},
        `style` = #{vo.style},
        `is_fold` = #{vo.isFold},
        `sequence` = #{vo.sequence},
        `update_date` = #{vo.updateDate},
        `current_flag` = #{vo.currentFlag},
        `is_delete` = 0,
        `update_time` = #{vo.updateTime},
        `updater` = #{vo.updater}
        WHERE
            `indicator_id` = #{vo.indicatorId} AND `item_name` = #{vo.itemName} ORDER BY create_time DESC LIMIT 1
    </update>

    <select id="getGeneralIndicatorDataByIndicatorId" resultMap="BaseResultMap">
        select * from general_indicator_data
        where indicator_id = #{indicatorId} and is_delete=0
    </select>

    <update id="updateByList" parameterType="com.onecity.os.management.zhibiao.entity.GeneralIndicatorData">
        <foreach collection="dataList" item="vo" separator=";">
            UPDATE
              general_indicator_data
            SET
                item_name = #{vo.itemName},
                item_value = #{vo.itemValue},
                item_value1 = #{vo.itemValue1},
                item_value2 = #{vo.itemValue2},
                item_value3 = #{vo.itemValue3},
                item_value4 = #{vo.itemValue4},
                item_value5 = #{vo.itemValue5},
                item_value6 = #{vo.itemValue6},
                item_unit = #{vo.itemUnit},
                item_unit_2nd = #{vo.itemUnit2nd},
                identify = #{vo.identify},
                style = #{vo.style},
                is_fold = #{vo.isFold},
                sequence = #{vo.sequence},
                update_date = #{vo.updateDate},
                current_flag = #{vo.currentFlag},
                is_delete = #{vo.isDelete},
                update_time = #{vo.updateTime},
                updater = #{vo.updater},
                url_ids = #{vo.urlIds},
                url_name = #{vo.urlName},
                url_type = #{vo.urlType}
            WHERE
                id = #{vo.id} and indicator_id = #{vo.indicatorId}
        </foreach>
    </update>

    <insert id="insertByList" parameterType="com.onecity.os.management.zhibiao.entity.GeneralIndicatorData">
       insert into general_indicator_data
       (id ,indicator_id ,indicator_name ,item_name ,item_value ,item_value1 ,item_value2 ,
        item_value3 ,item_value4  ,item_value5 ,item_value6 ,item_unit ,item_unit_2nd ,
        identify ,style ,is_fold ,sequence ,update_date ,current_flag ,
        is_delete ,create_time ,creater ,update_time ,updater,url_ids,url_name,url_type )
        values
        <foreach collection="dataList" item="vo" separator=",">
            (
            #{vo.id},
            #{vo.indicatorId},
            #{vo.indicatorName},
            #{vo.itemName},
            #{vo.itemValue},
            #{vo.itemValue1},
            #{vo.itemValue2},
            #{vo.itemValue3},
            #{vo.itemValue4},
            #{vo.itemValue5},
            #{vo.itemValue6},
            #{vo.itemUnit},
            #{vo.itemUnit2nd},
            #{vo.identify},
            #{vo.style},
            #{vo.isFold},
            #{vo.sequence},
            #{vo.updateDate},
            #{vo.currentFlag},
            0,
            #{vo.createTime},
            #{vo.creater},
            #{vo.updateTime},
            #{vo.updater},
            #{vo.urlIds},
            #{vo.urlName},
            #{vo.urlType}
            )
        </foreach>
    </insert>


    <select id="getIndicatorDataIdListBySourceIds" resultType="long">
        SELECT
          a.id
        FROM
        general_indicator_data a
        LEFT JOIN general_indicator b ON b.id = a.indicator_id
        WHERE
        b.source_id IN
        <foreach collection="sources" item="source" open="(" separator="," close=")">
            #{source}
        </foreach>
        order by a.id
    </select>

    <select id="queryItemValueByNameID" resultType="com.onecity.os.management.zhibiaowarning.entity.vo.IndicatorItemValueVO">
        SELECT
        a.item_name as itemName,
        <choose>
            <when test="itemValue == 'item_value'">
                a.item_value
            </when>
            <when test="itemValue == 'item_value1'">
                a.item_value1
            </when>
            <when test="itemValue == 'item_value2'">
                a.item_value2
            </when>
            <when test="itemValue == 'item_value3'">
                a.item_value3
            </when>
            <when test="itemValue == 'item_value4'">
                a.item_value4
            </when>
            <when test="itemValue == 'item_value5'">
                a.item_value5
            </when>
            <when test="itemValue == 'item_value6'">
                a.item_value6
            </when>
            <otherwise>
                a.item_value
            </otherwise>
        </choose>
        AS itemValue
        FROM general_indicator_data_tianbao a
        CROSS JOIN (
        SELECT MAX(update_date) as updateDate
        FROM general_indicator_data_tianbao
        WHERE current_flag = 1
        AND is_delete = 0
        AND indicator_id = #{indicatorId}
        <if test="itemName != null and itemName != ''">
            AND item_name = #{itemName}
        </if>
        ) aa
        WHERE a.indicator_id = #{indicatorId}
        AND a.update_date = aa.updateDate
        AND a.current_flag = 1
        AND a.is_delete = 0
        <if test="itemName != null and itemName != ''">
            AND a.item_name = #{itemName}
        </if>
        ORDER BY a.sequence
    </select>
    <select id="queryLastItemValueByNameID"
            resultType="com.onecity.os.management.zhibiaowarning.entity.vo.IndicatorItemValueVO">
        (
            SELECT
                t.update_date AS updateDate,
                t.item_name AS itemName,
                t.item_value AS itemValue
            FROM general_indicator_data_tianbao t
                     INNER JOIN (
                -- 获取当前 item_name 下的最大 update_date
                SELECT indicator_id, MAX(update_date) AS max_update_date
                FROM general_indicator_data_tianbao
                WHERE indicator_id = #{indicatorId}
                  AND item_name = #{itemName}
                  AND current_flag = 1
                  AND is_delete = 0
                GROUP BY indicator_id
            ) tmp ON t.indicator_id = tmp.indicator_id
                AND t.update_date = tmp.max_update_date
            WHERE t.current_flag = 1
              AND t.is_delete = 0
        )
        UNION ALL
        (
        SELECT
            t.update_date AS updateDate,
            t.item_name AS itemName,
            t.item_value AS itemValue
        FROM general_indicator_data_tianbao t
                 INNER JOIN (
            -- 获取该指标下比当前 update_date 更早的最后一个 update_date
            SELECT update_date
                FROM general_indicator_data_tianbao
            WHERE
                indicator_id = #{indicatorId}
              AND current_flag = 1
              AND is_delete = 0
              AND update_date &lt; (
                -- 当前 item_name 下的最大 update_date
                SELECT MAX(update_date)
                FROM general_indicator_data_tianbao
                WHERE
                    indicator_id = #{indicatorId}
                        <if test="itemName != null and itemName != ''">
                            AND item_name = #{itemName}
                        </if>
                  AND current_flag = 1
                  AND is_delete = 0
                 )
            ORDER BY update_date DESC
            LIMIT 1
        ) tmp ON t.update_date = tmp.update_date
            WHERE
                t.indicator_id = #{indicatorId}
              AND t.current_flag = 1
              AND t.is_delete = 0
            )
    </select>

    <select id="queryLastYearItemValueByNameID" resultType="com.onecity.os.management.zhibiaowarning.entity.vo.IndicatorItemValueVO">
        SELECT
            a.update_date AS updateDate,
            a.item_name AS itemName,
            a.item_value AS itemValue
        FROM general_indicator_data_tianbao a
        WHERE a.indicator_id = #{indicatorId}
        <if test="itemName != null and itemName != ''">
            AND a.item_name = #{itemName}
        </if>
          AND a.current_flag = 1
          AND a.is_delete = 0
          AND a.update_date IN (
            SELECT update_date
            FROM general_indicator_data_tianbao
            WHERE indicator_id = #{indicatorId}
            <if test="itemName != null and itemName != ''">
                AND a.item_name = #{itemName}
            </if>
              AND current_flag = 1
              AND is_delete = 0
            ORDER BY update_date DESC
            LIMIT 2
            )
        ORDER BY a.update_date DESC
</select>

    <select id="queryAllForWarnRule" resultType="com.onecity.os.management.zhibiaowarning.entity.vo.IndicatorItemValueVO">
        SELECT
        a.update_date AS updateDate,
        a.item_name AS itemName,
        <choose>
            <when test="itemValue == 'item_value'">
                a.item_value
            </when>
            <when test="itemValue == 'item_value1'">
                a.item_value1
            </when>
            <when test="itemValue == 'item_value2'">
                a.item_value2
            </when>
            <when test="itemValue == 'item_value3'">
                a.item_value3
            </when>
            <when test="itemValue == 'item_value4'">
                a.item_value4
            </when>
            <when test="itemValue == 'item_value5'">
                a.item_value5
            </when>
            <when test="itemValue == 'item_value6'">
                a.item_value6
            </when>
            <otherwise>
                a.item_value
            </otherwise>
        </choose>
        AS itemValue
        FROM general_indicator_data_tianbao a
        WHERE a.indicator_id = #{indicatorId}
<!--        <if test="itemName != null and itemName != ''">-->
<!--            AND a.item_name = #{itemName}-->
<!--        </if>-->
        AND a.current_flag = 1
        AND a.is_delete = 0
        ORDER BY a.update_date DESC ,a.item_name DESC
    </select>

</mapper>





















