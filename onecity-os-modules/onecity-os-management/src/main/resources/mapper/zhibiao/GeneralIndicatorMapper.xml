<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.zhibiao.mapper.GeneralIndicatorMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.management.zhibiao.entity.GeneralIndicator">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="indicator_name" jdbcType="VARCHAR" property="indicatorName"/>
        <result column="indicator_exhibit_type" jdbcType="VARCHAR" property="indicatorExhibitType"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="parent_name" jdbcType="VARCHAR" property="parentName"/>
        <result column="icon_url" jdbcType="VARCHAR" property="iconUrl"/>
        <result column="source_id" jdbcType="VARCHAR" property="sourceId"/>
        <result column="source_name" jdbcType="VARCHAR" property="sourceName"/>
        <result column="sequence" jdbcType="INTEGER" property="sequence"/>
        <result column="indicator_type" jdbcType="INTEGER" property="indicatorType"/>
        <result column="update_date" jdbcType="VARCHAR" property="updateDate"/>
        <result column="update_cycle" jdbcType="VARCHAR" property="updateCycle"/>
        <result column="leader" jdbcType="VARCHAR" property="leader"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="creater" jdbcType="VARCHAR" property="creater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="group_type" jdbcType="INTEGER" property="groupType"/>
        <result column="group_url" jdbcType="VARCHAR" property="groupUrl"/>
        <result column="plan_update_date" jdbcType="VARCHAR" property="planUpdateDate"/>
        <result property="isShow" column="is_show" jdbcType="INTEGER"/>
        <result property="isScreen" column="is_screen" jdbcType="INTEGER"/>
        <result property="isLegend" column="is_legend" jdbcType="INTEGER"/>
        <result property="dataUpdateMode" column="data_update_mode" jdbcType="INTEGER"/>
        <result property="dataConfigId" column="data_config_id" jdbcType="VARCHAR"/>
        <result property="urlIds" column="url_ids" jdbcType="VARCHAR"/>
        <result property="urlName" column="url_name" jdbcType="VARCHAR"/>
        <result property="urlType" column="url_type" jdbcType="VARCHAR"/>
        <result property="sortType" column="sort_type" jdbcType="VARCHAR"/>
        <result property="paraUrl" column="para_url" jdbcType="VARCHAR"/>
        <result property="nameShowFlag" column="name_show_flag" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id as id,indicator_name as indicatorName,indicator_exhibit_type as indicatorExhibitType,
        parent_id as parentId,parent_name as parentName,icon_url as iconUrl,
        source_id as sourceId,source_name as sourceName,sequence as sequence,
        indicator_type as indicatorType,update_date as updateDate,update_cycle as updateCycle,
        leader as leader,is_delete as isDelete,create_time as createTime,
        creater as creater,update_time as updateTime,updater as updater,
        group_type as groupType,group_url as groupUrl,plan_update_date as planUpdateDate,
        is_show as isShow,is_legend AS isLegend,is_screen as isScreen,data_update_mode as dataUpdateMode,
        data_config_id as dataConfigId,url_ids as urlIds,url_type as urlType,url_name as urlName,
        sort_type as sortType,para_url as paraUrl,
        name_show_flag as nameShowFlag
    </sql>

    <sql id="Base_Column_List_Tianbao">
        ind.id as id,ind.indicator_name as indicatorName,ind.indicator_exhibit_type as indicatorExhibitType,
        ind.parent_id as parentId,ind.parent_name as parentName,ind.icon_url as iconUrl,
        ind.source_id as sourceId,ind.source_name as sourceName,ind.sequence as sequence,
        ind.indicator_type as indicatorType,ind.update_date as updateDate,ind.update_cycle as updateCycle,
        ind.leader as leader,ind.is_delete as isDelete,ind.create_time as createTime,
        ind.creater as creater,ind.update_time as updateTime,ind.updater as updater,
        ind.group_type as groupType,ind.group_url as groupUrl,ind.plan_update_date as planUpdateDate,
        ind.is_show as isShow,ind.is_screen as isScreen,ind.is_legend AS isLegend,ind.data_update_mode as dataUpdateMode,
        ind.data_config_id as dataConfigId
    </sql>

    <select id="getIndicatorListBySourceIds"
            resultType="com.onecity.os.management.zhibiao.entity.GeneralIndicatorTianbao">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        general_indicator
        WHERE
        source_id IN
        <foreach collection="sources" item="source" open="(" separator="," close=")">
            #{source}
        </foreach>
        order by id
    </select>

    <select id="getIndicatorInfoById" resultType="com.onecity.os.management.zhibiao.entity.GeneralIndicator">
        select <include refid="Base_Column_List"/> from general_indicator where id = #{id}
    </select>

    <select id="getIndicatorListBySourceIdAndCycle" resultType="String">
        SELECT
        c3.indicator_name
        FROM
        (
        SELECT
        c1.id,
        c1.parent_id,
        c1.indicator_name,
        c1.update_cycle,
        c1.indicator_type,
        IF
        (
        find_in_set( c1.parent_id, @p ) > 0,
        @p := concat( @p, ',', c1.id ),
        0
        ) AS childIds
        FROM
        (
        SELECT
        c.id,
        c.parent_id,
        c.indicator_name,
        c.update_cycle,
        c.indicator_type
        FROM
        general_indicator c
        WHERE
        c.source_id = #{sourceId}
        AND c.is_delete = 0
        ORDER BY
        c.update_cycle
        ) c1,
        ( SELECT @p := #{parentIndicatorId} ) c2
        ) c3
        WHERE
        c3.childIds != '0'
        AND c3.indicator_type = 0
        AND c3.update_cycle = #{updateCycle}
        <if test="null != startTime and '' != startTime">
            AND c3.update_time &gt;= #{startTime}
        </if>
        <if test="null != endTime and '' != endTime">
            AND c3.update_time &lt;= #{endTime}
        </if>
    </select>

    <select id="getParentIndicatorIdListBySourceId" resultType="String">
        SELECT id
        FROM general_indicator
        WHERE is_delete = 0
          AND parent_id = '0'
          AND source_id = #{sourceId}
    </select>

    <select id="getIndicatorInfoListBySourceIdAndCycle"
            resultType="com.onecity.os.management.configmanage.entity.dto.RemindIndicatorInfoDto">
        SELECT
        c3.id as id, c3.indicator_name as indicatorName, c3.update_time as updateTime, c3.plan_update_date as planUpdateDate, c3.update_time AS updateTimeDate
        FROM
        (
        SELECT
        c1.id,
        c1.parent_id,
        c1.indicator_name,
        c1.update_cycle,
        c1.update_time,
        c1.plan_update_date,
        c1.indicator_type,
        c1.sequence,
        c1.create_time,
        IF
        (
        find_in_set( c1.parent_id, @p ) > 0,
        @p := concat( @p, ',', c1.id ),
        0
        ) AS childIds
        FROM
        (
        SELECT
        c.id,
        c.parent_id,
        c.indicator_name,
        c.update_cycle,
        c.update_time,
        c.plan_update_date,
        c.indicator_type,
        c.sequence,
        c.create_time
        FROM
        general_indicator c
        WHERE
        c.source_id = #{sourceId}
        AND c.is_delete = 0
        ORDER BY
        c.update_cycle
        ) c1,
        ( SELECT @p := #{parentIndicatorId} ) c2
        ) c3
        WHERE
        c3.childIds != '0'
        AND c3.indicator_type = 0
        AND c3.update_cycle = #{updateCycle}
        <if test="null != startTime and '' != startTime">
            AND c3.create_time &gt;= #{startTime}
        </if>
        <if test="null != endTime and '' != endTime">
            AND c3.create_time &lt;= #{endTime}
        </if>
        ORDER BY
        c3.sequence
    </select>

    <select id="getParentIndicatorIdsBySourceId" resultType="String">
        SELECT id
        FROM general_indicator
        WHERE is_delete = 0
          and parent_id = '0'
          and source_id = #{sourceSimpleName}
          and data_update_mode = 1
    </select>

    <select id="getIndicatorsByParentIdAndSourceByPage"
            resultType="com.onecity.os.management.zhibiao.model.dto.IndicatorCycleDto">
        SELECT
        <include refid="Base_Column_List_Tianbao"/>,
        pInd.indicator_name AS parentName
        FROM
        general_indicator_tianbao AS ind
        LEFT JOIN general_indicator_tianbao AS pInd ON ind.parent_id = pInd.id
        WHERE
        ind.is_delete = 0
        AND ind.indicator_type = 0
        AND ind.update_cycle != ''
        AND ind.update_cycle IS NOT NULL
        <if test="null != updateCycle and '' != updateCycle">
            AND ind.update_cycle = #{updateCycle}
        </if>
        AND ind.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY
        ind.create_time DESC
    </select>

    <update id="updateIndicatorPlanCycleById">
        UPDATE general_indicator
        SET update_cycle=#{vo.updateCycle},
            plan_update_date=#{vo.planUpdateDate}
        WHERE id = #{vo.id}
    </update>

    <update id="updateIndicatorTianbaoPlanCycleById">
        UPDATE general_indicator_tianbao
        SET update_cycle=#{vo.updateCycle},
            plan_update_date=#{vo.planUpdateDate}
        WHERE id = #{vo.id}
    </update>


    <select id="getParentIndicatorIdBySourceIds" resultType="String">
        SELECT
        id
        FROM
        general_indicator
        WHERE
        is_delete = 0
        AND parent_id = '0'
        AND source_id IN
        <foreach collection="sourceIds" item="sourceId" open="(" separator="," close=")">
            #{sourceId}
        </foreach>
    </select>

    <select id="getParentIndicatorIdBySourceNames" resultType="String">
        SELECT
        id
        FROM
        general_indicator_tianbao
        WHERE
        is_delete = 0
        AND parent_id = '0'
        AND source_name IN
        <foreach collection="sourceNames" item="sourceName" open="(" separator="," close=")">
            #{sourceName}
        </foreach>
    </select>

    <select id="getParentIndicatorIdBySourceName" resultType="String">
        SELECT
        id
        FROM
        general_indicator_tianbao
        WHERE
        is_delete = 0
        AND parent_id = '0'
        AND source_id = #{sourceName}
    </select>

    <select id="getIndicatorsByParentIds" resultType="com.onecity.os.management.zhibiao.entity.GeneralIndicator">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        general_indicator
        WHERE
        is_delete = 0
        AND parent_id IN
        <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>
    </select>

    <select id="getIndicatorsByParentIdsAndTJ" resultType="com.onecity.os.management.zhibiao.entity.GeneralIndicator">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        general_indicator
        WHERE
        is_delete = 0
        and source_id = #{tj}
        AND parent_id IN
        <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>
    </select>

    <select id="getAllIndicatorsByParentIdsAndTJ" resultType="com.onecity.os.management.zhibiao.entity.GeneralIndicator">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        general_indicator
        WHERE
        source_id = #{tj} and is_delete = 0
        AND parent_id IN
        <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>
    </select>

    <select id="getIndicatorsByParentIdsAndupdateCycle"
            resultType="com.onecity.os.management.configmanage.entity.dto.RemindIndicatorInfoDto">
        SELECT
        ind.id as id, ind.indicator_name as indicatorName, ind.update_time as updateTime, ind.plan_update_date as planUpdateDate, ind.update_time AS updateTimeDate,
        ind.update_cycle as updateCycle,ind.indicator_type as indicatorType,parent.indicator_name AS parentName
        from
        general_indicator AS ind
        LEFT JOIN general_indicator AS parent ON parent.id = ind.parent_id
        WHERE
        ind.is_delete = 0
        <if test="null != updateCycle and '' != updateCycle">
            AND ind.update_cycle = #{updateCycle}
        </if>
        AND ind.parent_id IN
        <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>
        <if test="null != startTime and '' != startTime">
            AND ind.create_time &gt;= #{startTime}
        </if>
        <if test="null != endTime and '' != endTime">
            AND ind.create_time &lt;= #{endTime}
        </if>
        ORDER BY
        ind.sequence
    </select>

    <select id="getIndicatorIdsByParentIds" resultType="String">
        SELECT
        id
        FROM
        general_indicator
        WHERE
        is_delete = 0
        and data_update_mode = 1
        AND parent_id IN
        <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>
    </select>

    <select id="getIndicatorTianBaoIdsByParentIds" resultType="String">
        SELECT
        id
        FROM
        general_indicator_tianbao
        WHERE
        is_delete = 0
        AND parent_id IN
        <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>
    </select>

    <select id="getIndicatorNameSourceNameById"
            resultType="com.onecity.os.management.zhibiao.model.vo.IndicatorDataExcel">
        SELECT a.indicator_name         AS indicatorName,
               a.indicator_exhibit_type AS type,
               b.source_name as sourceName
        FROM general_indicator_tianbao a
                 LEFT JOIN source_manage b on b.source_simple_name = a.source_id
        WHERE a.id = #{indicatorId} LIMIT 1
    </select>

    <select id="getIndicatorExhibitTypeById" resultType="String">
        SELECT indicator_exhibit_type
        FROM general_indicator
        WHERE id = #{indicatorId}
          AND is_delete = 0 LIMIT 1
    </select>

    <select id="getIndicatorExhibitTypeByIdTianbao" resultType="String">
        SELECT indicator_exhibit_type
        FROM general_indicator_tianbao
        WHERE id = #{indicatorId}
          AND is_delete = 0 LIMIT 1
    </select>

    <select id="getIndicatorCycleByIdTianbao" resultType="String">
        SELECT update_cycle
        FROM general_indicator_tianbao
        WHERE id = #{indicatorId}
          AND is_delete = 0 LIMIT 1
    </select>

    <select id="selectUpdateIndicatorsBySource" resultType="com.onecity.os.management.statisticanalysis.entity.IndicatorUpdateInfo">
        SELECT gitur.id as id,gitur.indicator_id as indicatorId,gitur.indicator_name as indicatorName,
        gitur.create_time as createTime,gitur.is_send_ding as isSendDing, git.source_name as sourceName
        from general_indicator_tianbao_update_record gitur
        left join general_indicator_tianbao git on gitur.indicator_id = git.id
        WHERE gitur.is_send_ding = 0 and git.data_update_mode=1 and git.source_id in
        <foreach collection="sources" item="source" open="(" separator="," close=")">
            #{source}
        </foreach>
          AND git.is_delete = 0 and data_flag = 1
    </select>

    <select id="selectTodayUpdateIndicatorsBySourceForWarn" resultType="com.onecity.os.management.statisticanalysis.entity.IndicatorUpdateInfo">
        SELECT gitur.id as id,gitur.indicator_id as indicatorId,gitur.indicator_name as indicatorName,
        gitur.create_time as createTime,gitur.is_send_ding as isSendDing, git.source_name as sourceName,gitur.data_flag as dataFlag
        from general_indicator_tianbao_update_record gitur
        left join general_indicator_tianbao git on gitur.indicator_id = git.id
        WHERE  git.data_update_mode=1 and gitur.create_time >= #{date}
          and git.source_id in
        <foreach collection="sources" item="source" open="(" separator="," close=")">
            #{source}
        </foreach>
        AND git.is_delete = 0 and gitur.data_flag = 1 group by gitur.indicator_id
    </select>

    <update id="updateByList" parameterType="com.onecity.os.management.zhibiao.entity.GeneralIndicator">
        <foreach collection="dataList" item="vo" separator=";">
            UPDATE
            general_indicator
            SET
            `indicator_name` = #{vo.indicatorName},
            `indicator_exhibit_type` = #{vo.indicatorExhibitType},
            `parent_id` = #{vo.parentId},
            <if test="null != vo.parentName and '' != vo.parentName">
                `parent_name` = #{vo.parentName},
            </if>
            <if test="null != vo.iconUrl and '' != vo.iconUrl">
                `icon_url` = #{vo.iconUrl},
            </if>
            <if test="null != vo.updateDate and '' != vo.updateDate">
                `update_date` = #{vo.updateDate},
            </if>
            <if test="null != vo.leader and '' != vo.leader">
                `leader` = #{vo.leader},
            </if>
            <if test="null != vo.createTime">
                `create_time` = #{vo.createTime},
            </if>
            <if test="null != vo.creater and '' != vo.creater">
                `creater` = #{vo.creater},
            </if>
            <if test="null != vo.updateTime">
                `update_time` = #{vo.updateTime},
            </if>
            <if test="null != vo.updater and '' != vo.updater">
                `updater` = #{vo.updater},
            </if>
            <if test="null != vo.groupType">
                `group_type` = #{vo.groupType},
            </if>
            <if test="null != vo.groupUrl and '' != vo.groupUrl">
                `group_url` = #{vo.groupUrl},
            </if>
            `update_cycle` = #{vo.updateCycle},
            `sequence` = #{vo.sequence},
            `indicator_type` = #{vo.indicatorType},
            `plan_update_date` = #{vo.planUpdateDate},
            `is_delete` = #{vo.isDelete},
            `is_show` = #{vo.isShow},
            `is_screen` = #{vo.isScreen},
            `is_legend` = #{vo.isLegend},
            `data_config_id` = #{vo.dataConfigId},
            `data_update_mode` = #{vo.dataUpdateMode},
            `url_ids` = #{vo.urlIds},
            url_name = #{vo.urlName},
            url_type = #{vo.urlType},
            `sort_type` = #{vo.sortType},
            `para_url` = #{vo.paraUrl},
            name_show_flag = #{vo.nameShowFlag}
            WHERE
            `id` = #{vo.id}
        </foreach>
    </update>


    <insert id="insertByList" parameterType="com.onecity.os.management.zhibiao.entity.GeneralIndicator">
        insert into general_indicator
        (id,indicator_name,indicator_exhibit_type,parent_id,parent_name,icon_url,
        source_id,source_name,sequence,indicator_type,update_date,update_cycle,
        leader ,is_delete ,create_time ,creater ,update_time ,updater ,group_type ,group_url ,plan_update_date ,
        is_show ,is_screen ,is_legend ,data_update_mode ,data_config_id,url_ids,url_name,url_type,sort_type,para_url,name_show_flag)
        values
        <foreach collection="dataList" item="vo" separator=",">
            (
            #{vo.id},
            #{vo.indicatorName},
            #{vo.indicatorExhibitType},
            #{vo.parentId},
            #{vo.parentName},
            #{vo.iconUrl},
            #{vo.sourceId},
            #{vo.sourceName},
            #{vo.sequence},
            #{vo.indicatorType},
            #{vo.updateDate},
            #{vo.updateCycle},
            #{vo.leader},
            0,
            #{vo.createTime},
            #{vo.creater},
            #{vo.updateTime},
            #{vo.updater},
            #{vo.groupType},
            #{vo.groupUrl},
            #{vo.planUpdateDate},
            #{vo.isShow},
            #{vo.isScreen},
            #{vo.isLegend},
            #{vo.dataUpdateMode},
            #{vo.dataConfigId},
            #{vo.urlIds},
            #{vo.urlName},
            #{vo.urlType},
             #{vo.sortType},
             #{vo.paraUrl},
            #{vo.nameShowFlag}
            )
        </foreach>
    </insert>
</mapper>










