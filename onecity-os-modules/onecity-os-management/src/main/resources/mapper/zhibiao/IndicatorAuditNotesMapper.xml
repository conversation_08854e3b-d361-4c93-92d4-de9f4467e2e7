<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.onecity.os.management.zhibiao.mapper.IndicatorAuditNotesMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.management.zhibiao.entity.IndicatorAuditNotes">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="source_id" property="sourceId" jdbcType="VARCHAR"/>
        <result column="commit_user_id" property="commitUserId" jdbcType="VARCHAR"/>
        <result column="commit_user_name" property="commitUserName" jdbcType="VARCHAR"/>
        <result column="audit_user_id" property="auditUserId" jdbcType="VARCHAR"/>
        <result column="audit_user_name" property="auditUserName" jdbcType="VARCHAR"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="audit_result" property="auditResult" jdbcType="TINYINT"/>
        <result column="audit_views" property="auditViews" jdbcType="VARCHAR"/>
        <result column="commit_time" property="commitTime" jdbcType="TIMESTAMP"/>
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
    </resultMap>

    <resultMap id="DepartUserListDtoMap" type="com.onecity.os.management.zhibiao.model.dto.DepartUserListDto">
        <result column="departId" property="departId" jdbcType="VARCHAR"/>
        <result column="departName" property="departName" jdbcType="VARCHAR"/>
        <collection property="users" resultMap="UsersMap"/>
    </resultMap>
    <resultMap id="UsersMap" type="com.onecity.os.management.zhibiao.model.dto.UserNameDto">
        <result column="userId" property="userId" jdbcType="VARCHAR"/>
        <result column="realName" property="realName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id as id,source_id as sourceId,commit_user_id as commitUserId,
        commit_user_name as commitUserName,audit_user_id as auditUserId,
        audit_user_name as auditUserName,remarks as remarks,
        audit_result as auditResult,audit_views as auditViews,
        commit_time as commitTime,audit_time as auditTime,is_delete as isDelete
    </sql>

    <select id="getUserNameByName" resultMap="DepartUserListDtoMap">
        SELECT
        c.id AS departId,
        c.depart_name AS departName,
        a.id AS userId,
        a.realname AS realName
        FROM
        sys_user a
        LEFT JOIN sys_user_depart b ON b.user_id = a.id
        LEFT JOIN sys_depart c ON c.id = b.dep_id
        WHERE
        a.`status` = 1
        AND a.del_flag = 0
        AND c.del_flag = 0
        <if test="null != name and '' != name">
            AND INSTR(a.realname,#{name})
        </if>
    </select>

    <select id="getUserNameByNameAndSourceSimpleName" resultMap="DepartUserListDtoMap">
        SELECT
 	        f.id AS departId,
            f.depart_name AS departName,
            d.id AS userId,
            d.realname AS realName
        FROM
        	sys_permission a
            LEFT JOIN sys_role_permission b ON a.id = b.permission_id
            LEFT JOIN sys_user_role c ON b.role_id = c.role_id
            LEFT JOIN sys_user d ON c.user_id = d.id
            LEFT JOIN sys_user_depart e ON d.id = e.user_id
            LEFT JOIN sys_depart f ON e.dep_id = f.id
        WHERE
        	a.name = '指标数据审核'
            AND d.id IS NOT NULL
            AND d.del_flag = 0
            AND a.del_flag = 0
            AND f.del_flag = 0
            AND trim(SUBSTRING_INDEX(a.url, '/' ,- 1)) = #{sourceSimpleName}
        <if test="null != name and '' != name">
            and INSTR(d.realname,#{name})
        </if>
    </select>

    <select id="getUserNameByNameAndDepartId" resultType="com.onecity.os.management.zhibiao.model.dto.UserNameDto">
        SELECT
        a.id AS userId,
        a.realname AS realName
        FROM
        sys_user a
        WHERE
        a.`status` = 1
        AND a.del_flag = 0
        AND a.id NOT IN ( SELECT user_id FROM sys_user_depart )
        <if test="null != name and '' != name">
            AND INSTR(a.realname,#{name})
        </if>
    </select>

    <select id="getUserNameNoDepart" resultType="com.onecity.os.management.zhibiao.model.dto.UserNameDto">
        SELECT
        f.id AS departId,
        f.depart_name AS departName,
        d.id AS userId,
        d.realname AS realName
        FROM
        sys_permission a
        LEFT JOIN sys_role_permission b ON a.id = b.permission_id
        LEFT JOIN sys_user_role c ON b.role_id = c.role_id
        LEFT JOIN sys_user d ON c.user_id = d.id
        LEFT JOIN sys_user_depart e ON d.id = e.user_id
        LEFT JOIN sys_depart f ON e.dep_id = f.id
        WHERE
        a.name = '指标数据审核'
        AND d.id IS NOT NULL
        AND d.del_flag = 0
        AND a.del_flag = 0
        AND f.del_flag = 0
        AND trim(SUBSTRING_INDEX(a.url, '/' ,- 1)) = #{sourceSimpleName}
        AND f.id is null
        <if test="null != name and '' != name">
            and INSTR(d.realname,#{name})
        </if>
    </select>

    <select id="getSourceByAuditUserId" resultType="String">
        SELECT
            source_id
        FROM
            indicator_audit_notes
        WHERE
            is_delete = 0
        AND audit_user_id = #{userId}
        AND source_id = #{sourceId}
        AND audit_result = 2
        group by source_id
    </select>

    <select id="getSourceAndRemarksByAuditUserId" resultType="com.onecity.os.management.zhibiao.model.dto.IndicatorAuditNotesRemarks">
        SELECT
            source_id,
            remarks
        FROM
            indicator_audit_notes
        WHERE
            is_delete = 0
        AND audit_user_id = #{userId}
        AND source_id = #{sourceId}
        AND audit_result = 2
        order by commit_time desc limit 1
    </select>

    <update id="updateByAuditUserId">
        UPDATE
        indicator_audit_notes
        SET audit_result = #{vo.auditResult},audit_views=#{vo.auditViews},audit_time=#{vo.auditTime}
        WHERE
        audit_user_id = #{vo.userId}
        AND source_id = #{vo.sourceId}
        AND audit_result = 2
        AND is_delete = 0
    </update>

    <update id="updateNotDoByAuditUserId">
        UPDATE
        indicator_audit_notes
        SET audit_result = 3
        WHERE
        audit_user_id = #{userId}
        AND source_id = #{sourceId}
        AND audit_result = 2
        AND is_delete = 0
    </update>


    <update id="updateNotDoByAuditUserIdList">
        <foreach collection="dataList" item="vo" separator=";">
            UPDATE
            indicator_audit_notes
            SET audit_result = 3
            WHERE
            audit_user_id = #{vo.auditUserId}
            AND source_id = #{vo.sourceId}
            AND audit_result = 2
            AND is_delete = 0
        </foreach>
    </update>


    <select id="getIndicatorAuditNotes" resultType="com.onecity.os.management.zhibiao.entity.IndicatorAuditNotes">
        select <include refid="Base_Column_List"/> from indicator_audit_notes
        WHERE
        audit_user_id = #{userId}
        AND source_id = #{sourceId}
        AND audit_result = 2
        AND is_delete = 0
    </select>

    <select id="getIndicatorAuditNotesNotDone" resultType="com.onecity.os.management.zhibiao.entity.IndicatorAuditNotes">
        select <include refid="Base_Column_List"/> from indicator_audit_notes
        WHERE
        audit_user_id <![CDATA[<>]]> #{userId}
        AND source_id = #{sourceId}
        AND audit_result = 2
        AND is_delete = 0
    </select>

    <select id="getIndicatorAuditNotesList" resultType="com.onecity.os.management.zhibiao.entity.IndicatorAuditNotes">
        select <include refid="Base_Column_List"/> from indicator_audit_notes
        where is_delete = 0
        <if test="null != vo.sourceId and '' != vo.sourceId">
            and source_id = #{vo.sourceId}
        </if>
        <if test="null != vo.commitUserName and '' != vo.commitUserName">
            and INSTR(commit_user_name,#{vo.commitUserName})
        </if>
        <if test="null != vo.auditUserName and '' != vo.auditUserName">
            and INSTR(audit_user_name,#{vo.auditUserName})
        </if>
        <if test="null != vo.auditResult">
            and audit_result = #{vo.auditResult}
        </if>
        <if test="null != vo.startTime">
            and commit_time >= #{vo.startTime}
        </if>
        <if test="null != vo.endTime">
            and commit_time &lt;= #{vo.endTime}
        </if>
        <if test="null != vo.auditTimeStart">
            and audit_time >= #{vo.auditTimeStart}
        </if>
        <if test="null != vo.auditTimeEnd">
            and audit_time &lt;= #{vo.auditTimeEnd}
        </if>
        order by commit_time DESC
    </select>

    <select id="getWaitDoneCenterPageList"
            resultType="com.onecity.os.management.zhibiao.model.dto.GetWaitDoneCenterPageListDto">
        SELECT a.id as id,
               a.commit_time        AS createTime,
               b.source_name        AS sourceName,
               b.source_simple_name AS sourceSimpleName,
               a.audit_result       AS auditResult,
               a.audit_views        AS auditViews
        FROM indicator_audit_notes a
                 LEFT JOIN source_manage b ON b.source_simple_name = a.source_id
        WHERE a.is_delete = 0
          AND b.is_delete = 0
          AND b.is_start = 1
          AND ((a.audit_result = 2 AND a.audit_user_id = #{userId})
            OR (a.audit_result = 0 AND a.commit_user_id = #{userId}))
        ORDER BY a.commit_time DESC
    </select>

    <select id="getAuditTimeBySourceId" resultType="java.util.Date">
        SELECT
            a.audit_time
        FROM
            indicator_audit_notes a
            LEFT JOIN source_manage b ON b.source_simple_name = a.source_id
        WHERE
            a.is_delete = 0
            AND a.audit_result = 1
            AND b.is_delete = 0
            AND b.is_start = 1
            AND b.id = #{sourceId}
        ORDER BY
            a.audit_time DESC
            LIMIT 1
    </select>

    <select id="getAuditTimeBySourceSimpleName" resultType="java.util.Date">
        SELECT
            a.audit_time
        FROM
            indicator_audit_notes a
            LEFT JOIN source_manage b ON b.source_simple_name = a.source_id
        WHERE
            a.is_delete = 0
            AND a.audit_result = 1
            AND b.is_delete = 0
            AND b.is_start = 1
            AND b.source_simple_name = #{sourceSimpleName}
            AND a.audit_time &lt; #{date}
    ORDER BY
            a.audit_time DESC
            LIMIT 1
    </select>



</mapper>













