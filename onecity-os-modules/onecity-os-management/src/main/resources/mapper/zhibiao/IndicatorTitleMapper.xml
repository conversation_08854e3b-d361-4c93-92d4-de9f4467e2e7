<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.zhibiao.mapper.IndicatorTitleMapper">


    <insert id="insertIndicatorTitle" parameterType="com.onecity.os.management.zhibiao.entity.GeneralIndicatorDataTitle"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO general_indicator_data_title(id, indicator_id, indicator_name, item_name, is_delete,
                                                 create_time, creator, update_time, updater, item_value_name,
                                                 item_name_name, main_value, column_name, sequence)
        VALUES (#{title.id}, #{title.indicatorId}, #{title.indicatorName}, #{title.itemName},
                #{title.isDelete}, #{title.createTime}, #{title.creator}, #{title.updateTime},
                #{title.updater}, #{title.itemValueName}, #{title.itemNameName}, #{title.mainValue},
                #{title.columnName}, #{title.sequence})
    </insert>

    <update id="delIndicatorTitle" parameterType="String">
        UPDATE general_indicator_data_title AS t
        set t.is_delete = 1
        where t.id = #{id}
    </update>

    <update id="delIndicatorTitleByIndicatorId" parameterType="String">
        UPDATE general_indicator_data_title AS t
        set t.is_delete = 1
        where t.indicator_id = #{id}
    </update>

    <update id="updateIndicatorTitle"
            parameterType="com.onecity.os.management.zhibiao.entity.GeneralIndicatorDataTitle">
        UPDATE general_indicator_data_title
        <set>
            <if test="title.itemName != null and title.itemName != ''">
                item_name = #{title.itemName},
            </if>
            <if test="title.updateTime != null ">
                update_time = #{title.updateTime},
            </if>
            <if test="title.updater != null and title.updater != ''">
                updater = #{title.updater},
            </if>
            <if test="title.itemValueName != null and title.itemValueName != ''">
                item_value_name = #{title.itemValueName},
            </if>
            <if test="title.itemNameName != null  and title.itemNameName != ''">
                item_name_name = #{title.itemNameName},
            </if>
            <if test="title.mainValue != null  and title.mainValue != ''">
                main_value = #{title.mainValue},
            </if>
            <if test="title.columnName != null  and title.columnName != ''">
                column_name = #{title.columnName},
            </if>
            <if test="title.sequence != null  and title.sequence != ''">
                sequence = #{title.sequence}
            </if>
        </set>
        where id = #{title.id}
    </update>

    <select id="queryById"
            resultType="com.onecity.os.management.zhibiao.entity.GeneralIndicatorDataTitle">
        SELECT t.id,
               t.indicator_id    AS indicatorId,
               t.indicator_name  AS indicatorName,
               t.item_name       AS itemName,
               t.is_delete       AS isDelete,
               t.create_time     AS createTime,
               t.creator         AS creator,
               t.update_time     AS updateTime,
               t.updater         AS updater,
               t.item_value_name AS itemValueName,
               t.item_name_name  AS itemNameName,
               t.main_value      AS mainValue,
               t.column_name     AS columnName,
               t.sequence        AS sequence
        FROM general_indicator_data_title AS t
        WHERE 1 = 1
          AND t.is_delete = 0
          AND t.indicator_id = #{indicatorId}
        ORDER BY t.sequence
    </select>

    <update id="updateTitleMain" parameterType="String">
        UPDATE general_indicator_data_title AS t
        set t.main_value = #{mainValue}
        where t.id = #{id}
    </update>

    <update id="updateUnitByIndicatorId" parameterType="String">
        UPDATE general_indicator_data_tianbao AS t
        set t.item_unit     = #{itemUnit},
            t.item_unit_2nd = #{itemUnit2nd}
        where t.indicator_id = #{indicatorId}
    </update>
</mapper>





















