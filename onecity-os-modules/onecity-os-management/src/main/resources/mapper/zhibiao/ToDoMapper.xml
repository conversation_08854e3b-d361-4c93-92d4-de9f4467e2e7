<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.onecity.os.management.zhibiao.mapper.ToDoMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.management.zhibiao.entity.ToDo">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="source_id" property="sourceId" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="to_do_user_id" property="toDoUserId" jdbcType="VARCHAR"/>
        <result column="to_do_user_name" property="toDoUserName" jdbcType="VARCHAR"/>
        <result column="audit_result" property="auditResult" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="to_do_source" property="toDoSource" jdbcType="VARCHAR"/>
        <result column="questionnaire_id" property="questionnaireId" jdbcType="VARCHAR"/>
        <result column="questionnaire_jump" property="questionnaireJump" jdbcType="VARCHAR"/>
        <result column="step_id" property="stepId" jdbcType="VARCHAR"/>
    </resultMap>

    <delete id="deleteToDoById">
        UPDATE
        to_do
        SET is_delete=1,update_time=now()
        WHERE
        id = #{id}
        AND is_delete = 0
    </delete>

    <delete id="deleteToDoByUserIdAndSourceID">
        UPDATE
        to_do
        SET is_delete=1,update_time=now()
        WHERE
        to_do_user_id = #{userId}
        AND source_id = #{sourceId}
        AND is_delete = 0
        AND to_do_source in ('1','5')
    </delete>

    <delete id="deleteToDoByUserIdAndSourceID2">
        UPDATE
        to_do
        SET is_delete=1,update_time=now()
        WHERE
        to_do_user_id = #{userId}
        AND source_id = #{sourceId}
        AND audit_result='2'
        AND is_delete = 0
        AND to_do_source in ('1','5')
    </delete>

    <delete id="deleteToDoByUserIdAndSourceID0">
        UPDATE
        to_do
        SET is_delete=1,update_time=now()
        WHERE
        to_do_user_id = #{userId}
        AND source_id = #{sourceId}
        AND audit_result='0'
        AND is_delete = 0
        AND to_do_source in ('1','5')
    </delete>

    <delete id="deleteToDoByUserIdAndQuestionnaireId">
        UPDATE
        to_do
        SET is_delete=1,update_time=now()
        WHERE
        questionnaire_id = #{questionnaireId}
        <if test="userId != null">
            AND to_do_user_id = #{userId}
        </if>
        AND is_delete = 0
        AND to_do_source = '2'
    </delete>

    <delete id="deleteToDoByQuestionnaireId">
        UPDATE
        to_do
        SET is_delete=1,update_time=now()
        WHERE questionnaire_id = #{questionnaireId}
        AND is_delete = 0
        AND to_do_source = '2'
    </delete>

    <delete id="deleteToDoByPram">
        UPDATE
            to_do
        SET is_delete=1,update_time=now()
        WHERE questionnaire_id = #{questionnaireId}
          AND is_delete = 0
          AND to_do_source = #{source}
    </delete>

    <select id="getWaitDoneCenterPageList"
            resultType="com.onecity.os.management.zhibiao.model.dto.GetWaitDoneCenterPageListDto">
        SELECT id,
               create_time        AS createTime,
               source_id AS sourceSimpleName,
               audit_result       AS auditResult,
               to_do_source       AS toDoSource,
               title as title,
               questionnaire_jump as questionnaireJump,
               questionnaire_id as questionnaireId,
               step_id as stepId
        FROM to_do
        WHERE is_delete = 0 and to_do_user_id = #{userId}
        ORDER BY create_time DESC
    </select>

    <insert id="addToDo">
        insert into to_do (
        id,
        source_id,
        title,
        to_do_user_id,
        to_do_user_name,
        audit_result,
        create_time,
        update_time,
        is_delete,
        to_do_source,
        questionnaire_id,
        questionnaire_jump,
        step_id
        )
        VALUES
	    (
		#{vo.id},
		#{vo.sourceId},
		#{vo.title},
		#{vo.toDoUserId},
		#{vo.toDoUserName},
		#{vo.auditResult},
		#{vo.createTime},
		#{vo.updateTime},
		0,
		#{vo.toDoSource},
		#{vo.questionnaireId},
		#{vo.questionnaireJump},
		#{vo.stepId}
	    );
    </insert>

</mapper>













