<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.zhibiaowarning.mapper.WarnResultHandleLogMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.management.zhibiaowarning.entity.WarnResultHandleLog">
        <id column="handle_id" jdbcType="VARCHAR" property="handleId"/>
        <result column="warn_result_id" jdbcType="VARCHAR" property="warnResultId"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="creater" jdbcType="VARCHAR" property="creater"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="handle_content" jdbcType="VARCHAR" property="handleContent"/>
        <result column="handle_result" jdbcType="VARCHAR" property="handleResult"/>
        <result column="handle_user_id" jdbcType="BIGINT" property="handleUserId"/>
    </resultMap>
    
    <sql id="Base_Column_List">
        handle_id, warn_result_id, updater, update_time, creater, create_time,
        handle_content, handle_result, handle_user_id,handle_remark
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from warn_result_handle_log
        where handle_id = #{handleId}
    </select>

    <select id="selectByWarnResultId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from warn_result_handle_log
        where warn_result_id = #{warnResultId}
        order by create_time desc
    </select>

    <select id="selectSuperviseByWarnResultId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from warn_result_handle_log
        where warn_result_id = #{warnResultId}
        and handle_result = '督办'
        order by create_time desc
    </select>

    <select id="selectByHandleUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from warn_result_handle_log
        where handle_user_id = #{handleUserId}
        order by create_time desc
    </select>

    <insert id="insert" parameterType="com.onecity.os.management.zhibiaowarning.entity.WarnResultHandleLog">
        insert into warn_result_handle_log (
            handle_id, warn_result_id, updater,
            update_time, creater, create_time,
            handle_content, handle_result, handle_user_id,handle_remark
        )
        values (
            #{handleId}, #{warnResultId}, #{updater},
            #{updateTime}, #{creater}, #{createTime},
            #{handleContent}, #{handleResult}, #{handleUserId},#{handleRemark}
        )
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.onecity.os.management.zhibiaowarning.entity.WarnResultHandleLog">
        update warn_result_handle_log
        set warn_result_id = #{warnResultId},
            updater = #{updater},
            update_time = #{updateTime},
            handle_content = #{handleContent},
            handle_result = #{handleResult},
            handle_user_id = #{handleUserId}
        where handle_id = #{handleId}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from warn_result_handle_log
        where handle_id = #{handleId}
    </delete>

    <delete id="deleteByWarnResultId" parameterType="java.lang.String">
        delete from warn_result_handle_log
        where warn_result_id = #{warnResultId}
    </delete>

    <delete id="deleteByWarnRuleId" parameterType="java.lang.String">
        delete from warn_result_handle_log
        where warn_result_id in (select warn_result_id from warn_result where warn_rule_id = #{warnRuleId})
    </delete>

    <select id="countByWarnResultId" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1)
        from warn_result_handle_log
        where warn_result_id = #{warnResultId}
    </select>
</mapper>