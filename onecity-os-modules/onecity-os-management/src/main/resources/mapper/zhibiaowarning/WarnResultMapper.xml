<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.zhibiaowarning.mapper.WarnResultMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.management.zhibiaowarning.entity.WarnResult">
        <id column="warn_result_id" property="warnResultId" jdbcType="VARCHAR"/>
        <result column="source_id" property="sourceId" jdbcType="VARCHAR"/>
        <result column="warn_rule_id" property="warnRuleId" jdbcType="VARCHAR"/>
        <result column="indicator_id" property="indicatorId" jdbcType="VARCHAR"/>
        <result column="indicator_name" property="indicatorName" jdbcType="VARCHAR"/>
        <result column="indicator_exhibit_type" property="indicatorExhibitType" jdbcType="INTEGER"/>
        <result column="indicator_data" property="indicatorData" jdbcType="VARCHAR"/>
        <result column="result_status" property="resultStatus" jdbcType="INTEGER"/>
        <result column="compare_value" property="compareValue" jdbcType="VARCHAR"/>
        <result column="real_value" property="realValue" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="warn_conut_rule_sub" property="warnConutRuleSub" jdbcType="VARCHAR"/>
        <result column="remind_user_ids" property="remindUserIds" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="WarnResultVoMap" type="com.onecity.os.management.zhibiaowarning.entity.vo.WarnResultVo">
        <id column="warn_result_id" property="warnResultId" jdbcType="VARCHAR"/>
        <result column="rule_name" property="warnRuleName" jdbcType="VARCHAR"/>
        <result column="source_id" property="sourceId" jdbcType="VARCHAR"/>
        <result column="warn_rule_id" property="warnRuleId" jdbcType="VARCHAR"/>
        <result column="indicator_id" property="indicatorId" jdbcType="VARCHAR"/>
        <result column="indicator_name" property="indicatorName" jdbcType="VARCHAR"/>
        <result column="result_status" property="resultStatus" jdbcType="INTEGER"/>
        <result column="warn_conut_rule_sub" property="warnCountRuleSubs" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="warn_level" property="warnLevel" jdbcType="INTEGER"/>
    </resultMap>
    
    <sql id="Base_Column_List">
        warn_result_id, source_id, warn_rule_id, indicator_id, indicator_name, indicator_exhibit_type,
        indicator_data, result_status, compare_value, real_value, create_time, warn_conut_rule_sub,remind_user_ids
    </sql>
    
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from warn_result
        where warn_result_id = #{warnResultId,jdbcType=VARCHAR}
    </select>
    
    <select id="selectByWarnRuleId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from warn_result
        where warn_rule_id = #{warnRuleId,jdbcType=VARCHAR}
        order by create_time desc
    </select>
    
    <select id="selectByIndicatorId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from warn_result
        where indicator_id = #{indicatorId,jdbcType=VARCHAR}
        order by create_time desc
    </select>

    <select id="selectByIndicatorIds" parameterType="java.util.List" resultType="String">
        select
        indicator_id
        from warn_result
        where indicator_id in
        <foreach collection="indicatorIds" item="indicatorId" open="(" separator="," close=")">
            #{indicatorId}
        </foreach>
        and result_status = 2
        order by create_time desc
    </select>

    <select id="checkByWarnRuleId"  resultType="String">
        select
            warn_result_id
        from warn_result
        where warn_rule_id = #{warnRuleId}
        and result_status = 2
        order by create_time desc
    </select>


    <insert id="insert" parameterType="com.onecity.os.management.zhibiaowarning.entity.WarnResult">
        insert into warn_result (
            warn_result_id, source_id, warn_rule_id, 
            indicator_id, indicator_name, indicator_exhibit_type, 
            indicator_data, result_status, compare_value, 
            real_value, create_time, warn_conut_rule_sub,remind_user_ids
        )
        values (
            #{warnResultId,jdbcType=VARCHAR}, #{sourceId,jdbcType=VARCHAR}, #{warnRuleId,jdbcType=VARCHAR},
            #{indicatorId,jdbcType=VARCHAR}, #{indicatorName,jdbcType=VARCHAR}, #{indicatorExhibitType,jdbcType=VARCHAR},
            #{indicatorData,jdbcType=VARCHAR}, #{resultStatus,jdbcType=INTEGER}, #{compareValue,jdbcType=VARCHAR},
            #{realValue,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{warnConutRuleSub,jdbcType=VARCHAR},#{remindUserIds,jdbcType=VARCHAR}
        )
    </insert>
    
    <update id="updateByPrimaryKey" parameterType="com.onecity.os.management.zhibiaowarning.entity.WarnResult">
        update warn_result
        set source_id = #{sourceId,jdbcType=VARCHAR},
            warn_rule_id = #{warnRuleId,jdbcType=VARCHAR},
            indicator_id = #{indicatorId,jdbcType=VARCHAR},
            indicator_name = #{indicatorName,jdbcType=VARCHAR},
            indicator_exhibit_type = #{indicatorExhibitType,jdbcType=VARCHAR},
            indicator_data = #{indicatorData,jdbcType=VARCHAR},
            result_status = #{resultStatus,jdbcType=INTEGER},
            compare_value = #{compareValue,jdbcType=VARCHAR},
            real_value = #{realValue,jdbcType=VARCHAR},
            warn_conut_rule_sub = #{warnConutRuleSub,jdbcType=VARCHAR}
        where warn_result_id = #{warnResultId,jdbcType=VARCHAR}
    </update>
    
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from warn_result
        where warn_result_id = #{warnResultId,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByWarnRuleId" parameterType="java.lang.String">
        delete from warn_result
        where warn_rule_id = #{warnRuleId,jdbcType=VARCHAR}
    </delete>

    <!-- 根据用户ID查询预警结果列表 -->
    <select id="selectWarnResultListByUser" resultMap="WarnResultVoMap">
        SELECT
        wr.warn_result_id,
        wru.rule_name,
        wr.source_id,
        wr.warn_rule_id,
        wr.indicator_id,
        wr.indicator_name,
        wr.result_status,
        wr.warn_conut_rule_sub,
        wr.create_time,
        wru.warn_level
        FROM
        warn_result wr
        INNER JOIN
        warn_rule wru ON wr.warn_rule_id = wru.warn_rule_id
        WHERE
        FIND_IN_SET(#{userId}, wr.remind_user_ids)
        <if test="name != null and name != ''">
            AND (instr(wru.rule_name, #{name}) OR instr(wr.indicator_name, #{name}))
        </if>
        ORDER BY wr.result_status asc ,
        wr.create_time DESC
    </select>

    <resultMap id="WarnResultPoMap" type="com.onecity.os.management.zhibiaowarning.entity.po.WarnResultPo">
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="warn_conut_rule_sub" property="warnCountRuleSubs" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 查询预警历史（当前预警结果之前的记录） -->
    <select id="selectHistoryByWarnRuleId" resultMap="WarnResultPoMap">
        SELECT 
            create_time,
            warn_conut_rule_sub
        FROM 
            warn_result
        WHERE 
            warn_rule_id = #{warnRuleId}
            AND warn_result_id != #{warnResultId}
            AND create_time &lt; (SELECT create_time FROM warn_result WHERE warn_result_id = #{warnResultId})
        ORDER BY 
            create_time DESC
        LIMIT 
            #{limit}
    </select>
    <!--更新预警结果状态-->
    <update id="updateWarnResultStatus" parameterType="com.onecity.os.management.zhibiaowarning.entity.WarnResult">
        update warn_result
        set result_status = #{wr.resultStatus,jdbcType=INTEGER}
        where warn_result_id = #{wr.warnResultId,jdbcType=VARCHAR}
    </update>
    <resultMap id="CountResultMap" type="com.onecity.os.management.zhibiaowarning.entity.vo.CountStatusVo">
        <result column="result_count" property="resultCount" jdbcType="BIGINT"/>
        <result column="result_status" property="resultStatus" jdbcType="INTEGER"/>
    </resultMap>
    <select id="countWarnResultByStatus" resultMap="CountResultMap">
        SELECT COUNT(*) as result_count,result_status
        FROM warn_result
        WHERE 1=1
        <if test="beginTime != null">
            AND create_time &gt;= #{beginTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        group by result_status
    </select>

    <select id="countTotalWarnResult" resultType="long">
        SELECT COUNT(*)
        FROM warn_result
        WHERE 1=1
        <if test="beginTime != null">
            AND create_time &gt;= #{beginTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
    </select>

    <resultMap id="TopSourceResultMap" type="com.onecity.os.management.zhibiaowarning.entity.vo.TopSource">
        <result column="result_count" property="resultCount" jdbcType="INTEGER"/>
        <result column="source_name" property="sourceName" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getTopSourceWarnResults" resultMap="TopSourceResultMap">
        SELECT
            COUNT(wr.source_id) AS result_count,
            sm.source_name AS source_name
        FROM
            warn_result wr
        LEFT JOIN
            source_manage sm ON wr.source_id = sm.source_simple_name
        WHERE 1=1
        <if test="beginTime != null">
            AND wr.create_time &gt;= #{beginTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND wr.create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        GROUP BY
            wr.source_id, sm.source_name
        ORDER BY
            result_count DESC
        LIMIT #{limit}
    </select>

    <!-- 预警结果历史列表结果映射 -->
    <resultMap id="WarnResultPo1Map" type="com.onecity.os.management.zhibiaowarning.entity.po.WarnResultPo1">
        <result column="rule_name" property="ruleName" jdbcType="VARCHAR"/>
        <result column="warn_count_rule_subs" property="warnCountRuleSubs" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="warn_result_id" property="warnResultId" jdbcType="VARCHAR"/>
        <result column="result_status" property="resultStatus" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 根据预警规则ID查询预警历史列表 -->
    <select id="getWarnResultListByWarnRuleId" resultMap="WarnResultPo1Map">
        SELECT
            wr.rule_name,
            wres.warn_conut_rule_sub AS warn_count_rule_subs,
            wres.create_time,
            wres.warn_result_id,
            wres.result_status
        FROM
            warn_result wres
        LEFT JOIN
            warn_rule wr ON wres.warn_rule_id = wr.warn_rule_id
        WHERE
            wres.warn_rule_id = #{warnRuleId}
        ORDER BY
            wres.create_time DESC
    </select>

    <!--根据预警结果ID查询通知用户列表-->
    <select id="getRemindUserList" resultType="com.onecity.os.management.zhibiaowarning.entity.po.ReminUserHandleLog">
        SELECT
            wru.remind_user_ids AS remind_user_ids,
            wru.remind_type AS remind_type,
            wru.remind_role_id AS remind_role_id,
            wrh.handle_result AS handle_result,
            wrh.create_time AS create_time,
            wrh.handle_content AS handle_content,
            wrh.handle_user_id AS handle_user_id
        FROM
            warn_result wr
        LEFT JOIN
            warn_rule wru ON wr.warn_rule_id = wru.warn_rule_id
        LEFT JOIN
            warn_result_handle_log wrh ON wr.warn_result_id = wrh.warn_result_id
        WHERE
            wr.warn_result_id = #{warnResultId} and wrh.handle_result='督办'
    </select>

</mapper>