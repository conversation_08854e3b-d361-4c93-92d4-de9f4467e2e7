<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.zhibiaowarning.mapper.WarnRuleDetailMapper">
    
    <resultMap type="com.onecity.os.management.zhibiaowarning.entity.WarnRuleDetail" id="WarnRuleDetailResult">
        <id property="detailId" column="detail_id"/>
        <result property="warnRuleId" column="warn_rule_id"/>
        <result property="detailName" column="detail_name"/>
        <result property="itemName" column="item_name"/>
        <result property="itemValue" column="item_value"/>
        <result property="compareType" column="compare_type"/>
        <result property="ruleType" column="rule_type"/>
        <result property="compareRule" column="compare_rule"/>
        <result property="compareValue" column="compare_value"/>
        <result property="updater" column="updater"/>
        <result property="updateTime" column="update_time"/>
        <result property="creater" column="creater"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    
    <sql id="selectWarnRuleDetailVo">
        select detail_id, warn_rule_id, detail_name, item_name, item_value, compare_type, rule_type, 
        compare_rule, compare_value, updater, update_time, creater, create_time
        from warn_rule_detail
    </sql>
    
    <!-- 根据预警规则ID查询预警规则详情列表 -->
    <select id="selectByWarnRuleId" parameterType="String" resultMap="WarnRuleDetailResult">
        <include refid="selectWarnRuleDetailVo"/>
        where warn_rule_id = #{warnRuleId} order by detail_name
    </select>
    
    <!-- 根据预警规则ID删除预警规则详情 -->
    <delete id="deleteByWarnRuleId" parameterType="String">
        delete from warn_rule_detail where warn_rule_id = #{warnRuleId}
    </delete>

    <!-- 批量插入预警规则详情 -->
    <insert id="inserBatch" parameterType="java.util.List">
        insert into warn_rule_detail (detail_id, warn_rule_id, detail_name, item_name, item_value, compare_type, rule_type, compare_rule, compare_value, updater, update_time, creater, create_time)
        values
        <foreach collection="warnRuleDetails" item="item" index="index" separator=",">
            (#{item.detailId}, #{item.warnRuleId}, #{item.detailName}, #{item.itemName}, #{item.itemValue}, #{item.compareType}, #{item.ruleType}, #{item.compareRule}, #{item.compareValue}, #{item.updater}, #{item.updateTime}, #{item.creater}, #{item.createTime})
        </foreach>
    </insert>

    <!-- 批量更新预警规则详情 -->
    <update id="updateBatch" parameterType="java.util.List">
        update warn_rule_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="detail_name = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.detailName != null">
                        when detail_id = #{item.detailId} then #{item.detailName}
                    </if>
                </foreach>
            </trim>
            <trim prefix="item_name = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.itemName!= null">
                        when detail_id = #{item.detailId} then #{item.itemName}
                    </if>
                </foreach>
            </trim>
            <trim prefix="item_value = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.itemValue!= null">
                        when detail_id = #{item.detailId} then #{item.itemValue}
                    </if>
                </foreach>
            </trim>
            <trim prefix="compare_type = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.compareType!= null">
                        when detail_id = #{item.detailId} then #{item.compareType}
                    </if>
                </foreach>
            </trim>
            <trim prefix="rule_type = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.ruleType!= null">
                        when detail_id = #{item.detailId} then #{item.ruleType}
                    </if>
                </foreach>
            </trim>
            <trim prefix="compare_rule = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.compareRule!= null">
                        when detail_id = #{item.detailId} then #{item.compareRule}
                    </if>
                </foreach>
            </trim>
            <trim prefix="compare_value = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.compareValue!= null">
                        when detail_id = #{item.detailId} then #{item.compareValue}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.updater!= null">
                        when detail_id = #{item.detailId} then #{item.updater}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.updateTime!= null">
                        when detail_id = #{item.detailId} then #{item.updateTime}
                    </if>
                </foreach>
            </trim>
        </trim>
        where detail_id in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item.detailId}
        </foreach>
    </update>
</mapper>