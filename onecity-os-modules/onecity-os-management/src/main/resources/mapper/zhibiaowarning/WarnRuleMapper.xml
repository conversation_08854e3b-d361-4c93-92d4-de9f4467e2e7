<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.zhibiaowarning.mapper.WarnRuleMapper">
    <resultMap id="BaseResultMap" type="com.onecity.os.management.zhibiaowarning.entity.WarnRule">
        <id column="warn_rule_id" jdbcType="VARCHAR" property="warnRuleId"/>
        <result column="source_id" jdbcType="VARCHAR" property="sourceId"/>
        <result column="indicator_id" jdbcType="VARCHAR" property="indicatorId"/>
        <result column="data_update_mode" jdbcType="INTEGER" property="dataUpdateMode"/>
        <result column="indicator_name" jdbcType="VARCHAR" property="indicatorName"/>
        <result column="update_cycle" jdbcType="VARCHAR" property="updateCycle"/>
        <result column="last_update_date" jdbcType="VARCHAR" property="lastUpdateDate"/>
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName"/>
        <result column="warn_level" jdbcType="INTEGER" property="warnLevel"/>
        <result column="warn_status" jdbcType="INTEGER" property="warnStatus"/>
        <result column="remind_user_ids" jdbcType="VARCHAR" property="remindUserIds"/>
        <result column="remind_role_id" jdbcType="VARCHAR" property="remindRoleId"/>
        <result column="remind_type" jdbcType="INTEGER" property="remindType"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="DATE" property="updateTime"/>
        <result column="creater" jdbcType="VARCHAR" property="creater"/>
        <result column="create_time" jdbcType="DATE" property="createTime"/>
        <result column="warn_interval" jdbcType="VARCHAR" property="warnInterval"/>
        <result column="warn_count_rule" jdbcType="VARCHAR" property="warnCountRule"/>
        <result column="udf_rule" jdbcType="VARCHAR" property="udfRule"/>
    </resultMap>

    <resultMap id="IndicatorWarnMap" type="com.onecity.os.management.zhibiaowarning.entity.vo.IndicatorWarn">
        <result column="indicator_id" property="indicatorId"/>
        <result column="indicator_name" property="indicatorName"/>
        <result column="data_update_mode" property="updateMode"/>
        <result column="update_cycle" property="updateCycle"/>
        <result column="start_warn_rule_num" property="startWarnRuleNum"/>
    </resultMap>

    <resultMap id="WarnInfoPoMap" type="com.onecity.os.management.zhibiaowarning.entity.po.WarnInfoPo">
        <result column="warn_rule_id" property="warnRuleId"/>
        <result column="rule_name" property="warnRuleName"/>
        <result column="warn_level" property="warnLevel"/>
        <result column="warn_status" property="warnStatus"/>
        <result column="remind_role_id" property="remindRoleId"/>
        <result column="remind_type" property="remindType"/>
        <result column="remind_user_ids" property="remindUserIds"/>
        <result column="indicator_id" property="indicatorId"/>
        <result column="updater" property="updater"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        warn_rule_id, source_id, indicator_id, data_update_mode, indicator_name, update_cycle,
        last_update_date, rule_name, warn_level, warn_status, remind_user_ids, remind_role_id,
        remind_type, updater, update_time, creater, create_time, warn_interval, warn_count_rule, udf_rule
    </sql>

    <!-- 根据指标id获取其下面的子指标信息以及预警规则信息 -->
    <select id="getWarnAndInfoByParentId" resultMap="IndicatorWarnMap">
        SELECT
            gi.id AS indicator_id,
            gi.indicator_name,
            gi.data_update_mode,
            gi.update_cycle,
            (SELECT COUNT(1) FROM warn_rule wr WHERE wr.indicator_id = gi.id AND wr.warn_status = 1) AS start_warn_rule_num
        FROM
            general_indicator gi
        WHERE
            gi.parent_id = #{indicatorId}
            AND gi.is_delete = 0
            AND gi.is_show = 1
        ORDER BY
            gi.sequence ASC
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from warn_rule
        where warn_rule_id = #{warnRuleId}
    </select>

    <select id="selectWarnRuleList" parameterType="com.onecity.os.management.zhibiaowarning.entity.WarnRule" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from warn_rule
        <where>
            <if test="sourceId != null and sourceId != ''">
                AND source_id = #{sourceId}
            </if>
            <if test="indicatorId != null and indicatorId != ''">
                AND indicator_id = #{indicatorId}
            </if>
            <if test="dataUpdateMode != null">
                AND data_update_mode = #{dataUpdateMode}
            </if>
            <if test="ruleName != null and ruleName != ''">
                AND rule_name like concat('%', #{ruleName}, '%')
            </if>
            <if test="warnLevel != null">
                AND warn_level = #{warnLevel}
            </if>
            <if test="warnStatus != null">
                AND warn_status = #{warnStatus}
            </if>
            <if test="remindType != null">
                AND remind_type = #{remindType}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectByRuleName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from warn_rule
        where rule_name = #{ruleName}
    </select>

    <select id="selectByRuleNameAndId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from warn_rule
        where rule_name = #{ruleName} and warn_rule_id != #{warnRuleId}
    </select>

    <select id="selectByIndicatorId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from warn_rule
        where indicator_id = #{indicatorId}
        order by create_time desc
    </select>

    <insert id="insertWarnRule" parameterType="com.onecity.os.management.zhibiaowarning.entity.WarnRule">
        insert into warn_rule (
            warn_rule_id, source_id, indicator_id,
            data_update_mode, indicator_name, update_cycle,
            last_update_date, rule_name, warn_level,
            warn_status, remind_user_ids, remind_role_id,
            remind_type, updater, update_time,
            creater, create_time, warn_interval,
            warn_count_rule, udf_rule
        )
        values (
            #{warnRule.warnRuleId}, #{warnRule.sourceId}, #{warnRule.indicatorId},
            #{warnRule.dataUpdateMode}, #{warnRule.indicatorName}, #{warnRule.updateCycle},
            #{warnRule.lastUpdateDate}, #{warnRule.ruleName}, #{warnRule.warnLevel},
            #{warnRule.warnStatus}, #{warnRule.remindUserIds}, #{warnRule.remindRoleId},
            #{warnRule.remindType}, #{warnRule.updater}, #{warnRule.updateTime},
            #{warnRule.creater}, #{warnRule.createTime}, #{warnRule.warnInterval},
            #{warnRule.warnCountRule}, #{warnRule.udfRule}
        )
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.onecity.os.management.zhibiaowarning.entity.WarnRule">
        update warn_rule
        set source_id = #{warnRule.sourceId},
            indicator_id = #{warnRule.indicatorId},
            data_update_mode = #{warnRule.dataUpdateMode},
            indicator_name = #{warnRule.indicatorName},
            update_cycle = #{warnRule.updateCycle},
            last_update_date = #{warnRule.lastUpdateDate},
            rule_name = #{warnRule.ruleName},
            warn_level = #{warnRule.warnLevel},
            warn_status = #{warnRule.warnStatus},
            remind_user_ids = #{warnRule.remindUserIds},
            remind_role_id = #{warnRule.remindRoleId},
            remind_type = #{warnRule.remindType},
            updater = #{warnRule.updater},
            update_time = #{warnRule.updateTime},
            warn_interval = #{warnRule.warnInterval},
            warn_count_rule = #{warnRule.warnCountRule},
            udf_rule = #{warnRule.udfRule}
        where warn_rule_id = #{warnRule.warnRuleId}
    </update>


<update id="updateByPrimaryKeySelective" parameterType="com.onecity.os.management.zhibiaowarning.entity.WarnRule">
    update warn_rule
    <set>
        <if test="sourceId != null">
            source_id = #{sourceId},
        </if>
        <if test="indicatorId != null">
            indicator_id = #{indicatorId},
        </if>
        <if test="dataUpdateMode != null">
            data_update_mode = #{dataUpdateMode},
        </if>
        <if test="indicatorName != null">
            indicator_name = #{indicatorName},
        </if>
        <if test="updateCycle != null">
            update_cycle = #{updateCycle},
        </if>
        <if test="lastUpdateDate != null">
            last_update_date = #{lastUpdateDate},
        </if>
        <if test="ruleName != null">
            rule_name = #{ruleName},
        </if>
        <if test="warnLevel != null">
            warn_level = #{warnLevel},
        </if>
        <if test="warnStatus != null">
            warn_status = #{warnStatus},
        </if>
        <if test="remindUserIds != null">
            remind_user_ids = #{remindUserIds},
        </if>
        <if test="remindRoleId != null">
            remind_role_id = #{remindRoleId},
        </if>
        <if test="remindType != null">
            remind_type = #{remindType},
        </if>
        <if test="updater != null">
            updater = #{updater},
        </if>
        <if test="updateTime != null">
            update_time = #{updateTime},
        </if>
        <if test="warnInterval != null">
            warn_interval = #{warnInterval},
        </if>
        <if test="warnCountRule != null">
            warn_count_rule = #{warnCountRule},
        </if>
        <if test="udfRule != null">
            udf_rule = #{udfRule},
        </if>
    </set>
    where warn_rule_id = #{warnRuleId}
</update>



    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from warn_rule
        where warn_rule_id = #{warnRuleId}
    </delete>

    <delete id="deleteWarnRuleByIds" parameterType="String">
        delete from warn_rule where warn_rule_id in
        <foreach collection="array" item="warnRuleId" open="(" separator="," close=")">
            #{warnRuleId}
        </foreach>
    </delete>

    <update id="updateStatus">
        update warn_rule
        set warn_status = #{warnStatus},
            update_time = now()
        where warn_rule_id = #{warnRuleId}
    </update>

    <!-- 根据指标id获取其配置的预警规则列表 -->
    <select id="getWarnInfoListByIndicatorId" resultMap="WarnInfoPoMap">
        SELECT
            warn_rule_id,
            rule_name,
            warn_level,
            warn_status,
            remind_type,
            remind_user_ids,
            remind_role_id,
            updater,
            update_time
        FROM
            warn_rule
        WHERE
            indicator_id = #{indicatorId}
        ORDER BY
            create_time DESC
    </select>

    <select id="getWarnInfoListByWarnRuleIds" resultMap="WarnInfoPoMap">
        SELECT
            warn_rule_id,
            rule_name,
            warn_level,
            warn_status,
            remind_type,
            remind_user_ids,
            remind_role_id,
            indicator_id,
            updater,
            update_time
        FROM
            warn_rule
        WHERE
            warn_rule_id in
        <foreach collection="warnRuleIds" item="warnRuleId" open="(" separator="," close=")">
            #{warnRuleId}
        </foreach>
        ORDER BY
            create_time DESC
    </select>

    <!-- 根据预警规则ID、版块编码和指标ID查询预警规则 -->
    <select id="selectWarnRuleByIdAndSourceId" resultType="com.onecity.os.management.zhibiaowarning.entity.WarnRule">
        select
            <include refid="Base_Column_List"/>
        from warn_rule
        where warn_rule_id = #{warnRuleId} and source_id = #{sourceId} and indicator_id = #{indicatorId}
    </select>

    <!-- 预警规则及结果列表结果映射 -->
    <resultMap id="WarnRuleAndResultMap" type="com.onecity.os.management.zhibiaowarning.entity.vo.WarnRuleAndResult">
        <result column="source_id" property="sourceId" jdbcType="VARCHAR"/>
        <result column="source_name" property="sourceName" jdbcType="VARCHAR"/>
        <result column="warn_rule_id" property="warnRuleId" jdbcType="VARCHAR"/>
        <result column="indicator_id" property="indicatorId" jdbcType="VARCHAR"/>
        <result column="indicator_name" property="indicatorName" jdbcType="VARCHAR"/>
        <result column="rule_name" property="ruleName" jdbcType="VARCHAR"/>
        <result column="warn_level" property="warnLevel" jdbcType="INTEGER"/>
        <result column="warn_status" property="warnStatus" jdbcType="INTEGER"/>
        <result column="warn_count_rule_subs" property="warnCountRuleSubs" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="warn_result_id" property="warnResultId" jdbcType="VARCHAR"/>
        <result column="result_status" property="resultStatus" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 获取全部的预警规则及相应的预警结果列表 -->
    <select id="getWarnRuleAndResultList" resultMap="WarnRuleAndResultMap" parameterType="com.onecity.os.management.zhibiaowarning.entity.dto.WarnRuleAndResultQueryDto">
        SELECT
            wr.source_id,
            sm.source_name,
            wr.warn_rule_id,
            wr.indicator_id,
            wr.indicator_name,
            wr.rule_name,
            wr.warn_level,
            wr.warn_status,
            wres.warn_conut_rule_sub AS warn_count_rule_subs,
            wres.create_time,
            wres.warn_result_id,
            wres.result_status
        FROM
        warn_result wres
        LEFT JOIN
        warn_rule wr ON wres.warn_rule_id = wr.warn_rule_id
        LEFT JOIN
        source_manage sm ON wr.source_id = sm.source_simple_name
        WHERE 1=1 and wr.warn_rule_id is not null
        <if test="sourceName != null and sourceName != ''">
            AND INSTR(sm.source_name , #{sourceName})
        </if>
        <if test="indicatorName != null and indicatorName != ''">
            AND INSTR(wr.indicator_name, #{indicatorName})
        </if>
        <if test="warnRuleName != null and warnRuleName != ''">
            AND INSTR(wr.rule_name, #{warnRuleName})
        </if>
        <if test="resultBeginTime != null">
            AND wres.create_time &gt;= #{resultBeginTime}
        </if>
        <if test="resultEndTime != null">
            AND wres.create_time &lt;= #{resultEndTime}
        </if>
        ORDER BY
            wres.create_time DESC,
            wr.warn_level DESC
    </select>

    <!--根据预警结果id获取预警规则-->
    <select id="getWarnRuleByResultId" resultType="com.onecity.os.management.zhibiaowarning.entity.WarnRule" parameterType="java.lang.String">
        SELECT
            wr.warn_rule_id as warn_rule_id,
            wr.source_id as source_id,
            wr.indicator_id as indicator_id,
            wr.data_update_mode as data_update_mode,
            wr.indicator_name as indicator_name,
            wr.update_cycle as update_cycle,
            wr.last_update_date as last_update_date,
            wr.rule_name as rule_name,
            wr.warn_level as warn_level,
            wr.warn_status as warn_status,
            wr.remind_user_ids as remind_user_ids,
            wr.remind_role_id as remind_role_id,
            wr.remind_type as remind_type,
            wr.updater as updater,
            wr.update_time as update_time,
            wr.creater as creater,
            wr.create_time as create_time,
            wr.warn_interval as warn_interval,
            wr.warn_count_rule as warn_count_rule,
            wr.udf_rule as udf_rule
        FROM
            warn_rule wr
        LEFT JOIN
            warn_result wres ON wr.warn_rule_id = wres.warn_rule_id
        where wres.warn_result_id = #{warnResultId}
    </select>


    <select id="getWarnRuleIdsByWarnInterval" resultType="java.lang.String">
        select warn_rule_id
        from warn_rule
        where warn_interval = #{warnInterval} and warn_status = 1 and data_update_mode = 2
    </select>
</mapper>