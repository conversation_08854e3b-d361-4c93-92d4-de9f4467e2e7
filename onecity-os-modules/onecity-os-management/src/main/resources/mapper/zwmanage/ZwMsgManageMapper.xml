<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.management.zwmanage.mapper.ZwMsgManageMapper">

    <select id="selectPageByNameAndType1" resultType="com.onecity.os.management.zwmanage.entity.ZwPcMsgManage">
        SELECT zmm.id as id,zmm.name as name,zmm.journal_num as journalNum,sdi.item_value as typeValue,
        case when sdi.item_text is null then zmm.type else sdi.item_text end as type,
        zmm.label as label,zmm.file_name as fileName,zmm.pic_path as picPath,zmm.pic_is_big as picIsBig,
        zmm.content_formate as contentFormate,zmm.content as content,zmm.is_delete as isDelete,zmm.creater as creater,
        zmm.create_time as createTime,zmm.updater as updater,zmm.update_time as updateTime
        from zw_msg_manage zmm
        left join sys_dict sd on sd.dict_code='zwxxlx'
        left join sys_dict_item sdi on sd.id = sdi.dict_id and sdi.item_value = zmm.type
        WHERE zmm.is_delete = 0
        <if test="name!=null and name!=''">
            and INSTR(zmm.name,#{name})
        </if>
        <if test="type!=null and type!='' or type==0">
            and zmm.type = #{type}
        </if>
        order by zmm.create_time desc
    </select>

    <select id="selectPageByNameAndType" resultType="com.onecity.os.management.zwmanage.model.dto.GetZwMsgManageListDto">
        SELECT zmm.id as id,zmm.name as name,zmm.journal_num as journalNum,zmm.type as typeValue,
        zmm.label as label,zmm.file_name as fileName,zmm.pic_path as picPath,zmm.pic_is_big as picIsBig,
        zmm.content_formate as contentFormate,zmm.content as content,zmm.is_delete as isDelete,zmm.creater as creater,
        zmm.create_time as createTime,zmm.updater as updater,zmm.update_time as updateTime
        from zw_msg_manage zmm
        WHERE zmm.is_delete = 0
        <if test="name!=null and name!=''">
            and INSTR(zmm.name,#{name})
        </if>
        <if test="type!=null and type!='' or type==0">
            and zmm.type = #{type}
        </if>
        order by zmm.create_time desc
    </select>

    <select id="getDepartNamesByDeparts" resultType="String">
        SELECT
        GROUP_CONCAT(`name`)
        FROM
        dingmail_department
        WHERE
        departmentid IN
        <foreach collection="departs" item="depart" open="(" separator="," close=")">
            #{depart}
        </foreach>
    </select>

</mapper>















