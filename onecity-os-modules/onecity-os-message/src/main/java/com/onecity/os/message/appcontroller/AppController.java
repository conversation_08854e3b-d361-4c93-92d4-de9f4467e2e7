package com.onecity.os.message.appcontroller;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.message.service.ManageService;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
//import org.bson.types.ObjectId;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.HashMap;

@Slf4j
@RestController
@RequestMapping("/app")
@Api(tags = "App端消息提醒中心接口")
public class AppController {

    @Resource
    private ManageService manageService;


    /**
     * 获取提醒中心列表
     * @param page
     * @param size
     * @return
     */
    @ApiOperation(value = "首页-获取提醒中心列表")
    @GetMapping("/remind/getRemindInfoPageListApp")
    public BaseResult<JSONObject> getRemindInfoPageListApp(@RequestParam(name = "userId") String userId,
                                                           @RequestParam(name = "appMsgType") String appMsgType,
                                                        @RequestParam(name="flag") String flag,
                                                        @RequestParam(name = "page", defaultValue = "1") Integer page,
                                                        @RequestParam(name = "size", defaultValue = "10") Integer size) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(org.springframework.util.ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        //当flag为1时返回结果去除已读的数据
        return manageService.getRemindInfoPageListApp(loginUser.getUserid().toString(),appMsgType, page, size,flag);
    }
    /**
     * App端获取未读消息数量
     *
     * @return
     */
    @ApiOperation(value = "首页-获取未读消息数量")
    @GetMapping("/remind/getUnReadNumApp")
    public BaseResult<Long> getUnReadNumApp(@RequestParam(name = "userId") String userId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(org.springframework.util.ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        return manageService.getUnReadNumApp(loginUser.getUserid().toString());
    }


    /**
     * 根据提醒中心消息id,将消息设置为已读状态
     *
     * @param vo
     * @return
     */
    @ApiOperation(value = "首页-根据提醒中心消息id,将消息设置为已读状态并返回对应类型的未读数量")
    @PostMapping("/remind/updateRemindIsReadByIdAndUnReadNum")
    public BaseResult updateRemindIsReadByIdAndUnReadNum(@RequestBody HashMap<String,String> vo) throws Exception {
        if (ObjectUtils.isEmpty(vo.get("id"))) {
            return BaseResult.fail("id必传");
        }
        if(ObjectUtils.isEmpty(vo.get("username"))){
            return BaseResult.fail("username必传");
        }
        if(ObjectUtils.isEmpty(vo.get("appMsgType"))){
            return BaseResult.fail("appMsgType必传");
        }
//        if(ObjectUtils.isEmpty(vo.get("userId"))){
//            return BaseResult.fail("userId必传");
//        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(org.springframework.util.ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        manageService.updateRemindIsReadById(vo.get("id"),vo.get("username"));
        return manageService.getUnReadNumAppByType(loginUser.getUserid().toString(),vo.get("appMsgType"));
    }
}
