//package com.onecity.os.message.controller;
//
//
//import com.onecity.os.message.entity.RemindInfo;
//import com.onecity.os.message.service.ManageFeignService;
//import com.ruoyi.common.core.domain.BaseResult;
//import io.swagger.annotations.ApiOperation;
//import org.bson.types.ObjectId;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import java.util.List;
//
//@RestController
//public class ManageFeignController {
//
//    @Resource
//    ManageFeignService manageFeignService;
//
//    /**
//     * 往消息中心添加消息
//     */
//    @ApiOperation(value = "往消息中心中添加消息")
//    @PostMapping("/addMessage")
//    public BaseResult<String> addMessage(@RequestBody List<RemindInfo> remindInfoList){
//        return manageFeignService.addMessage(remindInfoList);
//    }
//
//    /**
//     * 删除消息
//     */
//    @ApiOperation(value = "根据消息id删除消息")
//    @PostMapping("/deleteMessage")
//    public BaseResult<String> deleteMessage(@RequestParam(name = "id") String id){
//        return manageFeignService.delete(id);
//    }
//
//}
