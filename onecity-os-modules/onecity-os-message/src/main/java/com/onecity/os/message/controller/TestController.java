package com.onecity.os.message.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Author: yuk
 * @menu
 * @Description:
 * @Date: 2022/5/20 9:30
 * @Version: 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/test")
@Api(tags = "")
public class TestController {
    @GetMapping("/test")
    @ApiOperation("测试接口")
    public String test() {
        return "success";
    }
}