package com.onecity.os.message.entity;


import lombok.Data;
//import org.bson.types.ObjectId;
//import org.springframework.data.mongodb.core.mapping.Document;
//import org.springframework.data.mongodb.core.mapping.Column;

import java.util.Date;
import javax.persistence.*;

@Data
@Table(name = "remind_info")
public class RemindInfo {
    /**
     * 主键id
     */
    @Id
    private String id;

    /**
     * 提醒标题
     */
    @Column(name = "remind_title")
    private String remindTitle;

    /**
     * 厅局id(source_manage的主键id)
     */
    @Column(name = "source_id")
    private Long sourceId;

    /**
     * 是否已读 0-否;1-是
     */
    @Column(name = "is_read")
    private Integer isRead;

    /**
     * 已经提醒的次数:0-日提醒;1-首次提醒;2-逾期黄色提醒;3-逾期红色提醒;4-统计分析提醒
     */
    @Column(name = "reminded_count")
    private Integer remindedCount;

    /**
     * 提醒类型:0-非日提醒;1-日提醒
     */
    @Column(name = "reminded_type")
    private Integer remindedType;

    /**
     * 是否删除 0-否;1-是
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建人
     */
    @Column(name = "creater")
    private String creater;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private String createTime;

    /**
     * 修改人
     */
    @Column(name = "updater")
    private String updater;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private String updateTime;

    /**
     * 提醒内容
     */
    @Column(name = "remind_content")
    private String remindContent;

    /**
     * HTML提醒内容
     */
    @Column(name = "html_content")
    private String htmlContent;

    /**
     * 管理平台提醒人ids,多个用英文逗号隔开
     */
    @Column(name = "pc_user_id")
    private String pcUserId;

    /**
     * 移动端提醒人,多个用英文逗号隔开
     */
    @Column(name = "app_user_id")
    private String appUserId;

    /**
     * 消息来源类型
     */
    @Column(name = "source_type")
    private String sourceType;

    /**
     * 业务id
     */
    @Column(name = "service_id")
    private String serviceId;

    /**
     * 业务来源
     */
    @Column(name = "service_type")
    private String serviceType;

    /**
     * 消息级别1-红色，2-黄色，3-绿色
     */
    @Column(name = "level")
    private String level;

    /**
     * APP连接
     */
    @Column(name = "message_url")
    private String messageUrl;

    /**
     * APP消息类型 1-通知； 2-待办
     */
    @Column(name = "app_msg_type")
    private String appMsgType;

    /**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public String getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取提醒标题
     *
     * @return remind_title - 提醒标题
     */
    public String getRemindTitle() {
        return remindTitle;
    }

    /**
     * 设置提醒标题
     *
     * @param remindTitle 提醒标题
     */
    public void setRemindTitle(String remindTitle) {
        this.remindTitle = remindTitle;
    }

    /**
     * 获取是否已读 0-否;1-是
     *
     * @return is_read - 是否已读 0-否;1-是
     */
    public Integer getIsRead() {
        return isRead;
    }

    /**
     * 设置是否已读 0-否;1-是
     *
     * @param isRead 是否已读 0-否;1-是
     */
    public void setIsRead(Integer isRead) {
        this.isRead = isRead;
    }

    /**
     * 获取已经提醒的次数
     *
     * @return reminded_count - 已经提醒的次数:0-日提醒;1-首次提醒;2-逾期黄色提醒;3-逾期红色提醒;4-统计分析提醒
     */
    public Integer getRemindedCount() {
        return remindedCount;
    }

    /**
     * 设置已经提醒的次数
     *
     * @param remindedCount 已经提醒的次数:0-日提醒;1-首次提醒;2-逾期黄色提醒;3-逾期红色提醒;4-统计分析提醒
     */
    public void setRemindedCount(Integer remindedCount) {
        this.remindedCount = remindedCount;
    }

    /**
     * 获取是否删除 0-否;1-是
     *
     * @return is_delete - 是否删除 0-否;1-是
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    /**
     * 设置是否删除 0-否;1-是
     *
     * @param isDelete 是否删除 0-否;1-是
     */
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * 获取创建人
     *
     * @return creater - 创建人
     */
    public String getCreater() {
        return creater;
    }

    /**
     * 设置创建人
     *
     * @param creater 创建人
     */
    public void setCreater(String creater) {
        this.creater = creater;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public String getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改人
     *
     * @return updater - 修改人
     */
    public String getUpdater() {
        return updater;
    }

    /**
     * 设置修改人
     *
     * @param updater 修改人
     */
    public void setUpdater(String updater) {
        this.updater = updater;
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public String getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取提醒内容
     *
     * @return remind_content - 提醒内容
     */
    public String getRemindContent() {
        return remindContent;
    }

    /**
     * 设置提醒内容
     *
     * @param remindContent 提醒内容
     */
    public void setRemindContent(String remindContent) {
        this.remindContent = remindContent;
    }

    /**
     * 获取管理平台提醒人id
     *
     * @return pc_user_id - 管理平台提醒人id
     */
    public String getPcUserId() {
        return pcUserId;
    }

    /**
     * 设置管理平台提醒人id
     *
     * @param pcUserId 管理平台提醒人id
     */
    public void setPcUserId(String pcUserId) {
        this.pcUserId = pcUserId;
    }

    /**
     * 获取移动端提醒人,多个用英文逗号隔开
     *
     * @return app_user_id - 移动端提醒人
     */
    public String getAppUserId() {
        return appUserId;
    }

    /**
     * 设置移动端提醒人,多个用英文逗号隔开
     *
     * @param appUserId 移动端提醒人,多个用英文逗号隔开
     */
    public void setAppUserId(String appUserId) {
        this.appUserId = appUserId;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public Integer getRemindedType() {
        return remindedType;
    }

    public void setRemindedType(Integer remindedType) {
        this.remindedType = remindedType;
    }

    public String getMessageUrl() {
        return messageUrl;
    }

    public void setMessageUrl(String messageUrl) {
        this.messageUrl = messageUrl;
    }

    public String getAppMsgType() {
        return appMsgType;
    }

    public void setAppMsgType(String appMsgType) {
        this.appMsgType = appMsgType;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }
}
