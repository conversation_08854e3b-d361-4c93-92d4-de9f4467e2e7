package com.onecity.os.message.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * message对象 remind_message
 * 
 * <AUTHOR>
 * @date 2025-04-28
 */
@Data
@Table(name = "remind_message")
public class RemindMessage
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Id
    @Column(name = "id")
    private String id;

    /** 厅局id(source_manage的主键id) */
    @Column(name = "source_id")
    private Long sourceId;

    /** 提醒标题 */
    @Column(name = "remind_title")
    private String remindTitle;

    /** 是否已读 0-否;1-是 */
    @Column(name = "is_read")
    private Integer isRead;

    /** 提醒类型:0-非日提醒;1-日提醒 */
    @Column(name = "reminded_type")
    private Integer remindedType;

    /** 已经提醒的次数:0-日提醒;1-首次提醒;2-逾期黄色提醒;3-逾期红色提醒;4-统计分析提醒 */
    @Column(name = "reminded_count")
    private Integer remindedCount;

    /** 是否删除 0-否;1-是 */
    @Column(name = "is_delete")
    private Integer isDelete;

    /** 提醒内容 */
    @Column(name = "remind_content")
    private String remindContent;

    /** HTML提醒内容 */
    @Column(name = "html_content")
    private String htmlContent;

    /** 管理平台提醒人id */
    @Column(name = "pc_user_id")
    private String pcUserId;

    /** 移动端提醒人 */
    @Column(name = "app_user_id")
    private String appUserId;

    /** 创建人 */
    @Column(name = "creater")
    private String creater;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private String createTime;

    /** 修改人 */
    @Column(name = "updater")
    private String updater;
    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private String updateTime;

    /** 消息来源类型 */
    @Column(name = "source_type")
    private String sourceType;

    /** 业务id */
    @Column(name = "service_id")
    private String serviceId;

    /** 业务来源 */
    @Column(name = "service_type")
    private String serviceType;

    /** 消息级别1-红色，2-黄色，3-绿色 */
    @Column(name = "level")
    private String level;

    /** APP连接 */
    @Column(name = "message_url")
    private String messageUrl;

    /** APP消息类型 1-通知； 2-待办 */
    @Column(name = "app_msg_type")
    private String appMsgType;
}
