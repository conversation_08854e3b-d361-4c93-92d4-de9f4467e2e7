//package com.onecity.os.message.service;
//
//import com.onecity.os.message.entity.RemindInfo;
//import com.ruoyi.common.core.domain.BaseResult;
//import org.apache.commons.math3.stat.descriptive.summary.Product;
//import org.bson.types.ObjectId;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
////指定需要调用的微服务名称
//@FeignClient(name="onecity-os-message")
//public interface ManageFeignService {
//    //调用的请求路径
//    @RequestMapping(value = "/addMessage",method = RequestMethod.POST)
//    public BaseResult addMessage(@RequestBody List<RemindInfo> remindInfoList);
//
//    //调用的请求路径
//    @RequestMapping(value = "/deleteMessage",method = RequestMethod.POST)
//    public BaseResult delete(@RequestBody String id);
//}
//
