package com.ruoyi.mq;

import com.ruoyi.common.swagger.annotation.EnableCustomSwagger2;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;


/**
 * <AUTHOR>
 * @Date 2022-07-18 10:31
 */
@EnableCustomSwagger2
@SpringBootApplication
@MapperScan(basePackages = "com.ruoyi.mq.mapper")
@EnableRabbit
@EnableDiscoveryClient
@EnableFeignClients
public class MqApplication {
    public static void main(String[] args) {
        SpringApplication.run(MqApplication.class, args);
    }
}
