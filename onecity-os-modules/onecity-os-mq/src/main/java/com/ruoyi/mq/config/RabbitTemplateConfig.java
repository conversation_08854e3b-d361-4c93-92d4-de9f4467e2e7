package com.ruoyi.mq.config;

import com.alibaba.fastjson.JSON;
import com.ruoyi.mq.model.po.MqMessage;
import com.ruoyi.mq.service.MqMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022-07-18 12:45
 */
@Slf4j
@Component
public class RabbitTemplateConfig implements RabbitTemplate.ConfirmCallback, RabbitTemplate.ReturnCallback{
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private MqMessageService mqMessageService;

    @PostConstruct
    private void init() {
        //true:交换机无法将消息进行路由时，会将该消息返回给生产者；false:如果发现消息无法进行路由，则直接丢弃
        rabbitTemplate.setMandatory(true);
        rabbitTemplate.setConfirmCallback(this);
        rabbitTemplate.setReturnCallback(this);
    }

    @Override
    public void confirm(CorrelationData correlationData, boolean ack, String cause) {
        String id = correlationData != null ? correlationData.getId() : "";
        if (ack) {
            log.info("交换机接收到id为:{}的消息确认成功!", id);
//            this.saveOrUpdateMessage(correlationData,1);
        } else {
            log.info("id为:{}的消息未成功投递到交换机,原因是:{}", id, cause);
            this.saveOrUpdateMessage(correlationData,2);
        }
    }

    @Override
    public void returnedMessage(Message message, int replyCode, String replyText, String exchange, String routingKey) {
        log.error("消息:{}被交换机:{}退回,退回码是:{},退回原因是:{},路由是:{}", message.getBody(), exchange, replyCode, replyText, routingKey);
        MessageProperties messageProperties = message.getMessageProperties();
        String messageId = messageProperties.getMessageId();
        MqMessage mqMessage = new MqMessage();
        mqMessage.setId(Long.valueOf(messageId));
        mqMessage.setToExchange(exchange);
        mqMessage.setRoutingKey(routingKey);
        mqMessage.setContent(JSON.toJSONString(message.getBody()));
        mqMessage.setClassType(messageProperties.getType());
        mqMessage.setUpdateTime(new Date());
        mqMessageService.updateById(mqMessage);
    }

    private void saveOrUpdateMessage(CorrelationData correlationData, Integer messageType){
        MqMessage mqMessage = new MqMessage();
        mqMessage.setId(Long.valueOf(correlationData.getId()));
        mqMessage.setMessageType(messageType);
        Message returnedMessage = correlationData.getReturnedMessage();
        if(returnedMessage!=null){
            mqMessage.setContent(JSON.toJSONString(returnedMessage.getBody()));
            MessageProperties messageProperties = returnedMessage.getMessageProperties();
            mqMessage.setToExchange(messageProperties.getReceivedExchange());
            mqMessage.setRoutingKey(messageProperties.getReceivedRoutingKey());
            mqMessage.setClassType(messageProperties.getType());
        }
        Date date = new Date();
        mqMessage.setCreateTime(date);
        mqMessage.setUpdateTime(date);
        mqMessageService.saveOrUpdate(mqMessage);
    }
}
