package com.ruoyi.mq.exception;

import com.onecity.os.common.core.enums.ResultInfoEnum;
import lombok.Data;

/**
 * 业务异常
 *
 * <AUTHOR>
 * @date 2022/08/18
 */
@Data
public class BusinessException extends RuntimeException {


    private static final long serialVersionUID = 2499189342726925091L;

    private String status;

    private ResultInfoEnum e;

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(String message, String status) {
        super(message);
        this.status = status;
    }

    public BusinessException(ResultInfoEnum e) {
        super(e.getMsg());
        this.status = e.getCode();
        this.e = e;
    }
}
