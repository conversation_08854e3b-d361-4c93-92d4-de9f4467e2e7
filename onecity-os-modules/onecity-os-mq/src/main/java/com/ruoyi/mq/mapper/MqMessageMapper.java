package com.ruoyi.mq.mapper;

import com.onecity.os.common.core.dao.BaseMapper;
import com.ruoyi.mq.model.po.MqMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022-07-19 15:02
 */
@Mapper
public interface MqMessageMapper extends BaseMapper<MqMessage> {
    List<MqMessage> selectMessageByType(@Param("typeList") List<Integer> typeList);

    void remove10dayMessage(@Param("startDate")Date startDate, @Param("type")int type);
}
