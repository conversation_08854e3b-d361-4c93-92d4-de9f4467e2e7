package com.ruoyi.mq.service.impl;

import com.alibaba.fastjson.JSON;
import com.onecity.os.common.core.enums.ResultInfoEnum;
import com.onecity.os.common.core.utils.IdWorker;
import com.onecity.os.common.core.utils.StringUtils;
import com.ruoyi.mq.constant.MqConstant;
import com.ruoyi.mq.exception.BusinessException;
import com.ruoyi.mq.model.po.MqMessage;
import com.ruoyi.mq.model.vo.MqMessageReqVo;
import com.ruoyi.mq.service.MqMessageService;
import com.ruoyi.mq.service.RabbitMqService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022-07-19 15:21
 */
@Slf4j
@Service
public class RabbitMqServiceImpl implements RabbitMqService {
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private AmqpAdmin amqpAdmin;
    @Autowired
    private MqMessageService mqMessageService;
    @Autowired
    private IdWorker idWorker;
    @Override
    public void sendMessage(MqMessageReqVo reqVo){
        String exChangeName = reqVo.getExChangeName();
        String exChangeType = reqVo.getExChangeType();
        String queueName = reqVo.getQueueName();
        String routingKey =  reqVo.getRoutingKey();
        Object messageContent = reqVo.getMessageContent();
        if(StringUtils.isBlank(exChangeName)){
            exChangeName = MqConstant.TOPIC_EXCHANGE;
            reqVo.setExChangeName(exChangeName);
        }
        if(StringUtils.isBlank(exChangeType)){
            exChangeType = MqConstant.TOPIC;
            reqVo.setExChangeType(exChangeType);
        }else{
            if(!(MqConstant.TOPIC.equals(exChangeType)||MqConstant.DIRECT.equals(exChangeType)||MqConstant.FANOUT.equals(exChangeType))){
                throw new BusinessException(ResultInfoEnum.EXHCANGE_TYPE_ERROR);
            }
        }
        if(StringUtils.isBlank(queueName)){
            queueName = MqConstant.DEFAULT_QUEUE_NAME;
            reqVo.setQueueName(queueName);
        }
        if(StringUtils.isBlank(routingKey)){
            routingKey = MqConstant.DEFAULT_ROUTING_KEY;
            reqVo.setRoutingKey(routingKey);
        }
        CorrelationData correlationData = new CorrelationData();
        Long messageId = idWorker.nextId();
        correlationData.setId(messageId.toString());
        Exchange exChange = new ExchangeBuilder(exChangeName, exChangeType).build();
        amqpAdmin.declareExchange(exChange);
        Queue queue = new Queue(queueName);
        amqpAdmin.declareQueue(queue);

        Binding binding = BindingBuilder.bind(queue).to(exChange).with(routingKey).noargs();
        amqpAdmin.declareBinding(binding);

        MqMessage mqMessage = this.saveMessage(reqVo,messageId);
        try {
            log.info("发送消息到mq");
            rabbitTemplate.convertAndSend(exChangeName, routingKey, messageContent,correlationData);
        } catch (AmqpException e) {
            log.error("发送消息到mq异常：{}",e);
            //错误抵达
            this.updateMessage(mqMessage,2);
        }
        log.info("消息生产成功");
        //已抵达
        this.updateMessage(mqMessage,3);

    }

    private MqMessage saveMessage(MqMessageReqVo reqVo,Long messageId){
        MqMessage message = new MqMessage();
        message.setId(messageId);
        Object messageContent = reqVo.getMessageContent();
        String json = JSON.toJSONString(messageContent);
        log.info("消费的消息为："+json);
        message.setContent(json);
        message.setClassType(messageContent.getClass().getTypeName());
        message.setToExchange(reqVo.getExChangeName());
        message.setRoutingKey(reqVo.getRoutingKey());
        message.setMessageType(0);
        Date date = new Date();
        message.setCreateTime(date);
        message.setUpdateTime(date);
        mqMessageService.saveOrUpdate(message);
        return message;
    }
    private void updateMessage(MqMessage message,Integer messageType){
        message.setMessageType(messageType);
        message.setUpdateTime(new Date());
        mqMessageService.updateById(message);
    }

}
