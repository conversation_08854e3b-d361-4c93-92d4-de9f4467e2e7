package com.onecity.os.news;

import com.onecity.os.news.recommend.dbconnection.DBKit;
import com.onecity.os.news.recommend.dbconnection.DatabaseManager;
import com.onecity.os.news.recommend.main.JobSetter;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 新闻信息服务
 *
 * <AUTHOR>
 */
@Slf4j
@EnableDiscoveryClient
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@MapperScan(basePackages = {"com.onecity.os.news.modules.news.mapper"})
//@EnableFeignClients(basePackages = "com.onecity.os.news.modules.news.feign")
public class OneCityNewsApplication {
    public static void main(String[] args) throws UnknownHostException {
        ConfigurableApplicationContext application = SpringApplication.run(OneCityNewsApplication.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String path = env.getProperty("server.servlet.context-path");
        log.info("\n----------------------------------------------------------\n\t" +
                "Application 新闻服务 is running! \n\t" +
                "----------------------------------------------------------");

        System.out.println("OneCityNewsApplication  \n" +
                " .-------.       ____     __        \n" +
                " |  _ _   \\      \\   \\   /  /    \n" +
                " | ( ' )  |       \\  _. /  '       \n" +
                " |(_ o _) /        _( )_ .'         \n" +
                " | (_,_).' __  ___(_ o _)'          \n" +
                " |  |\\ \\  |  ||   |(_,_)'         \n" +
                " |  | \\ `'   /|   `-'  /           \n" +
                " |  |  \\    /  \\      /           \n" +
                " ''-'   `'-'    `-..-'              ");
        try {
            DatabaseManager.startup();
            Runtime.getRuntime().addShutdownHook(new Thread(DatabaseManager::shutdown));
        } catch (Exception e) {
            log.error("---jjoobb--数据库初始化 异常", e);
        }
        try {
            //选择要在推荐系统中运行的推荐算法
            boolean enableCF = true, enableCB = true, enableHR = true;

            // 模拟定时任务高频执行
//            for (int i=0; i<100; i++) {
            // 为所有用户执行推荐
                new JobSetter(enableCF, enableCB, enableHR).executeQuartzJobForAllUsers();
                DBKit.logPoolStatus("main");
//            }
        } catch (Exception e) {
            log.error("---jjoobb--为所有用户执行推荐 异常", e);
        }
    }
}
