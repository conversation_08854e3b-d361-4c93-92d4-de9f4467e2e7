package com.onecity.os.news.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.onecity.os.common.core.constant.HttpStatus;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.common.core.utils.XssUtil;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.PageDomain;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.core.web.page.TableSupport;
import com.onecity.os.news.modules.news.entity.Comment;
import com.onecity.os.news.modules.news.entity.News;
import com.onecity.os.news.modules.news.entity.UserLog;
import com.onecity.os.news.modules.news.entity.VisitLog;
import com.onecity.os.news.modules.news.vo.CommentVo;
import com.onecity.os.news.modules.news.vo.NewsVo;
import com.onecity.os.news.service.CommentService;
import com.onecity.os.news.service.NewsService;
import com.onecity.os.news.service.UserService;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.ansj.app.keyword.Keyword;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 新闻信息
 *
 * @Author: zack
 * @Date: 2023/11/7 10:53
 */
@Slf4j
@RestController
@RequestMapping("/news")
public class NewsController extends BaseController {

    @Resource
    private NewsService newsService;

    @Resource
    private CommentService commentService;

    @Resource
    private UserService userService;

    /**
     * 新闻信息查询接口
     *
     * @return 成功message
     */
    @GetMapping("/list")
    public TableDataInfo queryPageList(NewsVo newsVo) {
        //置顶数据处理
        List<News> topList = new ArrayList<>();
        List<String> topIdList = new ArrayList<>();
        boolean needTop = null != newsVo.getNeedTop() && 1 == newsVo.getNeedTop();
        if (needTop) {
            //查询置顶数据 教育部官网，工信部官网，中国政府网，黑龙江政府网，哈尔滨政府网
            topList = newsService.queryTopNews();
            //获取展示数据的ID
            for (News t : topList) {
                topIdList.add(t.getId());
                t.setTop(1);
            }
        }
        //用户信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        newsVo.setUserId(String.valueOf(loginUser.getUserid()));
        //判断查询总量是否超过查询上限
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer inputPageNum = pageDomain.getPageNum();
        startPage();
        List<News> newsList;
        // 用来区分 搜索和切换 tab
        log.info("----lliisstt----- (获取分页列表) tab 切换查询 size:{}", JSON.toJSON(newsVo));
        if (StringUtils.isBlank(newsVo.getTitle())) {
            newsList = newsService.queryByParam(newsVo, topIdList);
            log.info("----jjoobb-----newsService.queryByParam(获取分页列表耗时) tab 切换查询 size:{}", newsList.size());
        } else {
            newsList = newsService.queryByTitle(newsVo);
            log.info("----jjoobb-----newsService.queryByTitle(获取分页列表耗时) 模糊查询 title：{} size:{}", JSONObject.toJSONString(newsVo.getTitle()), JSONObject.toJSONString(newsList.size()));
        }
        newsList.addAll(topList);
        return getDataTable(newsList, inputPageNum);
    }

    /**
     * 新闻信息查询接口
     * @return 成功message
     */
    @GetMapping("/recommendList")
    public TableDataInfo queryPageList(@RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        //用户信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long userid = loginUser.getUserid();
        log.info("------recommendList-----userid:" + userid);
        // 异步写入用户信息
        long startTime = System.nanoTime();
        userService.saveUserInfo(loginUser.getUserid() + "");
        long endTime = System.nanoTime();
        // 计算并打印耗时  单位是纳秒
        long duration = endTime - startTime;
        log.info("userService.saveUserInfo(根据id 异步写入数据库) 执行耗时: " + duration / 1000000 + " 毫秒" + "--startTime:" + startTime + "--endTime:" + endTime);
        startPage(1, pageSize);
        //推荐数据处理
        List<News> newsList = newsService.queryRecommendList(userid, pageNum, pageSize);
        return getDataTable(newsList,true);
    }
    /**
     * 新闻信息查询接口
     * @return 成功message
     */
    @GetMapping("/getKeyWord")
    public TableDataInfo getKeyWord(@RequestParam(name = "newsId") Long newsId,
                                    @RequestParam(name = "num", defaultValue = "20") Integer num) {

        //推荐数据处理
        List<Keyword> keywords = newsService.getKeyWord(newsId+"", num);
        return getDataTable(keywords);
    }
    /**
     * 临时接口，获取用户喜好程度和用户的匹配度
     *
     * @return 成功message
     */
    @GetMapping("/getMatchValue")
    public BaseResult getMatchValue
    (@RequestParam(name = "newsId") Long newsId,
     @RequestParam(name = "userId", defaultValue = "0") Long userId) {
        if (userId == 0) {
            //用户信息
            LoginUser loginUser = SecurityUtils.getLoginUser();
            userId = loginUser.getUserid();
        }
        //获取用户和某个新闻的匹配度
        String value = newsService.getMatchValue(userId, newsId);
        return BaseResult.ok(value);
    }

    @PostMapping("/saveVisitMsg")
    public BaseResult<VisitLog> saveVisitMsg(@RequestBody @Valid VisitLog visitLog) {
        //用户信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        visitLog.setUserId(String.valueOf(loginUser.getSysUser().getUserId()));
        visitLog.setUserName(loginUser.getSysUser().getNickName());
        return newsService.saveVisitMsg(visitLog);
    }

    /**
     * 用户操作记录接口
     */
    @PostMapping("/saveUserOperate")
    public BaseResult<UserLog> saveUserOperate(@RequestBody @Valid UserLog userLog) {
        //用户信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        userLog.setUserId(String.valueOf(loginUser.getSysUser().getUserId()));
        userLog.setUserName(loginUser.getSysUser().getNickName());
        return userService.saveUserLog(userLog);
    }

    /**
     * 用户操作记录接口
     */
    @PostMapping("/delUserOperate")
    public BaseResult<String> delUserOperate(@RequestBody @Valid UserLog userLog) {
        //用户信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        userLog.setUserId(String.valueOf(loginUser.getSysUser().getUserId()));
        userLog.setUserName(loginUser.getSysUser().getNickName());
        userService.delUserLog(userLog.getNewsId(), userLog.getUserId(), userLog.getOperate());
        return BaseResult.ok("操作成功");
    }

    /**
     * 用户操作查询接口
     */
    @PostMapping("/queryUserOperate")
    public TableDataInfo queryUserLog(@RequestBody @Valid UserLog userLog) {
        //用户信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        userLog.setUserId(String.valueOf(loginUser.getSysUser().getUserId()));
        userLog.setUserName(loginUser.getSysUser().getNickName());
        startPage();
        List<UserLog> userLogList = userService.queryUserLog(userLog.getUserName(), userLog.getNewsSource(),
                userLog.getNewsType(), userLog.getOperate());
        return getDataTable(userLogList);
    }

    /**
     * 评论查询
     */
    @PostMapping("/commentList")
    public TableDataInfo commentList(@RequestBody @Valid CommentVo commentVo) {
        startPage();
        List<Comment> commentList = commentService.queryByParam(commentVo.getNewsId(), commentVo.getEffect());
        return getDataTable(commentList);
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected TableDataInfo getDataTable(List<?> list, Integer inputPageNum) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setTotal(new PageInfo(list).getTotal());
        //判断查询总量是否超过查询上限
        if (inputPageNum > new PageInfo(list).getPageNum()) {
            rspData.setRows(new ArrayList<>());
        } else {
            rspData.setRows(list);
        }
//        return rspData;
        return XssUtil.filterXssRichTextResult(rspData);
    }
}
