package com.onecity.os.news.modules.news;

import com.onecity.os.common.core.utils.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 新闻类型枚举对照 便于推荐
 * 1	szyw	时政要闻
 * 2	xnkx	校内快讯
 * 3	gxdt	领导动态
 * 4	mtjj	媒体聚焦
 */
public enum NewsTypeEnum {


    /**
     * 1	szyw	时政要闻
     * 2	xnkx	校内快讯
     * 3	gxdt	领导动态
     * 4	mtjj	媒体聚焦
     */
    szyw(1, "szyw", "时政要闻"), xnkx(2, "xnkx", "校内快讯"), gxdt(3, "gxdt", "领导动态"), mtjj(4, "mtjj", "媒体聚焦");

    /**
     * code
     * 对应userLog.operate
     */
    private final int code;
    /**
     * operate
     * 对照newslog.prefer_degree
     */
    private final String type;

    /**
     * 注释
     */
    private final String note;

    /**
     * 构造函数
     */
    NewsTypeEnum(int code, String type, String note) {
        this.code = code;
        this.type = type;
        this.note = note;
    }

    /**
     * 获取code
     *
     * @return code
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取preferDegree
     *
     * @return preferDegree
     */
    public String getType() {
        return type;
    }

    /**
     * 获取note
     *
     * @return note
     */
    public String getNote() {
        return note;
    }

    /**
     * 根据code找枚举
     *
     * @param code
     * @return
     */
    public static NewsTypeEnum getNewsTypeEnumByCode(int code) {
        Optional<NewsTypeEnum> first = Arrays.stream(values()).filter(e -> e.getCode() == code).findFirst();
        return first.isPresent() ? first.get() : null;
    }

    /**
     * 根据type找枚举
     *
     * @param type
     * @return
     */
    public static NewsTypeEnum getNewsTypeEnumByType(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        Optional<NewsTypeEnum> first = Arrays.stream(values()).filter(e -> type.equals(e.getType())).findFirst();
        return first.orElse(null);
    }

    public static Map<String, String> getTypeMap() {
        Map<String, String> typeMap = new HashMap<>();
        Arrays.stream(values()).forEach(e -> typeMap.put(e.getType(), e.getNote()));
        return typeMap;
    }

}
