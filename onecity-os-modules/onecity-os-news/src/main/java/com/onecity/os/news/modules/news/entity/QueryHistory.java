package com.onecity.os.news.modules.news.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 用户搜索记录
 * @TableName query_history
 */
@Table(name ="query_history")
@Data
public class QueryHistory {
    /**
     * 
     */
    @Id
    @GeneratedValue
    private Integer id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 搜索内容
     */
    private String content;

    /**
     * 类型(szyw-时政要闻;gxdt-领导动态;mtjj-媒体聚焦)  
     */
    @Column(name = "type")
    private String type;

    /**
     * 搜索结果数量
     */
    private Integer resultCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备用字段
     */
    private String remark;
}