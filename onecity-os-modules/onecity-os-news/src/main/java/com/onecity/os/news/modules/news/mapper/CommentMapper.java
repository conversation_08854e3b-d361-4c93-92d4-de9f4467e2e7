package com.onecity.os.news.modules.news.mapper;

import com.onecity.os.news.modules.news.entity.Comment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2023/12/14 16:14
 */
@Mapper
public interface CommentMapper {

    /**
     * @param newsId
     * @param effect
     * @return
     */
    List<Comment> queryByParam(@Param("newsId") String newsId, @Param("effect") Integer effect);
}
