package com.onecity.os.news.modules.news.mapper;

import com.onecity.os.news.modules.news.entity.News;
import com.onecity.os.news.modules.news.vo.NewsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2023/11/8 09:20
 */
@Mapper
public interface NewsMapper {

    /**
     * 新闻信息查询
     * @param newsVo
     * @return
     */
    List<News> queryByParam(@Param("news") NewsVo newsVo, @Param("ids") List<String> topIdList);
    /**
     * 新闻信息模糊查询
     * @param newsVo
     * @return
     */
    List<News> queryByTitle(@Param("news") NewsVo newsVo);

    /**
     * 推荐新闻信息查询 根据新闻 id 查询
     * @return
     */
    List<News> queryByIds(@Param("ids") List<String> topIdList);

    /**
     * 新闻信息查询 （20240319 mtjj类型数据只保留抖音数据）
     * 新增 查询除推荐新闻 id 外的 limit 条数据，以便展示
     * @param limit
     * @return
     */
    List<News> queryByLimit(@Param("limit") int limit, @Param("ids") List<String> topIdList);

    List<News> queryTopNews();

}
