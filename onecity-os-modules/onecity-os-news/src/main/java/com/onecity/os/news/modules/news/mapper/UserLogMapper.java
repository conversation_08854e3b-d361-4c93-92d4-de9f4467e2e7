package com.onecity.os.news.modules.news.mapper;

import com.onecity.os.news.modules.news.entity.UserLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2024/1/4 09:53
 */
@Mapper
public interface UserLogMapper {

    /**
     * 新建用户操作记录
     *
     * @param userLog
     */
    void insertUserLog(@Param("user") UserLog userLog);

    /**
     * 用户记录查询
     *
     * @param userName
     * @param newsSource
     * @param newsType
     * @param operate
     * @return
     */
    List<UserLog> queryByParam(@Param("userName") String userName, @Param("newsSource") String newsSource,
                               @Param("newsType") String newsType, @Param("operate") String operate);

    void delUserLog(@Param("newsId") String newsId, @Param("userId") String userId, @Param("operate") String operate);

    List<UserLog> queryByNewsId(@Param("userId") String userId, @Param("newsIds") List<String> topIdList);

    UserLog queryOneByNewsId(@Param("userId") String userId, @Param("newsId") String topIdList);


}
