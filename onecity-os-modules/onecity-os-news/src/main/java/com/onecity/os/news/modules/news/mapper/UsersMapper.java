package com.onecity.os.news.modules.news.mapper;

import com.onecity.os.news.modules.news.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【users】的数据库操作Mapper
 * @createDate 2025-02-20 18:19:35
 */
@Mapper
public interface UsersMapper {

    int insertUser(@Param("userId") String userId, @Param("prefList") String prefList, @Param("name") String name);

    User queryByUserId(@Param("userId") String userId);

    int updateUser(@Param("userId") String userId);


}




