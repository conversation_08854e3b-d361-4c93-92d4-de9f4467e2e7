/**
 *
 */
package com.onecity.os.news.recommend.UserBasedCollaborativeRecommender;

import com.alibaba.fastjson.JSON;
import org.apache.log4j.Logger;
import org.apache.mahout.cf.taste.common.TasteException;
import org.apache.mahout.cf.taste.impl.model.jdbc.MySQLBooleanPrefJDBCDataModel;
import org.apache.mahout.cf.taste.impl.neighborhood.NearestNUserNeighborhood;
import org.apache.mahout.cf.taste.impl.recommender.GenericUserBasedRecommender;
import org.apache.mahout.cf.taste.impl.similarity.LogLikelihoodSimilarity;
import org.apache.mahout.cf.taste.neighborhood.UserNeighborhood;
import org.apache.mahout.cf.taste.recommender.RecommendedItem;
import org.apache.mahout.cf.taste.recommender.Recommender;
import org.apache.mahout.cf.taste.similarity.UserSimilarity;
import com.onecity.os.news.recommend.algorithms.PropGetKit;
import com.onecity.os.news.recommend.algorithms.RecommendAlgorithm;
import com.onecity.os.news.recommend.algorithms.RecommendKit;
import com.onecity.os.news.recommend.dbconnection.DBKit;
import com.onecity.os.news.recommend.model.Newslogs;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @github https://github.com/bluemapleman
 * @date 2016年10月18日 协同过滤
 */

/**
 * Collaborative-Based Filter 基于用户的协同过滤
 *
 */
public class MahoutUserBasedCollaborativeRecommender implements RecommendAlgorithm {
    public static final Logger logger = Logger.getLogger(MahoutUserBasedCollaborativeRecommender.class);

    /**
     * 对应计算相似度时的时效天数
     */
    private static int inRecDays = PropGetKit.getInt("CFValidDay");

    /**
     * 给每个用户推荐的新闻的条数
     */
    public static int N = PropGetKit.getInt("CFRecNum");

    /**
     * 给特定的一批用户进行新闻推荐
     *
     */
    @SuppressWarnings("unused")
    @Override
    public void recommend(List<String> users) {
        int count = 0;
        try {
            logger.info("-----开始给给特定的一批用户进行新闻推荐---CF start at :{} 用户列表:{} " + new Date() + JSON.toJSONString(users));

            MySQLBooleanPrefJDBCDataModel dataModel = DBKit.getMySQLJDBCDataModel();

            List<Newslogs> newslogList = Newslogs.dao.find("select " + DBKit.PREF_TABLE_USERID + ","
                    + DBKit.PREF_TABLE_NEWSID + "," + DBKit.PREF_TABLE_TIME + " from user_log" +
                    "where status =0 and  view_time > now() - interval " + inRecDays + " day");
            logger.info("------临时日志 记录user_log 查询结果-------CF start to remove expired user_log 可以优化 sql 查询最近的新闻");
            // 移除过期的用户浏览新闻行为，这些行为对计算用户相似度不再具有较大价值
            for (Newslogs newslog : newslogList) {
                if (newslog.getViewTime().before(RecommendKit.getInRecTimestamp(inRecDays))) {
                    dataModel.removePreference(Long.parseLong(newslog.getUserId()), newslog.getNewsId());
                }
            }

            UserSimilarity similarity = new LogLikelihoodSimilarity(dataModel);

            // NearestNeighborhood的数量有待考察
            UserNeighborhood neighborhood = new NearestNUserNeighborhood(5, similarity, dataModel);

            Recommender recommender = new GenericUserBasedRecommender(dataModel, neighborhood, similarity);

            for (String user : users) {
                long start = System.currentTimeMillis();

                Long userid = Long.valueOf(user);

                List<RecommendedItem> recItems = recommender.recommend(userid, N);

                Set<Long> hs = new HashSet<Long>();

                for (RecommendedItem recItem : recItems) {
                    hs.add(recItem.getItemID());
                }

                // 过滤掉已推荐新闻和已过期新闻
                RecommendKit.filterOutDateNews(hs, user);
                RecommendKit.filterReccedNews(hs, user);

                // 无可推荐新闻
                if (hs == null) {
                    continue;
                }

                if (hs.size() > N) {
                    RecommendKit.removeOverNews(hs, N);
                }

                RecommendKit.insertRecommend(user, hs.iterator(), RecommendAlgorithm.CF);

                count += hs.size();
            }
        } catch (TasteException e) {
            logger.error("CF算法构造偏好对象失败！",e);
        } catch (Exception e) {
            logger.error("CF算法数据库操作失败！", e);
        }
        logger.info("------平均推荐新闻条数。----CF has contributed " + (count / users.size()) + " recommending news on average");
        logger.info("-----CF算法 推荐结束-------CF finish at " + new Date());
    }

    public int getRecNums() {
        return N;
    }

    public void setRecNums(int recNums) {
        N = recNums;
    }
}
