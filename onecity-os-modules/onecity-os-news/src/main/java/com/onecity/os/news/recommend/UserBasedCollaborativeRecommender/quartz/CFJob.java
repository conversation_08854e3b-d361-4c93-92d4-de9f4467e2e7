/**
 * 
 */
package com.onecity.os.news.recommend.UserBasedCollaborativeRecommender.quartz;

import com.onecity.os.news.recommend.UserBasedCollaborativeRecommender.MahoutUserBasedCollaborativeRecommender;
import org.quartz.Job;
import org.quartz.JobExecutionContext;

import java.util.List;

/**
 * 每天定时根据用户当日的新闻浏览记录来更新用户的喜好关键词列表
 */
public class CFJob implements Job
{
	@SuppressWarnings("unchecked")
	@Override
	public void execute(JobExecutionContext arg0)
	{
		List<String> users=(List<String>) arg0.getJobDetail().getJobDataMap().get("users");
		new MahoutUserBasedCollaborativeRecommender().recommend(users);
	}

}

