package com.onecity.os.news.recommend.algorithms;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @<PERSON> <PERSON>
 * @Date 2025/2/14
 * @ClassName: PropGetKitConfig
 * @Version 1.0
 */
@Data
@Component
public class DBConfig {

    // db url
    @Value("${recommend.db.url:*************************************************************************************}")
    private String url;

    /**
     * # db user
     */
    @Value("${recommend.db.user:root}")
    private String user;

    /**
     * # db password
     */
    @Value("${recommend.db.pass:JSc_@2d0e2v4#}")
    private String pass;
}
