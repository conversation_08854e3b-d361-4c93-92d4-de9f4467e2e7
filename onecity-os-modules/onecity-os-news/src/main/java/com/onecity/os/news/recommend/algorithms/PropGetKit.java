package com.onecity.os.news.recommend.algorithms;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 用以读取配置文件，获取对应属性
 * 参考 DataSourceProperty
 */
@Slf4j
public class PropGetKit {

    public static Properties propGetKit = new Properties();

    public static void loadProperties(String configFileName) {
        // 使用类加载器加载资源文件
        InputStream inputStream = PropGetKit.class.getClassLoader().getResourceAsStream(configFileName + ".properties");
        if (inputStream == null) {
            throw new IllegalArgumentException("配置文件 " + configFileName + ".properties 未找到");
        }
        try {
            propGetKit.load(inputStream);
        } catch (IOException e) {
            log.error("装载文件--->失败!", e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭输入流失败", e);
                }
            }
        }
    }

    public static String getString(String key) {
        return propGetKit.getProperty(key);
    }

    public static int getInt(String key) {
        return Integer.parseInt(propGetKit.getProperty(key));
    }
}
