/**
 *
 */
package com.onecity.os.news.recommend.contentbasedrecommend;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.onecity.os.news.modules.news.NewsTypeEnum;
import com.onecity.os.news.recommend.algorithms.ParaConfig;
import com.onecity.os.news.recommend.algorithms.PropGetKit;
import com.onecity.os.news.recommend.algorithms.RecommendAlgorithm;
import com.onecity.os.news.recommend.algorithms.RecommendKit;
import com.onecity.os.news.recommend.model.NewsDTO;
import com.onecity.os.news.service.NewsService;
import lombok.extern.slf4j.Slf4j;
import org.ansj.app.keyword.Keyword;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 基于内容的推荐 Content-Based
 *
 *       思路：提取抓取进来的新闻的关键词列表（tf-idf），与每个用户的喜好关键词列表，做关键词相似度计算，取最相似的N个新闻推荐给用户。
 *
 *       Procedure: 1、Every time that the recommendation is started up(according
 *       to quartz framework), the current day's coming in news will be
 *       processed by class TF-IDF's getTFIDF() method to obtain their key words
 *       list.And then the system go over every user and calculate the
 *       similarity between every news's key words list with user's preference
 *       list.After that, rank the news according to the similarities and
 *       recommend them to users.
 */
@Slf4j
public class ContentBasedRecommender implements RecommendAlgorithm {
    //加载系统配置文件
    @Resource
    private ParaConfig paraConfig;
    @Resource
    private NewsService newsService;

    // TFIDF提取关键词数
    private static final int KEY_WORDS_NUM = PropGetKit.getInt("TFIDFKeywordsNum");
    // 推荐新闻数
    private static final int N = PropGetKit.getInt("CBRecNum");

    @Override
    public void recommend(List<String> users) {
        try {
            int count = 0;
            log.info("----CB算法开始推荐-----CB start at " + new Date() +"用户列表"+ JSON.toJSONString(users));
            // 首先进行用户喜好关键词列表的衰减更新+用户当日历史浏览记录的更新
            new UserPrefRefresher().refresh(users);
            // 新闻及对应关键词列表的Map
            HashMap<Long, List<Keyword>> newsKeyWordsMap = new HashMap();
            HashMap<Long, String> newsModuleMap = new HashMap<>();
            // 用户喜好关键词列表
            HashMap<String, CustomizedHashMap<Integer, CustomizedHashMap<String, Double>>> userPrefListMap = RecommendKit
                    .getUserPrefListMap(users);
            List<NewsDTO> newsList = NewsDTO.dao.find("select id,title,content,content_html,type,video_image from news where create_time>" + RecommendKit.getInRecDate());
            for (NewsDTO news : newsList) {
                // TFIDF提取关键词数
                String type = news.getType();
                String videoImage = news.getVideoImage();
                String contentHtml = news.getContentHtml();
                boolean mtjjFlag = type.equals(NewsTypeEnum.mtjj.getType());
                // 过滤新闻内容contentHtml为空的 及媒体聚焦的过滤
                if (mtjjFlag || StringUtils.isBlank(contentHtml) || contentHtml.length() < 5) {
                    // 过滤新闻内容contentHtml为空的
                    continue;
                }
                newsKeyWordsMap.put(news.getId(), TFIDF.getTFIDE(news.getTitle(), news.getContent(), KEY_WORDS_NUM));
                newsModuleMap.put(news.getId(), news.getType());
            }

            for (String userId : users) {
                Map<Long, Double> tempMatchMap = new HashMap<Long, Double>();
                Iterator<Long> ite = newsKeyWordsMap.keySet().iterator();
                while (ite.hasNext()) {
                    Long newsId = ite.next();
                    String type = newsModuleMap.get(newsId);
                    NewsTypeEnum newsTypeEnumByType = NewsTypeEnum.getNewsTypeEnumByType(type);
                    if(null== newsTypeEnumByType){
                        log.error("----CB推荐 获取新闻类型失败 不存在。--------CB has type ");
                    }
                    int typeCode = NewsTypeEnum.getNewsTypeEnumByType(type).getCode();
                    CustomizedHashMap<String, Double> stringDoubleCustomizedHashMap = userPrefListMap.get(userId).get(typeCode);
                    if (MapUtil.isNotEmpty(stringDoubleCustomizedHashMap)){
                        tempMatchMap.put(newsId, getMatchValue(stringDoubleCustomizedHashMap, newsKeyWordsMap.get(newsId)));
                    }

                }
                // 去除匹配值<0的项目
                removeZeroItem(tempMatchMap);
                if (!tempMatchMap.isEmpty()) {
                    tempMatchMap = sortMapByValue(tempMatchMap);
                    Set<Long> toBeRecommended = tempMatchMap.keySet();
                    //过滤掉已经推荐过的新闻
                    RecommendKit.filterReccedNews(toBeRecommended, userId);
                    //过滤掉用户已经看过的新闻
                    RecommendKit.filterBrowsedNews(toBeRecommended, userId);
                    //如果可推荐新闻数目超过了系统默认为CB算法设置的单日推荐上限数（N），则去掉一部分多余的可推荐新闻，剩下的N个新闻才进行推荐
                    if(toBeRecommended.size()>N){
                        RecommendKit.removeOverNews(toBeRecommended,N);
                    }
//                    if (toBeRecommended.size() > paraConfig.getCBRecNum()) {
//                        RecommendKit.removeOverNews(toBeRecommended, paraConfig.getCBRecNum());
//                    }
                    RecommendKit.insertRecommend(userId, toBeRecommended.iterator(), RecommendAlgorithm.CB);
                    count += toBeRecommended.size();
                }
            }
            log.info("----CB推荐结束了 平均推荐新闻条数。--------CB has contributed " + (count / users.size()) + " recommending news on average");
        } catch (Exception e) {
            log.info("----CB推荐结束了 平均推荐新闻条数。--------CB 推荐异常", e);
        }
    }

    /**
     * 获得用户的关键词列表和新闻关键词列表的匹配程度
     *
     * @return
     */
    private double getMatchValue(CustomizedHashMap<String, Double> map, List<Keyword> list) {
        Set<String> keywordsSet = map.keySet();
        double matchValue = 0;
        for (Keyword keyword : list) {
            if (keywordsSet.contains(keyword.getName())) {
                matchValue += keyword.getScore() * map.get(keyword.getName());
            }
        }
        return matchValue;
    }

    private void removeZeroItem(Map<Long, Double> map) {
        HashSet<Long> toBeDeleteItemSet = new HashSet<Long>();
        Iterator<Long> ite = map.keySet().iterator();
        while (ite.hasNext()) {
            Long newsId = ite.next();
            if (map.get(newsId) <= 0) {
                toBeDeleteItemSet.add(newsId);
            }
        }
        for (Long item : toBeDeleteItemSet) {
            map.remove(item);
        }
    }

    /**
     * 使用 Map按value进行排序
     * @return
     */
    public static Map<Long, Double> sortMapByValue(Map<Long, Double> oriMap) {
        if (oriMap == null || oriMap.isEmpty()) {
            return null;
        }
        Map<Long, Double> sortedMap = new LinkedHashMap<Long, Double>();
        List<Map.Entry<Long, Double>> entryList = new ArrayList<Map.Entry<Long, Double>>(
                oriMap.entrySet());
        entryList.sort(new MapValueComparator());

        Iterator<Map.Entry<Long, Double>> iter = entryList.iterator();
        Map.Entry<Long, Double> tmpEntry;
        while (iter.hasNext()) {
            tmpEntry = iter.next();
            sortedMap.put(tmpEntry.getKey(), tmpEntry.getValue());
        }
        return sortedMap;
    }
}
