package com.onecity.os.news.recommend.contentbasedrecommend;

import java.util.HashMap;
import java.util.LinkedHashMap;

public class CustomizedHashMap<K, V> extends HashMap<K, V> {
	private static final long serialVersionUID = -6214411688659198244L;

	@Override
	public String toString() {
		StringBuilder sb = new StringBuilder("{");
		for (K key : keySet()) {
			sb.append("\"").append(key).append("\":").append(get(key)).append(",");
		}
		if (sb.length() > 1) {
			sb.setLength(sb.length() - 1); // 移除最后一个逗号
		}
		sb.append("}");
		return sb.toString();
	}

	public CustomizedHashMap<K, V> copyFromLinkedHashMap(LinkedHashMap<K, V> linkedHashMap) {
		this.putAll(linkedHashMap);
		return this;
	}
}
