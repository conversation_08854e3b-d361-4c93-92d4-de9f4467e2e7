package com.onecity.os.news.recommend.contentbasedrecommend;

import cn.hutool.core.map.MapUtil;
import com.jfinal.plugin.activerecord.Db;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.news.modules.news.NewsTypeEnum;
import com.onecity.os.news.modules.news.OperateEnum;
import com.onecity.os.news.recommend.algorithms.JsonKit;
import com.onecity.os.news.recommend.algorithms.RecommendKit;
import com.onecity.os.news.recommend.model.NewsDTO;
import com.onecity.os.news.recommend.model.Newslogs;
import com.onecity.os.news.recommend.model.QueryHistoryDTO;
import com.onecity.os.news.recommend.model.Users;
import lombok.extern.slf4j.Slf4j;
import org.ansj.app.keyword.Keyword;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 每次用户浏览新的新闻时，用以更新用户的喜好关键词列表
 */
@Slf4j
public class UserPrefRefresher {
    //设置TFIDF提取的关键词数目
    private static final int KEY_WORDS_NUM = 20;

    //每日衰减系数
    private static final double DEC_COEE = 0.7;

    // 默认关键词
    private static final String DEFAULT_KEYWORD = "哈工大";
    // 默认关键词权重
    private static final double DEFAULT_KEYWORD_WEIGHT = 200.0;
    // 不喜欢加权权重
    private static final double SPECIAL_WEIGHT =  -2.25;

    public void refresh() {
        refresh(RecommendKit.getUserList());
    }

    /**
     * 按照推荐频率调用的方法，一般为一天执行一次。
     * 定期根据前一天所有用户的浏览记录，在对用户进行喜好关键词列表TFIDF值衰减的后，将用户前一天看的新闻的关键词及相应TFIDF值更新到列表中去。
     * @param userIdsCol
     */
    @SuppressWarnings("unchecked")
    public void refresh(Collection<String> userIdsCol) {
        //首先对用户的喜好关键词列表进行衰减更新
        autoDecRefresh(userIdsCol);

        //用户操作新闻纪录：operateHistoryMap:<Long(userid),ArrayList<String>(newsid List)>
        Map<String, List<Newslogs>> operateHistoryMap = getYesterdayOperateHistoryMap(userIdsCol);
        log.info("getYesterdayOperateHistoryMap.提取出当天有浏览行为的用户及其各自所浏览过的新闻列表 operateHistoryMap:{}", operateHistoryMap.size());
        // 提取用户 set 为后续更新用户列表
        Set<String> operateUserIdSet = new HashSet<>(operateHistoryMap.keySet());
        // 如果前一天没有搜素记录，则不需要执行搜索结果关键词更新步骤
        // 用户搜素记录：queryHistoryMap:<String(userid),List<QueryHistoryDTO>>
        Map<String, List<QueryHistoryDTO>> queryHistoryMap = getQueryHistoryMap(userIdsCol);
        log.error("getQueryHistoryMap.提取出当天有搜索行为的用户及其各自所浏览过的新闻id列表queryHistoryMap:{}", queryHistoryMap.size());
        Set<String> queryUserIdSet = queryHistoryMap.keySet();
        //如果前一天没有浏览记录（比如新闻门户出状况暂时关停的情况下，或者初期用户较少的时候均可能出现这种情况），则不需要执行浏览关键词更新步骤
        if (MapUtil.isEmpty(operateHistoryMap) && MapUtil.isEmpty(queryHistoryMap)){
            return;
        }
        // 获取所有 userID
        operateUserIdSet.addAll(queryUserIdSet);
        //用户喜好关键词列表：userPrefListMap:<String(userid),String(json))>
        HashMap<String, CustomizedHashMap<Integer, CustomizedHashMap<String, Double>>> userPrefListMap = RecommendKit.getUserPrefListMap(operateUserIdSet);
        // 增加搜索记录关键词到用户喜欢列表
        addQueryHistory(userPrefListMap, queryHistoryMap);
        //新闻对应关键词列表与模块ID：newsTFIDFMap:<String(newsid),List<Keyword>>,<String(newsModuleId),Integer(moduleid)>
        HashMap<String, Object> newsTFIDFMap = getNewsTFIDFMap(userIdsCol,KEY_WORDS_NUM);
        log.info("getNewsTFIDFMap. 将所有当天被浏览过的新闻提取出来 newsTFIDFMap:{}", newsTFIDFMap.size());
        //开始遍历用户操作记录，更新用户喜好关键词列表
        //对每个用户（外层循环），循环他所看过的每条新闻（内层循环），对每个新闻，更新它的关键词列表到用户的对应模块中
        Iterator<String> ite = operateHistoryMap.keySet().iterator();
        while (ite.hasNext()) {
            String userId = ite.next();
            // 指定用户的浏览记录
            List<Newslogs> newslogs = operateHistoryMap.get(userId);
            // 根据操作记录获取新闻权重
            HashMap<String, Double> newsWeightMap = getNewsWeightByHistory(newslogs);
            // 去重获取该用户浏览过的新闻id 重复原因存在用户多个操作
            List<Long> newsList = newslogs.stream().map(Newslogs::getNewsId).distinct().collect(Collectors.toList());
            CustomizedHashMap<Integer, CustomizedHashMap<String, Double>> prefListMap = userPrefListMap.get(userId);
            // 遍历用户的浏览新闻id
            for (Long news : newsList) {
                String type = String.valueOf(newsTFIDFMap.get(news + "type"));
                int typeCode = NewsTypeEnum.getNewsTypeEnumByType(type).getCode();
                // 获取用户浏览后的加权值 此为系数
                Double newsWeight = newsWeightMap.getOrDefault(news.toString(), Double.valueOf(1));
                log.info("----uusseerr----添加操作权重到用户喜好列表userId:{},news:{},typeCode:{},newsWeight:{}", userId, news, typeCode, newsWeight);
                // 获得对应模块的（关键词：喜好）map
                CustomizedHashMap<String, Double> rateMap = prefListMap.getOrDefault(typeCode, new CustomizedHashMap<>());
                // 获得新闻的（关键词：TFIDF值）map
                List<Keyword> keywordList = (List<Keyword>) newsTFIDFMap.get(news.toString());

                if (keywordList != null) {
                    for (Keyword keyword : keywordList) {
                        String name = keyword.getName();
                        double score = keyword.getScore() * newsWeight;
                        rateMap.merge(name, score, Double::sum);
                    }
                }
                // 更新 prefListMap 中的 rateMap，确保最新状态
                prefListMap.put(typeCode, rateMap);
            }
        }
        Iterator<String> iterator = operateHistoryMap.keySet().iterator();
        while (iterator.hasNext()) {
            String userId = iterator.next();
            try {
                Db.update("update users set pref_list='" + userPrefListMap.get(userId) + "' where user_id=?", userId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        // 更新用户操作记录
        addOperateHistoryUsed(operateHistoryMap);

    }

    /**
     * 将用户的查询历史添加到用户偏好列表中
     * 此方法旨在通过用户的搜索历史来丰富用户的偏好信息，以便提供更个性化的服务
     *
     * @param userPrefListMap 用户偏好的列表映射，键为用户ID，值为该用户不同类型的偏好列表
     * @param queryHistoryMap 用户查询历史映射，键为用户ID，值为该用户的查询历史列表
     * @return 更新后的用户偏好列表映射
     */
    private HashMap<String, CustomizedHashMap<Integer, CustomizedHashMap<String, Double>>> addQueryHistory(
            HashMap<String, CustomizedHashMap<Integer, CustomizedHashMap<String, Double>>> userPrefListMap,
            Map<String, List<QueryHistoryDTO>> queryHistoryMap) {
        log.info("----uusseerr----添加搜索到用户喜好列表 开始更新用户偏好列表 入口打印 userPrefListMap.size:{} queryHistoryMap.size:{}", userPrefListMap.size(), queryHistoryMap.size());
        try {
            // 搜索记录为空，则直接返回
            if (MapUtil.isEmpty(queryHistoryMap)) {
                return userPrefListMap;
            }
            log.info("----uusseerr----添加搜索到用户喜好列表 开始更新用户偏好列表");
            // 遍历用户偏好列表
            for (Map.Entry<String, CustomizedHashMap<Integer, CustomizedHashMap<String, Double>>> entry : userPrefListMap.entrySet()) {
                String userId = entry.getKey();
                // 用户偏好列表
                CustomizedHashMap<Integer, CustomizedHashMap<String, Double>> prefListMap = entry.getValue();

                // 获取用户的搜索记录数组，如果为空则跳过
                List<QueryHistoryDTO> queryHistoryDTOS = queryHistoryMap.getOrDefault(userId, new ArrayList<>());
                if (CollectionUtils.isEmpty(queryHistoryDTOS)) {
                    log.info("----uusseerr----添加搜索到用户喜好列表 userId:{} 没有搜索记录 prefListMap.size:{}", userId, prefListMap.size());
                    continue;
                }

                // 提前计算每个类型的最大搜索记录，避免重复计算
                Map<Integer, QueryHistoryDTO> queryByCode = calculateQueryByCode(queryHistoryDTOS);
                // 提前计算每个类型的累加搜索记录，
                Map<Integer, Double> queryCountByCode = calculateQueryCountByCode(queryHistoryDTOS);
                log.info("----uusseerr----添加搜索到用户喜好列表 userId:{} 每个类型的最大搜索记录 queryByCode.size:{}", userId, queryByCode.size());
                // 遍历用户的偏好列表
                for (Map.Entry<Integer, CustomizedHashMap<String, Double>> prefMapEntry : prefListMap.entrySet()) {
                    // 偏好列表的类型 code
                    Integer typeCode = prefMapEntry.getKey();
                    // 类型 code 对应的偏好列表
                    CustomizedHashMap<String, Double> typeCodeMap = prefMapEntry.getValue();

                    // 如果有对应的搜索记录 内容不在偏好列表中新增，否则更新
                    if (queryByCode.containsKey(typeCode)) {
                        QueryHistoryDTO queryHistoryDTO = queryByCode.get(typeCode);
                        String content = queryHistoryDTO.getContent();
                        // 根据搜索结果数量 计算搜索权重 调用方法
                        Double resultCount = queryCountByCode.get(typeCode);
                        Double value = calculateQuery(resultCount);
                        if (typeCodeMap.containsKey(content)) {
                            typeCodeMap.merge(content, value, Double::sum);
                            log.info("----uusseerr----添加搜索到用户喜好列表 更新用户偏好列表：userId:{},所属分类 typeCode:{} ,搜索内容 content:{} 搜索结果对应分类数据resultCount:{} 对应的权重：{} 计算后的权重value:{}", userId, typeCode, content, resultCount, value, typeCodeMap.get(content));
                        } else {
                            if (resultCount != null && resultCount > 0) {
                                typeCodeMap.put(content, value);
                                log.info("----uusseerr----添加搜索到用户喜好列表 新增内容用户偏好列表：userId:{},所属分类 typeCode:{} ,搜索内容 content:{} 搜索结果对应分类数据resultCount:{} 计算后的权重value:{}" , userId, typeCode, content, resultCount,value);
                            }
                        }
                    }else {
                        log.info("----uusseerr----添加搜索到用户喜好列表 新增用户偏好列表：userId:{},所属分类 typeCode:{} , 搜索记录为空", userId, typeCode);
                    }
                }
                // 更新 prefListMap 中的 rateMap，确保最新状态
                userPrefListMap.put(userId, prefListMap);
            }
            // 更新搜索记录已使用
            addQueryHistoryUsed(queryHistoryMap);
        } catch (Exception e) {
            log.info("----uusseerr----添加搜索到用户喜好列表 新增内容用户偏好列表异常 :{}", e);
        }

        return userPrefListMap;
    }

    // 更新搜索记录已使用
    private void addQueryHistoryUsed(Map<String, List<QueryHistoryDTO>> queryHistoryMap) {
        updateHistoryUsed(queryHistoryMap, QueryHistoryDTO::getId, "query_history", "query");
    }

    // 更新操作记录已使用
    private void addOperateHistoryUsed(Map<String, List<Newslogs>> operateHistoryMap) {
        updateHistoryUsed(operateHistoryMap, Newslogs::getId, "user_log", "operate");
    }

    // 通用更新使用状态的方法
    private <T> void updateHistoryUsed(Map<String, List<T>> historyMap,
                                       Function<T, String> idExtractor,
                                       String tableName,
                                       String logType) {
        if (MapUtil.isEmpty(historyMap)) {
            return;
        }
        try {
            List<String> ids = historyMap.values().stream()
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(List::stream)
                    .map(idExtractor)
                    .collect(Collectors.toList());

            if (ids.isEmpty()) {
                return;
            }

            String idsByList = formatIds(ids);
            String sql = String.format("UPDATE %s SET used = 1 WHERE used = 0 AND id IN %s", tableName, idsByList);
            Db.update(sql);

            log.info("----uusseerr----添加搜索/操作到用户喜好列表 后更新使用过标识成功 type:%s ids:%s", logType, idsByList);
        } catch (Exception e) {
            log.error("----uusseerr----添加搜索/操作到用户喜好列表 后更新使用过标识失败 type:{} size:{}", logType, historyMap.size(), e);
        }
    }

    // 统一格式化 ID 列表
    private String formatIds(List<String> ids) {
        return ids.stream().map(id -> "'" + id + "'").collect(Collectors.joining(",", "(", ")"));
    }



    /**
     * 计算s搜索权重
     * @param x
     * @return
     */
    private double calculateQuery(double x) {
        return Double.valueOf(x * 400 / (double) (x + 400));
    }


    /**
     * 计算s搜索权重
     * @param x
     * @return
     */
    private double calculateQuery1(double x) {
        if (x == 0) {
            return 100;
        } else if (x >= 1 && x <= 20) {
            // 第一阶段：从 200 增长到 400
            double k1 = 0.6;
            double x01 = 10;
            return 200 + 200 / (1 + Math.exp(-k1 * (x - x01)));
        } else if (x >= 21 && x <= 50) {
            // 第二阶段：从 400 缓慢增长到 500
            double k2 = 0.2;
            double x02 = 35;
            return 400 + 100 / (1 + Math.exp(-k2 * (x - x02)));
        } else if (x > 50) {
            return 500;
        } else {
            // x < 0 的情况处理（可选）
            return 100;
        }
    }

    /**
     * 提取重复逻辑：计算每个类型的最新搜索记录
     * 此方法用于处理查询历史，将其按类型分组，并找出每种类型的最新查询记录
     *
     * @param queryHistoryDTOS 用户查询历史列表
     * @return 每个类型的最新查询记录映射，键为类型代码，值为最新查询记录
     */
    private Map<Integer, QueryHistoryDTO> calculateQueryByCode(List<QueryHistoryDTO> queryHistoryDTOS) {
        return queryHistoryDTOS.stream()
                // 过滤掉可能的 null 值
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(
                        n -> {
                            NewsTypeEnum newsTypeEnum = NewsTypeEnum.getNewsTypeEnumByType(n.getType());
                            // 防止 null
                            return newsTypeEnum != null ? newsTypeEnum.getCode() : null;
                        },
                        Collectors.collectingAndThen(
                                Collectors.maxBy(Comparator.comparing(QueryHistoryDTO::getCreateTime)),
                                optionalNew -> optionalNew.orElse(null)
                        )
                ));
    }

    /**
     * 提取重复逻辑：计算每个类型的搜索记录的累加和
     * 此方法用于处理查询历史，将其按类型分组，并计算每种类型的搜索记录的累加和
     *
     * @param queryHistoryDTOS 用户查询历史列表
     * @return 每个类型的搜索记录累加和映射，键为类型代码，值为累加和
     */
    private Map<Integer, Double> calculateQueryCountByCode(List<QueryHistoryDTO> queryHistoryDTOS) {
        return queryHistoryDTOS.stream()
                // 过滤掉可能的 null 值
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(
                        n -> {
                            NewsTypeEnum newsTypeEnum = NewsTypeEnum.getNewsTypeEnumByType(n.getType());
                            // 防止 null
                            return newsTypeEnum != null ? newsTypeEnum.getCode() : null;
                        },
                        Collectors.summingDouble(QueryHistoryDTO::getResultCount)
                ));
    }


    /**
     * 根据历史记录获取新闻权重
     * 该方法通过分析用户的历史新闻记录来计算每条新闻的权重
     * 权重计算基于新闻的操作类型，不同的操作可能会影响新闻的权重
     *
     * @param newslogs 用户的历史新闻记录列表，包含每条新闻的操作类型
     * @return 返回一个映射，键为新闻ID，值为该新闻的权重
     */
    private HashMap<String, Double> getNewsWeightByHistory(List<Newslogs> newslogs) {
        // 浏览记录为空，则直接返回
        if (CollectionUtils.isEmpty(newslogs)) {
            return new HashMap<>();
        }
        // 新闻 id 和 操作类型 map
        Map<String, List<Newslogs>> queryById = newslogs.stream().collect(Collectors.groupingBy(n -> String.valueOf(n.getNewsId())));
        // 根据操作类型获取新闻权重映射
        HashMap<String, Double> newsWeightMapByOperate = getNewsWeightMapByOperate(queryById);
        return newsWeightMapByOperate;
    }


    /**
     * 根据用户操作记录计算新闻权重并返回映射表。
     *
     * @param queryById 用户操作记录的映射表，键为新闻ID，值为操作记录列表。
     * @return 新闻ID与权重的映射表。
     */
    private HashMap<String, Double> getNewsWeightMapByOperate(Map<String, List<Newslogs>> queryById) {
        // 检查输入参数是否为空，避免空指针异常
        if (MapUtil.isEmpty(queryById)) {
            return new HashMap<>();
        }

        HashMap<String, Double> newsWeightMap = new HashMap<>();

        // 遍历新闻ID和操作记录列表
        for (Map.Entry<String, List<Newslogs>> entry : queryById.entrySet()) {
            String newsId = entry.getKey();
            List<Newslogs> newslogs = entry.getValue();

            // 跳过空的操作记录列表
            if (CollectionUtils.isEmpty(newslogs)) {
                continue;
            }

            try {
                // 计算用户操作的新闻权重
                double newsWeight = getNewsWeightByList(newslogs);
               if(newsWeight == 0){
                   log.error("----uusseerr----根据用户操作记录计算新闻权重值为 0 异常 用户操作列表 size:{}", newslogs.size());
               }
                newsWeightMap.put(newsId, newsWeight);
            } catch (Exception e) {
                // 捕获异常，避免单个新闻权重计算失败影响整体结果
                log.error("----uusseerr----根据用户操作记录计算新闻权重并返回映射表异常 :{}", e);
            }
        }

        return newsWeightMap;
    }


    /**
     * 根据新闻日志列表计算新闻的权重
     * 权重计算基于用户对新闻的操作（如点击、分享等），每个操作类型都有对应的权重值
     * 如果用户有不喜欢的操作（权重为负值），则累加权重；否则取所有操作中的最大权重值
     *
     * @param newslogs 新闻日志列表，包含用户对新闻的各种操作信息
     * @return 计算得到的新闻权重
     */
    private double getNewsWeightByList(List<Newslogs> newslogs) {
        double weight = 0;
        // 检查新闻日志列表是否为空，为空则直接返回初始权重值
        if (CollectionUtils.isEmpty(newslogs)) {
            return weight;
        }
        // 找到第一个 DISLIKE 操作 如果存在操作的权重为负值，设置不喜欢的标志为true
        boolean dislikeflag = newslogs.stream().anyMatch(newslog -> newslog.getOperate().equals(OperateEnum.DISLIKE.getOperate()));
        int size = newslogs.size();
        // 遍历新闻日志列表，根据操作类型计算权重
        for (Newslogs newslog : newslogs) {
            double weightOnce = OperateEnum.getOperateEnumByOperate(newslog.getOperate()).getWeight();
            // 根据权重规则更新 weight  如果出现过负权重，直接累加 没有取最大权重
            if (dislikeflag && weightOnce < 0) {
                continue;
            } else {
                weight = Math.max(weight, weightOnce);
            }
        }
        // 不喜欢 单独计算  非不喜欢:weight then size>1 时 说明要放大不喜欢 SPECIAL_WEIGHT ； 只有不喜欢就 对应权重
        weight = dislikeflag ? size > 1 ? weight + SPECIAL_WEIGHT : OperateEnum.DISLIKE.getWeight() : weight;
        // 返回最终计算的权重值 因为相乘的结果为0，则取1
        return weight;
    }

    /**
     * 所有用户的喜好关键词列表TFIDF值随时间进行自动衰减更新
     */
    public void autoDecRefresh() {
        autoDecRefresh(RecommendKit.getUserList());
    }

    /**
     * 所有用户的喜好关键词列表TFIDF值随时间进行自动衰减更新
     */
    public void autoDecRefresh(Collection<String> userIdsCol) {
        try {
            String inQuery = RecommendKit.getInQueryStringWithSingleQuote(userIdsCol.iterator());
            if (inQuery.equals("()")) {
                return;
            }
            List<Users> userList = Users.dao.find("select id,user_id,pref_list from users where user_id in " + inQuery);
            //用以更新的用户喜好关键词map的json串
            //用于删除喜好值过低的关键词
            ArrayList<String> keywordToDelete = new ArrayList<String>();
            for (Users user : userList) {
                String newPrefList = "{";
                HashMap<Integer, CustomizedHashMap<String, Double>> map = JsonKit.jsonPrefListtoMap(user.getPrefList());
                Iterator<Integer> ite = map.keySet().iterator();
                while (ite.hasNext()) {
                    //用户对应模块的喜好不为空
                    Integer moduleId = ite.next();
                    CustomizedHashMap<String, Double> moduleMap = map.get(moduleId);
                    newPrefList += "\"" + moduleId + "\":";
                    //N:{"X1":n1,"X2":n2,.....}
                    if (!(moduleMap.toString().equals("{}"))) {
                        Iterator<String> inIte = moduleMap.keySet().iterator();
                        while (inIte.hasNext()) {
                            String key = inIte.next();
                            //累计TFIDF值乘以衰减系数
                            double result = moduleMap.get(key) * DEC_COEE;
                            if (result < 10) {
                                keywordToDelete.add(key);
                            }
                            moduleMap.put(key, result);
                        }
                    }
                    for (String deleteKey : keywordToDelete) {
                        moduleMap.remove(deleteKey);
                    }
                    keywordToDelete.clear();
                    // 当某个分类为空时，则添加默认值
                    if (MapUtil.isEmpty(moduleMap)) {
                        moduleMap.put(DEFAULT_KEYWORD, DEFAULT_KEYWORD_WEIGHT);
                    }
                    newPrefList += moduleMap.toString() + ",";
                }
                newPrefList = "'" + newPrefList.substring(0, newPrefList.length() - 1) + "}'";
                log.info("---uusseerr用户兴趣关键字-----UserPrefRefresher.autoDecRefresh 更改用户喜欢列表.是否更新用户信息 end----.userId:{}--newPrefList:{}", user.getUserId(), newPrefList);
                Db.update("update users set pref_list=" + newPrefList + " where user_id=?", user.getUserId());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 提取出当天有浏览行为的用户及其各自所浏览过的新闻id列表
     * @return
     */
    private HashMap<String, ArrayList<Long>> getBrowsedHistoryMap(String inQuery) {
        HashMap<String, ArrayList<Long>> userBrowsedMap = new HashMap<String, ArrayList<Long>>();
        try {
            List<Newslogs> newslogsList = Newslogs.dao.find("select * from user_log where status =0 and  view_time>" + RecommendKit.getSpecificDayFormat(0)  + "  and  user_id in " + inQuery);
            for (Newslogs newslogs : newslogsList) {
                if (userBrowsedMap.containsKey(newslogs.getUserId())) {
                    userBrowsedMap.get(newslogs.getUserId()).add(newslogs.getNewsId());
                } else {
                    userBrowsedMap.put(newslogs.getUserId(), new ArrayList<Long>());
                    userBrowsedMap.get(newslogs.getUserId()).add(newslogs.getNewsId());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return userBrowsedMap;
    }
    /**
     * 提取出当天有浏览行为的用户及其各自所浏览过的新闻列表
     * @return
     */
    private Map<String, List<Newslogs>> getYesterdayOperateHistoryMap(Collection<String> userIdsCol) {
        Map<String, List<Newslogs>> userBrowsedMap = new HashMap<>();
        String today = RecommendKit.getSpecificDayFormat(0);
        String inQuery = RecommendKit.getInQueryStringWithSingleQuote(userIdsCol.iterator());
        if (inQuery.equals("()")) {
            return userBrowsedMap;
        }
        try {
            List<Newslogs> newslogsList = Newslogs.dao.find("select * from user_log where status =0 and used =0  and  view_time>" + today + " and user_id in " + inQuery);
            userBrowsedMap = newslogsList.stream().collect(Collectors.groupingBy(Newslogs::getUserId));
        } catch (Exception e) {
            log.error("getYesterdayOperateHistoryMap.提取出当天有浏览行为的用户及其各自所浏览过的新闻列表异常 即不存在操作记录 today:{}", today, e);
        }
        return userBrowsedMap;
    }
    /**
     * 提取出当天有搜索行为的用户及其各自所浏览过的新闻id列表
     * @return
     */
    private Map<String, List<QueryHistoryDTO>> getQueryHistoryMap(Collection<String> userIdsCol) {
        Map<String, List<QueryHistoryDTO>> result = new HashMap<>();
        String today = RecommendKit.getSpecificDayFormat(0);
        String inQuery = RecommendKit.getInQueryStringWithSingleQuote(userIdsCol.iterator());
        if (inQuery.equals("()")) {
            return result;
        }
        try {
            List<QueryHistoryDTO> queryHistoryDTOS = QueryHistoryDTO.dao.find("select * from query_history where  used =0  and create_time>" + today + "  and  user_id in " + inQuery);
            queryHistoryDTOS = Optional.ofNullable(queryHistoryDTOS).orElse(new ArrayList<>());
            result = queryHistoryDTOS.stream().collect(Collectors.groupingBy(QueryHistoryDTO::getUserId));
        } catch (Exception e) {
            log.error("getQueryHistoryMap.提取出当天有搜索行为的用户及其各自所浏览过的新闻id列表异常 即不存在搜索记录 today:{}", today, e);
        }
        return result;
    }

    /**
     * 获得浏览过的新闻的集合
     * @return
     */
    private HashSet<Long> getBrowsedNewsSet(String inQuery) {
        HashMap<String, ArrayList<Long>> browsedMap = getBrowsedHistoryMap(inQuery);
        HashSet<Long> newsIdSet = new HashSet<Long>();
        Iterator<String> ite = browsedMap.keySet().iterator();
        while (ite.hasNext()) {
            Iterator<Long> inIte = browsedMap.get(ite.next()).iterator();
            while (inIte.hasNext()) {
                newsIdSet.add(inIte.next());
            }
        }
        return newsIdSet;
    }

    /**
     * 将所有当天被浏览过的新闻提取出来，以便进行TFIDF求值操作，以及对用户喜好关键词列表的更新。
     * @return
     */
    private HashMap<String, Object> getNewsTFIDFMap(Collection<String> userIdsCol, int keyWordsNum) {
        HashMap<String, Object> newsTFIDFMap = new HashMap<String, Object>();
        String inQuery = RecommendKit.getInQueryStringWithSingleQuote(userIdsCol.iterator());
        if (inQuery.equals("()")) {
            return newsTFIDFMap;
        }
        try {
            // 没有浏览记录直接返回
            HashSet<Long> browsedNewsSet = getBrowsedNewsSet( inQuery);
            if (browsedNewsSet.size() == 0) {
                return newsTFIDFMap;
            }
            Iterator<Long> ite = browsedNewsSet.iterator();
            StringBuilder newsIdListQuery = new StringBuilder("(");
            while (ite.hasNext()) {
                long next = ite.next();
                newsIdListQuery.append(next).append(",");
            }

            newsIdListQuery = new StringBuilder(newsIdListQuery.substring(0, newsIdListQuery.length() - 1) + ")");
            //提取出所有新闻的关键词列表及对应TF-IDf值，并放入一个map中
            List<NewsDTO> newsList = NewsDTO.dao.find("select id,title,content,type,video_image from news where id in " + newsIdListQuery);
            for (NewsDTO news : newsList) {
                String type = news.getType();
                String videoImage = news.getVideoImage();
                if (type.equals(NewsTypeEnum.mtjj.getType()) && StringUtils.isBlank(videoImage)) {
                    // 过滤抖音不可播放的视频
                    continue;
                }
                List<Keyword> tfide = TFIDF.getTFIDE(news.getTitle(), news.getContent(), keyWordsNum);
                newsTFIDFMap.put(String.valueOf(news.getId()), tfide);
                newsTFIDFMap.put(news.getId() + "type", news.getType());
            }
        } catch (Exception e) {
            log.error("getNewsTFIDFMap. 将所有当天被浏览过的新闻提取出来异常 即不存在当天浏览过新闻", e);
        }
        return newsTFIDFMap;
    }
}
