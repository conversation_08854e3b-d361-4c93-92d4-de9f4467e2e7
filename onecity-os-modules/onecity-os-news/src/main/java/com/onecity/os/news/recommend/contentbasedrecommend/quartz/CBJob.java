/**
 * 
 */
package com.onecity.os.news.recommend.contentbasedrecommend.quartz;

import com.onecity.os.news.recommend.contentbasedrecommend.ContentBasedRecommender;
import org.quartz.Job;
import org.quartz.JobExecutionContext;

import java.util.List;

/**
 * 每天定时根据用户当日的新闻浏览记录来更新用户的喜好关键词列表
 */
public class CBJob implements Job
{
	@SuppressWarnings("unchecked")
	@Override
	public void execute(JobExecutionContext arg0)
	{
		List<String> users=(List<String>) arg0.getJobDetail().getJobDataMap().get("users");
		new ContentBasedRecommender().recommend(users);
	}

}

