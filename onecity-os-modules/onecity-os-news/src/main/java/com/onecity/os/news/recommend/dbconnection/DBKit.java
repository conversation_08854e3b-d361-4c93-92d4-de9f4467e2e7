package com.onecity.os.news.recommend.dbconnection;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.ActiveRecordPlugin;
import com.jfinal.plugin.c3p0.C3p0Plugin;
import com.mchange.v2.c3p0.ComboPooledDataSource;
import com.onecity.os.news.recommend.algorithms.PropGetKit;
import com.onecity.os.news.recommend.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.mahout.cf.taste.impl.model.jdbc.MySQLBooleanPrefJDBCDataModel;

import javax.sql.DataSource;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Properties;

@Slf4j
public class DBKit {
    //加载DB配置文件


    //偏好表表名
    public static final String PREF_TABLE = "user_log";
    //用户id列名
    public static final String PREF_TABLE_USERID = "user_id";
    //新闻id列名
    public static final String PREF_TABLE_NEWSID = "news_id";
    //偏好值列名
    public static final String PREF_TABLE_PREFVALUE = "prefer_degree";
    //用户浏览时间列名
    public static final String PREF_TABLE_TIME = "view_time";

    private static C3p0Plugin cp;

    // 新增状态标识
    private static volatile boolean initialized = false;

    public static void initalize() {
        // 防止重复初始化
        if (initialized) {
            return;
        }
        try {
            HashMap<String, String> info = getDBInfo();
            if (info == null) {
                log.error("无法获取数据库配置信息！");
                return;
            }
            info.forEach((k, v) -> log.info("----mmyyssqqll----数据库配置信息：" + JSONObject.toJSONString(k) + "=" + JSONObject.toJSONString(v)));
            Properties cpProperties = new Properties();

            cpProperties.put("jdbcUrl", validateString(info.get("url"), ""));
            cpProperties.put("user", validateString(info.get("user"), ""));
            cpProperties.put("password", validateString(info.get("password"), ""));
            cpProperties.put("driverClass", validateString(info.get("driverClass"), "com.mysql.cj.jdbc.Driver"));

            // 设置C3P0连接池参数并提供合理的默认值
            cpProperties.put("maxPoolSize", validateInt(info.get("maxPoolSize"), 50)); // 最大连接数
            cpProperties.put("minPoolSize", validateInt(info.get("minPoolSize"), 10));  // 最小空闲连接数
            cpProperties.put("initialPoolSize", validateInt(info.get("initialPoolSize"), 10)); // 初始连接数
            cpProperties.put("acquireIncrement", validateInt(info.get("acquireIncrement"), 5)); // 每次增长的连接数
            cpProperties.put("maxIdleTime", validateInt(info.get("maxIdleTime"), 60)); // 最大空闲时间（秒）
            cpProperties.put("checkoutTimeout", validateInt(info.get("checkoutTimeout"), 30000)); // 获取连接超时时间（毫秒）
            // 在 DBKit.initalize() 中添加
            cpProperties.put("maxConnectionAge", 1800); // 30分钟强制回收连接
            cpProperties.put("unreturnedConnectionTimeout", 120); // 2分钟未关闭连接自动回收
            cpProperties.put("automaticTestTable", "connection_test"); // 启用连接测试
            cpProperties.put("testConnectionOnCheckin", true); // 归还时测试连接


            cp = new C3p0Plugin(cpProperties);


            ActiveRecordPlugin arp = new ActiveRecordPlugin(cp);
            arp.addMapping("users", Users.class);
            arp.addMapping("news", NewsDTO.class);
            arp.addMapping("newsmodules", Newsmodules.class);
            arp.addMapping("user_log", Newslogs.class);
            arp.addMapping("query_history", QueryHistoryDTO.class);
            arp.addMapping("recommendations", Recommendations.class);

            boolean c3p0Started = cp.start();
            boolean arpStarted = arp.start();
            if (c3p0Started && arpStarted) {
                log.info("----mmyyssqqll----数据库连接池插件启动成功......");
            } else {
                if (!c3p0Started) {
                    log.error("----mmyyssqqll----C3P0插件启动失败！");
                }
                if (!arpStarted) {
                    log.error("----mmyyssqqll----ActiveRecord插件启动失败！");
                }
                // 关闭已启动的部分
                if (c3p0Started) {
                    cp.stop();
                }
                if (arpStarted) {
                    arp.stop();
                }
            }
            log.info("----mmyyssqqll----数据库初始化工作完毕！");
            // 标记已初始化
            initialized = true;
        } catch (Exception e) {
            log.error("----mmyyssqqll----数据库连接初始化错误！", e);
        }
    }
    public static void destroy() {
        if (cp != null) {
            cp.stop();
            log.info("关闭数据库连接池");
            initialized = false; // 重置状态
        }
    }
    // 在 DBKit 中定期打印连接池状态
// 修改 DBKit 类中的方法
    public static void logPoolStatus(String source) {
        if (cp != null) {
            // 强制转换为 C3P0 的具体实现类
            ComboPooledDataSource ds = (ComboPooledDataSource) cp.getDataSource();
            try {
                log.info("---jjoobb---source{}活跃连接: {}", source, ds.getNumBusyConnectionsDefaultUser());
                log.info("---jjoobb---source{}空闲连接: {}", source, ds.getNumIdleConnectionsDefaultUser());
            } catch (SQLException e) {
                log.info("---jjoobb---source{} 打印 mysql连接异常", source, e);
            }
        }
    }



    public static HashMap<String, String> getDBInfo() {
        HashMap<String, String> info = new HashMap<>();
        try {
            PropGetKit.loadProperties("dbconfig");
            info.put("url", PropGetKit.getString("url"));
            info.put("user", PropGetKit.getString("user"));
            info.put("password", PropGetKit.getString("password"));
        } catch (Exception e) {
            log.error("----mmyyssqqll----读取 db文件--->失败!");
        }
        return info;
    }

    public static DataSource getDataSource() {
        if (cp == null) initalize();
        return cp.getDataSource();
    }

    public static MySQLBooleanPrefJDBCDataModel getMySQLJDBCDataModel() {
        return new MySQLBooleanPrefJDBCDataModel(DBKit.getDataSource(), PREF_TABLE, PREF_TABLE_USERID, PREF_TABLE_NEWSID, PREF_TABLE_TIME);
    }

    public static void main(String[] args) {
        Properties p = new Properties();
        try {
            p.load(Files.newInputStream(Paths.get(System.getProperty("user.dir") + "/onecity-os-modules/onecity-os-news/src/main/resources/dbconfig.properties")));
            String url1 = p.getProperty("url");
            System.out.println(url1);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    private static String validateString(String value, String defaultValue) {
        return value != null ? value : defaultValue;
    }

    private static String validateInt(String value, int defaultValue) {
        try {
            Integer.parseInt(value);
            return value;
        } catch (NumberFormatException e) {
            return String.valueOf(defaultValue);
        }
    }
}
