package com.onecity.os.news.recommend.hotrecommend;

import com.alibaba.fastjson.JSON;
import com.onecity.os.news.recommend.algorithms.RecommendAlgorithm;
import com.onecity.os.news.recommend.algorithms.RecommendKit;
import com.onecity.os.news.recommend.model.Newslogs;
import com.onecity.os.news.recommend.model.Recommendations;
import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.util.*;

/**
 * 基于“热点新闻”生成的推荐，一般用于在CF和CB算法推荐结果数较少时进行数目的补充
 */
@Slf4j
public class HotRecommender implements RecommendAlgorithm {


    // 热点新闻的有效时间
    public static int beforeDays = -10;
    // 推荐系统每日为每位用户生成的推荐结果的总数，当CF与CB算法生成的推荐结果数不足此数时，由该算法补充
    public static int TOTAL_REC_NUM = 20;
    // 将每天生成的“热点新闻”ID，按照新闻的热点程度从高到低放入此List
    private static ArrayList<Long> topHotNewsList = new ArrayList<Long>();

    @Override
    public void recommend(List<String> users) {
        log.info("HR start at " + new Date());
        int count = 0;
        // 获取今天 0 点的时间戳对象
        Timestamp timestamp = getCertainTimestamp(0, 0, 0);
        for (String userId : users) {
            try {
                //获得已经预备为当前用户推荐的新闻 且未展示，若数目不足达不到单次的最低推荐数目要求，则用热点新闻补充
                Recommendations recommendation = Recommendations.dao.findFirst("select user_id,count(*) as recnums from recommendations where derive_time>'" + timestamp + "' and displayed=0 " + " and user_id='" + userId + "' group by user_id");

                boolean flag = (recommendation != null);
                Integer tmpRecNums = 0;
                if (recommendation != null) {
                    Long recnums = recommendation.getLong("recnums");
                    tmpRecNums = recnums.intValue();
                }
                int delta = flag ? TOTAL_REC_NUM - Integer.valueOf(tmpRecNums.toString()) : TOTAL_REC_NUM;
                Set<Long> toBeRecommended = new HashSet<Long>();
                if (delta > 0) {
                    int i = topHotNewsList.size() > delta ? delta : topHotNewsList.size();
                    while (i-- > 0) toBeRecommended.add(topHotNewsList.get(i));
                }
                RecommendKit.filterBrowsedNews(toBeRecommended, userId);
                RecommendKit.filterReccedNews(toBeRecommended, userId);
                RecommendKit.insertRecommend(userId, toBeRecommended.iterator(), RecommendAlgorithm.HR);
                count += toBeRecommended.size();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        log.info("HR has contributed " + (users.size() == 0 ? 0 : count / users.size()) + " recommending news on average");

    }

    public static void formTodayTopHotNewsList() {
        topHotNewsList.clear();
        ArrayList<Long> hotNewsTobeReccommended = new ArrayList<Long>();
        try {
            List<Newslogs> newslogsList = Newslogs.dao.find("select news_id,count(*) as visitNums from user_log where status =0 and view_time>" + RecommendKit.getInRecDate(beforeDays) + " group by news_id order by visitNums desc");
            log.info("--jjoobb--临时日志 获得热点新闻列表 最近十天按照浏览数量倒序排列新闻 id 此处查询 浏览表 操作表都可 ------" + JSON.toJSONString(newslogsList));
            for (Newslogs newslog : newslogsList) {
                hotNewsTobeReccommended.add(newslog.getNewsId());
            }
            for (Long news : hotNewsTobeReccommended) {
                topHotNewsList.add(news);
            }
        } catch (Exception e) {
            log.error("--jjoobb-- 获得热点新闻列表 最近十天按照浏览数量倒序排列新闻 id 此处查询 浏览表 操作表都可 失败", e);
        }
    }

    public static List<Long> getTopHotNewsList() {
        log.info("--jjoobb--临时日志 热点新闻”ID，按照新闻的热点程度从高到低放入此List------topHotNewsList size is " + topHotNewsList.size() + "----临时日志------" + JSON.toJSONString(topHotNewsList));
        return topHotNewsList;
    }

    public static int getTopHopNewsListSize() {
        return topHotNewsList.size();
    }

    /**
     * 获取指定时分秒的时间戳
     * 此方法用于根据指定的时分秒，获取对应的时间戳对象它通过修改当前日历对象的时间字段来实现，
     * 将小时、分钟和秒设置为指定的值，然后从日历对象中获取时间，并将其转换为时间戳对象返回
     *
     * @param hour    指定的小时数，范围是0-23
     * @param minute  指定的分钟数，范围是0-59
     * @param second  指定的秒数，范围是0-59
     * @return 返回设置指定时分秒后的时间戳对象
     */
    private Timestamp getCertainTimestamp(int hour, int minute, int second) {
        Calendar calendar = Calendar.getInstance(); // 得到日历
        calendar.set(Calendar.HOUR_OF_DAY, hour); // 设置小时
        calendar.set(Calendar.MINUTE, minute); // 设置分钟
        calendar.set(Calendar.SECOND, second); // 设置秒
        return new Timestamp(calendar.getTime().getTime()); // 根据设置的时间生成时间戳并返回
    }

}
