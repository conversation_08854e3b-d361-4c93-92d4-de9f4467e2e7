/**
 * 
 */
package com.onecity.os.news.recommend.hotrecommend.quartz;

import com.onecity.os.news.recommend.hotrecommend.HotRecommender;
import org.quartz.Job;
import org.quartz.JobExecutionContext;

/**
 * 每天定时生成热点新闻的列表
 */
public class HRJob implements Job
{
	@Override
	public void execute(JobExecutionContext arg0)
	{
		HotRecommender.getTopHotNewsList().clear();
		HotRecommender.formTodayTopHotNewsList();
	}

}

