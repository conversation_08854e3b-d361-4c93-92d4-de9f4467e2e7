package com.onecity.os.news.recommend.model.base;

import com.jfinal.plugin.activerecord.IBean;
import com.jfinal.plugin.activerecord.Model;

import java.time.LocalDateTime;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseQueryHistory<M extends BaseQueryHistory<M>> extends Model<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}

	public String getId() {
		return get("id");
	}

	public void setUserId(String userId) {
		set("user_id", userId);
	}

	public String getUserId() {
		return get("user_id");
	}

	public void setContent(String content) {
		set("content", content);
	}

	public String getContent() {
		return get("content");
	}
	public void setType(String type) {
		set("type", type);
	}

	public String getType() {
		return get("type");
	}


	public void setResultCount(Integer resultCount) {
		set("result_count", resultCount);
	}

	public Integer getResultCount() {
		return get("result_count");
	}

	public void setUsed(Boolean used) {
		set("used", used);
	}

	public Boolean getUsed() {
		return get("used");
	}


	public void setCreateTime(LocalDateTime createTime) {
		set("create_time", createTime);
	}

	public LocalDateTime getCreateTime() {
		return get("create_time");
	}



}
