package com.onecity.os.news.recommend.model.base;

import com.jfinal.plugin.activerecord.IBean;
import com.jfinal.plugin.activerecord.Model;

/**
 * Generated by <PERSON><PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseRecommendations<M extends BaseRecommendations<M>> extends Model<M> implements IBean {

	public void setId(Long id) {
		set("id", id);
	}

	public Long getId() {
		return get("id");
	}

	public void setUserId(String userId) {
		set("user_id", userId);
	}

	public String getUserId() {
		return get("user_id");
	}

	public void setNewsId(Long newsId) {
		set("news_id", newsId);
	}

	public Long getNewsId() {
		return get("news_id");
	}

	public void setDeriveTime(java.util.Date deriveTime) {
		set("derive_time", deriveTime);
	}

	public java.util.Date getDeriveTime() {
		return get("derive_time");
	}

	public void setFeedback(Boolean feedback) {
		set("feedback", feedback);
	}

	public Boolean getFeedback() {
		return get("feedback");
	}

	public void setDeriveAlgorithm(Integer deriveAlgorithm) {
		set("derive_algorithm", deriveAlgorithm);
	}

	public Integer getDeriveAlgorithm() {
		return get("derive_algorithm");
	}

}
