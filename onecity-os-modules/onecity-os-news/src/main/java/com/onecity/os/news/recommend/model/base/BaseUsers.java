package com.onecity.os.news.recommend.model.base;

import com.jfinal.plugin.activerecord.IBean;
import com.jfinal.plugin.activerecord.Model;

/**
 * Generated by JF<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseUsers<M extends BaseUsers<M>> extends Model<M> implements IBean {

    public void setId(Long id) {
        set("id", id);
    }

    public Long getId() {
        return get("id");
    }

    public void setUserId(String userId) {
        set("user_id", userId);
    }

    public String getUserId() {
        return get("user_id");
    }

    public void setPrefList(String prefList) {
        set("pref_list", prefList);
    }

    public String getPrefList() {
        return get("pref_list");
    }

    public void setLatestLogTime(java.util.Date latestLogTime) {
        set("latest_log_time", latestLogTime);
    }

    public java.util.Date getLatestLogTime() {
        return get("latest_log_time");
    }

    public void setName(String name) {
        set("name", name);
    }

    public String getName() {
        return get("name");
    }

}
