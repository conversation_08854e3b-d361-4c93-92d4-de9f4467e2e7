package com.onecity.os.news.recommend.schedule;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 新闻推荐定时任务
 * <AUTHOR>
 * @Date 2025/2/24
 * @ClassName: NewsSchedule
 * @Version 1.0
 */
@Slf4j
@Component
public class NewsSchedule {
    @Async
    @Scheduled(cron = "0 58 23 * * ?")
    public void ScheduledRecommendation() {
        log.info("-----jjoobb-----测试定时任务");
        //选择要在推荐系统中运行的推荐算法
        boolean enableCF = true, enableCB = true, enableHR = true;
        long startTime = System.currentTimeMillis();
        try {
            // 为所有用户执行推荐
//            new JobSetter(enableCF, enableCB, enableHR).executeQuartzJobForAllUsers();

            // 记录结束时间
            long endTime = System.currentTimeMillis();
            // 计算耗时
            long duration = endTime - startTime;

            // 如果耗时超过1秒，打印日志
            log.info("---jjoobb--为所有用户执行推荐 耗时:{} 毫秒", duration);
        } catch (Exception e) {
            log.error("---jjoobb--为所有用户执行推荐 异常", e);
        }
    }
}
