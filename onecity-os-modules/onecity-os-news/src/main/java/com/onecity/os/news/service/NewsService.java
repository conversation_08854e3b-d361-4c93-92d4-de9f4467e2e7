package com.onecity.os.news.service;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.news.modules.news.entity.News;
import com.onecity.os.news.modules.news.entity.VisitLog;
import com.onecity.os.news.modules.news.vo.NewsVo;
import org.ansj.app.keyword.Keyword;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2023/11/8 08:48
 */
public interface NewsService {

    /**
     * 条件查询
     *
     * @param newsVo
     * @return
     */
    List<News> queryByParam(NewsVo newsVo, List<String> topIdList);
    /**
     * 模糊搜索查询
     *
     * @param newsVo
     * @return
     */
    List<News> queryByTitle(NewsVo newsVo);
    /**
     * 查询推荐列表
     *
     * @param userid
     * @return
     */
    List<News> queryRecommendList(Long userid,Integer pageNum,Integer pageSize);
    /**
     * 查询keyword列表
     *
     * @param newsId
     * @return
     */
    List<Keyword> getKeyWord(String newsId, Integer num);
    /**
     * 用户和某个新闻的匹配度
     *
     * @param newsId
     * @return
     */
    String getMatchValue(Long userId, Long newsId);

    /**
     * 查询置顶数据
     *
     * @return
     */
    List<News> queryTopNews();


    /**
     * 新建访问历史数据
     *
     * @param visitLog
     * @return
     */
    BaseResult<VisitLog> saveVisitMsg(VisitLog visitLog);
}

