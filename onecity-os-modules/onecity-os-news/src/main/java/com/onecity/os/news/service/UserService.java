package com.onecity.os.news.service;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.news.modules.news.entity.UserLog;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2024/1/4 08:49
 */
public interface UserService {

    /**
     * 新建用户操作记录
     *
     * @param userLog
     * @return
     */
    BaseResult<UserLog> saveUserLog(UserLog userLog);

    /**
     * 新建用户操作记录
     *
     */
    void delUserLog(String newsId, String userId, String operate);

    /**
     * 操作记录查询
     *
     * @param userName
     * @param newsSource
     * @param newsType
     * @param operate
     * @return
     */
    List<UserLog> queryUserLog(String userName, String newsSource, String newsType, String operate);


    /**
     * 根据 token 保存登录用户
     *
     * @param userId
     */
    void saveUserInfo(String userId);
}
