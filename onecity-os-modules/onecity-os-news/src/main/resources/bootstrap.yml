# Spring
spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  application:
    # 应用名称
    name: onecity-os-news
  profiles:
    # 环境配置
    active: dev
  jmx:
    enabled: false
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        # 测试环境 CHBN
        server-addr: onecity-os-nacos:8848
      config:
        # 配置中心地址
        server-addr: onecity-os-nacos:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}