<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.news.modules.news.mapper.CommentMapper">

    <select id="queryByParam"
            resultType="com.onecity.os.news.modules.news.entity.Comment">
        SELECT c.id,
        c.news_id AS newsId,
        c.content,
        c.status,
        c.p_effect AS pEffect,
        c.n_effect AS nEffect,
        c.create_time AS createTime,
        c.parent_id AS parentId,
        c.creator_id AS creatorId,
        c.creator_name AS creatorName,
        c.remark AS remark
        FROM `comment` AS c
        WHERE 1 = 1
        AND c.news_id = #{newsId}
        <if test="effect == 1">
            AND c.p_effect - c.n_effect >= 0
            ORDER BY
            c.p_effect - c.n_effect DESC
        </if>
        <if test="0 == effect">
            AND c.n_effect - c.p_effect > 0
            ORDER BY
            c.p_effect - c.n_effect
        </if>

    </select>

</mapper>