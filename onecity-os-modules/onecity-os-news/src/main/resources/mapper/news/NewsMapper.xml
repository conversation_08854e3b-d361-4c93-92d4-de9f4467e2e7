<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.news.modules.news.mapper.NewsMapper">

    <select id="queryByParam"
            resultType="com.onecity.os.news.modules.news.entity.News">
        SELECT
        n.id,
        n.title,
        n.content,
        n.content_url AS contentUrl,
        n.content_html AS contentHtml,
        n.create_time AS createTime,
        n.update_time AS updateTime,
        n.source,
        n.source_url AS sourceUrl,
        n.comments,
        n.status AS status,
        n.type AS type,
        t.new_type_name AS typeName,
        n.news_mark AS newsMark,
        n.video_name AS videoName,
        n.video_url AS videoUrl,
        n.video_play_count AS videoPlayCount,
        n.video_up_count AS videoUpCount,
        n.video_coll_count AS videoCollCount,
        n.video_author AS videoAuthor,
        n.video_image AS videoImage,
        n.video_local_path AS videoLocalPath,
        n.news_tag AS newsTag,
        n.remark AS remark
        FROM
        news AS n
        JOIN news_type AS t ON n.type = t.new_type_code
        WHERE
        1 = 1
        <if test="null != news.type and '' != news.type">
            AND n.type = #{news.type}
            <if test="'mtjj' == news.type">
                AND n.source = '抖音' AND n.video_local_path IS NOT NULL AND n.video_image IS NOT NULL
            </if>
            <if test="'mtjj' != news.type">
                AND LENGTH(n.content_html) > 4
            </if>
        </if>

        <if test="null != ids and ids.size > 0 ">
            AND n.id NOT IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        ORDER BY n.create_time DESC
    </select>

    <select id="queryByTitle"
            resultType="com.onecity.os.news.modules.news.entity.News">
        SELECT
        n.id,
        n.title,
        n.content,
        n.content_url AS contentUrl,
        n.content_html AS contentHtml,
        n.create_time AS createTime,
        n.update_time AS updateTime,
        n.source,
        n.source_url AS sourceUrl,
        n.comments,
        n.status AS status,
        n.type AS type,
        n.news_mark AS newsMark,
        n.video_name AS videoName,
        n.video_url AS videoUrl,
        n.video_play_count AS videoPlayCount,
        n.video_up_count AS videoUpCount,
        n.video_coll_count AS videoCollCount,
        n.video_author AS videoAuthor,
        n.video_image AS videoImage,
        n.video_local_path AS videoLocalPath,
        n.news_tag AS newsTag,
        n.remark AS remark
        FROM
        news AS n
        WHERE
            (INSTR(n.title,#{news.title}) or INSTR(n.content,#{news.title}))
          AND (
            (n.type = 'mtjj' AND n.source = '抖音' AND n.video_local_path IS NOT NULL AND n.video_image IS NOT NULL)
                OR ( n.type != 'mtjj' AND LENGTH(n.content_html) > 4 )
            )
        ORDER BY n.create_time DESC
    </select>

    <select id="queryByIds"
            resultType="com.onecity.os.news.modules.news.entity.News">
        SELECT
        n.id,
        n.title,
        n.content,
        n.content_url AS contentUrl,
        n.content_html AS contentHtml,
        n.create_time AS createTime,
        n.update_time AS updateTime,
        n.source,
        n.source_url AS sourceUrl,
        n.comments,
        n.status AS status,
        n.type AS type,
        n.news_mark AS newsMark,
        n.video_name AS videoName,
        n.video_url AS videoUrl,
        n.video_play_count AS videoPlayCount,
        n.video_up_count AS videoUpCount,
        n.video_coll_count AS videoCollCount,
        n.video_author AS videoAuthor,
        n.video_image AS videoImage,
        n.video_local_path AS videoLocalPath,
        n.news_tag AS newsTag,
        n.remark AS remark
        FROM
        news AS n
        WHERE
        n.id  IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND (
        (n.type = 'mtjj' AND n.source = '抖音' AND n.video_local_path IS NOT NULL AND n.video_image IS NOT NULL)
        OR ( n.type != 'mtjj' AND LENGTH(n.content_html) > 4 )
        )
        ORDER BY n.create_time DESC
    </select>

    <select id="queryByLimit"
            resultType="com.onecity.os.news.modules.news.entity.News">
        SELECT
        n.id,
        n.title,
        n.content,
        n.content_url AS contentUrl,
        n.content_html AS contentHtml,
        n.create_time AS createTime,
        n.update_time AS updateTime,
        n.source,
        n.source_url AS sourceUrl,
        n.comments,
        n.status AS status,
        n.type AS type,
        n.news_mark AS newsMark,
        n.video_name AS videoName,
        n.video_url AS videoUrl,
        n.video_play_count AS videoPlayCount,
        n.video_up_count AS videoUpCount,
        n.video_coll_count AS videoCollCount,
        n.video_author AS videoAuthor,
        n.video_image AS videoImage,
        n.video_local_path AS videoLocalPath,
        n.news_tag AS newsTag,
        n.remark AS remark
        FROM
        news AS n
        WHERE
        n.type != 'mtjj'
        AND LENGTH(n.content_html) > 4
        <if test="null != ids and ids.size > 0 ">
            AND n.id NOT IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        ORDER BY n.create_time DESC limit #{limit}
    </select>


    <select id="queryTopNews"
            resultType="com.onecity.os.news.modules.news.entity.News">
        SELECT * FROM (
                          SELECT
                              id,
                              title,
                              content,
                              content_url AS contentUrl,
                              content_html AS contentHtml,
                              create_time AS createTime,
                              update_time AS updateTime,
                              source,
                              source_url AS sourceUrl,
                              comments,
                              status,
                              type,
                              news_mark AS newsMark,
                              video_name AS videoName,
                              video_url AS videoUrl,
                              video_play_count AS videoPlayCount,
                              video_up_count AS videoUpCount,
                              video_coll_count AS videoCollCount,
                              video_author AS videoAuthor,
                              video_image AS videoImage,
                              video_local_path AS videoLocalPath,
                              news_tag AS newsTag,
                              remark,
                              @rank := IF(@currentSource = source, @rank + 1,
                    IF(@currentSource := source, 1, 1)) AS rn
                          FROM news, (SELECT @currentSource := '', @rank := 0) vars
                          WHERE source IN ('中国政府网', '教育部', '工信部网站', '黑龙江政府网', '哈尔滨政府网')
                          ORDER BY source, create_time DESC
                      ) ranked
        WHERE rn = 1
    </select>

</mapper>