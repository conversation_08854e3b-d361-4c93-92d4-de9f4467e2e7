<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.news.modules.news.mapper.QueryHistoryMapper">

    <resultMap id="BaseResultMap" type="com.onecity.os.news.modules.news.entity.QueryHistory">
            <id property="id" column="id" />
            <result property="userId" column="user_id" />
            <result property="userName" column="user_name" />
            <result property="content" column="content" />
            <result property="type" column="type" />
            <result property="resultCount" column="result_count" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="remark" column="remark" />
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,user_name,content, type,result_count,create_time,
        update_time,remark
    </sql>

    <insert id="insertQueryHistoryBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO query_history(user_id, content, type, result_count, create_time)
        VALUES
        <foreach collection="querys" item="query" separator=",">
            (#{query.userId}, #{query.content}, #{query.type}, #{query.resultCount}, sysdate())
        </foreach>
    </insert>
</mapper>
