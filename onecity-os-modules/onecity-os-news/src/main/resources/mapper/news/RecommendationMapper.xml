<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.news.modules.news.mapper.RecommendationMapper">

    <resultMap id="BaseResultMap" type="com.onecity.os.news.modules.news.entity.Recommendation">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="newsId" column="news_id"/>
        <result property="deriveTime" column="derive_time"/>
        <result property="displayed" column="displayed"/>
        <result property="feedback" column="feedback"/>
        <result property="deriveAlgorithm" column="derive_algorithm"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,news_id,derive_time,feedback,derive_algorithm
    </sql>
    <select id="queryFeedback" resultMap="BaseResultMap">
        SELECT
            id,user_id,news_id,derive_time,displayed,feedback,derive_algorithm
        from recommendations where user_id = #{userId} and news_id = #{newsId} and feedback = 0 and displayed = 1
    </select>

    <select id="queryByUserId" resultMap="BaseResultMap">
        SELECT
            id,user_id,news_id,derive_time,displayed,feedback,derive_algorithm
        from recommendations where user_id = #{userId}  and feedback = 0 and displayed = 0 ORDER BY news_id DESC
    </select>

    <update id="updateFeedback">
        UPDATE recommendations
        SET feedback = 1
        WHERE user_id = #{userId}
          and news_id = #{newsId}
          and feedback = 0
    </update>
    <update id="updateDisplayed">
        UPDATE recommendations
        SET displayed = 1
        WHERE user_id = #{userId}
          and displayed = 0
        AND news_id IN
        <foreach collection="newsIds" item="newsId" open="(" separator="," close=")">
            #{newsId}
        </foreach>
    </update>
</mapper>
