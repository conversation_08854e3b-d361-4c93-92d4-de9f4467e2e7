<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.news.modules.news.mapper.UserLogMapper">

    <insert id="insertUserLog" parameterType="com.onecity.os.news.modules.news.entity.UserLog"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO user_log(id, user_id, user_name, create_time,view_time, news_id, news_title,
                             news_source, news_type, news_type_name, operate, prefer_degree, status, remark)
        VALUES (#{user.id}, #{user.userId}, #{user.userName}, #{user.createTime}, #{user.viewTime}, #{user.newsId}, #{user.newsTitle},
                #{user.newsSource}, #{user.newsType}, #{user.newsTypeName}, #{user.operate}, #{user.preferDegree}, #{user.status},
                #{user.remark})
    </insert>

    <select id="queryByParam" resultType="com.onecity.os.news.modules.news.entity.UserLog">
        SELECT u.id,
        u.user_id AS userId,
        u.user_name AS userName,
        u.create_time AS createTime,
        u.view_time AS viewTime,
        u.news_id AS newsId,
        u.news_title AS newsTitle,
        u.news_source AS newsSource,
        u.news_type AS newsType,
        u.news_type_name AS newsTypeName,
        u.operate AS operate,
        u.prefer_degree AS preferDegree,
        u.remark AS remark
        FROM user_log AS u
        WHERE 1 = 1
        <if test="userName != null and userName !=''">
            AND INSTR(u.userName,#{userName})
        </if>
        <if test="newsSource != null and newsSource !=''">
            AND news_source = #{newsSource}
        </if>
        <if test="newsType != null and newsType !=''">
            AND news_type = #{newsType}
        </if>
        <if test="operate != null and operate !=''">
            AND operate = #{operate}
        </if>
    </select>

    <update id="delUserLog">
        UPDATE user_log
        SET status = "1"
        WHERE user_id = #{userId}
          AND news_id = #{newsId}
          AND operate = #{operate}
          AND status = '0'
    </update>

    <select id="queryOneByNewsId" resultType="com.onecity.os.news.modules.news.entity.UserLog">
        SELECT u.id,
        u.user_id AS userId,
        u.user_name AS userName,
        u.create_time AS createTime,
        u.view_time AS viewTime,
        u.news_id AS newsId,
        u.news_title AS newsTitle,
        u.news_source AS newsSource,
        u.news_type AS newsType,
        u.news_type_name AS newsTypeName,
        u.operate AS operate,
        u.prefer_degree AS preferDegree,
        u.remark AS remark
        FROM user_log AS u
        WHERE  u.user_id = #{userId}
        AND u.status = '0'
        AND u.news_id = #{newsId}
        order by u.create_time desc
        limit 1
    </select>
    <select id="queryByNewsId" resultType="com.onecity.os.news.modules.news.entity.UserLog">
        SELECT u.id,
        u.user_id AS userId,
        u.user_name AS userName,
        u.create_time AS createTime,
        u.view_time AS viewTime,
        u.news_id AS newsId,
        u.news_title AS newsTitle,
        u.news_source AS newsSource,
        u.news_type AS newsType,
        u.news_type_name AS newsTypeName,
        u.operate AS operate,
        u.prefer_degree AS preferDegree,
        u.remark AS remark
        FROM user_log AS u
        WHERE  u.user_id = #{userId}
        AND u.status = '0'
        AND u.news_id IN
        <foreach collection="newsIds" item="newsId" open="(" separator="," close=")">
            #{newsId}
        </foreach>
    </select>
</mapper>