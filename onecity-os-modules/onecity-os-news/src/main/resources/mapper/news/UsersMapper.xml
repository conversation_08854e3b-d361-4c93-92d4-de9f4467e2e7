<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.news.modules.news.mapper.UsersMapper">

    <resultMap id="BaseResultMap" type="com.onecity.os.news.modules.news.entity.User">
            <id property="id" column="id" />
            <result property="userId" column="user_id" />
            <result property="prefList" column="pref_list" />
            <result property="latestLogTime" column="latest_log_time" />
            <result property="name" column="name" />
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,pref_list,latest_log_time,name
    </sql>

    <insert id="insertUser" parameterType="com.onecity.os.news.modules.news.entity.User"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into users(user_id,pref_list,
        <if test="name != null and name != ''">name,</if>
        latest_log_time
        ) values (#{userId},#{prefList},
        <if test="name != null and name != ''">#{name,jdbcType=VARCHAR},</if>
        sysdate()
        )
    </insert>

    <select id="queryByUserId" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List"/> from users where user_id = #{userId}
    </select>

    <update id="updateUser">
        UPDATE users
        SET latest_log_time = sysdate()
        WHERE user_id = #{userId}
    </update>


</mapper>
