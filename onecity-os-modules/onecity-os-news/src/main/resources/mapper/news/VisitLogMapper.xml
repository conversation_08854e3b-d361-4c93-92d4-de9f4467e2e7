<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.news.modules.news.mapper.VisitLogMapper">

    <insert id="insertVistLog" parameterType="com.onecity.os.news.modules.news.entity.VisitLog"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO visit_log(id, user_id, user_name, visit_time, visit_start_time, visit_end_time, news_id, news_title,
                              news_source, news_type, news_type_name, news_tab, remark)
        VALUES (#{visit.id}, #{visit.userId}, #{visit.userName}, #{visit.visitTime}, #{visit.visitStartTime},
                #{visit.visitEndTime}, #{visit.newsId}, #{visit.newsTitle}, #{visit.newsSource}, #{visit.newsType},
                #{visit.newsTypeName}, #{visit.newsTab}, #{visit.remark})
    </insert>

</mapper>