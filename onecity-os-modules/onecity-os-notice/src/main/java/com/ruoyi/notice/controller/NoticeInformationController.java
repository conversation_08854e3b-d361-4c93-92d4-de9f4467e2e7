package com.ruoyi.notice.controller;

import java.io.*;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.constant.Constant;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.notice.domain.vo.NoticeInformationParam;
import com.ruoyi.notice.domain.vo.NoticeInformationVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdfparser.PDFParser;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.notice.domain.NoticeInformation;
import com.ruoyi.notice.service.INoticeInformationService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 通知公告信息Controller
 * 
 * <AUTHOR>
 * @date 2022-05-20
 */
@RestController
@RequestMapping("/notice")
@Slf4j
@Api(tags = "通知公告pc端api")
public class NoticeInformationController extends BaseController
{

    @Autowired
    private INoticeInformationService noticeInformationService;

    /**
     * 全部文件(普通文件,图片,)后缀 支持的类型
     */
    private static final String[] FILE_SUFFIX_SUPPORT = {".xlsx", ".xls", ".doc", ".docx", ".txt",
            ".jpg", ".jpeg", ".png", ".pdf"};
    /**
     * 问档文件pdf后缀 支持的类型
     */
    private static final String[] FILE_SUFFIX_PDF = {".pdf"};

    /**
     * 查询我发布的通知公告信息列表
     */
    @GetMapping("/publishList")
    @ApiOperation(value = "查询发布的通知公告信息列表（pc端）")
    public TableDataInfo publishList(NoticeInformationParam noticeInformationParam)
    {
        startPage();
        List<NoticeInformation> list = noticeInformationService.publishList(noticeInformationParam);
        return getDataTable(list, true);
    }

    /**
     * 查询我接收的通知公告信息列表
     */
    @GetMapping("/receiveList")
    @ApiOperation(value = "查询我接收的通知公告信息列表")
    public TableDataInfo receiveList(NoticeInformationParam noticeInformationParam)
    {
        startPage();
        List<NoticeInformation> list = noticeInformationService.receiveList(noticeInformationParam);
        return getDataTable(list);
    }

    /**
     * 新增保存通知公告信息
     */
    @PostMapping("/add")
    @ApiOperation(value = "发布（保存）通知公告")
    @Log(title="通知公告-新建通知",businessType = BusinessType.INSERT)
    public AjaxResult addSave(@RequestBody NoticeInformationVo noticeInformation)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return AjaxResult.error("为获取到当前用户，请联系管理员");
        }
        if (StringUtils.isNotEmpty(noticeInformation.getContent()) && noticeInformation.getContent().length() > Constant.CONTEST_MAX) {
            return error("无效格式内容过多，请减少后再操作");
        }
        noticeInformation.setUserId(loginUser.getUserid().toString());
        return toAjax(noticeInformationService.insertNoticeInformation(noticeInformation));
    }


    /**
     * 修改保存通知公告信息
     */
    @PostMapping("/edit")
    @ApiOperation(value = "编辑通知公告")
    @Log(title="通知公告-修改通知",businessType = BusinessType.UPDATE)
    public AjaxResult editSave(@RequestBody NoticeInformationVo noticeInformation)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return AjaxResult.error("为获取到当前用户，请联系管理员");
        }
        if (StringUtils.isNotEmpty(noticeInformation.getContent()) && noticeInformation.getContent().length() > Constant.CONTEST_MAX) {
            return error("无效格式内容过多，请减少后再操作");
        }
        noticeInformation.setUserId(loginUser.getUserid().toString());
        return toAjax(noticeInformationService.updateNoticeInformation(noticeInformation));
    }

    /**
     * 删除通知公告信息
     */
    @GetMapping( "/remove")
    @ApiOperation(value = "删除通知公告")
    @Log(title="通知公告-删除通知",businessType = BusinessType.DELETE)
    public AjaxResult remove(@RequestParam(name="id",required=true) Long id)
    {
        noticeInformationService.deleteNoticeInformationById(id);
        return AjaxResult.success("删除成功！");
    }

    /**
     * 发布通知公告信息
     */
    @GetMapping( "/publish")
    @ApiOperation(value = "发布通知公告")
    @Log(title="通知公告-发布通知",businessType = BusinessType.OTHER)
    public AjaxResult publish(@RequestParam(name="id",required=true) Long id)
    {
        noticeInformationService.publish(id);
        return AjaxResult.success("发布成功！");
    }

    /**
     * 撤回通知公告信息
     */
    @GetMapping( "/recall")
    @ApiOperation(value = "撤回通知公告")
    public AjaxResult recall(@RequestParam(name="id",required=true) Long id)
    {
        noticeInformationService.recall(id);
        return AjaxResult.success("撤回成功！");
    }

    /**
     * 获取通知公告详情
     */
    @GetMapping( "/getDetail")
    @ApiOperation(value = "获取通知公告详情")
    public AjaxResult getDetail(@RequestParam(name="id",required=true) Long id)
    {
        NoticeInformation information = noticeInformationService.selectNoticeInformationById(id);
        return AjaxResult.success(information, true);
    }

    /**
     * 已读通知公告信息
     */
    @GetMapping( "/readNotice")
    @ApiOperation(value = "已读某个通知公告")
    public AjaxResult readNotice(@RequestParam(name="id",required=true) Long id,@RequestParam(name="userName",required=true) String userName)
    {
        noticeInformationService.readNotice(id,userName);
        return AjaxResult.success("已读成功！");
    }

    @ApiOperation(value = "上传文件")
    @PostMapping("/uploadMatterFile")
    public AjaxResult uploadMatterFile(MultipartFile file) {
        //获取文件大小
        double fileSize = (double) file.getSize() / 1048576;
        // 校验文件名字
        String originalFilename = file.getOriginalFilename();
        String suffix = originalFilename.substring(originalFilename.lastIndexOf('.'));
        String fileName = originalFilename.substring(0,originalFilename.lastIndexOf('.'));
        log.info("上传文件大小为=" + fileSize + "M");
        //校验文件是否被修改后缀
        String fileSuffixName = suffix.toLowerCase(Locale.ROOT);
        log.info("文件后缀为："+ JSONObject.toJSONString(fileSuffixName));
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            byte[] byteArray = new byte[4];
            inputStream.read(byteArray, 0, 4);
            StringBuilder hex = new StringBuilder();
            for (byte b : byteArray) {
                hex.append(String.format("%02X", b));
            }
            //获取文件的魔数png魔数-89504e470d0a1a0a jpg魔数-
            switch (fileSuffixName){
                case ".png":
                    if(!"89504E47".equals(hex.toString())){
                        return AjaxResult.error("文件后缀名被修改");
                    }
                    break;
                case ".pdf":
                    if(!"25504446".equals(hex.toString())){
                        return AjaxResult.error("文件后缀名被修改");
                    }
                    try {
                        // 判断文件xss攻击
                        PDDocument doc = PDDocument.load(inputStream);
                        String CosName = doc.getDocument().getTrailer().toString();
                        if (CosName.contains("COSName{JavaScript}") || CosName.contains("COSName{JS}")) {
                            return AjaxResult.error("文件包含攻击脚本");
                        }
                    } catch (Exception e) {
                        log.error("PDF效验异常：" + e.getMessage());
                        return AjaxResult.error("文件包含攻击脚本");
                    }
                    break;
                case ".jpg":
                    log.info("进入jpg比对");
                    if(!"FFD8FFE0".equals(hex.toString())){
                        return AjaxResult.error("文件后缀名被修改");
                    }
                    break;
                default:
                    return AjaxResult.error("文件格式不支持,请更换后重试!");
            }
        }catch (IOException e){
            log.error("打开文件报错："+e.getMessage());
            return AjaxResult.error("文件内容出错,请更换后重试!");
        }finally {
            IOUtils.closeQuietly(inputStream);
        }

        if(fileSize > 20 ){
            return AjaxResult.error("上传文件最大为20M!");
        }
        if(!originalFilename.contains(".")){
            return AjaxResult.error("文件不能没有后缀!");
        }
        if(!Arrays.asList(FILE_SUFFIX_SUPPORT).contains(suffix.toLowerCase(Locale.ROOT))){
            return AjaxResult.error("文件格式不支持,请更换后重试!");
        }
        if(fileName.length() > 200){
            return AjaxResult.error("文件名长度不允许超过200，请更换后重试！");
        }
        return AjaxResult.success(noticeInformationService.uploadMatterFile(file));
    }

    @ApiOperation(value = "上传5m 大小pdf文件")
    @PostMapping("/uploadMatterFileForPdf")
    public AjaxResult uploadMatterFileForPdf(MultipartFile file) {
        //获取文件大小
        double fileSize = (double) file.getSize() / 1048576;
        // 校验文件名字
        String originalFilename = file.getOriginalFilename();
        String suffix = originalFilename.substring(originalFilename.lastIndexOf('.'));
        String fileName = originalFilename.substring(0, originalFilename.lastIndexOf('.'));
        log.info("上传文件大小为=" + fileSize + "M");
        if (fileSize > 5) {
            return AjaxResult.error("文件过大，请上传5M以内的文件");
        }
        if (!originalFilename.contains(".")) {
            return AjaxResult.error("文件不能没有后缀!");
        }
        if (!Arrays.asList(FILE_SUFFIX_PDF).contains(suffix.toLowerCase(Locale.ROOT))) {
            return AjaxResult.error("报告格式有误，请上传PDF格式文档");
        }
        if (fileName.length() > 200) {
            return AjaxResult.error("文件名长度不允许超过200，请更换后重试！");
        }
        //校验文件是否被修改后缀
        String fileSuffixName = suffix.toLowerCase(Locale.ROOT);
        log.info("文件后缀为："+JSONObject.toJSONString(fileSuffixName));
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            byte[] byteArray = new byte[4];
            inputStream.read(byteArray, 0, 4);
            StringBuilder hex = new StringBuilder();
            for (byte b : byteArray) {
                hex.append(String.format("%02X", b));
            }
            //获取文件的魔数png魔数-89504e470d0a1a0a jpg魔数-
            switch (fileSuffixName){
                case ".png":
                    if(!"89504E47".equals(hex.toString())){
                        return AjaxResult.error("文件后缀名被修改");
                    }
                    break;
                case ".pdf":
                    if(!"25504446".equals(hex.toString())){
                        return AjaxResult.error("文件后缀名被修改");
                    }
                    try {
                        // 判断文件xss攻击
                        PDDocument doc = PDDocument.load(inputStream);
                        String CosName = doc.getDocument().getTrailer().toString();
                        if (CosName.contains("COSName{JavaScript}") || CosName.contains("COSName{JS}")) {
                            return AjaxResult.error("文件包含攻击脚本");
                        }
                    } catch (Exception e) {
                        log.error("PDF效验异常：" + e.getMessage());
                        return AjaxResult.error("文件包含攻击脚本");
                    }
                    break;
                case ".jpg":
                    log.info("进入jpg比对");
                    if(!"FFD8FFE0".equals(hex.toString())){
                        return AjaxResult.error("文件后缀名被修改");
                    }
                    break;
                default:
                    return AjaxResult.error("文件格式不支持,请更换后重试!");
            }
        }catch (IOException e){
            log.error("打开文件报错："+e.getMessage());
            return AjaxResult.error("文件内容出错,请更换后重试!");
        }finally {
            IOUtils.closeQuietly(inputStream);
        }
        return AjaxResult.success(noticeInformationService.uploadMatterFile(file));
    }

    @ApiOperation(value = "下载文件")
    @GetMapping("/downloadMatterFile")
    public void downloadMatterFile(@ApiParam("文件路径")String fileUrl, @ApiParam("文件名称")String fileName, HttpServletResponse response) throws IOException {
        noticeInformationService.downloadMatterFile(fileUrl,fileName,response);
    }

    @ApiOperation(value = "删除文件")
    @GetMapping("/deleteMatterFile")
    public void deleteMatterFile(@ApiParam("文件路径")String fileUrl) {
        noticeInformationService.deleteMatterFile(fileUrl);
    }
}
