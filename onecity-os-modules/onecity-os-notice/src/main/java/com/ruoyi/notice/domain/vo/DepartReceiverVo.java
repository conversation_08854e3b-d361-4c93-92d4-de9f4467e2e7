package com.ruoyi.notice.domain.vo;

import com.ruoyi.notice.domain.NoticeReceiver;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 通知公告接收人信息对象 notice_receiver
 * 
 * <AUTHOR>
 * @date 2022-05-20
 */
@Data
@ApiModel("收人部门信息对象")
public class DepartReceiverVo
{
    @ApiModelProperty(value = "部门id")
    private String deptId;
    @ApiModelProperty(value = "部门名")
    private String deptName;
    @ApiModelProperty(value = "部门下人员列表")
    private List<NoticeReceiver> receivers;
}
