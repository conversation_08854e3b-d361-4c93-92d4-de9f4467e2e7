package com.ruoyi.notice.domain.vo;
import com.onecity.os.common.core.web.domain.BaseEntityWeb;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 通知公告信息
 *
 * <AUTHOR>
 * @date 2022-05-20
 */
@Data
@ApiModel("通知公告信息列表查询入参")
public class NoticeInformationParam extends BaseEntityWeb
{
    @ApiModelProperty(value = "公告标题")
    private String title;

    @ApiModelProperty(value = "日期 格式：yyyy-MM-dd")
    private String time;
    @ApiModelProperty(value = "创建人")
    private String userName;
}
