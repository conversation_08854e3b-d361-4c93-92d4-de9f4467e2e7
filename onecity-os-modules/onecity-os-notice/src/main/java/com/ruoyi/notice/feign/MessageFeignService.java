package com.ruoyi.notice.feign;

import com.onecity.os.common.core.domain.BaseResult;
import com.ruoyi.notice.domain.dto.RemindInfoDto;
import com.ruoyi.notice.feign.fallback.MessageFeignFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2022/5/30 15:56
 */
@FeignClient(name = "onecity-os-message", fallbackFactory = MessageFeignFallbackFactory.class, decode404 = true)
public interface MessageFeignService {

    @PostMapping("/remind/addMessage")
    BaseResult addMsg(@RequestBody List<RemindInfoDto> remindInfo);
}
