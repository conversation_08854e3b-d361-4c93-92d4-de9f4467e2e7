package com.ruoyi.notice.feign.fallback;

import com.onecity.os.common.core.domain.BaseResult;
import com.ruoyi.notice.domain.vo.NoticeMessageVO;
import com.ruoyi.notice.feign.CockpitFeignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class CockpitFeignServiceFallbackFactory implements FallbackFactory<CockpitFeignService> {
    @Override
    public CockpitFeignService create(Throwable cause) {
        return new CockpitFeignService() {
            @Override
            public BaseResult sendNoticeNote(NoticeMessageVO noticeMessageVO) {
                log.error("远程（填报系统）服务：发送通知公告通知异常！", cause);
                return BaseResult.fail("远程（填报系统）服务：发送通知公告通知异常！");
            }
        };
    }
}
