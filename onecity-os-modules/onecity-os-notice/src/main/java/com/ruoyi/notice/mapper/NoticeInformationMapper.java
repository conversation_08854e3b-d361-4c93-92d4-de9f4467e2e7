package com.ruoyi.notice.mapper;

import java.util.List;
import com.ruoyi.notice.domain.NoticeFiles;
import com.ruoyi.notice.domain.NoticeInformation;
import com.ruoyi.notice.domain.vo.AppNoticeInformationParam;
import com.ruoyi.notice.domain.vo.NoticeInformationParam;
import org.apache.ibatis.annotations.Mapper;

/**
 * 通知公告信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-05-20
 */
@Mapper
public interface NoticeInformationMapper
{
    /**
     * 查询通知公告信息
     * 
     * @param id 通知公告信息主键
     * @return 通知公告信息
     */
    public NoticeInformation selectNoticeInformationById(Long id);

    /**
     * 新增通知公告信息
     *
     * @param noticeInformation 通知公告信息
     * @return 结果
     */
    public int insertNoticeInformation(NoticeInformation noticeInformation);

    /**
     * 查询我发布的通知公告信息列表
     * @param noticeInformationParam
     * @return
     */
    List<NoticeInformation> publishList(NoticeInformationParam noticeInformationParam);

    /**
     * 查询我接收的通知公告信息列表
     * @param noticeInformationParam
     * @return
     */
    List<NoticeInformation> receiveList(NoticeInformationParam noticeInformationParam);
    List<NoticeInformation> appReceiveList(AppNoticeInformationParam appNoticeInformationParam);

    /**
     * 获取当前用户未读数量
     * @param userId
     * @return
     */
    Integer getNoReadCount(String userId);


    /**
     * 修改通知公告信息
     * 
     * @param noticeInformation 通知公告信息
     * @return 结果
     */
    public int updateNoticeInformation(NoticeInformation noticeInformation);

    /**
     * 删除通知公告信息
     * 
     * @param id 通知公告信息主键
     * @return 结果
     */
    public int deleteNoticeInformationById(String id);
}
