package com.ruoyi.notice.service;

import java.io.IOException;
import java.util.List;
import com.ruoyi.notice.domain.NoticeInformation;
import com.ruoyi.notice.domain.vo.AppNoticeInformationParam;
import com.ruoyi.notice.domain.vo.NoticeInformationParam;
import com.ruoyi.notice.domain.vo.NoticeInformationVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 通知公告信息Service接口
 * 
 * <AUTHOR>
 * @date 2022-05-20
 */
public interface INoticeInformationService 
{
    /**
     * 查询通知公告信息
     * 
     * @param id 通知公告信息主键
     * @return 通知公告信息
     */
    public NoticeInformation selectNoticeInformationById(Long id);
    NoticeInformation appSelectNoticeInformationById(Long id);

    /**
     * 查询我发布的通知公告信息列表
     * 
     * @param
     * @return 通知公告信息集合
     */
    public List<NoticeInformation> publishList(NoticeInformationParam noticeInformationParam);

    /**
     * 查询我接收的通知公告信息列表
     *
     * @param
     * @return 通知公告信息集合
     */
    public List<NoticeInformation> receiveList(NoticeInformationParam noticeInformationParam);
    List<NoticeInformation> appReceiveList(AppNoticeInformationParam appNoticeInformationParam);

    /**
     * 新增通知公告信息
     * 
     * @param noticeInformation 通知公告信息
     * @return 结果
     */
    public int insertNoticeInformation(NoticeInformationVo noticeInformation);

    /**
     * 修改通知公告信息
     * 
     * @param noticeInformation 通知公告信息
     * @return 结果
     */
    public int updateNoticeInformation(NoticeInformationVo noticeInformation);

    /**
     * 删除通知公告信息信息
     * 
     * @param id 通知公告信息主键
     * @return 结果
     */
    public int deleteNoticeInformationById(Long id);

    /**
     * 发布通知公告信息信息
     *
     * @param id 通知公告信息主键
     * @return 结果
     */
    public int publish(Long id);

    /**
     * 撤回通知公告信息信息
     *
     * @param id 通知公告信息主键
     * @return 结果
     */
    public int recall(Long id);

    /**
     * 撤回通知公告信息信息
     *
     * @param id 通知公告信息主键
     * @return 结果
     */
    public int readNotice(Long id,String userName);

    /**
     * 上传文件
     * @param file
     * @return
     */
    String uploadMatterFile(MultipartFile file);

    void downloadMatterFile(String fileUrl, String fileName, HttpServletResponse response) throws IOException;

    void deleteMatterFile(String fileUrl);

    Integer getNoReadCount(String userId);
}
