package com.ruoyi.notice.service.impl;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.onecity.os.common.core.utils.DateUtils;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.notice.domain.NoticeFiles;
import com.ruoyi.notice.domain.NoticeReceiver;
import com.ruoyi.notice.domain.dto.RemindInfoDto;
import com.ruoyi.notice.domain.vo.*;
import com.ruoyi.notice.feign.CockpitFeignService;
import com.ruoyi.notice.feign.MessageFeignService;
import com.ruoyi.notice.mapper.NoticeFilesMapper;
import com.ruoyi.notice.mapper.NoticeReceiverMapper;
import com.ruoyi.notice.utils.FastDFSClient;
import com.ruoyi.notice.utils.IdWorker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.ruoyi.notice.mapper.NoticeInformationMapper;
import com.ruoyi.notice.domain.NoticeInformation;
import com.ruoyi.notice.service.INoticeInformationService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

/**
 * 通知公告信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-05-20
 */
@Service
public class NoticeInformationServiceImpl implements INoticeInformationService 
{

    @Value("${app.baseMessageUrl}")
    private String baseMessageUrl;

    @Value("${app.noticeMessageUrl}")
    private String noticeMessageUrl;

    @Autowired
    private NoticeInformationMapper noticeInformationMapper;
    @Autowired
    private NoticeFilesMapper noticeFilesMapper;
    @Autowired
    private NoticeReceiverMapper noticeReceiverMapper;
    @Resource
    private IdWorker idWorker;
    @Resource
    private FastDFSClient fastDFSClient;
    @Resource
    private CockpitFeignService cockpitFeignService;
    @Resource
    private MessageFeignService messageFeignService;

    /**
     * 查询通知公告信息
     * 
     * @param id 通知公告信息主键
     * @return 通知公告信息
     */
    @Override
    public NoticeInformation selectNoticeInformationById(Long id)
    {
        NoticeInformation result = noticeInformationMapper.selectNoticeInformationById(id);
        /**查询附件列表*/
        List<NoticeFiles> fileList = noticeFilesMapper.getFilesListById(id);
        result.setFilesList(fileList);
        /**获取接收人列表*/
        //先获取部门列表
        List<DepartReceiverVo> departReceiverVoList = noticeReceiverMapper.getDepartListById(id);
        //获取人员列表
        List<NoticeReceiver> receivers =  noticeReceiverMapper.getListById(id);
        if(null != receivers){
            for(DepartReceiverVo departReceiverVo : departReceiverVoList){
                if(StringUtils.isEmpty(departReceiverVo.getDeptId())){
                    List<NoticeReceiver> children = receivers.stream().filter(re -> StringUtils.isEmpty(re.getReceiveDeptId())).collect(Collectors.toList());
                    departReceiverVo.setReceivers(children);
                }else {
                    List<NoticeReceiver> children = receivers.stream().filter(re -> departReceiverVo.getDeptId().equals(re.getReceiveDeptId())).collect(Collectors.toList());
                    departReceiverVo.setReceivers(children);
                }
            }
        }
        result.setDepartReceiverVoList(departReceiverVoList);
        return result;
    }

    @Override
    public NoticeInformation appSelectNoticeInformationById(Long id) {
        NoticeInformation result = noticeInformationMapper.selectNoticeInformationById(id);
        /**查询附件列表*/
        List<NoticeFiles> fileList = noticeFilesMapper.getFilesListById(id);
        result.setFilesList(fileList);
        return result;
    }

    /**
     * 查询我发布的通知公告信息列表
     *
     * @param
     * @return 通知公告信息
     */
    @Override
    public List<NoticeInformation> publishList(NoticeInformationParam noticeInformationParam)
    {
        List<NoticeInformation> result = noticeInformationMapper.publishList(noticeInformationParam);
        for(NoticeInformation noticeInformation : result){
            if(0 == noticeInformation.getStatus()){
                noticeInformation.setStatusStr("未发布");
                /**获取接收人列表*/
                List<DepartReceiverVo> departReceiverVoList = noticeReceiverMapper.getDepartListById(noticeInformation.getId());
                noticeInformation.setDepartReceiverVoList(departReceiverVoList);
            }else {
                noticeInformation.setStatusStr("已发布");
            }
        }
        return result;
    }

    /**
     * 查询我接收的通知公告信息列表
     *
     * @param
     * @return 通知公告信息
     */
    @Override
    public List<NoticeInformation> receiveList(NoticeInformationParam noticeInformationParam)
    {
        List<NoticeInformation> result = noticeInformationMapper.receiveList(noticeInformationParam);
        for(NoticeInformation noticeInformation : result){
            if(0 == noticeInformation.getIsRead()){
                noticeInformation.setStatusStr("未读");
            }else {
                noticeInformation.setStatusStr("已读");
            }
        }
        return result;
    }

    @Override
    public List<NoticeInformation> appReceiveList(AppNoticeInformationParam appNoticeInformationParam) {
        List<NoticeInformation> result = noticeInformationMapper.appReceiveList(appNoticeInformationParam);
        for(NoticeInformation noticeInformation : result){
            if(0 == noticeInformation.getIsRead()){
                noticeInformation.setStatusStr("未读");
            }else {
                noticeInformation.setStatusStr("已读");
            }
        }
        return result;
    }

    /**
     * 新增通知公告信息
     * 
     * @param noticeInformation 通知公告信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertNoticeInformation(NoticeInformationVo noticeInformation)
    {
        NoticeInformation information = new NoticeInformation();
        information.setTitle(noticeInformation.getTitle());
        information.setContent(noticeInformation.getContent());
        information.setSource(noticeInformation.getSource());
        information.setStatus(noticeInformation.getStatus());
        information.setIsDelete(0);
        if(1 == noticeInformation.getStatus()){
            information.setPublicTime(DateUtils.getNowDate());
            if(null != noticeInformation.getReceiverVoList() && noticeInformation.getReceiverVoList().size() > 0){
                //发送APP消息
                List<String> userIds = noticeInformation.getReceiverVoList().stream().map(ReceiverVo::getUserId).collect(Collectors.toList());
                LoginUser loginUser = SecurityUtils.getLoginUser();
                String userName = (null != loginUser) ? loginUser.getUsername() : "null";
                this.addMsg(userName, "通知公告 新通知发布", "您有新的通知--" + noticeInformation.getTitle() + "，请查收。", userIds);
            }
        }
        information.setCreateUserId(noticeInformation.getUserId());
        information.setCreateUserName(noticeInformation.getUserName());
        information.setCreateTime(DateUtils.getNowDate());
        information.setUpdateTime(DateUtils.getNowDate());
        /*生成id*/
        Long id = idWorker.nextId();
        information.setId(id);
        /**添加附件*/
        if(null != noticeInformation.getFilesList() && noticeInformation.getFilesList().size() > 0){
            addFile(id,noticeInformation.getFilesList());
        }
        /**添加接收人信息*/
        if(null != noticeInformation.getReceiverVoList() && noticeInformation.getReceiverVoList().size() > 0){
            addReceiver(id,noticeInformation.getTitle(),noticeInformation.getReceiverVoList());
        }
        return noticeInformationMapper.insertNoticeInformation(information);
    }

    private void sendMessage(String noticeTitle,List<String> userIds){
        StringBuilder content = new StringBuilder();
        content.append("【通知】").append(noticeTitle);
        NoticeMessageVO noticeMessageVO = new NoticeMessageVO();
        noticeMessageVO.setMessage(content.toString());
        noticeMessageVO.setUserIds(userIds);
        noticeMessageVO.setSource("通知公告");
        cockpitFeignService.sendNoticeNote(noticeMessageVO);
    }

    private void addFile(Long informationId,List<NoticeFiles> fileList){
        for(NoticeFiles file : fileList){
            file.setNoticeId(informationId);
        }
        noticeFilesMapper.insertFilesList(fileList);
    }
    private void addReceiver(Long informationId,String noticeTitle,List<ReceiverVo> receiverVoList){
        List<NoticeReceiver> noticeReceiverList = new ArrayList<>();
        for(ReceiverVo receiverVo : receiverVoList){
            NoticeReceiver noticeReceiver = new NoticeReceiver();
            noticeReceiver.setNoticeId(informationId);
            noticeReceiver.setNoticeTitle(noticeTitle);
            noticeReceiver.setIsRead(0);
            noticeReceiver.setReceiveUserId(receiverVo.getUserId());
            noticeReceiver.setReceiveUserName(receiverVo.getUserName());
            noticeReceiver.setRoleName(receiverVo.getRoleName());
            noticeReceiver.setReceiveDeptId(receiverVo.getDeptId());
            noticeReceiver.setReceiveDeptName(receiverVo.getDeptName());
            //暂时默认app端
            noticeReceiver.setUserSource("1");
            noticeReceiverList.add(noticeReceiver);
        }
        noticeReceiverMapper.insertList(noticeReceiverList);
    }

    /**
     * 修改通知公告信息
     * 
     * @param noticeInformation 通知公告信息
     * @return 结果
     */
    @Override
    public int updateNoticeInformation(NoticeInformationVo noticeInformation)
    {
        NoticeInformation information = new NoticeInformation();
        information.setId(noticeInformation.getId());
        information.setTitle(noticeInformation.getTitle());
        information.setContent(noticeInformation.getContent());
        information.setSource(noticeInformation.getSource());
        information.setStatus(noticeInformation.getStatus());
        if(1 == noticeInformation.getStatus()){
            information.setPublicTime(DateUtils.getNowDate());
        }
        information.setUpdateUserId(noticeInformation.getUserId());
        information.setUpdateUserName(noticeInformation.getUserName());
        information.setUpdateTime(DateUtils.getNowDate());

        /**删除之前的附件*/
        noticeFilesMapper.deleteFilesByNoticeId(noticeInformation.getId());
        /**删除之前的接收人信息*/
        noticeReceiverMapper.deleteByNoticeId(noticeInformation.getId());
        /**添加附件*/
        if(null != noticeInformation.getFilesList() && noticeInformation.getFilesList().size() > 0){
            addFile(noticeInformation.getId(),noticeInformation.getFilesList());
        }
        /**添加接收人信息*/
        if(null != noticeInformation.getReceiverVoList() && noticeInformation.getReceiverVoList().size() > 0){
            addReceiver(noticeInformation.getId(),noticeInformation.getTitle(),noticeInformation.getReceiverVoList());
        }
        return noticeInformationMapper.updateNoticeInformation(information);
    }

    /**
     * 删除通知公告信息信息
     * 
     * @param id 通知公告信息主键
     * @return 结果
     */
    @Override
    public int deleteNoticeInformationById(Long id)
    {
        NoticeInformation information = new NoticeInformation();
        information.setId(id);
        information.setIsDelete(1);
        return noticeInformationMapper.updateNoticeInformation(information);
    }

    @Override
    @Transactional
    public int publish(Long id) {
        NoticeInformation information = new NoticeInformation();
        information.setId(id);
        information.setStatus(1);
        information.setPublicTime(DateUtils.getNowDate());
        /**发送APP消息*/
        NoticeInformation result = noticeInformationMapper.selectNoticeInformationById(id);
        //获取人员
        List<NoticeReceiver> receivers =  noticeReceiverMapper.getListById(id);
        if(null != receivers && receivers.size() > 0 ){
            List<String> userIds = receivers.stream().map(NoticeReceiver::getReceiveUserId).collect(Collectors.toList());
            //发送APP消息
            LoginUser loginUser = SecurityUtils.getLoginUser();
            String userName = (null != loginUser) ? loginUser.getUsername() : "null";
            this.addMsg(userName,"通知公告 新通知发布", "您有新的通知--" + result.getTitle() + "，请查收。", userIds);
        }
        return noticeInformationMapper.updateNoticeInformation(information);
    }

    @Override
    @Transactional
    public int recall(Long id) {
        NoticeInformation information = new NoticeInformation();
        information.setId(id);
        information.setStatus(0);
        information.setPublicTime(null);
        //将已读改为未读
        noticeReceiverMapper.recallNoticeRead(id);
        return noticeInformationMapper.updateNoticeInformation(information);
    }

    @Override
    public int readNotice(Long id,String userId) {
        NoticeReceiver noticeReceiver = new NoticeReceiver();
        noticeReceiver.setNoticeId(id);
        noticeReceiver.setReceiveUserId(userId);
        noticeReceiver.setReadTime(DateUtils.getNowDate());
        return noticeReceiverMapper.readNotice(noticeReceiver);
    }

    @Override
    public String uploadMatterFile(MultipartFile file) {
        String result;
        try {
            result = fastDFSClient.uploadFile(file);
        } catch (IOException e) {
            e.printStackTrace();
            result = "上传失败";
        }
        return result;
    }

    @Override
    public void downloadMatterFile(String fileUrl, String fileName, HttpServletResponse response) throws IOException {
        byte[] bytes = fastDFSClient.download(fileUrl);
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setCharacterEncoding("UTF-8");
        ServletOutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            outputStream.write(bytes);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                outputStream.flush();
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void deleteMatterFile(String fileUrl){
        fastDFSClient.deleteFile(fileUrl);
    }

    @Override
    public Integer getNoReadCount(String userId) {
        return noticeInformationMapper.getNoReadCount(userId);
    }

    private void addMsg(String creatorName, String title, String content, List<String> userIds) {
        List<RemindInfoDto> remindList = new ArrayList();
        for(String userId:userIds) {
            RemindInfoDto remind = new RemindInfoDto();
            remind.setRemindTitle(title);
            remind.setIsRead((byte) 0);
            remind.setRemindedType((byte) 2);
            remind.setIsDelete((byte) 0);
            remind.setCreater(creatorName);
            remind.setCreateTime(DateUtils.dateTimeIntact());
            remind.setRemindContent(content);
            remind.setLevel("2");
            remind.setPcUserId(null);
//            StringBuilder appUserIds = new StringBuilder();
//            userIds.forEach(userId -> appUserIds.append(userId).append(","));
            //去掉最后一个,
//            String usrIds = appUserIds.substring(0, appUserIds.length() - 1);
            remind.setAppUserId(userId);
            remind.setSourceType("APP");
            remind.setMessageUrl(baseMessageUrl + noticeMessageUrl);
            remind.setAppMsgType("1");
            remindList.add(remind);
        }
        messageFeignService.addMsg(remindList);
    }
}
