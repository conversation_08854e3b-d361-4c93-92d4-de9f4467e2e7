<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.notice.mapper.NoticeReceiverMapper">

    <resultMap type="com.ruoyi.notice.domain.NoticeReceiver" id="NoticeReceiverResult">
        <result property="noticeId"    column="notice_id"    />
        <result property="noticeTitle"    column="notice_title"    />
        <result property="receiveUserId"    column="receive_user_id"    />
        <result property="receiveUserName"    column="receive_user_name"    />
        <result property="roleName"    column="role_name"    />
        <result property="receiveDeptId"    column="receive_dept_id"    />
        <result property="receiveDeptName"    column="receive_dept_name"    />
        <result property="isRead"    column="is_read"    />
        <result property="readTime"    column="read_time"    />
    </resultMap>

    <insert id="insertList" parameterType="com.ruoyi.notice.domain.NoticeReceiver">
        insert into notice_receiver
        (notice_id, notice_title, receive_user_id,receive_user_name,role_name,receive_dept_id,receive_dept_name,is_read,user_source)
        values
        <foreach collection="receiverList" item="item" separator=",">
            (#{item.noticeId},
            #{item.noticeTitle},
            #{item.receiveUserId},
            #{item.receiveUserName},
            #{item.roleName},
            #{item.receiveDeptId},
            #{item.receiveDeptName},
            #{item.isRead},
            #{item.userSource})
        </foreach>
    </insert>

    <select id="getListById" resultMap="NoticeReceiverResult">
        select *  from notice_receiver
        WHERE notice_id = #{id}
    </select>

    <delete id="deleteByNoticeId" parameterType="Long">
        delete from notice_receiver where notice_id = #{id}
    </delete>

    <select id="getDepartListById" resultType="com.ruoyi.notice.domain.vo.DepartReceiverVo">
        select IFNULL(receive_dept_id,"") as deptId,IFNULL(receive_dept_name,"") as deptName  from notice_receiver
        WHERE notice_id = #{id}
        group by receive_dept_id
    </select>

    <update id="readNotice" parameterType="com.ruoyi.notice.domain.NoticeReceiver">
        update notice_receiver
        set is_read = 1,read_time = #{readTime}
        where notice_id = #{noticeId} and receive_user_id = #{receiveUserId}
    </update>

    <update id="recallNoticeRead" parameterType="Long">
        update notice_receiver
        set is_read = 0
        where notice_id = #{noticeId}
    </update>
</mapper>