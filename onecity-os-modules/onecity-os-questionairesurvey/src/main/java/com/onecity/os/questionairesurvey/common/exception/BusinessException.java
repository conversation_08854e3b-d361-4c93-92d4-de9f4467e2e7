package com.onecity.os.questionairesurvey.common.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务异常
 * 
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BusinessException extends RuntimeException
{
    private static final long serialVersionUID = 1L;

    private String status;

    protected final String message;

    public BusinessException(String message)
    {
        this.message = message;
    }

    public BusinessException(String message, String status, Throwable e)
    {
        super(message, e);
        this.message = message;
        this.status = status;
    }

    @Override
    public String getMessage()
    {
        return message;
    }
}
