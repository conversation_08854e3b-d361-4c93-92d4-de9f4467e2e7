package com.onecity.os.questionairesurvey.controller;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.onecity.os.common.core.utils.poi.ExcelUtil;
import com.onecity.os.questionairesurvey.common.IdWorker;
import com.onecity.os.questionairesurvey.common.constant.SecurityConstants;
import com.onecity.os.questionairesurvey.common.domain.Result;
import com.onecity.os.questionairesurvey.feign.IndicatorFeignService;
import com.onecity.os.questionairesurvey.feign.MessageFeignService;
import com.onecity.os.questionairesurvey.feign.model.RemindInfo;
import com.onecity.os.questionairesurvey.feign.model.ToDo;
import com.onecity.os.questionairesurvey.mapper.ExamineLinkMapper;
import com.onecity.os.questionairesurvey.mapper.NoticeExamineMapper;
import com.onecity.os.questionairesurvey.model.ExamineLink;
import com.onecity.os.questionairesurvey.model.MessageConsumer;
import com.onecity.os.questionairesurvey.model.NoticeExamine;
import com.onecity.os.questionairesurvey.model.Questionnaire;
import com.onecity.os.questionairesurvey.model.po.AnswerPo;
import com.onecity.os.questionairesurvey.model.po.ExamineSearchResult;
import com.onecity.os.questionairesurvey.model.vo.*;
import com.onecity.os.questionairesurvey.service.QuestionnaireService;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecgframework.poi.excel.def.MapExcelConstants;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.jeecgframework.poi.excel.view.JeecgMapExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 问卷管理
 */
@Slf4j
@RestController
@Api(tags = "问卷管理")
@RequestMapping("/questionnaire")
public class QuestionnaireController {
    @Autowired
    private QuestionnaireService questionnaireService;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private ExamineLinkMapper examineLinkMapper;
    @Autowired
    private NoticeExamineMapper noticeExamineMapper;
    @Autowired
    private IdWorker idWorker;
    @Autowired
    private MessageFeignService messageFeignService;
    @Autowired
    private IndicatorFeignService indicatorFeignService;


    /**
     * 问卷列表查询
     * 权限处理完成
     *
     * @param page,size,questionnaire
     * @return
     */
    @ApiOperation(value = "问卷列表查询")
    @GetMapping("/getQuestionnaires")
    public Result getQuestionnaires(@RequestHeader(SecurityConstants.USER_ID_HEADER) String userId,
                                    @RequestHeader(SecurityConstants.USER_ID_ORG_HEADER) String orgId, Integer page, Integer size, Questionnaire questionnaire) {
        return Result.succeed(questionnaireService.findQuestionnaires(userId, orgId, page, size, questionnaire));
    }

    /**
     * 问卷模板查询
     *
     * @param page,size,questionnaire
     * @return
     */
    @ApiOperation(value = "问卷模板查询")
    @GetMapping("/getQuestionnairesModel")
    public Result getQuestionnairesModel(@RequestHeader(SecurityConstants.USER_ID_HEADER) String userId,
                                         @RequestHeader(SecurityConstants.USER_ID_ORG_HEADER) String orgId, Integer page, Integer size, Questionnaire questionnaire) {
        return Result.succeed(questionnaireService.findQuestionnairesModel(userId, orgId, page, size, questionnaire));
    }

    /**
     * 问卷新增 保存至草稿
     * 权限版本 完成
     *
     * @param questionnaireVo
     * @return
     */
    @ApiOperation(value = "问卷新增 保存至草稿")
    @Log(title = "问卷管理-新增问卷",businessType = BusinessType.INSERT)
    @PostMapping("/insertQuestionnaire")
    public Result insertQuestionnaire(@RequestHeader(SecurityConstants.USER_ID_HEADER) String userId,
                                      @RequestHeader(SecurityConstants.USER_ID_ORG_HEADER) String orgId,
                                      @RequestHeader(SecurityConstants.USER_ORG_HEADER) String orgName,
                                      @RequestBody QuestionnaireVo questionnaireVo) throws UnsupportedEncodingException {
//        username = URLDecoder.decode(username,"UTF-8");

        if (questionnaireVo.getLabelCategoryTreeIds() != null && questionnaireVo.getLabelCategoryTreeIds().size() != 0) {
            if ("".equals(questionnaireVo.getLabelCategoryTreeIds().get(0))) {
                questionnaireVo.setLabelCategoryTreeIds(Collections.singletonList(""));
            }
        }
        if (questionnaireVo.getCategorysIds() != null && questionnaireVo.getCategorysIds().size() != 0) {
            if ("".equals(questionnaireVo.getCategorysIds().get(0))) {
                questionnaireVo.setCategorysIds(Collections.singletonList(""));
            }
        }
        if (questionnaireVo.getFunctionIds() != null && questionnaireVo.getFunctionIds().size() != 0) {
            if ("-1".equals(questionnaireVo.getFunctionIds().get(0))) {
                questionnaireVo.setFunctionIds(Collections.singletonList("0"));
            }
        }
        questionnaireVo.setEndTime(new Date(questionnaireVo.getEndTime().getTime()+1000*60*60*23));

        orgName = URLDecoder.decode(orgName, "UTF-8");
        String username = questionnaireVo.getCreatePeople();
        if (questionnaireVo.getSubjectList() == null || questionnaireVo.getSubjectList().size() == 0) {
            return Result.failed("请输入题目信息");
        }
        // 1 草稿 2 待审核 11 模板草稿 12 新增模板
        String type = questionnaireVo.getStatus();
        questionnaireVo.setTitle(new String(Base64.getDecoder().decode(questionnaireVo.getTitle()), StandardCharsets.UTF_8));
        String s = questionnaireService.insertQuestionnaire(userId, orgId, orgName, username, questionnaireVo);
        switch (type) {
            case "1":
                return Result.succeed(s, "保存草稿成功");
            case "2":
                return Result.succeed(s, "此信息将会进入审核流程");
            default:
                return Result.succeed("失败");
        }
    }

    /**
     * 保存模板
     *
     * @param questionnaireVo
     * @return
     */
    @ApiOperation(value = "保存模板")
    @Log(title = "问卷模板管理-新增模板",businessType = BusinessType.INSERT)
    @PostMapping("/insertQuestionnaireModel")
    public Result insertQuestionnaireModel(@RequestHeader(SecurityConstants.USER_ID_HEADER) String userId,
                                           @RequestHeader(SecurityConstants.USER_ID_ORG_HEADER) String orgId,
                                           @RequestHeader(SecurityConstants.USER_ORG_HEADER) String orgName,
                                           @RequestBody QuestionnaireVo questionnaireVo) throws UnsupportedEncodingException {
        if (questionnaireVo.getLabelCategoryTreeIds() != null && questionnaireVo.getLabelCategoryTreeIds().size() != 0) {
            if ("".equals(questionnaireVo.getLabelCategoryTreeIds().get(0))) {
                questionnaireVo.setLabelCategoryTreeIds(Collections.singletonList(""));
            }
        }
        if (questionnaireVo.getCategorysIds() != null && questionnaireVo.getCategorysIds().size() != 0) {
            if ("".equals(questionnaireVo.getCategorysIds().get(0))) {
                questionnaireVo.setCategorysIds(Collections.singletonList(""));
            }
        }
        if (questionnaireVo.getFunctionIds() != null && questionnaireVo.getFunctionIds().size() != 0) {
            if ("-1".equals(questionnaireVo.getFunctionIds().get(0))) {
                questionnaireVo.setFunctionIds(Collections.singletonList("0"));
            }
        }
        questionnaireVo.setEndTime(new Date(questionnaireVo.getEndTime().getTime()+1000*60*60*23));
//        username = URLDecoder.decode(username,"UTF-8");
        orgName = URLDecoder.decode(orgName, "UTF-8");
        String username = questionnaireVo.getCreatePeople();
        // 1 草稿 2 待审核 11 模板草稿 12 新增模板
        String type = questionnaireVo.getStatus();
        questionnaireVo.setTitle(new String(Base64.getDecoder().decode(questionnaireVo.getTitle()), StandardCharsets.UTF_8));
        String s = questionnaireService.insertQuestionnaireModel(userId, username, orgId, orgName, questionnaireVo);
        switch (type) {
            case "1":
                return Result.succeed(s, "保存草稿成功");
            case "2":
                return Result.succeed(s, "此信息将会进入审核流程");
            case "11":
                return Result.succeed(s, "保存模板草稿成功");
            case "12":
                return Result.succeed(s, "新增模板成功");
            default:
                return Result.succeed("失败");
        }
    }

    /**
     * 回显问卷
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "回显问卷")
    @GetMapping("/getQuestionnaire")
    public Result getQuestionnaires(String id) {
        return Result.succeed(questionnaireService.findQuestionnaire(id));
    }

    /**
     * 回显模板
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "回显问卷模板")
    @GetMapping("/getQuestionnaireModel")
    public Result getQuestionnaireModel(String id) {
        return Result.succeed(questionnaireService.findQuestionnaireModel(id));
    }

    /**
     * 删除问卷
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "删除问卷")
    @GetMapping("/deleteQuestionnaire")
    @Log(title = "问卷管理-删除问卷",businessType = BusinessType.DELETE)
    public Result deleteQuestionnaire(String id) {
        Questionnaire questionnaire = questionnaireService.getById(id);
        if (questionnaire.getStatus().equals("3") || questionnaire.getStatus().equals("4") || questionnaire.getStatus().equals("5")) {
            // type 作用
            Query query = Query.query(Criteria.where("noticeId").is(id).and("type").is(1));
            mongoTemplate.remove(query, MessageConsumer.class);
        }
        questionnaireService.deleteQuestionnaire(id);
        return Result.succeed("问卷已删除");
    }

    /**
     * 删除模板
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "删除问卷模板")
    @GetMapping("/deleteQuestionnaireModel")
    @Log(title = "问卷模板管理-删除问卷模板",businessType = BusinessType.DELETE)
    public Result deleteQuestionnaireModel(String id) {
        questionnaireService.deleteQuestionnaireModel(id);
        return Result.succeed("问卷模板已删除");
    }

    /**
     * 编辑问卷 草稿 模板 模板草稿
     *
     * @param questionnaireVo
     * @return
     */
    @ApiOperation(value = "编辑问卷 保存至草稿 保存模板")
    @PostMapping("/editQuestionnaire")
    @Log(title = "问卷管理-编辑问卷",businessType = BusinessType.UPDATE)
    public Result editQuestionnaire(@RequestHeader(SecurityConstants.USER_ID_HEADER) String userId,
                                    @RequestHeader(SecurityConstants.USER_ID_ORG_HEADER) String orgId,
                                    @RequestHeader(SecurityConstants.USER_ORG_HEADER) String orgName,
                                    @RequestBody QuestionnaireVo questionnaireVo) throws UnsupportedEncodingException {
        if (questionnaireVo.getLabelCategoryTreeIds() != null && questionnaireVo.getLabelCategoryTreeIds().size() != 0) {
            if ("".equals(questionnaireVo.getLabelCategoryTreeIds().get(0))) {
                questionnaireVo.setLabelCategoryTreeIds(Collections.singletonList(""));
            }
        }
        if (questionnaireVo.getCategorysIds() != null && questionnaireVo.getCategorysIds().size() != 0) {
            if ("".equals(questionnaireVo.getCategorysIds().get(0))) {
                questionnaireVo.setCategorysIds(Collections.singletonList(""));
            }
        }
        if (questionnaireVo.getFunctionIds() != null && questionnaireVo.getFunctionIds().size() != 0) {
            if ("-1".equals(questionnaireVo.getFunctionIds().get(0))) {
                questionnaireVo.setFunctionIds(Collections.singletonList("0"));
            }
        }
        questionnaireVo.setEndTime(new Date(questionnaireVo.getEndTime().getTime()+1000*60*60*23));
//        username = URLDecoder.decode(username,"UTF-8");
        orgName = URLDecoder.decode(orgName, "UTF-8");
        String username = questionnaireVo.getCreatePeople();
        if (questionnaireVo.getSubjectList() == null || questionnaireVo.getSubjectList().size() == 0) {
            return Result.failed("请输入题目信息");
        }
        // 1 草稿 2 待审核 11 模板草稿 12 新增模板
        String type = questionnaireVo.getStatus();
        questionnaireVo.setTitle(new String(Base64.getDecoder().decode(questionnaireVo.getTitle()), StandardCharsets.UTF_8));
        questionnaireService.editQuestionnaire(userId, orgId, orgName, username, questionnaireVo);
        switch (type) {
            case "1":
                return Result.succeed("保存草稿成功");
            case "2":
                return Result.succeed("此信息将会进入审核流程");
            case "11":
                return Result.succeed("保存模板草稿成功");
            case "12":
                return Result.succeed("修改模板成功");
            default:
                return Result.succeed("失败");
        }
    }

    /**
     * 编辑模板草稿
     *
     * @param questionnaireVo
     * @return
     */
    @ApiOperation(value = "编辑模板草稿")
    @PostMapping("/editQuestionnaireModel")
    @Log(title = "问卷模板管理-编辑问卷模板",businessType = BusinessType.UPDATE)
    public Result editQuestionnaireModel(@RequestHeader(SecurityConstants.USER_ID_HEADER) String userId,
                                         @RequestHeader(SecurityConstants.USER_ID_ORG_HEADER) String orgId,
                                         @RequestHeader(SecurityConstants.USER_ORG_HEADER) String orgName,
                                         @RequestBody QuestionnaireVo questionnaireVo) throws UnsupportedEncodingException {
        if (questionnaireVo.getLabelCategoryTreeIds() != null && questionnaireVo.getLabelCategoryTreeIds().size() != 0) {
            if ("".equals(questionnaireVo.getLabelCategoryTreeIds().get(0))) {
                questionnaireVo.setLabelCategoryTreeIds(Collections.singletonList(""));
            }
        }
        if (questionnaireVo.getCategorysIds() != null && questionnaireVo.getCategorysIds().size() != 0) {
            if ("".equals(questionnaireVo.getCategorysIds().get(0))) {
                questionnaireVo.setCategorysIds(Collections.singletonList(""));
            }
        }
        if (questionnaireVo.getFunctionIds() != null && questionnaireVo.getFunctionIds().size() != 0) {
            if ("-1".equals(questionnaireVo.getFunctionIds().get(0))) {
                questionnaireVo.setFunctionIds(Collections.singletonList("0"));
            }
        }
        questionnaireVo.setEndTime(new Date(questionnaireVo.getEndTime().getTime()+1000*60*60*23));
//        username = URLDecoder.decode(username,"UTF-8");
        orgName = URLDecoder.decode(orgName, "UTF-8");
        String username = questionnaireVo.getCreatePeople();
        // 1 草稿 2 待审核 11 模板草稿 12 新增模板
        String type = questionnaireVo.getStatus();
        questionnaireVo.setTitle(new String(Base64.getDecoder().decode(questionnaireVo.getTitle()), StandardCharsets.UTF_8));
        questionnaireService.editQuestionnaireModel(userId, username, orgId, orgName, questionnaireVo);
        switch (type) {
            case "1":
                return Result.succeed("保存草稿成功");
            case "2":
                return Result.succeed("此信息将会进入审核流程");
            case "11":
                return Result.succeed("保存模板草稿成功");
            case "12":
                return Result.succeed("修改模板成功");
            default:
                return Result.succeed("失败");
        }
    }

    /**
     * 交批
     * 权限完成
     *
     * @param
     * @return
     */
    @ApiOperation(value = "交批")
    @PostMapping("/submitQuestionnairePro")
    public Result submitQuestionnairePro(@RequestHeader(SecurityConstants.USER_ID_HEADER) String userId,@RequestBody JSONObject jsonObject) {
        // 1 草稿 2 待审核 11 模板草稿 12 新增模板
        Questionnaire byId = questionnaireService.getById(jsonObject.getStr("id"));
        byId.setStatus("2");
        // 更新 创建时间
        byId.setCreateTime(new Date());
        byId.setApproverId(jsonObject.getStr("approverId"));
        // 开发审核链功能
        NoticeExamine noticeExamine = new NoticeExamine();
        String s = UUID.randomUUID().toString();
        noticeExamine.setId(s);
        noticeExamine.setApproverId(jsonObject.getStr("approverId"));
        noticeExamine.setCreateTime(new Date());
        noticeExamine.setBeforeId(byId.getCreatePeopleId());
        noticeExamine.setBeforeName(byId.getCreatePeople());
        noticeExamine.setNoticeCreaterOrgId(byId.getCreatePeopleOrgId());
        noticeExamine.setBeforeOrgId(byId.getCreatePeopleOrgId());
        noticeExamine.setNoticeCreaterId(byId.getCreatePeopleId());
        noticeExamine.setNoticeId(byId.getId());
        noticeExamine.setNoticeCreater(byId.getCreatePeople());
        noticeExamine.setNoticeTitle(byId.getTitle());
        noticeExamineMapper.insert(noticeExamine);
        // 将交批行为加入待办
        ExamineLink link = new ExamineLink();
        link.setAction("交批");
        link.setId(UUID.randomUUID().toString());
        link.setDealId(byId.getCreatePeopleId());
        link.setDealOrgName(byId.getCreateDepart());
        link.setDealName(byId.getCreatePeople());
        link.setMark(jsonObject.getStr("mark"));
        link.setDealTime(new Date());
        link.setExamineId(s);
        link.setNoticeId(byId.getId());
//        link.setStatus("已办");
        examineLinkMapper.insert(link);
        // 链
        ExamineLink examineLink = new ExamineLink();
        examineLink.setAction("待办");
        examineLink.setId(UUID.randomUUID().toString());
        examineLink.setDealId(jsonObject.getStr("approverId"));
        examineLink.setDealOrgName(jsonObject.getStr("approverOrgName"));
        examineLink.setDealName(jsonObject.getStr("approverName"));
        examineLink.setExamineId(s);
        examineLink.setNoticeId(byId.getId());
        examineLink.setStatus("待办");
        examineLinkMapper.insert(examineLink);
        questionnaireService.updateById(byId);
        List<ToDo> vo = new ArrayList<>();
        ToDo toDoVo = new ToDo();
        toDoVo.setId(idWorker.nextId());
        toDoVo.setQuestionnaireId(byId.getId());
        toDoVo.setCreateTime(new Date());
        toDoVo.setTitle("【调查问卷】待审核\r\n您有新的问卷需要审核，请尽快处理。");
        toDoVo.setToDoUserId(jsonObject.getStr("approverId"));
        toDoVo.setQuestionnaireJump("3");
        toDoVo.setToDoSource("2");
        toDoVo.setToDoUserName(jsonObject.getStr("approverName"));
        toDoVo.setIsDelete(new Byte("0"));
        vo.add(toDoVo);
        indicatorFeignService.addToDoBatch(vo);
        //删除待办
        indicatorFeignService.deleteToDoByUserIdAndQuestionnaireId(byId.getId(),userId);
        return Result.succeed("此信息将会进入审核流程");
    }


    @PostMapping("/examineService")
    @ApiOperation(value = "审核相关操作性业务")
    public Result examineService(@RequestHeader(SecurityConstants.USER_ID_HEADER) String userId,
                                 @RequestHeader(SecurityConstants.USER_HEADER) String dealUser,
                                 @RequestHeader(SecurityConstants.USER_ORG_HEADER) String orgName,
                                 @RequestHeader(SecurityConstants.USER_ID_ORG_HEADER) String orgId, @RequestBody JSONObject jsonObject) throws UnsupportedEncodingException {
        String username = jsonObject.getStr("createPeople");
        orgName = URLDecoder.decode(orgName, "UTF-8");
        String id = jsonObject.getStr("id");
        NoticeExamine noticeExamine = noticeExamineMapper.selectById(id);

        Questionnaire quest = questionnaireService.getById(noticeExamine.getNoticeId());
        Questionnaire questionnaire = new Questionnaire();
        String questionnaireId = quest.getId();
        questionnaire.setId(questionnaireId);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (jsonObject.getStr("action").equals("通过")) {
            //删除待办
            indicatorFeignService.deleteToDoByUserIdAndQuestionnaireId(questionnaireId,userId);
            String[] strings = quest.getCategorysIds().split(",");
            String[] usernameList = quest.getLabelCategoryTreeIds().split(",");
            List<ToDo> vo = new ArrayList<>();
            for (int i = 0; i < strings.length; i++) {
                ToDo toDoVo = new ToDo();
                toDoVo.setId(idWorker.nextId());
                toDoVo.setQuestionnaireId(quest.getId());
                toDoVo.setCreateTime(new Date());
                toDoVo.setTitle("【调查问卷】待提交\r\n您有新的问卷待提交，请尽快提交。");
                toDoVo.setToDoUserId(strings[i]);
                toDoVo.setQuestionnaireJump("2");
                toDoVo.setToDoSource("2");
                toDoVo.setToDoUserName(usernameList[i]);
                toDoVo.setIsDelete(new Byte("0"));
                vo.add(toDoVo);
            }
            indicatorFeignService.addToDoBatch(vo);
            // 时间处理
            Date startTime = quest.getStartTime();
            Date endTime = quest.getEndTime();
            String cycleTime = quest.getCycleTime();
            SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String format = dateFormat1.format(endTime);
            if (cycleTime != null && !"".equals(cycleTime)) {
                format = format + " " + cycleTime + ":00";
            } else {
                format = format + " 23:50:00";
            }
            Date parse = null;
            try {
                parse = dateFormat2.parse(format);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            // 未发布状态
            if (new Date().getTime() < startTime.getTime()) {
                questionnaire.setStatus("3");
                // 直接发布
            } else if (new Date().getTime() >= startTime.getTime() && new Date().getTime() <= Objects.requireNonNull(parse).getTime()) {
                String[] split = quest.getCategorysIds().split(",");
                String[] departmentList = quest.getFunctionIds().split(",");
                List<MessageConsumer> msgList = new LinkedList<>();
                for (int i = 0; i < split.length; i++) {
                    MessageConsumer messageConsumer1 = new MessageConsumer();
                    messageConsumer1.setId(String.valueOf(idWorker.nextId()));
                    messageConsumer1.setType(1);
                    messageConsumer1.setUserId(split[i]);
                    messageConsumer1.setUserName(usernameList[i]);
                    messageConsumer1.setOrganizationName(departmentList[i]);
                    messageConsumer1.setMessage("1");
                    messageConsumer1.setPlatformType(0);
                    messageConsumer1.setMillis(System.currentTimeMillis());
                    messageConsumer1.setCreateTime(dateFormat.format(new Date()));
                    messageConsumer1.setIsClose(0);
                    messageConsumer1.setNoticeIsRead(0);
                    messageConsumer1.setNoticeId(quest.getId());
                    if (messageConsumer1.getNoticeIsRemind() == null){
                        messageConsumer1.setNoticeIsRemind(0);
                    }
                    msgList.add(messageConsumer1);
                }
                mongoTemplate.insertAll(msgList);
                questionnaire.setStatus("4");
            } else {
                questionnaire.setStatus("5");
            }
            noticeExamine.setBeforeId(userId);
            noticeExamine.setBeforeName(username);
            noticeExamine.setBeforeOrgId(orgId);
            // 审核链
            ExamineLink examineLink = new ExamineLink();
            examineLink.setAction("发布");
            examineLink.setDealId(userId);
            examineLink.setDealName(username);
            examineLink.setDealOrgId(orgId);
            examineLink.setDealOrgName(orgName);
            examineLink.setExamineId(id);
            examineLink.setMark(jsonObject.getStr("mark"));
            examineLink.setNoticeId(quest.getId());
            examineLink.setDealTime(new Date());
            examineLink.setStatus("已办");

            ExamineLink query = new ExamineLink();
            query.setDealId(userId);
            query.setExamineId(id);
            query.setNoticeId(quest.getId());
            query.setStatus("待办");
            QueryWrapper<ExamineLink> examineLinkQueryWrapper = new QueryWrapper<>(query);

            examineLinkMapper.update(examineLink, examineLinkQueryWrapper);
            questionnaire.setQuestionnairePublisher(username);
            questionnaire.setQuestionnairePublishDepartment(orgName);


        } else if (jsonObject.getStr("action").equals("转发")) {
            //消息提醒
            List<ToDo> vo = new ArrayList<>();
            ToDo toDoVo = new ToDo();
            toDoVo.setId(idWorker.nextId());
            toDoVo.setQuestionnaireId(quest.getId());
            toDoVo.setCreateTime(new Date());
            toDoVo.setTitle("【调查问卷】待审核\r\n您有新的问卷需要审核，请尽快处理。");
            toDoVo.setToDoUserId(jsonObject.getStr("approverId"));
            toDoVo.setQuestionnaireJump("3");
            toDoVo.setToDoSource("2");
            toDoVo.setToDoUserName(jsonObject.getStr("approverName"));
            toDoVo.setIsDelete(new Byte("0"));
            vo.add(toDoVo);
            indicatorFeignService.addToDoBatch(vo);
            //删除待办
            indicatorFeignService.deleteToDoByUserIdAndQuestionnaireId(questionnaireId,userId);

            questionnaire.setStatus("2");
            noticeExamine.setBeforeId(userId);
            noticeExamine.setBeforeName(URLDecoder.decode(dealUser,"UTF-8"));
            noticeExamine.setBeforeOrgId(orgId);
            // 审核链
            ExamineLink examineLink = new ExamineLink();
            examineLink.setAction("转发");
            examineLink.setDealId(userId);
            examineLink.setDealName(username);
            examineLink.setDealOrgId(orgId);
            examineLink.setDealOrgName(orgName);
            examineLink.setExamineId(id);
            examineLink.setMark(jsonObject.getStr("mark"));
            examineLink.setNoticeId(quest.getId());
            examineLink.setDealTime(new Date());
            examineLink.setStatus("已办");

            ExamineLink query = new ExamineLink();
            query.setDealId(userId);
            query.setExamineId(id);
            query.setNoticeId(quest.getId());
            query.setStatus("待办");
            QueryWrapper<ExamineLink> examineLinkQueryWrapper = new QueryWrapper<>(query);
            examineLinkMapper.update(examineLink, examineLinkQueryWrapper);

            ExamineLink newExam = new ExamineLink();
            newExam.setId(UUID.randomUUID().toString());
            newExam.setDealId(jsonObject.getStr("approverId"));
            newExam.setDealName(jsonObject.getStr("approverName"));
            newExam.setDealOrgName(jsonObject.getStr("approverOrgName"));
            newExam.setExamineId(id);
            newExam.setNoticeId(quest.getId());
            newExam.setStatus("待办");
            newExam.setAction("待办");
            examineLinkMapper.insert(newExam);
        } else {
            //驳回操作
            //发送待办
            List<ToDo> vo = new ArrayList<>();
            ToDo toDoVo = new ToDo();
            toDoVo.setId(idWorker.nextId());
            toDoVo.setQuestionnaireId(quest.getId());
            toDoVo.setCreateTime(new Date());
            toDoVo.setTitle("【调查问卷】被驳回\r\n您发起的问卷被驳回，请调整后再提交。");
            toDoVo.setToDoUserId(noticeExamine.getBeforeId());
            toDoVo.setQuestionnaireJump("1");
            toDoVo.setToDoSource("2");
            toDoVo.setToDoUserName(jsonObject.getStr("approverName"));
            toDoVo.setIsDelete(new Byte("0"));
            vo.add(toDoVo);
            indicatorFeignService.addToDoBatch(vo);
            //删除待办
            indicatorFeignService.deleteToDoByUserIdAndQuestionnaireId(questionnaireId,userId);

            questionnaire.setStatus("6");
            noticeExamine.setBeforeId(userId);
            noticeExamine.setBeforeName(username);
            noticeExamine.setBeforeOrgId(orgId);
            // 审核链
            ExamineLink examineLink = new ExamineLink();
            examineLink.setAction("驳回");
            examineLink.setDealId(userId);
            examineLink.setDealName(username);
            examineLink.setDealOrgId(orgId);
            examineLink.setDealOrgName(orgName);
            examineLink.setExamineId(id);
            examineLink.setMark(jsonObject.getStr("mark"));
            examineLink.setNoticeId(quest.getId());
            examineLink.setDealTime(new Date());
            examineLink.setStatus("已办");

            ExamineLink query = new ExamineLink();
            query.setDealId(userId);
            query.setExamineId(id);
            query.setNoticeId(quest.getId());
            query.setStatus("待办");
            QueryWrapper<ExamineLink> examineLinkQueryWrapper = new QueryWrapper<>(query);
            examineLinkMapper.update(examineLink, examineLinkQueryWrapper);

        }
        questionnaire.setCreateTime(new Date());
        noticeExamineMapper.updateById(noticeExamine);
        if (questionnaireService.updateById(questionnaire)) {
            return Result.succeed("操作成功");
        }
        return Result.failed("操作失败！");
    }

    /*审核轨迹*/
    @GetMapping("/showExamineLink")
    @ApiOperation(value = "审核轨迹")
    public Result showExamineLink(String noticeId) {
        ExamineLink examineLink = new ExamineLink();
        examineLink.setNoticeId(noticeId);
        QueryWrapper<ExamineLink> examineLinkQueryWrapper = new QueryWrapper<>(examineLink);
        examineLinkQueryWrapper.isNotNull("action");
        examineLinkQueryWrapper.last("ORDER BY ISNULL(deal_time),deal_time asc");
        List<ExamineLink> examineLinks = examineLinkMapper.selectList(examineLinkQueryWrapper);
        return Result.succeed(examineLinks);
    }

    /*审核展示*/
    @PostMapping("/showExamine")
    @ApiOperation(value = "审核展示")
    public Result showExamine(@RequestHeader(SecurityConstants.USER_ID_HEADER) String userId,
                              @RequestBody ExamineSearchVo examineSearchVo) {
        if ("已办".equals(examineSearchVo.getType())) {
            ExamineLink examineLink = new ExamineLink();
            examineLink.setDealId(userId);
            examineLink.setStatus("已办");
            QueryWrapper<ExamineLink> examineLinkQueryWrapper = new QueryWrapper<>(examineLink);
            List<ExamineLink> examineLinks = examineLinkMapper.selectList(examineLinkQueryWrapper);
            ArrayList<NoticeExamine> noticeExamines = new ArrayList<>();
            for (ExamineLink link : examineLinks) {
                NoticeExamine noticeExamine = noticeExamineMapper.selectById(link.getExamineId());
                String noticeId = noticeExamine.getNoticeId();
                Questionnaire byId = questionnaireService.getById(noticeId);
                if (byId == null) continue;
                noticeExamine.setNoticeStatus(byId.getStatus());
                noticeExamines.add(noticeExamine);
            }
            for (int i = 0; i < noticeExamines.size(); i++) {
                NoticeExamine noticeExamine = noticeExamines.get(i);
                if (examineSearchVo.getTitle() != null) {
                    if (!noticeExamine.getNoticeTitle().contains(examineSearchVo.getTitle())) {
                        noticeExamines.remove(i);
                        i--;
                        continue;
                    }
                }
                if (examineSearchVo.getName() != null) {
                    if (!noticeExamine.getNoticeCreater().contains(examineSearchVo.getName())) {
                        noticeExamines.remove(i);
                        i--;
                        continue;
                    }
                }
                if (examineSearchVo.getStartTime() != null && examineSearchVo.getEndTime() != null) {
                    if (examineSearchVo.getStartTime().getTime() >= noticeExamine.getCreateTime().getTime()) {
                        noticeExamines.remove(i);
                        i--;
                        continue;
                    }
                    if ((examineSearchVo.getEndTime().getTime() + 1000 * 60 * 60 * 24) <= noticeExamine.getCreateTime().getTime()) {
                        noticeExamines.remove(i);
                        i--;
                    }
                } else if (examineSearchVo.getStartTime() != null) {
                    if (examineSearchVo.getStartTime().getTime() >= noticeExamine.getCreateTime().getTime()) {
                        noticeExamines.remove(i);
                        i--;
                    }
                } else if (examineSearchVo.getEndTime() != null) {
                    if ((examineSearchVo.getEndTime().getTime() + 1000 * 60 * 60 * 24) <= noticeExamine.getCreateTime().getTime()) {
                        noticeExamines.remove(i);
                        i--;
                    }
                }
            }
            noticeExamines = noticeExamines.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(NoticeExamine::getId))), ArrayList::new));
            noticeExamines = (ArrayList<NoticeExamine>) noticeExamines.stream().sorted(Comparator.comparing(NoticeExamine::getCreateTime)).collect(Collectors.toList());
            //Collections.reverse(noticeExamines);
            int startNum = (examineSearchVo.getPage() - 1) * examineSearchVo.getSize();
            int endNum;
            int total = noticeExamines.size();
            if (examineSearchVo.getPage() * examineSearchVo.getSize() < noticeExamines.size()) {
                endNum = examineSearchVo.getPage() * examineSearchVo.getSize();
            } else {
                endNum = total;
            }
            ExamineSearchResult examineSearchResult = new ExamineSearchResult();
            examineSearchResult.setTotalNum(noticeExamines.size());
            examineSearchResult.setNoticeExamineList(noticeExamines.subList(startNum, endNum));
            return Result.succeed(examineSearchResult);
        } else {
            ExamineLink examineLink = new ExamineLink();
            examineLink.setDealId(userId);
            examineLink.setStatus("待办");
            QueryWrapper<ExamineLink> examineLinkQueryWrapper = new QueryWrapper<>(examineLink);
            List<ExamineLink> examineLinks = examineLinkMapper.selectList(examineLinkQueryWrapper);
            ArrayList<NoticeExamine> noticeExamines = new ArrayList<>();
            for (ExamineLink link : examineLinks) {
                NoticeExamine noticeExamine = noticeExamineMapper.selectById(link.getExamineId());
                String noticeId = noticeExamine.getNoticeId();
                Questionnaire byId = questionnaireService.getById(noticeId);
                if (byId == null) continue;
                noticeExamine.setNoticeStatus(byId.getStatus());
                noticeExamines.add(noticeExamine);
            }
            for (int i = 0; i < noticeExamines.size(); i++) {
                NoticeExamine noticeExamine = noticeExamines.get(i);
                if (examineSearchVo.getTitle() != null) {
                    if (!noticeExamine.getNoticeTitle().contains(examineSearchVo.getTitle())) {
                        noticeExamines.remove(i);
                        i--;
                        continue;
                    }
                }
                if (examineSearchVo.getName() != null) {
                    if (!noticeExamine.getNoticeCreater().contains(examineSearchVo.getName())) {
                        noticeExamines.remove(i);
                        i--;
                        continue;
                    }
                }
                if (examineSearchVo.getStartTime() != null && examineSearchVo.getEndTime() != null) {
                    if (examineSearchVo.getStartTime().getTime() >= noticeExamine.getCreateTime().getTime()) {
                        noticeExamines.remove(i);
                        i--;
                        continue;
                    }
                    if ((examineSearchVo.getEndTime().getTime() + 1000 * 60 * 60 * 24) <= noticeExamine.getCreateTime().getTime()) {
                        noticeExamines.remove(i);
                        i--;
                    }
                } else if (examineSearchVo.getStartTime() != null) {
                    if (examineSearchVo.getStartTime().getTime() >= noticeExamine.getCreateTime().getTime()) {
                        noticeExamines.remove(i);
                        i--;
                    }
                } else if (examineSearchVo.getEndTime() != null) {
                    if ((examineSearchVo.getEndTime().getTime() + 1000 * 60 * 60 * 24) <= noticeExamine.getCreateTime().getTime()) {
                        noticeExamines.remove(i);
                        i--;
                    }
                }
            }
            noticeExamines = noticeExamines.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(NoticeExamine::getId))), ArrayList::new));
            noticeExamines = (ArrayList<NoticeExamine>) noticeExamines.stream().sorted(Comparator.comparing(NoticeExamine::getCreateTime)).collect(Collectors.toList());
            //Collections.reverse(noticeExamines);
            int startNum = (examineSearchVo.getPage() - 1) * examineSearchVo.getSize();
            int endNum;
            int total = noticeExamines.size();
            if (examineSearchVo.getPage() * examineSearchVo.getSize() < noticeExamines.size()) {
                endNum = examineSearchVo.getPage() * examineSearchVo.getSize();
            } else {
                endNum = total;
            }
            ExamineSearchResult examineSearchResult = new ExamineSearchResult();
            examineSearchResult.setTotalNum(noticeExamines.size());
            examineSearchResult.setNoticeExamineList(noticeExamines.subList(startNum, endNum));
            return Result.succeed(examineSearchResult);
        }
    }

    /**
     * 下架
     * 权限完成
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "下架")
    @GetMapping("/overQuestionnaire")
    @Log(title = "问卷管理-下架问卷",businessType = BusinessType.OTHER)
    public Result overQuestionnaire(String id) {
        // 1 草稿 2 待审核 11 模板草稿 12 新增模板
        Questionnaire questionnaire = new Questionnaire();
        questionnaire.setId(id);
        questionnaire.setStatus("7");
        // 更新 创建时间
        questionnaire.setCreateTime(new Date());
        questionnaireService.updateById(questionnaire);
        return Result.succeed("此问卷已下架");
    }

    /**
     * 审核
     * 权限完成
     *
     * @param examineVo
     * @return
     */
    @ApiOperation(value = "发布")
    @PostMapping("/examineQuestionnaire")
    public Result examineQuestionnaire(@RequestHeader(SecurityConstants.USER_ID_HEADER) String userId,
                                       @RequestHeader(SecurityConstants.USER_ORG_HEADER) String orgName,
                                       @RequestHeader(SecurityConstants.USER_HEADER) String username, @RequestBody ExamineVo examineVo) throws UnsupportedEncodingException {
        // 1 草稿 2 待审核 11 模板草稿 12 新增模板
        questionnaireService.examineQuestionnaire(userId, orgName, username, examineVo);
        Questionnaire quest = questionnaireService.getById(examineVo.getId());
        //删除待办
        indicatorFeignService.deleteToDoByUserIdAndQuestionnaireId(quest.getId(),userId);
        String[] strings = quest.getCategorysIds().split(",");
        String[] usernameList = quest.getLabelCategoryTreeIds().split(",");
        List<ToDo> vo = new ArrayList<>();
        for (int i = 0; i < strings.length; i++) {
            ToDo toDoVo = new ToDo();
            toDoVo.setId(idWorker.nextId());
            toDoVo.setQuestionnaireId(quest.getId());
            toDoVo.setCreateTime(new Date());
//            toDoVo.setTitle("【问卷待提交】您有新的问卷“"+quest.getTitle()+"”待提交，请尽快提交。");
            toDoVo.setTitle("【调查问卷】待提交\r\n您有新的问卷待提交，请尽快提交。");
            toDoVo.setToDoUserId(strings[i]);
            toDoVo.setQuestionnaireJump("2");
            toDoVo.setToDoSource("2");
            toDoVo.setToDoUserName(usernameList[i]);
            toDoVo.setIsDelete(new Byte("0"));
            vo.add(toDoVo);
        }
        indicatorFeignService.addToDoBatch(vo);
        if ("1".equals(examineVo.getExamineType())) Result.succeed("发布成功");
        if ("2".equals(examineVo.getExamineType())) Result.succeed("已驳回");
        return Result.succeed("此信息将会进入审核流程");
    }

    /**
     * 发布
     * 权限完成
     *
     * @param examineVo
     * @return
     */
    @ApiOperation(value = "发布模板")
    @PostMapping("/examineQuestionnaireModel")
    public Result examineQuestionnaireModel(@RequestBody ExamineVo examineVo) {
        // 1 草稿 2 待审核 11 模板草稿 12 新增模板
        questionnaireService.examineQuestionnaireModel(examineVo);
        if ("1".equals(examineVo.getExamineType())) Result.succeed("发布成功");
        if ("2".equals(examineVo.getExamineType())) Result.succeed("已驳回");
        return Result.succeed("此信息将会进入审核流程");
    }

    /**
     * 查看单一企业问卷答案
     *
     * @param userId,questionnaireId
     * @return
     */
    @ApiOperation(value = "查看单一企业问卷答案")
    @GetMapping("/showOneEnterpriseAnswer")
    public Result showOneEnterpriseAnswer(@RequestHeader(SecurityConstants.USER_ID_HEADER) String userId,
                                          String questionnaireId) {
        return Result.succeed(questionnaireService.showOneEnterpriseAnswer(userId, questionnaireId));
    }

    /**
     * 查看单一企业问卷答案
     *
     * @param , questionnaireId
     * @return
     */
    @ApiOperation(value = "查看单一企业问卷答案1")
    @GetMapping("/showOneEnterpriseAnswerEnterprise")
    public Result showOneEnterpriseAnswerEnterprise(String companyCode, String questionnaireId) {
//        String companyCode = UserUtils.getCompanyCode(request, redisUtil);
        return Result.succeed(questionnaireService.showOneEnterpriseAnswer(companyCode, questionnaireId));
    }

    /**
     * 查看企业问卷答案汇总
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查看企业问卷答案汇总")
    @GetMapping("/showEnterpriseAnswers")
    public Result showEnterpriseAnswers(String id) {
        return Result.succeed(questionnaireService.showEnterpriseAnswers(id));
    }

    /**
     * 查看企业问卷列表
     * 权限完成
     *
     * @param
     * @return
     */
    @ApiOperation(value = "查看企业问卷列表")
    @PostMapping("/showSubmitDetails")
    public Result showSubmitDetails(@RequestBody SubmitVo submitVo) {
        return Result.succeed(questionnaireService.showSubmitDetails(submitVo.getPage(), submitVo.getSize(), submitVo.getQuestionnaireId(), submitVo));
    }

    /**
     * 导出
     *
     * @param
     * @return
     */
    @ApiOperation(value = "导出")
    @PostMapping("/export")
    @Log(title = "问卷管理-导出问卷填写详情",businessType = BusinessType.EXPORT)
    public void importSubmit(HttpServletResponse response, @RequestBody SubmitVo submitVo) throws IOException {
        Page<SubmitVo> voPage = questionnaireService.showSubmitDetails(submitVo.getPage(), submitVo.getSize(), submitVo.getQuestionnaireId(), submitVo);
        List<SubmitVo> voList = voPage.getRecords();
        ExcelUtil<SubmitVo> util = new ExcelUtil<>(SubmitVo.class);
        util.exportExcel(response, voList, "填写详情");
    }


    /*
        用户填写问卷相关内容
     */


    /**
     * 查询填写问卷列表
     *
     * @param
     * @return
     */
    @ApiOperation(value = "查询填写问卷列表")
    @GetMapping("/getQuestionnairePoList")
    public Result getQuestionnairePoList(@RequestHeader(SecurityConstants.USER_ID_HEADER) String userId,
                                         @RequestHeader(SecurityConstants.USER_ORG_HEADER) String orgName,
                                         @RequestHeader(SecurityConstants.USER_HEADER) String username, Integer page, Integer size, String title, String questionnaireId, String enterpriseId) {
        return Result.succeed(questionnaireService.getQuestionnairePoList(userId, orgName, username, page, size, title, questionnaireId, enterpriseId));
    }

    /**
     * 查询填写问卷列表 - 企业侧
     *
     * @param
     * @return
     */
    @ApiOperation(value = "查询填写问卷列表 - 企业侧")
    @GetMapping("/getQuestionnairePoListEnterprise")
    public Result getQuestionnairePoListEnterprise(Integer page, Integer size, String title, String questionnaireId, String enterpriseId, String auth, String orgName, String userName) {
//        auth = UserUtils.getCompanyCode(request, redisUtil);
        return Result.succeed(questionnaireService.getQuestionnairePoListEnterprise(auth, orgName, userName, page, size, title, questionnaireId, enterpriseId));
    }

    /**
     * 填写问卷
     *
     * @param
     * @return
     */
    @ApiOperation(value = "填写问卷")
    @PostMapping("/submitQuestionnaire")
    public Result submitQuestionnaire(@RequestHeader(SecurityConstants.USER_ID_HEADER) String userId,
                                      @RequestHeader(SecurityConstants.USER_ORG_HEADER) String orgName,
                                      @RequestHeader(SecurityConstants.USER_HEADER) String username, @RequestBody AnswerPo answerPo) {
        questionnaireService.submitQuestionnaire(userId, orgName, username, answerPo);
        return Result.succeed("已提交");
    }

    /**
     * 填写问卷 - 企业侧
     *
     * @param
     * @return
     */
    @ApiOperation(value = "填写问卷 - 企业侧")
    @PostMapping("/submitQuestionnaireEnterprise")
    public Result submitQuestionnaireEnterprise(@RequestBody AnswerPo answerPo) {
//        answerPo.setAuth(UserUtils.getCompanyCode(request, redisUtil));
        questionnaireService.submitQuestionnaireEnterprise(answerPo.getAuth(), answerPo.getOrgName(), answerPo.getUserName(), answerPo);
        return Result.succeed("已提交");
    }

    /**
     * 填写问卷 - 暂存
     *
     * @param
     * @return
     */
    @ApiOperation(value = "填写问卷 - 暂存")
    @PostMapping("/submitQuestionnaireWhen")
    public Result submitQuestionnaireWhen(@RequestHeader(SecurityConstants.USER_ID_HEADER) String userId,
                                          @RequestBody AnswerPo answerPo) {
        answerPo.setEnterpriseId(userId);
        questionnaireService.submitQuestionnaireWhen(answerPo);
        return Result.succeed("已提交");
    }

    /**
     * 填写问卷 - 暂存
     *
     * @param
     * @return
     */
    @ApiOperation(value = "填写问卷 - 暂存 - 企业侧")
    @PostMapping("/submitQuestionnaireWhenEnterprise")
    public Result submitQuestionnaireWhenEnterprise(@RequestBody AnswerPo answerPo) {
//        answerPo.setEnterpriseId(UserUtils.getCompanyCode(request, redisUtil));
        questionnaireService.submitQuestionnaireWhen(answerPo);
        return Result.succeed("已提交");
    }

    /**
     * 暂存回显
     *
     * @param
     * @return
     */
    @ApiOperation(value = "暂存回显")
    @GetMapping("/showQuestionnaireWhen")
    public Result showQuestionnaireWhen(@RequestHeader(SecurityConstants.USER_ID_HEADER) String userId,
                                        String questionnaireId) {
        EnterpriseAnswerVo enterpriseAnswerVo = questionnaireService.showQuestionnaireWhen(userId, questionnaireId);
        return Result.succeed(enterpriseAnswerVo);
    }

    /**
     * 暂存回显
     *
     * @param
     * @return
     */
    @ApiOperation(value = "暂存回显 - 企业侧")
    @GetMapping("/showQuestionnaireWhenEnterprise")
    public Result showQuestionnaireWhenEnterprise(String enterpriseId, String questionnaireId) {

//        enterpriseId = UserUtils.getCompanyCode(request, redisUtil);
        EnterpriseAnswerVo enterpriseAnswerVo = questionnaireService.showQuestionnaireWhen(enterpriseId, questionnaireId);
        return Result.succeed(enterpriseAnswerVo);
    }

    /**
     * 站内信
     *
     * @param
     * @return
     */
    @ApiOperation(value = "站内信")
    @GetMapping("/stationReminder")
    @Log(title = "问卷管理-催办",businessType = BusinessType.OTHER)
    public Result stationReminder(String id) {
        questionnaireService.stationReminder(id);
        return Result.succeed("已发送提醒");
    }

}
