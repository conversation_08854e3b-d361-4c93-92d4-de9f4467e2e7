package com.onecity.os.questionairesurvey.controller;

import com.onecity.os.questionairesurvey.feign.IndicatorFeignService;
import com.onecity.os.questionairesurvey.mapper.QuestionnaireMapper;
import com.onecity.os.questionairesurvey.model.Questionnaire;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2023/2/13 15:40
 */

@Slf4j
@Component
public class QuestionnaireRunner implements ApplicationRunner {

    @Autowired
    private QuestionnaireMapper questionnaireMapper;

    @Autowired
    private IndicatorFeignService indicatorFeignService;

    @Override
    public void run(ApplicationArguments args) {
        log.info("0.0.0 : QuestionnaireRunner : start ");
        //匹配需要删除的数据
        List<Questionnaire> questionnaires = questionnaireMapper.getAllOverTimeData();
        log.info("2.1.1 : questionnaires . size ： {} ", questionnaires.size());
        //调用feign执行删除操作
        questionnaires.forEach(questionnaire -> indicatorFeignService.deleteToDoByQuestionnaireId(questionnaire.getId()));
        log.info("1.1.1 : toDoCalSchedule start ");
    }
}
