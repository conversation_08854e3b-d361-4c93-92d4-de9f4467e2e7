package com.onecity.os.questionairesurvey.feign;

import com.onecity.os.questionairesurvey.feign.model.BaseResult;
import com.onecity.os.questionairesurvey.feign.model.ToDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2022/5/31 10:04
 */
@Slf4j
@Component
public class IndicatorFeignFallbackFactory implements FallbackFactory<IndicatorFeignService> {
    @Override
    public IndicatorFeignService create(Throwable cause) {
        return new IndicatorFeignService() {
            @Override
            public BaseResult addToDoBatch(List<ToDo> vo) {
                log.error("添加代办异常！");
                BaseResult result = new BaseResult();
                result.setMsg("添加代办异常！");
                return result;
            }

            @Override
            public BaseResult deleteToDoByUserIdAndQuestionnaireId(String questionnaireId, String userId) {
                log.error("删除代办异常！");
                BaseResult result = new BaseResult();
                result.setMsg("删除代办异常！");
                return result;
            }

            @Override
            public BaseResult deleteToDoByQuestionnaireId(String questionnaireId) {
                log.error("删除代办异常！");
                BaseResult result = new BaseResult();
                result.setMsg("删除代办异常！");
                return result;
            }
        };
    }
}
