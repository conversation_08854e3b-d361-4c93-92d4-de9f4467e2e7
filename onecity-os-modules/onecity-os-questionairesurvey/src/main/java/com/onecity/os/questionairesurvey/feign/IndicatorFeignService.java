package com.onecity.os.questionairesurvey.feign;

import com.onecity.os.questionairesurvey.common.constant.Constants;
import com.onecity.os.questionairesurvey.feign.model.BaseResult;
import com.onecity.os.questionairesurvey.feign.model.ToDo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

//指定需要调用的微服务名称
@FeignClient(name= Constants.COCKPIT_SERVICE,fallbackFactory = IndicatorFeignFallbackFactory.class)
public interface IndicatorFeignService {
    //调用的请求路径
    @RequestMapping(value = "/indicator/addToDoBatch",method = RequestMethod.POST)
    BaseResult addToDoBatch(@RequestBody List<ToDo> vo);

    @RequestMapping(value = "/indicator/deleteToDoByUserIdAndQuestionnaireId",method = RequestMethod.POST)
    BaseResult deleteToDoByUserIdAndQuestionnaireId(@RequestParam("questionnaireId") String questionnaireId, @RequestBody String userId);

    /**
     * @param questionnaireId
     * @return
     */
    @RequestMapping(value = "/indicator/deleteToDoByQuestionnaireId",method = RequestMethod.POST)
    BaseResult deleteToDoByQuestionnaireId(@RequestBody String questionnaireId);
}

