package com.onecity.os.questionairesurvey.feign;

import com.onecity.os.questionairesurvey.common.constant.Constants;
import com.onecity.os.questionairesurvey.feign.model.BaseResult;
import com.onecity.os.questionairesurvey.feign.model.RemindInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

//指定需要调用的微服务名称
@FeignClient(name= Constants.MESSAGE_SERVICE,fallbackFactory = MessageFeignFallbackFactory.class)
//@FeignClient(url= "192.168.108.1:32108",name= "onecity-os-message",fallbackFactory = MessageFeignFallbackFactory.class)
public interface MessageFeignService {
    //调用的请求路径
    @RequestMapping(value = "/remind/addMessage",method = RequestMethod.POST)
    BaseResult addMessage(@RequestBody List<RemindInfo> remindInfoList);
}

