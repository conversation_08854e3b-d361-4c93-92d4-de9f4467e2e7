package com.onecity.os.questionairesurvey.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

@Data
@TableName("answer_when")
@ToString
public class AnswerWhen {
    @TableId
    private String id;
    private String questionnaireId;
    private String subjectId;
    private String optionId;
    private String answer;
    private String type;
    private String enterpriseId;
}
