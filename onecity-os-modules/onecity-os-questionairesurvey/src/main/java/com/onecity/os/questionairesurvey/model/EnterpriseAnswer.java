package com.onecity.os.questionairesurvey.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;
import org.springframework.data.annotation.Id;

import java.util.Date;

@Data
@TableName("enterprise_answer")
@ToString
public class EnterpriseAnswer {
    @Id
    private String id;
    private String enterpriseId;
    private Date submitTime;
    // 1 提交 2 未提交
    private String status;
    private String questionnaireId;
    private String enterpriseName;
    private Date openTime;
    // 企业1|职能2 类型
    private String enterpriseType;

}
