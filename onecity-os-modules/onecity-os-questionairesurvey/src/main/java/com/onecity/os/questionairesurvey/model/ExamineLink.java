package com.onecity.os.questionairesurvey.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("examine_link")
@ApiModel(value="审核流程轨迹")
public class ExamineLink {
    @ApiModelProperty(value = "id")
    @TableId(value = "id")
    private String id;
    @ApiModelProperty(value = "处理人id")
    @TableField(value = "deal_id")
    private String dealId;
    @ApiModelProperty(value = "处理人")
    @TableField(value = "deal_name")
    private String dealName;
    @ApiModelProperty(value = "处理人部门")
    @TableField(value = "deal_orgName")
    private String dealOrgName;
    @ApiModelProperty(value = "处理人部门id")
    @TableField(value = "deal_orgId")
    private String dealOrgId;
    @ApiModelProperty(value = "动作")
    @TableField(value = "action")
    private String action;
    @ApiModelProperty(value = "信息")
    @TableField(value = "mark")
    private String mark;
    @ApiModelProperty(value = "处理时间")
    @TableField(value = "deal_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dealTime;
    @ApiModelProperty(value = "审核id")
    @TableField(value = "examine_id")
    private String examineId;
    @ApiModelProperty(value = "公告id")
    @TableField(value = "notice_id")
    private String noticeId;
    @ApiModelProperty(value = "状态")
    @TableField(value = "status")
    private String status;

}
