package com.onecity.os.questionairesurvey.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("notice_examine")
@ApiModel(value="通知公告审核列表")
public class NoticeExamine {
    @ApiModelProperty(value = "id")
    @TableId(value = "id")
    private String id;

    @ApiModelProperty(value = "通知公告标题")
    @TableField(value = "notice_title")
    private String noticeTitle;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "notice_creater")
    private String noticeCreater;

    @ApiModelProperty(value = "创建人Id")
    @TableField(value = "notice_createrId")
    private String noticeCreaterId;

    @ApiModelProperty(value = "创建人组织Id")
    @TableField(value = "notice_createrOrgId")
    private String noticeCreaterOrgId;

    @ApiModelProperty(value = "上一节点Id")
    @TableField(value = "before_id")
    private String beforeId;

    @ApiModelProperty(value = "上一节点人")
    @TableField(value = "before_name")
    private String beforeName;

    @ApiModelProperty(value = "上一节点部门")
    @TableField(value = "before_orgId")
    private String beforeOrgId;

    @ApiModelProperty(value = "审批人ID")
    @TableField(value = "approver_id")
    private String approverId;


    @ApiModelProperty(value = "记录")
    @TableField(value = "mark")
    private String mark;

    @ApiModelProperty(value = "通知id")
    @TableField(value = "notice_id")
    private String noticeId;

    @ApiModelProperty(value = "问卷状态")
    @TableField(exist = false)
    private String noticeStatus;

}
