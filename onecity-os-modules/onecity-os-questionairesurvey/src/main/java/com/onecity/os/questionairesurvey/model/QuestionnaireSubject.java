package com.onecity.os.questionairesurvey.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

import java.util.Date;

/**
 *
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("questionnaire_subject")
public class QuestionnaireSubject extends Model {
	@TableId
	private String id;
	private String questionnaireId;
	private String subjectId;
	private Date createTime;
}
