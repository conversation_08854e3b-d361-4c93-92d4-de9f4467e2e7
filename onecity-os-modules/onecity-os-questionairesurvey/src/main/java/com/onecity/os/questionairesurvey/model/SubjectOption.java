package com.onecity.os.questionairesurvey.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

/**
 *
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("subject_option")
public class SubjectOption extends Model {
	@TableId
	private String id;
	private String subjectId;
	private String optionId;
	private Long createTime;
}
