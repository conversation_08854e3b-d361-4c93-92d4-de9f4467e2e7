package com.onecity.os.questionairesurvey.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="读取统计")
public class AnnouncementStatistics{

    @ApiModelProperty(value = "公告Id")
    private String noticeId;

    @ApiModelProperty(value = "读取人Id")
    private String userId;

    @ApiModelProperty(value = "读取人姓名")
    private String userName;

    @ApiModelProperty(value = "电话")
    private String phone;

    @ApiModelProperty(value = "Email")
    private String email;

    @ApiModelProperty(value = "组织Id")
    private String organizationId;

    @ApiModelProperty(value = "组织编码")
    private String organizationCode;

    @ApiModelProperty(value = "组织名称")
    private String organizationName;

    @ApiModelProperty(value = "读取状态")
    private String isRead;
}
