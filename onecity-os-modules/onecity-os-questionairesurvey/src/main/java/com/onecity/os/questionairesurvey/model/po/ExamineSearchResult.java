package com.onecity.os.questionairesurvey.model.po;

import com.onecity.os.questionairesurvey.model.NoticeExamine;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value="公告列表")
public class ExamineSearchResult {
    @ApiModelProperty(value = "总计条目数")
    private Integer totalNum;

    @ApiModelProperty(value = "本页条目信息")
    List<NoticeExamine> noticeExamineList;
}
