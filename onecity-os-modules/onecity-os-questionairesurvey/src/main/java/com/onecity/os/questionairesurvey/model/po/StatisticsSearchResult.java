package com.onecity.os.questionairesurvey.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value="调拨历史结果")
public class StatisticsSearchResult {

    @ApiModelProperty(value = "总计条目数")
    private Integer totalNum;

    @ApiModelProperty(value = "本页条目信息")
    List<AnnouncementStatistics> statisticsList;

}
