package com.onecity.os.questionairesurvey.model.vo;

import com.onecity.os.questionairesurvey.model.vo.base.PageSearchBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="通知公告模糊搜索视图")
public class FuzzySearchStatisticsVO extends PageSearchBaseVO {

    @ApiModelProperty(value = "主键questionnaireId",required = true)
    private String questionnaireId;

    @ApiModelProperty(value = "状态（已读，未读）")
    private String state;

    @ApiModelProperty(value = "名称")
    private String deptName;

    @ApiModelProperty(value = "读取者所属范围",required = true) //职能门户、企业门户、全平台用户
    private String readerRange;
}
