package com.onecity.os.questionairesurvey.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 *
 */
@Data
public class QuestionnaireVo {
	private String id;
	private String title;
	private String type;
	private String description;
	private Date startTime;
	private Date endTime;
	private String cycleTime;
	@ApiModelProperty(value = "发布范围（发布范围的必填）",required = true)
	private String shareType;
	private String createPeople;
	private String createDepart;
	private String status;
	private String cycle;
	private List<SubjectVo> subjectList;

	@ApiModelProperty(value = "标签树（企业端）Ids")
	private List<String> labelCategoryTreeIds;

	@ApiModelProperty(value = "分类库（企业端）Ids")
	private List<String> categorysIds;

	@ApiModelProperty(value = "职能标签Ids")
	private List<String> functionIds;

	@ApiModelProperty(value = "审批人ID")
	private String approverId;
}
