package com.onecity.os.questionairesurvey.util;

import com.google.common.base.Joiner;

import java.util.ArrayList;
import java.util.List;

public class ObjectUtil {
    public static String listToStrings(List<String> list){
        if(list == null || list.size() == 0){
            return null;
        }
        return Joiner.on(",").join(list);
        /*StringBuilder sb = new StringBuilder();
        String resultString = "";
        boolean flag = false;
        for(Long one : list){
            if(flag){
                sb.append(",");
            }else{
                flag = true;
            }
            sb.append(one);
        }
        return sb.toString();*/
    }

    public static List<String> stringToList(String strs){
        if(strs == null || strs.equals("")){
            return null;
        }
        String str[] = strs.split(",");
        List<String> ids = new ArrayList<>();
        for(String s : str){
            ids.add(s);
        }
        return ids;
    }

    public static List<Long> StringsToLongs(List<String> strs){
        if(strs == null || strs.size() == 0){
            return null;
        }
        List<Long> results = new ArrayList<>();
        for(String s : strs){
            results.add(Long.valueOf(s));
        }
        return results;
    }

    public static List<Long> strToLongs(String str){
        if(str == null || str.equals("")){
            return null;
        }
        String[] strs = str.split(",");
        List<Long> result = new ArrayList<>();
        for(String s : strs){
            result.add(Long.valueOf(s));
        }
        return result;
    }
}
