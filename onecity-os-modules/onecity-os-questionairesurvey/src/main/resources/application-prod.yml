spring:
  data:
    mongodb:
      #带有认证
      uri: **********************************************************************************************************
      #不带认证
  #      uri: mongodb://*************:30550/cmict-g2b-consumer
  #redis集群 配置
  redis:
    host: redis
    cluster:
      #获取失败 最大重定向次数
      max-redirects: 3
      nodes:
        - *************:7001
        - *************:7002
        - *************:7003
        - *************:7004
        - ************:7005
        - ************:7006
    lettuce:
      pool:
        max-active: 100   #最大连接数据库连接数,设 -1 为没有限制
        max-idle: 10     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
        min-idle: 5     #最小等待连接中的数量,设 0 为没有限制
      shutdown-timeout: 100ms
    password: 1qaz2wsx-pl,
    timeout: 6000ms