apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: $DEPLOY_NAMESPACE
  name: $APP_NAME
  labels:
    app: $APP_NAME
spec:
  replicas: 1
  selector:
    matchLabels:
      app: $APP_NAME
  template:
    metadata:
      labels:
        app: $APP_NAME
    spec:
      containers:
        - name: $APP_NAME
          image: $REGISTRY/$HARBOR_PROJECT_NAME/$IMAGE_NAME
          ports:
            - name: http-8091
              containerPort: 8091
              protocol: TCP
          imagePullPolicy: Always
      dnsPolicy: ClusterFirst
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  namespace: $DEPLOY_NAMESPACE
  name: $APP_NAME
spec:
  type: ClusterIP
  ports:
    - port: 8091
      targetPort: 8091
      protocol: TCP
  selector:
    app: $APP_NAME