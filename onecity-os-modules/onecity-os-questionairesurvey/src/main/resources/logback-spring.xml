<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true">
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <contextName>archives-server</contextName>
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />

    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}" />
    <property name="logHome" value="/opt/logs/cmict-questionairesurvey" scope="context"/>
    <!-- 文件切割大小 -->
    <property name="maxFileSize" value="100MB" />
    <!-- 文档保留天数 -->
    <property name="maxHistory" value="30" />
    <!-- 文档保留总大小 -->
    <property name="totalSizeCap" value="10GB" />
    <jmxConfigurator />
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
        <encoder>
<!--            <pattern>%date{yyyy-MM-dd HH:mm:ss} %highlight(%-5level) (%file:%line\)- %m%n</pattern>-->
            <Pattern>${CONSOLE_LOG_PATTERN}</Pattern>
            <!-- 控制台也要使用utf-8，不要使用gbk -->
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--系统服务日志-->
        <file>${logHome}/logFile.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logHome}/%d/logFile.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 单个日志文件最多 100MB, 60天的日志周期，最大不能超过10GB -->
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <charset>utf-8</charset>
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level  %logger{50}: %msg%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logHome}/logErrorFile.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logHome}/%d/logErrorFile.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <charset>utf-8</charset>
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level  %logger{50}: %msg%n</Pattern>
        </encoder>
    </appender>

    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>
    <springProfile name="local">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
        </root>
        <logger name="com.alibaba.nacos" level="WARN" additivity="false">
            <appender-ref ref="STDOUT" />
        </logger>
    </springProfile>
    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="INFO_FILE"/>
            <appender-ref ref="ERROR_FILE"/>
        </root>
        <logger name="com.alibaba.nacos" level="WARN" additivity="false">
            <appender-ref ref="STDOUT" />
            <appender-ref ref="INFO_FILE"/>
            <appender-ref ref="ERROR_FILE"/>
        </logger>
    </springProfile>
    <springProfile name="test">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="INFO_FILE"/>
            <appender-ref ref="ERROR_FILE"/>
        </root>
<!--        <root level="INFO">-->
<!--            <appender-ref ref="STDOUT"/>-->
<!--        </root>-->
        <logger name="com.alibaba.nacos" level="WARN" additivity="false">
            <appender-ref ref="STDOUT" />
            <appender-ref ref="INFO_FILE"/>
            <appender-ref ref="ERROR_FILE"/>
        </logger>
    </springProfile>
    <springProfile name="uat">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="INFO_FILE"/>
            <appender-ref ref="ERROR_FILE"/>
        </root>
        <!--        <root level="INFO">-->
        <!--            <appender-ref ref="STDOUT"/>-->
        <!--        </root>-->
        <logger name="com.alibaba.nacos" level="WARN" additivity="false">
            <appender-ref ref="STDOUT" />
            <appender-ref ref="INFO_FILE"/>
            <appender-ref ref="ERROR_FILE"/>
        </logger>
    </springProfile>
    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="INFO_FILE"/>
            <appender-ref ref="ERROR_FILE"/>
        </root>
        <logger name="com.alibaba.nacos" level="WARN" additivity="false">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="INFO_FILE"/>
            <appender-ref ref="ERROR_FILE"/>
        </logger>
    </springProfile>
</configuration>
