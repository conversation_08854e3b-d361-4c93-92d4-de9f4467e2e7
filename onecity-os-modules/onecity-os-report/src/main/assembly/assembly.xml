<assembly>
	<id>assembly</id>
	<formats>
		<format>zip</format>
	</formats>
	<!-- 	<dependencySets>
            <dependencySet>
                <outputDirectory>lib</outputDirectory>
                <scope>runtime</scope>
            </dependencySet>
        </dependencySets> -->
	<includeBaseDirectory>true</includeBaseDirectory>
	<fileSets>
		<fileSet>
			<directory>lib/mssqlserver</directory>
			<outputDirectory>lib</outputDirectory>
			<fileMode>0755</fileMode>
		</fileSet>
		<fileSet>
			<directory>lib/kudu</directory>
			<outputDirectory>lib</outputDirectory>
			<fileMode>0755</fileMode>
		</fileSet>
		<fileSet>
			<directory>lib/oracle</directory>
			<outputDirectory>lib</outputDirectory>
			<fileMode>0755</fileMode>
		</fileSet>
		<fileSet>
			<directory>lib/postgresql</directory>
			<outputDirectory>lib</outputDirectory>
			<fileMode>0755</fileMode>
		</fileSet>
		<fileSet>
			<directory>lib/dm8</directory>
			<outputDirectory>lib</outputDirectory>
			<fileMode>0755</fileMode>
		</fileSet>
		<fileSet>
			<directory>lib/opengauss</directory>
			<outputDirectory>lib</outputDirectory>
			<fileMode>0755</fileMode>
		</fileSet>
		<fileSet>
			<directory>lib/kingbase</directory>
			<outputDirectory>lib</outputDirectory>
			<fileMode>0755</fileMode>
		</fileSet>
		<fileSet>
			<directory>src/main/assembly/bin</directory>
			<outputDirectory>bin</outputDirectory>
			<fileMode>0755</fileMode>
		</fileSet>
		<fileSet>
			<directory>src/main/resources</directory>
			<outputDirectory>conf</outputDirectory>
			<includes>
				<include>bootstrap.yml</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>target</directory>
			<outputDirectory>lib</outputDirectory>
			<includes>
				<include>aj-report-*.jar</include>
			</includes>
			<excludes>
				<exclude>*-javadoc.jar</exclude>
				<exclude>*-sources.jar</exclude>
			</excludes>
			<fileMode>0755</fileMode>
		</fileSet>
		<fileSet>
			<directory>./../</directory>
			<outputDirectory>/</outputDirectory>
			<includes>
				<include>LICENSE</include>
				<include>NOTICE</include>
			</includes>
		</fileSet>
	</fileSets>
</assembly>
