package com.anjiplus.template.gaea.business.filter;

import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Created by raodeming on 2021/6/24.
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class CorsFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse res = (HttpServletResponse) response;
        // 设置允许Cookie
        res.addHeader("Access-Control-Allow-Credentials", "true");
        String origin = req.getHeader("Origin");

        // 更严格地验证和清理Origin头
        if (origin != null) {
            // 移除所有控制字符和不可见字符
            origin = sanitizeHeaderValue(origin);

            // 使用更严格的正则表达式验证Origin格式
            if (origin.matches("^https?://([a-zA-Z0-9][a-zA-Z0-9.-]*\\.[a-zA-Z]{2,})(:\\d{1,5})?$")) {
                // 格式正确，设置CORS头
                res.addHeader("Access-Control-Allow-Origin", origin);
            } else {
                // 如果Origin不符合要求，设置为null或特定值
                res.addHeader("Access-Control-Allow-Origin", "null");
            }
        } else {
            res.addHeader("Access-Control-Allow-Origin", "null");
        }

        // 设置允许跨域请求的方法（使用明确的列表而不是通配符）
        res.addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");

        // 设置允许的头（使用明确的列表而不是通配符）
        res.addHeader("Access-Control-Allow-Headers",
                "Origin, X-Requested-With, Content-Type, Accept, Authorization");

        // 设置暴露的头
        res.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
//        String origin = req.getHeader("Origin");
//        if (origin != null && origin.matches("^https?://([a-zA-Z0-9.-]+)(:\\d+)?$")) {
//            // 格式正确，继续处理,去掉换行符
//            origin = origin.replaceAll("[\r\n]", "");
//            // 允许http://www.xxx.com域（自行设置，这里只做示例）发起跨域请求
//            res.addHeader("Access-Control-Allow-Origin", req.getHeader("Origin"));
//        } else {
//            origin = "null"; // 或在某些情况下拒绝请求
//        }
//        // 设置允许跨域请求的方法
//        res.addHeader("Access-Control-Allow-Methods", "*");
//        // 允许跨域请求包含content-type
//        res.addHeader("Access-Control-Allow-Headers", "*");
//        res.addHeader("Access-Control-Expose-Headers", "*");
        chain.doFilter(req, res);
    }
    /**
     * 清理头信息值，防止HTTP响应截断攻击
     * @param value 头信息值
     * @return 清理后的值
     */
    private String sanitizeHeaderValue(String value) {
        if (value == null) {
            return null;
        }

        // 移除所有可能导致HTTP响应截断的字符
        // CR, LF, 空字符, 以及其他控制字符
        String sanitized = value.replaceAll("[\\p{Cntrl}]", "");

        // 移除可能导致XSS的字符
        sanitized = sanitized.replaceAll("<", "&lt;").replaceAll(">", "&gt;");

        // 限制头信息值的长度，防止过长的值
        final int MAX_HEADER_LENGTH = 1024;
        if (sanitized.length() > MAX_HEADER_LENGTH) {
            sanitized = sanitized.substring(0, MAX_HEADER_LENGTH);
        }

        return sanitized;
    }
    @Override
    public void destroy() {
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }
}
