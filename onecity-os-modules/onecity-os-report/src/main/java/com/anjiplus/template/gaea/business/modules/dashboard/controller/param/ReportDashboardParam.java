/**/
package com.anjiplus.template.gaea.business.modules.dashboard.controller.param;

import lombok.Data;
import java.io.Serializable;
import com.anji.plus.gaea.annotation.Query;
import com.anji.plus.gaea.constant.QueryEnum;
import com.anji.plus.gaea.curd.params.PageParam;

import java.util.List;


/**
* @desc ReportDashboard 大屏设计查询输入类
* <AUTHOR>
* @date 2021-04-12 14:52:21.761
**/
@Data
public class ReportDashboardParam extends PageParam implements Serializable{
}
