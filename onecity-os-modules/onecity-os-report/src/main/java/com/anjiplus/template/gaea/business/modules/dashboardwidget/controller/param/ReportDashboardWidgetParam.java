/**/
package com.anjiplus.template.gaea.business.modules.dashboardwidget.controller.param;

import lombok.Data;
import java.io.Serializable;
import com.anji.plus.gaea.annotation.Query;
import com.anji.plus.gaea.constant.QueryEnum;
import com.anji.plus.gaea.curd.params.PageParam;

import java.util.List;


/**
* @desc ReportDashboardWidget 大屏看板数据渲染查询输入类
* <AUTHOR>
* @date 2021-04-12 15:12:43.724
**/
@Data
public class ReportDashboardWidgetParam extends PageParam implements Serializable{
}
