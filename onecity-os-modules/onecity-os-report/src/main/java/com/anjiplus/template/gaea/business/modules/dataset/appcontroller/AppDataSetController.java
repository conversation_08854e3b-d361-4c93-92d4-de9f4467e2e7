
package com.anjiplus.template.gaea.business.modules.dataset.appcontroller;
import com.anji.plus.gaea.annotation.Permission;
import com.anji.plus.gaea.bean.ResponseBean;
import com.anji.plus.gaea.curd.controller.GaeaBaseController;
import com.anji.plus.gaea.curd.service.GaeaBaseService;
import com.anjiplus.template.gaea.business.modules.dataset.controller.dto.DataSetDto;
import com.anjiplus.template.gaea.business.modules.dataset.controller.param.DataSetParam;
import com.anjiplus.template.gaea.business.modules.dataset.dao.entity.DataSet;
import com.anjiplus.template.gaea.business.modules.dataset.service.DataSetService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
**/
@RestController
@Api(tags = "App端数据集管理入口")
//@Permission(code = "resultsetManage", name = "数据集管理")
@RequestMapping("/app/dataSet")
public class AppDataSetController extends GaeaBaseController<DataSetParam, DataSet, DataSetDto> {

    @Autowired
    private DataSetService dataSetService;

    @Override
    public GaeaBaseService<DataSetParam, DataSet> getService() {
        return dataSetService;
    }

    @Override
    public DataSet getEntity() {
        return new DataSet();
    }

    @Override
    public DataSetDto getDTO() {
        return new DataSetDto();
    }

    @GetMapping("/detailById/{id}")
//    @Permission( code = "query", name = "明细" )
    public ResponseBean detailById(@PathVariable("id") Long id) {
        DataSetDto dataSetDto = dataSetService.detailSet(id);
        return responseSuccessWithData(dataSetService.testTransform(dataSetDto));
    }


}
