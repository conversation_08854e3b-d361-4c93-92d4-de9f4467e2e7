package com.anjiplus.template.gaea.business.modules.datasettransform.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.anji.plus.gaea.exception.BusinessExceptionBuilder;
import com.anjiplus.template.gaea.business.code.ResponseCode;
import com.anjiplus.template.gaea.business.modules.datasettransform.controller.dto.DataSetTransformDto;
import com.anjiplus.template.gaea.business.modules.datasettransform.service.TransformStrategy;
import jdk.nashorn.api.scripting.NashornScriptEngineFactory;
import jdk.nashorn.api.scripting.ScriptObjectMirror;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.regex.Pattern;

/**
 * Created by raodeming on 2021/3/23.
 */
@Component
@Slf4j
public class JsTransformServiceImpl implements TransformStrategy {

    private ScriptEngine engine;
    {
        ScriptEngineManager manager = new ScriptEngineManager();
        engine = manager.getEngineByName("JavaScript");
    }
    // 使用NashornScriptEngineFactory创建引擎，并设置安全限制
    private ScriptEngine createSafeEngine() {
        NashornScriptEngineFactory factory = new NashornScriptEngineFactory();
        // 禁用Java类访问，只允许基本的JavaScript功能
        return factory.getScriptEngine(new String[] { "--no-java", "--no-syntax-extensions" });
    }
    /**
     * 数据清洗转换 类型
     *
     * @return
     */
    @Override
    public String type() {
        return "js";
    }

    /***
     * 清洗转换算法接口
     * @param def
     * @param data
     * @return
     */
    @Override
    public List<JSONObject> transform(DataSetTransformDto def, List<JSONObject> data) {
        return getValueFromJs(def,data);
    }
    private List<JSONObject> getValueFromJs(DataSetTransformDto def, List<JSONObject> data) {
        String js = def.getTransformScript();

        // 1. 验证JavaScript代码安全性
        if (!isScriptSafe(js)) {
            log.error("JavaScript脚本包含不安全的代码: {}", JSONObject.toJSONString(js));
            throw BusinessExceptionBuilder.build(ResponseCode.EXECUTE_JS_ERROR, "脚本包含不允许的代码");
        }

        // 2. 创建安全的脚本引擎
        ScriptEngine engine = createSafeEngine();

        try {
            // 3. 使用超时机制执行JavaScript
            ExecutorService executor = Executors.newSingleThreadExecutor();
            Future<List<JSONObject>> future = executor.submit(new Callable<List<JSONObject>>() {
                @Override
                public List<JSONObject> call() throws Exception {
                    engine.eval(js);
                    if (engine instanceof Invocable) {
                        Invocable invocable = (Invocable) engine;
                        Object dataTransform = invocable.invokeFunction("dataTransform", data);

                        if (dataTransform instanceof List) {
                            return (List<JSONObject>) dataTransform;
                        }

                        // 处理ScriptObjectMirror
                        ScriptObjectMirror scriptObjectMirror = (ScriptObjectMirror) dataTransform;
                        List<JSONObject> result = new ArrayList<>();
                        scriptObjectMirror.forEach((key, value) -> {
                            ScriptObjectMirror valueObject = (ScriptObjectMirror) value;
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.putAll(valueObject);
                            result.add(jsonObject);
                        });
                        return result;
                    }
                    return null;
                }
            });

            // 设置超时时间（例如10秒）
            try {
                return future.get(10, TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                future.cancel(true);
                log.error("JavaScript执行超时", e);
                throw BusinessExceptionBuilder.build(ResponseCode.EXECUTE_JS_ERROR, "脚本执行超时");
            } finally {
                executor.shutdownNow();
            }
        } catch (Exception ex) {
            log.error("执行js异常", ex);
            throw BusinessExceptionBuilder.build(ResponseCode.EXECUTE_JS_ERROR, ex.getMessage());
        }
    }

    /**
     * 检查JavaScript代码是否安全
     *
     * @param script JavaScript代码
     * @return 是否安全
     */
    private boolean isScriptSafe(String script) {
        // 移除注释和空白字符，以防止绕过检测
        String cleanedScript = script.replaceAll("(?:/\\*(?:[^*]|(?:\\*+[^*/]))*\\*+/)|(?://.*)", "")
                .replaceAll("\\s+", " ");

        // 定义危险模式的正则表达式
        Pattern[] dangerousPatterns = {
                // Java访问
                Pattern.compile(".*java\\s*\\..*"),
                Pattern.compile(".*Java\\s*\\..*"),
                // 系统访问
                Pattern.compile(".*System\\s*\\..*"),
                Pattern.compile(".*Runtime\\s*\\..*"),
                Pattern.compile(".*Process\\s*\\..*"),
                Pattern.compile(".*File\\s*\\..*"),
                // 反射和类加载
                Pattern.compile(".*reflect\\s*\\..*"),
                Pattern.compile(".*Class\\s*\\.forName.*"),
                Pattern.compile(".*ClassLoader.*"),
                // 网络访问
                Pattern.compile(".*Socket\\s*\\(.*"),
                Pattern.compile(".*URL\\s*\\(.*"),
                // 其他危险操作
                Pattern.compile(".*load\\s*\\(.*\\).*"),
                Pattern.compile(".*eval\\s*\\(.*\\).*"),
                Pattern.compile(".*exec\\s*\\(.*\\).*"),
                Pattern.compile(".*importPackage\\s*\\(.*\\).*"),
                Pattern.compile(".*importClass\\s*\\(.*\\).*"),
                Pattern.compile(".*Java\\.type\\s*\\(.*\\).*"),
                // 禁止访问全局对象
                Pattern.compile(".*this\\s*\\.constructor\\s*\\.constructor.*"),
                Pattern.compile(".*global\\s*\\..*"),
                Pattern.compile(".*process\\s*\\..*"),
                Pattern.compile(".*require\\s*\\(.*\\).*")
        };

        // 检查代码是否匹配任何危险模式
        for (Pattern pattern : dangerousPatterns) {
            if (pattern.matcher(cleanedScript).find()) {
                return false;
            }
        }

        return true;
    }
//    private List<JSONObject> getValueFromJs(DataSetTransformDto def, List<JSONObject> data) {
//        String js = def.getTransformScript();
//        try {
//            engine.eval(js);
//            if(engine instanceof Invocable){
//                Invocable invocable = (Invocable) engine;
//                Object dataTransform = invocable.invokeFunction("dataTransform", data);
//                if (dataTransform instanceof List) {
//                    return (List<JSONObject>) dataTransform;
//                }
//                //前端js自定义的数组[{"aa":"bb"}]解析后变成{"0":{"aa":"bb"}}
//                ScriptObjectMirror scriptObjectMirror = (ScriptObjectMirror) dataTransform;
//                List<JSONObject> result = new ArrayList<>();
//                scriptObjectMirror.forEach((key, value) -> {
//                    ScriptObjectMirror valueObject = (ScriptObjectMirror) value;
//                    JSONObject jsonObject = new JSONObject();
//                    jsonObject.putAll(valueObject);
//                    result.add(jsonObject);
//                });
//                return result;
//            }
//
//        } catch (Exception ex) {
//            log.info("执行js异常", ex);
//            throw BusinessExceptionBuilder.build(ResponseCode.EXECUTE_JS_ERROR, ex.getMessage());
//        }
//        return null;
//    }
}
