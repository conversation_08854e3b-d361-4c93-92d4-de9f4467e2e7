package com.anjiplus.template.gaea.business.modules.file.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.IOUtils;

import java.io.*;

/**
 * Created by raodeming on 2021/7/8.
 */
@Slf4j
public class FileUtilss {
    public static byte[] readFileToByteArray(File file) {
        byte[] buffer = null;
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] b = new byte[1024];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            fis.close();
            bos.close();
            buffer = bos.toByteArray();
        } catch (IOException e) {
            log.error("error", e);
        }finally {
            IOUtils.closeQuietly(fis);
        }
        return buffer;
    }

}
