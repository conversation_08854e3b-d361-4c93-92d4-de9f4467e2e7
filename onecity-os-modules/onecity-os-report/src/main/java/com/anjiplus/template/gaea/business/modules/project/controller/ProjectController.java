package com.anjiplus.template.gaea.business.modules.project.controller;

import com.anji.plus.gaea.annotation.Permission;
import com.anji.plus.gaea.annotation.log.GaeaAuditLog;
import com.anji.plus.gaea.bean.ResponseBean;
import com.anji.plus.gaea.curd.controller.GaeaBaseController;
import com.anji.plus.gaea.curd.service.GaeaBaseService;
import com.anjiplus.template.gaea.business.modules.project.controller.dto.ProjectDto;
import com.anjiplus.template.gaea.business.modules.project.controller.dto.ProjectSetDto;
import com.anjiplus.template.gaea.business.modules.project.controller.param.ProjectParam;
import com.anjiplus.template.gaea.business.modules.project.dao.entity.Project;
import com.anjiplus.template.gaea.business.modules.project.service.ProjectService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/8/2 10:19
 */
@RestController
@Api(tags = "项目管理")
//@Permission(code = "projectManage", name = "项目管理")
@RequestMapping("/project")
public class ProjectController extends GaeaBaseController<ProjectParam, Project, ProjectDto> {

    @Autowired
    private ProjectService projectService;

    @Override
    public GaeaBaseService<ProjectParam, Project> getService() {
        return projectService;
    }

    @Override
    public Project getEntity() {
        return new Project();
    }

    @Override
    public ProjectDto getDTO() {
        return new ProjectDto();
    }

    @PostMapping("/set")
//    @Permission(code = "set", name = "配置")
    @GaeaAuditLog(pageTitle = "配置")
    public ResponseBean set(@RequestBody ProjectSetDto dto) {
        projectService.set(dto);
        return ResponseBean.builder().build();
    }
}
