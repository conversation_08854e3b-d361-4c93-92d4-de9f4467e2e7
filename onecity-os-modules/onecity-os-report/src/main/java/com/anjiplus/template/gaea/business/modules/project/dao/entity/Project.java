package com.anjiplus.template.gaea.business.modules.project.dao.entity;

import com.anji.plus.gaea.annotation.Unique;
import com.anji.plus.gaea.curd.entity.GaeaBaseEntity;
import com.anjiplus.template.gaea.business.code.ResponseCode;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/8/1 16:20
 */
@TableName(keepGlobalPrefix=true, value="gaea_project")
@Data
public class Project extends GaeaBaseEntity {

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目编码")
    @Unique(code = ResponseCode.PROJECT_CODE_ISEXIST)
    private String projectCode;

    @ApiModelProperty(value = "首页报表编码")
    private String reportCode;

    @ApiModelProperty(value = "报表描述")
    private String projectDesc;

    @ApiModelProperty(value = "报表作者")
    private String projectAuthor;

    @ApiModelProperty(value = "0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG")
    private Integer enableFlag;

    @ApiModelProperty(value = "0--未删除 1--已删除 DIC_NAME=DELETE_FLAG")
    private Integer deleteFlag;
}
