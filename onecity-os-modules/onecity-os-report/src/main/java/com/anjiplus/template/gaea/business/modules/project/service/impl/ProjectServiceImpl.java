package com.anjiplus.template.gaea.business.modules.project.service.impl;

import com.anji.plus.gaea.constant.BaseOperationEnum;
import com.anji.plus.gaea.curd.mapper.GaeaBaseMapper;
import com.anji.plus.gaea.exception.BusinessException;
import com.anji.plus.gaea.exception.BusinessExceptionBuilder;
import com.anji.plus.gaea.utils.GaeaBeanUtils;
import com.anjiplus.template.gaea.business.code.ResponseCode;
import com.anjiplus.template.gaea.business.enums.ReportTypeEnum;
import com.anjiplus.template.gaea.business.modules.dashboard.dao.entity.ReportDashboard;
import com.anjiplus.template.gaea.business.modules.dashboard.service.ReportDashboardService;
import com.anjiplus.template.gaea.business.modules.dashboardwidget.dao.entity.ReportDashboardWidget;
import com.anjiplus.template.gaea.business.modules.dashboardwidget.service.ReportDashboardWidgetService;
import com.anjiplus.template.gaea.business.modules.project.controller.dto.ProjectDto;
import com.anjiplus.template.gaea.business.modules.project.controller.dto.ProjectSetDto;
import com.anjiplus.template.gaea.business.modules.project.dao.ProjectMapper;
import com.anjiplus.template.gaea.business.modules.project.dao.ProjectReportMapper;
import com.anjiplus.template.gaea.business.modules.project.dao.entity.Project;
import com.anjiplus.template.gaea.business.modules.project.dao.entity.ProjectReport;
import com.anjiplus.template.gaea.business.modules.project.service.ProjectService;
import com.anjiplus.template.gaea.business.modules.report.dao.entity.Report;
import com.anjiplus.template.gaea.business.modules.reportexcel.dao.entity.ReportExcel;
import com.anjiplus.template.gaea.business.modules.reportexcel.service.ReportExcelService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2022/8/1 16:20
 */
@Service
public class ProjectServiceImpl implements ProjectService {

    @Autowired
    private ProjectReportMapper projectReportMapper;

    @Autowired
    private  ProjectMapper projectMapper;

    @Override
    public GaeaBaseMapper<Project> getMapper() {
        return projectMapper;
    }


    @Override
    public void processBatchBeforeOperation(List<Project> entities, BaseOperationEnum operationEnum) throws BusinessException {
        ProjectService.super.processBatchAfterOperation(entities, operationEnum);
        switch (operationEnum) {
            case DELETE_BATCH:
                entities.forEach(project1 -> {
                    Project project = selectOne(project1.getId());
                    projectReportMapper.delete(new QueryWrapper<ProjectReport>().lambda().eq(ProjectReport::getProjectCode,project.getProjectCode()));
                });
                break;
            default:

        }
    }


    @Override
    @Transactional
    public void set(ProjectSetDto dto) {
        if (null == dto.getProjectCode()) {
            throw BusinessExceptionBuilder.build(ResponseCode.NOT_NULL, "项目编码");
        }
        if (CollectionUtils.isEmpty(dto.getReportCodeList())) {
            return;
        }
        if (null == dto.getReportCode()) {
            throw BusinessExceptionBuilder.build(ResponseCode.NOT_NULL, "首页大屏");
        }

        List<String>reportCodes = dto.getReportCodeList();
        String projectCode = dto.getProjectCode();
        Project project = selectOne(new QueryWrapper<Project>().lambda().eq(Project::getProjectCode,projectCode));
        project.setReportCode(dto.getReportCode());
        update(project);
        projectReportMapper.delete(new QueryWrapper<ProjectReport>().lambda().eq(ProjectReport::getProjectCode,projectCode));
        reportCodes.forEach(code->{
            ProjectReport projectReport = new ProjectReport();
            projectReport.setProjectCode(projectCode);
            projectReport.setReportCode(code);
            projectReportMapper.insert(projectReport);
        });
    }
}
