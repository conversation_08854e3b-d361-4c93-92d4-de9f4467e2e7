# 该文件配置会继承bootstrap.xml，只需要配置数据库等差异配置
spring:
  redis:
    host: onecity-os-redis
    port: 6379
    password: DONT(QkOmrw81EnDa0CDf9y1hywwU2sWxykwRkIHa8ZXhBvSUmcPtXUJerFvBv/77k4AxY40uLtxHEcjv86VWFXHNoPxPcGIgG6n10aMBOlsrWLwWZAjlOWr3N4JrG04kk+fPG+57s7x6Zu6algBVF8FWVJfIxkMWMIhU0IZGZ5njtok=)
  datasource:
    url: **************************************************************************************************************
#    url: jdbc:mysql://*************:31403/aj_report?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: root
    password: DONT(QkOmrw81EnDa0CDf9y1hywwU2sWxykwRkIHa8ZXhBvSUmcPtXUJerFvBv/77k4AxY40uLtxHEcjv86VWFXHNoPxPcGIgG6n10aMBOlsrWLwWZAjlOWr3N4JrG04kk+fPG+57s7x6Zu6algBVF8FWVJfIxkMWMIhU0IZGZ5njtok=)
  gaea:
    subscribes:
      oss: #文件存储
        enabled: true
        ##允许上传的文件后缀
        file-type-while-list: .png|.jpg|.gif|.icon|.pdf|.xlsx|.xls|.csv|.mp4|.avi|.jpeg|.aaa|.svg
        # 用于文件上传成功后，生成文件的下载公网完整URL
        downloadPath: http://127.0.0.1:9095/file/download
        nfs:
          path: D:\\aaa\\
# 本应用自定义参数
customer:
  # 开发测试用本地文件，如果是生产，请考虑使用对象存储
  file:
    #上传对应本地全路径，目录必须是真实存在的，注意 Win是 \ 且有盘符，linux是 / 无盘符
    dist-path: /data/minio_data/
#    dist-path: /app/disk/upload/
    #上传对应下载的下载链接路径 http://serverip:9095/file/download
    downloadPath: http://*************:31634/file/download

minio:
  endpoint: http://**************:19000
  accessKey: admin
  secretKey: admin123
  bucketName: ioc-files