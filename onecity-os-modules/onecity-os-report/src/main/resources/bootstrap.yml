server:
  port: 8080
  servlet:
    encoding:
      charset: UTF-8
      force: true
      enabled: true

spring:
  profiles:
    active: dev
  jmx:
    enabled: false
  application:
    name: onecity-os-report
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  servlet:
    multipart:
      max-file-size: 500MB #上传图片大小限制为10MB
      max-request-size: 500MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
  messages:
    basename: i18n/messages
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: onecity-os-nacos:8848
        namespace:
  datasource:
    hikari:
      maximum-pool-size: 10
      minimum-idle: 1
  #数据源连接池配置
  druid:
    initial-size: 10 # 初始化时建立物理连接的个数。初始化发生在显示调用init方法，或者第一次getConnection时
    min-idle: 10 # 最小连接池数量
    maxActive: 200 # 最大连接池数量
    maxWait: 3000 # 获取连接时最大等待时间，单位毫秒。配置了maxWait之后，缺省启用公平锁，并发效率会有所下降，如果需要可以通过配置
    timeBetweenEvictionRunsMillis: 60000 # 关闭空闲连接的检测时间间隔.Destroy线程会检测连接的间隔时间，如果连接空闲时间大于等于minEvictableIdleTimeMillis则关闭物理连接。
    minEvictableIdleTimeMillis: 300000 # 连接的最小生存时间.连接保持空闲而不被驱逐的最小时间
    testWhileIdle: true # 申请连接时检测空闲时间，根据空闲时间再检测连接是否有效.建议配置为true，不影响性能，并且保证安全性。申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRun
    poolPreparedStatements: true # 开启PSCache
    maxPoolPreparedStatementPerConnectionSize: 20 #设置PSCache值
    connectionErrorRetryAttempts: 3 # 连接出错后再尝试连接三次
    breakAfterAcquireFailure: true # 数据库服务宕机自动重连机制
    timeBetweenConnectErrorMillis: 300000 # 连接出错后重试时间间隔
  flyway:
    enabled: true    #是否开启flyway，默认true.
    baseline-on-migrate: true
    #数据库连接配置
    url: ${spring.datasource.url}
    user: ${spring.datasource.username}
    password: ${spring.datasource.password}
    placeholder-replacement: false
    init-sqls:
      - CREATE DATABASE IF NOT EXISTS `aj_report` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
  gaea:
    subscribes:
      oss: #文件存储 都配置的情况下优先级minio->amazonS3->nfs
        enabled: true
        ##允许上传的文件后缀
        file-type-while-list: .png|.jpg|.gif|.icon|.pdf|.xlsx|.xls|.csv|.mp4|.avi|.jpeg|.aaa|.svg
        # 用于文件上传成功后，生成文件的下载公网完整URL，http://serverip:9095/file/download，注意填写IP必须填写后端服务所在的机器IP
        downloadPath: http://*************:9095/file/download
        nfs:
          #上传对应本地全路径，注意目录不会自动创建，注意 Win是 \ 且有盘符，linux是 / 无盘符，注意目录权限问题
          path: /app/disk/upload/
        #若要使用minio文件存储，请启用以下配置
        #minio:
        #  url: http://127.0.0.1
        #  port: 9000
        #  access-key: minioreport
        #  secret-key: minioreport
        #  bucket-name: aj-report
        #若要使用amazonS3文件存储，请启用以下配置
        #amazonS3:
        #  url: http://127.0.0.1
        #  access-key: access-key
        #  secret-key: secret-key
        #  bucket-name: AJ-Report
        #若minio和amazonS3都没有，使用服务器高可用的nfs共享盘
    Security:
      # jwt密钥，生产环境请自行修改，避免被远程伪造登录攻击
      jwtSecret: TybmmfrgsIqpPsBOYxvygCMVJWKNfDJU
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql打印
    call-setters-on-nulls: true
  mapperLocations:
    - classpath*:/mapper/**/*.xml
    - classpath*:/modeler-mybatis-mappings/**/*.xml

management:
  endpoints:
    web:
      base-path: /
logging:
  config: classpath:logback.xml

# 本应用自定义参数
customer:
  # 跳过token验证和权限验证的url清单
  skip-authenticate-urls: /gaeaDict/all, /login, /static, /file/download/, /index.html, /favicon.ico, /reportShare/detailByCode, /v2/api-docs
  file:
    #导入导出临时文件夹 默认.代表当前目录，拼接/tmp_zip/目录
    tmpPath: .
  user:
    ##新增用户默认密码
    default:
      password: 123456
other:
  ALGORITHMSTR: AES/ECB/PKCS5Padding
  AES_KEY: AnjiPLUSAjReport