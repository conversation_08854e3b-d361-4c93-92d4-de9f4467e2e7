CREATE
DATABASE IF NOT EXISTS `aj_report` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
/*
 Navicat Premium Data Transfer

 Source Server         : 大屏工具后端
 Source Server Type    : MySQL
 Source Server Version : 80023
 Source Host           : **************:13306
 Source Schema         : aj_report

 Target Server Type    : MySQL
 Target Server Version : 80023
 File Encoding         : 65001

 Date: 20/06/2022 15:49:24
*/

use aj_report;

-- ----------------------------
-- Table structure for access_authority
-- ----------------------------
DROP TABLE IF EXISTS `access_authority`;
CREATE TABLE `access_authority`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `parent_target` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '父ID',
  `target` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '菜单代码',
  `target_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '菜单名称',
  `action` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '按钮代码',
  `action_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '按钮名称',
  `sort` int(0) NULL DEFAULT NULL,
  `enable_flag` int(0) NOT NULL DEFAULT 1 COMMENT '0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG',
  `delete_flag` int(0) NULL DEFAULT 0 COMMENT ' 0--未删除 1--已删除 DIC_NAME=DEL_FLAG',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '修改人',
  `update_time` datetime(0) NOT NULL COMMENT '修改时间',
  `version` tinyint(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `TA_uniq_index`(`target`, `action`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 327 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '运营权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of access_authority
-- ----------------------------
INSERT INTO `access_authority` VALUES (1, NULL, 'screen', '大屏管理', '', '', 1, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (2, NULL, 'data', '数据管理', '', '', 2, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (3, NULL, 'access', '用户管理', '', '', 3, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (4, NULL, 'system', '系统设置', '', '', 4, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (110, 'access', 'userManage', '用户管理', 'insert', '新增用户', 101, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (111, 'access', 'userManage', '用户管理', 'update', '修改用户', 102, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (112, 'access', 'userManage', '用户管理', 'delete', '删除用户', 103, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (113, 'access', 'userManage', '用户管理', 'query', '查询用户', 104, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (114, 'access', 'userManage', '用户管理', 'resetPassword', '重置密码', 105, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (115, 'access', 'userManage', '用户管理', 'grantRole', '分配角色', 106, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (116, 'access', 'userManage', '用户管理', 'detail', '用户明细', 107, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (120, 'access', 'roleManage', '角色管理', 'insert', '新建角色', 110, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2021-07-17 20:41:46', 2);
INSERT INTO `access_authority` VALUES (121, 'access', 'roleManage', '角色管理', 'update', '修改角色', 111, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (122, 'access', 'roleManage', '角色管理', 'delete', '删除角色', 112, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (123, 'access', 'roleManage', '角色管理', 'query', '查询角色', 113, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (124, 'access', 'roleManage', '角色管理', 'grantAuthority', '分配权限', 114, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (125, 'access', 'roleManage', '角色管理', 'detail', '角色明细', 115, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2021-07-17 20:41:46', 2);
INSERT INTO `access_authority` VALUES (130, 'access', 'authorityManage', '权限管理', 'insert', '新增权限', 121, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (131, 'access', 'authorityManage', '权限管理', 'update', '修改权限', 122, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (132, 'access', 'authorityManage', '权限管理', 'delete', '删除权限', 123, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (133, 'access', 'authorityManage', '权限管理', 'query', '查询权限', 124, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (134, 'access', 'authorityManage', '权限管理', 'detail', '权限明细', 125, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (200, 'data', 'datasourceManage', '数据源管理', 'insert', '新建数据源', 200, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (201, 'data', 'datasourceManage', '数据源管理', 'update', '修改数据源', 201, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (202, 'data', 'datasourceManage', '数据源管理', 'delete', '删除数据源', 202, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (203, 'data', 'datasourceManage', '数据源管理', 'query', '查询数据源', 203, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (204, 'data', 'resultsetManage', '数据集管理', 'insert', '新建数据集', 204, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (205, 'data', 'resultsetManage', '数据集管理', 'update', '修改数据集', 205, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (206, 'data', 'resultsetManage', '数据集管理', 'delete', '删除数据集', 206, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (207, 'data', 'resultsetManage', '数据集管理', 'query', '查询数据集', 207, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (221, 'screen', 'reportManage', '大屏列表', 'insert', '新建报表', 221, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (222, 'screen', 'reportManage', '大屏列表', 'update', '修改报表', 222, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (223, 'screen', 'reportManage', '大屏列表', 'delete', '删除报表', 223, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (224, 'screen', 'reportManage', '大屏列表', 'query', '查询报表', 224, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (225, 'screen', 'reportManage', '大屏列表', 'detail', '报表明细', 225, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (226, 'screen', 'reportManage', '大屏列表', 'copy', '报表复制', 226, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (231, 'screen', 'bigScreenManage', '视图列表', 'share', '分享报表', 231, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (232, 'screen', 'bigScreenManage', '视图列表', 'view', '查看大屏', 232, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (233, 'screen', 'bigScreenManage', '视图列表', 'design', '设计大屏', 233, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (234, 'screen', 'bigScreenManage', '视图列表', 'export', '导出大屏', 234, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (235, 'screen', 'bigScreenManage', '视图列表', 'import', '导入大屏', 235, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (236, 'screen', 'bigScreenManage', '视图列表', 'detail', '大屏明细', 236, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (300, 'system', 'fileManage', '文件管理', 'query', '查询文件', 300, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (301, 'system', 'fileManage', '文件管理', 'upload', '上传文件', 300, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (302, 'system', 'fileManage', '文件管理', 'update', '更新文件', 300, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (303, 'system', 'fileManage', '文件管理', 'delete', '删除文件', 300, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (304, 'system', 'dictManage', '数据字典', 'query', '数据字典查询', 300, 1, 1, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (305, 'system', 'dictManage', '数据字典', 'insert', '数据字典新增', 300, 1, 1, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (306, 'system', 'dictManage', '数据字典', 'update', '更新数据字典', 300, 1, 1, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (307, 'system', 'dictManage', '数据字典', 'delete', '删除数据字典', 300, 1, 1, 'admin', '2019-07-23 15:59:40', 'admin', '2022-05-23 12:01:57', 2);
INSERT INTO `access_authority` VALUES (308, 'system', 'dictManage', '数据字典', 'fresh', '刷新数据字典', 300, 1, 1, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (309, 'system', 'dictItemManage', '数据字典项', 'query', '查询数据字典项', 300, 0, 1, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (310, 'system', 'dictItemManage', '数据字典项', 'insert', '新增数据字典项', 300, 0, 1, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (311, 'system', 'dictItemManage', '数据字典项', 'update', '更新数据字典项', 300, 0, 1, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (312, 'system', 'dictItemManage', '数据字典项', 'delete', '删除数据字典项', 300, 0, 1, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (316, 'data', 'datasourceManage', '数据源管理', 'detail', '数据源明细', 200, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (317, 'data', 'resultsetManage', '数据集管理', 'detail', '数据集明细', 204, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (321, 'system', 'fileManage', '文件管理', 'detail', '文件明细', 300, 1, 0, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (322, 'system', 'dictManage', '数据字典', 'detail', '数据字典明细', 300, 1, 1, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);
INSERT INTO `access_authority` VALUES (323, 'system', 'dictItemManage', '数据字典项', 'detail', '数据字典项明细', 300, 0, 1, 'admin', '2019-07-23 15:59:40', 'admin', '2019-07-23 15:59:40', 1);

-- ----------------------------
-- Table structure for access_role
-- ----------------------------
DROP TABLE IF EXISTS `access_role`;
CREATE TABLE `access_role`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `role_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色编码',
  `role_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色名称',
  `enable_flag` int(0) NOT NULL DEFAULT 1 COMMENT '0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG',
  `delete_flag` int(0) NULL DEFAULT 0 COMMENT ' 0--未删除 1--已删除 DIC_NAME=DEL_FLAG',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `version` tinyint(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '运营角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of access_role
-- ----------------------------
INSERT INTO `access_role` VALUES (1, 'root', '超级管理员', 1, 0, 'root', '2019-07-23 16:00:33', 'root', '2019-07-23 16:00:33', 1);
INSERT INTO `access_role` VALUES (2, 'designer', '设计员', 1, 0, 'root', '2019-07-23 16:00:33', 'root', '2019-07-23 16:00:33', 1);
INSERT INTO `access_role` VALUES (3, 'viewer', '查看员', 1, 0, 'root', '2019-07-23 16:00:33', 'root', '2019-07-23 16:00:33', 1);
INSERT INTO `access_role` VALUES (4, 'test-role', '权限测试', 1, 0, 'test-xth', '2022-06-09 14:46:56', 'test-xth', '2022-06-09 14:46:56', 1);

-- ----------------------------
-- Table structure for access_role_authority
-- ----------------------------
DROP TABLE IF EXISTS `access_role_authority`;
CREATE TABLE `access_role_authority`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `role_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色名称',
  `target` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '权限目标',
  `action` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 883 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '运营角色权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of access_role_authority
-- ----------------------------
INSERT INTO `access_role_authority` VALUES (465, 'designer', 'datasourceManage', 'insert');
INSERT INTO `access_role_authority` VALUES (466, 'designer', 'datasourceManage', 'update');
INSERT INTO `access_role_authority` VALUES (467, 'designer', 'datasourceManage', 'delete');
INSERT INTO `access_role_authority` VALUES (468, 'designer', 'datasourceManage', 'query');
INSERT INTO `access_role_authority` VALUES (469, 'designer', 'resultsetManage', 'insert');
INSERT INTO `access_role_authority` VALUES (470, 'designer', 'resultsetManage', 'update');
INSERT INTO `access_role_authority` VALUES (471, 'designer', 'resultsetManage', 'delete');
INSERT INTO `access_role_authority` VALUES (472, 'designer', 'resultsetManage', 'query');
INSERT INTO `access_role_authority` VALUES (473, 'designer', 'reportManage', 'insert');
INSERT INTO `access_role_authority` VALUES (474, 'designer', 'reportManage', 'update');
INSERT INTO `access_role_authority` VALUES (475, 'designer', 'reportManage', 'delete');
INSERT INTO `access_role_authority` VALUES (476, 'designer', 'reportManage', 'query');
INSERT INTO `access_role_authority` VALUES (477, 'designer', 'bigScreenManage', 'share');
INSERT INTO `access_role_authority` VALUES (478, 'designer', 'bigScreenManage', 'view');
INSERT INTO `access_role_authority` VALUES (479, 'designer', 'bigScreenManage', 'design');
INSERT INTO `access_role_authority` VALUES (704, 'test-role', 'authorityManage', 'detail');
INSERT INTO `access_role_authority` VALUES (705, 'test-role', 'authorityManage', 'insert');
INSERT INTO `access_role_authority` VALUES (706, 'test-role', 'authorityManage', 'update');
INSERT INTO `access_role_authority` VALUES (707, 'test-role', 'authorityManage', 'delete');
INSERT INTO `access_role_authority` VALUES (708, 'test-role', 'authorityManage', 'query');
INSERT INTO `access_role_authority` VALUES (709, 'test-role', 'roleManage', 'insert');
INSERT INTO `access_role_authority` VALUES (710, 'test-role', 'roleManage', 'detail');
INSERT INTO `access_role_authority` VALUES (711, 'test-role', 'roleManage', 'update');
INSERT INTO `access_role_authority` VALUES (712, 'test-role', 'roleManage', 'delete');
INSERT INTO `access_role_authority` VALUES (713, 'test-role', 'roleManage', 'query');
INSERT INTO `access_role_authority` VALUES (714, 'test-role', 'roleManage', 'grantAuthority');
INSERT INTO `access_role_authority` VALUES (715, 'test-role', 'userManage', 'insert');
INSERT INTO `access_role_authority` VALUES (716, 'test-role', 'userManage', 'detail');
INSERT INTO `access_role_authority` VALUES (717, 'test-role', 'userManage', 'update');
INSERT INTO `access_role_authority` VALUES (718, 'test-role', 'userManage', 'delete');
INSERT INTO `access_role_authority` VALUES (719, 'test-role', 'userManage', 'query');
INSERT INTO `access_role_authority` VALUES (720, 'test-role', 'userManage', 'resetPassword');
INSERT INTO `access_role_authority` VALUES (721, 'test-role', 'userManage', 'grantRole');
INSERT INTO `access_role_authority` VALUES (722, 'test-role', 'datasourceManage', 'insert');
INSERT INTO `access_role_authority` VALUES (723, 'test-role', 'datasourceManage', 'detail');
INSERT INTO `access_role_authority` VALUES (724, 'test-role', 'datasourceManage', 'update');
INSERT INTO `access_role_authority` VALUES (725, 'test-role', 'datasourceManage', 'delete');
INSERT INTO `access_role_authority` VALUES (726, 'test-role', 'datasourceManage', 'query');
INSERT INTO `access_role_authority` VALUES (727, 'test-role', 'resultsetManage', 'detail');
INSERT INTO `access_role_authority` VALUES (728, 'test-role', 'resultsetManage', 'insert');
INSERT INTO `access_role_authority` VALUES (729, 'test-role', 'resultsetManage', 'update');
INSERT INTO `access_role_authority` VALUES (730, 'test-role', 'resultsetManage', 'delete');
INSERT INTO `access_role_authority` VALUES (731, 'test-role', 'resultsetManage', 'query');
INSERT INTO `access_role_authority` VALUES (732, 'test-role', 'bigScreenManage', 'share');
INSERT INTO `access_role_authority` VALUES (733, 'test-role', 'bigScreenManage', 'detail');
INSERT INTO `access_role_authority` VALUES (734, 'test-role', 'bigScreenManage', 'view');
INSERT INTO `access_role_authority` VALUES (735, 'test-role', 'bigScreenManage', 'design');
INSERT INTO `access_role_authority` VALUES (736, 'test-role', 'bigScreenManage', 'export');
INSERT INTO `access_role_authority` VALUES (737, 'test-role', 'bigScreenManage', 'import');
INSERT INTO `access_role_authority` VALUES (738, 'test-role', 'bigScreenManage', 'copy');
INSERT INTO `access_role_authority` VALUES (830, 'viewer', 'reportManage', 'query');
INSERT INTO `access_role_authority` VALUES (831, 'viewer', 'bigScreenManage', 'share');
INSERT INTO `access_role_authority` VALUES (832, 'viewer', 'bigScreenManage', 'view');
INSERT INTO `access_role_authority` VALUES (833, 'viewer', 'bigScreenManage', 'query');
INSERT INTO `access_role_authority` VALUES (834, 'viewer', 'datasourceManage', 'query');
INSERT INTO `access_role_authority` VALUES (835, 'viewer', 'resultsetManage', 'query');
INSERT INTO `access_role_authority` VALUES (836, 'root', 'reportManage', 'insert');
INSERT INTO `access_role_authority` VALUES (837, 'root', 'reportManage', 'update');
INSERT INTO `access_role_authority` VALUES (838, 'root', 'reportManage', 'delete');
INSERT INTO `access_role_authority` VALUES (839, 'root', 'reportManage', 'query');
INSERT INTO `access_role_authority` VALUES (840, 'root', 'reportManage', 'detail');
INSERT INTO `access_role_authority` VALUES (841, 'root', 'bigScreenManage', 'share');
INSERT INTO `access_role_authority` VALUES (842, 'root', 'bigScreenManage', 'view');
INSERT INTO `access_role_authority` VALUES (843, 'root', 'bigScreenManage', 'design');
INSERT INTO `access_role_authority` VALUES (844, 'root', 'bigScreenManage', 'export');
INSERT INTO `access_role_authority` VALUES (845, 'root', 'bigScreenManage', 'import');
INSERT INTO `access_role_authority` VALUES (846, 'root', 'bigScreenManage', 'detail');
INSERT INTO `access_role_authority` VALUES (847, 'root', 'bigScreenManage', 'copy');
INSERT INTO `access_role_authority` VALUES (848, 'root', 'bigScreenManage', 'query');
INSERT INTO `access_role_authority` VALUES (849, 'root', 'bigScreenManage', 'insert');
INSERT INTO `access_role_authority` VALUES (850, 'root', 'datasourceManage', 'insert');
INSERT INTO `access_role_authority` VALUES (851, 'root', 'datasourceManage', 'detail');
INSERT INTO `access_role_authority` VALUES (852, 'root', 'datasourceManage', 'update');
INSERT INTO `access_role_authority` VALUES (853, 'root', 'datasourceManage', 'delete');
INSERT INTO `access_role_authority` VALUES (854, 'root', 'datasourceManage', 'query');
INSERT INTO `access_role_authority` VALUES (855, 'root', 'resultsetManage', 'detail');
INSERT INTO `access_role_authority` VALUES (856, 'root', 'resultsetManage', 'insert');
INSERT INTO `access_role_authority` VALUES (857, 'root', 'resultsetManage', 'update');
INSERT INTO `access_role_authority` VALUES (858, 'root', 'resultsetManage', 'delete');
INSERT INTO `access_role_authority` VALUES (859, 'root', 'resultsetManage', 'query');
INSERT INTO `access_role_authority` VALUES (860, 'root', 'userManage', 'insert');
INSERT INTO `access_role_authority` VALUES (861, 'root', 'userManage', 'update');
INSERT INTO `access_role_authority` VALUES (862, 'root', 'userManage', 'delete');
INSERT INTO `access_role_authority` VALUES (863, 'root', 'userManage', 'query');
INSERT INTO `access_role_authority` VALUES (864, 'root', 'userManage', 'resetPassword');
INSERT INTO `access_role_authority` VALUES (865, 'root', 'userManage', 'grantRole');
INSERT INTO `access_role_authority` VALUES (866, 'root', 'userManage', 'detail');
INSERT INTO `access_role_authority` VALUES (867, 'root', 'roleManage', 'insert');
INSERT INTO `access_role_authority` VALUES (868, 'root', 'roleManage', 'update');
INSERT INTO `access_role_authority` VALUES (869, 'root', 'roleManage', 'delete');
INSERT INTO `access_role_authority` VALUES (870, 'root', 'roleManage', 'query');
INSERT INTO `access_role_authority` VALUES (871, 'root', 'roleManage', 'grantAuthority');
INSERT INTO `access_role_authority` VALUES (872, 'root', 'roleManage', 'detail');
INSERT INTO `access_role_authority` VALUES (873, 'root', 'authorityManage', 'insert');
INSERT INTO `access_role_authority` VALUES (874, 'root', 'authorityManage', 'update');
INSERT INTO `access_role_authority` VALUES (875, 'root', 'authorityManage', 'delete');
INSERT INTO `access_role_authority` VALUES (876, 'root', 'authorityManage', 'query');
INSERT INTO `access_role_authority` VALUES (877, 'root', 'authorityManage', 'detail');
INSERT INTO `access_role_authority` VALUES (878, 'root', 'fileManage', 'query');
INSERT INTO `access_role_authority` VALUES (879, 'root', 'fileManage', 'upload');
INSERT INTO `access_role_authority` VALUES (880, 'root', 'fileManage', 'update');
INSERT INTO `access_role_authority` VALUES (881, 'root', 'fileManage', 'delete');
INSERT INTO `access_role_authority` VALUES (882, 'root', 'fileManage', 'detail');

-- ----------------------------
-- Table structure for access_user
-- ----------------------------
DROP TABLE IF EXISTS `access_user`;
CREATE TABLE `access_user`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `login_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT ' 登录名',
  `real_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '真实用户',
  `password` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '密码',
  `phone` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号码',
  `email` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户邮箱',
  `remark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `last_login_time` datetime(0) NULL DEFAULT NULL COMMENT '最后一次登陆时间',
  `last_login_ip` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '最后一次登录IP',
  `enable_flag` int(0) NOT NULL DEFAULT 1 COMMENT '0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG',
  `delete_flag` int(0) NOT NULL DEFAULT 0 COMMENT ' 0--未删除 1--已删除 DIC_NAME=DEL_FLAG',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `version` tinyint(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `IDX1`(`login_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 314 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '运营用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of access_user
-- ----------------------------
INSERT INTO `access_user` VALUES (1, 'admin', '管理员', '70e7de5981b67cfa1b67fead2c41a085', '18019216253', '<EMAIL>', NULL, '2018-08-20 22:04:02', '172.30.16.129', 1, 0, 'admin', '2019-07-23 16:00:38', 'admin', '2022-06-17 16:15:05', 2);
INSERT INTO `access_user` VALUES (2, 'guest', '访客', '8a02e51571e390c84bb14f4cb05daaa7', '18019214578', '<EMAIL>', NULL, '2018-08-20 22:04:02', '172.30.16.129', 1, 0, 'guest', '2019-07-23 16:00:38', 'admin', '2019-07-23 16:00:38', 1);

-- ----------------------------
-- Table structure for access_user_role
-- ----------------------------
DROP TABLE IF EXISTS `access_user_role`;
CREATE TABLE `access_user_role`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `login_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '权限目标',
  `role_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '运营角色权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of access_user_role
-- ----------------------------
INSERT INTO `access_user_role` VALUES (13, 'guest', 'viewer');
INSERT INTO `access_user_role` VALUES (14, 'admin', 'root');
INSERT INTO `access_user_role` VALUES (15, 'admin', 'designer');
INSERT INTO `access_user_role` VALUES (16, 'admin', 'viewer');

-- ----------------------------
-- Table structure for flyway_schema_history
-- ----------------------------
DROP TABLE IF EXISTS `flyway_schema_history`;
CREATE TABLE `flyway_schema_history`  (
  `installed_rank` int(0) NOT NULL,
  `version` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `description` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `script` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `checksum` int(0) NULL DEFAULT NULL,
  `installed_by` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `installed_on` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `execution_time` int(0) NOT NULL,
  `success` tinyint(1) NOT NULL,
  PRIMARY KEY (`installed_rank`) USING BTREE,
  INDEX `flyway_schema_history_s_idx`(`success`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gaea_dict
-- ----------------------------
DROP TABLE IF EXISTS `gaea_dict`;
CREATE TABLE `gaea_dict`  (
  `id` int(0) NOT NULL AUTO_INCREMENT,
  `dict_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '字典名称',
  `dict_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '字典编码',
  `remark` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新用户',
  `update_time` timestamp(0) NULL DEFAULT NULL COMMENT '更新时间',
  `version` int(0) NULL DEFAULT NULL COMMENT '版本',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 61 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '数组字典' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gaea_dict
-- ----------------------------
INSERT INTO `gaea_dict` VALUES (4, '删除状态', 'DELETE_FLAG', NULL, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict` VALUES (6, '是否启用', 'ENABLE_FLAG', NULL, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict` VALUES (7, '文件状态', 'FILE_STATUS', NULL, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict` VALUES (8, '是否启用', 'FILTER_FLAG', NULL, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict` VALUES (9, '数据过滤类型', 'FILTER_TYPE', NULL, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict` VALUES (11, '指标类型', 'ITEM_TYPE', NULL, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict` VALUES (12, '国际化标识', 'LOCALE', 'ssssss', 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-27 22:20:00', 2);
INSERT INTO `gaea_dict` VALUES (35, '数据源类型', 'SOURCE_TYPE', '', 'admin', '2021-03-23 13:16:58', 'admin', '2021-04-13 16:43:30', 4);
INSERT INTO `gaea_dict` VALUES (36, '数据转换类型', 'TRANSFORM_TYPE', NULL, 'admin', '2021-03-23 13:24:28', 'admin', '2021-03-23 13:24:32', 1);
INSERT INTO `gaea_dict` VALUES (37, '是否必填', 'REQUIRED_FLAG', NULL, 'admin', '2021-03-23 13:25:50', 'admin', '2021-03-23 13:25:53', 1);
INSERT INTO `gaea_dict` VALUES (39, '行业类型', 'SYS_CATA_TYPE', '系统所属行业类型', 'admin', '2021-03-25 13:07:38', 'admin', '2021-03-25 13:07:38', 1);
INSERT INTO `gaea_dict` VALUES (40, '报表类型', 'REPORT_TYPE', '报表类型', 'admin', '2021-03-26 11:48:06', 'admin', '2021-03-26 11:48:11', NULL);
INSERT INTO `gaea_dict` VALUES (45, '菜单国际化', 'MENU_LANGUAGE', '菜单国际化配置', 'admin', '2021-04-06 16:33:33', 'admin', '2021-04-06 16:33:33', 1);
INSERT INTO `gaea_dict` VALUES (46, '报表分组', 'REPORT_GROUP', '报表分组', 'admin', '2021-04-07 09:36:40', 'admin', '2021-04-07 09:36:42', 1);
INSERT INTO `gaea_dict` VALUES (47, '报表组件类型', 'DASHBOARD_PANEL_TYPE', '报表组件类型', 'admin', '2021-04-12 10:42:50', 'admin', '2021-04-12 10:42:55', 1);
INSERT INTO `gaea_dict` VALUES (48, '图表属性', 'CHART_PROPERTIES', '报表属性', 'admin', '2021-04-29 10:28:15', 'admin', '2021-06-23 10:47:20', 3);
INSERT INTO `gaea_dict` VALUES (49, '分享有效期', 'SHARE_VAILD', '报表分享', 'admin', '2021-08-18 13:29:19', 'admin', '2021-08-18 13:29:24', 1);
INSERT INTO `gaea_dict` VALUES (50, '柱状图属性', 'BAR_PROPERTIES', '柱状图属性', 'admin', '2021-04-29 10:28:15', 'admin', '2021-06-23 10:47:20', 1);
INSERT INTO `gaea_dict` VALUES (51, '折线图属性', 'LINE_PROPERTIES', '折线图属性', 'admin', '2021-04-29 10:28:15', 'admin', '2021-06-23 10:47:20', 1);
INSERT INTO `gaea_dict` VALUES (52, '柱线图属性', 'BAR_LINE_PROPERTIES', '柱线图属性', 'admin', '2021-04-29 10:28:15', 'admin', '2021-06-23 10:47:20', 1);
INSERT INTO `gaea_dict` VALUES (53, '饼图属性', 'PIE_PROPERTIES', '饼图、漏斗图', 'admin', '2021-04-29 10:28:15', 'admin', '2021-06-23 10:47:20', 1);
INSERT INTO `gaea_dict` VALUES (54, '单文本图属性', 'TEXT_PROPERTIES', '仪表盘、百分比、文本框、滚动文本', 'admin', '2021-04-29 10:28:15', 'admin', '2021-06-23 10:47:20', 1);
INSERT INTO `gaea_dict` VALUES (55, '堆叠图属性', 'STACK_PROPERTIES', '堆叠图属性', 'admin', '2021-04-29 10:28:15', 'admin', '2021-06-23 10:47:20', 1);
INSERT INTO `gaea_dict` VALUES (56, '地图属性', 'MAP_PROPERTIES', '地图属性', 'admin', '2021-04-29 10:28:15', 'admin', '2021-06-23 10:47:20', 1);
INSERT INTO `gaea_dict` VALUES (58, 'XY坐标属性', 'COORD_PROPERTIES', 'XY坐标属性', 'admin', '2022-05-24 22:34:33', 'admin', '2022-05-24 22:34:33', 1);

-- ----------------------------
-- Table structure for gaea_dict_item
-- ----------------------------
DROP TABLE IF EXISTS `gaea_dict_item`;
CREATE TABLE `gaea_dict_item`  (
  `id` int(0) NOT NULL AUTO_INCREMENT,
  `dict_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据字典编码',
  `item_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '字典项名称',
  `item_value` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '字典项值',
  `item_extend` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '字典扩展项',
  `enabled` int(0) NULL DEFAULT 1 COMMENT '1:启用 0:禁用',
  `locale` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '语言标识',
  `remark` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
  `sort` int(0) NULL DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新用户',
  `update_time` timestamp(0) NULL DEFAULT NULL COMMENT '更新时间',
  `version` int(0) NULL DEFAULT NULL COMMENT '版本',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 322 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '数据字典项' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gaea_dict_item
-- ----------------------------
INSERT INTO `gaea_dict_item` VALUES (1, 'AUDIT_FLAG', '待审核', 'waiting', NULL, 1, 'zh', '待审核', 1, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (2, 'AUDIT_FLAG', '审核中', 'ongoing', NULL, 1, 'zh', '审核中', 2, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (3, 'AUDIT_FLAG', '通过', 'approved', NULL, 1, 'zh', '通过', 3, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (4, 'AUDIT_FLAG', '拒绝', 'rejected', NULL, 1, 'zh', '拒绝', 4, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (5, 'DELETE_FLAG', '已删除', '1', NULL, 1, 'zh', '已删除', 5, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-31 12:04:26', 2);
INSERT INTO `gaea_dict_item` VALUES (6, 'DELETE_FLAG', '未删除', '0', NULL, 1, 'zh', '未删除', 6, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-31 12:04:49', 3);
INSERT INTO `gaea_dict_item` VALUES (7, 'ENABLE_FLAG', '禁用', '0', NULL, 1, 'zh', '已禁用', 2, 'admin', '2021-03-09 16:43:09', 'admin', '2021-04-08 14:41:13', 3);
INSERT INTO `gaea_dict_item` VALUES (8, 'ENABLE_FLAG', '启用', '1', NULL, 1, 'zh', '已启用', 1, 'admin', '2021-03-09 16:43:09', 'admin', '2021-04-08 14:41:18', 3);
INSERT INTO `gaea_dict_item` VALUES (9, 'SYSTEM_CODE', 'CTS电脑端', 'CTS-PC', NULL, 1, 'zh', 'CTS电脑端', 9, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (10, 'SYSTEM_CODE', 'CTS App端', 'CTS-APP', NULL, 1, 'zh', 'CTS App端', 10, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (11, 'FILTER_FLAG', '已禁用', '0', NULL, 1, 'zh', '已禁用', 7, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (12, 'FILTER_FLAG', '已启用', '1', NULL, 1, 'zh', '已启用', 8, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (13, 'FILTER_TYPE', 'js脚本', 'jsCalc', NULL, 1, 'zh', 'js脚本', 3, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (14, 'FILTER_TYPE', '新增字段', 'addField', NULL, 1, 'zh', '新增字段', 3, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (15, 'FILTER_TYPE', '替换字段', 'replaceField', NULL, 1, 'zh', '替换字段', 3, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (16, 'FILTER_TYPE', 'ip解析java处理', 'ipTransform', NULL, 1, 'zh', 'ip解析java处理', 3, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (17, 'FILTER_TYPE', 'vpn在线时长java处理', 'vpnTransform', NULL, 1, 'zh', 'vpn在线时长java处理', 3, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (18, 'SETTING_TYPE', '字符串', 'input', NULL, 1, 'zh', '字符串', 10, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (19, 'SETTING_TYPE', '数字', 'input-number', NULL, 1, 'zh', '数字', 10, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (20, 'SETTING_TYPE', '文本区域', 'textarea', NULL, 1, 'zh', '文本区域', 10, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (21, 'SETTING_TYPE', '数据字典', 'code-select', NULL, 1, 'zh', '数据字典', 10, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (22, 'SETTING_TYPE', '下拉列表', 'select', NULL, 1, 'zh', '下拉列表', 10, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (23, 'SETTING_TYPE', '单选按钮', 'radio-group', NULL, 1, 'zh', '单选按钮', 10, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (24, 'SETTING_TYPE', '多选按钮', 'checkbox-group', NULL, 1, 'zh', '多选按钮', 10, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (25, 'SETTING_TYPE', '自定义表单', 'custom-form', NULL, 1, 'zh', '自定义表单', 10, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (86, 'FILE_STATUS', '生成中', 'creating', NULL, 1, 'zh', '生成中', 3, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (87, 'FILE_STATUS', '生成成功', 'success', NULL, 1, 'zh', '生成成功', 3, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (88, 'FILE_STATUS', '生成失败', 'failed', NULL, 1, 'zh', '生成失败', 3, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (89, 'LOCALE', '中文', 'zh', NULL, 1, 'zh', '中文', NULL, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (90, 'LOCALE', '英文', 'en', NULL, 1, 'zh', '英文', NULL, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (91, 'LOCALE', 'Chinese', 'zh', NULL, 1, 'en', 'Chinese', NULL, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (92, 'LOCALE', 'English', 'en', NULL, 1, 'en', 'English', 1, 'admin', '2021-03-09 16:43:09', 'admin', '2021-03-09 16:43:09', 1);
INSERT INTO `gaea_dict_item` VALUES (140, 'REQUIRED_FLAG', '必填', '1', NULL, 1, 'zh', NULL, 2, 'admin', '2021-03-23 10:54:08', 'admin', '2021-03-23 10:54:08', 1);
INSERT INTO `gaea_dict_item` VALUES (141, 'REQUIRED_FLAG', '非必填', '0', NULL, 1, 'zh', NULL, 2, 'admin', '2021-03-23 10:54:08', 'admin', '2021-03-23 10:54:08', 1);
INSERT INTO `gaea_dict_item` VALUES (150, 'TRANSFORM_TYPE', 'js脚本', 'js', NULL, 1, 'zh', NULL, 2, 'admin', '2021-03-23 10:54:08', 'admin', '2021-03-23 10:54:08', 1);
INSERT INTO `gaea_dict_item` VALUES (151, 'TRANSFORM_TYPE', 'java脚本', 'javaBean', NULL, 1, 'zh', NULL, 2, 'admin', '2021-03-23 10:54:08', 'admin', '2021-03-23 10:54:08', 1);
INSERT INTO `gaea_dict_item` VALUES (152, 'TRANSFORM_TYPE', '字典翻译', 'dict', '{\"dict1\": {\"1\": \"男\",\"0\": \"女\"},\"dict2\": {\"mysql\": \"mysql\",\"sqlserver\": \"sqlserver\"}}', 1, 'zh', NULL, 2, 'admin', '2021-03-23 10:54:08', 'admin', '2021-03-23 10:54:08', 1);
INSERT INTO `gaea_dict_item` VALUES (153, 'SYS_CATA_TYPE', '行业01', '1001', NULL, 1, 'zh', NULL, 1, 'admin', '2021-03-25 13:10:32', 'admin', '2021-03-25 13:10:32', 1);
INSERT INTO `gaea_dict_item` VALUES (154, 'SYS_CATA_TYPE', '行业02', '1002', NULL, 1, 'zh', NULL, 2, 'admin', '2021-03-25 13:11:01', 'admin', '2021-03-25 13:11:01', 1);
INSERT INTO `gaea_dict_item` VALUES (158, 'REPORT_TYPE', 'excel报表', 'report_excel', NULL, 1, 'zh', NULL, NULL, 'admin', '2021-03-26 11:49:28', 'admin', '2021-03-26 11:50:19', NULL);
INSERT INTO `gaea_dict_item` VALUES (159, 'REPORT_TYPE', '大屏报表', 'report_screen', NULL, 1, 'zh', NULL, NULL, 'admin', '2021-03-26 11:50:25', 'admin', '2021-03-26 11:50:21', NULL);
INSERT INTO `gaea_dict_item` VALUES (230, 'REPORT_GROUP', '分组1', 'group_1', NULL, 1, 'zh', NULL, 1, 'admin', '2021-04-07 09:39:45', 'admin', '2021-04-13 16:44:31', 2);
INSERT INTO `gaea_dict_item` VALUES (231, 'REPORT_GROUP', '分组2', 'group_2', NULL, 1, 'zh', NULL, 1, 'admin', '2021-04-07 09:40:55', 'admin', '2021-04-07 09:40:52', 1);
INSERT INTO `gaea_dict_item` VALUES (232, 'INF_STATUS', '审核通过', '5', NULL, 1, 'zh', NULL, 25, 'admin', '2021-04-07 11:02:42', 'admin', '2021-04-07 15:01:35', 3);
INSERT INTO `gaea_dict_item` VALUES (233, 'RULE_PARAM_TYPE', 'int', 'int', NULL, 1, 'zh', NULL, 3, 'zouya', '2021-04-09 09:43:22', 'zouya', '2021-04-09 09:43:22', 1);
INSERT INTO `gaea_dict_item` VALUES (234, 'PUSH_TYPE', 'APP端', 'appsp', NULL, 1, 'zh', 'App端推送采用推送平台Appsp', 4, 'admin', '2021-04-09 10:13:33', 'admin', '2021-04-09 10:14:11', 3);
INSERT INTO `gaea_dict_item` VALUES (240, 'DASHBOARD_PANEL_TYPE', '文本', 'text', NULL, 1, 'zh', 'App端推送采用推送平台Appsp', 4, 'admin', '2021-04-09 10:13:33', 'admin', '2021-04-09 10:14:11', 3);
INSERT INTO `gaea_dict_item` VALUES (241, 'DASHBOARD_PANEL_TYPE', '滚动文本', '\r\nscrollingText', NULL, 1, 'zh', 'App端推送采用推送平台Appsp', 4, 'admin', '2021-04-09 10:13:33', 'admin', '2021-04-09 10:14:11', 3);
INSERT INTO `gaea_dict_item` VALUES (242, 'DASHBOARD_PANEL_TYPE', '超链接', 'hyperlinks', NULL, 1, 'zh', 'App端推送采用推送平台Appsp', 4, 'admin', '2021-04-09 10:13:33', 'admin', '2021-04-09 10:14:11', 3);
INSERT INTO `gaea_dict_item` VALUES (243, 'DASHBOARD_PANEL_TYPE', '当前时间', 'currentTime', NULL, 1, 'zh', 'App端推送采用推送平台Appsp', 4, 'admin', '2021-04-09 10:13:33', 'admin', '2021-04-09 10:14:11', 3);
INSERT INTO `gaea_dict_item` VALUES (244, 'DASHBOARD_PANEL_TYPE', '图片', 'picture', NULL, 1, 'zh', 'App端推送采用推送平台Appsp', 4, 'admin', '2021-04-09 10:13:33', 'admin', '2021-04-09 10:14:11', 3);
INSERT INTO `gaea_dict_item` VALUES (245, 'DASHBOARD_PANEL_TYPE', '轮播图片', 'rotatePictures', NULL, 1, 'zh', 'App端推送采用推送平台Appsp', 4, 'admin', '2021-04-09 10:13:33', 'admin', '2021-04-09 10:14:11', 3);
INSERT INTO `gaea_dict_item` VALUES (246, 'MENU_LANGUAGE', '图标库', 'SvgDemo', '', 1, 'zh', '', 1, 'admin', '2021-04-06 16:34:10', 'admin', '2021-04-06 16:34:10', 1);
INSERT INTO `gaea_dict_item` VALUES (247, 'MENU_LANGUAGE', '代码生成', 'Generator', NULL, 1, 'zh', '', 1, 'admin', '2021-04-06 16:34:10', 'admin', '2021-04-06 16:34:10', 1);
INSERT INTO `gaea_dict_item` VALUES (248, 'MENU_LANGUAGE', '项目列表', 'Project', '', 1, 'zh', '', 1, 'admin', '2021-04-06 16:34:10', 'admin', '2021-04-06 16:34:10', 1);
INSERT INTO `gaea_dict_item` VALUES (249, 'MENU_LANGUAGE', '生成示例-单表', 'AlipayConfig', '', 1, 'zh', '', 1, 'admin', '2021-04-06 16:34:10', 'admin', '2021-04-06 16:34:10', 1);
INSERT INTO `gaea_dict_item` VALUES (250, 'MENU_LANGUAGE', '设备信息-主表', 'DeviceInfo', '', 1, 'zh', '', 1, 'admin', '2021-04-06 16:34:10', 'admin', '2021-04-06 16:34:10', 1);
INSERT INTO `gaea_dict_item` VALUES (251, 'MENU_LANGUAGE', '设备类型-子表', 'DeviceModel', '', 1, 'zh', '', 1, 'admin', '2021-04-06 16:34:10', 'admin', '2021-04-06 16:34:10', 1);
INSERT INTO `gaea_dict_item` VALUES (252, 'MENU_LANGUAGE', '设备日志-子表', 'DeviceLogDetail', '', 1, 'zh', '', 1, 'admin', '2021-04-06 16:34:10', 'admin', '2021-04-06 16:34:10', 1);
INSERT INTO `gaea_dict_item` VALUES (253, 'MENU_LANGUAGE', 'API文档', 'API', '', 1, 'zh', '', 1, 'admin', '2021-04-06 16:34:10', 'admin', '2021-04-06 16:34:10', 1);
INSERT INTO `gaea_dict_item` VALUES (254, 'MENU_LANGUAGE', '系统通告', 'Announcement', '', 1, 'zh', '', 1, 'admin', '2021-04-06 16:34:10', 'admin', '2021-04-06 16:34:10', 1);
INSERT INTO `gaea_dict_item` VALUES (255, 'REPORT_GROUP', '分组3', 'group_3', NULL, 1, 'zh', NULL, 3, 'admin', '2021-04-13 16:45:28', 'admin', '2021-04-13 16:45:28', 1);
INSERT INTO `gaea_dict_item` VALUES (257, 'MENU_LANGUAGE', '文件管理', 'FileManagement', NULL, 1, 'zh', NULL, 1, 'admin', '2021-04-27 13:57:58', 'admin', '2021-04-27 13:57:58', 1);
INSERT INTO `gaea_dict_item` VALUES (260, 'CHART_PROPERTIES', 'x轴字段', 'xAxis', NULL, 1, 'zh', NULL, 1, 'admin', '2021-12-16 11:09:19', 'admin', '2021-12-16 11:09:19', 1);
INSERT INTO `gaea_dict_item` VALUES (261, 'CHART_PROPERTIES', '柱状', 'bar', NULL, 1, 'zh', NULL, 6, 'admin', '2021-04-29 10:48:43', 'admin', '2021-04-29 10:48:50', 1);
INSERT INTO `gaea_dict_item` VALUES (262, 'CHART_PROPERTIES', '折线', 'line', NULL, 1, 'zh', NULL, 7, 'admin', '2021-04-29 10:48:43', 'admin', '2021-04-29 10:48:50', 1);
INSERT INTO `gaea_dict_item` VALUES (263, 'CHART_PROPERTIES', '饼图/仪表盘/气泡地图name', 'name', NULL, 1, 'zh', NULL, 8, 'admin', '2021-04-29 10:48:43', 'admin', '2021-04-29 10:48:50', 1);
INSERT INTO `gaea_dict_item` VALUES (264, 'CHART_PROPERTIES', '饼图/仪表盘/气泡地图value', 'value', NULL, 1, 'zh', NULL, 9, 'admin', '2021-04-29 10:48:43', 'admin', '2021-04-29 10:48:50', 1);
INSERT INTO `gaea_dict_item` VALUES (265, 'CHART_PROPERTIES', '时间轴-时', 'xAxis-hour', NULL, 1, 'zh', NULL, 2, 'admin', '2021-04-29 10:48:43', 'admin', '2021-04-29 10:48:50', 1);
INSERT INTO `gaea_dict_item` VALUES (266, 'CHART_PROPERTIES', '时间轴-天', 'xAxis-day', NULL, 1, 'zh', NULL, 3, 'admin', '2021-04-29 10:48:43', 'admin', '2021-04-29 10:48:50', 1);
INSERT INTO `gaea_dict_item` VALUES (267, 'CHART_PROPERTIES', '时间轴-月', 'xAxis-month', NULL, 1, 'zh', NULL, 4, 'admin', '2021-04-29 10:48:43', 'admin', '2021-04-29 10:48:50', 1);
INSERT INTO `gaea_dict_item` VALUES (268, 'CHART_PROPERTIES', '时间轴-年', 'xAxis-year', NULL, 1, 'zh', NULL, 5, 'admin', '2021-04-29 10:48:43', 'admin', '2021-04-29 10:48:50', 1);
INSERT INTO `gaea_dict_item` VALUES (269, 'CHART_PROPERTIES', '文本数字', 'text', NULL, 1, 'zh', NULL, 10, 'admin', '2021-12-16 11:09:16', 'admin', '2021-12-16 11:09:16', 1);
INSERT INTO `gaea_dict_item` VALUES (270, 'SOURCE_TYPE', 'mysql', 'mysql', '[{\"label\":\"driverName\",\"value\":\"com.mysql.cj.jdbc.Driver\",\"labelValue\":\"驱动类\"},{\"label\":\"jdbcUrl\",\"value\":\"**************************************************************************************************\",\"labelValue\":\"连接串\"},{\"label\":\"username\",\"value\":\"root\",\"labelValue\":\"用户名\"},{\"label\":\"password\",\"value\":\"root\",\"labelValue\":\"密码\"}]', 1, 'zh', NULL, 1, '管理员', '2021-03-23 10:54:08', 'lixiaoyan', '2021-03-26 13:22:05', 3);
INSERT INTO `gaea_dict_item` VALUES (271, 'SOURCE_TYPE', 'oracle', 'oracle', '[{\"label\":\"driverName\",\"value\":\"oracle.jdbc.driver.OracleDriver\",\"labelValue\":\"驱动类\"},{\"label\":\"jdbcUrl\",\"value\":\"***************************************\",\"labelValue\":\"连接串\"},{\"label\":\"username\",\"value\":\"root\",\"labelValue\":\"用户名\"},{\"label\":\"password\",\"value\":\"root\",\"labelValue\":\"密码\"}]', 1, 'zh', NULL, 2, 'admin', '2021-03-23 10:54:08', 'admin', '2021-03-23 10:54:08', 1);
INSERT INTO `gaea_dict_item` VALUES (272, 'SOURCE_TYPE', 'mssqlserver', 'mssqlserver', '[{\"label\":\"driverName\",\"value\":\"com.microsoft.sqlserver.jdbc.SQLServerDriver\",\"labelValue\":\"驱动类\"},{\"label\":\"jdbcUrl\",\"value\":\"****************************************************\",\"labelValue\":\"连接串\"},{\"label\":\"username\",\"value\":\"root\",\"labelValue\":\"用户名\"},{\"label\":\"password\",\"value\":\"root\",\"labelValue\":\"密码\"}]', 1, 'zh', NULL, 3, 'admin', '2021-03-23 10:54:08', 'admin', '2021-03-23 10:54:08', 1);
INSERT INTO `gaea_dict_item` VALUES (273, 'SOURCE_TYPE', 'elasticsearch_sql', 'elasticsearch_sql', '[{\"label\":\"apiUrl\",\"value\":\"http://*************:9200/_xpack/sql?format=json\",\"labelValue\":\"请求路径\"},{\"label\":\"method\",\"value\":\"POST\",\"labelValue\":\"请求方式\"},{\"label\":\"header\",\"value\":\"{\\\"Content-Type\\\":\\\"application/json\\\"}\",\"labelValue\":\"请求头\"},{\"label\":\"body\",\"value\":\"{\\\"query\\\":\\\"select 1\\\"}\",\"labelValue\":\"请求体\"}]', 1, 'zh', NULL, 11, '管理员', '2021-03-23 10:54:08', 'admin', '2021-04-13 13:12:33', 9);
INSERT INTO `gaea_dict_item` VALUES (274, 'SOURCE_TYPE', 'kudu impala', 'kudu_impala', '[{\"label\":\"driverName\",\"value\":\"com.cloudera.impala.jdbc41.Driver\",\"labelValue\":\"驱动类\"},{\"label\":\"jdbcUrl\",\"value\":\"************************************\",\"labelValue\":\"连接串\"},{\"label\":\"username\",\"value\":\"\",\"labelValue\":\"用户名\"},{\"label\":\"password\",\"value\":\"\",\"labelValue\":\"密码\"}]', 1, 'zh', NULL, 12, 'admin', '2021-03-23 10:54:08', 'admin', '2021-04-01 09:18:09', 3);
INSERT INTO `gaea_dict_item` VALUES (275, 'SOURCE_TYPE', 'jdbc', 'jdbc', '[{\"label\":\"driverName\",\"value\":\"com.mysql.cj.jdbc.Driver\",\"labelValue\":\"驱动类\"},{\"label\":\"jdbcUrl\",\"value\":\"**************************************************************************************************\",\"labelValue\":\"连接串\"},{\"label\":\"username\",\"value\":\"root\",\"labelValue\":\"用户名\"},{\"label\":\"password\",\"value\":\"root\",\"labelValue\":\"密码\"}]', 1, 'zh', NULL, 21, '管理员', '2021-04-13 17:26:38', 'admin', '2021-04-13 17:26:38', 1);
INSERT INTO `gaea_dict_item` VALUES (276, 'SOURCE_TYPE', 'http', 'http', '[{\"label\":\"apiUrl\",\"value\":\"https://gateway.test.com/api/getdata\",\"labelValue\":\"请求路径\"},{\"label\":\"method\",\"value\":\"POST\",\"labelValue\":\"请求方式\"},{\"label\":\"header\",\"value\":\"{\\\"Content-Type\\\":\\\"application/json;charset=UTF-8\\\"}\",\"labelValue\":\"请求头\"},{\"label\":\"body\",\"value\":\"{\\\"username\\\":\\\"admin\\\",\\\"password\\\":\\\"de12878c0ef5beb7d8848c3af8f54afb\\\",\\\"verifyCode\\\":\\\"\\\"}\",\"labelValue\":\"请求体\"}]', 1, 'zh', NULL, 29, '管理员', '2021-04-13 17:26:38', 'admin', '2021-04-13 17:26:38', 1);
INSERT INTO `gaea_dict_item` VALUES (288, 'CHART_PROPERTIES', 'y轴字段', 'yAxis', NULL, 1, 'zh', NULL, 11, 'admin', '2021-07-05 15:33:59', 'admin', '2021-07-05 15:33:59', 1);
INSERT INTO `gaea_dict_item` VALUES (289, 'SHARE_VAILD', '永久有效', '0', NULL, 1, 'zh', NULL, 1, 'admin', '2021-08-18 13:30:21', 'admin', '2021-08-18 13:30:21', 1);
INSERT INTO `gaea_dict_item` VALUES (290, 'SHARE_VAILD', '1天', '1', NULL, 1, 'zh', NULL, 2, 'admin', '2021-08-18 13:30:39', 'admin', '2021-08-18 13:30:39', 1);
INSERT INTO `gaea_dict_item` VALUES (291, 'SHARE_VAILD', '7天', '7', NULL, 1, 'zh', NULL, 3, 'admin', '2021-08-18 13:30:51', 'admin', '2021-08-18 13:30:56', 2);
INSERT INTO `gaea_dict_item` VALUES (292, 'SHARE_VAILD', '30天', '30', NULL, 1, 'zh', NULL, 4, 'admin', '2021-08-18 13:31:11', 'admin', '2021-08-18 13:31:11', 1);
INSERT INTO `gaea_dict_item` VALUES (293, 'BAR_PROPERTIES', 'x轴字段', 'xAxis', NULL, 1, 'zh', NULL, 1, 'admin', '2021-08-20 10:19:35', 'admin', '2021-08-20 10:19:35', 1);
INSERT INTO `gaea_dict_item` VALUES (294, 'BAR_PROPERTIES', '柱状', 'bar', NULL, 1, 'zh', NULL, 2, 'admin', '2021-08-20 10:19:56', 'admin', '2021-08-20 10:19:56', 1);
INSERT INTO `gaea_dict_item` VALUES (295, 'LINE_PROPERTIES', 'x轴字段', 'xAxis', NULL, 1, 'zh', NULL, 1, 'admin', '2021-08-20 10:27:39', 'admin', '2021-08-20 10:27:39', 1);
INSERT INTO `gaea_dict_item` VALUES (296, 'LINE_PROPERTIES', '折线', 'line', NULL, 1, 'zh', NULL, 2, 'admin', '2021-08-20 10:27:49', 'admin', '2021-08-20 10:27:49', 1);
INSERT INTO `gaea_dict_item` VALUES (297, 'BAR_LINE_PROPERTIES', 'x轴字段', 'xAxis', NULL, 1, 'zh', NULL, 1, 'admin', '2021-08-20 10:31:51', 'admin', '2021-08-20 10:31:51', 1);
INSERT INTO `gaea_dict_item` VALUES (298, 'BAR_LINE_PROPERTIES', 'x轴时间轴-时', 'xAxis-hour', NULL, 1, 'zh', NULL, 2, 'admin', '2021-08-20 10:32:11', 'admin', '2021-08-20 10:32:11', 1);
INSERT INTO `gaea_dict_item` VALUES (299, 'BAR_LINE_PROPERTIES', 'x轴时间轴-天', 'xAxis-day', NULL, 1, 'zh', NULL, 3, 'admin', '2021-08-20 10:32:25', 'admin', '2021-08-20 10:32:25', 1);
INSERT INTO `gaea_dict_item` VALUES (300, 'BAR_LINE_PROPERTIES', 'x轴时间轴-月', 'xAxis-month', NULL, 1, 'zh', NULL, 4, 'admin', '2021-08-20 10:32:38', 'admin', '2021-08-20 10:32:38', 1);
INSERT INTO `gaea_dict_item` VALUES (301, 'BAR_LINE_PROPERTIES', '时间轴-年', 'xAxis-year', NULL, 1, 'zh', NULL, 5, 'admin', '2021-08-20 10:32:52', 'admin', '2021-08-20 10:32:52', 1);
INSERT INTO `gaea_dict_item` VALUES (302, 'BAR_LINE_PROPERTIES', '柱状', 'bar', NULL, 1, 'zh', NULL, 6, 'admin', '2021-08-20 10:33:02', 'admin', '2021-08-20 10:33:02', 1);
INSERT INTO `gaea_dict_item` VALUES (303, 'BAR_LINE_PROPERTIES', '折线', 'line', NULL, 1, 'zh', NULL, 7, 'admin', '2021-08-20 10:33:11', 'admin', '2021-08-20 10:33:11', 1);
INSERT INTO `gaea_dict_item` VALUES (304, 'PIE_PROPERTIES', '名称name', 'name', NULL, 1, 'zh', NULL, 1, 'admin', '2021-08-20 10:35:27', 'admin', '2021-08-20 10:35:27', 1);
INSERT INTO `gaea_dict_item` VALUES (305, 'PIE_PROPERTIES', '数值value', 'value', NULL, 1, 'zh', NULL, 2, 'admin', '2021-08-20 10:35:38', 'admin', '2021-08-20 10:35:38', 1);
INSERT INTO `gaea_dict_item` VALUES (306, 'TEXT_PROPERTIES', '文本数字', 'text', NULL, 1, 'zh', NULL, 1, 'admin', '2021-08-20 10:36:04', 'admin', '2021-08-20 10:36:04', 1);
INSERT INTO `gaea_dict_item` VALUES (307, 'STACK_PROPERTIES', 'x轴字段', 'xAxis', NULL, 1, 'zh', NULL, 1, 'admin', '2021-08-20 10:31:51', 'admin', '2021-08-20 10:31:51', 1);
INSERT INTO `gaea_dict_item` VALUES (308, 'STACK_PROPERTIES', 'x轴时间轴-时', 'xAxis-hour', NULL, 1, 'zh', NULL, 2, 'admin', '2021-08-20 10:32:11', 'admin', '2021-08-20 10:32:11', 1);
INSERT INTO `gaea_dict_item` VALUES (309, 'STACK_PROPERTIES', 'x轴时间轴-天', 'xAxis-day', NULL, 1, 'zh', NULL, 3, 'admin', '2021-08-20 10:32:25', 'admin', '2021-08-20 10:32:25', 1);
INSERT INTO `gaea_dict_item` VALUES (310, 'STACK_PROPERTIES', 'x轴时间轴-月', 'xAxis-month', NULL, 1, 'zh', NULL, 4, 'admin', '2021-08-20 10:32:38', 'admin', '2021-08-20 10:32:38', 1);
INSERT INTO `gaea_dict_item` VALUES (311, 'STACK_PROPERTIES', '时间轴-年', 'xAxis-year', NULL, 1, 'zh', NULL, 5, 'admin', '2021-08-20 10:32:52', 'admin', '2021-08-20 10:32:52', 1);
INSERT INTO `gaea_dict_item` VALUES (312, 'STACK_PROPERTIES', 'y轴字段', 'yAxis', NULL, 1, 'zh', NULL, 6, 'admin', '2021-08-20 10:32:52', 'admin', '2021-08-20 10:32:52', 1);
INSERT INTO `gaea_dict_item` VALUES (313, 'STACK_PROPERTIES', '柱状', 'bar', NULL, 1, 'zh', NULL, 7, 'admin', '2021-08-20 10:33:02', 'admin', '2021-08-20 10:33:02', 1);
INSERT INTO `gaea_dict_item` VALUES (314, 'STACK_PROPERTIES', '折线', 'line', NULL, 1, 'zh', NULL, 8, 'admin', '2021-08-20 10:33:11', 'admin', '2021-08-20 10:33:11', 1);
INSERT INTO `gaea_dict_item` VALUES (315, 'MAP_PROPERTIES', '名称name', 'name', NULL, 1, 'zh', NULL, 1, 'admin', '2021-08-20 10:41:00', 'admin', '2021-08-20 10:41:00', 1);
INSERT INTO `gaea_dict_item` VALUES (316, 'MAP_PROPERTIES', '数值value', 'value', NULL, 1, 'zh', NULL, 2, 'admin', '2021-08-20 10:41:11', 'admin', '2021-08-20 10:41:11', 1);
INSERT INTO `gaea_dict_item` VALUES (317, 'SET_TYPE', 'sql', 'sql', NULL, 1, 'zh', NULL, NULL, 'admin', '2021-11-16 14:43:42', 'admin', '2021-11-16 14:43:42', 1);
INSERT INTO `gaea_dict_item` VALUES (318, 'SET_TYPE', 'http', 'http', NULL, 1, 'zh', NULL, NULL, 'admin', '2021-11-16 14:43:51', 'admin', '2021-11-16 14:43:51', 1);
INSERT INTO `gaea_dict_item` VALUES (319, 'COORD_PROPERTIES', '数据', 'series', NULL, 1, 'zh', NULL, NULL, 'admin', '2022-05-24 22:34:33', 'admin', '2022-05-24 22:34:33', 1);
INSERT INTO `gaea_dict_item` VALUES (320, 'COORD_PROPERTIES', 'X轴', 'xAxis', NULL, 1, 'zh', NULL, NULL, 'admin', '2022-05-24 22:34:33', 'admin', '2022-05-24 22:34:33', 1);
INSERT INTO `gaea_dict_item` VALUES (321, 'COORD_PROPERTIES', 'Y轴', 'yAxis', NULL, 1, 'zh', NULL, NULL, 'admin', '2022-05-24 22:34:33', 'admin', '2022-05-24 22:34:33', 1);

-- ----------------------------
-- Table structure for gaea_file
-- ----------------------------
DROP TABLE IF EXISTS `gaea_file`;
CREATE TABLE `gaea_file`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `file_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生成的唯一uuid',
  `file_type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件类型，字典FILE_TYPE',
  `file_path` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件在linux中的完整目录，比如/app/dist/export/excel/${fileid}.xlsx',
  `url_path` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '通过接口的下载完整http路径',
  `file_instruction` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件内容说明，比如 对账单(202001~202012)',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` timestamp(0) NULL DEFAULT NULL,
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `update_time` timestamp(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
  `version` int(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 870 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for gaea_report
-- ----------------------------
DROP TABLE IF EXISTS `gaea_report`;
CREATE TABLE `gaea_report`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `report_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `report_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '报表编码',
  `report_group` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分组',
  `report_type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '报表类型',
  `report_image` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '报表缩略图',
  `report_desc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '报表描述',
  `report_author` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '报表作者',
  `download_count` bigint(0) NULL DEFAULT NULL COMMENT '报表下载次数',
  `enable_flag` int(0) NULL DEFAULT 1 COMMENT '0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG',
  `delete_flag` int(0) NULL DEFAULT 0 COMMENT '0--未删除 1--已删除 DIC_NAME=DELETE_FLAG',
  `create_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `version` int(0) NULL DEFAULT NULL COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UNIQUE_REPORT_CODE`(`report_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 216 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for gaea_report_dashboard
-- ----------------------------
DROP TABLE IF EXISTS `gaea_report_dashboard`;
CREATE TABLE `gaea_report_dashboard`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '看板id',
  `report_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '报表编码',
  `title` varchar(254) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '看板标题',
  `width` bigint(0) NULL DEFAULT NULL COMMENT '宽度px',
  `height` bigint(0) NULL DEFAULT NULL COMMENT '高度px',
  `background_color` varchar(24) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '背景色',
  `background_image` varchar(254) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '背景图片',
  `preset_line` varchar(4096) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工作台中的辅助线',
  `refresh_seconds` int(0) NULL DEFAULT NULL COMMENT '自动刷新间隔秒',
  `enable_flag` int(0) NULL DEFAULT 1 COMMENT '0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG',
  `delete_flag` int(0) NULL DEFAULT 0 COMMENT ' 0--未删除 1--已删除 DIC_NAME=DEL_FLAG',
  `sort` int(0) NULL DEFAULT 0 COMMENT '排序，降序',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `create_time` datetime(0) NULL DEFAULT NULL,
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  `version` int(0) NULL DEFAULT NULL COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UNIQUE_REPORT_CODE`(`report_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 291 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for gaea_report_dashboard_widget
-- ----------------------------
DROP TABLE IF EXISTS `gaea_report_dashboard_widget`;
CREATE TABLE `gaea_report_dashboard_widget`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '组件id',
  `report_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '报表编码',
  `type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组件类型参考字典DASHBOARD_PANEL_TYPE',
  `setup` varchar(4096) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组件的渲染属性json',
  `data` varchar(4096) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组件的数据属性json',
  `collapse` varchar(4096) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组件的配置属性json',
  `position` varchar(4096) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组件的大小位置属性json',
  `options` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'options配置项',
  `refresh_seconds` int(0) NULL DEFAULT NULL COMMENT '自动刷新间隔秒',
  `enable_flag` int(0) NULL DEFAULT 1 COMMENT '0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG',
  `delete_flag` int(0) NULL DEFAULT 0 COMMENT ' 0--未删除 1--已删除 DIC_NAME=DEL_FLAG',
  `sort` bigint(0) NULL DEFAULT 0 COMMENT '排序，图层的概念',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `create_time` datetime(0) NULL DEFAULT NULL,
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  `version` int(0) NULL DEFAULT NULL COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9209 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for gaea_report_data_set
-- ----------------------------
DROP TABLE IF EXISTS `gaea_report_data_set`;
CREATE TABLE `gaea_report_data_set`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `set_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据集编码',
  `set_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据集名称',
  `set_desc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据集描述',
  `source_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据源编码',
  `dyn_sentence` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '动态查询sql或者接口中的请求体',
  `case_result` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '结果案例',
  `enable_flag` int(0) NULL DEFAULT 1 COMMENT '0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG',
  `delete_flag` int(0) NULL DEFAULT 0 COMMENT '0--未删除 1--已删除 DIC_NAME=DELETE_FLAG',
  `create_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `version` int(0) NULL DEFAULT NULL,
  `set_type` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_set_code`(`set_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 79 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '数据集管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gaea_report_data_set_param
-- ----------------------------
DROP TABLE IF EXISTS `gaea_report_data_set_param`;
CREATE TABLE `gaea_report_data_set_param`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `set_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据集编码',
  `param_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数名',
  `param_desc` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数描述',
  `param_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数类型，字典=',
  `sample_item` varchar(1080) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数示例项',
  `required_flag` int(0) NULL DEFAULT 1 COMMENT '0--非必填 1--必填 DIC_NAME=REQUIRED_FLAG',
  `validation_rules` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'js校验字段值规则，满足校验返回 true',
  `order_num` int(0) NULL DEFAULT NULL COMMENT '排序',
  `enable_flag` int(0) NULL DEFAULT 1 COMMENT '0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG',
  `delete_flag` int(0) NULL DEFAULT 0 COMMENT '0--未删除 1--已删除 DIC_NAME=DELETE_FLAG',
  `create_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `version` int(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 62 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '数据集查询参数' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gaea_report_data_set_transform
-- ----------------------------
DROP TABLE IF EXISTS `gaea_report_data_set_transform`;
CREATE TABLE `gaea_report_data_set_transform`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `set_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据集编码',
  `transform_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据转换类型，DIC_NAME=TRANSFORM_TYPE; js，javaBean，字典转换',
  `transform_script` varchar(10800) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据转换script,处理逻辑',
  `order_num` int(0) NULL DEFAULT NULL COMMENT '排序,执行数据转换顺序',
  `enable_flag` int(0) NULL DEFAULT 1 COMMENT '0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG',
  `delete_flag` int(0) NULL DEFAULT 0 COMMENT '0--未删除 1--已删除 DIC_NAME=DELETE_FLAG',
  `create_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `version` int(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 50 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '数据集数据转换' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for gaea_report_data_source
-- ----------------------------
DROP TABLE IF EXISTS `gaea_report_data_source`;
CREATE TABLE `gaea_report_data_source`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `source_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据源编码',
  `source_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据源名称',
  `source_desc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据源描述',
  `source_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据源类型 DIC_NAME=SOURCE_TYPE; mysql，orace，sqlserver，elasticsearch，接口，javaBean，数据源类型字典中item-extend动态生成表单',
  `source_config` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据源连接配置json：关系库{ jdbcUrl:\'\', username:\'\', password:\'\' } ES{ hostList:\'ip1:9300,ip2:9300,ip3:9300\', clusterName:\'elasticsearch_cluster\' }  接口{ apiUrl:\'http://ip:port/url\', method:\'\' } javaBean{ beanNamw:\'xxx\' }',
  `enable_flag` int(0) NULL DEFAULT 1 COMMENT '0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG',
  `delete_flag` int(0) NULL DEFAULT 0 COMMENT '0--未删除 1--已删除 DIC_NAME=DELETE_FLAG',
  `create_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `version` int(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_source_code`(`source_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '数据源管理' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for gaea_report_excel
-- ----------------------------
DROP TABLE IF EXISTS `gaea_report_excel`;
CREATE TABLE `gaea_report_excel`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `report_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '报表编码',
  `set_codes` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据集编码，以|分割',
  `set_param` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据集查询参数',
  `json_str` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '报表json串',
  `enable_flag` int(0) NULL DEFAULT 1 COMMENT '0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG',
  `delete_flag` int(0) NULL DEFAULT 0 COMMENT '0--未删除 1--已删除 DIC_NAME=DELETE_FLAG',
  `create_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `version` int(0) NULL DEFAULT NULL COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UNIQUE_REPORT_CODE`(`report_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 216 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;



-- ----------------------------
-- Table structure for gaea_report_share
-- ----------------------------
DROP TABLE IF EXISTS `gaea_report_share`;
CREATE TABLE `gaea_report_share`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `share_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分享编码，系统生成，默认UUID',
  `share_valid_type` int(0) NULL DEFAULT NULL COMMENT '分享有效期类型，DIC_NAME=SHARE_VAILD',
  `share_valid_time` datetime(0) NULL DEFAULT NULL COMMENT '分享有效期',
  `share_token` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分享token',
  `share_url` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分享url',
  `report_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '报表编码',
  `enable_flag` int(0) NULL DEFAULT 1 COMMENT '0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG',
  `delete_flag` int(0) NULL DEFAULT 0 COMMENT '0--未删除 1--已删除 DIC_NAME=DELETE_FLAG',
  `create_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `version` int(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UNIQUE_SHARE_CODE`(`share_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '报表分享' ROW_FORMAT = Dynamic;
