500=Server error
200=success

User.password.error=Password error
user.password.config.password.canot.equal=user password config password canot equal
user.inconsistent.password.error=user inconsistent password error
user.old.password.error=user old password error

1013=The code does not allow duplication
2001=File names are not allowed to be empty
2002=Unsupported suffix type
2003=File upload failed:{0}
2004=File does not exist
file.operation.failed=File operation failed\uFF1A{0}

field.not.null={} can not be null
field.not.empty={} can not be empty
field.min={} must great then {}
field.max={} must less then {}
field.dict.error={} value error

login.error=username or password error
User.token.expired=User token has expired

3001=Template code does not allow duplication
3002=The receiver is not allowed to be empty
Insert.failure=Insert failure
Update.failure=Update failure\uFF0Ccheck version
Delete.failure=Delete failure
Dict.item.code.exist=Dict item code exist
Rule.execute.param.null=Rule execute param null
Rule.content.compile.error=Rule content compile error
Rule.content.execute.error=Rule content execute error
Rule.content.not.exist=Rule content not exist
Rule.code.exist=Rule code exist
Rule.fields.not.exist=Rule fields not exist
Rule.field.value.is.required=Rule field value is required
Rule.field.value.type.error=Rule field value type error
Rule.fields.check.error=Rule fields check error
Component.load.check.error={0} Component not load
4001=Data source connection failed {0}
4002=Data source type is not currently supported
4003=execute sql error, {0}
4004=Incomplete parameter replacement values {0}
4005=execute js error {0}
4006=analysis data error {0}
4007=The report code does not allow duplication
4008=The set code does not allow duplication
4009=The source code does not allow duplication {0}
4010=Can't auto find match driver class
4011=execute javaBean error {0}

report.share.link.invalid=report share link invalid
