500=\u7CFB\u7EDF\u5F02\u5E38
200=\u64CD\u4F5C\u6210\u529F

User.password.error=\u5BC6\u7801\u4E0D\u6B63\u786E
user.password.config.password.canot.equal=\u65B0\u5BC6\u7801\u4E0D\u80FD\u548C\u539F\u5BC6\u7801\u4E00\u81F4
user.inconsistent.password.error=\u5BC6\u7801\u548C\u786E\u8BA4\u5BC6\u7801\u4E0D\u4E00\u81F4
user.old.password.error=\u65E7\u5BC6\u7801\u4E0D\u6B63\u786E
1013=\u7F16\u7801\u4E0D\u5141\u8BB8\u91CD\u590D
2001=\u6587\u4EF6\u540D\u4E0D\u5141\u8BB8\u4E3A\u7A7A
2002=\u6587\u4EF6\u7C7B\u578B\u4E0D\u652F\u6301
2003=\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25\uFF1A{0}
2004=\u6587\u4EF6\u4E0D\u5B58\u5728
file.operation.failed=\u6587\u4EF6\u64CD\u4F5C\u5931\u8D25\uFF1A{0}

field.not.null={}\u4E0D\u80FD\u4E3Anull
field.not.empty={}\u4E0D\u80FD\u4E3A\u7A7A\u5B57\u7B26\u4E32
field.min={}\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E{}
field.max={}\u5FC5\u987B\u5C0F\u4E8E\u7B49\u4E8E{}
field.dict.error={}\u5C5E\u6027

login.error=\u7528\u6237\u540D\u6216\u8005\u5BC6\u7801\u4E0D\u6B63\u786E
User.token.expired=\u7528\u6237\u672A\u767B\u5F55\u6216\u767B\u5F55\u4FE1\u606F\u8FC7\u671F


3001=\u6A21\u677F\u4EE3\u7801\u4E0D\u5141\u8BB8\u91CD\u590D
3002=\u63A5\u6536\u4EBA\u4E0D\u5141\u8BB8\u4E3A\u7A7A
Dict.item.code.exist=\u6570\u636E\u5B57\u5178\u9879\u503C\u5DF2\u5B58\u5728
Insert.failure=\u63D2\u5165\u5931\u8D25
Update.failure=\u66F4\u65B0\u5931\u8D25\uFF0C\u68C0\u67E5\u7248\u672C\u53F7\u662F\u5426\u4E00\u81F4
Delete.failure=\u5220\u9664\u5931\u8D25
Rule.execute.param.null=\u89C4\u5219\u6267\u884C\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
Rule.content.compile.error=\u89C4\u5219\u5185\u5BB9\u7F16\u8BD1\u5931\u8D25
Rule.content.execute.error=\u89C4\u5219\u6267\u884C\u5931\u8D25
Rule.code.exist=\u89C4\u5219\u7F16\u7801\u5DF2\u5B58\u5728
Rule.content.not.exist=\u5BF9\u5E94\u89C4\u5219\u5185\u5BB9\u4E0D\u5B58\u5728
Rule.fields.not.exist=\u5BF9\u5E94\u89C4\u5219\u5B57\u6BB5\u503C\u4E0D\u5B58\u5728
Rule.field.value.is.required=\u89C4\u5219\u5B57\u6BB5\u5FC5\u586B
Rule.field.value.type.error=\u89C4\u5219\u5B57\u6BB5\u503C\u7C7B\u578B\u9519\u8BEF
Rule.fields.check.error=\u89C4\u5219\u53C2\u6570\u6821\u9A8C\u4E0D\u901A\u8FC7
Component.load.check.error={0}\u7EC4\u4EF6\u672A\u52A0\u8F7D

4001=\u6570\u636E\u6E90\u8FDE\u63A5\u5931\u8D25\uFF0C{0}
4002=\u6570\u636E\u6E90\u7C7B\u578B\u6682\u4E0D\u652F\u6301
4003=\u6267\u884Csql\u5931\u8D25\uFF0C{0}
4004=\u53C2\u6570\u66FF\u6362\u503C\u4E0D\u5168\uFF0C{0}
4005=\u6267\u884Cjs\u5931\u8D25\uFF0C{0}
4006=\u89E3\u6790\u6570\u636E\u5931\u8D25\uFF0C{0}
4007=\u62A5\u8868\u7F16\u7801\u4E0D\u5141\u8BB8\u91CD\u590D
4008=\u6570\u636E\u96C6\u7F16\u7801\u4E0D\u5141\u8BB8\u91CD\u590D
4009=\u6570\u636E\u6E90\u7F16\u7801\u4E0D\u5141\u8BB8\u91CD\u590D
4010=\u9A71\u52A8\u5305\u4E0D\u5B58\u5728\uFF0C{0}
4011=\u6267\u884CjavaBean\u5931\u8D25\uFF0C{0}
6001={0}

7001=\u89E3\u6790\u5931\u8D25

report.share.link.invalid=\u5206\u4EAB\u94FE\u63A5\u5DF2\u5931\u6548
