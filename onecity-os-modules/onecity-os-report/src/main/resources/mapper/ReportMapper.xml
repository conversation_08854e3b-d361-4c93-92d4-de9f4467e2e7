<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.anjiplus.template.gaea.business.modules.report.dao.ReportMapper">

    <resultMap type="com.anjiplus.template.gaea.business.modules.report.dao.entity.Report" id="ReportMap">
        <!--jdbcType="{column.columnType}"-->
        <result property="id" column="id"  />
        <result property="reportName" column="report_name"  />
        <result property="reportCode" column="report_code"  />
        <result property="setCods" column="set_codes"  />
        <result property="reportGroup" column="report_group"  />
        <result property="reportDesc" column="report_desc"  />
        <result property="reportType" column="report_type"  />
        <result property="enableFlag" column="enable_flag"  />
        <result property="deleteFlag" column="delete_flag"  />
        <result property="createBy" column="create_by"  />
        <result property="createTime" column="create_time"  />
        <result property="updateBy" column="update_by"  />
        <result property="updateTime" column="update_time"  />
        <result property="version" column="version"  />

    </resultMap>

    <sql id="Base_Column_List">
        id,report_name,report_code,json_str,enable_flag,delete_flag,create_by,create_time,update_by,update_time,version
    </sql>

    <!--自定义sql -->

</mapper>
