package com.onecity.os.supervise.controller;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.supervise.modules.superviseNote.entity.RemindNote;
import com.onecity.os.supervise.modules.superviseNote.entity.RemindRecipientRel;
import com.onecity.os.supervise.modules.superviseNote.vo.RemindVo;
import com.onecity.os.supervise.service.RemindNoteService;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 红灯提醒信息api入口
 *
 * @Author: zack
 * @Date: 2022/5/19 17:36
 */
@Slf4j
@RestController
@RequestMapping("/app/remind")
@Api(tags = "红灯提醒信息api入口")
public class AppRemindNoteController extends BaseController {

    @Resource
    private RemindNoteService remindNoteService;

    /**
     * 新增/编辑，数据集接口
     * @return 成功message
     */
    @PostMapping("/saveData")
    @ApiOperation("新建/编辑")
    public BaseResult<RemindNote> saveData(@RequestBody @Valid RemindNote remind,
                                           @RequestParam(name = "userId") String userId,
                                           @RequestParam(name = "userName") String userName) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isNotEmpty(loginUser)){
            return remindNoteService.saveData(remind, loginUser.getUserid().toString(), loginUser.getSysUser().getUserName());
        }else {
            return BaseResult.fail("获取用户失败，请联系管理员");
        }
    }

    /**
     * 红灯信息信息发起人查询接口
     * @return 成功message
     */
    @GetMapping("/list/creator")
    @ApiOperation("红灯信息信息发起人查询接口-用于红灯信息发起页面查询界面")
    public TableDataInfo queryPageListByCreator(RemindVo remind) {
        startPage();
        List<RemindNote> RemindNotes = remindNoteService.queryByCreator(remind, remind.getUserId());
        return getDataTable(RemindNotes);
    }

    /**
     * 红灯信息信息接受人查询接口
     * @return 成功message
     */
    @GetMapping("/list/recipient")
    @ApiOperation("红灯信息信息接收人查询接口-用于红灯信息接收页面查询界面")
    public TableDataInfo queryPageListByRecipient(RemindVo remind) {
        startPage();
        List<RemindNote> RemindNotes = remindNoteService.queryByRecipient(remind, remind.getUserId(), "0");
        return getDataTable(RemindNotes);
    }

    /**
     * 根据状态查询待办数量
     * @return 成功message
     */
    @GetMapping("/count/recipient")
    @ApiOperation("红灯信息信息责任人人查询接口-用于红灯信息待办页面查询界面")
    public BaseResult<Integer> countRecipientForPending(RemindVo remind) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isNotEmpty(loginUser)) {
            List<RemindNote> remindNotes = remindNoteService.countRecipientForPending(remind, loginUser.getUserid().toString(), "0");
            return BaseResult.ok(remindNotes.size());
        }else {
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
    }

    /**
     * 红灯信息接收行操作接口
     * 包括转发，反馈功能
     * @return 成功message
     */
    @PostMapping("/recipientOperation")
    @ApiOperation("红灯信息接收行操作接口 (转发(FORWARD)/反馈(FEEDBACK))")
    public BaseResult<String> recipientOperation(@RequestBody @Valid RemindRecipientRel rel) {
        return remindNoteService.recipientOperation(rel, rel.getOperation());
    }

    /**
     * 红灯信息信息详情接口
     * userId有值-表示接受人纬度查询；为空，则表示发起人纬度查询
     * @return 成功message
     */
    @GetMapping("/details")
    @ApiOperation("红灯信息信息详情接口")
    public BaseResult<RemindNote> queryDetails(@RequestParam(name = "id") String id,
                                               @RequestParam(name = "userId", required = false) String userId) {
        if(StringUtils.isNotEmpty(userId)) {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (ObjectUtils.isNotEmpty(loginUser)) {
                userId = loginUser.getUserid().toString();
            }
            return remindNoteService.queryDetails(id, userId);
        }else {
            return remindNoteService.queryDetails(id, userId);
        }
    }


    /**
     * 获取最近选择接受人/区分PC/APP
     * @return 成功message
     */
    @GetMapping("/recipientList")
    @ApiOperation("查询最近选择接收人,返回结构为<id,name>")
    public BaseResult<Map<String, String>> queryRecipientList(@RequestParam(name = "userId") String userId,
                                                              @RequestParam(name = "source") String source) {
        return remindNoteService.queryRecipientList(userId, source);
    }

    /**
     * 根据发起人，指标id查询红灯信息
     * @return 成功message
     */
    @GetMapping("/queryIndicatorHistory")
    @ApiOperation("根据发起人，指标id查询红灯信息 红灯提醒")
    public BaseResult<?> queryIndicatorHistory(@RequestParam(name = "creator") String creator,
                                          @RequestParam(name = "indicatorId") String indicatorId) {
        List<RemindNote> remindNote = remindNoteService.queryByIndicator(creator, indicatorId);
        return BaseResult.ok(remindNote);
    }

}
