package com.onecity.os.supervise.controller;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.supervise.annotation.RepeatSubmit;
import com.onecity.os.supervise.modules.superviseNote.entity.SuperviseNote;
import com.onecity.os.supervise.modules.superviseNote.entity.SuperviseRecipientRel;
import com.onecity.os.supervise.modules.superviseNote.vo.SuperviseNoteParam;
import com.onecity.os.supervise.modules.superviseNote.vo.SuperviseVo;
import com.onecity.os.supervise.service.SuperviseNoteService;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 督办信息api入口
 *
 * @Author: zack
 * @Date: 2022/5/19 17:36
 */
@Slf4j
@RestController
@RequestMapping("/app/supervise")
@Api(tags = "督办信息api入口")
public class AppSuperviseNoteController extends BaseController {

    @Resource
    private SuperviseNoteService superviseNoteService;

    /**
     * 新增/编辑，数据集接口
     * @return 成功message
     */
    @PostMapping("/saveData")
    @ApiOperation("新建/编辑")
    public BaseResult<SuperviseNote> saveData(@RequestBody @Valid SuperviseNote superviseNote) {
        return superviseNoteService.saveData(superviseNote);
    }

    /**
     * 督办信息发起人查询接口
     * @return 成功message
     */
    @GetMapping("/list/creator")
    @ApiOperation("督办信息发起人查询接口-用于督办发起页面查询界面")
    public TableDataInfo queryPageListByCreator(SuperviseVo supervise) {
        startPage();
        List<SuperviseNote> superviseNotes = new ArrayList<>();LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return getDataTable(superviseNotes);
        }
        superviseNotes = superviseNoteService.queryByCreator(supervise, loginUser.getUserid().toString());
        return getDataTable(superviseNotes);
    }

    /**
     * 督办信息接受人查询接口
     * @return 成功message
     */
    @GetMapping("/list/recipient")
    @ApiOperation("督办信息接收人查询接口-用于督办接收页面查询界面")
    public TableDataInfo queryPageListByRecipient(SuperviseVo supervise) {
        startPage();
        List<SuperviseNote> superviseNotes = new ArrayList<>();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return getDataTable(superviseNotes);
        }
        superviseNotes = superviseNoteService.queryByRecipient(supervise, loginUser.getUserid().toString(), "0");
        return getDataTable(superviseNotes);
    }

    /**
     * 根据状态查询待办数量
     * @return 成功message
     */
    @GetMapping("/count/recipient")
    @ApiOperation("根据状态查询待办数量")
    public BaseResult<Integer> countRecipientForPending(SuperviseVo supervise) {
        List<SuperviseNote> superviseNotes = new ArrayList<>();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        superviseNotes = superviseNoteService.countRecipientForPending(supervise, loginUser.getUserid().toString(), "0");
        return BaseResult.ok(superviseNotes.size());
    }

    /**
     * 督办接收行操作接口
     * 包括转发，反馈功能
     * @return 成功message
     */
    @PostMapping("/recipientOperation")
    @ApiOperation("督办接收行操作接口 (转发(FORWARD)/反馈(FEEDBACK))")
    @RepeatSubmit(interval = 5000, timeUnit = TimeUnit.MILLISECONDS, message = "接口调用频繁，请稍后再试。")
    public BaseResult<String> recipientOperation(@RequestBody @Valid SuperviseRecipientRel rel) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        rel.setUserId(loginUser.getUserid().toString());
        rel.setUserName(loginUser.getSysUser().getNickName());
        return superviseNoteService.recipientOperation(rel, rel.getOperation());
    }

    /**
     * 督办信息详情接口
     * userId有值-表示接受人纬度查询；为空，则表示发起人纬度查询
     * @return 成功message
     */
    @GetMapping("/details")
    @ApiOperation("督办信息详情接口")
    public BaseResult<SuperviseNote> queryDetails(@RequestParam(name = "id") String id,
                                                  @RequestParam(name = "userId", required = false) String userId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        return superviseNoteService.queryDetails(id, loginUser.getUserid().toString());
    }


    /**
     * 获取最近选择接受人/区分PC/APP
     * @return 成功message
     */
    @GetMapping("/recipientList")
    @ApiOperation("查询最近选择接收人,返回结构为<id,name>")
    public BaseResult<Map<String, String>> queryRecipientList(@RequestParam(name = "userId") String userId,
                                                              @RequestParam(name = "source") String source) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        return superviseNoteService.queryRecipientList(loginUser.getUserid().toString(), source);
    }

    /**
     * 根据发起人，指标id查询督办
     * @return 成功message
     */
    @GetMapping("/queryIndicatorHistory")
    @ApiOperation("根据发起人，指标id查询督办")
    public BaseResult<?> queryIndicatorHistory(@RequestParam(name = "creator") String creator,
                                               @RequestParam(name = "indicatorId") String indicatorId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (ObjectUtils.isNotEmpty(loginUser)){
            List<SuperviseNote> superviseNote = superviseNoteService.queryByIndicator(loginUser.getUserid().toString(), indicatorId);
            return BaseResult.ok(superviseNote);
        }else {
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
    }
}
