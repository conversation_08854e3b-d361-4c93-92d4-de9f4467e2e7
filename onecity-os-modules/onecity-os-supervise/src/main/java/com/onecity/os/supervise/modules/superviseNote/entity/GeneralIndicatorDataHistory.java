package com.onecity.os.supervise.modules.superviseNote.entity;

import com.onecity.os.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 指标数据表历史数据对象 general_indicator_data_history
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
@Data
public class GeneralIndicatorDataHistory
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 指标Id */
    private String indicatorId;

    /** 指标名称 */
    private String indicatorName;

    /** 指标项目 */
    private String itemName;

    /** 指标项目值 */
    private String itemValue;

    /** 指标项目值1 */
    private String itemValue1;

    /** 指标项目值1 */
    private String itemValue2;

    /** 指标项目值1 */
    private String itemValue3;

    /** 指标项目值1 */
    private String itemValue4;

    /** 指标项目值1 */
    private String itemValue5;

    /** 指标项目值1 */
    private String itemValue6;

    /** 单位 */
    private String itemUnit;

    /** 第二单位 */
    private String itemUnit2nd;

    /** 用来表示指标数据是增加还是减少，前端显示不同的颜色 */
    private String identify;

    /** 指标文字展示方式，0：平铺；1：加横线 */
    private Long style;

    /** 是否折行0：否1：是 */
    private Long isFold;

    /** 排序 */
    private Long sequence;

    /** 更新日期 */
    private String updateDate;

    /** 是否当前展示0：是1：否 */
    private Long currentFlag;

    /** 是否删除0:否1:是 */
    private Integer isDelete;

    /** 创建人 */
    private String creater;

    /** 更新人 */
    private String updater;

    /** 约定更新日期 */
    private String planUpdateDate;
}
