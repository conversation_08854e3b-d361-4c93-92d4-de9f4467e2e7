package com.onecity.os.supervise.modules.superviseNote.entity;

import com.onecity.os.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 指标数据数据项目对象 general_indicator_data_title_history
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
@Data
public class GeneralIndicatorDataTitleHistory
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private String id;

    /** 指标Id */
    private String indicatorId;

    /** 指标名称 */
    private String indicatorName;

    /** 指标项目 */
    private String itemName;

    /** 是否删除0:否1:是 */
    private Integer isDelete;

    /** 创建人 */
    private String creator;

    /** 更新人 */
    private String updater;

    /** 指标项目名称 */
    private String itemNameName;

    /** 指标值名称 */
    private String itemValueName;

    /** 是否为主值项（主值-1；副值-0） */
    private String mainValue;

    /** 指标值字段名（对应指标数据表中字段名） */
    private String columnName;

    /** 排序 */
    private Long sequence;
}
