package com.onecity.os.supervise.modules.superviseNote.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 红灯提醒信息日志表
 *
 * @Author: zack
 * @Date: 2022/5/18 15:59
 */
@Data
@Table(name = "remind_note_log")
public class RemindNoteLog {

    /**
     * 主键自增
     **/
    @Id
    @GeneratedValue
    private String id;

    /**
     * 督办信息id
     */
    @Column(name = "remind_id")
    private String remindId;

    /**
     * 操作
     */
    @Column(name = "operate")
    private String operate;

    /**
     * 操作人id
     */
    @Column(name = "operator")
    private String operator;

    /**
     * 操作人名称
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 转发接收人
     */
    @Column(name = "recipients")
    private String recipients;

    /**
     * 操作时间
     */
    @Column(name = "operation_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date operationTime;


    /**
     * 备用字段
     */
    @Column(name = "backup")
    private String backup;
}
