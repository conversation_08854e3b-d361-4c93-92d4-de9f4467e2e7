package com.onecity.os.supervise.modules.superviseNote.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 督办信息表附件关联表
 *
 * @Author: zack
 * @Date: 2022/5/18 16:55
 */
@Data
@Table(name ="supervise_file_rel")
public class SuperviseFileRel {

    /**
     * 主键自增
     **/
    @Id
    @GeneratedValue
    private String id;

    /**
     * 督办信息id
     */
    @Column(name = "supervise_Id")
    private String superviseId;

    /**
     * 文件上传时间
     */
    @Column(name = "upload_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date uploadTime;

    /**
     * 操作人id
     */
    @Column(name = "operator")
    private String operator;

    /**
     * 操作人名称
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 文件名称
     */
    @Column(name = "file_name")
    private String fileName;

    /**
     * 文件路径前缀
     */
    @Column(name = "file_path")
    private String filePath;

    /**
     * 文件路径
     */
    @Column(name = "file_url")
    private String fileUrl;

    /**
     * 文件类型
     * 包括 JPG PNG HEIC
     */
    @Column(name = "file_type")
    private String fileType;

    /**
     * 状态
     * ACTIVE = 生效； DELETED = 删除
     */
    @Column(name = "state")
    private String state;
}
