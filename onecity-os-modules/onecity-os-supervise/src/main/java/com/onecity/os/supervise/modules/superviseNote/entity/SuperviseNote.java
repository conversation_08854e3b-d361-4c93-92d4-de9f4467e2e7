package com.onecity.os.supervise.modules.superviseNote.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.onecity.os.supervise.modules.superviseNote.vo.LogVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 督办信息表
 * supervise_note
 *
 * @Author: zack
 * @Date: 2022/5/18 15:59
 */
@Data
@Table(name = "supervise_note")
public class SuperviseNote implements Serializable {

    /**
     * 主键自增
     **/
    @Id
    @GeneratedValue
    private String id;

    /**
     * 创建人id
     */
    @Column(name = "creator")
    @ApiModelProperty(value = "创建人id")
    @NotNull(message = "创建人信息不可为空")
    private String creator;

    /**
     * 创建人名称
     */
    @Column(name = "creator_name")
    @ApiModelProperty(value = "创建人名称")
    @NotNull(message = "创建人名称不可为空")
    private String creatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", example = "1990-01-01 00:00:00")
    private java.util.Date createTime;

    /**
     * 状态
     */
    @Column(name = "state")
    @ApiModelProperty(value = "状态",
            notes = "DRAFT = 草稿；PUBLISH = 发布；EDIT = 编辑; REVOKE = 撤回；URGE = 催办； FORWARD = 转发； FEEDBACK = 反馈 " +
                    "COMPLETE = 完结； DELETED = 删除",
            example = "PENDING")
    private String state;

    /**
     * 内容
     */
    @Column(name = "content")
    @ApiModelProperty(value = "内容")
    private String content;

    /**
     * 督办附件url
     * 记录当前生效附件地址，便于使用
     */
    @Column(name = "file_url")
    @ApiModelProperty(value = "督办附件url")
    private String fileUrl;

    /**
     * 督办附件id
     * 记录supervise_recipient_relation表中的id
     */
    @Column(name = "file_id")
    @ApiModelProperty(value = "督办附件id")
    private String fileId;

    /**
     * 督办附件名称
     */
    @Column(name = "file_name")
    @ApiModelProperty(value = "督办附件名称")
    private String fileName;

    /**
     * 发布时间
     */
    @Column(name = "publish_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发布时间", example = "1990-01-01 00:00:00")
    private java.util.Date publishTime;

    /**
     * 修改人id
     */
    @Column(name = "updater")
    @ApiModelProperty(value = "修改人id")
    private String updater;

    /**
     * 修改人名称
     */
    @Column(name = "updater_name")
    @ApiModelProperty(value = "修改人名称")
    private String updaterName;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间", example = "1990-01-01 00:00:00")
    private java.util.Date updateTime;

    /**
     * 备用字段
     */
    @Column(name = "backup")
    private String backup;

    /**
     * 接受人信息
     */
    @Transient
    @ApiModelProperty(value = "接受人信息（创建，编辑时该字段为必传字段）")
    private List<SuperviseRecipientRel> recipients;

    /**
     * 接受人
     */
    @Transient
    @ApiModelProperty(value = "接受人信息（详情接口返回字段）")
    private Map<String, String> recipient;

    /**
     * 责任人
     */
    @Transient
    @ApiModelProperty(value = "责任人信息（详情接口返回字段）")
    private Map<String, String> responsible;

    /**
     * 操作日志
     */
    @Transient
    @ApiModelProperty(value = "操作日志（详情接口返回字段）")
    private List<SuperviseNoteLog> superviseLog;

    /**
     * 催办内容
     */
    @Transient
    @ApiModelProperty(value = "催办内容")
    private String urgeContent;

    /**
     * 前端获取到的当前时间
     */
    @Transient
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "前端获取到的当前时间", example = "1990-01-01 00:00:00")
    private java.util.Date localTime;

    /**
     * 关联指标Id
     */
    @Column(name = "indicator_id")
    @ApiModelProperty(value = "关联指标Id")
    private String indicatorId;

    /**
     * 关联指标Id
     */
    @Column(name = "indicator_msg")
    @ApiModelProperty(value = "关联指标信息")
    private String indicatorMsg;

    /**
     * 关联指标Id
     */
    @Column(name = "indicator_url")
    @ApiModelProperty(value = "关联指标数据图片信息")
    private String indicatorUrl;

    /**
     * 关联指标生效标识
     */
    @Column(name = "indicator_flag")
    @ApiModelProperty(value = "关联指标生效标识 true-生效  false-失效")
    private String indicatorFlag;

    /**
     * 是否可催办标记 传ture标识可催办，传false标识不可催办，默认为ture
     * 催办按钮显示判断追加逻辑；如果所有接受都是 反馈FEEDBACK/转发FORWARD状态，就不允许点催办按钮
     */
    @Transient
    @ApiModelProperty(value = "是否可催办标记")
    private boolean urge;
}
