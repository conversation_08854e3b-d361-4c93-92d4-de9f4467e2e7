package com.onecity.os.supervise.modules.superviseNote.mapper;

import com.onecity.os.supervise.modules.superviseNote.entity.GeneralIndicatorDataHistory;

import java.util.List;

/**
 * 指标数据表历史数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
public interface GeneralIndicatorDataHistoryMapper 
{
    /**
     * 查询指标数据表历史数据
     * 
     * @param id 指标数据表历史数据主键
     * @return 指标数据表历史数据
     */
     GeneralIndicatorDataHistory selectGeneralIndicatorDataHistoryById(Long id);

    /**
     * 查询指标数据表历史数据列表
     * 
     * @param generalIndicatorDataHistory 指标数据表历史数据
     * @return 指标数据表历史数据集合
     */
     List<GeneralIndicatorDataHistory> selectGeneralIndicatorDataHistoryList(GeneralIndicatorDataHistory generalIndicatorDataHistory);

    /**
     * 新增指标数据表历史数据
     * 
     * @param generalIndicatorDataHistory 指标数据表历史数据
     * @return 结果
     */
     int insertGeneralIndicatorDataHistory(GeneralIndicatorDataHistory generalIndicatorDataHistory);

    /**
     * 修改指标数据表历史数据
     * 
     * @param generalIndicatorDataHistory 指标数据表历史数据
     * @return 结果
     */
     int updateGeneralIndicatorDataHistory(GeneralIndicatorDataHistory generalIndicatorDataHistory);

    /**
     * 删除指标数据表历史数据
     * 
     * @param id 指标数据表历史数据主键
     * @return 结果
     */
     int deleteGeneralIndicatorDataHistoryById(Long id);

    /**
     * 批量删除指标数据表历史数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deleteGeneralIndicatorDataHistoryByIds(String[] ids);
}
