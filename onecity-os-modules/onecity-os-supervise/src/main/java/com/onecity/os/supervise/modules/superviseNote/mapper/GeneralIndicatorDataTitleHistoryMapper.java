package com.onecity.os.supervise.modules.superviseNote.mapper;

import com.onecity.os.supervise.modules.superviseNote.entity.GeneralIndicatorDataTitleHistory;

import java.util.List;

/**
 * 指标数据数据项目Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
public interface GeneralIndicatorDataTitleHistoryMapper 
{
    /**
     * 查询指标数据数据项目
     * 
     * @param id 指标数据数据项目主键
     * @return 指标数据数据项目
     */
    public GeneralIndicatorDataTitleHistory selectGeneralIndicatorDataTitleHistoryById(String id);

    /**
     * 查询指标数据数据项目列表
     * 
     * @param generalIndicatorDataTitleHistory 指标数据数据项目
     * @return 指标数据数据项目集合
     */
    public List<GeneralIndicatorDataTitleHistory> selectGeneralIndicatorDataTitleHistoryList(GeneralIndicatorDataTitleHistory generalIndicatorDataTitleHistory);

    /**
     * 新增指标数据数据项目
     * 
     * @param generalIndicatorDataTitleHistory 指标数据数据项目
     * @return 结果
     */
    public int insertGeneralIndicatorDataTitleHistory(GeneralIndicatorDataTitleHistory generalIndicatorDataTitleHistory);

    /**
     * 修改指标数据数据项目
     * 
     * @param generalIndicatorDataTitleHistory 指标数据数据项目
     * @return 结果
     */
    public int updateGeneralIndicatorDataTitleHistory(GeneralIndicatorDataTitleHistory generalIndicatorDataTitleHistory);

    /**
     * 删除指标数据数据项目
     * 
     * @param id 指标数据数据项目主键
     * @return 结果
     */
    public int deleteGeneralIndicatorDataTitleHistoryById(String id);

    /**
     * 批量删除指标数据数据项目
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGeneralIndicatorDataTitleHistoryByIds(String[] ids);
}
