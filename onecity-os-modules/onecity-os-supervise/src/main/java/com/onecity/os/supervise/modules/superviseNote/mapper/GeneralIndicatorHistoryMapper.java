package com.onecity.os.supervise.modules.superviseNote.mapper;


import com.onecity.os.supervise.modules.superviseNote.entity.GeneralIndicatorHistory;
import java.util.List;

/**
 * 指标表历史数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
public interface GeneralIndicatorHistoryMapper 
{
    /**
     * 查询指标表历史数据
     * 
     * @param id 指标表历史数据主键
     * @return 指标表历史数据
     */
    public GeneralIndicatorHistory selectGeneralIndicatorHistoryById(String id);

    /**
     * 查询指标表历史数据列表
     * 
     * @param generalIndicatorHistory 指标表历史数据
     * @return 指标表历史数据集合
     */
    public List<GeneralIndicatorHistory> selectGeneralIndicatorHistoryList(GeneralIndicatorHistory generalIndicatorHistory);

    /**
     * 新增指标表历史数据
     * 
     * @param generalIndicatorHistory 指标表历史数据
     * @return 结果
     */
    public int insertGeneralIndicatorHistory(GeneralIndicatorHistory generalIndicatorHistory);

    /**
     * 修改指标表历史数据
     * 
     * @param generalIndicatorHistory 指标表历史数据
     * @return 结果
     */
    public int updateGeneralIndicatorHistory(GeneralIndicatorHistory generalIndicatorHistory);

    /**
     * 删除指标表历史数据
     * 
     * @param id 指标表历史数据主键
     * @return 结果
     */
    public int deleteGeneralIndicatorHistoryById(String id);

    /**
     * 批量删除指标表历史数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGeneralIndicatorHistoryByIds(String[] ids);
}
