package com.onecity.os.supervise.modules.superviseNote.mapper;

import com.onecity.os.supervise.modules.superviseNote.entity.RemindNote;
import com.onecity.os.supervise.modules.superviseNote.vo.RemindVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2022/5/23 17:30
 */
@Mapper
public interface RemindNoteMapper {

    /**
     * 新建RemindNote
     * @param remind
     */
    void insertRemindNote(@Param("remind") RemindNote remind);

    /**
     * 更新RemindNote
     * @param remind
     */
    void updateRemindNote(@Param("remind") RemindNote remind);

    /**
     * 红灯信息信息发起人查询接口
     *
     * @param remind
     * @return
     */
    List<RemindNote> queryByCreator(@Param("remind") RemindVo remind, @Param("creatorId") String creatorId);

    /**
     * 红灯信息信息接收人查询接口
     *
     * @param remind
     * @return
     */
    List<RemindNote> queryByRecipient(@Param("remind") RemindVo remind, @Param("recipientId") String recipientId,
                                      @Param("isResponsible") String isResponsible);

    List<RemindNote> countRecipientForPending(@Param("remind") RemindVo remind, @Param("recipientId") String recipientId,
                                      @Param("isResponsible") String isResponsible);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    RemindNote queryById(@Param("id") String id);

    /**
     * 获取最大ID
     * @return
     */
    String maxForId();

    List<RemindNote> queryByIndicator(@Param("creator") String creator, @Param("indicatorId") String indicatorId);

    /**
     * 取消历史督办记录
     * @param indicatorId
     */
    void cancelIndicatorHistory(@Param("indicatorId") String indicatorId);
}
