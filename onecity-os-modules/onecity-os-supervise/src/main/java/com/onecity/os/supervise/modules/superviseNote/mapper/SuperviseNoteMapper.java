package com.onecity.os.supervise.modules.superviseNote.mapper;

import com.onecity.os.supervise.modules.superviseNote.entity.SuperviseNote;
import com.onecity.os.supervise.modules.superviseNote.vo.SuperviseVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2022/5/23 17:30
 */
@Mapper
public interface SuperviseNoteMapper {

    /**
     * 新建SuperviseNote
     * @param supervise
     */
    void insertSuperviseNote(@Param("supervise") SuperviseNote supervise);

    /**
     * 更新SuperviseNote
     * @param supervise
     */
    void updateSuperviseNote(@Param("supervise") SuperviseNote supervise);

    /**
     * 督办信息发起人查询接口
     *
     * @param supervise
     * @return
     */
    List<SuperviseNote> queryByCreator(@Param("supervise") SuperviseVo supervise, @Param("creatorId") String creatorId);

    /**
     * 督办信息接收人查询接口
     *
     * @param supervise
     * @return
     */
    List<SuperviseNote> queryByRecipient(@Param("supervise") SuperviseVo supervise, @Param("recipientId") String recipientId,
                                         @Param("isResponsible") String isResponsible);

    /**
     * 督办信息接收人查询接口
     *
     * @param supervise
     * @return
     */
    List<SuperviseNote> countRecipientForPending(@Param("supervise") SuperviseVo supervise, @Param("recipientId") String recipientId,
                                         @Param("isResponsible") String isResponsible);


    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    SuperviseNote queryById(@Param("id") String id);

    /**
     * 获取最大ID
     * @return
     */
    String maxForId();

    List<SuperviseNote> queryByIndicator(@Param("creator") String creator, @Param("indicatorId") String indicatorId);

    /**
     * 取消历史督办记录
     * @param indicatorId
     */
    void cancelIndicatorHistory(@Param("indicatorId") String indicatorId);

    /**
     * 保存指标json数据
     * @param noteId
     * @param jsonData
     */
    void insertJsonData(@Param("noteId") String noteId,@Param("jsonData") String jsonData);

    /**
     * 获取指标json数据
     * @param noteId
     * @return
     */
    String getJsonDataById(@Param("noteId") String noteId);
}
