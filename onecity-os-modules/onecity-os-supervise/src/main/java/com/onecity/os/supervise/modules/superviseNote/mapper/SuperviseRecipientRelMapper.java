package com.onecity.os.supervise.modules.superviseNote.mapper;

import com.onecity.os.supervise.modules.superviseNote.entity.SuperviseNote;
import com.onecity.os.supervise.modules.superviseNote.entity.SuperviseRecipientRel;
import com.onecity.os.supervise.modules.superviseNote.vo.RecipientCountVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Author: zack
 * @Date: 2022/5/24 09:34
 */
@Mapper
public interface SuperviseRecipientRelMapper {

    /**
     * 新建superviseRecipientRel
     *
     * @param rel
     */
    void insertSuperviseRecipientRel(@Param("rel") SuperviseRecipientRel rel);

    /**
     * 更新superviseRecipientRel
     *
     * @param rel
     */
    void updateSuperviseRecipientRel(@Param("rel") SuperviseRecipientRel rel);

    /**
     * 根据督办id，查询督办接收行 （排除删除，转发,两种状态）
     *
     * @param superviseId
     * @return
     */
    List<SuperviseRecipientRel> getRecipientsBySuperviseId(@Param("superviseId") String superviseId);

    /**
     * 根据督办id，接受人id 查询接受人行信息（排除删除，转发）
     *
     * @param superviseId
     * @return
     */
    List<SuperviseRecipientRel> getRelByRecipientId(@Param("superviseId") String superviseId,
                                                    @Param("recipientId") String recipientId);

    /**
     * 根据督办id，查询督办接收行 （排除删除,使用场景冲突，所以单独列出来）
     *
     * @param superviseId
     * @return
     */
    List<SuperviseRecipientRel> queryRecipientsBySuperviseId(@Param("superviseId") String superviseId);

    /**
     * 获取最近选择接受人，降序排序
     *
     * @return
     */
    List<RecipientCountVo> queryRecipientList(@Param("userId") String userId, @Param("source") String source);
}
