package com.onecity.os.supervise.modules.superviseNote.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Author: zack
 * @Date: 2022/5/25 10:41
 */
@Data
@ApiModel("督办查询列表查询入参")
public class SuperviseVo {

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "查询开始时间", example = "1990-01-01 00:00:00")
    private String startTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "查询结束时间", example = "1990-01-01 23:59:59")
    private String endTime;

    @ApiModelProperty(value = "督办描述")
    private String content;

    @ApiModelProperty(value = "发起人名称")
    private String creatorName;

    @ApiModelProperty(value = "接收人名称")
    private String recipientName;

    @ApiModelProperty(value = "状态",
            notes = "DRAFT = 草稿；PUBLISH = 发布；EDIT = 编辑; REVOKE = 撤回；URGE = 催办； FORWARD = 转发； FEEDBACK = 反馈 " +
                    "COMPLETE = 完结； DELETED = 删除",
            example = "PENDING")
    private String state;

    @ApiModelProperty(value = "督办id")
    private String id;

    @ApiModelProperty(value = "当前用户id")
    private String userId;

    @ApiModelProperty(value = "使用源头", notes = "PC;APP")
    private String callingSource;
}
