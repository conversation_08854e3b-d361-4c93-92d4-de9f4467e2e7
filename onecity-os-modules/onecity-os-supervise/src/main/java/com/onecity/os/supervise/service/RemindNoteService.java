package com.onecity.os.supervise.service;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.supervise.modules.superviseNote.entity.RemindNote;
import com.onecity.os.supervise.modules.superviseNote.entity.RemindRecipientRel;
import com.onecity.os.supervise.modules.superviseNote.vo.RemindVo;

import java.util.List;
import java.util.Map;

/**
 * @Author: zack
 * @Date: 2022/5/23 17:01
 */
public interface RemindNoteService {

    /**
     * 新增/编辑，数据集接口
     *
     * @param remind
     * @return
     */
    BaseResult<RemindNote> saveData(RemindNote remind, String userId, String userName);

    /**
     * 督办信息发起人查询接口
     *
     * @param remind
     * @return
     */
    List<RemindNote> queryByCreator(RemindVo remind, String userId);

    /**
     * 督办信息接收人查询接口
     *
     * @param remind
     * @return
     */
    List<RemindNote> queryByRecipient(RemindVo remind, String userId, String responsible);

    /**
     * 督办信息接收人查询接口
     *
     * @param remind
     * @return
     */
    List<RemindNote> countRecipientForPending(RemindVo remind, String userId, String responsible);

    /**
     * 督办接收行操作接口 (转发(FORWARD)/反馈(FEEDBACK))
     *
     * @param rel
     * @param operation
     * @return
     */
    BaseResult<String> recipientOperation(RemindRecipientRel rel, String operation);

    /**
     * 督办信息详情接口
     *
     * @param id
     * @return
     */
    BaseResult<RemindNote> queryDetails(String id, String userId);

    /**
     * 获取最近选择接受人
     *
     * @return
     */
    BaseResult<Map<String, String>> queryRecipientList(String userId, String source);

    /**
     * 根据发起人，指标id查询督办
     *
     * @param creator
     * @param indicatorId
     * @return
     */
    List<RemindNote> queryByIndicator(String creator, String indicatorId);

    /**
     * 取消历史督办记录
     * @param indicatorIds
     */
    void cancelIndicatorHistory(List<String> indicatorIds);
}
