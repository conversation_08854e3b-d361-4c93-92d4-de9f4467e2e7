package com.onecity.os.supervise.service;

import com.onecity.os.supervise.modules.superviseNote.entity.SuperviseNote;
import com.onecity.os.supervise.modules.superviseNote.entity.SuperviseRecipientRel;
import com.onecity.os.supervise.modules.superviseNote.vo.SuperviseNoteParam;
import com.onecity.os.supervise.modules.superviseNote.vo.SuperviseVo;
import com.onecity.os.common.core.domain.BaseResult;

import java.util.List;
import java.util.Map;

/**
 * @Author: zack
 * @Date: 2022/5/23 17:01
 */
public interface SuperviseNoteService {

    /**
     * 新增/编辑，数据集接口
     *
     * @param request
     * @return
     */
    BaseResult<SuperviseNote> saveData(SuperviseNote request);

    /**
     * 督办信息发起人查询接口
     *
     * @param supervise
     * @return
     */
    List<SuperviseNote> queryByCreator(SuperviseVo supervise, String userId);

    /**
     * 督办信息接收人查询接口
     *
     * @param supervise
     * @return
     */
    List<SuperviseNote> queryByRecipient(SuperviseVo supervise, String userId, String responsible);

    /**
     * 根据状态查询待办数量
     *
     * @param supervise
     * @return
     */
    List<SuperviseNote> countRecipientForPending(SuperviseVo supervise, String userId, String responsible);

    /**
     * 督办接收行操作接口 (转发(FORWARD)/反馈(FEEDBACK))
     *
     * @param rel
     * @param operation
     * @return
     */
    BaseResult<String> recipientOperation(SuperviseRecipientRel rel, String operation);

    /**
     * 督办信息详情接口
     *
     * @param id
     * @return
     */
    BaseResult<SuperviseNote> queryDetails(String id, String userId);

    /**
     * 获取最近选择接受人
     *
     * @return
     */
    BaseResult<Map<String, String>> queryRecipientList(String userId, String source);

    /**
     * 根据发起人，指标id查询督办
     *
     * @param creator
     * @param indicatorId
     * @return
     */
    List<SuperviseNote> queryByIndicator(String creator, String indicatorId);

    /**
     * 取消历史督办记录
     * @param indicatorIds
     */
    void cancelIndicatorHistory(List<String> indicatorIds);
}
