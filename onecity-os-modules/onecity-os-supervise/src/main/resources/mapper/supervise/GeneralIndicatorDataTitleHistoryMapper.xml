<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.supervise.modules.superviseNote.mapper.GeneralIndicatorDataTitleHistoryMapper">
    
    <resultMap type="com.onecity.os.supervise.modules.superviseNote.entity.GeneralIndicatorDataTitleHistory" id="GeneralIndicatorDataTitleHistoryResult">
        <result property="id"    column="id"    />
        <result property="indicatorId"    column="indicator_id"    />
        <result property="indicatorName"    column="indicator_name"    />
        <result property="itemName"    column="item_name"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createTime"    column="create_time"    />
        <result property="creator"    column="creator"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updater"    column="updater"    />
        <result property="itemNameName"    column="item_name_name"    />
        <result property="itemValueName"    column="item_value_name"    />
        <result property="mainValue"    column="main_value"    />
        <result property="columnName"    column="column_name"    />
        <result property="sequence"    column="sequence"    />
    </resultMap>

    <sql id="selectGeneralIndicatorDataTitleHistoryVo">
        select id, indicator_id, indicator_name, item_name, is_delete, create_time, creator, update_time, updater, item_name_name, item_value_name, main_value, column_name, sequence from general_indicator_data_title_history
    </sql>

    <select id="selectGeneralIndicatorDataTitleHistoryList" parameterType="com.onecity.os.supervise.modules.superviseNote.entity.GeneralIndicatorDataTitleHistory" resultMap="GeneralIndicatorDataTitleHistoryResult">
        <include refid="selectGeneralIndicatorDataTitleHistoryVo"/>
        <where>  
        </where>
    </select>
    
    <select id="selectGeneralIndicatorDataTitleHistoryById" parameterType="String" resultMap="GeneralIndicatorDataTitleHistoryResult">
        <include refid="selectGeneralIndicatorDataTitleHistoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertGeneralIndicatorDataTitleHistory" parameterType="com.onecity.os.supervise.modules.superviseNote.entity.GeneralIndicatorDataTitleHistory">
        insert into general_indicator_data_title_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="indicatorId != null">indicator_id,</if>
            <if test="indicatorName != null">indicator_name,</if>
            <if test="itemName != null">item_name,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creator != null">creator,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updater != null">updater,</if>
            <if test="itemNameName != null">item_name_name,</if>
            <if test="itemValueName != null">item_value_name,</if>
            <if test="mainValue != null">main_value,</if>
            <if test="columnName != null">column_name,</if>
            <if test="sequence != null">sequence,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="indicatorId != null">#{indicatorId},</if>
            <if test="indicatorName != null">#{indicatorName},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="itemNameName != null">#{itemNameName},</if>
            <if test="itemValueName != null">#{itemValueName},</if>
            <if test="mainValue != null">#{mainValue},</if>
            <if test="columnName != null">#{columnName},</if>
            <if test="sequence != null">#{sequence},</if>
         </trim>
    </insert>

    <update id="updateGeneralIndicatorDataTitleHistory" parameterType="com.onecity.os.supervise.modules.superviseNote.entity.GeneralIndicatorDataTitleHistory">
        update general_indicator_data_title_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="indicatorId != null">indicator_id = #{indicatorId},</if>
            <if test="indicatorName != null">indicator_name = #{indicatorName},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="itemNameName != null">item_name_name = #{itemNameName},</if>
            <if test="itemValueName != null">item_value_name = #{itemValueName},</if>
            <if test="mainValue != null">main_value = #{mainValue},</if>
            <if test="columnName != null">column_name = #{columnName},</if>
            <if test="sequence != null">sequence = #{sequence},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGeneralIndicatorDataTitleHistoryById" parameterType="String">
        delete from general_indicator_data_title_history where id = #{id}
    </delete>

    <delete id="deleteGeneralIndicatorDataTitleHistoryByIds" parameterType="String">
        delete from general_indicator_data_title_history where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>