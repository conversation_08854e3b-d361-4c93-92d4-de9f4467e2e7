<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.supervise.modules.superviseNote.mapper.RemindNoteLogMapper">

    <insert id="insertLog"
            parameterType="com.onecity.os.supervise.modules.superviseNote.entity.RemindNoteLog"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO remind_note_log(id, remind_id, operator, operator_name, operate, content, recipients,
                                    operation_time, backup)
        VALUES (#{log.id}, #{log.remindId}, #{log.operator}, #{log.operatorName}, #{log.operate}, #{log.content},
                #{log.recipients}, #{log.operationTime}, #{log.backup})
    </insert>

    <select id="getLogByRemindId"
            resultType="com.onecity.os.supervise.modules.superviseNote.entity.RemindNoteLog">
        SELECT id             AS id,
               content        AS content,
               operate        AS operate,
               operator_name  AS operatorName,
               operation_time AS operationTime,
               remind_id      AS remindId,
               recipients     AS recipients
        FROM remind_note_log AS log
        WHERE log.remind_id = #{remindId}
        ORDER BY log.operation_time
    </select>

</mapper>