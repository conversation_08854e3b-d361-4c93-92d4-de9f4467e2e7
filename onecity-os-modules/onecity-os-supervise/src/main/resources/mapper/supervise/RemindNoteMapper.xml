<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.supervise.modules.superviseNote.mapper.RemindNoteMapper">

    <insert id="insertRemindNote" parameterType="com.onecity.os.supervise.modules.superviseNote.entity.RemindNote"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO remind_note(id, creator, creator_name, create_time, state, content, file_url, file_id,
                                file_name, publish_time, updater, updater_name, update_time, backup, indicator_id,
                                indicator_msg, indicator_url, indicator_flag)
        VALUES (#{remind.id}, #{remind.creator}, #{remind.creatorName}, #{remind.createTime},
                #{remind.state}, #{remind.content}, #{remind.fileUrl}, #{remind.fileId}, #{remind.fileName},
                #{remind.publishTime}, #{remind.updater}, #{remind.updaterName}, #{remind.updateTime},
                #{remind.backup}, #{remind.indicatorId}, #{remind.indicatorMsg}, #{remind.indicatorUrl},
                #{remind.indicatorFlag})
    </insert>

    <update id="updateRemindNote"
            parameterType="com.onecity.os.supervise.modules.superviseNote.entity.RemindNote">
        UPDATE remind_note
        <trim prefix="SET" suffixOverrides=",">
            <if test="remind.publishTime != null">
                publish_time = #{remind.publishTime},
            </if>
            <if test="remind.updaterName != null">
                updater_name = #{remind.updaterName},
            </if>
            <if test="remind.updater != null">
                updater = #{remind.updater},
            </if>
            <if test="remind.state != null  and remind.state != ''">
                state = #{remind.state},
            </if>
            <if test="remind.content != null  and remind.content != ''">
                content = #{remind.content},
            </if>
            <if test="remind.fileId != null  and remind.fileId != ''">
                file_id = #{remind.fileId},
            </if>
            <if test="remind.fileName != null  and remind.fileName != ''">
                file_name = #{remind.fileName},
            </if>
            <if test="remind.fileUrl != null  and remind.fileUrl != ''">
                file_url = #{remind.fileUrl},
            </if>
            <if test="remind.updateTime!= null">
                update_time = #{remind.updateTime},
            </if>
            <if test="remind.indicatorId!= null">
                indicator_id = #{remind.indicatorId},
            </if>
            <if test="remind.indicatorMsg!= null">
                indicator_msg = #{remind.indicatorMsg},
            </if>
            <if test="remind.indicatorUrl!= null">
                indicator_url = #{remind.indicatorUrl},
            </if>
        </trim>
        where id = #{remind.id}
    </update>

    <select id="queryByCreator"
            resultType="com.onecity.os.supervise.modules.superviseNote.entity.RemindNote">
        SELECT
        s.id,
        s.creator,
        s.creator_name AS creatorName,
        s.create_time AS createTime,
        s.state,
        s.content,
        s.file_url AS fileUrl,
        s.file_id AS fileId,
        s.file_Name AS fileName,
        s.publish_time AS publishTime,
        s.updater,
        s.updater_name AS updaterName,
        s.update_time AS updateTime,
        s.indicator_id AS indicatorId,
        s.indicator_msg AS indicatorMsg,
        s.indicator_url AS indicatorUrl,
        s.backup AS backup
        FROM
        remind_recipient_rel AS rel
        LEFT JOIN remind_note AS s ON rel.remind_id = s.id
        WHERE
        1 = 1
        <if test="null != remind.startTime and '' != remind.startTime">
            AND s.publish_time &gt;= #{remind.startTime}
        </if>
        <if test="null != remind.endTime and '' != remind.endTime">
            AND s.publish_time &lt;= #{remind.endTime}
        </if>
        <if test="null != remind.content and '' != remind.content">
            AND INSTR(s.content,#{remind.content})
        </if>
        <if test="null != remind.creatorName and '' != remind.creatorName">
            AND INSTR(s.creator_name,#{remind.creatorName})
        </if>
        <if test="null != remind.recipientName and '' != remind.recipientName">
            AND INSTR(rel.recipient_name,#{remind.recipientName})
        </if>
        <if test="null != remind.state and '' != remind.state">
            AND s.state = #{remind.state}
        </if>
        <if test="null != remind.id and '' != remind.id">
            AND INSTR(s.id,#{remind.id})
        </if>
        <if test="null != remind.indicatorMsg and '' != remind.indicatorMsg">
            AND INSTR(s.indicator_msg,#{remind.indicatorMsg})
        </if>
        <!--  APP端口区分发起人查询，PC端口不区分 -->
        <if test="null != remind.callingSource and '' != remind.callingSource and 'APP' == remind.callingSource">
            AND s.creator = #{creatorId}
        </if>
        AND s.state != "DELETED"
        AND rel.state != "DELETED"
        GROUP BY s.id
        ORDER BY s.create_time DESC
    </select>

    <select id="queryByRecipient" parameterType="com.onecity.os.supervise.modules.superviseNote.vo.RemindVo"
            resultType="com.onecity.os.supervise.modules.superviseNote.entity.RemindNote">
        SELECT
        s.id,
        s.creator,
        s.creator_name AS creatorName,
        s.create_time AS createTime,
        s.state,
        s.content,
        s.file_url AS fileUrl,
        s.file_id AS fileId,
        s.file_name AS fileName,
        s.publish_time AS publishTime,
        s.updater,
        s.updater_name AS updaterName,
        s.update_time AS updateTime,
        s.indicator_id AS indicatorId,
        s.indicator_msg AS indicatorMsg,
        s.indicator_url AS indicatorUrl,
        s.backup AS backup,
        <!--PC端排序方式-->
        (case s.state when 'PUBLISH' then 3
        when 'COMPLETE' then 2
        else 1 end) AS orde1,
        <!--APP端排序方式-->
        (case rel.state when 'PENDING' then 2
        else 1 end) AS orde2
        FROM
        remind_recipient_rel AS rel
        LEFT JOIN remind_note AS s ON rel.remind_id = s.id
        WHERE
        1 = 1
        <if test="null != remind.startTime and '' != remind.startTime">
            AND s.publish_time &gt;= #{remind.startTime}
        </if>
        <if test="null != remind.endTime and '' != remind.endTime">
            AND s.publish_time &lt;= #{remind.endTime}
        </if>
        <if test="null != remind.content and '' != remind.content">
            AND INSTR(s.content,#{remind.content})
        </if>
        <if test="null != remind.creatorName and '' != remind.creatorName">
            AND INSTR(s.creator_name,#{remind.creatorName})
        </if>
        <if test="null != remind.recipientName and '' != remind.recipientName">
            AND INSTR(rel.recipient_name,#{remind.recipientName})
        </if>
        <if test="null != remind.state and '' != remind.state">
            AND s.state = #{remind.state}
        </if>
        <if test="null != remind.id and '' != remind.id">
            AND INSTR(s.id,#{remind.id})
        </if>
        AND rel.recipient_id = #{recipientId}
        <if test="1 == isResponsible">
            AND rel.responsible = #{isResponsible}
            AND s.state != "COMPLETE"
        </if>
        <if test="null != remind.indicatorMsg and '' != remind.indicatorMsg">
            AND INSTR(s.indicator_msg,#{remind.indicatorMsg})
        </if>
        AND s.state != "DELETED"
        AND s.state != "DRAFT"
        AND rel.state != "DELETED"
        GROUP BY s.id
        <!--  PC排序方式 -->
        <if test="null != remind.callingSource and '' != remind.callingSource and 'PC' == remind.callingSource">
            ORDER BY orde1 DESC, s.publish_time DESC
        </if>
        <!--  APP排序方式 -->
        <if test="null != remind.callingSource and '' != remind.callingSource and 'APP' == remind.callingSource">
            ORDER BY orde2 DESC, s.publish_time DESC
        </if>
    </select>

    <select id="countRecipientForPending" parameterType="com.onecity.os.supervise.modules.superviseNote.vo.RemindVo"
            resultType="com.onecity.os.supervise.modules.superviseNote.entity.RemindNote">
        SELECT
        s.id,
        s.creator,
        s.creator_name AS creatorName,
        s.create_time AS createTime,
        s.state,
        s.content,
        s.file_url AS fileUrl,
        s.file_id AS fileId,
        s.file_name AS fileName,
        s.publish_time AS publishTime,
        s.updater,
        s.updater_name AS updaterName,
        s.update_time AS updateTime,
        s.indicator_id AS indicatorId,
        s.indicator_msg AS indicatorMsg,
        s.indicator_url AS indicatorUrl,
        s.backup AS backup
        FROM
        remind_recipient_rel AS rel
        LEFT JOIN remind_note AS s ON rel.remind_id = s.id
        WHERE
        1 = 1
        <if test="null != remind.startTime and '' != remind.startTime">
            AND s.publish_time &gt;= #{remind.startTime}
        </if>
        <if test="null != remind.endTime and '' != remind.endTime">
            AND s.publish_time &lt;= #{remind.endTime}
        </if>
        <if test="null != remind.content and '' != remind.content">
            AND INSTR(s.content,#{remind.content})
        </if>
        <if test="null != remind.creatorName and '' != remind.creatorName">
            AND INSTR(s.creator_name,#{remind.creatorName})
        </if>
        <if test="null != remind.recipientName and '' != remind.recipientName">
            AND INSTR(rel.recipient_name,#{remind.recipientName})
        </if>
        <if test="null != remind.state and '' != remind.state">
            AND s.state = #{remind.state}
        </if>
        <if test="null != remind.id and '' != remind.id">
            AND INSTR(s.id,#{remind.id})
        </if>
        AND rel.recipient_id = #{recipientId}
        <if test="1 == isResponsible">
            AND rel.responsible = #{isResponsible}
            AND s.state != "COMPLETE"
        </if>
        <if test="null != remind.indicatorMsg and '' != remind.indicatorMsg">
            AND INSTR(s.indicator_msg,#{remind.indicatorMsg})
        </if>
        AND s.state != "DELETED"
        AND s.state != "DRAFT"
        AND rel.state = "PENDING"
        GROUP BY s.id
    </select>

    <select id="queryById" parameterType="String"
            resultType="com.onecity.os.supervise.modules.superviseNote.entity.RemindNote">
        SELECT s.id,
               s.creator,
               s.creator_name  AS creatorName,
               s.create_time   AS createTime,
               s.state,
               s.content,
               s.file_url      AS fileUrl,
               s.file_id       AS fileId,
               s.file_Name     AS fileName,
               s.publish_time  AS publishTime,
               s.updater,
               s.updater_name  AS updaterName,
               s.update_time   AS updateTime,
               s.indicator_id  AS indicatorId,
               s.indicator_msg AS indicatorMsg,
               s.indicator_url AS indicatorUrl,
               s.backup        AS backup
        FROM remind_note AS s
        WHERE 1 = 1
          AND s.id = #{id}
    </select>

    <select id="maxForId" resultType="java.lang.String">
        SELECT MAX(id)
        FROM remind_note
        WHERE 1 = 1
    </select>

    <select id="queryByIndicator" parameterType="String"
            resultType="com.onecity.os.supervise.modules.superviseNote.entity.RemindNote">
        SELECT s.id,
               s.creator,
               s.creator_name  AS creatorName,
               s.create_time   AS createTime,
               s.state,
               s.content,
               s.file_url      AS fileUrl,
               s.file_id       AS fileId,
               s.file_Name     AS fileName,
               s.publish_time  AS publishTime,
               s.updater,
               s.updater_name  AS updaterName,
               s.update_time   AS updateTime,
               s.indicator_id  AS indicatorId,
               s.indicator_msg AS indicatorMsg,
               s.indicator_url AS indicatorUrl,
               s.backup        AS backup
        FROM supervise_note AS s
        WHERE 1 = 1
          AND s.creator = #{creator}
          AND s.indicator_id = #{indicatorId}
          AND s.state != "DELETED"
          AND s.state != "DRAFT"
          AND s.indicator_flag = "true"
    </select>

    <update id="cancelIndicatorHistory" parameterType="String">
        UPDATE remind_note
        SET indicator_flag = "false"
        where indicator_id = #{indicatorId}
    </update>

</mapper>