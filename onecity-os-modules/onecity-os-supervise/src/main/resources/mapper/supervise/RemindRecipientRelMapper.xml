<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.supervise.modules.superviseNote.mapper.RemindRecipientRelMapper">

    <insert id="insertRemindRecipientRel"
            parameterType="com.onecity.os.supervise.modules.superviseNote.entity.RemindRecipientRel"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO remind_recipient_rel(id, remind_id, creator, creator_name, recipient_id, recipient_name,
                                            state, responsible, backup, recipient_source)
        VALUES (#{rel.id}, #{rel.remindId}, #{rel.creator}, #{rel.creatorName}, #{rel.recipientId},
                #{rel.recipientName}, #{rel.state}, #{rel.responsible}, #{rel.backup}, #{rel.recipientSource})
    </insert>

    <update id="updateRemindRecipientRel"
            parameterType="com.onecity.os.supervise.modules.superviseNote.entity.RemindRecipientRel">
        UPDATE remind_recipient_rel
        <trim prefix="SET" suffixOverrides=",">
            <if test="rel.state != null  and rel.state != ''">
                state = #{rel.state},
            </if>
            <if test="rel.responsible != null  and rel.responsible != ''">
                responsible = #{rel.responsible},
            </if>
        </trim>
        where id = #{rel.id}
    </update>

    <select id="getRecipientsByRemindId"
            resultType="com.onecity.os.supervise.modules.superviseNote.entity.RemindRecipientRel">
        SELECT rel.id,
               rel.remind_id   AS remindId,
               rel.creator,
               rel.creator_name   AS creatorName,
               rel.recipient_id   AS recipientId,
               rel.recipient_name AS recipientName,
               rel.state,
               rel.responsible,
               rel.recipient_source AS recipientSource
        FROM remind_recipient_rel AS rel
        WHERE rel.remind_id = #{remindId}
          AND rel.state != "DELETED"
          AND rel.state != "FORWARD"
    </select>

    <select id="getRelByRecipientId"
            resultType="com.onecity.os.supervise.modules.superviseNote.entity.RemindRecipientRel">
        SELECT rel.id,
               rel.remind_id   AS remindId,
               rel.creator,
               rel.creator_name   AS creatorName,
               rel.recipient_id   AS recipientId,
               rel.recipient_name AS recipientName,
               rel.state,
               rel.responsible,
               rel.recipient_source AS recipientSource
        FROM remind_recipient_rel AS rel
        WHERE rel.remind_id = #{remindId}
          <if test="recipientId != null">
              AND rel.recipient_id = #{recipientId}
          </if>
          AND rel.state != "DELETED"
          AND rel.state != "FORWARD"
    </select>

    <select id="queryRecipientsByRemindId"
            resultType="com.onecity.os.supervise.modules.superviseNote.entity.RemindRecipientRel">
        SELECT rel.id,
               rel.remind_id   AS remindId,
               rel.creator,
               rel.creator_name   AS creatorName,
               rel.recipient_id   AS recipientId,
               rel.recipient_name AS recipientName,
               rel.state,
               rel.responsible,
               rel.recipient_source AS recipientSource
        FROM remind_recipient_rel AS rel
        WHERE rel.remind_id = #{remindId}
          AND rel.state != "DELETED"
    </select>

    <select id="queryRecipientList" resultType="com.onecity.os.supervise.modules.superviseNote.vo.RecipientCountVo">
        SELECT rel.recipient_id   AS recipientId,
               rel.recipient_name AS recipientName
        FROM remind_recipient_rel AS rel
        WHERE 1 = 1
        AND rel.creator = #{userId}
        AND rel.recipient_source = #{source}
        GROUP BY rel.recipient_id
        ORDER BY COUNT(rel.recipient_id) DESC LIMIT 10
    </select>
</mapper>