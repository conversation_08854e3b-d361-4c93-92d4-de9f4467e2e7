<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.supervise.modules.superviseNote.mapper.SuperviseNoteLogMapper">

    <insert id="insertLog"
            parameterType="com.onecity.os.supervise.modules.superviseNote.entity.SuperviseNoteLog"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO supervise_note_log(id, supervise_id, operator, operator_name, operate, content, recipients,
                                       operation_time, backup)
        VALUES (#{log.id}, #{log.superviseId}, #{log.operator}, #{log.operatorName}, #{log.operate}, #{log.content},
                #{log.recipients}, #{log.operationTime}, #{log.backup})
    </insert>

    <select id="getLogBySuperviseId"
            resultType="com.onecity.os.supervise.modules.superviseNote.entity.SuperviseNoteLog">
        SELECT id             AS id,
               content        AS content,
               operate        AS operate,
               operator_name  AS operatorName,
               operation_time AS operationTime,
               supervise_id   AS superviseId,
               recipients     AS recipients
        FROM supervise_note_log AS log
        WHERE log.supervise_id = #{superviseId}
        ORDER BY log.operation_time
    </select>

</mapper>