<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.supervise.modules.superviseNote.mapper.SuperviseNoteMapper">

    <insert id="insertSuperviseNote" parameterType="com.onecity.os.supervise.modules.superviseNote.entity.SuperviseNote"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO supervise_note(id, creator, creator_name, create_time, state, content, file_url, file_id,
                                   file_name, publish_time, updater, updater_name, update_time, backup, indicator_id,
                                   indicator_msg, indicator_url, indicator_flag)
        VALUES (#{supervise.id}, #{supervise.creator}, #{supervise.creatorName}, #{supervise.createTime},
                #{supervise.state}, #{supervise.content}, #{supervise.fileUrl}, #{supervise.fileId}, #{supervise.fileName},
                #{supervise.publishTime}, #{supervise.updater}, #{supervise.updaterName}, #{supervise.updateTime},
                #{supervise.backup}, #{supervise.indicatorId}, #{supervise.indicatorMsg}, #{supervise.indicatorUrl},
                #{supervise.indicatorFlag})
    </insert>

    <update id="updateSuperviseNote"
            parameterType="com.onecity.os.supervise.modules.superviseNote.entity.SuperviseNote">
        UPDATE supervise_note
        <trim prefix="SET" suffixOverrides=",">
            <if test="supervise.publishTime != null">
                publish_time = #{supervise.publishTime},
            </if>
            <if test="supervise.updaterName != null">
                updater_name = #{supervise.updaterName},
            </if>
            <if test="supervise.updater != null">
                updater = #{supervise.updater},
            </if>
            <if test="supervise.state != null  and supervise.state != ''">
                state = #{supervise.state},
            </if>
            <if test="supervise.content != null  and supervise.content != ''">
                content = #{supervise.content},
            </if>
            <if test="supervise.updateTime!= null">
                update_time = #{supervise.updateTime},
            </if>
            <if test="supervise.indicatorId!= null">
                indicator_id = #{supervise.indicatorId},
            </if>
            <if test="supervise.indicatorMsg!= null">
                indicator_msg = #{supervise.indicatorMsg},
            </if>
            <if test="supervise.indicatorUrl!= null">
                indicator_url = #{supervise.indicatorUrl},
            </if>
            file_id = #{supervise.fileId},
            file_name = #{supervise.fileName},
            file_url = #{supervise.fileUrl},
        </trim>
        where id = #{supervise.id}
    </update>

    <select id="queryByCreator"
            resultType="com.onecity.os.supervise.modules.superviseNote.entity.SuperviseNote">
        SELECT
        s.id,
        s.creator,
        s.creator_name AS creatorName,
        s.create_time AS createTime,
        s.state,
        s.content,
        s.file_url AS fileUrl,
        s.file_id AS fileId,
        s.file_Name AS fileName,
        s.publish_time AS publishTime,
        s.updater,
        s.updater_name AS updaterName,
        s.update_time AS updateTime,
        s.indicator_id AS indicatorId,
        s.indicator_msg AS indicatorMsg,
        s.indicator_url AS indicatorUrl,
        s.backup AS backup,
        (case s.state when 'DRAFT' then 9
        when 'COMPLETE' then 1
        else 2 end) AS orde
        FROM
        supervise_recipient_rel AS rel
        LEFT JOIN supervise_note AS s ON rel.supervise_id = s.id
        WHERE
        1 = 1
        <if test="null != supervise.startTime and '' != supervise.startTime">
            AND s.publish_time &gt;= #{supervise.startTime}
            <!--  发起时间查询时，排除草稿类型-->
            AND s.state != "DRAFT"
        </if>
        <if test="null != supervise.endTime and '' != supervise.endTime">
            AND s.publish_time &lt;= #{supervise.endTime}
            <!--  发起时间查询时，排除草稿类型-->
            AND s.state != "DRAFT"
        </if>
        <if test="null != supervise.content and '' != supervise.content">
            AND INSTR(s.content,#{supervise.content})
        </if>
        <if test="null != supervise.creatorName and '' != supervise.creatorName">
            AND INSTR(s.creator_name,#{supervise.creatorName})
        </if>
        <if test="null != supervise.recipientName and '' != supervise.recipientName">
            AND INSTR(rel.recipient_name,#{supervise.recipientName})
        </if>
        <if test="null != supervise.state and '' != supervise.state">
            AND s.state = #{supervise.state}
        </if>
        <if test="null != supervise.id and '' != supervise.id">
            AND INSTR(s.id,#{supervise.id})
        </if>
        <!--  APP端口区分发起人查询，PC端口不区分 APP端不展示草稿状态数据-->
        <if test="null != supervise.callingSource and '' != supervise.callingSource and 'APP' == supervise.callingSource">
            AND s.creator = #{creatorId}
            AND s.state != "DRAFT"
        </if>
        AND s.state != "DELETED"
        AND rel.state != "DELETED"
        GROUP BY s.id
        ORDER BY orde DESC, s.publish_time DESC
    </select>

    <select id="queryByRecipient" parameterType="com.onecity.os.supervise.modules.superviseNote.vo.SuperviseVo"
            resultType="com.onecity.os.supervise.modules.superviseNote.entity.SuperviseNote">
        SELECT
        s.id,
        s.creator,
        s.creator_name AS creatorName,
        s.create_time AS createTime,
        s.state,
        s.content,
        s.file_url AS fileUrl,
        s.file_id AS fileId,
        s.file_name AS fileName,
        s.publish_time AS publishTime,
        s.updater,
        s.updater_name AS updaterName,
        s.update_time AS updateTime,
        s.indicator_id AS indicatorId,
        s.indicator_msg AS indicatorMsg,
        s.indicator_url AS indicatorUrl,
        s.backup AS backup,
        <!--PC端排序方式-->
        (case s.state when 'PUBLISH' then 3
        when 'COMPLETE' then 2
        else 1 end) AS orde1,
        <!--APP端排序方式-->
        (case rel.state when 'PENDING' then 2
        else 1 end) AS orde2
        FROM
        supervise_recipient_rel AS rel
        LEFT JOIN supervise_note AS s ON rel.supervise_id = s.id
        WHERE
        1 = 1
        <if test="null != supervise.startTime and '' != supervise.startTime">
            AND s.publish_time &gt;= #{supervise.startTime}
        </if>
        <if test="null != supervise.endTime and '' != supervise.endTime">
            AND s.publish_time &lt;= #{supervise.endTime}
        </if>
        <if test="null != supervise.content and '' != supervise.content">
            AND INSTR(s.content,#{supervise.content})
        </if>
        <if test="null != supervise.creatorName and '' != supervise.creatorName">
            AND INSTR(s.creator_name,#{supervise.creatorName})
        </if>
        <if test="null != supervise.recipientName and '' != supervise.recipientName">
            AND INSTR(rel.recipient_name,#{supervise.recipientName})
        </if>
        <if test="null != supervise.state and '' != supervise.state">
            AND s.state = #{supervise.state}
        </if>
        <if test="null != supervise.id and '' != supervise.id">
            AND INSTR(s.id,#{supervise.id})
        </if>
        AND rel.recipient_id = #{recipientId}
        <if test="1 == isResponsible">
            AND rel.responsible = #{isResponsible}
            AND s.state != "COMPLETE"
        </if>
        AND s.state != "DELETED"
        AND s.state != "DRAFT"
        AND rel.state != "DELETED"
        <!-- APP端不展示草稿状态数据-->
        <if test="null != supervise.callingSource and '' != supervise.callingSource and 'APP' == supervise.callingSource">
            AND s.state != "DRAFT"
        </if>
        GROUP BY s.id
        <!--  PC排序方式 -->
        <if test="null != supervise.callingSource and '' != supervise.callingSource and 'PC' == supervise.callingSource">
        ORDER BY orde1 DESC, s.publish_time DESC
        </if>
        <!--  APP排序方式 -->
        <if test="null != supervise.callingSource and '' != supervise.callingSource and 'APP' == supervise.callingSource">
            ORDER BY orde2 DESC, s.publish_time DESC
        </if>
    </select>

    <select id="countRecipientForPending" parameterType="com.onecity.os.supervise.modules.superviseNote.vo.SuperviseVo"
            resultType="com.onecity.os.supervise.modules.superviseNote.entity.SuperviseNote">
        SELECT
        s.id,
        s.creator,
        s.creator_name AS creatorName,
        s.create_time AS createTime,
        s.state,
        s.content,
        s.file_url AS fileUrl,
        s.file_id AS fileId,
        s.file_name AS fileName,
        s.publish_time AS publishTime,
        s.updater,
        s.updater_name AS updaterName,
        s.update_time AS updateTime,
        s.indicator_id AS indicatorId,
        s.indicator_msg AS indicatorMsg,
        s.indicator_url AS indicatorUrl,
        s.backup AS backup
        FROM
        supervise_recipient_rel AS rel
        LEFT JOIN supervise_note AS s ON rel.supervise_id = s.id
        WHERE
        1 = 1
        <if test="null != supervise.startTime and '' != supervise.startTime">
            AND s.publish_time &gt;= #{supervise.startTime}
        </if>
        <if test="null != supervise.endTime and '' != supervise.endTime">
            AND s.publish_time &lt;= #{supervise.endTime}
        </if>
        <if test="null != supervise.content and '' != supervise.content">
            AND INSTR(s.content,#{supervise.content})
        </if>
        <if test="null != supervise.creatorName and '' != supervise.creatorName">
            AND INSTR(s.creator_name,#{supervise.creatorName})
        </if>
        <if test="null != supervise.recipientName and '' != supervise.recipientName">
            AND INSTR(rel.recipient_name,#{supervise.recipientName})
        </if>
        <if test="null != supervise.state and '' != supervise.state">
            AND s.state = #{supervise.state}
        </if>
        <if test="null != supervise.id and '' != supervise.id">
            AND INSTR(s.id,#{supervise.id})
        </if>
        AND rel.recipient_id = #{recipientId}
        <if test="1 == isResponsible">
            AND rel.responsible = #{isResponsible}
            AND s.state != "COMPLETE"
        </if>
        AND s.state != "DELETED"
        AND s.state != "DRAFT"
        AND rel.state = "PENDING"
        GROUP BY s.id
    </select>

            <select id="queryById" parameterType="String"
                    resultType="com.onecity.os.supervise.modules.superviseNote.entity.SuperviseNote">
                SELECT s.id,
                       s.creator,
                       s.creator_name AS creatorName,
                       s.create_time  AS createTime,
                       s.state,
                       s.content,
                       s.file_url     AS fileUrl,
                       s.file_id      AS fileId,
                       s.file_Name    AS fileName,
                       s.publish_time AS publishTime,
                       s.updater,
                       s.updater_name AS updaterName,
                       s.update_time  AS updateTime,
                       s.indicator_id AS indicatorId,
                       s.indicator_msg AS indicatorMsg,
                       s.indicator_url AS indicatorUrl,
                       s.backup AS backup
                FROM supervise_note AS s
                WHERE 1 = 1
                  AND s.id = #{id}
            </select>

    <select id="maxForId" resultType="java.lang.String">
        SELECT MAX(id) FROM supervise_note  WHERE 1=1
    </select>

    <select id="queryByIndicator" parameterType="String"
            resultType="com.onecity.os.supervise.modules.superviseNote.entity.SuperviseNote">
        SELECT s.id,
               s.creator,
               s.creator_name  AS creatorName,
               s.create_time   AS createTime,
               s.state,
               s.content,
               s.file_url      AS fileUrl,
               s.file_id       AS fileId,
               s.file_Name     AS fileName,
               s.publish_time  AS publishTime,
               s.updater,
               s.updater_name  AS updaterName,
               s.update_time   AS updateTime,
               s.indicator_id  AS indicatorId,
               s.indicator_msg AS indicatorMsg,
               s.indicator_url AS indicatorUrl,
               s.backup AS backup
        FROM supervise_note AS s
        WHERE 1 = 1
          AND s.creator = #{creator}
          AND s.indicator_id = #{indicatorId}
          AND s.state != "DELETED"
          AND s.state != "DRAFT"
          AND s.indicator_flag = "true"
    </select>


    <update id="cancelIndicatorHistory" parameterType="String">
        UPDATE supervise_note
        SET indicator_flag = "false"
        where indicator_id = #{indicatorId}
    </update>

    <insert id="insertJsonData">
        INSERT INTO supervise_indicator_data(supervise_id,json_data)
        VALUES(#{noteId},#{jsonData})
    </insert>

    <select id="getJsonDataById" resultType="java.lang.String">
        SELECT json_data FROM supervise_indicator_data  WHERE supervise_id=#{noteId}
    </select>
</mapper>