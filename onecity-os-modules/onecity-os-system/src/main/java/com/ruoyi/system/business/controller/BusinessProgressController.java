package com.ruoyi.system.business.controller;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.ruoyi.system.business.entity.BusinessProgress;
import com.ruoyi.system.business.model.vo.BusinessProgressVo;
import com.ruoyi.system.business.service.BusinessProgressService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 招商引资信息管理（详情）
 *
 * @Author: zack
 * @Date: 2024/6/18 11:29
 */
@Slf4j
@RestController
@RequestMapping("/businessProgress")
public class BusinessProgressController extends BaseController {

    @Resource
    private BusinessProgressService businessProgressService;

    @PostMapping("/saveData")
    @ApiOperation("新建/编辑")
    public BaseResult<BusinessProgress> saveData(@RequestBody @Valid BusinessProgressVo detailVo) {
        return businessProgressService.saveData(detailVo);
    }

    @GetMapping("/list")
    @ApiOperation("招商引资信息管理查询接口")
    public TableDataInfo queryBusinessDetail(@RequestParam(name = "businessId") String businessId,
                                             @RequestParam(name = "sourceSimpleName") String sourceSimpleName) {
        startPage();
        List<BusinessProgress> progresses = businessProgressService.queryBusinessProgress(businessId, sourceSimpleName);
        return getDataTable(progresses);
    }

    @PostMapping("/del")
    @ApiOperation("招商引资信息管理删除")
    public BaseResult<String> delDetail(@RequestBody @Valid BusinessProgressVo detailVo) {
        return businessProgressService.delProgress(detailVo.getId());
    }
}
