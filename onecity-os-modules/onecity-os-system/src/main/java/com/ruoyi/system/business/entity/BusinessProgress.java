package com.ruoyi.system.business.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 招商引资项目进度表
 *
 * @Author: zack
 * @Date: 2024/6/18 11:07
 */
@Data
@Table(name = "business_progress")
public class BusinessProgress implements Serializable {
    /**
     * 主键id
     */
    @Id
    private String id;

    /**
     * 招商引资项目id
     */
    @Column(name = "business_id")
    @ApiModelProperty(value = "招商引资项目id")
    private String businessId;

    /**
     * 招商引资项目名称
     */
    @Column(name = "business_name")
    @ApiModelProperty(value = "招商引资项目名称")
    private String businessName;

    /**
     * 板块编码
     */
    @Column(name = "source_simple_name")
    @ApiModelProperty(value = "板块编码")
    private String sourceSimpleName;

    /**
     * 进展时间
     */
    @Column(name = "progress_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "进展时间", example = "1990-01-01 00:00:00")
    private java.util.Date progressTime;

    /**
     * 进展内容
     */
    @Column(name = "progress_content")
    @ApiModelProperty(value = "进展内容")
    private String progressContent;

    /**
     * 创建人（id）
     */
    @Column(name = "creator")
    @ApiModelProperty(value = "创建人")
    private long creator;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", example = "1990-01-01 00:00:00")
    private java.util.Date createTime;

    /**
     * 修改人id
     */
    @Column(name = "updater")
    @ApiModelProperty(value = "修改人id")
    private long updater;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间", example = "1990-01-01 00:00:00")
    private java.util.Date updateTime;

    /**
     * 删除标识：0-未删除1-已删除
     */
    @Column(name = "del_flag")
    @ApiModelProperty(value = "删除标识")
    private int delFlag;

    /**
     * 备用字段
     */
    @Column(name = "backup")
    @ApiModelProperty(value = "备用字段")
    private String backup;

    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称")
    private String updaterName;







}
