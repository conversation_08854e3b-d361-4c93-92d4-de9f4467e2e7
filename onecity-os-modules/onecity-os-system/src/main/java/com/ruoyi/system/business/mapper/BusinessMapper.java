package com.ruoyi.system.business.mapper;

import com.ruoyi.system.business.entity.BusinessMessage;
import com.ruoyi.system.business.model.dto.GetBusinessListDto;
import com.ruoyi.system.business.model.vo.SaveBusinessVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2024/6/18 11:32
 */
@Mapper
public interface BusinessMapper {

    /**
     * 根据参数查询政务信息列表(分页)
     *
     * @param name 项目名称
     * @param location 项目地点
     * @param label 项目标签
     * @param moneyStart 签约金额-起
     * @param moneyEnd 签约金额-止
     * @param status 状态：1-已签约,2已开工,3-已投产
     * @param sourceSimpleName 板块编码
     * @param dictType 地区编码类型
     * @return List
     */
    List<GetBusinessListDto> selectBusinessPageList(@Param("name")String name,
                                                    @Param("location")String location,
                                                    @Param("label")List<String> label,
                                                    @Param("moneyStart")Double moneyStart,
                                                    @Param("moneyEnd")Double moneyEnd,
                                                    @Param("status")Integer status,
                                                    @Param("sourceSimpleName")String sourceSimpleName,
                                                    @Param("dictType")String dictType);

    /**
     * 查询招商引资数据
     * @param id 项目id
     * @param sourceSimpleName 地区编码
     * @return GetBusinessListDto
     */
    GetBusinessListDto selectByIdAndSource(@Param("id")String id,
                                        @Param("sourceSimpleName")String sourceSimpleName);

    /**
     * 更新（全量）
     * @param businessMessage
     */
    void update(BusinessMessage businessMessage);

    /**
     * 新增
     * @param businessMessage
     */
    void insert(BusinessMessage businessMessage);

    /**
     * 删除（软删除）
     * @param id 项目id
     * @param sourceSimpleName 地区编码
     */
    void deleteByIdAndSource(@Param("id")String id,
                             @Param("sourceSimpleName")String sourceSimpleName);
}
