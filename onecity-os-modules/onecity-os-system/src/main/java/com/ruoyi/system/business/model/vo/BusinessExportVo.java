package com.ruoyi.system.business.model.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * BusinessExportVo
 *
 * <AUTHOR>
 * @since 2024/6/18 14:17
 */
@Data
public class BusinessExportVo implements Serializable {
    /**
     * 项目名称
     */
    private String name;

    /**
     * 项目地点
     */
    private String location;

    /**
     * 项目标签(多个标签以，连接)
     */
    private String label;

    /**
     * 签约金额-起
     */
    private Double moneyStart;

    /**
     * 签约金额-止
     */
    private Double moneyEnd;

    /**
     * 状态：1-已签约,2已开工,3-已投产
     */
    private Integer status;

    /**
     * 板块编码
     */
    private String sourceSimpleName;

}
