package com.ruoyi.system.business.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import java.io.Serializable;

/**
 * @Author: zack
 * @Date: 2024/6/18 16:52
 */
@Data
public class BusinessProgressVo implements Serializable {

    /**
     * 主键id
     */
    @Id
    private String id;

    /**
     * 招商引资项目id
     */
    @ApiModelProperty(value = "招商引资项目id")
    private String businessId;

    /**
     * 招商引资项目名称
     */
    @ApiModelProperty(value = "招商引资项目名称")
    private String businessName;

    /**
     * 板块编码
     */
    @ApiModelProperty(value = "板块编码")
    private String sourceSimpleName;

    /**
     * 进展时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "进展时间", example = "1990-01-01 00:00:00")
    private java.util.Date progressTime;

    /**
     * 进展内容
     */
    @ApiModelProperty(value = "进展内容")
    private String progressContent;

    /**
     * 修改人id
     */
    @Column(name = "updater")
    @ApiModelProperty(value = "修改人id")
    private long updater;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间", example = "1990-01-01 00:00:00")
    private java.util.Date updateTime;
}
