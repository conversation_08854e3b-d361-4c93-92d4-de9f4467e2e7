package com.ruoyi.system.business.model.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * SaveBusinessVo
 *
 * <AUTHOR>
 * @since 2024/6/18 14:39
 */
@Data
public class SaveBusinessVo implements Serializable {
    /**
     * 新建时id字段为空，更新时传值
     */
    private String id;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 项目地点
     */
    private String location;

    /**
     * 项目标签(多个标签以，连接)
     */
    private String label;

    /**
     * 签约金额
     */
    private double money;

    /**
     * 状态：1-已签约,2已开工,3-已投产
     */
    private int status;

    /**
     * 内容，概括
     */
    private String content;

    /**
     * 板块编码
     */
    private String sourceSimpleName;
}
