package com.ruoyi.system.business.service;

import com.onecity.os.common.core.domain.BaseResult;
import com.ruoyi.system.business.entity.BusinessProgress;
import com.ruoyi.system.business.model.vo.BusinessProgressVo;

import java.util.List;

/**
 * 招商引资信息管理（详情）
 *
 * @Author: zack
 * @Date: 2024/6/18 11:33
 */
public interface BusinessProgressService {

    /**
     * 新建/编辑
     *
     * @param businessProgressVo
     * @return
     */
    BaseResult<BusinessProgress> saveData(BusinessProgressVo businessProgressVo);

    /**
     * 招商引资信息管理查询接口
     *
     * @param businessId
     * @return
     */
    List<BusinessProgress> queryBusinessProgress(String businessId, String sourceSimpleName);

    /**
     * 根据id删除
     *
     * @param id
     * @return
     */
    BaseResult<String> delProgress(String id);
}
