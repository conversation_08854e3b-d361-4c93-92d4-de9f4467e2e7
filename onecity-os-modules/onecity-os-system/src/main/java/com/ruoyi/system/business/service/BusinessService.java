package com.ruoyi.system.business.service;

import com.onecity.os.common.core.domain.BaseResult;
import com.ruoyi.system.business.model.dto.BusinessExportDto;
import com.ruoyi.system.business.model.dto.GetBusinessListDto;
import com.ruoyi.system.business.model.vo.BusinessExportVo;
import com.ruoyi.system.business.model.vo.BusinessVo;
import com.ruoyi.system.business.model.vo.SaveBusinessVo;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2024/6/18 11:33
 */
public interface BusinessService {

    /**
     * 获取招商引资page列表
     * @param name 项目名称
     * @param location 项目地点
     * @param label 项目标签
     * @param moneyStart 签约金额-起
     * @param moneyEnd 签约金额-止
     * @param status 状态：1-已签约,2已开工,3-已投产
     * @param sourceSimpleName 板块编码
     * @return List<GetBusinessListDto>
     */
    List<GetBusinessListDto> getBusinessPageList(String name, String location, String label, Double moneyStart, Double moneyEnd, Integer status, String sourceSimpleName);

    /**
     * 招商引资新建/编辑
     * @param vo
     * @return
     */
    BaseResult save(SaveBusinessVo vo);

    /**
     * 通过id删除
     * @param id 项目id
     * @param sourceSimpleName 板块编码
     * @return
     */
    BaseResult deleteByIdAndSource(String id, String sourceSimpleName);

    /**
     * 获取招商引资列表
     * @param businessExportVo
     * @return
     */
    List<BusinessExportDto> getBusinessList(BusinessExportVo businessExportVo);

    /**
     * 查询招商引资详情
     * @param id 项目id
     * @param sourceSimpleName 板块编码
     * @return
     */
    BaseResult getBusinessInfo(String id, String sourceSimpleName);
}
