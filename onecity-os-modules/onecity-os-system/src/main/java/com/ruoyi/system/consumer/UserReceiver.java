package com.ruoyi.system.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.constant.UserConstants;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.system.api.domain.SysUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.consumer.vo.UserConsumenVo;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.utils.RsaUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class UserReceiver {

    @Autowired
    private ISysUserService sysUserService;

    @Value("${user.default}")
    private String defaultP;

    @KafkaListener(topics = {"54a4dad33ba945e08f8449748501ce53"})
    public void listen(ConsumerRecord<String, String> record) {
        log.info("=============kafkaConsumer=>user receive msg:{}", record.toString());
        String message = record.value();
        log.info("-------------message:"+message);

        if(StringUtils.isEmpty(message)){
            log.warn("message is empty");
            return;
        }
        JSONObject userObjet = JSON.parseObject(message);
        String type = userObjet.getString("type");
        String data = userObjet.getString("userInfo");
        UserConsumenVo userConsumenVo = JSON.parseObject(data,UserConsumenVo.class);
        //处理用户
        this.dealWithUserMsg(type,userConsumenVo);
        log.info("=============kafka Consume msg finished!");
    }

    private void dealWithUserMsg(String type, UserConsumenVo userConsumenVo){
        SysUser user = new SysUser();
        user.setUserName(userConsumenVo.getLoginName());
        user.setNickName(userConsumenVo.getRealName());
        user.setIDCode("");
        user.setPhonenumber(userConsumenVo.getPhoneNumber());
        if ("ADD".equals(type)) {
            if (UserConstants.NOT_UNIQUE.equals(sysUserService.checkUserNameUnique(user.getUserName()))) {
                log.warn("同步用户失败，账号已存在！'" + user.getUserName() + "'未同步");
            } else if (UserConstants.NOT_UNIQUE.equals(sysUserService.checkPhoneUnique(user))) {
                log.warn("同步用户失败，手机号已存在！'" + user.getPhonenumber() + "'未同步");
            } else {
                try {
                    String numberRsa = RsaUtils.encryptByPublicKey(RsaUtils.pub, userConsumenVo.getPhoneNumber());
                    user.setPhonenumber(numberRsa);
                }catch (Exception e) {
                    log.warn("同步用户失败，手机号RSA加密失败！'" + user.getPhonenumber() + "'未同步" + e);
                    return;
                }
                // 同步新增用户默认密码为统一密码
                user.setPassword(SecurityUtils.encryptPassword(defaultP));
                sysUserService.insertUser(user);
            }
        } else if ("UPDATE".equals(type)) {
            //同步更新用户 不同步密码
            if (StringUtils.isNotBlank(user.getPhonenumber())){
                if (UserConstants.NOT_UNIQUE.equals(sysUserService.checkPhoneUnique(user))) {
                    log.warn("更新用户 手机号已存在！'" + user.getUserName() + "'未更新");
                    user.setPhonenumber(null);
                } else {
                    try {
                        String numberRsa = RsaUtils.encryptByPublicKey(RsaUtils.pub, userConsumenVo.getPhoneNumber());
                        user.setPhonenumber(numberRsa);
                    } catch (Exception e) {
                        log.error("更新用户手机号RSA加密失败！'" + user.getPhonenumber() + "'未更新手机号");
                        user.setPhonenumber(null);
                    }
                }
            }
            // 密码默认不更新
            sysUserService.updateUserByUserName(user);
        } else if ("DELETE".equals(type)) {
            sysUserService.deleteUserByUserName(userConsumenVo.getLoginName());
        }
    }
}
