package com.ruoyi.system.controller;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.JwtUtils;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.system.api.domain.SysDept;
import com.onecity.os.system.api.domain.SysDictData;
import com.onecity.os.system.api.domain.SysUser;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.business.entity.BusinessProgress;
import com.ruoyi.system.business.service.BusinessProgressService;
import com.ruoyi.system.business.service.BusinessService;
import com.ruoyi.system.domain.OutReport;
import com.ruoyi.system.domain.vo.DingMailInfo;
import com.ruoyi.system.domain.vo.GetPhoneUserInfoByParamsDto;
import com.ruoyi.system.domain.vo.OutReportApprovalParam;
import com.ruoyi.system.domain.vo.PageResult;
import com.ruoyi.system.duty.model.entity.Duty;
import com.ruoyi.system.duty.service.DutyService;
import com.ruoyi.system.keywork.model.WorkAction;
import com.ruoyi.system.keywork.model.po.KeyWorkPo;
import com.ruoyi.system.keywork.model.vo.KeyWorkDto;
import com.ruoyi.system.keywork.model.vo.KeyWorkVo;
import com.ruoyi.system.keywork.service.IUserDeptFuzeService;
import com.ruoyi.system.keywork.service.IWorkActionService;
import com.ruoyi.system.keywork.service.KeyWorkService;
import com.ruoyi.system.service.*;
import io.jsonwebtoken.Claims;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * system模块中App接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/app")
@Slf4j
public class AppController extends BaseController {

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Autowired
    private ISysUserService userService;

    @Resource
    private DutyService dutyService;

    @Resource
    private DutyDetailService dutyDetailService;

    @Resource
    private OutReportService outReportService;

    @Autowired
    private IPhoneService phoneService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private IUserDeptFuzeService userDeptFuzeService;

    @Autowired
    private KeyWorkService keyWorkService;

    @Autowired
    private IWorkActionService workActionService;

    @Resource
    private BusinessService businessService;

    @Resource
    private BusinessProgressService businessProgressService;

    /**
     * 根据字典类型查询字典数据信息
     */
    @GetMapping(value = "/dict/data/type/{dictType}")
    public BaseResult<List<SysDictData>> dictType(@PathVariable String dictType)
    {
        List<SysDictData> data = dictTypeService.selectDictDataByType(dictType);
        if (StringUtils.isNull(data))
        {
            data = new ArrayList<SysDictData>();
        }
        return BaseResult.ok(data);
    }

    @GetMapping("/user/getAllUserList")
    public BaseResult getAllUserList(@RequestParam(name = "deptId" , required = false) Long deptId){
        return BaseResult.ok(userService.getAllUserList(deptId));
    }

    @GetMapping("/user/getUserByPcUserIds")
    public BaseResult<List<SysUser>> getUserByPcUserIds(@RequestParam(name = "userIds" , required = false) String[] userIds){
        return BaseResult.ok(userService.getUserByPcUserIds(userIds));
    }

    /**
     * 查询部门下用户，排除当前用户
     *
     * @param departId
     * @return
     */
    @GetMapping("/user/getUserByDepartId")
    public BaseResult<List<SysUser>> getUserByDepartId(@RequestParam(name = "departId") String departId,
                                                       @RequestParam(name = "userId") String userId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        return BaseResult.ok(userService.getUserByDepartId(departId, loginUser.getUserid().toString()));
    }

    /**
     *手机端取用户信息,正式环境需要删除此接口
     * @return
     */
    @GetMapping("/user/getAppUserInfoByToken")
    public AjaxResult getAppUserInfoByToken()
    {
//        if (StringUtils.isEmpty(token))
//        {
//            return AjaxResult.error("令牌不能为空");
//        }
//        Claims claims = JwtUtils.parseToken(token);
//        if (claims == null)
//        {
//            return AjaxResult.error("令牌已过期或验证不正确！");
//        }
//        String userId = JwtUtils.getUserId(claims);
//        if (StringUtils.isEmpty(userId))
//        {
//            return AjaxResult.error("令牌验证失败！");
//        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return AjaxResult.error("令牌验证失败");
        }
        // 接口返回信息
        SysUser sysUser = userService.selectUserById(loginUser.getUserid());
        return AjaxResult.success(sysUser);
    }

    /**
     * 值班值守查询
     *
     * @return 成功message
     */
    @GetMapping("/duty/list")
    @ApiOperation("查询")
    public TableDataInfo queryDutyByParam(@RequestParam(name = "dutyName", required = false) String dutyName) {
        startPage();
        List<Duty> duties = dutyService.queryDutyByParam(dutyName);
        return getDataTable(duties);
    }


    @ApiOperation(value = "值班部门列表")
    @GetMapping("/duty/dutyList")
    public BaseResult getDutyList(@RequestParam(name = "dutyId") String dutyId,
                                  @RequestParam(name = "requestSource") String requestSource,
                                  @RequestParam(name = "departId", required = false) Long departId,
                                  @RequestParam(name = "departName", required = false) String departName,
                                  @RequestParam(name = "pageNum", defaultValue = "1", required = false) Integer pageNum,
                                  @RequestParam(name = "pageSize", defaultValue = "10", required = false) Integer pageSize) {
        PageResult list = dutyDetailService.dutyList(dutyId, departId, departName, requestSource, pageNum, pageSize);
        return BaseResult.ok(list, "查找成功");
    }

    @GetMapping("/duty/departDetail")
    @ApiOperation("值班部门详情列表")
    public BaseResult departDetail(@RequestParam(name = "dutyId") String dutyId,
                                   @RequestParam(name = "departId") Long departId,
                                   @RequestParam(name = "pageNum", defaultValue = "1", required = false) Integer pageNum,
                                   @RequestParam(name = "pageSize", defaultValue = "10", required = false) Integer pageSize) {
        PageResult list = dutyDetailService.departDetail(dutyId, departId, pageNum, pageSize);
        return BaseResult.ok(list, "查找成功");

    }

    @GetMapping("/duty/reportList")
    @ApiOperation("值班报告详情列表")
    public BaseResult reportList(@RequestParam(name = "dutyId") String dutyId,
                                 @RequestParam(name = "reportName", required = false) String reportName,
                                 @RequestParam(name = "pageNum", defaultValue = "1", required = false) Integer pageNum,
                                 @RequestParam(name = "pageSize", defaultValue = "10", required = false) Integer pageSize) {
        PageResult list = dutyDetailService.reportList(dutyId, reportName, pageNum, pageSize);
        return BaseResult.ok(list, "查找成功");
    }

    @GetMapping("/outReportAction/reportList")
    @ApiOperation("外出申请已办|待办列表页")
    public TableDataInfo reportList(@RequestParam(name = "todoType") Integer todoType,
                                    @RequestParam(name = "applyType", required = false) String applyType,
                                    @RequestParam(name = "keyWork", required = false) String keyWork,
                                    @RequestParam(name = "pageNum") Integer pageNum,
                                    @RequestParam(name = "pageSize") Integer pageSize) {
        startPage();
        List<OutReport> outReports = outReportService.reportList(todoType, applyType, keyWork);
        return getDataTable(outReports);
    }

    @Log(title = "外出报备-发起申请", businessType = BusinessType.INSERT)
    @PostMapping("/outReportAction/sendApply")
    public BaseResult applyReport(@Validated @RequestBody OutReport outReport){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        outReport.setApplyUserId(loginUser.getUserid());
        return outReportService.applyReport(outReport);
    }

    @Log(title = "外出报备-审核申请", businessType = BusinessType.INSERT)
    @PostMapping("/outReportAction/approvalApply")
    public BaseResult approvalApply(@Validated @RequestBody OutReportApprovalParam outReportApprovalParam){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        outReportApprovalParam.setApprovalUserId(loginUser.getUserid());
        return outReportService.approvalApply(outReportApprovalParam);
    }

    @GetMapping("/outReportAction/details")
    @ApiOperation("查询详情")
    public BaseResult<OutReport> details(@RequestParam(name = "reportId") Integer reportId) {
        return outReportService.details(reportId);
    }

    @GetMapping("/outReportAction/revoke")
    public BaseResult revoke(@RequestParam(name = "reportId") Long reportId) {
        return outReportService.revoke(reportId);
    }


    /**
     * 一键直连:查找列表信息(分页)
     *
     * @param userid
     * @param page
     * @param size
     * @return
     */
    @GetMapping("/phone/getPhoneInfosPageList")
    @ApiOperation(value = "一键直连:查找列表信息(分页)")
    public BaseResult getPhoneInfosPageList(@RequestParam(name = "userid") Long userid,
                                            @RequestParam(name = "page", defaultValue = "1") Integer page,
                                            @RequestParam(name = "size", defaultValue = "10") Integer size) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        log.info("一键直连:查找列表信息(分页)");
        PageResult dtos = phoneService.getPhoneInfosPageList(loginUser.getUserid(), page, size);
        log.info("用户 {} 通讯录一级部门相关信息 --> {}", dtos);
        return BaseResult.ok(dtos,"查找成功");
    }

    /**
     * 一键直连:根据部门id,查找下面的人员和部门
     *
     * @param userid
     * @param departmentid
     * @return
     */
    @GetMapping("/phone/getFirstDepartUserInfosById")
    @ApiOperation(value = "一键直连:根据一级部门id,查找下面的人员和部门")
    public BaseResult getFirstDepartUserInfosById(@RequestParam(name = "userid") Long userid,
                                                  @RequestParam(name = "departmentid") Long departmentid) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        log.info("一键直连:根据一级部门id,查找下面的人员和部门");
        DingMailInfo dingMailInfos = phoneService.getFirstDepartUserInfosById(loginUser.getUserid(), departmentid);
        return BaseResult.ok(dingMailInfos);
    }

    /**
     * 一键直连:根据查找内容,查找一级部门及人员下面相关信息(分页)
     *
     * @param userid
     * @param params
     * @param page
     * @param size
     * @return
     */
    @GetMapping("/phone/getPhoneUserInfoByParams")
    @ApiOperation(value = "一键直连:根据查找内容,查找下面相关信息")
    public PageResult getPhoneUserInfoByParams(@RequestParam(name = "userid") Long userid,
                                               @RequestParam(name = "params") String params,
                                               @RequestParam(name = "page", defaultValue = "1", required = false) Integer page,
                                               @RequestParam(name = "size", defaultValue = "20", required = false) Integer size) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return new PageResult(new ArrayList<GetPhoneUserInfoByParamsDto>(),0L,0L);
        }
        log.info("一键直连:根据一级部门id,查找下面的人员和部门");
        return phoneService.getPhoneUserInfoByParams(loginUser.getUserid(), params, page, size);
    }

    /**
     * 获取部门下拉树列表
     */
    @GetMapping("/dept/treeselect")
    public AjaxResult treeselect(SysDept dept)
    {
        List<SysDept> depts = deptService.selectDeptList(dept);
        if(CollectionUtils.isNotEmpty(depts)) {
            return AjaxResult.success(deptService.buildDeptTreeSelect(depts));
        }else {
            return AjaxResult.success("无此部门信息");
        }
    }

    @GetMapping("/responsibleDepart/getMyDept")
    @ResponseBody
    public BaseResult<?> getMyDept(@RequestParam(name = "userId")Long userId)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        List<SysDept> result = userDeptFuzeService.getMyDept(loginUser.getUserid());
        return  BaseResult.ok(result);
    }

    /**
     * 增加重点工作
     */
    @PostMapping("/keyWork/addKeyWork")
    public AjaxResult addKeyWork(@RequestBody KeyWorkPo keyWorkPo){
        if(ObjectUtils.isEmpty(keyWorkPo.getWorkType())){
            AjaxResult.error("重大项目类型不能为空");
        }
        if(ObjectUtils.isEmpty(keyWorkPo.getWorkContent())){
            AjaxResult.error("重大项目内容不能为空");
        }
        if(ObjectUtils.isEmpty(keyWorkPo.getDeptIdList())){
            AjaxResult.error("重大项目负责部门不能为空");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(org.apache.commons.lang3.ObjectUtils.isNotEmpty(loginUser)) {
            keyWorkPo.setCreaterId(loginUser.getSysUser().getUserId());
        }
        return toAjax(keyWorkService.addKeyWork(keyWorkPo));
    }

    /**
     * 跟据查询条件获取关于用户的重点工作列表
     * @param keyWorkVo
     * @return
     */
    @GetMapping("/keyWork/getKeyWorkListByUserIdAndParams")
    public TableDataInfo getKeyWorkListByUserIdAndParams(KeyWorkVo keyWorkVo){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(org.apache.commons.lang3.ObjectUtils.isNotEmpty(loginUser)) {
            keyWorkVo.setCreaterId(loginUser.getUserid());
        }
        startPage();
        List<KeyWorkDto> keyWorkDtoList = keyWorkService.getKeyWorkListByUserIdAndParams(keyWorkVo);
        return getDataTable(keyWorkDtoList);
    }

    /**
     * 跟据id获取重点工作
     * @param id
     * @return
     */
    @GetMapping("/keyWork/getKeyWorkById")
    public BaseResult getKeyWorkById(@RequestParam(name = "id") String id){
        Long workId1 = Long.parseLong(id);
        return BaseResult.ok(keyWorkService.getKeyWorkById(workId1));
    }

    /**
     * 根据重点工作id删除重点工作
     */
    @PostMapping("/keyWork/deleteKeyWorkById")
    public AjaxResult deleteKeyWorkById(@RequestParam(name = "workId") String workId){
        Long workId1 = Long.parseLong(workId);
        return toAjax(keyWorkService.deleteKeyWorkById(workId1));
    }

    /**
     * 查询重点工作操作记录列表
     */
    @GetMapping("/keyWordAction/queryHistory")
    @ResponseBody
    public BaseResult<?> list(@RequestParam(name = "workId") Long workId)
    {
        List<WorkAction> list = workActionService.selectWorkActionList(workId);
        return BaseResult.ok(list);
    }

    /**
     * 新增保存重点工作操作记录
     */
    @PostMapping("/keyWordAction/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody WorkAction workAction)
    {
        //查询重点工作状态
        KeyWorkDto keyWorkDto = keyWorkService.getKeyWorkById(workAction.getWorkId());
        if("2".equals(keyWorkDto.getWorkStatus())){
            return AjaxResult.error("该重大项目已结束，禁止进行操作！");
        }
        if(1 == keyWorkDto.getDelFlag()){
            return AjaxResult.error("该重大项目已删除，禁止进行操作！");
        }
        if(!ObjectUtils.isEmpty(workAction.getActType())&&3==workAction.getActType()){
            KeyWorkPo keyWorkPo = new KeyWorkPo();
            keyWorkPo.setId(workAction.getWorkId());
            keyWorkPo.setWorkStatus("2");
            keyWorkPo.setUpdateTime(new Date());
            keyWorkService.upDateKeyWorkById(keyWorkPo);
        }
        return toAjax(workActionService.insertWorkAction(workAction));
    }

    /**
     * 根据参数查询政务信息列表(分页)
     *
     * @param name 项目名称
     * @param location 项目地点
     * @param label 项目标签
     * @param moneyStart 签约金额-起
     * @param moneyEnd 签约金额-止
     * @param status 状态：1-已签约,2已开工,3-已投产
     * @param sourceSimpleName 板块编码
     * @return
     */
    @ApiOperation(value = "招商引资-信息查询接口")
    @GetMapping("/business/list")
    public TableDataInfo getBusinessList(@RequestParam(name = "name", required = false) String name,
                                         @RequestParam(name = "location", required = false) String location,
                                         @RequestParam(name = "label", required = false) String label,
                                         @RequestParam(name = "moneyStart", required = false) Double moneyStart,
                                         @RequestParam(name = "moneyEnd", required = false) Double moneyEnd,
                                         @RequestParam(name = "status", required = false) Integer status,
                                         @RequestParam(name = "sourceSimpleName") String sourceSimpleName) {
        startPage();
        return getDataTable(businessService.getBusinessPageList(name, location, label, moneyStart, moneyEnd, status, sourceSimpleName), true);
    }

    /**
     *
     * @param id 项目id
     * @param sourceSimpleName 板块编码
     * @return
     */
    @ApiOperation(value = "招商引资-详情接口")
    @GetMapping("/business/info")
    public BaseResult getBusinessInfo(@RequestParam(name = "id") String id,
                                      @RequestParam(name = "sourceSimpleName") String sourceSimpleName) {
        return businessService.getBusinessInfo(id, sourceSimpleName);

    }

    @GetMapping("/businessProgress/list")
    @ApiOperation("招商引资信息管理查询接口")
    public TableDataInfo queryBusinessDetail(@RequestParam(name = "businessId") String businessId,
                                             @RequestParam(name = "sourceSimpleName") String sourceSimpleName) {
        startPage();
        List<BusinessProgress> progresses = businessProgressService.queryBusinessProgress(businessId, sourceSimpleName);
        return getDataTable(progresses);
    }

}
