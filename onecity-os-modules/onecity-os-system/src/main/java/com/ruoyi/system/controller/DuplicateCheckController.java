package com.ruoyi.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.constant.UserConstants;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.SqlInjectionUtil;
import com.onecity.os.system.api.domain.SysUser;
import com.ruoyi.system.domain.vo.DuplicateCheckVo;
import com.ruoyi.system.mapper.SysDictTypeMapper;
import com.ruoyi.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @Title: DuplicateCheckAction
 * @Description: 重复校验工具
 * <AUTHOR>
 * @Date 2019-03-25
 * @Version V1.0
 */
@Slf4j
@RestController
@RequestMapping("/sys/duplicate")
@Api(tags="重复校验")
public class DuplicateCheckController {

	@Resource
	SysDictTypeMapper sysDictMapper;

	@Autowired
	private ISysUserService userService;

	// 特殊表字段校验
	private static final String SYS_USER = "sys_user";

	private static final String PHONENUMBER = "phonenumber";
	private static final String USER_NAME = "user_name";

	/**
	 * 校验数据是否在系统中是否存在
	 * 
	 * @return
	 */
	@RequestMapping(value = "/check", method = RequestMethod.GET)
	@ApiOperation("重复校验接口")
	public BaseResult<Object> doDuplicateCheck(DuplicateCheckVo duplicateCheckVo, HttpServletRequest request) {
		Long num = null;
		String failWord = "已存在,请修改！";
		// 用户名唯一性校验
		if (SYS_USER.equals(duplicateCheckVo.getTableName()) && USER_NAME.equals(duplicateCheckVo.getFieldName())) {
			failWord = "账号重复";
		}
		// 手机号n唯一性校验
		if (SYS_USER.equals(duplicateCheckVo.getTableName()) && PHONENUMBER.equals(duplicateCheckVo.getFieldName())) {
			failWord = "手机号码重复";
			SysUser user = new SysUser();
			if (StringUtils.isNotBlank(duplicateCheckVo.getDataId())) {
				try {
					user.setUserId(Long.valueOf(duplicateCheckVo.getDataId()));
				} catch (NumberFormatException e) {
					log.error("重复校验接口手机号，userId转换异常！{}", JSONObject.toJSONString(duplicateCheckVo.getDataId()));
				}
			}
			user.setPhonenumber(duplicateCheckVo.getFieldVal());
			if (StringUtils.isNotEmpty(duplicateCheckVo.getFieldVal())
					&& UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
				return BaseResult.fail(failWord);
			} else {
				return BaseResult.ok("该值可用！");
			}
		}

		log.info("----duplicate check------："+ JSONObject.toJSONString(duplicateCheckVo.toString()));
		if (StringUtils.isNotBlank(duplicateCheckVo.getDataId())) {
			//表名字段名合法性校验
			String[] values = {duplicateCheckVo.getFieldName(),duplicateCheckVo.getTableName()};
			SqlInjectionUtil.filterContent(values);
			// [2].编辑页面校验
			num = sysDictMapper.duplicateCheckCountSql(duplicateCheckVo);
		} else {
			//表名字段名合法性校验
			String[] values = {duplicateCheckVo.getFieldName(),duplicateCheckVo.getTableName()};
			SqlInjectionUtil.filterContent(values);
			// [1].添加页面校验
			num = sysDictMapper.duplicateCheckCountSqlNoDataId(duplicateCheckVo);
		}

		if (num == null || num == 0) {
			// 该值可用
			return BaseResult.ok("该值可用！");
		} else {
			// 该值不可用
			log.info("该值不可用，系统中已存在！");
			return BaseResult.fail(failWord);
		}
	}
}
