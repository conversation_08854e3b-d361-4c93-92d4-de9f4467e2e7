package com.ruoyi.system.controller;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.poi.ExcelUtil;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.system.api.RemoteIndicatorService;
import com.onecity.os.system.api.model.IndicatorDataExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 */
@Slf4j
@RestController
@RequestMapping("/indicator")
public class IndicatorController {

	@Autowired
	private RemoteIndicatorService remoteIndicatorService;

	@Log(title = "指标管理-导入数据", businessType = BusinessType.IMPORT)
	@PostMapping("/importData")
	public BaseResult importData(MultipartFile file,String indicatorId) throws Exception
	{
		InputStream inputStream = null;
		BaseResult result = new BaseResult();
		try {
			inputStream = file.getInputStream();
			ExcelUtil<IndicatorDataExcel> util = new ExcelUtil<>(IndicatorDataExcel.class);
			List<IndicatorDataExcel> listData = util.importExcel(inputStream);
			result = remoteIndicatorService.insertIndicatorDataExcel(listData,indicatorId);
		}catch (Exception e) {
			log.error(e.getMessage());
		}finally {
			IOUtils.closeQuietly(inputStream);
		}
		return result;
	}
}
