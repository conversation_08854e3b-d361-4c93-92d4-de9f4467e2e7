package com.ruoyi.system.controller;

import com.onecity.os.common.core.domain.BaseResult;
import com.ruoyi.system.domain.vo.DingMailInfo;
import com.ruoyi.system.domain.vo.PageResult;
import com.ruoyi.system.service.IPhoneService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 */
@Slf4j
@RestController
@RequestMapping("/phone")
@Api(tags = "钉盘通讯录同步")
public class PhoneController {

	@Autowired
	private IPhoneService phoneService;

	/**
	 * 一键直连:查找列表信息(分页)
	 *
	 * @param userid
	 * @param page
	 * @param size
	 * @return
	 */
	@GetMapping("/getPhoneInfosPageList")
	@ApiOperation(value = "一键直连:查找列表信息(分页)")
	public BaseResult getPhoneInfosPageList(@RequestParam(name = "userid") Long userid,
											@RequestParam(name = "page", defaultValue = "1") Integer page,
											@RequestParam(name = "size", defaultValue = "10") Integer size) {
		log.info("一键直连:查找列表信息(分页)");
		PageResult dtos = phoneService.getPhoneInfosPageList(userid, page, size);
		log.info("用户 {} 通讯录一级部门相关信息 --> {}", dtos);
		return BaseResult.ok(dtos,"查找成功");
	}

	/**
	 * 一键直连:根据部门id,查找下面的人员和部门
	 *
	 * @param userid
	 * @param departmentid
	 * @return
	 */
	@GetMapping("/getFirstDepartUserInfosById")
	@ApiOperation(value = "一键直连:根据一级部门id,查找下面的人员和部门")
	public BaseResult getFirstDepartUserInfosById(@RequestParam(name = "userid") Long userid,
												  @RequestParam(name = "departmentid") Long departmentid) {
		log.info("一键直连:根据一级部门id,查找下面的人员和部门");
		DingMailInfo dingMailInfos = phoneService.getFirstDepartUserInfosById(userid, departmentid);
		return BaseResult.ok(dingMailInfos);
	}

	/**
	 * 一键直连:根据查找内容,查找一级部门及人员下面相关信息(分页)
	 *
	 * @param userid
	 * @param params
	 * @param page
	 * @param size
	 * @return
	 */
	@GetMapping("/getPhoneUserInfoByParams")
	@ApiOperation(value = "一键直连:根据查找内容,查找下面相关信息")
	public PageResult getPhoneUserInfoByParams(@RequestParam(name = "userid") Long userid,
											   @RequestParam(name = "params") String params,
											   @RequestParam(name = "page", defaultValue = "1", required = false) Integer page,
											   @RequestParam(name = "size", defaultValue = "20", required = false) Integer size) {
		log.info("一键直连:根据一级部门id,查找下面的人员和部门");
		return phoneService.getPhoneUserInfoByParams(userid, params, page, size);
	}
}
