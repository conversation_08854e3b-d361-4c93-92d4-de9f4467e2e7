package com.ruoyi.system.controller;

import com.onecity.os.common.core.constant.UserConstants;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.common.core.utils.XssUtil;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.system.api.domain.SysDept;
import com.onecity.os.system.api.domain.SysUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.domain.vo.BatchParams;
import com.ruoyi.system.service.ISysDeptService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Iterator;
import java.util.List;

/**
 * 部门信息
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dept")
public class SysDeptController extends BaseController
{
    @Autowired
    private ISysDeptService deptService;

    /**
     * 获取部门列表
     */
    @GetMapping("/list")
    public AjaxResult list(SysDept dept)
    {
        List<SysDept> depts = deptService.selectDeptList(dept);
        return AjaxResult.success(depts);
    }

    /**
     * 查询部门列表（排除节点）
     */
    @GetMapping("/list/exclude/{deptId}")
    public AjaxResult excludeChild(@PathVariable(value = "deptId", required = false) Long deptId)
    {
        List<SysDept> depts = deptService.selectDeptList(new SysDept());
        Iterator<SysDept> it = depts.iterator();
        while (it.hasNext())
        {
            SysDept d = (SysDept) it.next();
            if (d.getDeptId().intValue() == deptId
                    || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), deptId + ""))
            {
                it.remove();
            }
        }
        return AjaxResult.success(depts);
    }

    /**
     * 根据部门编号获取详细信息
     */
    @GetMapping(value = "/{deptId}")
    public AjaxResult getInfo(@PathVariable Long deptId)
    {
        deptService.checkDeptDataScope(deptId);
        return AjaxResult.success(deptService.selectDeptById(deptId));
    }

    /**
     * 获取部门下拉树列表
     */
    @GetMapping("/treeselect")
    public AjaxResult treeselect(SysDept dept)
    {
        List<SysDept> depts = deptService.selectDeptList(dept);
        if(CollectionUtils.isNotEmpty(depts)) {
            return AjaxResult.success(deptService.buildDeptTreeSelect(depts));
        }else {
            return AjaxResult.success("无此部门信息");
        }
    }

    /**
     * 加载对应角色部门列表树
     */
    @GetMapping(value = "/roleDeptTreeselect/{roleId}")
    public AjaxResult roleDeptTreeselect(@PathVariable("roleId") Long roleId)
    {
        List<SysDept> depts = deptService.selectDeptList(new SysDept());
        AjaxResult ajax = AjaxResult.success();
        ajax.put("checkedKeys", deptService.selectDeptListByRoleId(roleId));
        ajax.put("depts", deptService.buildDeptTreeSelect(depts));
        return XssUtil.filterXssResult(ajax);
    }

    /**
     * 新增部门
     */
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDept dept)
    {
        if (UserConstants.NOT_UNIQUE.equals(deptService.checkDeptNameUnique(dept)))
        {
            return AjaxResult.error("新增部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        }
        dept.setCreateBy(SecurityUtils.getUsername());
        return toAjax(deptService.insertDept(dept));
    }

    /**
     * 修改部门
     */
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDept dept)
    {
        if (UserConstants.NOT_UNIQUE.equals(deptService.checkDeptNameUnique(dept)))
        {
            return AjaxResult.error("修改部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        }
        else if (dept.getParentId().equals(dept.getDeptId()))
        {
            return AjaxResult.error("修改部门'" + dept.getDeptName() + "'失败，上级部门不能是自己");
        }
        else if (StringUtils.equals(UserConstants.DEPT_DISABLE, dept.getStatus())
                && deptService.selectNormalChildrenDeptById(dept.getDeptId()) > 0)
        {
            return AjaxResult.error("该部门包含未停用的子部门！");
        }
        dept.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(deptService.updateDept(dept));
    }

    /**
     * 删除部门
     */
    @Log(title = "部门管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deptId}")
    public AjaxResult remove(@PathVariable Long deptId)
    {
        if (deptService.hasChildByDeptId(deptId))
        {
            return AjaxResult.error("存在下级部门,不允许删除");
        }
        if (deptService.checkDeptExistUser(deptId))
        {
            return AjaxResult.error("部门存在用户,不允许删除");
        }
        return toAjax(deptService.deleteDeptById(deptId));
    }

    /**
     * 批量删除部门
     */
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @PostMapping("/deleteBatch")
    public AjaxResult deleteBatch(@RequestBody BatchParams param)
    {
        List<String> departNames = deptService.deleteBatchWithChildren(param.getIds());
        if(departNames.size()==0) {
            return AjaxResult.success("删除成功",departNames);
        }else {
            return AjaxResult.error("删除失败",departNames);
        }
    }

    /**
     * 获取部门下用户列表
     */
    @GetMapping("/userList")
    public TableDataInfo list(@RequestParam(name = "deptId") Long deptId,
                              @RequestParam(name = "userName") String userName,
                              @RequestParam(name = "nickName") String nickName)
    {
        startPage();
        List<SysUser> list = deptService.getUserByDepId(deptId,userName,nickName);
        return getDataTable(list);
    }

    @GetMapping("/getUserNameByName")
    public BaseResult getUserNameByName(@RequestParam(name = "name") String name)
    {
        return BaseResult.ok(deptService.getUserNameByName(name));
    }
    @GetMapping("/getUserNameByNameAndSourceSimpleName")
    public BaseResult getUserNameByNameAndSourceSimpleName(@RequestParam(name = "name") String name,@RequestParam(name = "sourceSimpleName") String sourceSimpleName)
    {
        return BaseResult.ok(deptService.getUserNameByNameAndSourceSimpleName(name,sourceSimpleName));
    }
    @GetMapping("/getUserNameByNameAndDepartId")
    public BaseResult getUserNameByNameAndDepartId(@RequestParam(name = "name") String name)
    {
        return BaseResult.ok(deptService.getUserNameByNameAndDepartId(name));
    }
    @GetMapping("/getUserNameNoDepart")
    public BaseResult getUserNameNoDepart(@RequestParam(name = "name") String name,@RequestParam(name = "sourceSimpleName") String sourceSimpleName)
    {
        return BaseResult.ok(deptService.getUserNameNoDepart(name,sourceSimpleName));
    }
    @GetMapping("/getDeptNamesById")
    public BaseResult<String> getDeptNamesById(@RequestParam(name = "ids" , required = false) String[] ids){
        return BaseResult.ok(deptService.getDeptNamesById(ids));
    }
}
