package com.ruoyi.system.controller;

import com.onecity.os.common.core.constant.UserConstants;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.poi.ExcelUtil;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.system.api.domain.SysDictType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.service.ISysDictTypeService;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * 数据字典信息
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dict/type")
public class SysDictTypeController extends BaseController
{
    @Autowired
    private ISysDictTypeService dictTypeService;

    @GetMapping("/list")
    public TableDataInfo list(SysDictType dictType)
    {
        startPage();
        List<SysDictType> list = dictTypeService.selectDictTypeList(dictType);
        return getDataTable(list);
    }

    @Log(title = "字典类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,@RequestBody SysDictType dictType) throws IOException
    {
        List<SysDictType> list = dictTypeService.selectDictTypeList(dictType);
        list.forEach(sysDictType -> sysDictType.setExcelRemark(sysDictType.getRemark()));
        ExcelUtil<SysDictType> util = new ExcelUtil<SysDictType>(SysDictType.class);
        util.exportExcel(response, list, "字典类型");
    }

    @Log(title = "字典类型-导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception
    {
        InputStream inputStream = null;
        List<SysDictType> dictList = null;
        try {
            inputStream = file.getInputStream();
            ExcelUtil<SysDictType> util = new ExcelUtil<SysDictType>(SysDictType.class);
            dictList = util.importExcel(inputStream);
        }catch (Exception e) {
            return AjaxResult.error("导入出错");
        }finally {
            IOUtils.closeQuietly(inputStream);
        }
        String operName = SecurityUtils.getUsername();
        AjaxResult message = dictTypeService.importDict(dictList,operName);
        return message;
    }

    /**
     * 查询字典类型详细
     */
    @GetMapping(value = "/{dictId}")
    public AjaxResult getInfo(@PathVariable Long dictId)
    {
        return AjaxResult.success(dictTypeService.selectDictTypeById(dictId));
    }

    /**
     * 查询字典类型详细
     */
    @GetMapping(value = "/type/{dictType}")
    public BaseResult<SysDictType> getInfoByType(@PathVariable String dictType)
    {
        return BaseResult.ok(dictTypeService.selectDictTypeByType(dictType));
    }

    /**
     * 新增字典类型
     */
    @Log(title = "字典类型", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody SysDictType dict)
    {
        if (UserConstants.NOT_UNIQUE.equals(dictTypeService.checkDictTypeUnique(dict)))
        {
            return AjaxResult.error("新增字典'" + dict.getDictName() + "'失败，字典类型已存在");
        }
        dict.setCreateBy(SecurityUtils.getUsername());
        return toAjax(dictTypeService.insertDictType(dict));
    }

    /**
     * 修改字典类型
     */
    @Log(title = "字典类型", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody SysDictType dict)
    {
        if (UserConstants.NOT_UNIQUE.equals(dictTypeService.checkDictTypeUnique(dict)))
        {
            return AjaxResult.error("修改字典'" + dict.getDictName() + "'失败，字典类型已存在");
        }
        dict.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(dictTypeService.updateDictType(dict));
    }

    /**
     * 删除字典类型
     */
    @Log(title = "字典类型", businessType = BusinessType.DELETE)
    @PostMapping("/{dictIds}")
    public AjaxResult remove(@PathVariable Long[] dictIds)
    {
        dictTypeService.deleteDictTypeByIds(dictIds);
        return success();
    }

    /**
     * 刷新字典缓存
     */
    @Log(title = "字典类型", businessType = BusinessType.CLEAN)
    @PostMapping("/refreshCache")
    public AjaxResult refreshCache()
    {
        dictTypeService.resetDictCache();
        return AjaxResult.success();
    }

    /**
     * 获取字典选择框列表
     */
    @GetMapping("/optionselect")
    public AjaxResult optionselect()
    {
        List<SysDictType> dictTypes = dictTypeService.selectDictTypeAll();
        return AjaxResult.success(dictTypes);
    }

    /**
     * 回收站
     * @return
     */
    @GetMapping("/deleteList")
    public AjaxResult deleteList()
    {
        List<SysDictType> list = dictTypeService.deleteList();
        return AjaxResult.success(list);
    }

    /**
     * 物理删除
     * @return
     */
    @Log(title = "字典类型-彻底删除", businessType = BusinessType.DELETE)
    @PostMapping("/deletePhysic/{dictId}")
    public AjaxResult deletePhysic(@PathVariable Long dictId)
    {
        dictTypeService.deletePhysic(dictId);
        return AjaxResult.success("删除成功！");
    }

    /**
     * 物理删除
     * @return
     */
    @Log(title = "字典类型-字典取回", businessType = BusinessType.OTHER)
    @PostMapping("/back/{dictId}")
    public AjaxResult back(@PathVariable Long dictId)
    {
        dictTypeService.updateDictDelFlag(dictId);
        return AjaxResult.success("操作成功!");
    }
}
