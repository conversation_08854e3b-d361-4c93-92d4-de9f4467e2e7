package com.ruoyi.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.constant.UserConstants;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.*;
import com.onecity.os.common.core.utils.poi.ExcelUtil;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.system.api.domain.RouterVo;
import com.onecity.os.system.api.domain.SysRole;
import com.onecity.os.system.api.domain.SysUser;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.onecity.os.system.api.domain.SysMenu;
import com.ruoyi.system.domain.vo.BatchParams;
import com.ruoyi.system.domain.vo.StatisticsDataVo;
import com.ruoyi.system.domain.vo.SysUserParam;
import com.ruoyi.system.domain.vo.SysUserThird;
import com.ruoyi.system.service.*;
import com.ruoyi.system.utils.RSAUtil;
import com.ruoyi.system.utils.RsaUtils;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 用户信息
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
@Slf4j
public class SysUserController extends BaseController
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysPostService postService;

    @Autowired
    private ISysMenuService sysMenuService;

    @Autowired
    private ISysPermissionService permissionService;

    @Autowired
    private IUserPasswordHistoryService userPasswordHistoryService;

    @Value("${login.privateKey}")
    String privateKey;

    /**
     * 获取用户列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SysUserParam user)
    {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody SysUserParam user) throws IOException
    {
        List<SysUser> list = userService.selectUserList(user);
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.exportExcel(response, list, "用户数据");
    }

    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception
    {
        List<SysUser> userList = new ArrayList<>();
        //默认存在更新
        boolean updateSupport = true;
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
            userList = util.importExcel(inputStream);
        }catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("导入出错");
        }finally {
            IOUtils.closeQuietly(inputStream);
        }
        String operName = SecurityUtils.getUsername();
        String message = userService.importUser(userList, updateSupport, operName);
        return AjaxResult.success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.importTemplateExcel(response, "用户数据");
    }

    /**
     * 获取当前用户信息
     */
    @InnerAuth
    @GetMapping("/info/{username}")
    public BaseResult<LoginUser> info(@PathVariable("username") String username)
    {
        SysUser sysUser = userService.selectUserByUserName(username);
        if (ObjectUtils.isEmpty(sysUser))
        {
            return BaseResult.fail("用户名或密码错误");
        }
        try {
            String phone = RsaUtils.decryptByPrivateKey(RsaUtils.getPrivateKey(), sysUser.getPhonenumber());
            sysUser.setPhonenumber(phone);
        }catch (Exception e) {
            //不操作
        }
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(sysUser.getUserId());
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(sysUser.getUserId());
        LoginUser sysUserVo = new LoginUser();
        sysUserVo.setSysUser(sysUser);
        sysUserVo.setRoles(roles);
        sysUserVo.setPermissions(permissions);
        return BaseResult.ok(sysUserVo);
    }

    /**
     * 获取当前用户信息
     */
    @InnerAuth
    @GetMapping("/info1/{username}")
    public BaseResult<LoginUser> info1(@PathVariable("username") String username)
    {
        SysUser sysUser = userService.selectUserByUserName(username);
        if (ObjectUtils.isEmpty(sysUser))
        {
            return BaseResult.fail("用户名或密码错误");
        }
        try {
            String phone = RsaUtils.decryptByPrivateKey(RsaUtils.getPrivateKey(), sysUser.getPhonenumber());
            sysUser.setPhonenumber(phone);
        }catch (Exception e) {
            //不操作
        }
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(sysUser.getUserId());
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(sysUser.getUserId());
        List<com.onecity.os.system.api.domain.SysMenu> userMenus = sysMenuService.selectMenuTreeByUserId1(sysUser.getUserId());
        List<RouterVo> routerVoList =  sysMenuService.buildMenus1(userMenus);
        LoginUser sysUserVo = new LoginUser();
        sysUser.setRouterVos(routerVoList);
        sysUserVo.setSysUser(sysUser);
        sysUserVo.setRoles(roles);
        sysUserVo.setPermissions(permissions);
        return BaseResult.ok(sysUserVo);
    }

    /**
     * 获取当前用户信息
     */
    @InnerAuth
    @GetMapping("/findByPhoneNumber/{mobile}")
    public BaseResult<LoginUser> findByPhoneNumber(@PathVariable("mobile") String mobile)
    {
        SysUser sysUser = userService.selectUserByMobile(mobile);
        if (StringUtils.isNull(sysUser))
        {
            return BaseResult.fail("手机号输入错误");
        }
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(sysUser.getUserId());
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(sysUser.getUserId());
        LoginUser sysUserVo = new LoginUser();
        sysUserVo.setSysUser(sysUser);
        sysUserVo.setRoles(roles);
        sysUserVo.setPermissions(permissions);
        return BaseResult.ok(sysUserVo);
    }

    /**
     * 注册用户信息
     */
    @InnerAuth
    @PostMapping("/register")
    public BaseResult<Boolean> register(@RequestBody SysUser sysUser) {
        String username = sysUser.getUserName();
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(username))) {
            log.warn("注册用户'{}'失败，注册账号已存在,不作更改", JSONObject.toJSONString(sysUser.getNickName()));
            return BaseResult.ok();
        }
        if (UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(sysUser))) {
            log.warn("注册手机号已存在！保存用户'" + JSONObject.toJSONString(sysUser.getNickName()) + "'成功");
            return BaseResult.ok();
        }
        if (UserConstants.NOT_UNIQUE.equals(userService.checkIDCodeUnique(sysUser))) {
            log.warn("注册身份证号已存在！保存用户'" + JSONObject.toJSONString(sysUser.getNickName()) + "'成功");
            return BaseResult.ok();
        }
        return BaseResult.ok(userService.registerUser(sysUser));
    }

    /**
     * 获取用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        Long userId = SecurityUtils.getUserId();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(userId);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(userId);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", userService.selectUserById(userId));
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 根据用户编号获取详细信息
     */
    @GetMapping(value = { "/", "/{userId}" })
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId)
    {
        userService.checkUserDataScope(userId);
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        ajax.put("posts", postService.selectPostAll());
        if (StringUtils.isNotNull(userId))
        {
            ajax.put(AjaxResult.DATA_TAG, userService.selectUserById(userId));
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", roleService.selectRoleListByUserId(userId));
        }
        return XssUtil.filterXssResult(ajax);
    }

    /**
     * 新增用户
     */
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysUser user) {
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserName()))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        }
        if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        if (StringUtils.isBlank(user.getPassword())) {
            return AjaxResult.error("密码需为8-16位字母+数字+特殊字符的组合");
        }
        // 解密并校验密码格式
        String password = new RSAUtil().decrypt(privateKey, user.getPassword());
        if (!RegexUtils.checkPassword(password)) {
            return AjaxResult.error("密码需为8-16位字母+数字+特殊字符的组合");
        }
        try {
            String numberRsa = RsaUtils.encryptByPublicKey(RsaUtils.getPublicKey(), user.getPhonenumber());
            user.setPhonenumber(numberRsa);
        } catch (Exception e) {
            user.setPhonenumber(user.getPhonenumber());
        }
        user.setCreateBy(SecurityUtils.getUsername());
        user.setPassword(SecurityUtils.encryptPassword(password));
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysUser user) {
//        userService.checkUserAllowed(user);
        if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        user.setUpdateBy(SecurityUtils.getUsername());
        user.setPassword(null);
        try {
            String numberRsa = RsaUtils.encryptByPublicKey(RsaUtils.getPublicKey(), user.getPhonenumber());
            user.setPhonenumber(numberRsa);
        } catch (Exception e) {
            user.setPhonenumber(user.getPhonenumber());
        }
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        if (ArrayUtils.contains(userIds, SecurityUtils.getUserId()))
        {
            return AjaxResult.error("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 批量删除用户
     */
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @PostMapping("/deleteBatch")
    public AjaxResult deleteBatch(@RequestBody BatchParams param)
    {
        if (ArrayUtils.contains(param.getIds(), SecurityUtils.getUserId()))
        {
            return AjaxResult.error("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(param.getIds()));
    }

    /**
     * 重置密码
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin() && user.getUserId() != SecurityUtils.getUserId()) {
            // 捕获异常，并特殊处理
            return AjaxResult.error("仅允许超级管理员重置此密码");
        }
        String password = new RSAUtil().decrypt(privateKey, user.getPassword());
        if (!userPasswordHistoryService.checkHaveSamePassword(user.getUserId(), null, password)) {
            return AjaxResult.error("平台密码5次以内不能重复，请修改");
        }
        user.setPassword(SecurityUtils.encryptPassword(password));
        user.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @Log(title = "用户管理-冻结或解冻用户", businessType = BusinessType.OTHER)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysUser user)
    {
        userService.checkUserAllowed(user);
        user.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 批量修改用户状态
     */
    @Log(title = "用户管理-批量冻结或解冻用户", businessType = BusinessType.OTHER)
    @PostMapping("/changeStatusBatch")
    public AjaxResult changeStatusBatch(@RequestBody BatchParams param)
    {
        return toAjax(userService.changeStatusBatch(param));
    }

//    /**
//     * 根据用户编号获取授权角色
//     */
//    @GetMapping("/authRole/{userId}")
//    public AjaxResult authRole(@PathVariable("userId") Long userId)
//    {
//        AjaxResult ajax = AjaxResult.success();
//        SysUser user = userService.selectUserById(userId);
//        List<SysRole> roles = roleService.selectRolesByUserId(userId);
//        ajax.put("user", user);
//        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
//        return XssUtil.filterXssResult(ajax);
//    }

    /**
     * 用户授权角色
     */
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public AjaxResult insertAuthRole(Long userId, Long[] roleIds)
    {
        userService.insertUserAuth(userId, roleIds);
        return success();
    }

    @GetMapping("/getDepartByUserId")
    public BaseResult getDepartByUserId(@RequestParam(name = "userId") Long userId){
        return BaseResult.ok(userService.getDepartByUserId(userId));
    }

    @GetMapping("/getAllUserList")
    public BaseResult getAllUserList(@RequestParam(name = "deptId" , required = false) Long deptId){
        return BaseResult.ok(userService.getAllUserList(deptId));
    }

    @GetMapping("/getAuditUserList")
    public BaseResult getAuditUserList(@RequestParam(name = "deptId" , required = false) Long deptId,@RequestParam(name = "name") String name,@RequestParam(name = "sourceSimpleName") String sourceSimpleName){
        return BaseResult.ok(userService.getAuditUserList(deptId,name,sourceSimpleName));
    }

    @GetMapping("/getPcUserNamesByPcUserIds")
    public BaseResult<String> getPcUserNamesByPcUserIds(@RequestParam(name = "ids" , required = false) String[] ids){
        return BaseResult.ok(userService.getPcUserNamesByPcUserIds(ids));
    }

    @GetMapping("/getPcUserIdsByPcUserNames")
    public BaseResult<String> getPcUserIdsByPcUserNames(@RequestParam(name = "names" , required = false) String[] names){
        return BaseResult.ok(userService.getPcUserIdsByPcUserNames(names));
    }
    @GetMapping("/getNickNameByName")
    public BaseResult<String> getNickNameByName(@RequestParam(name = "userName") String userName){
        return BaseResult.ok(userService.getNickNameByName(userName));
    }

    @GetMapping("/getRoleNameByRoleIds")
    public BaseResult<List<SysRole>> getRoleNameByRoleIds(@RequestParam(name = "roleIds") String[] roleIds){
        return BaseResult.ok(userService.getRoleNameByRoleIds(roleIds));
    }

    @GetMapping("/getUsersByRoleIds")
    public BaseResult<List<SysUser>> getUsersByRoleIds(@RequestParam(name = "roleIds") String[] roleIds){
        return BaseResult.ok(userService.getUsersByRoleIds(roleIds));
    }

    /**
     *第三方获取用户信息
     * @return
     */
    @GetMapping("/getUserInfoByToken")
    public AjaxResult getUserInfoByToken(@RequestParam(name = "token") String token)
    {
        if (StringUtils.isEmpty(token))
        {
            return AjaxResult.error("令牌不能为空");
        }
        Claims claims = JwtUtils.parseToken(token);
        if (claims == null)
        {
            return AjaxResult.error("令牌已过期或验证不正确！");
        }
        String userId = JwtUtils.getUserId(claims);
        if (StringUtils.isEmpty(userId))
        {
            return AjaxResult.error("令牌验证失败！");
        }
        logger.info("userId------------" + userId);
        // 接口返回信息
        SysUser sysUser = userService.selectUserById(Long.valueOf(userId));
        //返回数据
        SysUserThird sysUserThird = BeanHelper.copyProperties(sysUser,SysUserThird.class);
        return AjaxResult.success(sysUserThird);
    }

    @GetMapping("/getUserIdByMenu")
    public BaseResult<List<String>> getUserIdByMenu(@RequestParam(name = "menu") String menu){
        return BaseResult.ok(userService.getUserIdByMenu(menu));
    }

    /**
     * 查询部门下用户，排除当前用户
     *
     * @param departId
     * @return
     */
    @GetMapping("/getUserByDepartId")
    public BaseResult<List<SysUser>> getUserByDepartId(@RequestParam(name = "departId") String departId,
                                                       @RequestParam(name = "userId") String userId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(ObjectUtils.isEmpty(loginUser)){
            return BaseResult.fail("未获取到当前用户，请联系管理员");
        }
        return BaseResult.ok(userService.getUserByDepartId(departId, loginUser.getUserid().toString()));
    }

    @GetMapping("/getUserByPcUserIds")
    public BaseResult<List<SysUser>> getUserByPcUserIds(@RequestParam(name = "userIds" , required = false) String[] userIds){
        return BaseResult.ok(userService.getUserByPcUserIds(userIds));
    }
    @GetMapping("/getUserByOneCodeId")
    public BaseResult<SysUser> getUserByOneCodeId(@RequestParam(name = "oneCodeUserId", required = false) String oneCodeUserId) {
        return BaseResult.ok(userService.getUserByOneCodeId(oneCodeUserId));
    }
    @PostMapping("/updateUserByOneCodeId")
    public BaseResult<String> updateUserByOneCodeId(@RequestBody SysUser user) {
        userService.updateUserByOneCodeId(user);
        return BaseResult.ok("更新成功");
    }
    @GetMapping("/getUserIdsByRoles")
    public BaseResult<String> getUserIdsByRoles(@RequestParam(name = "roleIds" ) String[] roleIds){
        return BaseResult.ok(userService.getUserIdsByRoles(roleIds));
    }

    /**
     * 查询用户指标头操作权限
     * @param sourceSimpleName
     * @param userId
     * @return
     */
    @GetMapping("/getUserIndicatorTitlePerms")
    public BaseResult<Boolean> getUserIndicatorTitlePerms(@RequestParam(name = "sourceSimpleName") String sourceSimpleName,
                                                       @RequestParam(name = "userId") Long userId) {
        return BaseResult.ok(userService.getUserIndicatorTitlePerms(sourceSimpleName, userId));
    }

    /**
     *查询用户是否有呈报归档权限
     * @param
     * @return
     */
    @GetMapping("/getUserArchivePerms")
    public BaseResult<Boolean> getUserArchivePerms(@RequestParam(name = "userId") Long userId) {
        return BaseResult.ok(userService.getUserArchivePerms(userId));
    }

    /**
     * 查询用户阅批呈报强制删除权限
     * @param
     * @return
     */
    @GetMapping("/getInformCleanPerms")
    public BaseResult<Boolean> getInformCleanPerms(@RequestParam(name = "userId") Long userId) {
        return BaseResult.ok(userService.getInformCleanPerms(userId));
    }

    /**
     *查询用户统计信息
     * @return
     */
    @GetMapping("/getStatisticsData")
    public BaseResult<StatisticsDataVo> getStatisticsData() {
        return BaseResult.ok(userService.getStatisticsData());
    }

    /**
     * 根据用户Id查询用户菜单列表
     */
    @GetMapping("/getMenuByUserId")
    public BaseResult<List<SysMenu>> getMenuByUserId(@RequestParam(name = "userId") Long userId) {
        com.ruoyi.system.domain.SysMenu sysMenu = new com.ruoyi.system.domain.SysMenu();
        sysMenu.setStatus("0");
        List<com.ruoyi.system.domain.SysMenu> menuList = sysMenuService.selectMenuList(sysMenu,userId);
        return BaseResult.ok(BeanHelper.copyWithCollection(menuList,com.onecity.os.system.api.domain.SysMenu.class));
    }
}
