package com.ruoyi.system.domain;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 值班详情列表
 *
 * <AUTHOR>
 * @since 2024-05-16 13:55:27
 */
@Data
public class DutyDepartDetail implements Serializable{


    private static final long serialVersionUID = -3978266849027380684L;
    /**
     *   
     */
    @NotBlank(message = "【 】不能为空")
    private String id;

    /**
     *  值班任务id
     */
    @NotBlank(message = "【值班任务id】不能为空")
    private String dutyId;

    /**
     *  部门id
     */
    @NotBlank(message = "【部门id】不能为空")
    private Long departId;

    /**
     *  值班人名称
     */
    private String dutyPersonName;

    /**
     *  值班人联系方式
     */
    private String dutyPersonPhone;

    /**
     *  值班日期
     */
    private Date dutyDate;

    /**
     *  创建时间
     */
    private Date createTime;

    /**
     *  更新时间
     */
    private Date updateTime;

    /**
     *  删除状态（0-未删除，1-删除）
     */
    private Integer isDelete;

}