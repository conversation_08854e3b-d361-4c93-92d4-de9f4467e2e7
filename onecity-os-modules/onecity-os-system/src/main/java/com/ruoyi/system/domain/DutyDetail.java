package com.ruoyi.system.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 值班信息表
 *
 * <AUTHOR>
 * @since 2024-05-16 14:04:08
 */
@Data
public class DutyDetail implements Serializable{


    private static final long serialVersionUID = -3350870621077307909L;
    /**
     *   
     */
    private String id;

    /**
     *  值班任务id
     */
    private String dutyId;

    /**
     *  部门id
     */
    private Long departId;

    /**
     *  部门名称
     */
    private String departName;

    /**
     *  部门显示顺序同sys_dept表字段
     */
    private Integer orderNum;

    /**
     *  值班领导
     */
    private String dutyLeader;

    /**
     *  值班领导联系方式
     */
    private String dutyLeaderPhone;

    /**
     *  填报状态0-未完成1-已完成
     */
    private Integer dutyStatus;

    /**
     *  值班报告名称
     */
    private String dutyReportName;

    /**
     *  值班报告地址
     */
    private String dutyReportAddress;

    /**
     *  报告上传时间
     */
    private Date reportUploadTime;


    /**
     *  是否拥有值班报告0-没有1-有
     */
    private Integer haveReport;

    /**
     *  创建时间
     */
    private Date createTime;

    /**
     *  更新时间
     */
    private Date updateTime;

    /**
     *  删除状态（0-未删除，1-删除）
     */
    private Integer isDelete;

}