package com.ruoyi.system.domain;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户信息表 zjs
 * @TableName user_password_history
 */
@Data
public class UserPasswordHistory implements Serializable {

    private static final long serialVersionUID = -6245907431744226372L;
    /**
     * id
     */
    private Long id;

    /**
     * userId
     */
    private Long userId;

    /**
     * 密码
     */
    private String password;

    /**
     * 排序0-4，只记录最近 5次
     */
    private Integer sort;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     *  删除状态（0-未删除，1-删除）
     */
    private Integer isDelete;

}