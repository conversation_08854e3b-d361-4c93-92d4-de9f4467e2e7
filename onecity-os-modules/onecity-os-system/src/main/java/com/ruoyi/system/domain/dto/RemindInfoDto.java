package com.ruoyi.system.domain.dto;

import lombok.Data;

@Data
public class RemindInfoDto {

    /**
     * 提醒标题
     */
    private String remindTitle;

    /**
     * 厅局id(source_manage的主键id)
     */
    private Long sourceId;

    /**
     * 是否已读 0-否;1-是
     */
    private Byte isRead;

    /**
     * 已经提醒的次数:0-日提醒;1-首次提醒;2-逾期黄色提醒;3-逾期红色提醒;4-统计分析提醒
     */
    private Byte remindedCount;

    /**
     * 提醒类型:0-非日提醒;1-日提醒
     */
    private Byte remindedType;

    /**
     * 是否删除 0-否;1-是
     */
    private Byte isDelete;

    /**
     * 创建人
     */
    private String creater;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private String updateTime;

    /**
     * 提醒内容
     */
    private String remindContent;

    /**
     * 管理平台提醒人ids,多个用英文逗号隔开
     */
    private String pcUserId;

    /**
     * 移动端提醒人,多个用英文逗号隔开
     */
    private String appUserId;

    /**
     * 消息来源类型
     */
    private String sourceType;

    /**
     * 业务id
     */
    private String serviceId;

    /**
     * 业务来源
     */
    private String serviceType;

    /**
     * 消息级别1-红色，2-黄色，3-绿色
     */
    private String level;

    /**
     * APP连接
     */
    private String messageUrl;

    /**
     * APP消息类型 1-通知； 2-待办
     */
    private String appMsgType;
}
