
package com.ruoyi.system.domain.enums;


import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ErrorCodeEnum
 *
 * <AUTHOR>
 */
@Getter
public enum ErrorCodeEnum {

    Ok(0, "ok", "返回正常"),
    ILLEGAL_ARGS_ERROR(1, "参数错误", "参数错误"),
    // 值班值守
    ILLEGAL_PHONE_ERROR(1001, "手机号错误，请核对后重试", "手机号错误，请核对后重试"),
    ILLEGAL_FILE_TYPE_ERROR(1002, "报告格式有误，请上传PDF格式文档", "报告格式有误，请上传PDF格式文档"),
    ILLEGAL_FILE_SIZE_ERROR(1003, "文件过大，请上传5M以内的文件", "文件过大，请上传5M以内的文件"),
    FILE_PATH_NON_EXIST_ERROR(1004, "文件路径不存在，请重试", "文件路径不存在，请重试"),
    FILE_UPLOAD_FAIL_ERROR(1005, "文件上传失败，请重试", "文件上传失败，请重试"),
    FILE_NAME_TYPE_ERROR(1006, "文件不能没有后缀!", "文件不能没有后缀!"),
    FILE_NAME_LONG_ERROR(1007, "文件名长度过长,请更换后重试!", "文件名长度过长,请更换后重试!"),
    NAME_TOO_LONG_ERROR(1008, "名字长度超过8个字，请重新输入", "名字长度限制为8个字"),
    NO_PERMISSION_ERROR(1009, "请授权后再试", "请授权后再试"),
    DUTY_EXIST_ERROR(1010, "当前任务已存在", "当前任务已存在"),
    DUTY_DATE_ERROR(1011, "值班日期不在值班周期内，请重新选择", "值班日期不在值班周期内，请重新选择"),
    ADD_FAIL_ERROR(1012, "添加失败,请重试", "添加失败,请重试"),
    UPDATE_FAIL_ERROR(10013, "更新失败,请重试", "更新失败,请重试"),
    DELETE_FAIL_ERROR(1014, "删除失败,请重试", "删除失败,请重试"),
    EXCEEDING_DEADLINE_ERROR(1015, "超过填报截止日期,不可编辑！", "超过填报截止日期,不可编辑！");


    ErrorCodeEnum(int code, String msg, String desc) {
        this.code = code;
        this.msg = msg;
        this.desc = desc;
    }


    /**
     * 错误 code
     */
    private final int code;

    /**
     * 错误展示信息
     */
    private final String msg;

    /**
     * 描述
     */
    private final String desc;


    /**
     * 获取来源
     *
     * @param code
     * @return
     */
    public static ErrorCodeEnum getDesc(int code) {
        for (ErrorCodeEnum errorCodeEnum : ErrorCodeEnum.values()) {
            if (code == errorCodeEnum.getCode()) {
                return errorCodeEnum;
            }
        }
        return null;
    }


    public static List<Map<String, Object>> getMap() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ErrorCodeEnum errorCodeEnum : ErrorCodeEnum.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("code", errorCodeEnum.getCode());
            map.put("msg", errorCodeEnum.getMsg());
            list.add(map);
        }
        return list;
    }


}
