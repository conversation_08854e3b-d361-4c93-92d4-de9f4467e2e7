package com.ruoyi.system.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: zack
 * @Date: 2022/4/2 16:03
 */
@Data
public class DingOperateVo {

    @ApiModelProperty(value = "用户账号")
    private String userName;

    @ApiModelProperty(value = "姓名")
    private String nickName;

    @ApiModelProperty(value = "职位")
    private String userPosition;

    @ApiModelProperty(value = "电话")
    private String mobile;

    @ApiModelProperty(value = "访问信息")
    private String logContent;

    @ApiModelProperty(value = "访问时间")
    private String createTime;
}
