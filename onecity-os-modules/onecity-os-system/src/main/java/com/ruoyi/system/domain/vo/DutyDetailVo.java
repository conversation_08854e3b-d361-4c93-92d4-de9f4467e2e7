package com.ruoyi.system.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 值班信息表
 *
 * <AUTHOR>
 * @since 2024-05-16 14:04:08
 */
@Data
public class DutyDetailVo implements Serializable{


    private static final long serialVersionUID = -7101467077845463587L;

    /**
     *  值班任务id
     */
    @NotNull(message = "值班id不可为空")
    private String dutyId;
    /**
     *  值班任务名称
     */
    private String dutyName;

    /**
     *  部门id
     */
    @NotNull(message = "值班部门id不可为空")
    private Long departId;

    /**
     *  部门名称
     */
    private String departName;

    /**
     *  值班领导
     */
    @NotNull(message = "值班领导不可为空")
    private String dutyLeader;

    /**
     *  值班领导联系方式
     */
    @NotNull(message = "值班领导手机号不可为空")
    private String dutyLeaderPhone;

    /**
     *  填报状态0-未完成1-已完成
     */
    private Integer dutyStatus;


    /**
     *  值班报告名称
     */
    private String dutyReportName;

    /**
     *  值班报告地址
     */
    private String dutyReportAddress;


}