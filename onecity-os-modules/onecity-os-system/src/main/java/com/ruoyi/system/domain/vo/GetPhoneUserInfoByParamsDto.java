package com.ruoyi.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * 一键直连:根据查找内容,查找下面相关信息出参
 *
 * <AUTHOR>
 * @date 2020/10/26 12:12
 */
@Data
public class GetPhoneUserInfoByParamsDto {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 用户名(或者部门名)
     */
    private String amember;

    /**
     * 数据库存储的字段 用户名
     */
    @JsonIgnore
    private String name;

    /**
     * 手机号码
     */
    private String number;

    /**
     * 部门+职位信息
     */
    private String departAndJob;

    @JsonIgnore
    private Long total;
    @JsonIgnore
    private Long totalPage;

    /**
     * 返回前端结果类型0-人，1-部门
     */
    private Integer type;

}
