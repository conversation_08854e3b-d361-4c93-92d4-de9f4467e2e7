package com.ruoyi.system.domain.vo;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @date 2024-04-18
 */
@Data
public class OutReportApprovalParam
{
    /** 外出报备主体id */
    @NotNull(message = "reportId不能为空")
    private Long reportId;
    /** 审批人用户id */
    @NotNull(message = "审批人不能为空")
    private Long approvalUserId;
    /** 审批状态0-通过，1-拒绝 */
    @NotNull(message = "审批状态不能为空")
    private Integer approvalStatus;
    /** 审批意见 */
    private String approvalContent;
    /**当前审批步骤*/
    private int step;
}
