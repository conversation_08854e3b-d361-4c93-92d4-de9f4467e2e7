package com.ruoyi.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: zjs
 * @Date: 2024-08-09
 */
@Data
@ApiModel("统计数据-汇总")
public class StatisticsDataVo {

    @ApiModelProperty(value = "用户总数")
    private int userCount;
    @ApiModelProperty(value = "移动端登录总次数")
    private int appLoginUserTimes;
    @ApiModelProperty(value = "PC端登录总次数")
    private int pcLoginUserTimes;
    @ApiModelProperty(value = "呈报总数")
    private int informCount;
    @ApiModelProperty(value = "批示总数")
    private int instructionCount;

}
