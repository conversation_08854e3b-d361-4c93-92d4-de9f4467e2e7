package com.ruoyi.system.domain.vo;

import com.onecity.os.common.core.annotation.Excel;
import com.onecity.os.common.core.web.domain.BaseEntityWeb;
import lombok.Data;

/**
 * 用户请求入参
 * 
 * <AUTHOR>
 */
@Data
public class SysUserParam extends BaseEntityWeb
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    private Long userId;
    /** 用户账号 */
    private String userName;
    /** 用户昵称 */
    private String nickName;
    /** 手机号码 */
    private String phonenumber;
    /** 帐号状态（0正常 1停用） */
    private String status;
    /** 角色 */
    private String roleName;
    /** 部门 */
    private String departName;
}
