package com.ruoyi.system.duty.mapper;

import com.ruoyi.system.duty.model.entity.Duty;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2024/5/15 15:57
 */
@Mapper
public interface DutyMapper {

    /**
     * 新建duty
     *
     * @param duty
     */
    void insertDuty(@Param("duty") Duty duty);

    /**
     * 根据 名称+创建时间 判断是否存在
     *
     * @param dutyName
     * @param createTime
     * @return
     */
    Duty queryByDate(@Param("dutyName") String dutyName, @Param("createTime") java.util.Date createTime, @Param("dutyId") String dutyId);

    /**
     * 根据id更新
     *
     * @param duty
     */
    void updateDuty(@Param("duty") Duty duty);

    /**
     * 数据删除
     * @param dutyId
     */
    void delById(@Param("dutyId") String dutyId);

    /**
     * 根据标题查询
     * @param dutyName
     * @return
     */
    List<Duty> queryDutyByParam(@Param("dutyName") String dutyName);

    /**
     * 根据 id 查询值班任务
     *
     * @param id
     * @return
     */
    Duty queryById(@Param("id") String id);
}
