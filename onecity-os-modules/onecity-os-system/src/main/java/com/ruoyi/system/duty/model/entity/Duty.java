package com.ruoyi.system.duty.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Table(name = "duty_list")
public class Duty {

    /**
     * 主键自增
     **/
    @Id
    @GeneratedValue
    private String id;

    /**
     * 值班名称
     */
    @Column(name = "duty_name")
    @ApiModelProperty(value = "值班名称")
    @NotNull(message = "值班名称不可为空")
    private String dutyName;

    /**
     * 值班开始日期
     */
    @Column(name = "duty_begin_date")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "值班开始日期", example = "1990-01-01")
    @NotNull(message = "值班开始日期不可为空")
    private Date dutyBeginDate;
    /**
     * 值班结束日期
     */
    @Column(name = "duty_end_date")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "值班结束日期", example = "1990-01-01")
    @NotNull(message = "值班结束日期不可为空")
    private Date dutyEndDate;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", example = "1990-01-01 00:00:00")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间", example = "1990-01-01 00:00:00")
    private Date updateTime;

    /**
     * 截止时间
     */
    @Column(name = "deadline")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "截止时间", example = "1990-01-01")
    private Date deadline;

    /**
     * 是否删除 0:否1:是
     */
    @Column(name = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

}
