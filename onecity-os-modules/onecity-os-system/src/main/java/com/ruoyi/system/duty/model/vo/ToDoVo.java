package com.ruoyi.system.duty.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ToDoVo {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 指标来源简称
     */
    private String sourceId;

    /**
     * 待办标题
     */
    private String title;

    /**
     * 待办人id
     */
    private String toDoUserId;

    /**
     * 待办人姓名
     */
    private String toDoUserName;
    
    /**
     * 审核结果 0-不通过;1-通过
     */
    private String auditResult;

    /**
     * 提交时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 审核时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 是否删除0-否;1-是
     */
    private Byte isDelete;

    /**
     * 待办来源1-指标管理；2-调查问卷 ;3-阅批呈报; 4-值班值守
     */
    private String toDoSource;

    /**
     * 调查问卷id
     */
    private String questionnaireId;

    /**
     * 调查问卷跳转标识1-问卷管理；2-填写问卷；3-问卷审核
     */
    private String questionnaireJump;
}