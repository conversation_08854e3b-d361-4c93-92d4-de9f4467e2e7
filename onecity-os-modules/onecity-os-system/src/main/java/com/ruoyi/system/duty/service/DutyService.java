package com.ruoyi.system.duty.service;

import com.onecity.os.common.core.domain.BaseResult;
import com.ruoyi.system.duty.model.entity.Duty;

import java.text.ParseException;
import java.util.List;

/**
 * @Author: zack
 * @Date: 2024/5/15 14:42
 */
public interface DutyService {

    /**
     * 新增/编辑，数据集接口
     *
     * @param duty
     * @return
     */
    BaseResult<Duty> saveData(Duty duty) throws ParseException;

    /**
     * 查询
     * @param dutyName
     * @return
     */
    List<Duty> queryDutyByParam(String dutyName);

    /**
     * 删除值班信息
     * @param dutyId
     * @return
     */
    BaseResult<String> delDuty(String dutyId);

    /**
     * 获取用户操作权限
     * @return
     */
    Boolean getUserPerms();
}
