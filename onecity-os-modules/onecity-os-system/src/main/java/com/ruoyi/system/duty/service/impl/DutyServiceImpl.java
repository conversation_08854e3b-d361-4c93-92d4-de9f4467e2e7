package com.ruoyi.system.duty.service.impl;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.IdUtils;
import com.onecity.os.system.api.domain.SysUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.duty.mapper.DutyMapper;
import com.ruoyi.system.duty.model.entity.Duty;
import com.ruoyi.system.duty.model.vo.ToDoVo;
import com.ruoyi.system.duty.service.DutyService;
import com.ruoyi.system.feign.TodoFeignService;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.DutyDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @Author: zack
 * @Date: 2024/5/15 14:45
 */
@Slf4j
@Service("dutyService")
public class DutyServiceImpl implements DutyService {

    @Resource
    private DutyMapper dutyMapper;
    @Resource
    private SysUserMapper userMapper;

    @Resource
    private DutyDetailService dutyDetailService;
    @Resource
    private TodoFeignService todoFeignService;

    @Override
    public BaseResult<Duty> saveData(Duty duty) throws ParseException {
        DateFormat dayFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Date date = new Date();
        duty.setUpdateTime(date);
        duty.setDutyBeginDate(dayFormat.parse(dayFormat.format(duty.getDutyBeginDate())));
        duty.setDutyEndDate((dayFormat.parse(DateFormatUtils.format(duty.getDutyEndDate(), "yyyy-MM-dd 23:59:59"))));
        duty.setDeadline((dayFormat.parse(DateFormatUtils.format(duty.getDeadline(), "yyyy-MM-dd 23:59:59"))));
        if (StringUtil.isEmpty(duty.getId())) {
            //新建
            duty.setId(IdUtils.simpleUUID().trim());
            duty.setCreateTime(date);
            //值班任务+发布日期 重复时提示：“当前任务已存在”
            Duty check = dutyMapper.queryByDate(duty.getDutyName(), duty.getCreateTime(), null);
            if (null != check) {
                return BaseResult.fail("当前任务已存在");
            }
            duty.setIsDelete(0);
            dutyMapper.insertDuty(duty);
            // 新增值班任务后，同步新增值班部门表
            dutyDetailService.beatchInsertDutyDepart(duty.getId());
            //给有值班值守角色的人发送待办消息
            List<String> userIds = userMapper.getUserIdByRoleKey("zbzs");
            sendToDo(userIds, duty.getId());
            return BaseResult.ok(duty);
        } else {
            //更新
            //值班任务+发布日期 重复时提示：“当前任务已存在”，并且非当前数据
            Duty check = dutyMapper.queryByDate(duty.getDutyName(), new Date(), duty.getId());
            if (null != check) {
                return BaseResult.fail("当前任务已存在");
            }
            log.info("update duty : {}", JSONObject.toJSONString(duty));
            dutyMapper.updateDuty(duty);
        }
        return BaseResult.ok();
    }

    @Override
    public List<Duty> queryDutyByParam(String dutyName) {
        return dutyMapper.queryDutyByParam(dutyName);
    }

    @Override
    public BaseResult<String> delDuty(String dutyId) {
        dutyMapper.delById(dutyId);
        // 删除值班任务后，同步删除值班部门表、值班部门详情表，需要校验操作权限
        dutyDetailService.deleteDutyDepart(dutyId);
        return BaseResult.ok("删除成功");
    }

    @Override
    public Boolean getUserPerms() {
        // 用户id
        Long userId = SecurityUtils.getUserId();
        int count = userMapper.getUserIndicatorTitlePerms("zbzs", userId);
        return count > 0;
    }

    /**
     * 根据用户id发送待办消息
     *
     * @param userIds
     */
    private void sendToDo(List<String> userIds, String dutyId) {
        Date nowDate = new Date();
        for (String userId : userIds) {
            ToDoVo toDoVo = new ToDoVo();
            toDoVo.setTitle("【值班值守】任务待填报\r\n新的值班填报任务已发布，请您及时填报。");
            toDoVo.setToDoUserId(userId);
            SysUser sysUser = userMapper.selectUserById(Long.valueOf(userId));
            toDoVo.setToDoUserName(sysUser.getUserName());
            toDoVo.setCreateTime(nowDate);
            toDoVo.setIsDelete((byte) 0);
            toDoVo.setToDoSource("4");
            toDoVo.setQuestionnaireId(dutyId);
            toDoVo.setAuditResult("2");
            todoFeignService.addTodo(toDoVo);
        }
    }
}
