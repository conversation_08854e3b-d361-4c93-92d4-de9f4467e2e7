package com.ruoyi.system.feign;

import com.onecity.os.common.core.domain.BaseResult;
import com.ruoyi.system.domain.dto.RemindInfoDto;
import com.ruoyi.system.feign.fallback.MessageFeignFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2022/5/30 15:56
 */
@FeignClient(name = "onecity-os-message", fallbackFactory = MessageFeignFallbackFactory.class, decode404 = true)
public interface MessageFeignService {

    @PostMapping("/remind/addMessage")
    BaseResult addMsg(@RequestBody List<RemindInfoDto> remindInfo);

    @GetMapping("/remind/deleteMessageByService")
    BaseResult deleteMessageByService(@RequestParam(name = "serviceId") String serviceId, @RequestParam(name = "serviceType") String serviceType);

    @GetMapping("/remind/deleteMessageByServiceAndUserId")
    BaseResult deleteMessageByServiceAndUserId(@RequestParam(name = "serviceId") String serviceId,
                                               @RequestParam(name = "serviceType") String serviceType,
                                               @RequestParam(name = "appUserId") String appUserId,
                                               @RequestParam(name = "userName") String userName);
}
