package com.ruoyi.system.feign;

import com.onecity.os.common.core.domain.BaseResult;
import com.ruoyi.system.duty.model.vo.ToDoVo;
import com.ruoyi.system.feign.fallback.TodoFeignServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: zack
 * @Date: 2022/9/27 15:24
 */
@FeignClient(name = "onecity-os-management", fallbackFactory = TodoFeignServiceFallbackFactory.class, decode404 = true)
public interface TodoFeignService {

    @PostMapping("/indicator/addToDo")
    BaseResult addTodo(@RequestBody ToDoVo toDoVo);

    @PostMapping("/indicator/deleteToDoByPram")
    BaseResult deleteToDoByPram(@RequestParam(name = "questionnaireId", required = false) String questionnaireId, @RequestParam(name = "source", required = false) String source);

}
