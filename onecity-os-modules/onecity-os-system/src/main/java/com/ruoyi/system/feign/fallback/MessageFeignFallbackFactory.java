package com.ruoyi.system.feign.fallback;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.ruoyi.system.domain.dto.RemindInfoDto;
import com.ruoyi.system.feign.MessageFeignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: zack
 * @Date: 2022/5/31 10:04
 */
@Slf4j
@Component
public class MessageFeignFallbackFactory implements FallbackFactory<MessageFeignService> {
    @Override
    public MessageFeignService create(Throwable cause) {
        return new MessageFeignService() {
            @Override
            public BaseResult addMsg(List<RemindInfoDto> remindInfo) {
                log.info("addMsg  : {} ", JSONObject.toJSONString(remindInfo));
                log.error("发送消息失败！");
                return BaseResult.fail("发送消息异常");
            }

            @Override
            public BaseResult deleteMessageByService(String serviceId, String serviceType) {
                log.info("addMsg  : {} ", serviceId,serviceType);
                log.error("删除消息错误！");
                return BaseResult.fail("删除消息异常");
            }

            @Override
            public BaseResult deleteMessageByServiceAndUserId(String serviceId, String serviceType, String appUserId, String userName) {
                log.info("deleteMessageByServiceAndUserId : {} {} {} ", serviceId,serviceType, appUserId);
                log.error("删除消息错误！");
                return BaseResult.fail("删除消息异常");
            }
        };
    }
}
