package com.ruoyi.system.keywork.controller;

import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.system.api.domain.SysDept;
import com.ruoyi.system.keywork.model.UserDeptFuze;
import com.ruoyi.system.keywork.model.vo.AddUserDeptParam;
import com.ruoyi.system.keywork.model.vo.UserDeptParam;
import com.ruoyi.system.keywork.model.vo.UserDeptVo;
import com.ruoyi.system.keywork.service.IUserDeptFuzeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户责任部门Controller
 * 
 * <AUTHOR>
 * @date 2024-01-19
 */
@Controller
@RequestMapping("/responsibleDepart")
public class UserDeptFuzeController extends BaseController
{

    @Autowired
    private IUserDeptFuzeService userDeptFuzeService;


    /**
     * 查询用户责任部门列表
     */
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo list(UserDeptParam userDeptFuze)
    {
        startPage();
        List<UserDeptVo> list = userDeptFuzeService.selectUserDeptFuzeList(userDeptFuze);
        return getDataTable(list);
    }

    /**
     * 新增保存用户责任部门
     */
    @PostMapping("/add")
    @ResponseBody
    @Log(title="重大项目-新增用户责任部门",businessType = BusinessType.INSERT)
    public AjaxResult addSave(@RequestBody AddUserDeptParam addUserDeptParam)
    {
        return toAjax(userDeptFuzeService.insertUserDeptFuze(addUserDeptParam));
    }

    @GetMapping( "/getMyDept")
    @ResponseBody
    public BaseResult<?> getMyDept(@RequestParam(name = "userId")Long userId)
    {
        List<SysDept> result = userDeptFuzeService.getMyDept(userId);
        return  BaseResult.ok(result);
    }
}
