package com.ruoyi.system.keywork.mapper;
import com.onecity.os.system.api.domain.SysDept;
import com.ruoyi.system.domain.vo.UserNameDto;
import com.ruoyi.system.keywork.model.UserDeptFuze;
import com.ruoyi.system.keywork.model.vo.UserDeptParam;
import com.ruoyi.system.keywork.model.vo.UserDeptVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户责任部门Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-19
 */
public interface UserDeptFuzeMapper 
{
    /**
     * 查询用户责任部门列表
     * 
     * @param userDeptFuze 用户责任部门
     * @return 用户责任部门集合
     */
     List<UserDeptVo> selectUserDeptFuzeList(UserDeptParam userDeptFuze);

    /**
     * 查询责任部门ids
     * @param userId
     * @return
     */
     List<Long> selectDepartIdstByUserId(Long userId);

    /**
     * 查询责任部门名称
     * @param userId
     * @return
     */
     String selectDepartNamesByUserId(Long userId);
    /**
     * 新增用户责任部门
     * 
     * @param userDeptFuzeList 用户责任部门
     * @return 结果
     */
     int insertUserDeptFuze(@Param("userDeptFuzeList")List<UserDeptFuze> userDeptFuzeList);


    /**
     * 删除用户责任部门
     * 
     * @param userId 用户责任部门主键
     * @return 结果
     */
     int deleteUserDeptFuzeByUserId(Long userId);

    /**
     * 获取我的责任部门
     * @param userId
     * @return
     */
     List<SysDept> getMyDept(Long userId);

    /**
     * 通过部门id获取责任人
     */
    List<UserNameDto> selectUserByDeptId(@Param("deptIdList")List<Long> deptIdList);
}
