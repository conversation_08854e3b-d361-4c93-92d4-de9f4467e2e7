package com.ruoyi.system.keywork.mapper;


import com.ruoyi.system.keywork.model.WorkAction;

import java.util.List;

/**
 * 重点工作操作记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-19
 */
public interface WorkActionMapper 
{

    /**
     * 查询重点工作操作记录列表
     * 
     * @param workId 重点工作操作记录
     * @return 重点工作操作记录集合
     */
     List<WorkAction> selectWorkActionList(Long workId);

    /**
     * 新增重点工作操作记录
     * 
     * @param workAction 重点工作操作记录
     * @return 结果
     */
     int insertWorkAction(WorkAction workAction);


    /**
     * 删除重点工作操作记录
     * 
     * @param workId 重点工作操作记录主键
     * @return 结果
     */
     int deleteWorkActionById(Long workId);
}
