package com.ruoyi.system.keywork.schedule;

import com.onecity.os.common.core.domain.BaseResult;
import com.ruoyi.system.keywork.service.KeyWorkService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class KeyWorkSchedule {

    @Autowired
    private KeyWorkService keyWorkService;

    /**
     * 判断重点工作是否逾期然后更新其状态
     * * 0/10 * * * ?
     */
//    @Scheduled(cron = "0 0 1 * * ?")
    @Scheduled(cron = "0 0/10 * * * ?")
    public void keyWorkYuQi() {
        log.info("判断重大项目是否逾期开始");
        keyWorkService.checkYuQi();
        log.info("判断重大项目是否逾期结束");
    }
}
