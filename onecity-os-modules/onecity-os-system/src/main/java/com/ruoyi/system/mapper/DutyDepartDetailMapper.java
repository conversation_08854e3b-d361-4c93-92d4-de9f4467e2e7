package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.DutyDepartDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 值班详情列表
 *
 * <AUTHOR>
 * @since 2024-05-16 13:55:27
 */
@Mapper
public interface DutyDepartDetailMapper {


    /**
     * 批量新增部门详情=值班信息
     *
     * @param dutyDepartDetails
     * @return
     */
    int insertDutyDepartDetails(@Param("dutyDepartDetails") List<DutyDepartDetail> dutyDepartDetails);

    /**
     * 批量更新部门详情=值班信息
     *
     * @param dutyDepartDetails
     * @return
     */
    int updateDutyDepartDetails(@Param("dutyDepartDetails") List<DutyDepartDetail> dutyDepartDetails);

    /**
     * 查询部门值班详情 降序
     *
     * @param dutyId
     * @param departId
     * @return
     */
    List<DutyDepartDetail> selectList(@Param("dutyId") String dutyId, @Param("departId") Long departId);

    /**
     * 查询部门值班详情
     * 根据 ids
     * @param ids
     * @return
     */
    List<DutyDepartDetail> selectListByIds(@Param("ids") List<String> ids);

    /**
     * 逻辑删除，用于变更部门信息
     * 逻辑删除并同步更新
     *
     * @param dutyId
     * @param departId
     * @return
     */
    int deleteDutyDepartDetails(@Param("dutyId") String dutyId, @Param("departId") Long departId);

    /**
     *  根据 ids 批量逻辑删除
     * @param ids
     * @return
     */
    int deleteDetailsByIds(@Param("ids") List<String> ids);


}