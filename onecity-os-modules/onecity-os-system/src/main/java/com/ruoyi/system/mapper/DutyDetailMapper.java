package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.DutyDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 值班信息表
 *
 * <AUTHOR>
 * @since 2024-05-16 14:04:08
 */
@Mapper
public interface DutyDetailMapper {

    /**
     * 新增
     *
     * @param dutyDetail
     * @return
     */
    int insertSelective(DutyDetail dutyDetail);

    /**
     * 查询值班所有部门信息
     * 模糊搜索 部门、报告名
     *
     * @param dutyId
     * @return
     */
    List<DutyDetail> selectDutyDetailList(@Param("dutyId") String dutyId, @Param("departId") Long departId, @Param("departName") String departName);

    /**
     * 查询值班所有部门信息
     * 模糊搜索 部门、报告名
     *
     * @param dutyId
     * @return
     */
    List<DutyDetail> selectDutyReportList(@Param("dutyId") String dutyId, @Param("reportName") String reportName);

    /**
     * 查询值班部门信息
     *
     * @param dutyId
     * @param departId
     * @return
     */
    DutyDetail selectDutyDetail(@Param("dutyId") String dutyId, @Param("departId") Long departId);

    /**
     * 更新部门值班报告
     *
     * @param dutyId
     * @param departId
     * @param dutyReportName
     * @param dutyReportAddress
     */
    int updateReport(@Param("dutyId") String dutyId, @Param("departId") Long departId, @Param("dutyReport") String dutyReport, @Param("dutyReportName") String dutyReportName, @Param("dutyReportAddress") String dutyReportAddress);

    /**
     * 更新部门填报状态
     *
     * @param dutyId
     * @param departId
     * @param dutyStatus 0-未完成1-已完成
     */

    int updateDepartStatus(@Param("dutyId") String dutyId, @Param("departId") Long departId, @Param("dutyStatus") int dutyStatus);

    /**
     * 更新部门值班信息
     *
     * @param dutyId
     * @param departId
     * @param departName
     * @param dutyLeader
     * @param dutyLeaderPhone todo 加解密
     */

    int updateDepartInfo(@Param("dutyId") String dutyId, @Param("departId") Long departId, @Param("departName") String departName, @Param("dutyLeader") String dutyLeader, @Param("dutyLeaderPhone") String dutyLeaderPhone);

    /**
     * 逻辑删除，用于变更部门信息
     * 逻辑删除并同步更新
     *
     * @param dutyId
     * @param departId
     * @return
     */
    int deleteInfo(@Param("dutyId") String dutyId, @Param("departId") Long departId);

}