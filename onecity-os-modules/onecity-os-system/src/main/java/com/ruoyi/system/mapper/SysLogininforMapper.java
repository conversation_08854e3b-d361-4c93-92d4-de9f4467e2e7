package com.ruoyi.system.mapper;

import com.onecity.os.system.api.domain.SysLogininfor;
import com.ruoyi.system.domain.SysLog;
import com.ruoyi.system.domain.SysLogininforSelect;
import com.ruoyi.system.domain.vo.DingOperateDataExcel;
import com.ruoyi.system.domain.vo.DingOperateVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 系统访问日志情况信息 数据层
 * 
 * <AUTHOR>
 */
public interface SysLogininforMapper
{
    /**
     * 新增系统登录日志
     * 
     * @param logininfor 访问日志对象
     */
    public int insertLogininfor(SysLogininfor logininfor);

    /**
     * 查询系统登录日志集合
     * 
     * @param logininfor 访问日志对象
     * @return 登录记录集合
     */
    public List<SysLogininfor> selectLogininforList(SysLogininfor logininfor);

    /**
     * 查询系统登录日志集合结构适配jeecg
     *
     * @param vo 访问日志对象
     * @return 登录记录集合
     */
    public List<SysLog> selectLogininforListJeecg(SysLogininforSelect vo);

    /**
     * 批量删除系统登录日志
     * 
     * @param infoIds 需要删除的登录日志ID
     * @return 结果
     */
    public int deleteLogininforByIds(Long[] infoIds);

    /**
     * 清空系统登录日志
     * 
     * @return 结果
     */
    public int cleanLogininfor();

    /**
     * 获取系统总访问次数
     *
     * @return Long
     */
    Long findTotalVisitCount();

    /**
     * 获取APP系统总访问次数
     *
     * @return Long
     */
    Long findDingTotalVisitCount();

    //update-begin--Author:zhangweijian  Date:20190428 for：传入开始时间，结束时间参数
    /**
     * 获取系统今日访问次数
     *
     * @return Long
     */
    Long findTodayVisitCount(@Param("dayStart") Date dayStart, @Param("dayEnd") Date dayEnd);
    /**
     * 获取系统pc总访问次数
     *
     * @return Long
     */
    Long findAllVisitPcCount(@Param("today") Date today);

    /**
     * 获取pc端系统今日访问用户数
     *
     * @return Long
     */
    Long findTodayVisitUserCount(@Param("dayStart") Date dayStart, @Param("dayEnd") Date dayEnd);

    /**
     * 获取APP端系统今日访问用户数
     *
     * @return Long
     */
    Long findDingTodayVisitUserCount(@Param("dayStart") Date dayStart, @Param("dayEnd") Date dayEnd);
    /**
     * 获取APP端系统访问总数
     *
     * @return Long
     */
    Long findDingAllVisitUserCount(@Param("today") Date today);


    /**
     * 获取APP系统今日访问次数
     *
     * @return Long
     */
    Long findDingTodayVisitCount(@Param("dayStart") Date dayStart, @Param("dayEnd") Date dayEnd);

    /**
     * 获取系统今日访问 IP数 （非钉钉）
     *
     * @return Long
     */
    Long findTodayIp(@Param("dayStart") Date dayStart, @Param("dayEnd") Date dayEnd);
    //update-end--Author:zhangweijian  Date:20190428 for：传入开始时间，结束时间参数

    /**
     * 获取系统今日访问 IP数 （钉钉端）
     *
     * @return Long
     */
    Long findDingTodayIp(@Param("dayStart") Date dayStart, @Param("dayEnd") Date dayEnd);
    /**
     *   首页：根据时间统计访问数量/ip数量（非钉钉）
     * @param dayStart
     * @param dayEnd
     * @return
     */
    List<Map<String,Object>> findVisitCount(@Param("dayStart") Date dayStart, @Param("dayEnd") Date dayEnd, @Param("dbType") String dbType);

    /**
     *   首页：根据时间统计访问数量/ip数量（钉钉端）
     * @param dayStart
     * @param dayEnd
     * @return
     */
    List<Map<String,Object>> findDingVisitCount(@Param("dayStart") Date dayStart, @Param("dayEnd") Date dayEnd, @Param("dbType") String dbType);

    /**
     * 获取APP操作日志
     * @param dayStart dayStart
     * @param dayEnd dayEnd
     * @return
     */
    List<DingOperateVo> getDingOperateLog(@Param("dayStart") Date dayStart, @Param("dayEnd") Date dayEnd);

    /**
     * 导出APP操作日志
     * @param dayStart dayStart
     * @param dayEnd dayEnd
     * @return
     */
    List<DingOperateDataExcel> exportOperateLog(@Param("dayStart") Date dayStart, @Param("dayEnd") Date dayEnd);


}
