package com.ruoyi.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.ruoyi.system.domain.SysMenu;

/**
 * 菜单表 数据层
 * 
 * <AUTHOR>
 */
public interface SysMenuMapper
{
    /**
     * 查询系统菜单列表
     * 
     * @param menu 菜单信息
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuList(SysMenu menu);

    /**
     * 根据用户所有权限
     * 
     * @return 权限列表
     */
    public List<String> selectMenuPerms();

    /**
     * 根据用户查询系统菜单列表
     * 
     * @param menu 菜单信息
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuListByUserId(SysMenu menu);

    /**
     * 根据用户ID查询权限
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    public List<String> selectMenuPermsByUserId(Long userId);

    /**
     * 根据用户ID查询菜单
     * 
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuTreeAll();

    /**
     * 根据用户ID查询菜单
     *
     * @return 菜单列表
     */
    public List<com.onecity.os.system.api.domain.SysMenu> selectMenuTreeAll1();
    /**
     * 根据用户ID查询菜单
     * 
     * @param userId 用户ID
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuTreeByUserId(Long userId);

    /**
     * 根据用户ID查询菜单
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    public List<com.onecity.os.system.api.domain.SysMenu> selectMenuTreeByUserId1(Long userId);

    /**
     * 根据角色ID查询菜单树信息
     * 
     * @param roleId 角色ID
     * @param menuCheckStrictly 菜单树选择项是否关联显示
     * @return 选中菜单列表
     */
    public List<Integer> selectMenuListByRoleId(@Param("roleId") Long roleId, @Param("menuCheckStrictly") boolean menuCheckStrictly);

    /**
     * 根据菜单ID查询信息
     * 
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    public SysMenu selectMenuById(Long menuId);

    /**
     * 是否存在菜单子节点
     * 
     * @param menuId 菜单ID
     * @return 结果
     */
    public int hasChildByMenuId(Long menuId);

    /**
     * 是否存在菜单子节点包括全部类型的子菜单
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    public int hasChildByMenuIdAll(Long menuId);

    /**
     * 新增菜单信息
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    public int insertMenu(SysMenu menu);

    /**
     * 修改菜单信息
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    public int updateMenu(SysMenu menu);

    /**
     * 删除菜单管理信息
     * 
     * @param menuId 菜单ID
     * @return 结果
     */
    public int deleteMenuById(Long menuId);

    /**
     * 删除子菜单管理信息
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    public int deleteMenuByParentId(Long menuId);
    
    /**
     * 校验菜单名称是否唯一
     * 
     * @param menuName 菜单名称
     * @param parentId 父菜单ID
     * @return 结果
     */
    public SysMenu checkMenuNameUnique(@Param("menuName") String menuName, @Param("parentId") Long parentId);

    /**
     * 校验菜单编码是否唯一
     *
     * @param sourceSimpleName 菜单路径中编码
     * @param path 父菜单ID
     * @return 结果
     */
    public SysMenu checkMenusourceSimpleNameUnique(@Param("sourceSimpleName") String sourceSimpleName, @Param("path") String path);

    /**
     * 根据path中的板块编码与菜单的parent_id更新菜单名称
     * @param menuName
     * @param sourceSimpleName
     * @param path
     * @return
     */
    int updateNameBySourceSimpleNameAndPath(@Param("menuName")String menuName,@Param("sourceSimpleName") String sourceSimpleName, @Param("path") String path);

    int updateStatusBySourceSimpleNameAndPath(@Param("status")String status,@Param("sourceSimpleName") String sourceSimpleName, @Param("path") String path);

    int updateStatusByParam(@Param("menuName") String menuName, @Param("parentId") Long parentId, @Param("status") String status);

    List<SysMenu> getSystemMenuList();

    List<SysMenu> getMenuListByParent(Long parentId);
}
