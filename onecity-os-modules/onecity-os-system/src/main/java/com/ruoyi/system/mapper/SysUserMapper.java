package com.ruoyi.system.mapper;

import com.onecity.os.system.api.domain.SysUser;
import com.ruoyi.system.domain.vo.BatchParams;
import com.ruoyi.system.domain.vo.SysUserParam;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 用户表 数据层
 * 
 * <AUTHOR>
 */
public interface SysUserMapper
{
    /**
     * 根据条件分页查询用户列表
     * 
     * @param sysUser 用户信息
     * @return 用户信息集合信息
     */
     List<SysUser> selectUserList(SysUserParam sysUser);
    /**
     * 根据条件分页查询未已配用户角色列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
     List<SysUser> selectAllocatedList(SysUser user);

    /**
     * 根据条件分页查询未分配用户角色列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
     List<SysUser> selectUnallocatedList(SysUser user);

    /**
     * 通过用户名查询用户
     * 
     * @param userName 用户名
     * @return 用户对象信息
     */
     SysUser selectUserByUserName(String userName);

     SysUser selectUserByMobile(String mobile);

    /**
     * 通过用户ID查询用户
     * 
     * @param userId 用户ID
     * @return 用户对象信息
     */
     SysUser selectUserById(Long userId);

    /**
     * 新增用户信息
     * 
     * @param user 用户信息
     * @return 结果
     */
     int insertUser(SysUser user);

    /**
     * 修改用户信息
     * 
     * @param user 用户信息
     * @return 结果
     */
     int updateUser(SysUser user);

    /**
     * 修改用户头像
     * 
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
     int updateUserAvatar(@Param("userName") String userName, @Param("avatar") String avatar);

    /**
     * 重置用户密码
     * 
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
     int resetUserPwd(@Param("userName") String userName, @Param("password") String password);

    /**
     * 通过用户ID删除用户
     * 
     * @param userId 用户ID
     * @return 结果
     */
     int deleteUserById(Long userId);

    /**
     * 批量删除用户信息
     * 
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
     int deleteUserByIds(Long[] userIds);

    int changeStatusBatch(BatchParams param);

    /**
     * 校验用户名称是否唯一
     * 
     * @param userName 用户名称
     * @return 结果
     */
     int checkUserNameUnique(String userName);

    /**
     * 校验手机号码是否唯一
     *
     * @param phonenumber 手机号码
     * @return 结果
     */
     SysUser checkPhoneUnique(String phonenumber);


    /**
     * 校验身份证号是否唯一
     *
     * @param idCode 用户身份证
     * @return 结果
     */
     SysUser checkIDCodeUnique(String idCode);

    /**
     * 获取部门下用户列表
     * @param deptId
     * @param userName
     * @param nickName
     * @return
     */
    List<SysUser> getUserByDepId(@Param("deptId")Long deptId, @Param("userName")String userName, @Param("nickName")String nickName);

    /**
     * 根据用户id,查找pc端姓名
     *
     * @param ids
     * @return
     */
    String getPcUserNamesByPcUserIds(@Param("ids") String[] ids);

    /**
     * 根据用户名,查找pc端人员id
     *
     * @param names
     * @return
     */
    String getPcUserIdsByPcUserNames(@Param("names") String[] names);

    /**
     * 根据用户名,查找真实姓名
     *
     * @param userName
     * @return
     */
    String getNickNameByName(@Param("userName")String userName);

    Integer getRoleLevelByUserId(Long userId);

    List<SysUser> getUserByLevelAndDepartIds(@Param("levelUp")Integer levelUp, @Param("levelDown")Integer levelDown,@Param("ids") List<Long> ids);

    List<SysUser> getUserByLevelAndParams(@Param("levelUp")Integer levelUp, @Param("levelDown")Integer levelDown,@Param("name") String name);

    int deleteUserByUserName(String userName);

    /**
     * 根据用户名更新用户
     * @param user
     * @return
     */
    int updateUserByUserName(SysUser user);

    /**
     * 根据菜单获取有权限的用户
     * @param menu 菜单名称
     * @return
     */
    List<String> getUserIdByMenu(String menu);

    /**
     * 根据角色编码获取有权限的用户
     * @param roleKey 角色编码名称
     * @return
     */
    List<String> getUserIdByRoleKey(String roleKey);

    /**
     * 根据部门id查询用户
     * @param departId
     * @return
     */
    List<SysUser> getUserByDepartId(@Param("departId") String departId,
                                    @Param("userId") String userId);

    /**
     * 根据部门id查询用户
     * @param departId
     * @return
     */
    List<SysUser> getAuditUserByDepartId(@Param("departId") String departId,
                                    @Param("userId") String userId,@Param("name") String name,@Param("sourceSimpleName") String sourceSimpleName);

    /**
     * 根据用户ID批量查询查询用户
     * @param userIds
     * @return
     */
    List<SysUser> getUserByPcUserIds(@Param("userIds") String[] userIds);

    SysUser getUserByOneCodeId(@Param("oneCodeUserId") String oneCodeUserId);
    /**
     * 根据用户角色,查找pc端人员id
     *
     * @param roleIds
     * @return
     */
    String getUserIdsByRoles(@Param("roleIds") String[] roleIds);

    int getUserIndicatorTitlePerms(@Param("sourceSimpleName") String sourceSimpleName, @Param("userId") Long userId);
    void updateUserByOneCodeId(SysUser user);
    int getUserArchivePerms(@Param("userId") Long userId);

    int getInformCleanPerms(@Param("userId") Long userId);


    List<String> getOutReportRoleUserId(Long userId);

    /**
     * 根据 userid 判断是否是管理员
     * @param userId
     * @return
     */
    String checkAdminByUserId(@Param("userId") Long userId);

    /**
     * 获取今日之前总用户数
     *
     * @return Long
     */
    Long queryAllUserCount(@Param("today") Date today);
    /**
     * 获取所有用户列表
     *
     * @return List
     */
    List<SysUser> queryAllUserList();
}
