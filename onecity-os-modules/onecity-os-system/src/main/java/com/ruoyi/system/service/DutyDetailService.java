package com.ruoyi.system.service;

import com.onecity.os.common.core.domain.BaseResult;
import com.ruoyi.system.domain.vo.DutyDepartVo;
import com.ruoyi.system.domain.vo.DutyDetailVo;
import com.ruoyi.system.domain.vo.DutyReportVo;
import com.ruoyi.system.domain.vo.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-05-16 14:04:08
 */
public interface DutyDetailService {

    /**
     * 值班部门列表
     *
     * @param dutyId        任务 id
     * @param departId        部门 id
     * @param departName    部门名称 模糊
     * @param requestSource 请求来源 APP、PC
     * @return
     */
    PageResult dutyList(String dutyId, Long departId, String departName, String requestSource, Integer pageNum, Integer pageSize);

    /**
     * 新增任务后调用
     *
     * @param dutyId
     * @return
     */
    Boolean beatchInsertDutyDepart(String dutyId);

    /**
     * 谨慎调用！：删除值班任务后，批量删除值班部门信息、值班部门详情信息
     *
     * @param dutyId
     * @return
     */
    Boolean deleteDutyDepart(String dutyId);

    /**
     * 值班报告详情列表
     * 报告名称模糊
     *
     * @param dutyId   任务 id
     * @param departId 部门 id
     * @return
     */
    PageResult reportList(String dutyId, String reportName, Integer pageNum, Integer pageSize);

    /**
     * 值班部门列表
     * app
     *
     * @param dutyId
     * @param departId
     * @return
     */
    PageResult departDetail(String dutyId, Long departId, Integer pageNum, Integer pageSize);


    /**
     * 更新部门值班报告
     *
     * @param dutyId
     * @param departId
     * @param fileName 文件名
     * @param suffix   文件后缀名
     * @return
     */
    BaseResult<String> updateReportPath(String dutyId, Long departId, String fileName, String path);

    /**
     * 编辑值班部门列表
     *
     * @param dutyDetailVo
     * @return
     */
    BaseResult<String> updateDepart(DutyDetailVo dutyDetailVo);

    /**
     * 批量新增值班部门详情
     *
     * @param dutyDepartVos
     * @return
     */
    BaseResult<String> addDepartDetail(List<DutyDepartVo> dutyDepartVos);

    /**
     * 批量编辑值班部门详情
     *
     * @param dutyDepartVos
     * @return
     */
    BaseResult<String> updateDepartDetail(List<DutyDepartVo> dutyDepartVos);


    /**
     * 删除值班部门值班详情 支持批量
     *
     * @param ids
     * @return
     */
    BaseResult<String> deleteDetailList(String dutyId, Long departId, List<String> ids);


}