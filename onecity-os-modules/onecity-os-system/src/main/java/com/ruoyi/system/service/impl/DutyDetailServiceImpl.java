package com.ruoyi.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.IdUtils;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.system.api.domain.SysDept;
import com.onecity.os.system.api.domain.SysUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.domain.DutyDepartDetail;
import com.ruoyi.system.domain.DutyDetail;
import com.ruoyi.system.domain.enums.ErrorCodeEnum;
import com.ruoyi.system.domain.enums.RequestSourceEnum;
import com.ruoyi.system.domain.vo.DutyDepartVo;
import com.ruoyi.system.domain.vo.DutyDetailVo;
import com.ruoyi.system.domain.vo.DutyReportVo;
import com.ruoyi.system.domain.vo.PageResult;
import com.ruoyi.system.duty.mapper.DutyMapper;
import com.ruoyi.system.duty.model.entity.Duty;
import com.ruoyi.system.feign.TodoFeignService;
import com.ruoyi.system.mapper.DutyDepartDetailMapper;
import com.ruoyi.system.mapper.DutyDetailMapper;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.DutyDetailService;
import com.ruoyi.system.utils.FastDFSClient;
import com.ruoyi.system.utils.RsaUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2024-05-16 14:04:08
 */
@Service
@Slf4j
public class DutyDetailServiceImpl implements DutyDetailService {

    @Resource
    private DutyDetailMapper dutyDetailMapper;
    @Resource
    private DutyDepartDetailMapper dutyDepartDetailMapper;

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private DutyMapper dutyMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private FastDFSClient fastDFSClient;
    @Resource
    private TodoFeignService todoFeignService;

    @Value("${login.privateKey}")
    String privateKey;

    /**
     * 值班报告文件(doc/docx/pdf)后缀 支持的类型
     */
    private static final String[] FILE_SUFFIX_SUPPORT = {".pdf"};

    /**
     * 一级部门
     */
    private static final Long ONE_DEPART_PARENT_ID = 0L;

    /**
     * 更新校验 id 标识
     */
    private static final String CHECK_ID_FLAG = "update";

    public static final String REGEX_ID = "[0-9a-fA-F]{8}[0-9a-fA-F]{4}[0-9a-fA-F]{4}[0-9a-fA-F]{4}[0-9a-fA-F]{12}";
    /**
     * 手机号正则
     * 同 RegexPatterns.PHONE_REGEX
     */
    public static final String REGEX_PHONE = "1[3-9]\\d{9}";
    /**
     * 文件名非法字符正则
     */
    public static final String REGEX_FILE_NAME = "[\\u00A0\\s\"`~!@#$%^&*()+=|{}':;',\\[\\]<>/?~！@#￥%……&*（）——+|{}【】‘；：”“'。，、？]";


    /**
     * 值班部门列表
     *
     * @param dutyId        部门 id
     * @param departName    部门名称 模糊
     * @param requestSource 请求来源 APP、PC
     * @return
     */
    @Override
    public PageResult dutyList(String dutyId, Long departId, String departName, String requestSource, Integer pageNum, Integer pageSize) {
        if (StringUtils.isBlank(dutyId) || !dutyId.matches(REGEX_ID)) {
            // uuid 校验失败
            return new PageResult();
        }
        // 默认请求来源为 pc
        requestSource = StringUtils.isBlank(RequestSourceEnum.getDesc(requestSource)) ? RequestSourceEnum.PC.getKey() : requestSource;
        Long userId = SecurityUtils.getUserId();
        if (requestSource.equals(RequestSourceEnum.PC.getKey())) {
            // 根据 id 判断是不是管理员 非管理员逻辑
            if (!cherckIsAdminRole(userId)) {
                // 非管理员  获取所在部门的一级部门
                Long userDepartId = getOnelevelDepartId(userId);
                if (null == userDepartId) {
                    // 异常处理 获取用户所在部门 一级部门异常  即查不出来
                    log.warn("值班值守 dutyList获取用户一级部门失败userId：{} ，departId：{}", userId, departId);
                    return new PageResult();
                } else if (null != departId && !departId.equals(userDepartId)) {
                    log.warn("值班值守 dutyList 用户入参一级部门 departId 与本人不一致，即参数错误userId：{} userDepartId:{}，departId：{}", userId, userDepartId, departId);
                    return new PageResult();
                } else {
                    // 没传部门 id  pc 场景只允许查本人一级部门 或管理员指定了某个一级部门 或查询本部门
                    departId = userDepartId;
                }

            }
        }
        List<DutyDetailVo> dutyDetailVos = new ArrayList<>();
        // 手动分页
        PageHelper.startPage(pageNum, pageSize);
        List<DutyDetail> dutyDetails = Optional.ofNullable(dutyDetailMapper.selectDutyDetailList(dutyId, departId, departName)).orElse(new ArrayList<>());
        Long total = new PageInfo(dutyDetails).getTotal();
        int pages = new PageInfo(dutyDetails).getPages();
        for (DutyDetail dutyDetail : dutyDetails) {
            DutyDetailVo dutyDetailVo = new DutyDetailVo();
            BeanUtils.copyProperties(dutyDetail, dutyDetailVo);
            String dutyLeaderPhone = dutyDetail.getDutyLeaderPhone();
            if (StringUtils.isNotBlank(dutyLeaderPhone)) {
                //手机号解密
                try {
                    String phone = RsaUtils.decryptByPrivateKey(RsaUtils.pri, dutyDetail.getDutyLeaderPhone());
                    dutyDetailVo.setDutyLeaderPhone(phone);
                } catch (Exception e) {
                    log.error("值班值守 dutyList手机号解密异常：{} ", JSONObject.toJSONString(dutyDetail.getDutyLeaderPhone()), e);
                }
            }
            dutyDetailVos.add(dutyDetailVo);
        }
        log.info("值班值守 dutyList 获取部门入参 dutyId：{} departId:{}，departName:{} ，requestSource:{}", JSONObject.toJSONString(dutyId), JSONObject.toJSONString(departId), JSONObject.toJSONString(departName), JSONObject.toJSONString(requestSource));
        return new PageResult(dutyDetailVos, total, (long) pages);
    }


    /**
     * 新增任务后调用
     *
     * @param dutyId
     * @return
     */
    @Override
    public Boolean beatchInsertDutyDepart(String dutyId) {
        if (StringUtils.isBlank(dutyId) || !dutyId.matches(REGEX_ID)) {
            // uuid 校验失败
            return false;
        }
        // 获取所有一级部门 并根据部门id 排序 此处存在一级部门新增后，历史值班任务没有新的一级部门数据，需要删除任务重新建
        List<SysDept> departList = sysDeptMapper.getDepartByParentId(ONE_DEPART_PARENT_ID);
        log.info("值班值守 beatchInsertDutyDepart 新增值班任务后，批量新增值班部门信息userId：{}, dutyId：{} ", SecurityUtils.getUserId(), dutyId);
        for (SysDept sysDept : departList) {
            DutyDetail dutyDetail = new DutyDetail();
            String dutyDepartId = IdUtils.simpleUUID().trim();
            dutyDetail.setId(dutyDepartId);
            dutyDetail.setDutyId(dutyId);
            dutyDetail.setDepartId(sysDept.getDeptId());
            dutyDetail.setDepartName(sysDept.getDeptName());
            dutyDetail.setOrderNum(Integer.parseInt(sysDept.getOrderNum()));
            dutyDetail.setCreateTime(new Date());
            dutyDetailMapper.insertSelective(dutyDetail);
        }
        return true;
    }

    /**
     * 谨慎调用！：删除值班任务后，批量删除值班部门信息、值班部门详情信息
     *
     * @param dutyId
     * @return
     */
    @Override
    public Boolean deleteDutyDepart(String dutyId) {
        if (StringUtils.isBlank(dutyId) || !dutyId.matches(REGEX_ID)) {
            // uuid 校验失败
            return false;
        }
        log.info("值班值守 deleteDutyDepart 删除值班任务后，批量删除值班部门信息、值班部门详情信息userId：{}, dutyId：{} ", SecurityUtils.getUserId(), dutyId);
        dutyDetailMapper.deleteInfo(dutyId, null);
        dutyDepartDetailMapper.deleteDutyDepartDetails(dutyId, null);

        return true;
    }

    /**
     * 值班报告详情列表
     * 报告名称模糊
     *
     * @param dutyId     任务 id
     * @param reportName 报告名称 模糊
     * @return
     */
    @Override
    public PageResult reportList(String dutyId, String reportName, Integer pageNum, Integer pageSize) {
        // 参数校验 非空
        if (StringUtils.isBlank(dutyId) || !dutyId.matches(REGEX_ID)) {
            return new PageResult();
        }
        log.info("值班值守 reportList值班报告列表入参：dutyId:{},reportName:{} ", JSONObject.toJSONString(dutyId), JSONObject.toJSONString(reportName));
        List<DutyReportVo> dutyReportVos = new ArrayList<>();
        // 手动分页
        PageHelper.startPage(pageNum, pageSize);
        List<DutyDetail> dutyDetails = dutyDetailMapper.selectDutyReportList(dutyId, reportName);
        Long total = new PageInfo(dutyDetails).getTotal();
        int pages = new PageInfo(dutyDetails).getPages();
        for (DutyDetail dutyDetail : dutyDetails) {
            DutyReportVo dutyReportVo = new DutyReportVo();
            BeanUtils.copyProperties(dutyDetail, dutyReportVo);
            dutyReportVos.add(dutyReportVo);
        }
        return new PageResult(dutyReportVos, total, (long) pages);
    }

    /**
     * 值班部门详情列表
     *
     * @param dutyId
     * @param departId
     * @return
     */
    @Override
    public PageResult departDetail(String dutyId, Long departId, Integer pageNum, Integer pageSize) {
        // 参数校验 非空
        if (null == departId || StringUtils.isBlank(dutyId)) {
            return new PageResult();
        }
        if (StringUtils.isBlank(dutyId) || !dutyId.matches(REGEX_ID)) {
            // uuid 校验失败
            return new PageResult();
        }
        Duty duty = dutyMapper.queryById(dutyId);
        if (null == duty) {
            return new PageResult();
        }
        List<DutyDepartVo> dutyDepartVos = new ArrayList<>();
        // 手动分页
        PageHelper.startPage(pageNum, pageSize);
        // 获取值班任务部门值班详情列表 并赋值到 dutyDepartVos
        List<DutyDepartDetail> dutyDepartDetails = dutyDepartDetailMapper.selectList(dutyId, departId);
        Long total = new PageInfo(dutyDepartDetails).getTotal();
        int pages = new PageInfo(dutyDepartDetails).getPages();
        for (DutyDepartDetail dutyDepartDetail : dutyDepartDetails) {
            DutyDepartVo dutyDepartVo = new DutyDepartVo();
            BeanUtils.copyProperties(dutyDepartDetail, dutyDepartVo);
            String dutyPersonPhone = dutyDepartVo.getDutyPersonPhone();
            dutyDepartVo.setDepartDetailId(dutyDepartDetail.getId());
            // 添加值班任务名称
            dutyDepartVo.setDutyName(duty.getDutyName());
            //手机号解密
            try {
                String phone = RsaUtils.decryptByPrivateKey(RsaUtils.pri, dutyPersonPhone);
                dutyDepartVo.setDutyPersonPhone(phone);
            } catch (Exception e) {
                log.error("值班值守 departDetail值班部门详情手机号解密异常：dutyId:{},departId:{},dutyPersonPhone:{} ", JSONObject.toJSONString(dutyId), JSONObject.toJSONString(departId), JSONObject.toJSONString(dutyPersonPhone), e);
                dutyDepartVo.setDutyPersonPhone(dutyPersonPhone);
            }
            dutyDepartVos.add(dutyDepartVo);
        }
        return new PageResult(dutyDepartVos, total, (long) pages);
    }

    /**
     * 更新部门值班报告
     *
     * @param dutyId
     * @param departId
     * @param fileName 文件名
     * @param path     路径
     * @return
     */
    @Override
    public BaseResult<String> updateReportPath(String dutyId, Long departId, String fileName, String path) {
        // 参数校验 非空
        if (null == departId || StringUtils.isAnyBlank(dutyId, fileName, path) || !dutyId.matches(REGEX_ID)) {
            return BaseResult.fail(ErrorCodeEnum.ILLEGAL_ARGS_ERROR.getMsg());
        }
        if (!checkFileType(fileName)) {
            return BaseResult.fail(ErrorCodeEnum.ILLEGAL_FILE_TYPE_ERROR.getMsg());
        }
        log.info("值班值守 updateReport 部门值班报告更新 入参空校验通过 dutyId：{}，departId：{} ，fileName:{},path:{}  ", JSONObject.toJSONString(dutyId), JSONObject.toJSONString(departId), JSONObject.toJSONString(fileName), JSONObject.toJSONString(path));
        // 校验是否可以编辑
        ErrorCodeEnum checkResult = checkCanEditReport(SecurityUtils.getUserId(), departId);
        if (checkResult.getCode() != ErrorCodeEnum.Ok.getCode()) {
            return BaseResult.fail(checkResult.getMsg());
        }
        log.info("值班值守 updateReport 部门值班报告更新 入参权限校验通过 dutyId：{}，departId：{} ，fileName:{},path:{}  ", JSONObject.toJSONString(dutyId), JSONObject.toJSONString(departId), JSONObject.toJSONString(fileName), JSONObject.toJSONString(path));
        // 获取值班任务部门信息  冗余
        DutyDetail dutyDetail = dutyDetailMapper.selectDutyDetail(dutyId, departId);
        if (null == dutyDetail) {
            return BaseResult.fail(ErrorCodeEnum.ILLEGAL_ARGS_ERROR.getMsg());
        }
        String oldFilePath = dutyDetail.getDutyReportAddress();
        // 移除非法字符
        String reportName = Pattern.compile(REGEX_FILE_NAME).matcher(fileName).replaceAll("").trim();
        // 无论是否有报告 均新增记录
        String id = IdUtils.simpleUUID().trim();
        dutyDetail.setId(id);
        dutyDetail.setDutyReportAddress(path);
        dutyDetail.setDutyReportName(reportName);
        Date now = new Date();
        dutyDetail.setReportUploadTime(now);
        dutyDetail.setUpdateTime(now);
        dutyDetail.setHaveReport(1);
        // 更新日志
        log.info("值班值守 updateReport 部门值班报告更新dutyId：{}，departId：{} ,id:{} ", JSONObject.toJSONString(dutyId), JSONObject.toJSONString(departId), JSONObject.toJSONString(id));
        dutyDetailMapper.deleteInfo(dutyId, departId);
        int i = dutyDetailMapper.insertSelective(dutyDetail);
        if (i > 0) {
            if (StringUtils.isNotBlank(oldFilePath)) {
                // 有历史报告，需要调用删除文件接口
                log.info("值班值守 updateReport 部门值班报告更新 历史报告已删除 dutyId：{}，departId：{} ,oldFilePath:{} ", JSONObject.toJSONString(dutyId), JSONObject.toJSONString(departId), JSONObject.toJSONString(oldFilePath));
                fastDFSClient.deleteFile(oldFilePath);
            }
            return BaseResult.ok();
        }
        return BaseResult.fail(ErrorCodeEnum.FILE_UPLOAD_FAIL_ERROR.getMsg());
    }


    /**
     * 编辑值班部门信息
     *
     * @param dutyDetailVo
     * @return
     */
    @Override
    public BaseResult<String> updateDepart(DutyDetailVo dutyDetailVo) {
        // 参数校验 非空
        if (null == dutyDetailVo || null == dutyDetailVo.getDepartId() || StringUtils.isAnyBlank(dutyDetailVo.getDutyId(), dutyDetailVo.getDutyLeader(), dutyDetailVo.getDutyLeaderPhone())) {
            return BaseResult.fail(ErrorCodeEnum.ILLEGAL_ARGS_ERROR.getMsg());
        }
        String dutyId = dutyDetailVo.getDutyId();
        Long departId = dutyDetailVo.getDepartId();
        if (StringUtils.isBlank(dutyId) || !dutyId.matches(REGEX_ID)) {
            // uuid 校验失败
            return BaseResult.fail(ErrorCodeEnum.ILLEGAL_ARGS_ERROR.getMsg());
        }
        String phone = dutyDetailVo.getDutyLeaderPhone();
        // 联系方式正则校验
        if (!phone.matches(REGEX_PHONE)) {
            log.warn("值班值守 updateDepart 部门联系方式有误：{} ", JSONObject.toJSONString(phone));
            return BaseResult.fail(ErrorCodeEnum.ILLEGAL_PHONE_ERROR.getMsg());
        }
        String dutyLeader = dutyDetailVo.getDutyLeader();
        if (dutyLeader.length() > 8) {
            log.warn("值班值守 checkDutyDepartVo 值班信息姓名超过8位：dutyId:{} ,departId:{}，dutyLeader:{} ", JSONObject.toJSONString(dutyId), JSONObject.toJSONString(departId), JSONObject.toJSONString(dutyLeader));
            return BaseResult.fail(ErrorCodeEnum.NAME_TOO_LONG_ERROR.getMsg());
        }
        // 校验是否可以编辑
        ErrorCodeEnum checkResult = checkCanEdit(SecurityUtils.getUserId(), dutyId, departId);
        if (checkResult.getCode() != ErrorCodeEnum.Ok.getCode()) {
            return BaseResult.fail(checkResult.getMsg());
        }
        // 获取值班任务部门信息 主要为了保留值班报告 和创建时间
        DutyDetail dutyDetail = dutyDetailMapper.selectDutyDetail(dutyId, departId);
        if (null == dutyDetail) {
            return BaseResult.fail(ErrorCodeEnum.ILLEGAL_ARGS_ERROR.getMsg());
        }
        // 设置值班领导信息 校验 加密
        dutyDetail.setId(IdUtils.simpleUUID().trim());
        try {
            String numberRsa = RsaUtils.encryptByPublicKey(RsaUtils.pub, phone);
            dutyDetail.setDutyLeaderPhone(numberRsa);
        } catch (Exception e) {
            log.error("值班值守 updateDepart部门联系方式加密异常：{} ", JSONObject.toJSONString(phone), e);
            dutyDetail.setDutyLeaderPhone(phone);
        }
        dutyDetail.setDutyLeader(dutyLeader);
        dutyDetail.setUpdateTime(new Date());
        // 部门主表
        dutyDetailMapper.deleteInfo(dutyId, departId);
        int i = dutyDetailMapper.insertSelective(dutyDetail);
        if (i > 0) {
            return BaseResult.ok();
        }
        return BaseResult.fail(ErrorCodeEnum.UPDATE_FAIL_ERROR.getMsg());
    }

    /**
     * 新增部门值班详情列表
     *
     * @param dutyDepartVos
     * @return
     */
    @Override
    public BaseResult<String> addDepartDetail(List<DutyDepartVo> dutyDepartVos) {
        // 参数校验 非空
        if (CollUtil.isEmpty(dutyDepartVos)) {
            return BaseResult.fail(ErrorCodeEnum.ILLEGAL_ARGS_ERROR.getMsg());
        }
        String dutyId = dutyDepartVos.get(0).getDutyId();
        Long departId = dutyDepartVos.get(0).getDepartId();
        Duty duty = dutyMapper.queryById(dutyId);
        if (null == duty) {
            return BaseResult.fail(ErrorCodeEnum.ILLEGAL_ARGS_ERROR.getMsg());
        }
        // 获取值班周期
        Date dutyBeginDate = duty.getDutyBeginDate();
        Date dutyEndDate = duty.getDutyEndDate();
        // 获取所有更新的值班部门 id
        for (DutyDepartVo vo : dutyDepartVos) {
            // 参数校验
            ErrorCodeEnum checkResult = checkDutyDepartVo(vo, dutyBeginDate, dutyEndDate, "");
            if (checkResult.getCode() != ErrorCodeEnum.Ok.getCode()) {
                return BaseResult.fail(checkResult.getMsg());
            }
        }
        // 校验是否可以编辑
        ErrorCodeEnum checkResult = checkCanEdit(SecurityUtils.getUserId(), dutyId, departId);
        if (checkResult.getCode() != ErrorCodeEnum.Ok.getCode()) {
            return BaseResult.fail(checkResult.getMsg());
        }
        // 更新部门详情表
        List<DutyDepartDetail> dutyDepartDetails = new ArrayList<>();
        for (DutyDepartVo dutyDepartVo : dutyDepartVos) {
            DutyDepartDetail departDetail = new DutyDepartDetail();
            // 赋新值
            BeanUtils.copyProperties(dutyDepartVo, departDetail);
            departDetail.setId(IdUtils.simpleUUID().trim());
            departDetail.setDutyPersonName(dutyDepartVo.getDutyPersonName());
            departDetail.setDutyDate(dutyDepartVo.getDutyDate());
            departDetail.setCreateTime(new Date());
            String dutyPersonPhone = dutyDepartVo.getDutyPersonPhone();
            // 手机已经正则校验 加密
            try {
                String numberRsa = RsaUtils.encryptByPublicKey(RsaUtils.pub, dutyPersonPhone);
                departDetail.setDutyPersonPhone(numberRsa);
            } catch (Exception e) {
                log.error("值班值守 updateDepartDetail值班人联系方式加密异常：{} ", JSONObject.toJSONString(dutyPersonPhone), e);
                departDetail.setDutyPersonPhone(dutyPersonPhone);
            }
            dutyDepartDetails.add(departDetail);
        }
        // 根据 批量新增的部门详情表
        int i = dutyDepartDetailMapper.insertDutyDepartDetails(dutyDepartDetails);
        if (i > 0) {
            // 更新值班部门填报状态为已填报
            dutyDetailMapper.updateDepartStatus(dutyId, departId, 1);
            //移除待办信息
            todoFeignService.deleteToDoByPram(dutyId,"4");
            return BaseResult.ok();
        }
        return BaseResult.fail(ErrorCodeEnum.ADD_FAIL_ERROR.getMsg());
    }

    /**
     * 更新值班部门详情
     *
     * @param dutyDepartVos
     * @return
     */
    @Override
    public BaseResult<String> updateDepartDetail(List<DutyDepartVo> dutyDepartVos) {
        // 参数校验 非空
        if (CollUtil.isEmpty(dutyDepartVos)) {
            return BaseResult.fail(ErrorCodeEnum.ILLEGAL_ARGS_ERROR.getMsg());
        }
        // 获取所有更新的值班部门 id
        List<String> ids = new ArrayList<>();
        Map<String, DutyDepartVo> dutyDepartVoMap = new HashMap<>();
        String dutyId = dutyDepartVos.get(0).getDutyId();
        Long departId = dutyDepartVos.get(0).getDepartId();
        Duty duty = dutyMapper.queryById(dutyId);
        if (null == duty) {
            return BaseResult.fail(ErrorCodeEnum.ILLEGAL_ARGS_ERROR.getMsg());
        }
        // 获取值班周期
        Date dutyBeginDate = duty.getDutyBeginDate();
        Date dutyEndDate = duty.getDutyEndDate();
        // 获取所有更新的值班部门 id
        for (DutyDepartVo vo : dutyDepartVos) {
            // 参数校验
            ErrorCodeEnum checkResult = checkDutyDepartVo(vo, dutyBeginDate, dutyEndDate, CHECK_ID_FLAG);
            if (checkResult.getCode() != ErrorCodeEnum.Ok.getCode()) {
                return BaseResult.fail(checkResult.getMsg());
            }
            ids.add(vo.getDepartDetailId());
            dutyDepartVoMap.put(vo.getDepartDetailId(), vo);
        }
        // 校验是否可以编辑
        ErrorCodeEnum checkResult = checkCanEdit(SecurityUtils.getUserId(), dutyId, departId);
        if (checkResult.getCode() != ErrorCodeEnum.Ok.getCode()) {
            return BaseResult.fail(checkResult.getMsg());
        }
        // 获取值班任务部门信息 主要为了创建时间
        List<DutyDepartDetail> oldDutyDepartDetails = dutyDepartDetailMapper.selectListByIds(ids);
        // 更新部门详情表
        List<DutyDepartDetail> dutyDepartDetails = new ArrayList<>();
        for (DutyDepartDetail departDetail : oldDutyDepartDetails) {
            String id = departDetail.getId();
            // 组装新的值班人详情
            DutyDepartDetail newDepartDetail = new DutyDepartDetail();
            BeanUtils.copyProperties(departDetail, newDepartDetail);
            // 赋值最新的值班人信息 id
            DutyDepartVo dutyDepartVo = dutyDepartVoMap.get(id);
            newDepartDetail.setId(IdUtils.simpleUUID().trim());
            newDepartDetail.setDutyPersonName(dutyDepartVo.getDutyPersonName());
            newDepartDetail.setDutyDate(dutyDepartVo.getDutyDate());
            newDepartDetail.setUpdateTime(new Date());
            String dutyPersonPhone = dutyDepartVo.getDutyPersonPhone();
            // 手机已经正则校验 加密
            try {
                String numberRsa = RsaUtils.encryptByPublicKey(RsaUtils.pub, dutyPersonPhone);
                newDepartDetail.setDutyPersonPhone(numberRsa);
            } catch (Exception e) {
                log.error("值班值守 updateDepartDetail值班人联系方式加密异常：{} ", JSONObject.toJSONString(dutyPersonPhone), e);
                newDepartDetail.setDutyPersonPhone(dutyPersonPhone);
            }
            dutyDepartDetails.add(newDepartDetail);
        }
        // 根据 ids 先删除旧的部门详情表
        dutyDepartDetailMapper.deleteDetailsByIds(ids);
        int i = dutyDepartDetailMapper.updateDutyDepartDetails(dutyDepartDetails);
        if (i > 0) {
            // 校验现有值班信息数 以便认部门填报状态
            updateDutyStatus(dutyId, departId, "update", i);
            return BaseResult.ok();
        }
        return BaseResult.fail(ErrorCodeEnum.UPDATE_FAIL_ERROR.getMsg());
    }


    /**
     * 删除值班部门列表
     *
     * @param dutyId
     * @param departId
     * @param ids
     * @return
     */
    @Override
    public BaseResult<String> deleteDetailList(String dutyId, Long departId, List<String> ids) {
        // 参数校验 非空
        if (null == departId || StringUtils.isBlank(dutyId) || !dutyId.matches(REGEX_ID) || CollUtil.isEmpty(ids)) {
            return BaseResult.fail(ErrorCodeEnum.ILLEGAL_ARGS_ERROR.getMsg());
        }
        for (String id : ids) {
            if (StringUtils.isBlank(id) || !id.matches(REGEX_ID)) {
                return BaseResult.fail(ErrorCodeEnum.ILLEGAL_ARGS_ERROR.getMsg());
            }
        }
        // 校验是否可以编辑
        ErrorCodeEnum checkResult = checkCanEdit(SecurityUtils.getUserId(), dutyId, departId);
        if (checkResult.getCode() != ErrorCodeEnum.Ok.getCode()) {
            return BaseResult.fail(checkResult.getMsg());
        }
        // 根据 ids 删除旧的部门详情表
        int i = dutyDepartDetailMapper.deleteDetailsByIds(ids);
        if (i > 0) {
            // 校验现有值班信息数 以便认部门填报状态
            updateDutyStatus(dutyId, departId, "delete", i);
            return BaseResult.ok();
        }
        return BaseResult.fail(ErrorCodeEnum.DELETE_FAIL_ERROR.getMsg());
    }

    /**
     * 更新或删除后校验是否更新部门填报状态
     *
     * @param dutyId
     * @param departId
     * @param type     update、delete、
     * @param i        实际操作条数
     */
    private void updateDutyStatus(String dutyId, Long departId, String type, int i) {
        // 校验现有值班信息数 以便认部门填报状态
        List<DutyDepartDetail> noeDutyDepartDetails = dutyDepartDetailMapper.selectList(dutyId, departId);
        if (CollUtil.isEmpty(noeDutyDepartDetails)) {
            // 更新值班部门填报状态为未填报
            dutyDetailMapper.updateDepartStatus(dutyId, departId, 0);
        }
        log.info("值班值守 updateDutyStatus值班人信息dutyId:{},departId:{}, 共{}更新：{}条 ", JSONObject.toJSONString(dutyId), JSONObject.toJSONString(departId), JSONObject.toJSONString(type), JSONObject.toJSONString(i));
    }

    /**
     * 校验值班部门详细信息
     *
     * @param dutyDepartVo
     * @param beginDate    值班开始日期
     * @param endDate      值班结束日期
     * @param type         add 、update 用来校验 区分id校验
     * @return
     */
    private ErrorCodeEnum checkDutyDepartVo(DutyDepartVo dutyDepartVo, Date beginDate, Date endDate, String type) {
        if (null == dutyDepartVo) {
            return ErrorCodeEnum.ILLEGAL_ARGS_ERROR;
        }
        String departDetailId = dutyDepartVo.getDepartDetailId();
        if (type.equals(CHECK_ID_FLAG)) {
            if (!(StringUtils.isNotBlank(departDetailId) && departDetailId.matches(REGEX_ID))) {
                log.warn("值班值守 checkDutyDepartVo 值班信息id有误：type:{} ,departDetailId:{} ", JSONObject.toJSONString(departDetailId), JSONObject.toJSONString(type));
                return ErrorCodeEnum.ILLEGAL_ARGS_ERROR;
            }
        }
        Long departId = dutyDepartVo.getDepartId();
        String dutyId = dutyDepartVo.getDutyId();
        String dutyPersonName = dutyDepartVo.getDutyPersonName();
        String dutyPersonPhone = dutyDepartVo.getDutyPersonPhone();
        Date dutyDate = dutyDepartVo.getDutyDate();
        if (StringUtils.isAnyBlank(dutyId, dutyPersonName, dutyPersonPhone) || null == departId || null == dutyDate) {
            log.warn("值班值守 checkDutyDepartVo 值班信息参数有误：dutyId:{} ,departId:{}，dutyPersonName:{} ,dutyPersonPhone:{},dutyDate:{}  ", JSONObject.toJSONString(dutyId), JSONObject.toJSONString(departId), JSONObject.toJSONString(dutyPersonName), JSONObject.toJSONString(dutyPersonPhone), JSONObject.toJSONString(dutyDate));
            return ErrorCodeEnum.ILLEGAL_ARGS_ERROR;
        }
        if (dutyPersonName.length() > 8) {
            log.warn("值班值守 checkDutyDepartVo 值班信息值班人姓名超过8位：dutyId:{} ,departId:{}，departDetailId:{} ,dutyPersonName:{} ", JSONObject.toJSONString(dutyId), JSONObject.toJSONString(departId), JSONObject.toJSONString(departDetailId), JSONObject.toJSONString(dutyPersonName));
            return ErrorCodeEnum.NAME_TOO_LONG_ERROR;
        }
        if (!dutyPersonPhone.matches(REGEX_PHONE)) {
            log.warn("值班值守 checkDutyDepartVo 值班信息手机号有误：dutyId:{} ,departId:{}，departDetailId:{} ,dutyPersonName:{} ", JSONObject.toJSONString(dutyId), JSONObject.toJSONString(departId), JSONObject.toJSONString(departDetailId), JSONObject.toJSONString(dutyPersonPhone));
            return ErrorCodeEnum.ILLEGAL_PHONE_ERROR;
        }
        if (beginDate.compareTo(dutyDate) <= 0 && dutyDate.compareTo(endDate) <= 0) {
            return ErrorCodeEnum.Ok;
        }
        log.warn("值班值守 checkDutyDepartVo 值班信息值班日期不在周期内：dutyId:{} ,departId:{}，departDetailId:{} ,beginDate:{},dutyDate:{},endDate:{} ", JSONObject.toJSONString(dutyId), JSONObject.toJSONString(departId), JSONObject.toJSONString(departDetailId), JSONObject.toJSONString(beginDate), JSONObject.toJSONString(dutyDate), JSONObject.toJSONString(endDate));
        return ErrorCodeEnum.DUTY_DATE_ERROR;

    }


    /**
     * 根据用户 id 判断是否是管理员角色
     *
     * @param userId
     * @return
     */
    private boolean cherckIsAdminRole(Long userId) {
        // 根据 id 判断是不是管理员
        String adminUserId = sysUserMapper.checkAdminByUserId(userId);
        return StringUtils.isNotBlank(adminUserId);
    }

    /**
     * 判断是否可以编辑
     * 1、管理员
     * 2：一级部门为编辑部门+值班任务在截止日期前
     *
     * @param userId
     * @param dutyId
     * @param departId
     * @return
     */
    private ErrorCodeEnum checkCanEdit(Long userId, String dutyId, Long departId) {
        // 获取值班任务 并 判断今天 是否在截止日期之前
        Duty duty = dutyMapper.queryById(dutyId);
        if (null == duty) {
            return ErrorCodeEnum.ILLEGAL_ARGS_ERROR;
        }
        // 需要包含今日, 不包含即超出日期
        if (new Date().compareTo(duty.getDeadline()) > 0) {
            return ErrorCodeEnum.EXCEEDING_DEADLINE_ERROR;
        }
        // 判断是否是管理员
        boolean isAdmin = cherckIsAdminRole(userId);
        if (isAdmin) {
            return ErrorCodeEnum.Ok;
        }
        // 根据用户获取所在一级部门 并判断与修改的值班部门 id 是否一致
        Long userDepartId = getOnelevelDepartId(userId);
        return Objects.equals(userDepartId, departId) ? ErrorCodeEnum.Ok : ErrorCodeEnum.ILLEGAL_ARGS_ERROR;
    }

    /**
     * 判断是否可以编辑值班报告
     * 1、管理员
     * 2：一级部门为编辑部门
     *
     * @param userId
     * @param departId
     * @return
     */
    private ErrorCodeEnum checkCanEditReport(Long userId, Long departId) {
        // 值班报告不再判断 是否在截止日期之前 即截止日期之后仍可上传报告
        // 判断是否是管理员
        boolean isAdmin = cherckIsAdminRole(userId);
        if (isAdmin) {
            return ErrorCodeEnum.Ok;
        }
        // 根据用户获取所在一级部门 并判断与修改的值班部门 id 是否一致
        Long userDepartId = getOnelevelDepartId(userId);
        return Objects.equals(userDepartId, departId) ? ErrorCodeEnum.Ok : ErrorCodeEnum.ILLEGAL_ARGS_ERROR;
    }

    /**
     * 根据部门 id 获取一级部门
     *
     * @param userId
     * @return
     */
    private Long getOnelevelDepartId(Long userId) {
        // 非管理员 获取部门 id
        SysUser sysUser = sysUserMapper.selectUserById(userId);
        Long departId = sysUser.getDeptId();
        if (null == departId) {
            return null;
        }
        // 查询所有部门  并组装 map  部门 id——>parertId
        List<SysDept> sysDepts = sysDeptMapper.selectAllDept();
        Map<Long, Long> departIdMap = new HashMap<>();
        for (SysDept sysDept : sysDepts) {
            if (null != sysDept.getDeptId()) {
                departIdMap.put(sysDept.getDeptId(), sysDept.getParentId());
            }
        }
        // 获取所在部门的一级部门 并记录一级部门 id
        int temp = 0;
        Long departIdTemp = departId;
        while (departIdMap.size() > temp) {
            Long parentId = departIdMap.getOrDefault(departIdTemp, null);
            if (Objects.equals(parentId, ONE_DEPART_PARENT_ID)) {
                log.info("值班值守 获取所在部门id：{}的一级部门:{} ", departId, departIdTemp);
                return departIdTemp;
            } else {
                temp++;
                departIdTemp = parentId;
            }
        }
        log.info("值班值守 未获取到所在部门id：{}的一级部门 ", departId);
        return null;
    }

    /**
     * 文件类型校验
     *
     * @param fileName
     */
    private boolean checkFileType(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return false;
        }
        String suffix = fileName.substring(fileName.lastIndexOf('.'));
        return Arrays.asList(FILE_SUFFIX_SUPPORT).contains(suffix.toLowerCase(Locale.ROOT));
    }

}