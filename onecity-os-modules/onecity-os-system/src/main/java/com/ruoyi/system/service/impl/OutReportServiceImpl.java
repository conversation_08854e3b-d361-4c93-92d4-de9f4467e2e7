package com.ruoyi.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.DateUtils;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.system.api.domain.SysUser;
import com.onecity.os.system.api.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.domain.OutReport;
import com.ruoyi.system.domain.OutReportApproval;
import com.ruoyi.system.domain.OutReportHistory;
import com.ruoyi.system.domain.dto.RemindInfoDto;
import com.ruoyi.system.domain.vo.OutReportApprovalParam;
import com.ruoyi.system.feign.MessageFeignService;
import com.ruoyi.system.mapper.OutReportApprovalMapper;
import com.ruoyi.system.mapper.OutReportHistoryMapper;
import com.ruoyi.system.mapper.OutReportMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.OutReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @Author: zack
 * @Date: 2024/4/18 16:38
 */
@Slf4j
@Service("outReportService")
public class OutReportServiceImpl implements OutReportService {

    @Value("${app.baseMessageUrl}")
    private String baseMessageUrl;
    @Value("${app.outReportMessageUrl}")
    private String outReportMessageUrl;
    @Resource
    private MessageFeignService messageFeignService;

    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private OutReportMapper outReportMapper;
    @Resource
    OutReportApprovalMapper outReportApprovalMapper;
    @Resource
    OutReportHistoryMapper outReportHistoryMapper;
    @Resource
    private ISysUserService userService;



    @Override
    public List<OutReport> reportList(Integer todoType, String applyType, String keyWork) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<OutReport> outReports;
        if (1 == todoType) {
            // 已办查询
            outReports = outReportMapper
                    .queryOutReportDoneByPram(todoType, applyType, keyWork, String.valueOf(loginUser.getSysUser().getUserId()));
        } else {
            // 未办查询
            outReports = outReportMapper
                    .queryOutReportUnDoneByPram(todoType, applyType, keyWork, String.valueOf(loginUser.getSysUser().getUserId()));
        }
        return outReports;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BaseResult applyReport(OutReport outReport) {
        Date now = DateUtils.getNowDate();
        //校验开始结束时间--开始时间不得早于现在时间，结束时间不得早于开始时间
        Date nowDate = DateUtils.dateTime("yyyy-MM-dd",DateUtils.getDate());
        if(outReport.getStartTime().before(nowDate) || outReport.getEndTime().before(outReport.getStartTime())){
            return BaseResult.fail("开始时间结束时间校验失败！请重新申请！");
        }
        //查询申请人所在部门外出审核人角色人员
        List<String> ids = sysUserMapper.getOutReportRoleUserId(outReport.getApplyUserId());
        if(null == ids || ids.isEmpty()){
            return BaseResult.fail("未找到本部门外出审核人，请联系管理员配置！");
        }
        outReport.setCreateTime(now);
        //新增主体表
        outReportMapper.insertOutReport(outReport);
        //新增审批表
        List<OutReportApproval> outReportApprovalList = new ArrayList<>();
        //第一批审批人
        for(String userId : ids){
            OutReportApproval outReportApproval = new OutReportApproval();
            outReportApproval.setReportId(outReport.getId());
            outReportApproval.setApprovalUserId(Long.valueOf(userId));
            outReportApproval.setApprovalStep(1);
            outReportApproval.setIsApproval(1);
            outReportApproval.setCreator(SecurityUtils.getUsername());
            outReportApproval.setCreateTime(now);
            outReportApprovalList.add(outReportApproval);
        }
        //第二批审核人
        OutReportApproval outReportApprovalTwo = new OutReportApproval();
        outReportApprovalTwo.setReportId(outReport.getId());
        outReportApprovalTwo.setApprovalUserId(outReport.getApprovalUserId());
        outReportApprovalTwo.setApprovalStep(2);
        outReportApprovalTwo.setIsApproval(0);
        outReportApprovalTwo.setCreator(SecurityUtils.getUsername());
        outReportApprovalTwo.setCreateTime(now);
        outReportApprovalList.add(outReportApprovalTwo);
        //批量插入
        outReportApprovalMapper.insertOutReportApproval(outReportApprovalList);
        //新增操作记录表
        OutReportHistory outReportHistory = new OutReportHistory();
        outReportHistory.setReportId(outReport.getId());
        outReportHistory.setOperatorId(outReport.getApplyUserId());
        outReportHistory.setOperatorStep(0);
        outReportHistory.setCreateTime(now);
        outReportHistoryMapper.insertOutReportHistory(outReportHistory);
        //给第一批审批人发送App消息
        StringBuilder content = new StringBuilder();
//        content.append("【外出报备】").append(SecurityUtils.getLoginUser().getSysUser().getNickName()).append("用户于")
//                .append(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm",now)).append("发起外出报备申请，请及时办理。");
        content.append(SecurityUtils.getLoginUser().getSysUser().getNickName()).append("发起了外出报备申请，请及时处理。");
        this.addMsg(SecurityUtils.getUsername(),"外出报备 报备待审核",content,ids,"2",outReport.getId(), 2);
        return BaseResult.ok("申请成功");
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BaseResult approvalApply(OutReportApprovalParam outReportApprovalParam) {
        //查询报备主体
        OutReport outReport = outReportMapper.selectOutReportById(outReportApprovalParam.getReportId());
        //校验报备主体的状态
        if(5 == outReport.getStatus()){
            return BaseResult.fail("消息已撤回！");
        }
        if(1 == outReportApprovalParam.getApprovalStatus() && StringUtils.isEmpty(outReportApprovalParam.getApprovalContent())){
            //审批拒绝
            return BaseResult.fail("审核失败！拒绝原因不能为空！");
        }
        Date now = DateUtils.getNowDate();
        //查询当前审批所在的审批步骤
        int approvalStep = outReportApprovalMapper.getApprovalStepByParam(outReportApprovalParam.getReportId(),outReportApprovalParam.getApprovalUserId());
        if(0 == approvalStep){
            //未找到当前审批人
            return BaseResult.fail("审核失败！审核人没有审批权限！");
        }

        messageFeignService.deleteMessageByServiceAndUserId(String.valueOf(outReport.getId()), "outReport", String.valueOf(SecurityUtils.getLoginUser().getUserid()), SecurityUtils.getLoginUser().getUsername());
        //操作步骤实体
        OutReportHistory outReportHistory = new OutReportHistory();
        outReportHistory.setReportId(outReportApprovalParam.getReportId());
        outReportHistory.setOperatorId(outReportApprovalParam.getApprovalUserId());
        outReportHistory.setCreateTime(now);
        outReportHistory.setApprovalContent(outReportApprovalParam.getApprovalContent());
        outReportHistory.setOperatorStatus(outReportApprovalParam.getApprovalStatus());
        outReportHistory.setOperatorStep(approvalStep);
        //新增操作记录表
        outReportHistoryMapper.insertOutReportHistory(outReportHistory);
        //本次审核是拒绝--流程结束
        if(1 == outReportApprovalParam.getApprovalStatus()){
            //更改报备主体状态
            outReportMapper.updateStatusByIdAndStatus(outReportApprovalParam.getReportId(),3);
            //修改该次步骤的审核状态
            outReportApprovalMapper.updateStatusByIdAndStep(outReportApprovalParam.getReportId(),approvalStep,2);
            //发送消息--给发起人发送消息
            List<String> ids = new ArrayList<>();
            ids.add(outReport.getApplyUserId()+"");
            StringBuilder content = new StringBuilder();
//            content.append("【外出报备】您于").append(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm",outReport.getCreateTime())).append("发起的外出报备申请已被拒绝，拒绝原因为：")
//                    .append(outReportApprovalParam.getApprovalContent()).append("，请及时办理。");
            content.append("您发起的报备申请被拒绝，原因“").append(outReportApprovalParam.getApprovalContent()).append("”。");
            this.addMsg(SecurityUtils.getUsername(),"外出报备 报备已拒绝",content,ids,"1",outReport.getId(), 2);
        }else {
            //本次审核是通过
            //修改该次步骤的审核状态
            outReportApprovalMapper.updateStatusByIdAndStep(outReportApprovalParam.getReportId(),approvalStep,1);
            if(1 == approvalStep){//初审通过
                List<OutReportApproval> outReportApprovalList = outReportApprovalMapper.queryByReportId(outReportApprovalParam.getReportId());
                for (OutReportApproval outReportApproval : outReportApprovalList) {
                    if (outReportApproval.getApprovalStep() == 1) {
                        messageFeignService.deleteMessageByServiceAndUserId(String.valueOf(outReport.getId()), "outReport", String.valueOf(outReportApproval.getApprovalUserId()), SecurityUtils.getLoginUser().getUsername());
                    }
                }
                //更改报备主体状态
                outReportMapper.updateStatusByIdAndStatus(outReportApprovalParam.getReportId(),2);
                //更改复审状态为可以审批状态
                outReportApprovalMapper.updateIsApprovalById(outReportApprovalParam.getReportId());
                //发送消息--给主持人发送消息
                List<String> ids = new ArrayList<>();
                ids.add(outReport.getApprovalUserId()+"");
                StringBuilder content = new StringBuilder();
//                content.append("【外出报备】").append(outReport.getApplyUserName()).append("用户于")
//                        .append(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm",outReport.getCreateTime())).append("发起外出报备申请，请及时办理。");
                content.append(outReport.getApplyUserName()).append("发起了外出报备申请，请及时处理。");
                this.addMsg(SecurityUtils.getUsername(),"外出报备 报备待审核",content,ids,"2",outReport.getId(), 2);
            }else if(2 == approvalStep){//复审通过
                outReportMapper.updateStatusByIdAndStatus(outReportApprovalParam.getReportId(),4);
                //发送消息--给发起人发送消息
                List<String> ids = new ArrayList<>();
                ids.add(outReport.getApplyUserId()+"");
                StringBuilder content = new StringBuilder();
//                content.append("【外出报备】您于").append(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm",outReport.getCreateTime())).append("发起的外出报备申请已通过，请查阅。");
                content.append("您发起的报备申请已通过，请及时关注。");
                this.addMsg(SecurityUtils.getUsername(),"外出报备 报备已通过",content,ids,"1",outReport.getId(), 1);
            }
        }
        return BaseResult.ok("审核成功");
    }

    private void addMsg(String creatorName, String title, StringBuilder content, List<String> userIds, String appMsgType,Long reportId, Integer applyType) {
        RemindInfoDto remind = new RemindInfoDto();
        remind.setRemindTitle(title);
        remind.setIsRead((byte) 0);
        remind.setRemindedType((byte) 2);
        remind.setIsDelete((byte) 0);
        remind.setCreater(creatorName);
        remind.setCreateTime(DateUtils.dateTimeIntact());
        remind.setRemindContent(content.toString());
        remind.setLevel("2");
        remind.setPcUserId(null);
        StringBuilder appUserIds = new StringBuilder();
        for (String userId : userIds) {
            appUserIds.append(userId).append(",");
        }
        //去掉最后一个,
        String usrIds = appUserIds.substring(0, appUserIds.length() - 1);
        remind.setAppUserId(usrIds);
        remind.setServiceId(reportId+"");
        remind.setServiceType("outReport");
        remind.setSourceType("APP");
        String msgUrl = "wcbb";
        if ("2".equals(appMsgType)) {
            msgUrl = outReportMessageUrl;
        }
        remind.setMessageUrl(baseMessageUrl + msgUrl);
        remind.setAppMsgType(appMsgType);
        log.info("发送app消息ids:----"+ JSONObject.toJSONString(remind.getAppUserId()));
        BaseResult result = messageFeignService.addMsg(Collections.singletonList(remind));
        log.info("发送app消息result:----"+result.getCode());
        log.info("发送app消息result:----"+result.getMsg());
    }

    @Override
    public BaseResult<OutReport> details(Integer reportId) {
        // 查询主表
        OutReport outReport = outReportMapper.queryById(reportId);
        if (null == outReport) {
            return BaseResult.fail("申请不存在，请刷新查看");
        }
        // 用户id
        Long userId = SecurityUtils.getLoginUser().getSysUser().getUserId();
        // 可操作标识 默认false 用来验证当前登录用户是否可以操作
        boolean canOperateFlag = false;
        //获取历史操作记录
        List<OutReportHistory> histories = outReportHistoryMapper.selectByoutReportId(reportId);
        for (OutReportHistory history : histories) {
            //获取操作人用户信息
            SysUser operator = userService.selectUserById(history.getOperatorId());
            if (null != operator) {
                history.setOperatorName(operator.getNickName());
                // 可操作标识 为true 跳过，否则判断当前用户 是否可以操作过 包括 发起人 和两个审批人 若是置为true。
                if (!canOperateFlag) {
                    canOperateFlag = (null != userId) && (operator.getUserId().compareTo(userId) == 0);
                }
            }
        }
        // 查询待审核表
        List<OutReportApproval> outReportApprovals = outReportApprovalMapper.queryByReportId(Long.valueOf(reportId));
        // 根据外出审核状态，确定 遍历待审批人 待审批人亦可操作
        for (OutReportApproval outReportApproval : outReportApprovals) {
            if (outReportApproval.getApprovalUserId().equals(userId) && outReportApproval.getApprovalStep() == 1) {
                outReport.setApprovalPeopleFlag("1");
            }
            if (outReportApproval.getApprovalUserId().equals(userId) && outReportApproval.getApprovalStep() == 2) {
                outReport.setLeaderFlag("1");
            }
        }
        // 历史表操作人与当前登录人 非同一人，即为待审核人或者非法用户场景
        if (!canOperateFlag) {
            for (OutReportApproval outReportApproval : outReportApprovals) {
                if (null != outReportApproval && !canOperateFlag) {
                    // 可操作标识 为true 跳过，否则判断当前用户 是否可以操作过 包括 发起人 和两个审批人 若是置为true。
                    canOperateFlag = outReportApproval.getIsApproval() == 1 && (outReportApproval.getApprovalUserId().compareTo(userId) == 0);
                }
            }
        }
        // 可操作校验，若不可操作 报无权限错误
        if (!canOperateFlag) {
            return BaseResult.fail("您无权限操作");
        }
        // 插入操作记录
        outReport.setHistoryVo(histories);
        //获取申请人用户信息
        SysUser applyUser = userService.selectUserById(outReport.getApplyUserId());
        if (null != applyUser) {
            outReport.setApplyUserName(applyUser.getNickName());
            outReport.setApplyPhone(applyUser.getPhonenumber());
            outReport.setApplyDuties(applyUser.getDuties());
            outReport.setDeptName(applyUser.getDept().getDeptName());
        }
        //获取领导人用户信息
        SysUser approvalUser = userService.selectUserById(outReport.getApprovalUserId());
        if (null != approvalUser) {
            outReport.setApprovalUserName(approvalUser.getNickName());
            outReport.setApprovalPhone(approvalUser.getPhonenumber());
            outReport.setApprovalDuties(approvalUser.getDuties());
        }
        return BaseResult.ok(outReport);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BaseResult revoke(Long reportId) {
        Long userId = SecurityUtils.getLoginUser().getSysUser().getUserId();
        //查询报备主体
        OutReport outReport = outReportMapper.selectOutReportById(reportId);
        // 外出申请记录不存在
        if (null == outReport) {
            return BaseResult.fail("申请记录不存在！");
        }
        // 非本人操作
        if (outReport.getApplyUserId().compareTo(userId) != 0) {
            return BaseResult.fail("撤回失败！非本人操作！");
        }
        //校验报备主体的状态
        if (1 != outReport.getStatus()) {
            return BaseResult.fail("撤回失败！该申请已被审批！");
        }
        //更改报备主体状态 和删除状态
        outReportMapper.revokeById(reportId);
        //撤回消息
        log.info("撤回app消息id:----"+reportId);
        BaseResult result = messageFeignService.deleteMessageByService(reportId+"","outReport");
        log.info("撤回app消息result:----"+result.getMsg());
        return BaseResult.ok("撤回成功");
    }
}
