package com.ruoyi.system.service.impl;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

import com.ruoyi.system.domain.SysLog;
import com.ruoyi.system.domain.SysOperLogSelect;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import com.onecity.os.system.api.domain.SysOperLog;
import com.ruoyi.system.mapper.SysOperLogMapper;
import com.ruoyi.system.service.ISysOperLogService;

import javax.annotation.Resource;

/**
 * 操作日志 服务层处理
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class SysOperLogServiceImpl implements ISysOperLogService
{
    @Resource
    private SysOperLogMapper operLogMapper;

    /**
     * 新增操作日志
     * 
     * @param operLog 操作日志对象
     * @return 结果
     */
    @Override
    public int insertOperlog(SysOperLog operLog)
    {
        return operLogMapper.insertOperlog(operLog);
    }

    /**
     * 查询系统操作日志集合
     * 
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    @Override
    public List<SysOperLog> selectOperLogList(SysOperLog operLog)
    {
        return operLogMapper.selectOperLogList(operLog);
    }

    /**
     * 批量删除系统操作日志
     * 
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    @Override
    public int deleteOperLogByIds(Long[] operIds)
    {
        return operLogMapper.deleteOperLogByIds(operIds);
    }

    /**
     * 查询操作日志详细
     * 
     * @param operId 操作ID
     * @return 操作日志对象
     */
    @Override
    public SysOperLog selectOperLogById(Long operId)
    {
        return operLogMapper.selectOperLogById(operId);
    }

    /**
     * 清空操作日志
     */
    @Override
    public void cleanOperLog()
    {
        operLogMapper.cleanOperLog();
    }

    /**
     * pc端登录日志查询适配jeecg
     * @param paramsMap
     * @return
     */
    @Override
    public List<SysLog> selectOperLogListJeecg(Map<String,String[]> paramsMap){
        SysOperLogSelect sysOperLogSelect = new SysOperLogSelect();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if(ObjectUtils.isNotEmpty(paramsMap.get("ip"))){
            sysOperLogSelect.setOperIp(paramsMap.get("ip")[0]);
        }
        if(ObjectUtils.isNotEmpty(paramsMap.get("keyWord"))){
            sysOperLogSelect.setTitle(paramsMap.get("keyWord")[0]);
        }
        try {

            if(ObjectUtils.isNotEmpty(paramsMap.get("createTime_begin"))){
                sysOperLogSelect.setOperTimeBegin(simpleDateFormat.parse(paramsMap.get("createTime_begin")[0]));
            }
            if(ObjectUtils.isNotEmpty(paramsMap.get("createTime_end"))){
                sysOperLogSelect.setOperTimeEnd(simpleDateFormat.parse(paramsMap.get("createTime_end")[0]));
            }

        }catch (Exception e){
            log.error("查询日期转换错误："+e.getMessage());
        }
        if(ObjectUtils.isNotEmpty(paramsMap.get("username"))){
            sysOperLogSelect.setOperName(paramsMap.get("username")[0]);
        }
        if(ObjectUtils.isNotEmpty(paramsMap.get("operateType"))){
            sysOperLogSelect.setBusinessType(Integer.valueOf(paramsMap.get("operateType")[0]));
        }
        return operLogMapper.selectOperLogListJeecg(sysOperLogSelect);
    }
}
