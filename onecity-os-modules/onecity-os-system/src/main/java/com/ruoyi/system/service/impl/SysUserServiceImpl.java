package com.ruoyi.system.service.impl;

import com.onecity.os.common.core.constant.UserConstants;
import com.onecity.os.common.core.exception.ServiceException;
import com.onecity.os.common.core.utils.RegexUtils;
import com.onecity.os.common.core.utils.SpringUtils;
import com.onecity.os.common.core.utils.StringUtils;
import com.onecity.os.system.api.domain.SysDept;
import com.onecity.os.system.api.domain.SysRole;
import com.onecity.os.system.api.domain.SysUser;
import com.ruoyi.common.datascope.annotation.DataScope;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.domain.SysUserPost;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.domain.vo.BatchParams;
import com.ruoyi.system.domain.vo.CheckDeptUserVo;
import com.ruoyi.system.domain.vo.StatisticsDataVo;
import com.ruoyi.system.domain.vo.SysUserParam;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.IUserPasswordHistoryService;
import com.ruoyi.system.utils.RsaUtils;
import io.lettuce.core.RedisException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * 用户 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl implements ISysUserService
{
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysPostMapper postMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysUserPostMapper userPostMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private RedisService redisService;

    @Resource
    private SysLogininforMapper sysLogininforMapper;

    @Autowired
    private IUserPasswordHistoryService userPasswordHistoryService;

    /**
     * 用户手机号 redis key
     */
    public static final String USER_PHONE = "system_user_phone_map";

    /**
     * 根据条件分页查询用户列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUser> selectUserList(SysUserParam user)
    {
        List<SysUser> result = userMapper.selectUserList(user);
        //查询角色
        for(SysUser sysUser : result){
            List<Long> roles = roleMapper.selectRoleListByUserId(sysUser.getUserId());
            sysUser.setRoleIds(roles.toArray(new Long[roles.size()]));
            //角色名
            String roleNames = roleMapper.selectRoleNamesByUserId(sysUser.getUserId());
            sysUser.setRoleNames(roleNames);
            try {
                String phone = RsaUtils.decryptByPrivateKey(RsaUtils.pri, sysUser.getPhonenumber());
                sysUser.setPhonenumber(phone);
            }catch (Exception e) {
                continue;
            }
        }
        return result;
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectAllocatedList(SysUser user)
    {
        List<SysUser> result = userMapper.selectAllocatedList(user);
        //手机号解密
        for(SysUser sysUser : result){
            try {
                String phone = RsaUtils.decryptByPrivateKey(RsaUtils.pri, sysUser.getPhonenumber());
                sysUser.setPhonenumber(phone);
            }catch (Exception e) {
                log.error("手机号解密失败："+e.getMessage());
                continue;
            }
        }
        return result;
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUnallocatedList(SysUser user)
    {
        return userMapper.selectUnallocatedList(user);
    }

    /**
     * 通过用户名查询用户
     * 
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName)
    {
        SysUser user = userMapper.selectUserByUserName(userName);
        if(ObjectUtils.isEmpty(user)){
            return user;
        }
        List<Long> roles = roleMapper.selectRoleListByUserId(user.getUserId());
        user.setRoleIds(roles.toArray(new Long[roles.size()]));
        return user;
    }

    @Override
    public SysUser selectUserByMobile(String mobile) {

        return userMapper.selectUserByMobile(mobile);
    }

    /**
     * 通过用户ID查询用户
     * 
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId)
    {
        SysUser sysUser = userMapper.selectUserById(userId);
        try {
            String phone = RsaUtils.decryptByPrivateKey(RsaUtils.pri, sysUser.getPhonenumber());
            sysUser.setPhonenumber(phone);
        }catch (Exception e) {
            log.error(e.getMessage());
        }
        List<Long> roles = roleMapper.selectRoleListByUserId(userId);
        sysUser.setRoleIds(roles.toArray(new Long[roles.size()]));
        return sysUser;
    }

    /**
     * 查询用户所属角色组
     * 
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName)
    {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        StringBuffer idsStr = new StringBuffer();
        for (SysRole role : list)
        {
            idsStr.append(role.getRoleName()).append(",");
        }
        if (StringUtils.isNotEmpty(idsStr.toString()))
        {
            return idsStr.substring(0, idsStr.length() - 1);
        }
        return idsStr.toString();
    }

    /**
     * 查询用户所属岗位组
     * 
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName)
    {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        StringBuffer idsStr = new StringBuffer();
        for (SysPost post : list)
        {
            idsStr.append(post.getPostName()).append(",");
        }
        if (StringUtils.isNotEmpty(idsStr.toString()))
        {
            return idsStr.substring(0, idsStr.length() - 1);
        }
        return idsStr.toString();
    }

    /**
     * 校验用户名称是否唯一
     * 
     * @param userName 用户名称
     * @return 结果
     */
    @Override
    public String checkUserNameUnique(String userName)
    {
        int count = userMapper.checkUserNameUnique(userName);
        if (count > 0)
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户手机号是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkPhoneUnique(SysUser user) {
        if (StringUtils.isBlank(user.getPhonenumber())) {
            return UserConstants.UNIQUE;
        }
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        // 从缓存中获取手机号 map 并比较是否存在
        HashMap<String, Long> phoneMap = getAllUserPhoneList();
        if (phoneMap.containsKey(user.getPhonenumber()) && phoneMap.get(user.getPhonenumber()).longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 检验身份证是否唯一
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkIDCodeUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkIDCodeUnique(user.getIDCode());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     * 
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user)
    {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin())
        {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     * 
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId)
    {
        if (!SysUser.isAdmin(SecurityUtils.getUserId()))
        {
            SysUserParam user = new SysUserParam();
            user.setUserId(userId);
            List<SysUser> users = SpringUtils.getAopProxy(this).selectUserList(user);
            if (StringUtils.isEmpty(users))
            {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertUser(SysUser user) {
        // 新增用户信息
        int rows = userMapper.insertUser(user);
        // 新增密码记录
        String password = user.getPassword();
        if (StringUtils.isNotEmpty(password)) {
            userPasswordHistoryService.insertSelective(user.getUserId(), password);
        }
        if (StringUtils.isNotEmpty(user.getPhonenumber())) {
            // 异步刷新手机号缓存
            CompletableFuture.runAsync(this::refreshUserPhoneNumberListCache);
        }
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        return rows;
    }

    /**
     * 注册用户信息
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user) {
        // 新增密码记录
        String password = user.getPassword();
        if (StringUtils.isNotEmpty(password)) {
            userPasswordHistoryService.insertSelective(user.getUserId(), password);
        }
        int row = userMapper.insertUser(user);
        if (StringUtils.isNotEmpty(user.getPhonenumber())) {
            // 异步刷新手机号缓存
            CompletableFuture.runAsync(this::refreshUserPhoneNumberListCache);
        }
        return row > 0;
    }

    /**
     * 修改保存用户信息
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateUser(SysUser user) {
        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPostByUserId(userId);
        // 新增用户与岗位管理
        insertUserPost(user);
        // 新增用户密码记录
        String password = user.getPassword();
        if (StringUtils.isNotEmpty(password)) {
            userPasswordHistoryService.insertSelective(user.getUserId(), password);
        }
        int row = userMapper.updateUser(user);
        if (StringUtils.isNotEmpty(user.getPhonenumber())) {
            // 异步刷新手机号缓存
            CompletableFuture.runAsync(this::refreshUserPhoneNumberListCache);
        }
        return row;
    }

    /**
     * 用户授权角色
     * 
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional
    public void insertUserAuth(Long userId, Long[] roleIds)
    {
        userRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户状态
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        int row = userMapper.updateUser(user);
        // 调用方无修改密码，不做处理 若修改密码 需调用修改密码历史表
        if (StringUtils.isNotEmpty(user.getPhonenumber())) {
            // 异步刷新手机号缓存
            CompletableFuture.runAsync(this::refreshUserPhoneNumberListCache);
        }
        return row;
    }

    /**
     * 修改用户基本信息
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user) {
        int row = userMapper.updateUser(user);
        // 调用方无修改密码，不做处理 若修改密码 需调用修改密码历史表
        if (StringUtils.isNotEmpty(user.getPhonenumber())) {
            // 异步刷新手机号缓存
            CompletableFuture.runAsync(this::refreshUserPhoneNumberListCache);
        }
        return row;
    }

    /**
     * 修改用户头像
     * 
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar)
    {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     * 
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {
        // 更新密码历史记录
        userPasswordHistoryService.insertSelective(user.getUserId(), user.getPassword());
        return userMapper.updateUser(user);
    }

    /**
     * 重置用户密码
     * 
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(long userId, String userName, String password) {
        // 更新密码历史记录
        userPasswordHistoryService.insertSelective(userId, password);
        return userMapper.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     * 
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user)
    {
        Long[] roles = user.getRoleIds();
        if (StringUtils.isNotNull(roles))
        {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (Long roleId : roles)
            {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(user.getUserId());
                ur.setRoleId(roleId);
                list.add(ur);
            }
            if (list.size() > 0)
            {
                userRoleMapper.batchUserRole(list);
            }
        }
    }

    /**
     * 新增用户岗位信息
     * 
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user)
    {
        Long[] posts = user.getPostIds();
        if (StringUtils.isNotNull(posts))
        {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<SysUserPost>();
            for (Long postId : posts)
            {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            if (list.size() > 0)
            {
                userPostMapper.batchUserPost(list);
            }
        }
    }

    /**
     * 新增用户角色信息
     * 
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds)
    {
        if (StringUtils.isNotNull(roleIds))
        {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (Long roleId : roleIds)
            {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            if (list.size() > 0)
            {
                userRoleMapper.batchUserRole(list);
            }
        }
    }

    /**
     * 通过用户ID删除用户
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserById(Long userId)
    {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        // 删除用户与密码历史记录
        userPasswordHistoryService.deleteByUserId(userId);
        int row = userMapper.deleteUserById(userId);
        // 异步刷新手机号缓存
        CompletableFuture.runAsync(() -> {
            try {
                refreshUserPhoneNumberListCache();
            } catch (Exception e) {
                // 处理异步任务中的异常
                log.error("通过 id 删除用户deleteUserById:刷新用户手机号-userid 缓存异常 " + e);
            }
        });
        return row;
    }

    /**
     * 批量删除用户信息
     * 
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new SysUser(userId));
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        // 删除用户与密码历史记录
        userPasswordHistoryService.deleteByUserIds(Arrays.asList(userIds));
        int row = userMapper.deleteUserByIds(userIds);
        // 异步刷新手机号缓存
        // 异步刷新手机号缓存
        CompletableFuture.runAsync(() -> {
            try {
                refreshUserPhoneNumberListCache();
            } catch (Exception e) {
                // 处理异步任务中的异常
                log.error("批量删除用户deleteUserByIds:刷新用户手机号-userid 缓存异常 " + e);
            }
        });
        return row;
    }

    @Override
    public int changeStatusBatch(BatchParams param) {
        for (Long userId : param.getIds())
        {
            checkUserAllowed(new SysUser(userId));
        }
        return userMapper.changeStatusBatch(param);
    }

    /**
     * 导入用户数据
     * 
     * @param userList 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName)
    {
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        if (CollectionUtils.isEmpty(userList))
        {
            failureMsg.insert(0, "很抱歉，导入失败！导入数据不能为空 " );
            return failureMsg.toString();
        }
        String password = configService.selectConfigByKey("sys.user.initPassword");
        for (SysUser user : userList)
        {
            try
            {
                //校验
                Pattern regexpUserName = Pattern.compile("^[a-zA-Z][A-Za-z0-9!@#$%^&*_-]*$");
                if (StringUtils.isBlank(user.getUserName()) || !regexpUserName.matcher(user.getUserName()).matches()) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + "用户账号只能以字母，或者字母加特殊字符形式组成，必须以字母开头!";
                    failureMsg.append(msg);
                }
                if(user.getSex()==null){
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + "性别为空！";
                    failureMsg.append(msg);
                }
                if (StringUtils.isBlank(user.getPhonenumber()) || !RegexUtils.isPhone(user.getPhonenumber())) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + "手机号格式不正确！";
                    failureMsg.append(msg);
                }
                Pattern regexpName = Pattern.compile("[\\u4e00-\\u9fa5]{2,12}");
                if (org.apache.commons.lang3.StringUtils.isBlank(user.getNickName()) || !regexpName.matcher(user.getNickName()).matches()) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + "真实姓名长度不正确！";
                    failureMsg.append(msg);
                }
                //验证部门是否存在
                if(user.getDeptId() != null){
                    SysDept sysDept = sysDeptMapper.selectDeptById(user.getDeptId());
                    if(sysDept == null){
                        failureNum++;
                        String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + "输入部门不存在！";
                        failureMsg.append(msg);
                    }
                }
                try {
                    String numberRsa = RsaUtils.encryptByPublicKey(RsaUtils.pub, user.getPhonenumber());
                    user.setPhonenumber(numberRsa);
                } catch (Exception e) {
                    user.setPhonenumber(user.getPhonenumber());
                }
                // 验证是否存在这个用户
                SysUser u = userMapper.selectUserByUserName(user.getUserName());
                if (StringUtils.isNull(u)) {
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(operName);
                    this.insertUser(user);
                } else if (isUpdateSupport) {
                    user.setUpdateBy(operName);
                    user.setUserId(u.getUserId());
                    this.updateUser(user);
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            return failureMsg.toString();
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！");
        }
        return successMsg.toString();
    }

    @Override
    public SysDept getDepartByUserId(Long userId) {
        return sysDeptMapper.getDepartByUserId(userId);
    }

    @Override
    public List<CheckDeptUserVo> getAllUserList(Long deptId) {
        if(deptId != null){
            return this.getChildren(deptId);
        }else {
            return this.getChildren(0L);
        }
    }

    private List<CheckDeptUserVo> getChildren(Long deptId){
        List<CheckDeptUserVo> result = new ArrayList<>();
        //查询部门
        List<SysDept> deptChilds = sysDeptMapper.getChildrenDeptById(deptId);
        for(SysDept sysDept : deptChilds){
            CheckDeptUserVo checkDeptUserVo = new CheckDeptUserVo();
            checkDeptUserVo.setId(sysDept.getDeptId());
            checkDeptUserVo.setName(sysDept.getDeptName());
            checkDeptUserVo.setType("dept");
            List<CheckDeptUserVo> child = getChildren(sysDept.getDeptId());
            checkDeptUserVo.setChildren(child);
            result.add(checkDeptUserVo);
        }
        //查询用户
        List<SysUser> userChilds = userMapper.getUserByDepartId(Long.toString(deptId),"");
        for(SysUser user : userChilds){
            CheckDeptUserVo checkDeptUserVo = new CheckDeptUserVo();
            checkDeptUserVo.setId(user.getUserId());
            checkDeptUserVo.setName(user.getNickName());
            checkDeptUserVo.setType("user");
            result.add(checkDeptUserVo);
        }
        return result;
    }

    @Override
    public List<CheckDeptUserVo> getAuditUserList(Long deptId,String name,String sourceSimpleName) {
        if(deptId != null){
            return this.getChildrenAudit(deptId,name,sourceSimpleName);
        }else {
            return this.getChildrenAudit(0L,name,sourceSimpleName);
        }
    }
    private List<CheckDeptUserVo> getChildrenAudit(Long deptId,String name,String sourceSimpleName){
        List<CheckDeptUserVo> result = new ArrayList<>();
        //查询部门
        List<SysDept> deptChilds = sysDeptMapper.getChildrenDeptById(deptId);
        for(SysDept sysDept : deptChilds){
            CheckDeptUserVo checkDeptUserVo = new CheckDeptUserVo();
            checkDeptUserVo.setId(sysDept.getDeptId());
            checkDeptUserVo.setName(sysDept.getDeptName());
            checkDeptUserVo.setType("dept");
            List<CheckDeptUserVo> child = getChildrenAudit(sysDept.getDeptId(),name,sourceSimpleName);
            checkDeptUserVo.setChildren(child);
            if (child.size() > 0) {
                result.add(checkDeptUserVo);
            }
        }
        //查询用户
        List<SysUser> userChilds = userMapper.getAuditUserByDepartId(Long.toString(deptId),"",name,sourceSimpleName);
        for(SysUser user : userChilds){
            CheckDeptUserVo checkDeptUserVo = new CheckDeptUserVo();
            checkDeptUserVo.setId(user.getUserId());
            checkDeptUserVo.setName(user.getNickName());
            checkDeptUserVo.setType("user");
            result.add(checkDeptUserVo);
        }
        return result;
    }

    @Override
    public String getPcUserNamesByPcUserIds(String[] ids) {
        return userMapper.getPcUserNamesByPcUserIds(ids);
    }

    @Override
    public String getPcUserIdsByPcUserNames(String[] names) {
        return userMapper.getPcUserIdsByPcUserNames(names);
    }
    @Override
    public String getNickNameByName(String userName) {
        return userMapper.getNickNameByName(userName);
    }

    @Override
    public List<SysRole> getRoleNameByRoleIds(String[] roleIds) {
        return roleMapper.getRoleNameByRoleIds(roleIds);
    }

    @Override
    public List<SysUser> getUsersByRoleIds(String[] roleIds) {
        List<SysUser> sysUserList = roleMapper.getUsersByRoleIds(roleIds);
        //查询角色
        for(SysUser sysUser : sysUserList){
            if(ObjectUtils.isNotEmpty(sysUser.getUserId())) {
                List<Long> roles = roleMapper.selectRoleListByUserId(sysUser.getUserId());
                sysUser.setRoleIds(roles.toArray(new Long[roles.size()]));
                //角色名
                String roleNames = roleMapper.selectRoleNamesByUserId(sysUser.getUserId());
                sysUser.setRoleNames(roleNames);
                try {
                    String phone = RsaUtils.decryptByPrivateKey(RsaUtils.pri, sysUser.getPhonenumber());
                    sysUser.setPhonenumber(phone);
                } catch (Exception e) {
                    continue;
                }
            }
        }
        return sysUserList;
    }

    @Override
    public int deleteUserByUserName(String userName) {
        SysUser sysUser = userMapper.selectUserByUserName(userName);
        userRoleMapper.deleteUserRoleByUserId(sysUser.getUserId());
        int row = userMapper.deleteUserByUserName(userName);
        // 异步刷新手机号缓存
        CompletableFuture.runAsync(this::refreshUserPhoneNumberListCache);
        return row;
    }

    @Override
    public int updateUserByUserName(SysUser user) {
        int row = userMapper.updateUserByUserName(user);
        if (StringUtils.isNotEmpty(user.getPhonenumber())) {
            // 异步刷新手机号缓存
            CompletableFuture.runAsync(this::refreshUserPhoneNumberListCache);
        }
        return row;
    }

    @Override
    public List<String> getUserIdByMenu(String menu) {
        return userMapper.getUserIdByMenu(menu);
    }

    @Override
    public List<SysUser> getUserByDepartId(String departId, String userId) {
        return userMapper.getUserByDepartId(departId, userId);
    }

    @Override
    public List<SysUser> getUserByPcUserIds(String[] userIds) {
        List<SysUser> sysUserList = userMapper.getUserByPcUserIds(userIds);
        for(SysUser sysUser : sysUserList){
            try {
                String phone = RsaUtils.decryptByPrivateKey(RsaUtils.pri, sysUser.getPhonenumber());
                sysUser.setPhonenumber(phone);
            }catch (Exception e) {
            }
        }
        return sysUserList;
    }

    @Override
    public SysUser getUserByOneCodeId(String oneCodeUserId) {
        return userMapper.getUserByOneCodeId(oneCodeUserId);
    }
    @Override
    public void updateUserByOneCodeId(SysUser user) {
        userMapper.updateUserByOneCodeId(user);
    }
    @Override
    public String getUserIdsByRoles(String[] roleIds) {
        return userMapper.getUserIdsByRoles(roleIds);
    }

    @Override
    public Boolean getUserIndicatorTitlePerms(String sourceSimpleName, Long userId) {
        int count = userMapper.getUserIndicatorTitlePerms(sourceSimpleName,userId);
        return count > 0;
    }

    @Override
    public Boolean getUserArchivePerms(Long userId) {
        int count = userMapper.getUserArchivePerms(userId);
        return count > 0;
    }

    @Override
    public Boolean getInformCleanPerms(Long userId) {
        int count = userMapper.getInformCleanPerms(userId);
        return count > 0;
    }
    /**
     *查询用户统计信息
     * @return
     */
    @Override
    public StatisticsDataVo getStatisticsData() {
        Date today = getTodayMidnight();
        // 获取总用户数
        int count = Optional.ofNullable(userMapper.queryAllUserCount(today)).orElse(0L).intValue();
        // 获取系统pc总访问次数
        int allVisitPcCount = Optional.ofNullable(sysLogininforMapper.findAllVisitPcCount(today)).orElse(0L).intValue();
        // 获取访问总数APP端
        int dingAllVisitUserCount = Optional.ofNullable(sysLogininforMapper.findDingAllVisitUserCount(today)).orElse(0L).intValue();
        StatisticsDataVo result = new StatisticsDataVo();
        result.setUserCount(count);
        result.setPcLoginUserTimes(allVisitPcCount);
        result.setAppLoginUserTimes(dingAllVisitUserCount);
        return result;
    }
    /**
     * 获取当天的 00:00:00 时间
     * @return 当天的 00:00:00 时间
     */
    private static Date getTodayMidnight() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取所有用户手机号码列表
     * 该方法首先尝试从Redis缓存中获取用户手机号码列表如果缓存数据为空或出现Redis异常，
     * 则调用refreshUserPhoneNumberListCache方法刷新缓存
     *
     * @return 包含用户手机号码的HashMap，如果缓存刷新失败，则可能返回空HashMap或null
     */
    private HashMap<String, Long> getAllUserPhoneList() {
        try {
            // 尝试从Redis缓存中获取用户手机号码列表
            HashMap<String, Long> map = redisService.getCacheObject(USER_PHONE);
            if (MapUtils.isEmpty(map)) {
                // 如果缓存为空，则尝试重新加载数据
                return refreshUserPhoneNumberListCache();
            }
            return map;
        } catch (RedisException e) {
            log.error("获取用户手机号缓存列表失败", e);
            // 出现Redis异常时，尝试刷新缓存
            return refreshUserPhoneNumberListCache();
        } catch (Exception e) {
            log.error("获取用户手机号缓存列表异常", e);
            // 其他异常情况下，尝试刷新缓存
            return refreshUserPhoneNumberListCache();
        }
    }


    /**
     * 刷新用户手机号列表缓存
     *
     * 此方法旨在更新并缓存经过RSA加密的用户手机号与其对应的用户ID映射
     * 它首先从数据库中检索所有用户信息，对非空的用户手机号进行RSA加密，
     * 并将加密后的手机号作为键，用户ID作为值存储在HashMap中
     * 如果加密过程中出现异常，将直接使用未加密的手机号作为键进行存储
     * 最终，更新后的映射被存储在Redis中，以提高后续访问的效率
     *
     * @return 返回更新后的用户手机号缓存映射
     */
    private HashMap<String, Long> refreshUserPhoneNumberListCache() {
        // 初始化线程安全的HashMap用于存储手机号和用户ID
        HashMap<String, Long> map = new HashMap<>();
        // 从数据库查询所有用户列表
        List<SysUser> users = userMapper.queryAllUserList();
        // 遍历用户列表
        users.forEach(user -> {
            // 检查用户手机号是否非空
            if (StringUtils.isNotEmpty(user.getPhonenumber())) {
                try {
                    // 使用RSA公钥加密用户手机号
                    String numberRsa = RsaUtils.decryptByPrivateKey(RsaUtils.pri, user.getPhonenumber());
                    // 更新用户对象的手机号为加密后的值
                    user.setPhonenumber(numberRsa);
                    // 将加密后的手机号和用户ID加入到映射中
                    map.put(numberRsa, user.getUserId());
                } catch (Exception e) {
                    // 在加密过程中出现异常时，直接使用未加密的手机号作为键加入映射
                    map.put(user.getPhonenumber(), user.getUserId());
                }
            }
        });
        // 将映射存储在Redis中，设置过期时间为1小时
        redisService.setCacheObject(USER_PHONE, map, 60 * 60L, TimeUnit.SECONDS);
        // 返回更新后的映射
        return map;
    }

}
