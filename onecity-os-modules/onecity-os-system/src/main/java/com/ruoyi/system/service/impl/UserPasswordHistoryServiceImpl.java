package com.ruoyi.system.service.impl;

import com.onecity.os.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.domain.UserPasswordHistory;
import com.ruoyi.system.mapper.UserPasswordHistoryMapper;
import com.ruoyi.system.service.IUserPasswordHistoryService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.onecity.os.common.core.datasource.DynamicDataSourceContextHolder.log;

/**
 * 用户密码历史记录 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class UserPasswordHistoryServiceImpl implements IUserPasswordHistoryService {
    @Resource
    private UserPasswordHistoryMapper userPasswordHistoryMapper;


    /**
     * 检查新密码是否与最近几次密码相同
     *
     * @param userId 用户ID
     * @param lastPassword 最近一次的密码
     * @param password 新密码
     * @return 如果新密码与最近几次密码相同返回false，否则返回true
     */
    @Override
    public boolean checkHaveSamePassword(long userId, String lastPassword, String password) {
        // 获取特定用户的密码历史记录列表
        List<UserPasswordHistory> listByUserId = getListByUserId(userId);
        // 如果密码历史记录为空，检查新密码是否与最近一次密码相同
        if (CollectionUtils.isEmpty(listByUserId)) {
            // 如果新密码与上一次密码相同，则不允许使用
            return StringUtils.isBlank(lastPassword) || !SecurityUtils.matchesPassword(lastPassword, password);
        }

        // 遍历密码历史记录
        for (UserPasswordHistory userPasswordHistory : listByUserId) {
            // 检查新密码是否与任何一个历史密码匹配
            if (!SecurityUtils.matchesPassword(password, userPasswordHistory.getPassword())) {
                // 如果找到任何一个不匹配的历史密码，则继续检查下一个
                continue;
            }
            // 如果找到一个匹配的历史密码，则返回 false，表示不允许使用该密码
            return false;
        }
        // 如果所有历史密码都不匹配，则返回 true，表示可以使用该密码
        return true;
    }


    /**
     * 有选择性地插入用户密码记录
     * 此方法旨在处理用户密码的更新，确保密码历史记录不超过五条
     * 当记录超过五条时，最早的一条记录将被删除，其余记录的顺序将相应调整
     *
     * @param userId   用户ID
     * @param password 新密码
     * @return 影响的行数
     */
    @Override
    @Transactional
    public int insertSelective(long userId, String password) {
        try {
            // 获取特定用户的密码历史记录列表
            List<UserPasswordHistory> listByUserId = getListByUserId(userId);
            int result;
            // 如果密码历史记录少于五条，则直接添加新密码记录
            if (listByUserId.size() < 5) {
                UserPasswordHistory record = new UserPasswordHistory();
                record.setUserId(userId);
                record.setPassword(password);
                record.setCreateTime(new Date());
                record.setSort(listByUserId.size());
                result = insertSelective(record);

            } else {
                // 如果密码历史记录已达五条，删除最早的一条，并更新其余记录的顺序
                for (int i = 0; i < listByUserId.size(); i++) {
                    if (i == 0) {
                        deleteById(listByUserId.get(0).getId());
                    } else {
                        UserPasswordHistory userPasswordHistory = listByUserId.get(i);
                        userPasswordHistory.setSort(i - 1);
                        userPasswordHistory.setUpdateTime(new Date());
                        userPasswordHistory.setUpdateBy(SecurityUtils.getUsername());
                        updateBySelective(userPasswordHistory);
                    }
                }
                UserPasswordHistory record = new UserPasswordHistory();
                record.setUserId(userId);
                record.setPassword(password);
                record.setCreateTime(new Date());
                record.setSort(listByUserId.size()-1);
                result = insertSelective(record);
            }
            return result;
        } catch (Exception e) {
            // 处理异常，记录日志
            log.error("Failed to update password history 更新密码记录异常userId:{}", userId, e);
            return 0;
        }
    }

    public int insertSelective(UserPasswordHistory record) {
        return userPasswordHistoryMapper.insertSelective(record);
    }


    /**
     * 根据条件更新
     *
     * @param record
     * @return
     */
    @Override
    public int updateBySelective(UserPasswordHistory record) {
        return userPasswordHistoryMapper.updateBySelective(record);
    }

    /**
     * 根据用户id查询
     *
     * @param userId
     * @return
     */
    @Override
    public List<UserPasswordHistory> getListByUserId(long userId) {
        return userPasswordHistoryMapper.getListByUserId(userId);
    }

    /**
     * 根据用户id删除
     *
     * @param userId
     */
    @Override
    public int deleteByUserId(long userId) {
        return userPasswordHistoryMapper.deleteByUserId(userId);
    }

    /**
     * 根据用户删除
     *
     * @param id
     */
    @Override
    public int deleteById(long id) {
        return userPasswordHistoryMapper.deleteById(id);
    }
    @Override
    public int deleteByUserIds(List<Long> userIds) {
        return userPasswordHistoryMapper.batchDeleteByUserIds(userIds);
    }
}
