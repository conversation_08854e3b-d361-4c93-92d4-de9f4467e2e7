package com.ruoyi.system.utils;
import com.onecity.os.common.core.utils.DateUtils;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Date;

/**
 * RSA加密解密
 *
 * <AUTHOR>
 **/
public class RsaUtils {

    public static String pri= "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAL2KRpqMfleuyL5J2NVwFKbUV9AZf5Cxeei0VxMlNSb6qmPRdsVGlALBCKiW27xIHXbEoqjzmI7pIuEGgNkbgOz470fPlFqyDzSsXW66FqW6JxLXkzpSSUy6Z0v52Q3RLOg47JGGHkKEk7VSLKt2ZJKnnZqqzHiQEpEWD+cn8GBvAgMBAAECgYBqvq5GqesZnKEHsfVBN08aKaqO011pctpSeQY1DRZjLna5oqT+M2J2Lpqev99eqUqWseVdu1rm2VvAWXZFT10KoIYvtxM8ZLCjIbNSlbmLLK8sDnzXy+sy6JBBGQncf+d94DPV5dFfH3QXQdAgDfGaPnjBsSR3VXhnhVLKpCwGwQJBANyyAf8S83HmlnHDbTWnBOWSwu+kXiEBhnuwJ7fH0iJAlV+GmNtGlnhR0xDpAaZdEJgE7kjLWk2+OBsP4OOuPCECQQDb3GOqRIz9YsN8lJR7whyRD+f3Pc91rtnlcPkCngXXRtb0Nv5H7Tcy3I0aRIDllPAyYVuZpo8LpCAQ01C+04qPAkBKiTwvX8EkuNIavfwGYNBAkN6RfRvlXdSDtazUXwJTWyiXyKebdy2emVQFpAxQmaHfFds8bqGjHBlq2mQDwXbBAkEAre8p7b7zp1Xl/33wBgRn4x8pTUDaCmj8uvZoGPj49/lz/povCqoQ/CzdeEVvj7EHYWQCOok5K2V5dLYob/8c4wJAEwcehdVEXYmQVf+3GvQ8YtjdCu8EfcXaTY4Sb/MFwqhyaBXXXDXAPFgijnNd40Z+GWnGQDwflPdRDgDoci93sw==";
    //公钥
    public static String pub = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC9ikaajH5Xrsi+SdjVcBSm1FfQGX+QsXnotFcTJTUm+qpj0XbFRpQCwQioltu8SB12xKKo85iO6SLhBoDZG4Ds+O9Hz5Rasg80rF1uuhaluicS15M6UklMumdL+dkN0SzoOOyRhh5ChJO1UiyrdmSSp52aqsx4kBKRFg/nJ/BgbwIDAQAB";

    /**
     * 私钥解密
     *
     * @param text 待解密的文本
     * @return 解密后的文本
     */
    public String decryptByPrivateKey(String text) throws Exception
    {
        return decryptByPrivateKey(pri, text);
    }

    /**
     * 公钥解密
     *
     * @param publicKeyString 公钥
     * @param text 待解密的信息
     * @return 解密后的文本
     */
    public static String decryptByPublicKey(String publicKeyString, String text) throws Exception
    {
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(Base64.decodeBase64(text));
        return new String(result);
    }

    /**
     * 私钥加密
     *
     * @param privateKeyString 私钥
     * @param text 待加密的信息
     * @return 加密后的文本
     */
    public static String encryptByPrivateKey(String privateKeyString, String text) throws Exception
    {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }

    /**
     * 私钥解密
     *
     * @param privateKeyString 私钥
     * @param text 待解密文本
     * @return 解密后的文本
     */
    public static String decryptByPrivateKey(String privateKeyString, String text) throws Exception
    {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec5 = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec5);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] result = cipher.doFinal(Base64.decodeBase64(text));
        return new String(result);
    }

    /**
     * 公钥加密
     *
     * @param publicKeyString 公钥
     * @param text 待加密的文本
     * @return 加密后的文本
     */
    public static String encryptByPublicKey(String publicKeyString, String text) throws Exception
    {
        X509EncodedKeySpec x509EncodedKeySpec2 = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec2);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }

    /**
     * 构建RSA密钥对
     *
     * @return 生成后的公私钥信息
     */
    public static RsaKeyPair generateKeyPair() throws NoSuchAlgorithmException
    {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(2048);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();
        RSAPublicKey rsaPublicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) keyPair.getPrivate();
        String publicKeyString = Base64.encodeBase64String(rsaPublicKey.getEncoded());
        String privateKeyString = Base64.encodeBase64String(rsaPrivateKey.getEncoded());
        return new RsaKeyPair(publicKeyString, privateKeyString);
    }

    /**
     * RSA密钥对对象
     */
    public static class RsaKeyPair
    {
        private final String publicKey;
        private final String privateKey;

        public RsaKeyPair(String publicKey, String privateKey)
        {
            this.publicKey = publicKey;
            this.privateKey = privateKey;
        }

        public String getPublicKey()
        {
            return publicKey;
        }

        public String getPrivateKey()
        {
            return privateKey;
        }
    }

    public static void main(String[] args) {
//        try {
//            String txt = "18565709781";
//            System.out.println("加密前原文：" + txt);//加密后文本
//            String rsaText = RsaUtils.encryptByPublicKey(RsaUtils.publicKey, txt);//RsaUtils.publicKey 公钥加密 ！！！
//            System.out.println("密文：" + rsaText);//加密后文本
//            System.out.println("解密后原文：" + RsaUtils.decryptByPrivateKey(RsaUtils.privateKey, rsaText));//RsaUtils.privateKey 私钥解密 ！！！
//        }catch (Exception e) {
//            System.out.println(e.getStackTrace());
//            System.out.println("加解密失败");
//        }
//        String url = "/api-file/dad/dasd/ddad.png";
//        int index = url.lastIndexOf('.');
//        System.out.println(index);
//        String suffix = url.substring(index);
//        System.out.println(suffix);
        Date now = DateUtils.getNowDate();
        String s = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",now);
        System.out.println(s);
    }
}
