server:
  port: 8080
# spring配置
spring:
  main:
    allow-circular-references: true
  redis:
    host: onecity-os-redis
#    host: *************
    port: 6379
#    port: 30425
    password: DONT(qOabvXt6cR+BDQvtcBU6cbRv3KOdJFEmAPdN7Vo4b4rtkA0VqsPe5SXJrcIYMRhN4vV3+CPWcxs05elMVxPz28Akqgr408GaQRGEPXoT/2WAAz4u9ozHv+xGePp44vW7onza4utNLIzC0ymGmp2vapMuFO+6iQpX5eWm39s3TWM=)

  #kafka
  kafka:
    bootstrap-servers: **************:9092,**************:9092,**************:9092 # kafka brokers
    consumer:
      group-id: iocDevGroup       # 消息消费组
      auto-offset-reset: earliest  # 新的消费者将会从offset=0的位置从头开始消费；latest则相反
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

  datasource:
    druid:
      stat-view-servlet:
        enabled: false
        loginUsername: admin
        loginPassword: 123456
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
    dynamic:
      druid:
        initial-size: 5
        min-idle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j
      datasource:
        # 主库数据源
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ********************************************************************************************************************************************************
#          url: ******************************************************************************************************************************************************
          username: root
          password: DONT(qOabvXt6cR+BDQvtcBU6cbRv3KOdJFEmAPdN7Vo4b4rtkA0VqsPe5SXJrcIYMRhN4vV3+CPWcxs05elMVxPz28Akqgr408GaQRGEPXoT/2WAAz4u9ozHv+xGePp44vW7onza4utNLIzC0ymGmp2vapMuFO+6iQpX5eWm39s3TWM=)
          # 从库数据源
          # slave:
          # username:
          # password:
          # url:
          # driver-class-name:
      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭
# seata配置
seata:
  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启
  enabled: false
  # Seata 应用编号，默认为 ${spring.application.name}
  application-id: ${spring.application.name}
  # Seata 事务组编号，用于 TC 集群名
  tx-service-group: ${spring.application.name}-group
  # 关闭自动代理
  enable-auto-data-source-proxy: false
  # 服务配置项
  service:
    # 虚拟组和分组的映射
    vgroup-mapping:
      ruoyi-system-group: default
  config:
    type: nacos
    nacos:
      serverAddr: onecity-os-nacos:8848
#      server-addr: *************:30309
      group: SEATA_GROUP
      namespace:
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: onecity-os-nacos:8848
#      server-addr: *************:30309
      namespace:

# mybatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.system
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath:mapper/**/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 登录相关配置
onecity:
  authurl: http://192.168.106.240:6677
login:
  privateKey: MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAKunJa0Ld2ggOovlC43dbhnYsisbHTTtB9uaNXysBRYofJ1i/AKZffhXfaCzXDW1JXm3OfgccqAUNAskaWVQrG3P/CGgGO+2a5bPjvosw5tJrMNrXvWXejWCSI5fbgZM4GBRMHygGzVrfeRwIE5XEUX72kfDcDynr1jN1S08icOlAgMBAAECgYAAmXrA6HkLZMI5MKmCKz0e6DCMt8sGnzKEVCN0r7fsbf0r1Y34xycQyElyV4rUzmQQat3pqm74dSmbXyfirs3zXZKhXg0Wd+9dF3RchwGPtbJm1tLW0uDtsrlaH0yKlsONyGi/WrtxJJ320k4y4rw797TAsIKoZAbKL/MNFhFMXwJBANIrosWGnx1/+plfKYDSS8ZkEcNEbjpAW275SwxGuKOfS0lcHmVwfxMFFNggVt54PeFaFM1RKI6i9wCh1S/8IjcCQQDRFVf0WD+SgnOX/JztKFFZVNgZ3k5rCR2t7VoVPDsXCFPQUsmBdxH83hgZfK1lOjBSD+rdzKbZNb9BW8o3SwsDAkBmSyrMbTugxhuY7sJqmvCavTYQDox/+bcHCntGnF1d9jhUmmuI//o5ra15jDP70BZ+SrP5Qtk0o4kJW7tBwbx9AkA7R3bhcyfLDSti2x9O6hBuaGzAoNPQ6wu8JUBWJvaF6DmDJgW1rCGwdPOWPlAXFDFhAtxPOFfR27g2+qRRmvgrAkBk3yb6sBlrPHTdGFiDWG4WqkmfboajkBRuPBjnIvZPRWiMhMV9SKIVTf/7rPDEdgb3mLRsDw7Oir6F3MwnysFP
app:
  #发送APP通知消息，messageUrl
  baseMessageUrl: http://111.63.48.25:32753/app-portal/app/#/
  outReportMessageUrl: wcbb?tab=1
  keyWorkMessageUrl: zdxm
# swagger配置
other:
  privateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAL2KRpqMfleuyL5J2NVwFKbUV9AZf5Cxeei0VxMlNSb6qmPRdsVGlALBCKiW27xIHXbEoqjzmI7pIuEGgNkbgOz470fPlFqyDzSsXW66FqW6JxLXkzpSSUy6Z0v52Q3RLOg47JGGHkKEk7VSLKt2ZJKnnZqqzHiQEpEWD+cn8GBvAgMBAAECgYBqvq5GqesZnKEHsfVBN08aKaqO011pctpSeQY1DRZjLna5oqT+M2J2Lpqev99eqUqWseVdu1rm2VvAWXZFT10KoIYvtxM8ZLCjIbNSlbmLLK8sDnzXy+sy6JBBGQncf+d94DPV5dFfH3QXQdAgDfGaPnjBsSR3VXhnhVLKpCwGwQJBANyyAf8S83HmlnHDbTWnBOWSwu+kXiEBhnuwJ7fH0iJAlV+GmNtGlnhR0xDpAaZdEJgE7kjLWk2+OBsP4OOuPCECQQDb3GOqRIz9YsN8lJR7whyRD+f3Pc91rtnlcPkCngXXRtb0Nv5H7Tcy3I0aRIDllPAyYVuZpo8LpCAQ01C+04qPAkBKiTwvX8EkuNIavfwGYNBAkN6RfRvlXdSDtazUXwJTWyiXyKebdy2emVQFpAxQmaHfFds8bqGjHBlq2mQDwXbBAkEAre8p7b7zp1Xl/33wBgRn4x8pTUDaCmj8uvZoGPj49/lz/povCqoQ/CzdeEVvj7EHYWQCOok5K2V5dLYob/8c4wJAEwcehdVEXYmQVf+3GvQ8YtjdCu8EfcXaTY4Sb/MFwqhyaBXXXDXAPFgijnNd40Z+GWnGQDwflPdRDgDoci93sw==
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC9ikaajH5Xrsi+SdjVcBSm1FfQGX+QsXnotFcTJTUm+qpj0XbFRpQCwQioltu8SB12xKKo85iO6SLhBoDZG4Ds+O9Hz5Rasg80rF1uuhaluicS15M6UklMumdL+dkN0SzoOOyRhh5ChJO1UiyrdmSSp52aqsx4kBKRFg/nJ/BgbwIDAQAB
swagger:
  title: 系统模块接口文档
  license: Powered By ruoyi
  licenseUrl: https://ruoyi.vip
user:
  default: Wzf@2022


