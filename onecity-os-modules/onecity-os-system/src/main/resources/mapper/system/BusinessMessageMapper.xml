<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.business.mapper.BusinessMapper">
    <insert id="insert" parameterType="com.ruoyi.system.business.entity.BusinessMessage">
        insert into business_message (`id`, `name`, `location`, `money`, `source_simple_name`, `label`, status,
                                      `creator`, `create_time`, `updater`, `update_time`, `content`)
        values (
                #{id},
                #{name},
                #{location},
                #{money},
                #{sourceSimpleName},
                #{label},
                #{status},
                #{creator},
                SYSDATE(),
                #{updater},
                SYSDATE(),
                #{content}
               )
    </insert>
    <update id="update" parameterType="com.ruoyi.system.business.entity.BusinessMessage">
        update business_message set
                                    name = #{name},
                                    location = #{location},
                                    money = #{money},
                                    label = #{label},
                                    status = #{status},
                                    updater = #{updater},
                                    update_time = SYSDATE(),
                                    content = #{content}
        where id = #{id} and source_simple_name = #{sourceSimpleName}
    </update>
    <delete id="deleteByIdAndSource" parameterType="java.lang.String">
        update business_message set del_flag = 1 where id = #{id} and source_simple_name = #{sourceSimpleName}
    </delete>

    <select id="selectBusinessPageList" resultType="com.ruoyi.system.business.model.dto.GetBusinessListDto">
        SELECT b.id as id,b.name as name, b.location as location, b.money as money, d.dict_label as locationLabel,
               b.source_simple_name as sourceSimpleName,b.label as label, b.status as status,
               u1.nick_name as creatorName, u2.nick_name as updaterName, b.create_time as createTime,
               b.content as content, b.update_time as updateTime, b.backup as backup
        from business_message b
        left join sys_user u1
        on b.creator = u1.user_id
        left join sys_user u2
        on b.updater = u2.user_id
        left join sys_dict_data d
        on b.location = d.dict_value
        where b.source_simple_name = #{sourceSimpleName} and b.del_flag = 0 and d.dict_type = #{dictType}
        <if test="name!=null and name!=''">
            and INSTR(b.name,#{name})
        </if>
        <if test="location!=null and location!=''">
            and b.location = #{location}
        </if>
        <if test="label!=null and label.size()!=0">
            <foreach collection="label" item="key" open="AND (" close=")" separator="and" >
                INSTR(b.label, #{key})
            </foreach>
        </if>
        <if test="moneyStart!=null">
            and b.money <![CDATA[ >= ]]> #{moneyStart}
        </if>
        <if test="moneyEnd!=null">
            and b.money <![CDATA[ <= ]]> #{moneyEnd}
        </if>
        <if test="status!=null and status>0">
            and b.status = #{status}
        </if>
        order by b.update_time desc
    </select>
    <select id="selectByIdAndSource" resultType="com.ruoyi.system.business.model.dto.GetBusinessListDto"
            parameterType="java.lang.String">
        SELECT b.id as id,b.name as name, b.location as location, b.money as money,
               b.source_simple_name as sourceSimpleName,b.label as label, b.status as status,
               u1.nick_name as creatorName, u2.nick_name as updaterName, b.create_time as createTime,
               b.content as content, b.update_time as updateTime, b.backup as backup
        from business_message b
        left join sys_user u1
        on b.creator = u1.user_id
        left join sys_user u2
        on b.updater = u2.user_id
        where b.id =#{id} and b.source_simple_name = #{sourceSimpleName} and b.del_flag = 0
    </select>


</mapper>















