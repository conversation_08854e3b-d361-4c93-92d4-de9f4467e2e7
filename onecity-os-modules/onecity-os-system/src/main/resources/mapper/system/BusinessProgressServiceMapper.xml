<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.business.mapper.BusinessProgressServiceMapper">

    <insert id="insertBusinessProgress" parameterType="com.ruoyi.system.business.entity.BusinessProgress"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO business_progress(id, business_id, business_name, progress_time, progress_content,
                                      source_simple_name, creator, create_time, updater, update_time, del_flag, backup)
        VALUES (#{progress.id}, #{progress.businessId}, #{progress.businessName}, #{progress.progressTime},
                #{progress.progressContent}, #{progress.sourceSimpleName}, #{progress.creator}, #{progress.createTime},
                #{progress.updater}, #{progress.updateTime}, #{progress.delFlag}, #{progress.backup})
    </insert>

    <update id="updateProgress" parameterType="com.ruoyi.system.business.model.vo.BusinessProgressVo">
        UPDATE business_progress
        <trim prefix="SET" suffixOverrides=",">
            <if test="progressVo.progressTime != null">
                progress_time = #{progressVo.progressTime},
            </if>
            <if test="progressVo.progressContent != null  and progressVo.progressContent != ''">
                progress_content = #{progressVo.progressContent},
            </if>
            <if test="progressVo.updateTime != null">
                update_time = #{progressVo.updateTime},
            </if>
            <if test="progressVo.updater != null  and progressVo.updater != ''">
                updater = #{progressVo.updater},
            </if>
        </trim>
        WHERE 1 = 1
        AND id = #{progressVo.id}
        AND del_flag = 0
    </update>

    <update id="updateBusinessTime">
        UPDATE business_message
        SET updater     = #{updater},
            update_time = #{updateTime}
        WHERE 1 = 1
          AND id = #{businessId}
          AND del_flag = 0
    </update>

    <select id="getProgressByBusinessId" resultType="com.ruoyi.system.business.entity.BusinessProgress">
        select b.id                 AS id,
               b.business_id        AS businessId,
               b.business_name      AS businessName,
               b.source_simple_name AS sourceSimpleName,
               b.progress_time      AS progressTime,
               b.progress_content   AS progressContent,
               u.nick_name          AS updaterName,
               b.update_time        AS updateTime
        FROM business_progress AS b
                 LEFT JOIN sys_user u ON b.updater = u.user_id
        WHERE 1 = 1
          AND b.business_id = #{businessId}
          AND b.source_simple_name = #{sourceSimpleName}
          AND b.del_flag = 0
        ORDER BY b.progress_time DESC
    </select>

    <update id="delById">
        UPDATE business_progress
        SET del_flag = 1
        WHERE 1 = 1
          AND id = #{id}
    </update>
</mapper>