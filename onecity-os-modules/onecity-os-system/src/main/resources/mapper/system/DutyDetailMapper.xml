<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DutyDetailMapper">

    <resultMap type="com.ruoyi.system.domain.DutyDetail" id="DutyDetailResult">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="duty_id" jdbcType="VARCHAR" property="dutyId" />
        <result column="depart_id" jdbcType="BIGINT" property="departId" />
        <result column="depart_name" jdbcType="VARCHAR" property="departName" />
        <result column="order_num" jdbcType="TINYINT" property="orderNum" />
        <result column="duty_leader" jdbcType="VARCHAR" property="dutyLeader" />
        <result column="duty_leader_phone" jdbcType="VARCHAR" property="dutyLeaderPhone" />
        <result column="duty_status" jdbcType="TINYINT" property="dutyStatus" />
        <result column="duty_report_name" jdbcType="VARCHAR" property="dutyReportName" />
        <result column="duty_report_address" jdbcType="VARCHAR" property="dutyReportAddress" />
        <result column="report_upload_time" jdbcType="TIMESTAMP" property="reportUploadTime" />
        <result column="have_report" jdbcType="TINYINT" property="haveReport" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    </resultMap>

    <sql id="selectDutyDetailVo">
        select id, duty_id, depart_id, depart_name, order_num, duty_leader, duty_leader_phone, duty_status,
               duty_report_name, duty_report_address, report_upload_time, have_report, create_time,
               update_time, is_delete from duty_detail
    </sql>


    <select id="selectDutyDetailList" resultMap="DutyDetailResult">
        <include refid="selectDutyDetailVo"/>
        where duty_id = #{dutyId,jdbcType=VARCHAR}
        <if test="departId != null ">
            and depart_id = #{departId,jdbcType=BIGINT}
        </if>
        <if test="departName != null and departName != ''">
            and (INSTR(depart_name,#{departName})
            or INSTR(duty_leader,#{departName}))
        </if>
        and  is_delete = 0
        order by order_num
    </select>

    <select id="selectDutyReportList" resultMap="DutyDetailResult">
        <include refid="selectDutyDetailVo"/>
        where duty_id = #{dutyId,jdbcType=VARCHAR}
        and have_report = 1
        <if test="reportName != null and reportName != ''">
            and (INSTR(depart_name,#{reportName})
            or INSTR(duty_report_name,#{reportName}))
        </if>
        and  is_delete = 0
        order by report_upload_time desc
    </select>

    <select id="selectDutyDetail"  resultMap="DutyDetailResult">
        <include refid="selectDutyDetailVo"/>
        where duty_id = #{dutyId,jdbcType=VARCHAR}
        and depart_id= #{departId,jdbcType=BIGINT}
        and is_delete = 0
    </select>

    <update id="updateReport" >
        update duty_detail set duty_report_name = #{dutyReportName},
                               duty_report_address = #{dutyReportAddress},
                               report_upload_time = sysdate(),
        WHERE duty_id = #{dutyId}
          and depart_id = #{departId}
          and is_delete = 0
    </update>

    <update id="updateDepartInfo" >
        update duty_detail set depart_name = #{departName},
                               duty_leader = #{dutyLeader},
                               duty_leader_phone = #{dutyLeaderPhone},
                               update_time = sysdate()
        WHERE duty_id = #{dutyId}
          and depart_id = #{departId}
          and is_delete = 0
    </update>

    <update id="updateDepartStatus" >
        update duty_detail set duty_status = #{dutyStatus}
        WHERE duty_id = #{dutyId}
          and depart_id = #{departId}
          and is_delete = 0
    </update>

    <update id="deleteInfo" >
        update duty_detail set is_delete = 1
        WHERE duty_id = #{dutyId}
        <if test="departId != null ">
            and depart_id = #{departId}
        </if>
          and is_delete = 0
    </update>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.system.domain.DutyDetail" useGeneratedKeys="true">
        insert into duty_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            duty_id,
            depart_id,
            <if test="departName != null">
                depart_name,
            </if>
            <if test="orderNum != null">
                order_num,
            </if>
            <if test="dutyLeader != null">
                duty_leader,
            </if>
            <if test="dutyLeaderPhone != null">
                duty_leader_phone,
            </if>
            <if test="dutyStatus != null">
                duty_status,
            </if>
            <if test="dutyReportName != null">
                duty_report_name,
            </if>
            <if test="dutyReportAddress != null">
                duty_report_address,
            </if>
            <if test="reportUploadTime != null">
                report_upload_time,
            </if>
            <if test="haveReport != null">
                have_report,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=VARCHAR},
            #{dutyId,jdbcType=VARCHAR},
            #{departId,jdbcType=BIGINT},
            <if test="departName != null">
                #{departName,jdbcType=VARCHAR},
            </if>
            <if test="orderNum != null">
                #{orderNum,jdbcType=TINYINT},
            </if>
            <if test="dutyLeader != null">
                #{dutyLeader,jdbcType=VARCHAR},
            </if>
            <if test="dutyLeaderPhone != null">
                #{dutyLeaderPhone,jdbcType=VARCHAR},
            </if>
            <if test="dutyStatus != null">
                #{dutyStatus,jdbcType=TINYINT},
            </if>
            <if test="dutyReportName != null">
                #{dutyReportName,jdbcType=VARCHAR},
            </if>
            <if test="dutyReportAddress != null">
                #{dutyReportAddress,jdbcType=VARCHAR},
            </if>
            <if test="reportUploadTime != null">
                #{reportUploadTime,jdbcType=TIMESTAMP},
            </if>
            <if test="haveReport != null">
                #{haveReport,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
        

</mapper>