<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.OutReportHistoryMapper">
    
    <resultMap type="com.ruoyi.system.domain.OutReportHistory" id="OutReportHistoryResult">
        <result property="id"    column="id"    />
        <result property="reportId"    column="report_id"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="operatorStep"    column="operator_step"    />
        <result property="operatorStatus"    column="operator_status"    />
        <result property="approvalContent"    column="approval_content"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectOutReportHistoryVo">
        select id, report_id, operator_id, operator_step, operator_status, approval_content, create_time, update_time, is_delete from out_report_history
    </sql>

    <select id="selectOutReportHistoryList" parameterType="com.ruoyi.system.domain.OutReportHistory" resultMap="OutReportHistoryResult">
        <include refid="selectOutReportHistoryVo"/>
        <where>  
            <if test="reportId != null "> and report_id = #{reportId}</if>
            <if test="operatorId != null "> and operator_id = #{operatorId}</if>
            <if test="operatorStep != null "> and operator_step = #{operatorStep}</if>
            <if test="operatorStatus != null "> and operator_status = #{operatorStatus}</if>
            <if test="approvalContent != null  and approvalContent != ''"> and approval_content = #{approvalContent}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
        </where>
        ORDER BY create_time
    </select>

        
    <insert id="insertOutReportHistory" parameterType="com.ruoyi.system.domain.OutReportHistory" useGeneratedKeys="true" keyProperty="id">
        insert into out_report_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reportId != null">report_id,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorStep != null">operator_step,</if>
            <if test="operatorStatus != null">operator_status,</if>
            <if test="approvalContent != null">approval_content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDelete != null">is_delete,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reportId != null">#{reportId},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorStep != null">#{operatorStep},</if>
            <if test="operatorStatus != null">#{operatorStatus},</if>
            <if test="approvalContent != null">#{approvalContent},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
         </trim>
    </insert>

    <select id="selectByoutReportId" resultType="com.ruoyi.system.domain.OutReportHistory">
        SELECT h.id,
               h.report_id        AS reportId,
               h.operator_id      AS operatorId,
               h.operator_step    AS operatorStep,
               h.operator_status  AS operatorStatus,
               h.approval_content AS approvalContent,
               h.create_time      AS createTime,
               h.update_time      AS updateTime,
               h.is_delete        AS isDelete
        FROM out_report_history AS h
        WHERE 1 = 1
          AND h.report_id = #{reportId}
          AND h.is_delete = 0
    </select>
</mapper>