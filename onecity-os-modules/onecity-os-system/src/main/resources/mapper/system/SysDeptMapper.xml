<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysDeptMapper">

	<resultMap type="com.onecity.os.system.api.domain.SysDept" id="SysDeptResult">
		<id     property="deptId"     column="dept_id"     />
		<result property="parentId"   column="parent_id"   />
		<result property="ancestors"  column="ancestors"   />
		<result property="deptName"   column="dept_name"   />
		<result property="orderNum"   column="order_num"   />
		<result property="leader"     column="leader"      />
		<result property="phone"      column="phone"       />
		<result property="status"     column="status"      />
		<result property="delFlag"    column="del_flag"    />
		<result property="parentName" column="parent_name" />
		<result property="createBy"   column="create_by"   />
		<result property="createTime" column="create_time" />
		<result property="updateBy"   column="update_by"   />
		<result property="updateTime" column="update_time" />
		<result property="remark"       column="remark"       />
	</resultMap>

	<resultMap id="DepartUserListDtoMap" type="com.ruoyi.system.domain.vo.DepartUserListDto">
		<result column="departId" property="departId" jdbcType="VARCHAR"/>
		<result column="departName" property="departName" jdbcType="VARCHAR"/>
		<collection property="users" resultMap="UsersMap"/>
	</resultMap>
	<resultMap id="UsersMap" type="com.ruoyi.system.domain.vo.UserNameDto">
		<result column="userId" property="userId" jdbcType="VARCHAR"/>
		<result column="realName" property="realName" jdbcType="VARCHAR"/>
	</resultMap>

	<sql id="selectDeptVo">
        select d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.phone, d.status, d.del_flag, d.create_by, d.create_time, d.remark
        from sys_dept d
    </sql>
    
	<select id="selectDeptList" parameterType="com.onecity.os.system.api.domain.SysDept" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where d.del_flag = '0'
		<if test="deptId != null and deptId != 0">
			AND dept_id = #{deptId}
		</if>
        <if test="parentId != null and parentId != 0">
			AND parent_id = #{parentId}
		</if>
		<if test="deptName != null and deptName != ''">
			AND INSTR(dept_name,#{deptName})
		</if>
		<if test="status != null and status != ''">
			AND status = #{status}
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
		order by d.parent_id, d.order_num
    </select>
    
    <select id="selectDeptListByRoleId" resultType="Integer">
		select d.dept_id
		from sys_dept d
            left join sys_role_dept rd on d.dept_id = rd.dept_id
        where rd.role_id = #{roleId}
            <if test="deptCheckStrictly">
              and d.dept_id not in (select d.parent_id from sys_dept d inner join sys_role_dept rd on d.dept_id = rd.dept_id and rd.role_id = #{roleId})
            </if>
		order by d.parent_id, d.order_num
	</select>
    
    <select id="selectDeptById" parameterType="Long" resultMap="SysDeptResult">
		<include refid="selectDeptVo"/>
		where dept_id = #{deptId}
	</select>
	<select id="selectAllDept"  resultMap="SysDeptResult">
		<include refid="selectDeptVo"/>
		where status = '0' and del_flag = '0'
	</select>
    
    <select id="checkDeptExistUser" parameterType="Long" resultType="int">
		select count(1) from sys_user where dept_id = #{deptId} and del_flag = '0'
	</select>
	
	<select id="hasChildByDeptId" parameterType="Long" resultType="int">
		select count(1) from sys_dept
		where del_flag = '0' and parent_id = #{deptId} limit 1
	</select>
	
	<select id="selectChildrenDeptById" parameterType="Long" resultMap="SysDeptResult">
		select * from sys_dept where find_in_set(#{deptId}, ancestors)
	</select>

	<select id="getChildrenDeptById" parameterType="Long" resultMap="SysDeptResult">
		select * from sys_dept where status = '0' and del_flag = '0' and parent_id = #{deptId}
	</select>

	<select id="getDepartIsByDepartIds"  resultMap="SysDeptResult">
		SELECT * FROM sys_dept WHERE dept_id IN
		(SELECT dept_id FROM sys_user WHERE del_flag = 0 and dept_id in
		<foreach collection="ids" item="dep_id" open="(" separator="," close=")">
			#{dep_id}
		</foreach>) and del_flag = '0'
	</select>
	
	<select id="selectNormalChildrenDeptById" parameterType="Long" resultType="int">
		select count(*) from sys_dept where status = '0' and del_flag = '0' and find_in_set(#{deptId}, ancestors)
	</select>
	
	<select id="checkDeptNameUnique" resultMap="SysDeptResult">
	    <include refid="selectDeptVo"/>
		where dept_name=#{deptName} and parent_id = #{parentId} and del_flag = '0' limit 1
	</select>
    
    <insert id="insertDept" parameterType="com.onecity.os.system.api.domain.SysDept">
 		insert into sys_dept(
 			<if test="deptId != null and deptId != 0">dept_id,</if>
 			<if test="parentId != null and parentId != 0">parent_id,</if>
 			<if test="deptName != null and deptName != ''">dept_name,</if>
 			<if test="ancestors != null and ancestors != ''">ancestors,</if>
 			<if test="orderNum != null and orderNum != ''">order_num,</if>
 			<if test="leader != null and leader != ''">leader,</if>
 			<if test="phone != null and phone != ''">phone,</if>
 			<if test="status != null">status,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
			<if test="remark != null and remark != ''">remark,</if>
 			create_time
 		)values(
 			<if test="deptId != null and deptId != 0">#{deptId},</if>
 			<if test="parentId != null and parentId != 0">#{parentId},</if>
 			<if test="deptName != null and deptName != ''">#{deptName},</if>
 			<if test="ancestors != null and ancestors != ''">#{ancestors},</if>
 			<if test="orderNum != null and orderNum != ''">#{orderNum},</if>
 			<if test="leader != null and leader != ''">#{leader},</if>
 			<if test="phone != null and phone != ''">#{phone},</if>
 			<if test="status != null">#{status},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
			<if test="remark != null and remark != ''">#{remark},</if>
 			sysdate()
 		)
	</insert>
	
	<update id="updateDept" parameterType="com.onecity.os.system.api.domain.SysDept">
 		update sys_dept
 		<set>
 			<if test="parentId != null and parentId != 0">parent_id = #{parentId},</if>
 			<if test="deptName != null and deptName != ''">dept_name = #{deptName},</if>
 			<if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
 			<if test="orderNum != null and orderNum != ''">order_num = #{orderNum},</if>
 			<if test="leader != null">leader = #{leader},</if>
 			<if test="phone != null">phone = #{phone},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
			<if test="remark != null">remark = #{remark},</if>
 			update_time = sysdate()
 		</set>
 		where dept_id = #{deptId}
	</update>
	
	<update id="updateDeptChildren" parameterType="java.util.List">
	    update sys_dept set ancestors =
	    <foreach collection="depts" item="item" index="index"
	        separator=" " open="case dept_id" close="end">
	        when #{item.deptId} then #{item.ancestors}
	    </foreach>
	    where dept_id in
	    <foreach collection="depts" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.deptId}
	    </foreach>
	</update>
	 
	<update id="updateDeptStatusNormal" parameterType="Long">
 	    update sys_dept set status = '0' where dept_id in 
 	    <foreach collection="array" item="deptId" open="(" separator="," close=")">
        	#{deptId}
        </foreach>
	</update>
	
	<delete id="deleteDeptById" parameterType="Long">
		update sys_dept set del_flag = '2' where dept_id = #{deptId}
	</delete>

	<delete id="deleteDeptByIds">
		update sys_dept set del_flag = '2' where dept_id in
		<foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
			#{deptId}
		</foreach>
	</delete>

	<select id="selectUserIdsInDepts" resultType="java.lang.Long">
		select DISTINCT sur.user_id from sys_role_dept srd
		left join sys_user_role sur on srd.role_id = sur.role_id where srd.dept_id in
		<foreach collection="deptIdList" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</select>

	<select id="getDepartByUserId" parameterType="Long" resultMap="SysDeptResult">
		select d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.phone, d.status, d.del_flag, d.create_by, d.create_time
        from sys_dept d
        left join sys_user u on u.dept_id = d.dept_id
		where u.user_id = #{userId}
	</select>

	<select id="getAllUserList" parameterType="Long" resultType="com.ruoyi.system.domain.vo.SysUserAllVo">
		select u.user_id userId, u.dept_id deptId, u.user_name userName, u.nick_name realName, d.dept_name departName
        from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
		where u.del_flag = '0' and u.`status` = '0'
		<if test="deptId != null and deptId != ''">
			and u.dept_id = #{deptId}
		</if>
		order by u.user_id
	</select>

	<select id="getUserNameByName" resultMap="DepartUserListDtoMap">
		SELECT
		c.dept_id AS departId,
		c.dept_name AS departName,
		a.user_id AS userId,
		a.nick_name AS realName
		FROM
		sys_user a
		LEFT JOIN sys_dept c ON c.dept_id = a.dept_id
		WHERE
		a.`status` = '0'
		AND a.del_flag = 0
		AND c.del_flag = 0
		<if test="null != name and '' != name">
			AND INSTR(a.nick_name,#{name})
		</if>
	</select>

	<select id="getUserNameByNameAndSourceSimpleName" resultMap="DepartUserListDtoMap">
		SELECT
		f.dept_id AS departId,
		f.dept_name AS departName,
		d.user_id AS userId,
		d.nick_name AS realName
		FROM
		sys_menu a
		LEFT JOIN sys_role_menu b ON a.menu_id = b.menu_id
		LEFT JOIN sys_user_role c ON b.role_id = c.role_id
		LEFT JOIN sys_user d ON c.user_id = d.user_id
		LEFT JOIN sys_dept f ON d.dept_id = f.dept_id
		WHERE
		a.menu_name = '指标数据审核'
		AND d.user_id IS NOT NULL
		AND d.del_flag = 0
		AND d.`status` = '0'
		AND a.`status` = 0
		AND f.del_flag = 0
		AND trim(SUBSTRING_INDEX(a.path, '/' ,- 1)) = #{sourceSimpleName}
		<if test="null != name and '' != name">
			and INSTR(d.nick_name,#{name})
		</if>
	</select>

	<select id="getUserNameByNameAndDepartId" resultType="com.ruoyi.system.domain.vo.UserNameDto">
		SELECT
		a.user_id AS userId,
		a.nick_name AS realName
		FROM
		sys_user a
		WHERE
		a.`status` = '0'
		AND a.del_flag = 0
		AND a.dept_id IS NULL
		<if test="null != name and '' != name">
			AND INSTR(a.nick_name,#{name})
		</if>
	</select>

	<select id="getUserNameNoDepart" resultType="com.ruoyi.system.domain.vo.UserNameDto">
		SELECT
		f.dept_id AS departId,
		f.dept_name AS departName,
		d.user_id AS userId,
		d.nick_name AS realName
		FROM
		sys_menu a
		LEFT JOIN sys_role_menu b ON a.menu_id = b.menu_id
		LEFT JOIN sys_user_role c ON b.role_id = c.role_id
		LEFT JOIN sys_user d ON c.user_id = d.user_id
		LEFT JOIN sys_dept f ON d.dept_id = f.dept_id
		WHERE
		a.menu_name = '指标数据审核'
		AND d.user_id IS NOT NULL
		AND d.del_flag = 0
		AND d.status = '0'
		AND a.`status` = 0
		AND f.del_flag = 0
		AND trim(SUBSTRING_INDEX(a.path, '/' ,- 1)) = #{sourceSimpleName}
		AND f.dept_id is null
		<if test="null != name and '' != name">
			and INSTR(d.nick_name,#{name})
		</if>
	</select>

	<select id="getDeptNamesById" resultType="String">
		SELECT
		group_concat( sd.dept_name )
		FROM
		sys_dept sd
		left join sys_user su on sd.dept_id = su.dept_id
		WHERE
		su.del_flag = 0
		AND su.user_id IN
		<foreach collection="ids" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>

	<select id="getDepartByParentId" parameterType="Long" resultMap="SysDeptResult">
		select * from sys_dept where status = '0' and del_flag = '0' and parent_id = #{parentId} ORDER BY parent_id, order_num
	</select>

	<select id="getDepartmentIdByParentId" resultType="Long">
        SELECT dept_id FROM sys_dept WHERE find_in_set(#{id}, ancestors)
    </select>

	<select id="selectTopDepartByName" parameterType="String" resultMap="SysDeptResult">
		select * from sys_dept where status = '0' and del_flag = '0' and parent_id = 0
		<if test="name != null  and name != ''">
			and INSTR(dept_name, #{name})
		</if>
	</select>

	<select id="selectChildrenDepartByName" parameterType="String" resultMap="SysDeptResult">
		select * from sys_dept where status = '0' and del_flag = '0' and parent_id != 0
		<if test="name != null  and name != ''">
			and INSTR(dept_name, #{name})
		</if>
	</select>
</mapper> 