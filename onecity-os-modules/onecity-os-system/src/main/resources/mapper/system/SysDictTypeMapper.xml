<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysDictTypeMapper">

	<resultMap type="com.onecity.os.system.api.domain.SysDictType" id="SysDictTypeResult">
		<id     property="dictId"     column="dict_id"     />
		<result property="dictName"   column="dict_name"   />
		<result property="dictType"   column="dict_type"   />
		<result property="status"     column="status"      />
		<result property="createBy"   column="create_by"   />
		<result property="createTime" column="create_time" />
		<result property="updateBy"   column="update_by"   />
		<result property="updateTime" column="update_time" />
	</resultMap>
	
	<sql id="selectDictTypeVo">
        select dict_id, dict_name, dict_type, status, create_by, create_time, remark 
		from sys_dict_type
    </sql>

	<select id="selectDictTypeList" parameterType="com.onecity.os.system.api.domain.SysDictType" resultMap="SysDictTypeResult">
	    <include refid="selectDictTypeVo"/>
		where status = '0'
		<if test="dictName != null and dictName != ''">
			  AND INSTR(dict_name,#{dictName})
		</if>
		<if test="dictType != null and dictType != ''">
			  AND INSTR(dict_type, #{dictType})
		</if>
	</select>
	
	<select id="selectDictTypeAll" resultMap="SysDictTypeResult">
		<include refid="selectDictTypeVo"/>
	</select>
	
	<select id="selectDictTypeById" parameterType="Long" resultMap="SysDictTypeResult">
		<include refid="selectDictTypeVo"/>
		where dict_id = #{dictId}
	</select>
	
	<select id="selectDictTypeByType" parameterType="String" resultMap="SysDictTypeResult">
		<include refid="selectDictTypeVo"/>
		where dict_type = #{dictType}
	</select>
	
	<select id="checkDictTypeUnique" parameterType="String" resultMap="SysDictTypeResult">
		<include refid="selectDictTypeVo"/>
		where dict_type = #{dictType} limit 1
	</select>
	
	<delete id="deleteDictTypeById" parameterType="Long">
 		update sys_dict_type set status = '1' where dict_id = #{dictId}
 	</delete>
 	
 	<delete id="deleteDictTypeByIds" parameterType="Long">
 		delete from sys_dict_type where dict_id in
 		<foreach collection="array" item="dictId" open="(" separator="," close=")">
 			#{dictId}
        </foreach> 
 	</delete>

 	<update id="updateDictType" parameterType="com.onecity.os.system.api.domain.SysDictType">
 		update sys_dict_type
 		<set>
 			<if test="dictName != null and dictName != ''">dict_name = #{dictName},</if>
 			<if test="dictType != null and dictType != ''">dict_type = #{dictType},</if>
 			<if test="status != null">status = #{status},</if>
 			<if test="remark != null">remark = #{remark},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			update_time = sysdate()
 		</set>
 		where dict_id = #{dictId}
	</update>
 	
 	<insert id="insertDictType" parameterType="com.onecity.os.system.api.domain.SysDictType">
 		insert into sys_dict_type(
 			<if test="dictName != null and dictName != ''">dict_name,</if>
 			<if test="dictType != null and dictType != ''">dict_type,</if>
 			<if test="status != null">status,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			create_time
 		)values(
 			<if test="dictName != null and dictName != ''">#{dictName},</if>
 			<if test="dictType != null and dictType != ''">#{dictType},</if>
 			<if test="status != null">#{status},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			sysdate()
 		)
	</insert>

	<!-- 重复校验 sql语句 -->
	<select id="duplicateCheckCountSql" resultType="Long" parameterType="com.ruoyi.system.domain.vo.DuplicateCheckVo">
		SELECT COUNT(*) FROM ${tableName} WHERE ${fieldName} = #{fieldVal}
		<if test="'sys_user' == tableName">
			AND del_flag = 0 and user_id &lt;&gt; #{dataId}
		</if>
		<if test="'sys_role' == tableName">
			AND del_flag = 0 and role_id &lt;&gt; #{dataId}
		</if>
		<if test="'sys_dict_type' == tableName">
			and dict_id &lt;&gt; #{dataId}
		</if>
	</select>

	<!-- 重复校验 sql语句 -->
	<select id="duplicateCheckCountSqlNoDataId" resultType="Long" parameterType="com.ruoyi.system.domain.vo.DuplicateCheckVo">
		SELECT COUNT(*) FROM ${tableName} WHERE ${fieldName} = #{fieldVal}
		<if test="'sys_user' == tableName || 'sys_role' == tableName">
			AND del_flag = 0
		</if>
	</select>

	<select id="deleteList"  resultMap="SysDictTypeResult">
		<include refid="selectDictTypeVo"/>
		where status = '1'
	</select>

	<delete id="deleteOneDictPhysically" parameterType="Long">
 		delete from sys_dict_type where dict_id = #{dictId}
 	</delete>

	<delete id="deleteDataByDictId" parameterType="Long">
		delete from sys_dict_data where dict_type = (select dict_type from sys_dict_type where dict_id = #{dictId})
 	</delete>

	<delete id="updateDictDelFlag" parameterType="Long">
 		update sys_dict_type set status = '0' where dict_id = #{dictId}
 	</delete>
</mapper> 