<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysUserMapper">

	<resultMap type="com.onecity.os.system.api.domain.SysUser" id="SysUserResult">
		<id     property="userId"       column="user_id"      />
		<result property="deptId"       column="dept_id"      />
		<result property="userName"     column="user_name"    />
		<result property="nickName"     column="nick_name"    />
		<result property="phonenumber"  column="phonenumber"  />
		<result property="IDCode"  column="id_code"  />
		<result property="sex"          column="sex"          />
		<result property="avatar"       column="avatar"       />
		<result property="password"     column="password"     />
		<result property="status"       column="status"       />
		<result property="delFlag"      column="del_flag"     />
		<result property="loginIp"      column="login_ip"     />
		<result property="loginDate"    column="login_date"   />
		<result property="createBy"     column="create_by"    />
		<result property="createTime"   column="create_time"  />
		<result property="updateBy"     column="update_by"    />
		<result property="updateTime"   column="update_time"  />
		<result property="remark"       column="remark"       />
		<result property="roleNames"       column="roleNames"       />
		<result property="rolelevel"       column="rolelevel"       />
		<result property="duties"       column="duties"       />
		<association property="dept"    column="dept_id" javaType="com.onecity.os.system.api.domain.SysDept" resultMap="deptResult" />
		<collection  property="roles"   javaType="java.util.List"        resultMap="RoleResult" />
	</resultMap>
	
	<resultMap id="deptResult" type="com.onecity.os.system.api.domain.SysDept">
		<id     property="deptId"   column="dept_id"     />
		<result property="parentId" column="parent_id"   />
		<result property="deptName" column="dept_name"   />
		<result property="orderNum" column="order_num"   />
		<result property="leader"   column="leader"      />
		<result property="status"   column="dept_status" />
	</resultMap>
	
	<resultMap id="RoleResult" type="com.onecity.os.system.api.domain.SysRole">
		<id     property="roleId"       column="role_id"        />
		<result property="roleName"     column="role_name"      />
		<result property="roleKey"      column="role_key"       />
		<result property="roleSort"     column="role_sort"      />
		<result property="dataScope"     column="data_scope"    />
		<result property="status"       column="role_status"    />
	</resultMap>
	
	<sql id="selectUserVo">
        select u.user_id, u.dept_id, u.user_name, u.nick_name, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,
        d.dept_id, d.parent_id, d.dept_name, d.order_num, d.leader, d.status as dept_status,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status,u.duties
        from sys_user u
		    left join sys_dept d on u.dept_id = d.dept_id
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
    </sql>
    
    <select id="selectUserList" parameterType="com.ruoyi.system.domain.vo.SysUserParam" resultMap="SysUserResult">
		select u.user_id, u.dept_id, u.nick_name, u.rolelevel,u.user_name, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by,
		u.create_time, u.remark, u.duties,d.dept_name, d.leader,GROUP_CONCAT(sr.role_name) as roleNames from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		LEFT JOIN sys_user_role sur ON u.user_id = sur.user_id
		LEFT JOIN sys_role sr ON sur.role_id = sr.role_id
		where u.del_flag = '0'
		<if test="nickName != null and nickName != ''">
			AND INSTR(u.nick_name, #{nickName})
		</if>
		<if test="userName != null and userName != ''">
			AND INSTR(u.user_name, #{userName})
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND INSTR(u.phonenumber, #{phonenumber})
		</if>
		<if test="roleName!=null and roleName!=''">
			and INSTR(sr.role_name,#{roleName})
		</if>
		<if test="departName!=null and departName!=''">
			and INSTR(d.dept_name,#{departName})
		</if>
		GROUP BY u.user_id
		<!-- 按真实姓名拼音排序 -->
		order by u.user_name
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>
	
	<select id="selectAllocatedList" parameterType="com.onecity.os.system.api.domain.SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.phonenumber, u.status, u.create_time,
		GROUP_CONCAT(r.role_name) as roleNames,d.dept_name
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and r.role_id = #{roleId}
	    <if test="userName != null and userName != ''">
			AND INSTR(u.user_name, #{userName})
		</if>
		<if test="nickName != null and nickName != ''">
			AND INSTR(u.nick_name, #{nickName})
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND INSTR(u.phonenumber, #{phonenumber})
		</if>
		GROUP BY u.user_id
		order by convert(u.user_name using gbk)
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>
	
	<select id="selectUnallocatedList" parameterType="com.onecity.os.system.api.domain.SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.phonenumber, u.status, u.create_time
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL)
	    and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and ur.role_id = #{roleId})
	    <if test="userName != null and userName != ''">
			AND INSTR(u.user_name, #{userName})
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND INSTR(u.phonenumber, #{phonenumber})
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>
	
	<select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
	    <include refid="selectUserVo"/>
		where (u.user_name = BINARY#{userName} or u.phonenumber = BINARY#{userName} or u.id_code = BINARY#{userName})
		and u.del_flag = '0'
	</select>

	<select id="selectUserByMobile" parameterType="String" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.phonenumber = #{mobile}
	</select>
	
	<select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId}
	</select>
	
	<select id="checkUserNameUnique" parameterType="String" resultType="int">
		select count(1) from sys_user where user_name = #{userName} and del_flag = '0' limit 1
	</select>
	
	<select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, phonenumber from sys_user where phonenumber = #{phonenumber} and del_flag = '0' limit 1
	</select>

	<select id="checkIDCodeUnique" parameterType="String" resultMap="SysUserResult">
		select user_id from sys_user where id_code = #{idCode} and del_flag = '0' limit 1
	</select>
	
	<insert id="insertUser" parameterType="com.onecity.os.system.api.domain.SysUser" useGeneratedKeys="true" keyProperty="userId">
 		insert into sys_user(
 			<if test="deptId != null and deptId != 0">dept_id,</if>
 			<if test="userName != null and userName != ''">user_name,</if>
 			<if test="nickName != null and nickName != ''">nick_name,</if>
			<if test="rolelevel != null ">rolelevel,</if>
 			<if test="avatar != null and avatar != ''">avatar,</if>
 			<if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
			<if test="IDCode != null and IDCode != ''">id_code,</if>
 			<if test="sex != null and sex != ''">sex,</if>
 			<if test="password != null and password != ''">password,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			<if test="remark != null and remark != ''">remark,</if>
			<if test="duties != null and duties != ''">duties,</if>
 			create_time
 		)values(
 			<if test="deptId != null and deptId != ''">#{deptId},</if>
 			<if test="userName != null and userName != ''">#{userName},</if>
 			<if test="nickName != null and nickName != ''">#{nickName},</if>
			<if test="rolelevel != null ">#{rolelevel},</if>
 			<if test="avatar != null and avatar != ''">#{avatar},</if>
 			<if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
			<if test="IDCode != null and IDCode != ''">#{IDCode},</if>
 			<if test="sex != null and sex != ''">#{sex},</if>
 			<if test="password != null and password != ''">#{password},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
			<if test="duties != null and duties != ''">#{duties},</if>
 			sysdate()
 		)
	</insert>
	
	<update id="updateUser" parameterType="com.onecity.os.system.api.domain.SysUser">
 		update sys_user
 		<set>
 			<if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
 			<if test="userName != null and userName != ''">user_name = #{userName},</if>
 			<if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
			<if test="rolelevel != null ">rolelevel = #{rolelevel},</if>
 			<if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
 			<if test="sex != null and sex != ''">sex = #{sex},</if>
 			<if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
 			<if test="password != null and password != ''">password = #{password},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
 			<if test="loginDate != null">login_date = #{loginDate},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			<if test="remark != null">remark = #{remark},</if>
			<if test="duties != null">duties = #{duties},</if>
 			update_time = sysdate()
 		</set>
 		where user_id = #{userId}
	</update>
	
	<update id="updateUserStatus" parameterType="com.onecity.os.system.api.domain.SysUser">
 		update sys_user set status = #{status} where user_id = #{userId}
	</update>
	
	<update id="updateUserAvatar" parameterType="com.onecity.os.system.api.domain.SysUser">
 		update sys_user set avatar = #{avatar} where user_name = #{userName}
	</update>
	
	<update id="resetUserPwd" parameterType="com.onecity.os.system.api.domain.SysUser">
 		update sys_user set password = #{password} where user_name = #{userName}
	</update>
	
	<delete id="deleteUserById" parameterType="Long">
		update sys_user set del_flag = '2' where user_id = #{userId}
 	</delete>
 	
 	<delete id="deleteUserByIds" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach> 
 	</delete>

	<delete id="changeStatusBatch">
		update sys_user set status = #{status} where user_id in
		<foreach collection="ids" item="userId" open="(" separator="," close=")">
			#{userId}
		</foreach>
	</delete>

	<select id="getUserByDepId"  resultMap="SysUserResult">
		SELECT su.*,GROUP_CONCAT(sr.role_name) as roleNames FROM sys_user su
		LEFT JOIN sys_user_role sur ON su.user_id = sur.user_id
		LEFT JOIN sys_role sr ON sur.role_id = sr.role_id
		WHERE su.dept_id = #{deptId} and su.del_flag = '0'
		<if test="userName!=null and userName!=''">
			and INSTR(user_name,#{userName})
		</if>
		<if test="nickName!=null and nickName!=''">
			and INSTR(nick_name,#{nickName})
		</if>
		GROUP BY su.user_id
		order by su.user_name
	</select>

	<select id="getPcUserNamesByPcUserIds" resultType="String">
		SELECT
		group_concat( nick_name )
		FROM
		sys_user
		WHERE
		del_flag = 0
		AND user_id IN
		<foreach collection="ids" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>

	<select id="getPcUserIdsByPcUserNames" resultType="String">
		SELECT
		group_concat( user_id )
		FROM
		sys_user
		WHERE
		del_flag = 0
		AND nick_name IN
		<foreach collection="names" item="name" open="(" separator="," close=")">
			#{name}
		</foreach>
	</select>

	<select id="getNickNameByName" resultType="String">
		SELECT
		nick_name
		FROM
		sys_user
		WHERE user_name = #{userName}
		AND del_flag = '0'
	</select>

	<select id="getRoleLevelByUserId" resultType="Integer">
		SELECT
		rolelevel
		FROM
		sys_user
		WHERE
		del_flag = 0
		AND `status` = '0'
		AND user_id = #{userId}
	</select>

	<select id="getUserByLevelAndDepartIds" resultMap="SysUserResult">
		select * from sys_user
		where rolelevel &gt;= #{levelUp} and rolelevel &lt;= #{levelDown} and dept_id in
		<foreach collection="ids" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
		and del_flag = '0'
		and `status` = '0'
	</select>

	<select id="getUserByLevelAndParams" resultMap="SysUserResult">
		select * from sys_user
		where rolelevel &gt;= #{levelUp} and rolelevel &lt;= #{levelDown}
		<if test="name != null  and name != ''">
			and INSTR(nick_name, #{name})
		</if>
		and del_flag = '0'
	</select>

	<delete id="deleteUserByUserName" parameterType="String">
		update sys_user set del_flag = '2' where user_name = #{userName}
 	</delete>

	<update id="updateUserByUserName" parameterType="com.onecity.os.system.api.domain.SysUser">
		update sys_user
		<set>
			<if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
			<if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
			<if test="password != null and password != ''">password = #{password},</if>
			update_time = sysdate()
		</set>
		where user_name = #{userName}
	</update>

	<select id="getUserIdByMenu" resultType="String">
		SELECT u.user_id
		FROM sys_menu AS m
				 LEFT JOIN sys_role_menu AS r ON m.menu_id = r.menu_id
				 LEFT JOIN sys_user_role AS u ON r.role_id = u.role_id
		WHERE 1 = 1
		  AND m.menu_name = #{menu}
	</select>

	<select id="getUserIdByRoleKey" resultType="String">
		SELECT user_id FROM sys_user_role sur
		LEFT JOIN sys_role sr on sur.role_id = sr.role_id
		WHERE sr.role_key = #{roleKey}
	</select>

	<select id="getUserByDepartId" resultMap="SysUserResult">
		SELECT *
		FROM sys_user AS u
		WHERE 1 = 1
		  AND del_flag = 0
		  AND u.`status` = '0'
		  AND u.dept_id = #{departId}
		  AND u.user_id != #{userId}
	</select>

	<select id="getAuditUserByDepartId" resultMap="SysUserResult">
		SELECT
		u.user_id, u.dept_id, u.user_name, u.nick_name, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark
		FROM
		sys_menu a
		LEFT JOIN sys_role_menu b ON a.menu_id = b.menu_id
		LEFT JOIN sys_user_role c ON b.role_id = c.role_id
		LEFT JOIN sys_user u ON c.user_id = u.user_id
		LEFT JOIN sys_dept f ON u.dept_id = f.dept_id
		WHERE
		a.menu_name = '指标数据审核'
		AND u.user_id IS NOT NULL
		AND u.del_flag = 0
		AND u.dept_id = #{departId}
		AND u.user_id != #{userId}
		AND u.`status` = '0'
		AND a.`status` = 0
		AND f.del_flag = 0
		AND trim(SUBSTRING_INDEX(a.path, '/' ,- 1)) = #{sourceSimpleName}
		<if test="null != name and '' != name">
			and INSTR(u.nick_name,#{name})
		</if>
	</select>

	<select id="getUserByPcUserIds" resultMap="SysUserResult">
		SELECT
		*
		FROM
		sys_user
		WHERE
		del_flag = 0
		AND user_id IN
		<foreach collection="userIds" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
		order by user_name
	</select>

	<select id="getUserIdsByRoles" resultType="String">
		SELECT
		group_concat( user_id )
		FROM
		sys_user_role
		WHERE role_id IN
		<foreach collection="roleIds" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>

	<select id="getUserByOneCodeId" resultMap="SysUserResult">
		SELECT *
		FROM sys_user AS u
		WHERE 1 = 1
		  AND u.one_code_user_id = #{oneCodeUserId}
			LIMIT 1
	</select>

	<select id="getUserIndicatorTitlePerms"  resultType="int">
		SELECT count(1) FROM sys_user_role sur
		LEFT JOIN sys_role_menu srm on sur.role_id = srm.role_id
		LEFT JOIN sys_menu sm on sm.menu_id = srm.menu_id
		WHERE sur.user_id = #{userId} and sm.perms = #{sourceSimpleName}
	</select>

	<update id="updateUserByOneCodeId" parameterType="com.onecity.os.system.api.domain.SysUser">
		update sys_user
		<set>
			<if test="userName != null and userName != ''">user_name = #{userName},</if>
			<if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
			<if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
			<if test="sex != null and sex != ''">sex = #{sex},</if>
			<if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
			<if test="status != null and status != ''">status = #{status},</if>
			<if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
			<if test="IDCode != null and IDCode != ''">id_code = #{IDCode},</if>
			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
			<if test="remark != null">remark = #{remark},</if>
			update_time = sysdate()
		</set>
		where one_code_user_id = #{oneCodeUserId}
	</update>

	<select id="getUserArchivePerms"  resultType="int">
		SELECT count(1) FROM sys_user_role sur
		LEFT JOIN sys_role_menu srm on sur.role_id = srm.role_id
		LEFT JOIN sys_menu sm on sm.menu_id = srm.menu_id
		WHERE sur.user_id = #{userId} and sm.perms = 'ARCHIVE'
	</select>

	<select id="getInformCleanPerms" resultType="int">
		SELECT count(1)
		FROM sys_user_role sur
				 LEFT JOIN sys_role_menu srm on sur.role_id = srm.role_id
				 LEFT JOIN sys_menu sm on sm.menu_id = srm.menu_id
		WHERE sur.user_id = #{userId}
		  and sm.perms = 'INFORM_CLEAN'
	</select>

	<select id="getOutReportRoleUserId" resultType="String">
		select su.user_id FROM sys_user su
		LEFT JOIN sys_user_role sur on su.user_id = sur.user_id
		LEFT JOIN sys_role sr on sr.role_id = sur.role_id
		WHERE sr.role_key = 'WCSHR' AND su.del_flag = '0' and su.`status` = '0' and
		su.dept_id = (SELECT dept_id FROM sys_user WHERE user_id = #{userId})
	</select>

	<select id="checkAdminByUserId" resultType="String">
		select sur.user_id
			FROM sys_user_role sur
			LEFT JOIN sys_role sr on sur.role_id = sr.role_id
		WHERE sr.role_key = 'admin'
		      and sur.user_id = #{userId}
	</select>
	<!-- 获取用户总数 -->
	<select id="queryAllUserCount" resultType="long">
		select count(1) from sys_user where del_flag = 0 and create_time  &lt; #{today}
	</select>
	<!-- 获取所有用户列表 -->
	<select id="queryAllUserList" resultMap="SysUserResult">
		SELECT * FROM sys_user WHERE del_flag = 0
	</select>
</mapper>