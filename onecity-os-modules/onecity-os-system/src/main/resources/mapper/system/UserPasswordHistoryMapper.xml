<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserPasswordHistoryMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.UserPasswordHistory">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="password" column="password" jdbcType="VARCHAR"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result column="is_delete"  property="isDelete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        SELECT id,user_id,password,
        sort,create_time,update_by,
        update_time from user_password_history
    </sql>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.system.domain.UserPasswordHistory" useGeneratedKeys="true">
        insert into user_password_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="password != null">
                `password`,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="password != null">
                #{password,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateBySelective" parameterType="com.ruoyi.system.domain.UserPasswordHistory">
        update user_password_history
        <set>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
        and  is_delete = 0
    </update>

    <select id="getListByUserId"  resultMap="BaseResultMap">
        <include refid="Base_Column_List"/>
        where user_id=#{userId}
        and  is_delete = 0
        order by sort
    </select>

    <update id="deleteByUserId" parameterType="Long">
        update user_password_history set is_delete = 1, update_time = sysdate()
        where user_id=#{userId} and is_delete = 0
    </update>

    <update id="deleteById" parameterType="Long">
        update user_password_history set is_delete = 1, update_time = sysdate()
        where id=#{id} and is_delete = 0
    </update>

    <update id="batchDeleteByUserIds" >
        update user_password_history set is_delete = 1, update_time = sysdate()
        where  is_delete = 0
        AND user_id IN
        <foreach collection="userIds" item="userId" separator="," open="(" close=")">
            #{userId}
        </foreach>
    </update>
</mapper>
