<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.keywork.mapper.WorkDeptFuzeMapper">

	<resultMap type="com.ruoyi.system.keywork.model.WorkDeptFuze" id="WorkDeptFuzeResult">
		<result property="workId"       column="work_id"      />
		<result property="fuzeDeptId"       column="fuze_dept_id"      />
	</resultMap>


	<insert id="addWorkDeptFuze" parameterType="com.ruoyi.system.keywork.model.WorkDeptFuze">
		insert into work_dept_fuze(
		work_id,
		fuze_dept_id)
		values (#{vo.workId},
				#{vo.fuzeDeptId})
	</insert>

	<delete id="deleteWorkDeptFuzeById">
		delete from work_dept_fuze where work_id = #{workId}
	</delete>

	<select id="getWorkDeptFuzeIdList" resultType="java.lang.Long">
		select fuze_dept_id from work_dept_fuze where work_id = #{workId}
	</select>


</mapper> 