-- 报警信息表性能优化索引脚本
-- 用于提升根据类型和时间区间查询的性能

-- 1. 报警时间索引（最重要的索引，用于时间区间查询）
CREATE INDEX idx_alarm_time ON tb_alarm_info(alarm_time DESC);

-- 2. 报警类型和时间复合索引（用于按类型和时间查询）
CREATE INDEX idx_alarm_type_time ON tb_alarm_info(alarm_type, alarm_time DESC);

-- 3. 设备编号索引（用于按设备查询）
CREATE INDEX idx_device_id ON tb_alarm_info(device_id);

-- 4. 设备编号和时间复合索引（用于按设备和时间查询）
CREATE INDEX idx_device_id_time ON tb_alarm_info(device_id, alarm_time DESC);

-- 5. 通道ID索引（用于按通道查询）
CREATE INDEX idx_channel_id ON tb_alarm_info(channel_id);

-- 6. 报警编号索引（用于按报警编号查询）
CREATE INDEX idx_alarm_id ON tb_alarm_info(alarm_id);

-- 7. 创建时间索引（用于按创建时间排序）
CREATE INDEX idx_create_time ON tb_alarm_info(create_time DESC);

-- 8. 覆盖索引（包含常用查询字段，减少回表查询）
CREATE INDEX idx_alarm_cover ON tb_alarm_info(alarm_time DESC, alarm_type, device_id, device_name, alarm_type_name);

-- 查看索引创建情况
SHOW INDEX FROM tb_alarm_info;

-- 性能测试查询语句
-- 1. 按时间区间查询
EXPLAIN SELECT * FROM tb_alarm_info 
WHERE alarm_time >= '2024-01-01 00:00:00' AND alarm_time <= '2024-01-31 23:59:59'
ORDER BY alarm_time DESC LIMIT 10;

-- 2. 按类型和时间区间查询
EXPLAIN SELECT * FROM tb_alarm_info 
WHERE alarm_type = 'fire_alarm' 
AND alarm_time >= '2024-01-01 00:00:00' AND alarm_time <= '2024-01-31 23:59:59'
ORDER BY alarm_time DESC LIMIT 10;

-- 3. 按设备和时间区间查询
EXPLAIN SELECT * FROM tb_alarm_info 
WHERE device_id = 'device001' 
AND alarm_time >= '2024-01-01 00:00:00' AND alarm_time <= '2024-01-31 23:59:59'
ORDER BY alarm_time DESC LIMIT 10;

-- 4. 统计查询
EXPLAIN SELECT alarm_type, alarm_type_name, COUNT(1) as count
FROM tb_alarm_info 
WHERE alarm_time >= '2024-01-01 00:00:00' AND alarm_time <= '2024-01-31 23:59:59'
GROUP BY alarm_type, alarm_type_name
ORDER BY count DESC;

-- 索引使用建议：
-- 1. idx_alarm_time: 用于所有基于时间的查询，是最重要的索引
-- 2. idx_alarm_type_time: 用于按类型和时间的复合查询，提升查询效率
-- 3. idx_device_id_time: 用于按设备和时间的复合查询
-- 4. idx_alarm_cover: 覆盖索引，包含常用字段，减少回表操作
-- 5. 其他单列索引用于特定字段的查询和过滤

-- 注意事项：
-- 1. 索引会占用额外的存储空间
-- 2. 索引会影响INSERT/UPDATE/DELETE的性能
-- 3. 需要根据实际查询模式调整索引策略
-- 4. 定期分析索引使用情况，删除不必要的索引
