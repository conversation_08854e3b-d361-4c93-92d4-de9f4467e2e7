package com.onecity.os.video.controller;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.video.domain.AlarmInfo;
import com.onecity.os.video.domain.vo.AlarmInfoQueryVo;
import com.onecity.os.video.service.IAlarmInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.ArrayList;
import java.util.List;

import static com.onecity.os.common.core.utils.DateUtils.parseDate;

/**
 * 报警信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Api(tags = "报警信息管理")
@RestController
@RequestMapping("/app/alarm")
@Validated
public class AlarmInfoController extends BaseController {
    
    private static final Logger log = LoggerFactory.getLogger(AlarmInfoController.class);

    @Autowired
    private IAlarmInfoService alarmInfoService;

    /**
     * 根据类型和时间区间查询报警信息列表（分页）
     * 高性能查询接口，支持时间区间和类型过滤
     */
    @ApiOperation("根据类型和时间区间查询报警信息列表")
    @GetMapping("/listByTypeAndTime")
    public TableDataInfo listByTypeAndTime(
            @RequestParam(required = false) String alarmTypeName,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(required = true, defaultValue = "1") Integer pageNum,
            @RequestParam(required = true, defaultValue = "10") Integer pageSize) {

        AlarmInfoQueryVo queryVo = new AlarmInfoQueryVo();
        try {
            if (StringUtils.isNotBlank(alarmTypeName)) {
                queryVo.setAlarmTypeName(alarmTypeName);
            }
            //将日期字符串转换为date类型
            if (StringUtils.isNotBlank(startTime)) {
                queryVo.setStartTime(parseDate(startTime));
            }
            if (StringUtils.isNotBlank(endTime)) {
                queryVo.setEndTime(parseDate(endTime));
            }
            startPage();
            // 执行查询
            List<AlarmInfo> pageInfo = alarmInfoService.selectAlarmInfoByTypeAndTime(queryVo);
            
            log.info("查询报警信息列表完成，结果数量：{}", JSONObject.toJSONString(pageInfo.size()));
            
            return getDataTable(pageInfo);
            
        } catch (Exception e) {
            log.error("查询报警信息列表异常，查询条件：{}", JSONObject.toJSONString(queryVo), e);
            return getDataTable(new ArrayList<>());
        }
    }

}
