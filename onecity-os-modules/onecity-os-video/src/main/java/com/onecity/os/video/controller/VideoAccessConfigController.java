package com.onecity.os.video.controller;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.utils.poi.ExcelUtil;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.video.domain.VideoAccessConfig;
import com.onecity.os.video.domain.excel.AccessVideoConfigExcel;
import com.onecity.os.video.domain.vo.*;
import com.onecity.os.video.service.IVideoAccessConfigService;

import com.ruoyi.common.security.annotation.RequiresPermissions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

import static com.github.pagehelper.page.PageMethod.startPage;

/**
 * 视频接入配置Controller
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Slf4j
@RestController
@RequestMapping("/video/access")
public class VideoAccessConfigController extends BaseController {
    @Autowired
    private IVideoAccessConfigService videoAccessConfigService;

    /**
     * 查询视频接入配置列表
     */
    @GetMapping("/list")
    public TableDataInfo list(VideoAccessConfig videoAccessConfig) {
        startPage();
        List<VideoAccessConfig> list = videoAccessConfigService.selectVideoAccessConfigList(videoAccessConfig);
        return getDataTable(list);
    }

    /**
     * 导出视频接入配置列表
     */
    @Log(title = "视频接入配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestBody VideoMenuVo3 videoMenuVo3,HttpServletResponse response) {
        List<VideoAccessConfig> list = videoAccessConfigService.selectVideoAccessConfigByMenuId(videoMenuVo3.getVideoMenuId());
        List<AccessVideoConfigExcel> accessVideoConfigExcelList = list.stream().map(item -> {
            AccessVideoConfigExcel accessVideoConfigExcel = new AccessVideoConfigExcel();
            accessVideoConfigExcel.setVideoPointName(item.getVideoPointName());
            accessVideoConfigExcel.setDecodeFormat(item.getDecodeFormat());
            accessVideoConfigExcel.setPlayer(item.getPlayer());
            accessVideoConfigExcel.setVideoStream(item.getVideoStream());
            accessVideoConfigExcel.setDeviceId(item.getDeviceId());
            accessVideoConfigExcel.setChannelId(item.getChannelId());
            accessVideoConfigExcel.setVideoUrl(item.getVideoUrl());
            accessVideoConfigExcel.setDataSource(item.getDataSource());
            accessVideoConfigExcel.setOrderNum(item.getOrderNum());
            return accessVideoConfigExcel;
        }).collect(Collectors.toList());

        ExcelUtil<AccessVideoConfigExcel> util = new ExcelUtil<AccessVideoConfigExcel>(AccessVideoConfigExcel.class);
        try {
            util.exportExcel(response, accessVideoConfigExcelList, "视频接入配置数据");
        } catch (IOException e) {
            log.error("导出失败："+e.getMessage());
        }
    }

    /**
     * 获取视频接入配置详细信息
     */
    @GetMapping(value = "/{id}")
    public BaseResult getInfo(@PathVariable("id") String id) {
        return BaseResult.ok(videoAccessConfigService.selectVideoAccessConfigById(id));
    }

    /**
     * 根据视频菜单ID查询配置列表
     */
    @GetMapping("/menu/{videoMenuId}")
    public BaseResult getConfigByMenuId(@PathVariable("videoMenuId") String videoMenuId) {
        try {
            List<VideoAccessConfig> list = videoAccessConfigService.selectVideoAccessConfigByMenuId(videoMenuId);
            return BaseResult.ok(list);
        } catch (Exception e) {
            log.error("根据视频菜单ID查询配置列表异常，菜单ID：{}", videoMenuId, e);
            return BaseResult.fail("查询配置列表失败：" + e.getMessage());
        }
    }
    

    /**
     * 获取视频播放地址
     */
    @GetMapping("/play/{id}")
    public BaseResult getVideoPlayUrl(@PathVariable("id") String id) {
        try {
            String playUrl = videoAccessConfigService.getVideoPlayUrl(id);
            if (playUrl != null) {
                return BaseResult.ok("获取播放地址成功", playUrl);
            } else {
                return BaseResult.fail("获取播放地址失败，配置不存在或播放地址为空");
            }
        } catch (Exception e) {
            log.error("获取视频播放地址异常，配置ID：{}", id, e);
            return BaseResult.fail("获取播放地址失败：" + e.getMessage());
        }
    }
    

    /**
     * 统计视频菜单下的配置数量
     */
    @GetMapping("/count/menu/{videoMenuId}")
    public BaseResult countConfigByMenuId(@PathVariable("videoMenuId") String videoMenuId) {
        try {
            int count = videoAccessConfigService.countVideoAccessConfigByMenuId(videoMenuId);
            return BaseResult.ok(count);
        } catch (Exception e) {
            log.error("统计视频菜单配置数量异常，菜单ID：{}", videoMenuId, e);
            return BaseResult.fail("查询配置数量失败：" + e.getMessage());
        }
    }

    /**
     * 视频配置查看接口
     */
    @GetMapping("/listVideoConfig")
    public BaseResult listVideoConfig(@RequestParam("videoMenuId") String videoMenuId,
                                      @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                      @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        try {
            log.info("查询视频配置列表，菜单ID：{}，页码：{}，页大小：{}", JSONObject.toJSONString(videoMenuId), JSONObject.toJSONString(pageNum), JSONObject.toJSONString(pageSize));

            // 设置分页参数
            startPage();

            // 查询配置VO列表
            List<VideoAccessConfigVo3> videoAccessConfigList = videoAccessConfigService.selectVideoAccessConfigVoByMenuId(videoMenuId);

            // 返回分页结果
            TableDataInfo dataTable = getDataTable(videoAccessConfigList);

            log.info("查询视频配置列表成功，菜单ID：{}，返回数量：{}", videoMenuId, videoAccessConfigList.size());
            return BaseResult.ok(dataTable);

        } catch (Exception e) {
            log.error("查询视频配置列表异常，菜单ID：{}，页码：{}，页大小：{}", JSONObject.toJSONString(videoMenuId), JSONObject.toJSONString(pageNum), JSONObject.toJSONString(pageSize), e);
            return BaseResult.fail("查询视频配置列表失败");
        }
    }

    /**
     * 视频配置新增接口
     */
    @Log(title = "视频接入配置", businessType = BusinessType.INSERT)
    @PostMapping("/addVideoConfig")
    public BaseResult addVideoConfig(@Validated @RequestBody VideoAccessConfigVo videoAccessConfigVo) {
        try {
            log.info("新增视频配置请求参数：{}", JSONObject.toJSONString(videoAccessConfigVo));

            // 根据channelId和serviceId和videoMenuId校验数据唯一性
            String validationResult = videoAccessConfigService.validateVideoConfigUniqueness(
                    videoAccessConfigVo.getChannelId(),
                    videoAccessConfigVo.getDeviceId(),
                    videoAccessConfigVo.getVideoMenuId()
            );
            //视频配置不超过10000个
            int count = videoAccessConfigService.countVideoAccessConfigByMenuId(videoAccessConfigVo.getVideoMenuId());
            if (count >= 9999) {
                return BaseResult.fail("视频配置不能超过10000个");
            }

            if (!"0".equals(validationResult)) {
                log.warn("视频配置唯一性校验失败：{}", JSONObject.toJSONString(validationResult));
                return BaseResult.fail(validationResult);
            }

            // 新增配置
            int result = videoAccessConfigService.insertVideoAccessConfigByVo(videoAccessConfigVo);
            if (result > 0) {
                log.info("新增视频配置成功：{}", JSONObject.toJSONString(videoAccessConfigVo.getVideoPointName()));
                return BaseResult.ok("新增视频配置成功");
            } else {
                log.error("新增视频配置失败：{}", JSONObject.toJSONString(videoAccessConfigVo.getVideoPointName()));
                return BaseResult.fail("新增视频配置失败");
            }
        } catch (Exception e) {
            log.error("新增视频配置异常：{}", e.getMessage(), e);
            return BaseResult.fail("新增视频配置失败");
        }
    }

    /**
     * 视频配置修改接口
     */
    @Log(title = "视频接入配置", businessType = BusinessType.UPDATE)
    @PostMapping("/editVideoConfig")
    public BaseResult editVideoConfig(@Validated @RequestBody VideoAccessConfigVo1 videoAccessConfigVo1) {
        try {
            log.info("修改视频配置请求参数：{}", JSONObject.toJSONString(videoAccessConfigVo1));

            // 检查配置是否存在
            if (!videoAccessConfigService.checkVideoAccessConfigExists(videoAccessConfigVo1.getId())) {
                return BaseResult.fail("修改视频配置失败：配置不存在");
            }
            // 根据channelId和deviceId和videoMenuId校验数据唯一性（编辑时排除当前记录）
            String validationResult = videoAccessConfigService.validateVideoConfigUniquenessForUpdate(
                    videoAccessConfigVo1.getId(),
                    videoAccessConfigVo1.getChannelId(),
                    videoAccessConfigVo1.getDeviceId(),
                    videoAccessConfigVo1.getVideoMenuId()
            );

            if (!"0".equals(validationResult)) {
                log.warn("视频配置唯一性校验失败：{}", validationResult);
                return BaseResult.fail(validationResult);
            }

            // 修改配置
            int result = videoAccessConfigService.updateVideoAccessConfigByVo1(videoAccessConfigVo1);
            if (result > 0) {
                log.info("修改视频配置成功：ID={}，点位名称={}",
                        JSONObject.toJSONString(videoAccessConfigVo1.getId()), JSONObject.toJSONString(videoAccessConfigVo1.getVideoPointName()));
                return BaseResult.ok("修改视频配置成功");
            } else {
                log.error("修改视频配置失败：ID={}，点位名称={}",
                        JSONObject.toJSONString(videoAccessConfigVo1.getId()), JSONObject.toJSONString(videoAccessConfigVo1.getVideoPointName()));
                return BaseResult.fail("修改视频配置失败");
            }
        } catch (Exception e) {
            log.error("修改视频配置异常：", e.getMessage());
            return BaseResult.fail("修改视频配置失败");
        }
    }

    /**
     * 视频配置批量删除接口
     */
    @Log(title = "视频接入配置", businessType = BusinessType.DELETE)
    @PostMapping("/deleteVideoConfig")
    public BaseResult deleteVideoConfig(@Validated @RequestBody List<VideoAccessConfigVo2> videoAccessConfigVo2List) {
        try {
            log.info("批量删除视频配置请求参数：{}", JSONObject.toJSONString(videoAccessConfigVo2List));

            // 检查删除列表是否为空
            if (videoAccessConfigVo2List == null || videoAccessConfigVo2List.isEmpty()) {
                return BaseResult.fail("删除列表不能为空");
            }

            // 执行批量删除
            String deleteResult = videoAccessConfigService.batchDeleteVideoAccessConfigByVo2List(videoAccessConfigVo2List);

            if ("0".equals(deleteResult)) {
                log.info("批量删除视频配置成功，删除数量：{}", JSONObject.toJSONString(videoAccessConfigVo2List.size()));
                return BaseResult.ok("批量删除视频配置成功");
            } else {
                log.error("批量删除视频配置失败：{}", JSONObject.toJSONString(deleteResult));
                return BaseResult.fail("批量删除视频配置失败");
            }
        } catch (Exception e) {
            log.error("批量删除视频配置异常：", e.getMessage());
            return BaseResult.fail("批量删除视频配置失败");
        }
    }

    /**
     * Excel导入视频配置接口
     */
    @Log(title = "视频接入配置", businessType = BusinessType.IMPORT)
    @PostMapping(value = "/importVideoConfig")
    public BaseResult importVideoConfig(String videoMenuId,String videoMenuName,MultipartFile file) {
        InputStream inputStream = null;
        try {
            log.info("Excel导入视频配置，文件名：{}，菜单ID：{}，菜单名称：{}",
                    JSONObject.toJSONString(file.getOriginalFilename()), JSONObject.toJSONString(videoMenuId), JSONObject.toJSONString(videoMenuName));
            inputStream = file.getInputStream();
            // 检查文件是否为空
            if (file.isEmpty()) {
                return BaseResult.fail("上传文件不能为空");
            }
            //检查文件大小，限制10MB
            if (file.getSize() > 10 * 1024 * 1024) {
                return BaseResult.fail("文件大小不能超过10MB");
            }
            // 检查文件格式
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                return BaseResult.fail("文件格式不正确，请上传Excel文件");
            }
            //检查文件魔数，防止修改后缀的文件入侵
            String fileSuffixName = fileName.substring(fileName.lastIndexOf(".")).toLowerCase(Locale.ROOT);
            //魔数判断
            byte[] fileHeader = new byte[4];
            inputStream.read(fileHeader);
            String fileHex = bytesToHex(fileHeader).toLowerCase(Locale.ROOT);
            log.info("文件魔数：{},文件后缀：{}", JSONObject.toJSONString(fileHex), JSONObject.toJSONString(fileSuffixName));
            if (".xlsx".equals(fileSuffixName)) {
                if (!"504b0304".equals(fileHex)) {
                    return BaseResult.fail("文件格式不正确，请上传Excel文件");
                }
            }
            if (".xls".equals(fileSuffixName)) {
                if (!"d0cf11e0".equals(fileHex)) {
                    return BaseResult.fail("文件格式不正确，请上传Excel文件");
                }
            }

            // 关闭第一个inputStream，因为已经读取了文件头
            inputStream.close();

            // 检查参数
            if (StringUtils.isEmpty(videoMenuId)) {
                return BaseResult.fail("视频菜单ID不能为空");
            }
            if (StringUtils.isEmpty(videoMenuName)) {
                return BaseResult.fail("视频菜单名称不能为空");
            }

            // 重新获取InputStream用于Excel解析
            inputStream = file.getInputStream();

            // 解析Excel文件
            ExcelUtil<AccessVideoConfigExcel> util = new ExcelUtil<>(AccessVideoConfigExcel.class);
            List<AccessVideoConfigExcel> excelList = util.importExcel(inputStream);

            if (excelList == null || excelList.isEmpty()) {
                return BaseResult.fail("Excel文件中没有数据");
            }

            log.info("解析Excel文件成功，数据量：{}", excelList.size());
            int count = videoAccessConfigService.countVideoAccessConfigByMenuId(videoMenuId);
            if (count+excelList.size() >= 10000) {
                return BaseResult.fail("视频配置不能超过10000个");
            }
            // 执行导入
            String importResult = videoAccessConfigService.importVideoAccessConfigFromExcel(
                    excelList, videoMenuId, videoMenuName);

            if ("0".equals(importResult)) {
                log.info("Excel导入视频配置成功，数据量：{}", excelList.size());
                return BaseResult.ok("Excel导入视频配置成功");
            } else {
                log.error("Excel导入视频配置失败：{}", importResult);
                return BaseResult.fail("Excel导入视频配置失败：" + importResult);
            }

        } catch (Exception e) {
            log.error("Excel导入视频配置异常：", e);
            return BaseResult.fail("Excel导入视频配置失败：" + e.getMessage());
        }finally {
            //关闭文件流
            if (inputStream != null) {
                try {
                    inputStream.close();
                }catch (IOException e){
                    log.error("关闭文件流异常：{}", e.getMessage(), e);
                }
            }
        }
    }

    private String bytesToHex(byte[] fileHeader) {
        StringBuilder builder = new StringBuilder();
        for (byte b : fileHeader) {
            builder.append(String.format("%02x", b));
        }
        return builder.toString();
    }

    /**
     * 下载Excel导入模板
     */
    @PostMapping("/exportVideoConfigTemplate")
    public void exportVideoConfigTemplate(HttpServletResponse response) {
        try {
            log.info("下载Excel导入模板");

            // 生成模板数据
            List<AccessVideoConfigExcel> templateList = videoAccessConfigService.generateExcelTemplate();

            // 导出Excel模板
            ExcelUtil<AccessVideoConfigExcel> util = new ExcelUtil<>(AccessVideoConfigExcel.class);
            util.exportExcel(response, templateList, "视频接入配置导入模板");

            log.info("下载Excel导入模板成功");

        } catch (Exception e) {
            log.error("下载Excel导入模板异常：", e.getMessage());
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"下载模板失败\"}");
            } catch (IOException ioException) {
                log.error("写入错误响应异常：", ioException.getMessage());
            }
        }
    }
}
