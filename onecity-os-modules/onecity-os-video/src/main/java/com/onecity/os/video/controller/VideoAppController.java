package com.onecity.os.video.controller;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.video.domain.po.VideoAccessConfigPo1;
import com.onecity.os.video.domain.po.VideoMenuAndConfig;
import com.onecity.os.video.domain.po.VideoMenuAndConfigPo;
import com.onecity.os.video.domain.vo.VideoMenuAndConfigVo4;
import com.onecity.os.video.service.IVideoAccessConfigService;
import com.onecity.os.video.service.IVideoMenuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 视频应用接口控制器
 * 提供移动端和前端应用使用的接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Slf4j
@RestController
@RequestMapping("/app/video")
public class VideoAppController extends BaseController {

    @Autowired
    private IVideoMenuService videoMenuService;

    @Autowired
    private IVideoAccessConfigService videoAccessConfigService;

    /**
     * 获取全部菜单以及第一个三级菜单下的视频配置列表（分页）
     * 需请求视频云平台接口获取设备的在线状态
     */
    @GetMapping("/listAllVideoMenuAndFirstConfig")
    public BaseResult<List<VideoMenuAndConfig>> listAllVideoMenuAndFirstConfig(
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        try {
            long startTime = System.currentTimeMillis();
            
            log.info("获取全部菜单和第一个三级菜单配置请求");

            // 参数校验
            if (pageNum != null && pageNum <= 0) {
                log.warn("页码参数无效：{}", pageNum);
                return BaseResult.fail("页码必须大于0");
            }

            if (pageSize != null && pageSize <= 0) {
                log.warn("页大小参数无效：{}", pageSize);
                return BaseResult.fail("页大小必须大于0");
            }

            // 调用服务获取数据
            List<VideoMenuAndConfig> result = videoMenuService.listAllVideoMenuAndFirstConfig(pageNum, pageSize);

            long endTime = System.currentTimeMillis();

            log.info("获取全部菜单和配置接口完成：{}", JSONObject.toJSONString(result));

            return BaseResult.ok(result);

        } catch (Exception e) {
            log.error("获取全部菜单和配置接口异常：{}", e.getMessage(), e);
            return BaseResult.fail("获取菜单和配置失败：系统内部错误，请联系管理员");
        }
    }

    /**
     * 根据菜单ID获取其下的视频配置（分页）
     * 需请求视频云平台接口获取设备的在线状态
     */
    @GetMapping("/listVideoMenuConfig")
    public TableDataInfo listVideoMenuConfig(
            @RequestParam String videoMenuId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        TableDataInfo tableDataInfoError = new TableDataInfo();
        try {
            startPage();
            log.info("根据菜单ID获取视频配置请求：{}", JSONObject.toJSONString(videoMenuId));

            // 参数校验
            if (videoMenuId == null || videoMenuId.trim().isEmpty()) {
                log.warn("根据菜单ID获取视频配置参数校验失败：菜单ID为空");
                tableDataInfoError.setCode(500);
                tableDataInfoError.setMsg("菜单ID不能为空");
                return tableDataInfoError;
            }

            if (pageNum != null && pageNum <= 0) {
                log.warn("页码参数无效：{}", pageNum);
                tableDataInfoError.setCode(500);
                tableDataInfoError.setMsg("页码参数无效");
                return tableDataInfoError;
            }

            if (pageSize != null && pageSize <= 0) {
                log.warn("页大小参数无效：{}", pageSize);
                tableDataInfoError.setCode(500);
                tableDataInfoError.setMsg("页大小参数无效");
                return tableDataInfoError;
            }

            // 调用服务获取分页配置数据
            List<VideoAccessConfigPo1> configList = videoAccessConfigService
                    .listVideoMenuConfigWithPage(videoMenuId);

            log.info("根据菜单ID获取视频配置完成：videoMenuId={}", JSONObject.toJSONString(videoMenuId));

            return getDataTable(configList);

        } catch (Exception e) {
            log.error("根据菜单ID获取视频配置异常：{}", e.getMessage(), e);
            tableDataInfoError.setCode(500);
            tableDataInfoError.setMsg("获取视频配置失败：系统内部错误，请联系管理员");
            return tableDataInfoError;
        }
    }

    /**
     * 搜索视频配置接口
     * 根据视频点位名称搜索视频配置，返回包含菜单层级关系的结果
     */
    @GetMapping("/findVideoMenuAndFirstConfig")
    public BaseResult<List<VideoMenuAndConfigPo>> findVideoMenuAndFirstConfig(
            @RequestParam String videoPointName) {
        try {

            log.info("搜索视频配置请求：{}", JSONObject.toJSONString(videoPointName));

            // 参数校验
            if (videoPointName == null || videoPointName.trim().isEmpty()) {
                log.warn("搜索视频配置参数校验失败：搜索关键词为空");
                return BaseResult.fail("搜索关键词不能为空");
            }

            // 长度校验
            if (videoPointName.trim().length() > 200) {
                return BaseResult.fail("搜索关键词长度不能超过200个字符");
            }

            // 调用服务进行搜索
            List<VideoMenuAndConfigPo> searchResults = videoAccessConfigService
                    .findVideoMenuAndFirstConfig(videoPointName);

            log.info("搜索视频配置完成：{}", JSONObject.toJSONString(searchResults));

            return BaseResult.ok(searchResults);

        } catch (Exception e) {
            log.error("搜索视频配置异常：{}", e.getMessage(), e);
            return BaseResult.fail("搜索视频配置失败：系统内部错误，请联系管理员");
        }
    }

    /**
     * 获取全部的菜单以及其下的视频配置
     *
     * 性能优化：
     * 1. 使用缓存减少数据库查询
     * 2. 批量查询设备在线状态
     * 3. 分页处理大数据量
     * 4. 异步处理非关键数据
     *
     * 安全措施：
     * 1. 参数验证和清理
     * 2. 访问权限控制
     * 3. 敏感信息脱敏
     * 4. 防止数据泄露
     *
     * @return 菜单和配置列表
     */
    @GetMapping("/listAllVideoMenuAndConfig")
    public BaseResult<List<VideoMenuAndConfigVo4>> listAllVideoMenuAndConfig() {
        try {
            log.info("开始获取全部菜单和视频配置");

            // 调用服务层获取数据
            List<VideoMenuAndConfigVo4> result = videoMenuService.listAllVideoMenuAndConfig();


            return BaseResult.ok(result);

        } catch (Exception e) {
            log.error("获取全部菜单和视频配置异常：", e);
            return BaseResult.fail("获取菜单配置失败，请稍后重试");
        }
    }

}