package com.onecity.os.video.controller;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.domain.BaseResult;
import com.onecity.os.common.core.web.controller.BaseController;
import com.onecity.os.common.core.web.domain.AjaxResult;
import com.onecity.os.common.log.annotation.Log;
import com.onecity.os.common.log.enums.BusinessType;
import com.onecity.os.video.domain.po.VideoMenuPo;
import com.onecity.os.video.domain.po.VideoMenuPo1;
import com.onecity.os.video.domain.po.VideoMenuPo2;
import com.onecity.os.video.domain.vo.VideoMenuVo;
import com.onecity.os.video.domain.po.VideoAccessConfigPo;
import com.onecity.os.video.domain.vo.VideoMenuVo1;
import com.onecity.os.video.domain.vo.VideoMenuVo2;
import com.onecity.os.video.service.IVideoAccessConfigService;
import com.onecity.os.video.service.IVideoMenuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 视频菜单Controller
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Slf4j
@RestController
@RequestMapping("/video")
public class VideoMenuController extends BaseController {
    @Autowired
    private IVideoMenuService videoMenuService;

    @Autowired
    private IVideoAccessConfigService videoAccessConfigService;

    /**
     * 获取视频菜单详细信息
     */
    @GetMapping(value = "/getVideoMenu")
    public AjaxResult getVideoMenu(@PathVariable("videoMenuId") String videoMenuId) {
        return AjaxResult.success(videoMenuService.selectVideoMenuByMenuId(videoMenuId));
    }

    /**
     * 新增视频菜单
     */
    @Log(title = "视频菜单", businessType = BusinessType.INSERT)
    @PostMapping("/addVideoMenu")
    public BaseResult addVideoMenu(@Validated @RequestBody VideoMenuVo videoMenuVo) {
        try {
            log.info("新增视频菜单请求参数：{}", videoMenuVo);
            //校验子菜单不能操过100个
            int count = videoMenuService.selectVideoMenuCount(videoMenuVo.getParentId());
            if (count >= 99) {
                return BaseResult.fail("子菜单不能超过100个");
            }
            int result = videoMenuService.insertVideoMenu(videoMenuVo);
            if (result > 0) {
                log.info("新增视频菜单成功：{}", videoMenuVo.getVideoMenuName());
                return BaseResult.ok("新增视频菜单成功");
            } else {
                log.error("新增视频菜单失败：{}", videoMenuVo.getVideoMenuName());
                return BaseResult.fail("新增视频菜单失败");
            }
        } catch (Exception e) {
            log.error("新增视频菜单异常：{}", e.getMessage(), e);
            return BaseResult.fail("新增视频菜单失败");
        }
    }

    /**
     * 修改视频菜单
     */
    @Log(title = "视频菜单", businessType = BusinessType.UPDATE)
    @PostMapping("/editVideoMenu")
    public BaseResult editVideoMenu(@Validated @RequestBody VideoMenuVo1 videoMenuVo1) {
        try {
            log.info("修改视频菜单请求参数：{}", JSONObject.toJSONString(videoMenuVo1));
            
            // 暂不校验菜单名称唯一性
//            if (!"0".equals(videoMenuService.checkVideoMenuNameUnique(videoMenuVo1))) {
//                return error("修改菜单'" + videoMenuVo1.getVideoMenuName() + "'失败，菜单名称已存在");
//            }
            
            int result = videoMenuService.updateVideoMenu(videoMenuVo1);
            if (result > 0) {
                log.info("修改视频菜单成功：{}", videoMenuVo1.getVideoMenuName());
                return BaseResult.ok("修改视频菜单成功");
            } else {
                log.error("修改视频菜单失败：{}", videoMenuVo1.getVideoMenuName());
                return BaseResult.fail("修改视频菜单失败");
            }
        } catch (Exception e) {
            log.error("修改视频菜单异常：", e.getMessage());
            return BaseResult.fail("修改视频菜单失败");
        }
    }

    /**
     * 删除视频菜单
     */
    @Log(title = "视频菜单", businessType = BusinessType.DELETE)
    @PostMapping("/deleteVideoMenu")
    public BaseResult deleteVideoMenu(@RequestBody List<VideoMenuVo2> videoMenuVo2List) {
        try {
            log.info("删除视频菜单请求参数：{}", JSONObject.toJSONString(videoMenuVo2List));

            // 参数校验
            if (videoMenuVo2List == null || videoMenuVo2List.isEmpty()) {
                log.warn("删除视频菜单参数校验失败：菜单列表为空");
                return BaseResult.fail("删除菜单列表不能为空");
            }

            List<String> menuIds = videoMenuVo2List.stream()
                    .map(VideoMenuVo2::getVideoMenuId)
                    .collect(Collectors.toList());

            // 基础参数校验
            for (VideoMenuVo2 videoMenuVo2 : videoMenuVo2List) {
                if (videoMenuVo2.getVideoMenuId() == null || videoMenuVo2.getVideoMenuId().trim().isEmpty()) {
                    log.error("删除视频菜单参数校验失败：菜单ID为空");
                    return BaseResult.fail("菜单ID不能为空");
                }

                if (videoMenuVo2.getVideoMenuName() == null || videoMenuVo2.getVideoMenuName().trim().isEmpty()) {
                    log.error("删除视频菜单参数校验失败：菜单名称为空");
                    return BaseResult.fail("菜单名称不能为空");
                }
            }

            // 批量验证菜单名称与菜单ID是否一致（性能优化：一次查询替代多次查询）
            try {

                Map<String, VideoMenuVo1> existingMenusMap = videoMenuService.selectVideoMenusByIds(menuIds);

                for (VideoMenuVo2 videoMenuVo2 : videoMenuVo2List) {
                    VideoMenuVo1 existingMenu = existingMenusMap.get(videoMenuVo2.getVideoMenuId());

                    if (existingMenu == null) {
                        log.error("删除视频菜单验证失败：菜单不存在，menuId：{}", JSONObject.toJSONString(videoMenuVo2.getVideoMenuId()));
                        return BaseResult.fail("菜单不存在：" + videoMenuVo2.getVideoMenuName());
                    }

                    if (!existingMenu.getVideoMenuName().equals(videoMenuVo2.getVideoMenuName())) {
                        log.error("删除视频菜单验证失败：菜单名称不匹配，期望：{}，实际：{}",
                                JSONObject.toJSONString(videoMenuVo2.getVideoMenuName()),
                                JSONObject.toJSONString(existingMenu.getVideoMenuName()));
                        return BaseResult.fail("菜单名称不匹配：" + videoMenuVo2.getVideoMenuName());
                    }
                }

            } catch (Exception e) {
                log.error("批量验证菜单信息异常：{}",e.getMessage());
                return BaseResult.fail("菜单验证失败：" + e.getMessage());
            }

            // 执行删除操作
            int result = videoMenuService.deleteVideoMenuByMenuIds(menuIds);

            if (result > 0) {
                return BaseResult.ok("删除视频菜单成功，共删除 " + result + " 个菜单");
            } else {
                return BaseResult.fail("删除视频菜单失败，可能菜单已被删除");
            }

        } catch (Exception e) {
            log.error("删除视频菜单异常：{}", e.getMessage());

            // 根据异常类型返回不同的错误信息
            String errorMessage = "删除视频菜单失败";
            if (e instanceof RuntimeException && e.getMessage() != null) {
                errorMessage = e.getMessage(); // RuntimeException通常包含用户友好的错误信息
            } else if (e.getMessage() != null && !e.getMessage().isEmpty()) {
                errorMessage = "删除视频菜单失败：" + e.getMessage();
            } else {
                errorMessage = "删除视频菜单失败：系统内部错误，请联系管理员";
            }

            return BaseResult.fail(errorMessage);
        }
    }

    /**
     * 视频菜单树接口（异步菜单加载）
     */
    @GetMapping("/listVideoMenu")
    public BaseResult listVideoMenu(@RequestParam(value = "parentId", required = false) String parentId) {
        try {
            log.info("查询视频菜单树列表，父菜单ID：{}", JSONObject.toJSONString(parentId));

            List<VideoMenuPo> videoMenuPoList = videoMenuService.listVideoMenu(parentId);

            log.info("查询视频菜单树列表成功，返回数量：{}", JSONObject.toJSONString(videoMenuPoList.size()));
            return BaseResult.ok(videoMenuPoList);

        } catch (Exception e) {
            log.error("查询视频菜单树列表异常，父菜单ID：{}", JSONObject.toJSONString(parentId), e.getMessage());
            return BaseResult.fail("查询视频菜单树列表失败");
        }
    }

    /**
     * 视频菜单列表接口（包含视频数量和在线状态统计）
     * 需请求视频云平台接口获取设备的在线状态
     * 注意：只有三级菜单下才能建视频配置
     */
    @Log(title = "视频菜单", businessType = BusinessType.OTHER)
    @GetMapping("/listVideoMenuNumAndStatus")
    public BaseResult<List<VideoMenuPo1>> listVideoMenuNumAndStatus(
            @RequestParam(value = "parentId", required = false) String parentId) {
        try {
            log.info("查询视频菜单列表（包含统计），父菜单ID：{}", JSONObject.toJSONString(parentId));

            // 查询菜单列表及统计信息
            List<VideoMenuPo1> menuList = videoMenuService.listVideoMenuNumAndStatus(parentId);

            log.info("查询视频菜单列表成功：{}", JSONObject.toJSONString(menuList.size()));
            return BaseResult.ok(menuList);

        } catch (Exception e) {
            log.error("查询视频菜单列表异常：{}", e.getMessage(), e);
            return BaseResult.fail("查询视频菜单列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据视频点位名称查询视频菜单（显示父级及父级的父级）
     * 需请求视频云平台接口获取设备的在线状态
     */
    @Log(title = "视频菜单", businessType = BusinessType.OTHER)
    @GetMapping("/findVideoMenuNumAndStatusByName")
    public BaseResult<VideoMenuPo2> findVideoMenuNumAndStatusByName(
            @RequestParam("videoPointName") String videoPointName) {
        try {
            log.info("根据视频点位名称查询视频菜单，videoPointName：{}", JSONObject.toJSONString(videoPointName));

            // 参数校验
            if (videoPointName == null || videoPointName.trim().isEmpty()) {
                return BaseResult.fail("视频点位名称不能为空");
            }

            // 查询菜单列表及统计信息
            VideoMenuPo2 menuList = videoMenuService.findVideoMenuNumAndStatusByName(videoPointName);

            log.info("根据视频点位名称查询成功：{}", JSONObject.toJSONString(menuList));
            return BaseResult.ok(menuList);

        } catch (Exception e) {
            log.error("根据视频点位名称查询视频菜单异常：", e.getMessage());
            return BaseResult.fail("查询失败");
        }
    }

    /**
     * 根据菜单ID获取视频配置接口
     * 需请求视频云平台接口获取设备的在线状态
     */
    @Log(title = "视频配置", businessType = BusinessType.OTHER)
    @GetMapping("/listAllVideoConfig")
    public BaseResult<List<VideoAccessConfigPo>> listAllVideoConfig(@RequestParam String videoMenuId) {
        try {
            log.info("根据菜单ID获取视频配置请求：videoMenuId:{}", JSONObject.toJSONString(videoMenuId));

            // 参数校验
            if (videoMenuId == null || videoMenuId.trim().isEmpty()) {
                log.warn("根据菜单ID获取视频配置参数校验失败：菜单ID为空");
                return BaseResult.fail("菜单ID不能为空");
            }

            // 获取视频配置列表
            List<VideoAccessConfigPo> configList = videoAccessConfigService.listAllVideoConfig(videoMenuId);

            log.info("根据菜单ID获取视频配置成功：{}", JSONObject.toJSONString(videoMenuId));

            return BaseResult.ok(configList);

        } catch (Exception e) {
            log.error("根据菜单ID获取视频配置异常：{}", JSONObject.toJSONString(videoMenuId), e.getMessage());
            return BaseResult.fail("获取视频配置失败：系统内部错误，请联系管理员");
        }
    }

}
