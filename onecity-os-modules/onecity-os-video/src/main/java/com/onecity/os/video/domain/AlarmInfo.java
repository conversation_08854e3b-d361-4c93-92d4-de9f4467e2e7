package com.onecity.os.video.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.onecity.os.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 报警信息对象 tb_alarm_info
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class AlarmInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 设备编号 */
    private String deviceId;

    /** 设备名称 */
    private String deviceName;

    /** 算法解析id */
    private String coreVideoId;

    /** 解析配置id */
    private Long analysisId;

    /** 算法编码 */
    private String alarmCode;

    /** 报警类型id */
    private String alarmType;

    /** 报警类型名称 */
    private String alarmTypeName;

    /** 报警编号 */
    private String alarmId;

    /** 报警时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date alarmTime;

    /** 预警图 */
    private String alarmUrl;

    /** 预警坐标 */
    private String alarmLocation;

    /** 报警地址 */
    private String alarmAddress;

    /** 算法抓配类型 */
    private String captureType;

    /** 通道ID */
    private String channelId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    private String createBy;
    private String updateBy;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deviceId", getDeviceId())
            .append("deviceName", getDeviceName())
            .append("coreVideoId", getCoreVideoId())
            .append("analysisId", getAnalysisId())
            .append("alarmCode", getAlarmCode())
            .append("alarmType", getAlarmType())
            .append("alarmTypeName", getAlarmTypeName())
            .append("alarmId", getAlarmId())
            .append("alarmTime", getAlarmTime())
            .append("alarmUrl", getAlarmUrl())
            .append("alarmLocation", getAlarmLocation())
            .append("alarmAddress", getAlarmAddress())
            .append("captureType", getCaptureType())
            .append("channelId", getChannelId())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
