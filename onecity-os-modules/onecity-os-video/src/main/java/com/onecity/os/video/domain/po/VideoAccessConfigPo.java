package com.onecity.os.video.domain.po;

import lombok.Data;

/**
 * 视频接入配置PO
 * 用于获取视频配置列表，包含在线状态信息
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class VideoAccessConfigPo {

    /**
     * 主键id
     */
    private String id;

    /**
     * 视频菜单id
     */
    private String videoMenuId;

    /**
     * 视频菜单名称
     */
    private String videoMenuName;

    /**
     * 序号
     */
    private Integer orderNum;

    /**
     * 视频点位名称
     */
    private String videoPointName;

    /**
     * 解码格式
     */
    private String decodeFormat;

    /**
     * 播放器
     */
    private String player;

    /**
     * 视频流
     */
    private String videoStream;

    /**
     * 视频平台设备唯一标识
     */
    private String extendId;

    /**
     * 视频平台设备Id
     */
    private String deviceId;

    /**
     * 视频平台通道Id
     */
    private String channelId;

    /**
     * 设备在线标识：1-在线，0-不在线
     */
    private String videoOnlineFlag;

    /**
     * 视频封面
     */
    private String thumbnail;

    /**
     * 视频地址
     */
    private String videoUrl;

    /**
     * 数据来源
     */
    private String dataSource;
}
