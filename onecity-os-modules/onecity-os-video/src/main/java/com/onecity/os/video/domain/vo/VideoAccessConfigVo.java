package com.onecity.os.video.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 视频接入配置VO
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class VideoAccessConfigVo {

    /**
     * 主键id
     */
    private String id;

    /**
     * 视频菜单id
     */
    @NotBlank(message = "视频菜单id不能为空")
    private String videoMenuId;

    /**
     * 视频菜单名称
     */
    @NotBlank(message = "视频菜单名称不能为空")
    private String videoMenuName;

    /**
     * 序号，只能是正整数，不能为0，且小于Integer类型存储的最大值
     */
    @NotNull(message = "序号不能为空")
    private Integer orderNum;

    /**
     * 视频点位名称
     */
    @NotBlank(message = "视频点位名称不能为空")
    private String videoPointName;

    /**
     * 解码格式
     */
    private String decodeFormat;

    /**
     * 播放器
     */
    private String player;

    /**
     * 视频流
     */
    private String videoStream;

    /**
     * 视频平台设备唯一标识
     */
    private String extendId;

    /**
     * 视频平台设备Id
     */
    @NotBlank(message = "视频平台设备Id不能为空")
    @Pattern(regexp = "^.{1,255}$", message = "视频平台设备Id长度不能超过255个字符")
    private String deviceId;

    /**
     * 视频平台通道Id
     */
    @NotBlank(message = "视频平台通道Id不能为空")
    @Pattern(regexp = "^.{1,255}$", message = "视频平台通道Id长度不能超过255个字符")
    private String channelId;

    /**
     * 视频地址
     */
    @NotBlank(message = "视频地址不能为空")
    private String videoUrl;

    /**
     * 数据来源
     */
    private String dataSource;
}
