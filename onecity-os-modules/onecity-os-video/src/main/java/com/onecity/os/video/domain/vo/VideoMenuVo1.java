package com.onecity.os.video.domain.vo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import lombok.Data;

/**
 * 视频菜单VO
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class VideoMenuVo1 {

    /**
     * 菜单名称
     */
    @NotBlank(message = "视频菜单Id不能为空")
    private String videoMenuId;
    /**
     * 菜单名称，长度小于20
     */
    @NotBlank(message = "菜单名称不能为空")
    @Size(max = 20, message = "菜单名称长度不能超过20个汉字")
    private String videoMenuName;

    /**
     * 上级菜单id
     */
    private String parentId;

    /**
     * 上级菜单名称
     */
    private String parentName;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空")
    private Integer orderNum;
}
