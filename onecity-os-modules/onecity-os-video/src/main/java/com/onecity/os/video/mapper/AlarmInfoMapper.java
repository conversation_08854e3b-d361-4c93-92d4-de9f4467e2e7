package com.onecity.os.video.mapper;

import com.onecity.os.video.domain.AlarmInfo;
import com.onecity.os.video.domain.vo.AlarmInfoQueryVo;

import java.util.List;

/**
 * 报警信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface AlarmInfoMapper {

    /**
     * 根据类型和时间区间查询报警信息列表（高性能查询）
     * 
     * @param queryVo 查询条件
     * @return 报警信息集合
     */
    List<AlarmInfo> selectAlarmInfoByTypeAndTime(AlarmInfoQueryVo queryVo);


}
