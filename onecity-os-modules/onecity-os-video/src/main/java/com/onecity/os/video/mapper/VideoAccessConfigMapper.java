package com.onecity.os.video.mapper;

import com.onecity.os.video.domain.VideoAccessConfig;
import com.onecity.os.video.domain.po.VideoAccessConfigPo;
import com.onecity.os.video.domain.po.VideoAccessConfigPo1;
import com.onecity.os.video.domain.vo.VideoAccessConfigVo3;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 视频接入配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface VideoAccessConfigMapper {
    /**
     * 查询视频接入配置
     *
     * @param id 视频接入配置主键
     * @return 视频接入配置
     */
    VideoAccessConfig selectVideoAccessConfigById(String id);

    /**
     * 查询视频接入配置列表
     *
     * @param videoAccessConfig 视频接入配置
     * @return 视频接入配置集合
     */
    List<VideoAccessConfig> selectVideoAccessConfigList(VideoAccessConfig videoAccessConfig);

    /**
     * 新增视频接入配置
     *
     * @param videoAccessConfig 视频接入配置
     * @return 结果
     */
    int insertVideoAccessConfig(VideoAccessConfig videoAccessConfig);

    /**
     * 修改视频接入配置
     *
     * @param videoAccessConfig 视频接入配置
     * @return 结果
     */
    int updateVideoAccessConfig(VideoAccessConfig videoAccessConfig);

    /**
     * 删除视频接入配置
     *
     * @param id 视频接入配置主键
     * @return 结果
     */
    int deleteVideoAccessConfigById(String id);

    /**
     * 批量删除视频接入配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteVideoAccessConfigByIds(String[] ids);

    /**
     * 根据菜单ID批量删除视频接入配置
     *
     * @param menuIds 需要删除的菜单ID集合
     * @return 结果
     */
    int deleteVideoAccessConfigByMenuIds(@Param("list") List<String> menuIds);

    /**
     * 根据视频菜单ID查询配置列表
     *
     * @param videoMenuId 视频菜单ID
     * @return 配置列表
     */
    List<VideoAccessConfig> selectVideoAccessConfigByMenuId(String videoMenuId);

    /**
     * 根据视频菜单ID查询配置列表
     *
     * @param videoMenuId 视频菜单ID
     * @return 配置列表
     */
    List<VideoAccessConfigVo3> selectVideoAccessConfigVo3ByMenuId(String videoMenuId);

    /**
     * 检查设备ID和通道ID是否唯一
     *
     * @param deviceId 设备ID
     * @param channelId 通道ID
     * @param id 配置ID（排除自己）
     * @return 结果
     */
    VideoAccessConfig checkDeviceChannelUnique(@Param("deviceId") String deviceId,
                                               @Param("channelId") String channelId,
                                               @Param("id") String id);

    /**
     * 检查扩展ID是否唯一
     *
     * @param extendId 扩展ID
     * @param id 配置ID（排除自己）
     * @return 结果
     */
    VideoAccessConfig checkExtendIdUnique(@Param("extendId") String extendId,
                                          @Param("id") String id);

    /**
     * 根据菜单ID获取视频配置列表（包含菜单名称）
     * 性能优化：一次查询获取所有需要的数据
     *
     * @param videoMenuId 视频菜单ID
     * @return 视频配置列表
     */
    List<VideoAccessConfigPo> selectVideoConfigsByMenuId(@Param("videoMenuId") String videoMenuId);

    /**
     * 根据菜单ID分页获取视频配置列表（用于三级菜单）
     * 性能优化：支持分页查询，减少内存占用
     *
     * @param videoMenuId 视频菜单ID
     * @return 视频配置列表
     */
    List<VideoAccessConfigPo1> selectVideoConfigsByMenuIdWithPage(@Param("videoMenuId") String videoMenuId);


    List<VideoAccessConfigPo1> selectVideoConfigsByVideoPointName(@Param("videoPointName") String videoPointName,@Param("videoMenuId") String videoMenuId);

    List<VideoAccessConfigPo1> selectVideoConfigsByVideoPointNameAll(@Param("videoPointName") String videoPointName);


    /**
     * 根据菜单ID分页获取视频配置列表（用于三级菜单）
     * 性能优化：支持分页查询，减少内存占用
     *
     * @param videoMenuId 视频菜单ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 视频配置列表
     */
    List<VideoAccessConfigPo1> selectVideoConfigsByMenuIdWithPage1(@Param("videoMenuId") String videoMenuId,
                                                                  @Param("offset") int offset,
                                                                  @Param("limit") int limit);


    /**
     * 批量根据菜单ID列表获取第一页视频配置（性能优化）
     * 用于获取多个三级菜单的第一页配置数据
     *
     * @param menuIds 菜单ID列表
     * @param limit 每个菜单的配置数量限制
     * @return 视频配置列表
     */
    List<VideoAccessConfigPo1> selectFirstPageConfigsByMenuIds(@Param("list") List<String> menuIds,
                                                               @Param("limit") int limit);

    /**
     * 根据视频点位名称搜索视频配置（包含菜单层级信息）
     * 性能优化：一次查询获取配置和完整的菜单层级关系
     *
     * @param videoPointName 视频点位名称（支持模糊搜索）
     * @return 视频配置列表（包含菜单层级信息）
     */
    List<VideoAccessConfigPo1> searchVideoConfigsByPointName(@Param("videoPointName") String videoPointName);

    /**
     * 根据菜单ID统计视频配置数量
     * 用于分页查询的总数统计
     *
     * @param videoMenuId 视频菜单ID
     * @return 配置数量
     */
    long countVideoConfigsByMenuId(@Param("videoMenuId") String videoMenuId);

    /**
     * 批量根据菜单ID列表分页获取视频配置
     * 用于为多个三级菜单同时加载分页配置（性能优化）
     *
     * @param menuIds 菜单ID列表
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 视频配置列表
     */
    List<VideoAccessConfigPo1> selectVideoConfigsByMenuIdsWithPage(@Param("list") List<String> menuIds,
                                                                   @Param("offset") int offset,
                                                                   @Param("limit") int limit);

    /**
     * 统计视频菜单下的配置数量
     *
     * @param videoMenuId 视频菜单ID
     * @return 配置数量
     */
    int countVideoAccessConfigByMenuId(String videoMenuId);

    /**
     * 批量更新视频菜单名称
     *
     * @param videoMenuId 视频菜单ID
     * @param videoMenuName 视频菜单名称
     * @return 结果
     */
    int updateVideoMenuNameByMenuId(@Param("videoMenuId") String videoMenuId,
                                    @Param("videoMenuName") String videoMenuName);

    /**
     * 批量Upsert视频接入配置
     * 根据videoMenuId+deviceId+channelId组合判断，存在则更新，不存在则插入
     *
     * @param configList 配置列表
     * @return 影响行数
     */
    int batchUpsertVideoAccessConfig(@Param("list") List<VideoAccessConfig> configList);

    /**
     * 查询所有视频配置
     * 用于获取全部配置数据
     *
     * @return 所有配置列表
     */
    List<VideoAccessConfigPo1> selectAllVideoConfigs();

}
