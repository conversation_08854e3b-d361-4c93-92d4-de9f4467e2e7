package com.onecity.os.video.mapper;

import com.onecity.os.video.domain.VideoMenu;
import com.onecity.os.video.domain.dto.MenuDeviceDto;
import com.onecity.os.video.domain.po.VideoMenuAndConfig;
import com.onecity.os.video.domain.po.VideoMenuPo;
import com.onecity.os.video.domain.po.VideoMenuPo1;
import com.onecity.os.video.domain.po.VideoMenuPo2;
import com.onecity.os.video.domain.vo.VideoMenuVo1;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 视频菜单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface VideoMenuMapper {
    /**
     * 查询视频菜单
     *
     * @param menuId 视频菜单主键
     * @return 视频菜单
     */
    VideoMenuVo1 selectVideoMenuByMenuId(String menuId);

    /**
     * 查询视频菜单列表
     *
     * @param videoMenu 视频菜单
     * @return 视频菜单集合
     */
    List<VideoMenu> selectVideoMenuList(VideoMenu videoMenu);

    /**
     * 查询视频菜单列表
     *
     * @param menuIds 视频菜单
     * @return 视频菜单集合
     */
    List<VideoMenu> selectVideoMenusByMenuIds(List<String> menuIds);

    /**
     * 新增视频菜单
     *
     * @param videoMenu 视频菜单
     * @return 结果
     */
    int insertVideoMenu(VideoMenu videoMenu);

    /**
     * 查询视频菜单数量
     *
     * @param parentId 父菜单ID
     * @return 视频菜单数量
     */
    int selectVideoMenuCount(String parentId);

    /**
     * 修改视频菜单
     *
     * @param videoMenu 视频菜单
     * @return 结果
     */
    int updateVideoMenu(VideoMenu videoMenu);

    /**
     * 删除视频菜单
     *
     * @param menuId 视频菜单主键
     * @return 结果
     */
    int deleteVideoMenuByMenuId(String menuId);

    /**
     * 批量删除视频菜单
     *
     * @param menuIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteVideoMenuByMenuIds(List<String> menuIds);

    /**
     * 根据父菜单ID查询子菜单
     *
     * @param parentId 父菜单ID
     * @return 子菜单列表
     */
    List<VideoMenu> selectVideoMenuByParentId(String parentId);

    /**
     * 查询子菜单数量
     *
     * @param parentId 父菜单ID
     * @return 子菜单数量
     */
    int selectChildrenCountByParentId(String parentId);

    /**
     * 检查菜单名称是否唯一
     *
     * @param videoMenuName 菜单名称
     * @param parentId 父菜单ID
     * @param menuId 菜单ID（排除自己）
     * @return 结果
     */
    VideoMenu checkVideoMenuNameUnique(@Param("videoMenuName") String videoMenuName, 
                                       @Param("parentId") String parentId, 
                                       @Param("menuId") String menuId);

    /**
     * 根据菜单名称查询菜单
     *
     * @param videoMenuName 菜单名称
     * @return 菜单信息
     */
    VideoMenu selectVideoMenuByName(String videoMenuName);

    /**
     * 查询所有正常状态的菜单（不含按钮）
     *
     * @return 菜单列表
     */
    List<VideoMenu> selectVideoMenuNormalAll();

    /**
     * 查询视频菜单树列表（异步加载）
     *
     * @param parentId 父菜单ID（可选）
     * @return 视频菜单PO列表
     */
    List<VideoMenuPo> selectVideoMenuTreeList(@Param("parentId") String parentId);

    /**
     * 查询视频菜单列表（包含视频数量统计）
     *
     * @param parentId 父菜单ID（可选）
     * @return 视频菜单统计PO列表
     */
    List<VideoMenuPo1> selectVideoMenuWithVideoCount(@Param("parentId") String parentId);

    /**
     * 查询指定菜单下的所有设备ID（递归查询子菜单）
     *
     * @param menuId 菜单ID
     * @return 设备ID列表
     */
    List<String> selectDeviceIdsByMenuId(@Param("menuId") String menuId);

    /**
     * 查询指定菜单及其所有子菜单下的视频总数（递归统计）
     *
     * @param menuId 菜单ID
     * @return 视频总数
     */
    Integer selectVideoCountByMenuIdRecursive(@Param("menuId") String menuId);

    /**
     * 批量查询多个菜单的视频总数（递归统计）
     * 性能优化：一次查询获取多个菜单的统计结果
     *
     * @param menuIds 菜单ID列表
     * @return 菜单ID -> 视频总数的映射
     */
    @MapKey("menu_id")
    Map<String, Map<String, Object>> batchSelectVideoCountRecursive(@Param("menuIds") List<String> menuIds);

    /**
     * 批量查询多个菜单的设备ID
     * 性能优化：一次查询获取多个菜单的设备列表
     *
     * @param menuIds 菜单ID列表
     * @return 包含菜单ID和设备ID的结果列表
     */
    List<MenuDeviceDto> batchSelectDeviceIdsByMenuIds(@Param("menuIds") List<String> menuIds);

    List<MenuDeviceDto> batchSelectDeviceIdsByMenuIdsAndPointName(@Param("menuIds") List<String> menuIds,@Param("videoPointName") String videoPointName);

    /**
     * 根据视频点位名称查询视频菜单（显示父级及父级的父级）
     *
     * @param videoPointName 视频点位名称
     * @return 视频菜单查询结果列表
     */
    List<VideoMenuPo2> selectVideoMenuByVideoPointName(@Param("videoPointName") String videoPointName);

    /**
     * 批量查询视频菜单（性能优化）
     *
     * @param menuIds 菜单ID列表
     * @return 视频菜单列表
     */
    List<VideoMenuVo1> selectVideoMenusByIds(@Param("list") List<String> menuIds);

    /**
     * 查询所有菜单用于构建菜单树（性能优化）
     *
     * @return 菜单列表
     */
    List<VideoMenuAndConfig> selectAllVideoMenuForTree();

    /**
     * 查询所有视频菜单
     * 用于获取全部菜单树结构
     *
     * @return 所有菜单列表
     */
    List<VideoMenuPo> selectAllVideoMenus();
}
