package com.onecity.os.video.service;

import com.onecity.os.video.domain.AlarmInfo;
import com.onecity.os.video.domain.vo.AlarmInfoQueryVo;

import java.util.List;

/**
 * 报警信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IAlarmInfoService {

    /**
     * 根据类型和时间区间查询报警信息列表（分页）
     * 高性能查询，支持时间区间和类型过滤
     * 
     * @param queryVo 查询条件
     * @return 分页结果
     */
    List<AlarmInfo> selectAlarmInfoByTypeAndTime(AlarmInfoQueryVo queryVo);
}
