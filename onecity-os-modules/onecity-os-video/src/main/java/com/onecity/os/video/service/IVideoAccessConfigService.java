package com.onecity.os.video.service;

import com.onecity.os.video.domain.VideoAccessConfig;
import com.onecity.os.video.domain.excel.AccessVideoConfigExcel;
import com.onecity.os.video.domain.po.VideoAccessConfigPo;
import com.onecity.os.video.domain.po.VideoAccessConfigPo1;
import com.onecity.os.video.domain.po.VideoMenuAndConfigPo;
import com.onecity.os.video.domain.vo.VideoAccessConfigVo;
import com.onecity.os.video.domain.vo.VideoAccessConfigVo1;
import com.onecity.os.video.domain.vo.VideoAccessConfigVo2;
import com.onecity.os.video.domain.vo.VideoAccessConfigVo3;

import java.util.List;

/**
 * 视频接入配置Service接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IVideoAccessConfigService {
    /**
     * 查询视频接入配置
     *
     * @param id 视频接入配置主键
     * @return 视频接入配置
     */
    VideoAccessConfig selectVideoAccessConfigById(String id);

    /**
     * 查询视频接入配置列表
     *
     * @param videoAccessConfig 视频接入配置
     * @return 视频接入配置集合
     */
    List<VideoAccessConfig> selectVideoAccessConfigList(VideoAccessConfig videoAccessConfig);

    /**
     * 新增视频接入配置
     *
     * @param videoAccessConfig 视频接入配置
     * @return 结果
     */
    int insertVideoAccessConfig(VideoAccessConfig videoAccessConfig);

    /**
     * 修改视频接入配置
     *
     * @param videoAccessConfig 视频接入配置
     * @return 结果
     */
    int updateVideoAccessConfig(VideoAccessConfig videoAccessConfig);

    /**
     * 批量删除视频接入配置
     *
     * @param ids 需要删除的视频接入配置主键集合
     * @return 结果
     */
    int deleteVideoAccessConfigByIds(String[] ids);

    /**
     * 校验视频配置的唯一性（新增时使用）
     * 根据channelId、deviceId和videoMenuId校验数据唯一性
     *
     * @param channelId 通道ID
     * @param deviceId 设备ID
     * @param videoMenuId 视频菜单ID
     * @return 校验结果，"0"表示校验通过，其他表示错误信息
     */
    String validateVideoConfigUniqueness(String channelId, String deviceId, String videoMenuId);

    /**
     * 校验视频配置的唯一性（编辑时使用）
     * 根据channelId、deviceId和videoMenuId校验数据唯一性，排除当前记录
     *
     * @param id 当前记录ID（需要排除的记录）
     * @param channelId 通道ID
     * @param deviceId 设备ID
     * @param videoMenuId 视频菜单ID
     * @return 校验结果，"0"表示校验通过，其他表示错误信息
     */
    String validateVideoConfigUniquenessForUpdate(String id, String channelId, String deviceId, String videoMenuId);

    /**
     * 根据视频菜单ID查询配置列表
     *
     * @param videoMenuId 视频菜单ID
     * @return 配置列表
     */
    List<VideoAccessConfig> selectVideoAccessConfigByMenuId(String videoMenuId);

    /**
     * 统计视频菜单下的配置数量
     *
     * @param videoMenuId 视频菜单ID
     * @return 配置数量
     */
    int countVideoAccessConfigByMenuId(String videoMenuId);

    /**
     * 获取视频播放地址
     *
     * @param id 配置ID
     * @return 播放地址
     */
    String getVideoPlayUrl(String id);

    /**
     * 根据视频菜单ID查询配置VO列表
     *
     * @param videoMenuId 视频菜单ID
     * @return 配置VO列表
     */
    List<VideoAccessConfigVo3> selectVideoAccessConfigVoByMenuId(String videoMenuId);

    /**
     * 将VideoAccessConfig转换为VideoAccessConfigVo
     *
     * @param config 配置实体
     * @return 配置VO
     */
    VideoAccessConfigVo3 convertToVo(VideoAccessConfig config);

    /**
     * 批量将VideoAccessConfig转换为VideoAccessConfigVo
     *
     * @param configList 配置实体列表
     * @return 配置VO列表
     */
    List<VideoAccessConfigVo3> convertToVoList(List<VideoAccessConfig> configList);

    /**
     * 新增视频接入配置（通过VO）
     *
     * @param videoAccessConfigVo 视频接入配置VO
     * @return 结果
     */
    int insertVideoAccessConfigByVo(VideoAccessConfigVo videoAccessConfigVo);

    /**
     * 将VideoAccessConfigVo转换为VideoAccessConfig
     *
     * @param vo 配置VO
     * @return 配置实体
     */
    VideoAccessConfig convertFromVo(VideoAccessConfigVo vo);

    /**
     * 修改视频接入配置（通过VO1）
     *
     * @param videoAccessConfigVo1 视频接入配置修改VO
     * @return 结果
     */
    int updateVideoAccessConfigByVo1(VideoAccessConfigVo1 videoAccessConfigVo1);

    /**
     * 将VideoAccessConfigVo1转换为VideoAccessConfig
     *
     * @param vo1 配置修改VO
     * @return 配置实体
     */
    VideoAccessConfig convertFromVo1(VideoAccessConfigVo1 vo1);

    /**
     * 将VideoAccessConfig转换为VideoAccessConfigVo1
     *
     * @param config 配置实体
     * @return 配置修改VO
     */
    VideoAccessConfigVo1 convertToVo1(VideoAccessConfig config);


    /**
     * 检查配置是否存在
     *
     * @param id 配置ID
     * @return 是否存在
     */
    boolean checkVideoAccessConfigExists(String id);

    /**
     * 批量删除视频接入配置（通过VO2列表）
     *
     * @param videoAccessConfigVo2List 视频接入配置删除VO列表
     * @return 删除结果信息
     */
    String batchDeleteVideoAccessConfigByVo2List(List<VideoAccessConfigVo2> videoAccessConfigVo2List);

    /**
     * 校验批量删除的配置是否都存在
     *
     * @param videoAccessConfigVo2List 配置删除VO列表
     * @return 校验结果信息
     */
    String validateBatchDeleteConfigs(List<VideoAccessConfigVo2> videoAccessConfigVo2List);

    /**
     * 提取配置ID数组
     *
     * @param videoAccessConfigVo2List 配置删除VO列表
     * @return 配置ID数组
     */
    String[] extractConfigIds(List<VideoAccessConfigVo2> videoAccessConfigVo2List);

    /**
     * 检查配置是否可以删除
     *
     * @param id 配置ID
     * @return 是否可以删除
     */
    boolean checkConfigCanDelete(String id);

    /**
     * 批量检查配置是否存在
     *
     * @param ids 配置ID数组
     * @return 不存在的配置ID列表
     */
    List<String> checkBatchConfigExists(String[] ids);

    /**
     * Excel导入视频接入配置
     *
     * @param excelList Excel数据列表
     * @param videoMenuId 视频菜单ID
     * @param videoMenuName 视频菜单名称
     * @return 导入结果信息
     */
    String importVideoAccessConfigFromExcel(List<AccessVideoConfigExcel> excelList, String videoMenuId, String videoMenuName);

    /**
     * 校验Excel导入数据
     *
     * @param excelList Excel数据列表
     * @param videoMenuId 视频菜单ID
     * @param videoMenuName 视频菜单名称
     * @return 校验结果信息
     */
    String validateExcelImportData(List<AccessVideoConfigExcel> excelList, String videoMenuId, String videoMenuName);

    /**
     * 将Excel数据转换为VideoAccessConfig实体
     *
     * @param excel Excel数据
     * @param videoMenuId 视频菜单ID
     * @param videoMenuName 视频菜单名称
     * @return VideoAccessConfig实体
     */
    VideoAccessConfig convertExcelToEntity(AccessVideoConfigExcel excel, String videoMenuId, String videoMenuName);

    /**
     * 批量转换Excel数据为实体列表
     *
     * @param excelList Excel数据列表
     * @param videoMenuId 视频菜单ID
     * @param videoMenuName 视频菜单名称
     * @return 实体列表
     */
    List<VideoAccessConfig> convertExcelListToEntityList(List<AccessVideoConfigExcel> excelList, String videoMenuId, String videoMenuName);

    /**
     * 生成Excel导入模板
     *
     * @return Excel模板数据
     */
    List<AccessVideoConfigExcel> generateExcelTemplate();

    /**
     * 根据菜单ID获取视频配置列表（包含在线状态）
     * 需请求视频云平台接口获取设备的在线状态
     *
     * @param videoMenuId 视频菜单ID
     * @return 视频配置列表
     */
    List<VideoAccessConfigPo> listAllVideoConfig(String videoMenuId);

    /**
     * 根据菜单ID获取其下的视频配置（分页）
     * 需请求视频云平台接口获取设备的在线状态
     *
     * @param videoMenuId 视频菜单ID
     * @return 视频配置列表
     */
    List<VideoAccessConfigPo1> listVideoMenuConfigWithPage(String videoMenuId);

    /**
     * 搜索视频配置接口
     * 根据视频点位名称搜索视频配置，返回包含菜单层级关系的结果
     *
     * @param videoPointName 视频点位名称
     * @return 搜索结果列表
     */
    List<VideoMenuAndConfigPo> findVideoMenuAndFirstConfig(String videoPointName);

}
