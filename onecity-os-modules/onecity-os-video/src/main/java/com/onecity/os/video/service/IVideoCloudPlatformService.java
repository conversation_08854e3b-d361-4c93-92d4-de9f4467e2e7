package com.onecity.os.video.service;

import com.onecity.os.video.domain.po.VideoAccessConfigPo1;

import java.util.List;
import java.util.Map;

/**
 * 视频云平台接口服务
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IVideoCloudPlatformService {



    /**
     * 批量获取设备在线状态（新版本，使用deviceId和channelId）
     * 根据视频配置列表批量查询设备在线状态
     * 注意：视频的唯一性由deviceId和channelId两个字段确定
     *
     * @param configs 视频配置列表
     * @return 设备唯一标识(deviceId+channelId) -> 在线状态的映射
     */
    Map<String, Boolean> batchGetDeviceOnlineStatusByConfigs(List<VideoAccessConfigPo1> configs);

    /**
     * 获取视频封面
     * 根据设备唯一标识获取视频封面地址
     *
     * @param uniqueKey 设备唯一标识(deviceId|channelId)
     * @return 封面地址，如果没有则返回null
     */
    String getVideoThumbnail(String uniqueKey);

    /**
     * 根据设备ID和通道ID获取视频封面
     *
     * @param deviceId 设备ID
     * @param channelId 通道ID
     * @return 封面地址，如果没有则返回null
     */
    String getVideoThumbnailByDeviceAndChannel(String deviceId, String channelId);

}
