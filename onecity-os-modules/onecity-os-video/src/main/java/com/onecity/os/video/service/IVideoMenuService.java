package com.onecity.os.video.service;

import com.onecity.os.video.domain.po.VideoMenuAndConfig;
import com.onecity.os.video.domain.po.VideoMenuPo;
import com.onecity.os.video.domain.po.VideoMenuPo1;
import com.onecity.os.video.domain.po.VideoMenuPo2;
import com.onecity.os.video.domain.vo.VideoMenuAndConfigVo4;
import com.onecity.os.video.domain.vo.VideoMenuVo;
import com.onecity.os.video.domain.vo.VideoMenuVo1;

import java.util.List;
import java.util.Map;

/**
 * 视频菜单Service接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IVideoMenuService {
    /**
     * 查询视频菜单
     *
     * @param menuId 视频菜单主键
     * @return 视频菜单
     */
    VideoMenuVo1 selectVideoMenuByMenuId(String menuId);

    /**
     * 新增视频菜单
     *
     * @param videoMenuVo 视频菜单VO
     * @return 结果
     */
    int insertVideoMenu(VideoMenuVo videoMenuVo);

    /**
     * 查询视频菜单数量
     *
     * @param parentId 父菜单ID
     * @return 视频菜单数量
     */
    int selectVideoMenuCount(String parentId);

    /**
     * 修改视频菜单
     *
     * @param videoMenuVo1 视频菜单
     * @return 结果
     */
    int updateVideoMenu(VideoMenuVo1 videoMenuVo1);

    /**
     * 批量删除视频菜单
     *
     * @param menuIds 需要删除的视频菜单主键集合
     * @return 结果
     */
    int deleteVideoMenuByMenuIds(List<String> menuIds);

    /**
     * 查询视频菜单树列表（异步加载）
     *
     * @param parentId 父菜单ID（可选，为空时查询根节点）
     * @return 视频菜单PO列表
     */
    List<VideoMenuPo> listVideoMenu(String parentId);

    /**
     * 查询视频菜单列表（包含视频数量和在线状态统计）
     *
     * @param parentId 父菜单ID（可选，为空时查询根节点）
     * @return 视频菜单统计PO列表
     */
    List<VideoMenuPo1> listVideoMenuNumAndStatus(String parentId);

    /**
     * 根据视频点位名称查询视频菜单（显示父级及父级的父级）
     * 需请求视频云平台接口获取设备的在线状态
     *
     * @param videoPointName 视频点位名称
     * @return 视频菜单查询结果列表
     */
    VideoMenuPo2 findVideoMenuNumAndStatusByName(String videoPointName);

    /**
     * 批量查询视频菜单（性能优化）
     *
     * @param menuIds 菜单ID列表
     * @return 菜单ID -> 菜单信息的映射
     */
    Map<String, VideoMenuVo1> selectVideoMenusByIds(List<String> menuIds);

    /**
     * 获取全部菜单以及第一个三级菜单下的视频配置列表（分页）
     * 需请求视频云平台接口获取设备的在线状态
     * 注意：外层菜单结构不分页，只对菜单内的视频配置列表进行分页
     *
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 菜单树和配置列表
     */
    List<VideoMenuAndConfig> listAllVideoMenuAndFirstConfig(Integer pageNum, Integer pageSize);

    /**
     * 获取全部的菜单以及其下的视频配置
     *
     * 性能优化：
     * 1. 使用缓存减少数据库查询
     * 2. 批量查询设备在线状态
     * 3. 分页处理大数据量
     * 4. 异步处理非关键数据
     *
     * 安全措施：
     * 1. 参数验证和清理
     * 2. 访问权限控制
     * 3. 敏感信息脱敏
     * 4. 防止数据泄露
     *
     * @return 菜单和配置列表
     */
    List<VideoMenuAndConfigVo4> listAllVideoMenuAndConfig();

}
