package com.onecity.os.video.service.impl;

import com.onecity.os.video.domain.AlarmInfo;
import com.onecity.os.video.domain.vo.AlarmInfoQueryVo;
import com.onecity.os.video.mapper.AlarmInfoMapper;
import com.onecity.os.video.service.IAlarmInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 报警信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
@Slf4j
public class AlarmInfoServiceImpl implements IAlarmInfoService {

    @Autowired
    private AlarmInfoMapper alarmInfoMapper;


    /**
     * 根据类型和时间区间查询报警信息列表（分页）
     * 高性能查询，支持时间区间和类型过滤
     * 
     * @param queryVo 查询条件
     * @return 分页结果
     */
    @Override
    public List<AlarmInfo> selectAlarmInfoByTypeAndTime(AlarmInfoQueryVo queryVo) {
        try {
            // 参数校验
            String validationResult = validateQueryParams(queryVo);
            if (validationResult != null) {
                log.warn("查询参数校验失败：{}", validationResult);
                return new ArrayList<>();
            }

            // 执行查询
            List<AlarmInfo> list = alarmInfoMapper.selectAlarmInfoByTypeAndTime(queryVo);
            
            return list;
            
        } catch (Exception e) {
            log.error("查询报警信息异常，查询条件：{}", queryVo, e);
            return new ArrayList<>();
        }
    }

    /**
     * 校验查询参数
     *
     * @param queryVo 查询条件
     * @return 校验结果，null表示校验通过，否则返回错误信息
     */
    public String validateQueryParams(AlarmInfoQueryVo queryVo) {
        if (queryVo == null) {
            return "查询条件不能为空";
        }
        // 使用VO自带的校验方法
        String voValidationResult = queryVo.validate();
        if (voValidationResult != null) {
            return voValidationResult;
        }
        return null;
    }
}
