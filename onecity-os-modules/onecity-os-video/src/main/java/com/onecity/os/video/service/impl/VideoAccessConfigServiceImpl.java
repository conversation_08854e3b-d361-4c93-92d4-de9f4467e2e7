package com.onecity.os.video.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.onecity.os.common.core.utils.DateUtils;
import com.onecity.os.common.core.utils.IdUtils;
import com.onecity.os.video.domain.VideoAccessConfig;
import com.onecity.os.video.domain.excel.AccessVideoConfigExcel;
import com.onecity.os.video.domain.po.VideoAccessConfigPo;
import com.onecity.os.video.domain.po.VideoAccessConfigPo1;
import com.onecity.os.video.domain.po.VideoMenuAndConfigPo;
import com.onecity.os.video.domain.vo.VideoAccessConfigVo;
import com.onecity.os.video.domain.vo.VideoAccessConfigVo1;
import com.onecity.os.video.domain.vo.VideoAccessConfigVo2;
import com.onecity.os.video.domain.vo.VideoAccessConfigVo3;
import com.onecity.os.video.domain.vo.VideoMenuVo1;
import com.onecity.os.video.mapper.VideoAccessConfigMapper;
import com.onecity.os.video.service.IVideoAccessConfigService;
import com.onecity.os.video.service.IVideoCloudPlatformService;
import com.onecity.os.video.service.IVideoMenuService;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 视频接入配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Slf4j
@Service
public class VideoAccessConfigServiceImpl implements IVideoAccessConfigService {
    @Autowired
    private VideoAccessConfigMapper videoAccessConfigMapper;

    @Autowired
    private IVideoCloudPlatformService videoCloudPlatformService;

    @Autowired
    private IVideoMenuService videoMenuService;

    /**
     * 查询视频接入配置
     *
     * @param id 视频接入配置主键
     * @return 视频接入配置
     */
    @Override
    public VideoAccessConfig selectVideoAccessConfigById(String id) {
        return videoAccessConfigMapper.selectVideoAccessConfigById(id);
    }

    /**
     * 查询视频接入配置列表
     *
     * @param videoAccessConfig 视频接入配置
     * @return 视频接入配置
     */
    @Override
    public List<VideoAccessConfig> selectVideoAccessConfigList(VideoAccessConfig videoAccessConfig) {
        return videoAccessConfigMapper.selectVideoAccessConfigList(videoAccessConfig);
    }

    /**
     * 新增视频接入配置
     *
     * @param videoAccessConfig 视频接入配置
     * @return 结果
     */
    @Override
    public int insertVideoAccessConfig(VideoAccessConfig videoAccessConfig) {
        // 生成主键ID
        if (StringUtils.isEmpty(videoAccessConfig.getId())) {
            videoAccessConfig.setId(IdUtils.simpleUUID());
        }
        
        // 设置创建信息
        videoAccessConfig.setCreater(SecurityUtils.getLoginUser().getSysUser().getNickName());
        videoAccessConfig.setCreateTime(DateUtils.getNowDate());
        videoAccessConfig.setUpdater(SecurityUtils.getLoginUser().getSysUser().getNickName());
        videoAccessConfig.setUpdateTime(DateUtils.getNowDate());
        videoAccessConfig.setIsDelete("0"); // 未删除
        
        return videoAccessConfigMapper.insertVideoAccessConfig(videoAccessConfig);
    }

    /**
     * 修改视频接入配置
     *
     * @param videoAccessConfig 视频接入配置
     * @return 结果
     */
    @Override
    public int updateVideoAccessConfig(VideoAccessConfig videoAccessConfig) {
        // 设置更新信息
        videoAccessConfig.setUpdater(SecurityUtils.getLoginUser().getSysUser().getNickName());
        videoAccessConfig.setUpdateTime(DateUtils.getNowDate());
        
        return videoAccessConfigMapper.updateVideoAccessConfig(videoAccessConfig);
    }

    /**
     * 批量删除视频接入配置
     *
     * @param ids 需要删除的视频接入配置主键
     * @return 结果
     */
    @Override
    public int deleteVideoAccessConfigByIds(String[] ids) {
        return videoAccessConfigMapper.deleteVideoAccessConfigByIds(ids);
    }

    /**
     * 校验视频配置的唯一性（新增时使用）
     * 根据channelId、deviceId和videoMenuId校验数据唯一性
     *
     * @param channelId 通道ID
     * @param deviceId 设备ID
     * @param videoMenuId 视频菜单ID
     * @return 校验结果，"0"表示校验通过，其他表示错误信息
     */
    @Override
    public String validateVideoConfigUniqueness(String channelId, String deviceId, String videoMenuId) {
        try {
            // 参数校验
            if (StringUtils.isEmpty(channelId)) {
                return "通道ID不能为空";
            }
            if (StringUtils.isEmpty(deviceId)) {
                return "设备ID不能为空";
            }
            if (StringUtils.isEmpty(videoMenuId)) {
                return "视频菜单ID不能为空";
            }

            // 构建查询条件
            VideoAccessConfig queryConfig = new VideoAccessConfig();
            queryConfig.setChannelId(channelId);
            queryConfig.setDeviceId(deviceId);
            queryConfig.setVideoMenuId(videoMenuId);

            // 查询是否存在相同配置
            List<VideoAccessConfig> existingConfigs = videoAccessConfigMapper.selectVideoAccessConfigList(queryConfig);

            if (existingConfigs != null && !existingConfigs.isEmpty()) {
                VideoAccessConfig existingConfig = existingConfigs.get(0);
                return String.format("视频配置已存在：设备ID[%s]、通道ID[%s]在菜单[%s]下已配置，视频点位名称为[%s]",
                        deviceId, channelId, existingConfig.getVideoMenuName(), existingConfig.getVideoPointName());
            }

            // 校验通过
            return "0";

        } catch (Exception e) {
            log.error("校验视频配置唯一性异常，channelId：{}，deviceId：{}，videoMenuId：{}", JSONObject.toJSONString(channelId), JSONObject.toJSONString(deviceId), JSONObject.toJSONString(videoMenuId), e);
            return "校验视频配置唯一性时发生异常，请联系管理员";
        }
    }

    /**
     * 校验视频配置的唯一性（编辑时使用）
     * 根据channelId、deviceId和videoMenuId校验数据唯一性，排除当前记录
     *
     * @param id 当前记录ID（需要排除的记录）
     * @param channelId 通道ID
     * @param deviceId 设备ID
     * @param videoMenuId 视频菜单ID
     * @return 校验结果，"0"表示校验通过，其他表示错误信息
     */
    @Override
    public String validateVideoConfigUniquenessForUpdate(String id, String channelId, String deviceId, String videoMenuId) {
        try {
            // 参数校验
            if (StringUtils.isEmpty(id)) {
                return "记录ID不能为空";
            }
            if (StringUtils.isEmpty(channelId)) {
                return "通道ID不能为空";
            }
            if (StringUtils.isEmpty(deviceId)) {
                return "设备ID不能为空";
            }
            if (StringUtils.isEmpty(videoMenuId)) {
                return "视频菜单ID不能为空";
            }

            // 构建查询条件
            VideoAccessConfig queryConfig = new VideoAccessConfig();
            queryConfig.setChannelId(channelId);
            queryConfig.setDeviceId(deviceId);
            queryConfig.setVideoMenuId(videoMenuId);

            // 查询是否存在相同配置
            List<VideoAccessConfig> existingConfigs = videoAccessConfigMapper.selectVideoAccessConfigList(queryConfig);

            if (existingConfigs != null && !existingConfigs.isEmpty()) {
                // 编辑时需要排除当前记录：检查是否存在其他记录与当前配置冲突
                for (VideoAccessConfig existingConfig : existingConfigs) {
                    if (!id.equals(existingConfig.getId())) {
                        // 找到了不是当前记录的冲突配置
                        return String.format("视频配置已存在：设备ID[%s]、通道ID[%s]在菜单[%s]下已被其他配置使用，视频点位名称为[%s]",
                                deviceId, channelId, videoMenuId, existingConfig.getVideoPointName());
                    }
                }
            }

            // 校验通过
            return "0";

        } catch (Exception e) {
            log.error("校验视频配置唯一性异常（编辑），id：{}，channelId：{}，deviceId：{}，videoMenuId：{}", JSONObject.toJSONString(id), JSONObject.toJSONString(channelId), JSONObject.toJSONString(deviceId), JSONObject.toJSONString(videoMenuId), e);
            return "校验视频配置唯一性时发生异常，请联系管理员";
        }
    }

    /**
     * 根据视频菜单ID查询配置列表
     *
     * @param videoMenuId 视频菜单ID
     * @return 配置列表
     */
    @Override
    public List<VideoAccessConfig> selectVideoAccessConfigByMenuId(String videoMenuId) {
        return videoAccessConfigMapper.selectVideoAccessConfigByMenuId(videoMenuId);
    }

    /**
     * 统计视频菜单下的配置数量
     *
     * @param videoMenuId 视频菜单ID
     * @return 配置数量
     */
    @Override
    public int countVideoAccessConfigByMenuId(String videoMenuId) {
        return videoAccessConfigMapper.countVideoAccessConfigByMenuId(videoMenuId);
    }

    /**
     * 获取视频播放地址
     *
     * @param id 配置ID
     * @return 播放地址
     */
    @Override
    public String getVideoPlayUrl(String id) {
        try {
            VideoAccessConfig config = selectVideoAccessConfigById(id);
            if (config != null && StringUtils.isNotEmpty(config.getVideoUrl())) {
                log.info("获取视频播放地址成功，配置ID：{}，播放地址：{}", id, config.getVideoUrl());
                return config.getVideoUrl();
            }
            log.warn("未找到视频配置或播放地址为空，配置ID：{}", id);
            return null;
        } catch (Exception e) {
            log.error("获取视频播放地址异常，配置ID：{}", id, e);
            return null;
        }
    }

    /**
     * 根据视频菜单ID查询配置VO列表
     *
     * @param videoMenuId 视频菜单ID
     * @return 配置VO列表
     */
    @Override
    public List<VideoAccessConfigVo3> selectVideoAccessConfigVoByMenuId(String videoMenuId) {
        try {
            log.info("查询视频菜单配置VO列表，菜单ID：{}", JSONObject.toJSONString(videoMenuId));

            List<VideoAccessConfigVo3> configList = videoAccessConfigMapper.selectVideoAccessConfigVo3ByMenuId(videoMenuId);

            log.info("查询视频菜单配置VO列表成功，菜单ID：{}，数量：{}", JSONObject.toJSONString(videoMenuId), JSONObject.toJSONString(configList.size()));
            return configList;

        } catch (Exception e) {
            log.error("查询视频菜单配置VO列表异常，菜单ID：{}", JSONObject.toJSONString(videoMenuId), e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 将VideoAccessConfig转换为VideoAccessConfigVo
     *
     * @param config 配置实体
     * @return 配置VO
     */
    @Override
    public VideoAccessConfigVo3 convertToVo(VideoAccessConfig config) {
        if (config == null) {
            return null;
        }

        VideoAccessConfigVo3 vo = new VideoAccessConfigVo3();
        vo.setId(config.getId());
        vo.setVideoMenuId(config.getVideoMenuId());
        vo.setVideoMenuName(config.getVideoMenuName());
        vo.setOrderNum(config.getOrderNum());
        vo.setVideoPointName(config.getVideoPointName());
        vo.setDecodeFormat(config.getDecodeFormat());
        vo.setPlayer(config.getPlayer());
        vo.setVideoStream(config.getVideoStream());
        vo.setExtendId(config.getExtendId());
        vo.setDeviceId(config.getDeviceId());
        vo.setChannelId(config.getChannelId());
        vo.setVideoUrl(config.getVideoUrl());
        vo.setDataSource(config.getDataSource());

        return vo;
    }

    /**
     * 批量将VideoAccessConfig转换为VideoAccessConfigVo
     *
     * @param configList 配置实体列表
     * @return 配置VO列表
     */
    @Override
    public List<VideoAccessConfigVo3> convertToVoList(List<VideoAccessConfig> configList) {
        if (configList == null || configList.isEmpty()) {
            return new ArrayList<>();
        }

        List<VideoAccessConfigVo3> voList = new ArrayList<>();
        for (VideoAccessConfig config : configList) {
            VideoAccessConfigVo3 vo = convertToVo(config);
            if (vo != null) {
                voList.add(vo);
            }
        }

        return voList;
    }

    /**
     * 新增视频接入配置（通过VO）
     *
     * @param videoAccessConfigVo 视频接入配置VO
     * @return 结果
     */
    @Override
    public int insertVideoAccessConfigByVo(VideoAccessConfigVo videoAccessConfigVo) {
        try {
            log.info("新增视频接入配置（VO），点位名称：{}", videoAccessConfigVo.getVideoPointName());

            // 转换VO为实体
            videoAccessConfigVo.setId(IdUtils.simpleUUID().replace("-", ""));
            VideoAccessConfig config = convertFromVo(videoAccessConfigVo);

            // 调用原有的新增方法
            int result = insertVideoAccessConfig(config);

            log.info("新增视频接入配置（VO）{}，点位名称：{}", result > 0 ? "成功" : "失败", videoAccessConfigVo.getVideoPointName());
            return result;

        } catch (Exception e) {
            log.error("新增视频接入配置（VO）异常，点位名称：{}", videoAccessConfigVo.getVideoPointName(), e);
            throw e;
        }
    }

    /**
     * 将VideoAccessConfigVo转换为VideoAccessConfig
     *
     * @param vo 配置VO
     * @return 配置实体
     */
    @Override
    public VideoAccessConfig convertFromVo(VideoAccessConfigVo vo) {
        if (vo == null) {
            return null;
        }

        VideoAccessConfig config = new VideoAccessConfig();
        config.setId(vo.getId());
        config.setVideoMenuId(vo.getVideoMenuId());
        config.setVideoMenuName(vo.getVideoMenuName());
        config.setOrderNum(vo.getOrderNum());
        config.setVideoPointName(vo.getVideoPointName());
        config.setDecodeFormat(vo.getDecodeFormat());
        config.setPlayer(vo.getPlayer());
        config.setVideoStream(vo.getVideoStream());
        config.setExtendId(vo.getExtendId());
        config.setDeviceId(vo.getDeviceId());
        config.setChannelId(vo.getChannelId());
        config.setVideoUrl(vo.getVideoUrl());
        config.setDataSource(vo.getDataSource());

        return config;
    }

    /**
     * 修改视频接入配置（通过VO1）
     *
     * @param videoAccessConfigVo1 视频接入配置修改VO
     * @return 结果
     */
    @Override
    public int updateVideoAccessConfigByVo1(VideoAccessConfigVo1 videoAccessConfigVo1) {
        try {
            log.info("修改视频接入配置（VO1），配置ID：{}，点位名称：{}",
                    JSONObject.toJSONString(videoAccessConfigVo1.getId()), JSONObject.toJSONString(videoAccessConfigVo1.getVideoPointName()));

            // 转换VO1为实体
            VideoAccessConfig config = convertFromVo1(videoAccessConfigVo1);

            // 调用原有的修改方法
            int result = updateVideoAccessConfig(config);

            log.info("修改视频接入配置（VO1）{}，配置ID：{}，点位名称：{}",
                    result > 0 ? "成功" : "失败", JSONObject.toJSONString(videoAccessConfigVo1.getId()), JSONObject.toJSONString(videoAccessConfigVo1.getVideoPointName()));
            return result;

        } catch (Exception e) {
            log.error("修改视频接入配置（VO1）异常，配置ID：{}，点位名称：{}, 异常信息：{}",
                    JSONObject.toJSONString(videoAccessConfigVo1.getId()), JSONObject.toJSONString(videoAccessConfigVo1.getVideoPointName()), e.getMessage());
        }
        return 0;
    }

    /**
     * 将VideoAccessConfigVo1转换为VideoAccessConfig
     *
     * @param vo1 配置修改VO
     * @return 配置实体
     */
    @Override
    public VideoAccessConfig convertFromVo1(VideoAccessConfigVo1 vo1) {
        if (vo1 == null) {
            return null;
        }

        VideoAccessConfig config = new VideoAccessConfig();
        config.setId(vo1.getId());
        config.setVideoMenuId(vo1.getVideoMenuId());
        config.setVideoMenuName(vo1.getVideoMenuName());
        config.setOrderNum(vo1.getOrderNum());
        config.setVideoPointName(vo1.getVideoPointName());
        config.setDecodeFormat(vo1.getDecodeFormat());
        config.setPlayer(vo1.getPlayer());
        config.setVideoStream(vo1.getVideoStream());
        config.setExtendId(vo1.getExtendId());
        config.setDeviceId(vo1.getDeviceId());
        config.setChannelId(vo1.getChannelId());
        config.setVideoUrl(vo1.getVideoUrl());
        config.setDataSource(vo1.getDataSource());

        return config;
    }

    /**
     * 将VideoAccessConfig转换为VideoAccessConfigVo1
     *
     * @param config 配置实体
     * @return 配置修改VO
     */
    @Override
    public VideoAccessConfigVo1 convertToVo1(VideoAccessConfig config) {
        if (config == null) {
            return null;
        }

        VideoAccessConfigVo1 vo1 = new VideoAccessConfigVo1();
        vo1.setId(config.getId());
        vo1.setVideoMenuId(config.getVideoMenuId());
        vo1.setVideoMenuName(config.getVideoMenuName());
        vo1.setOrderNum(config.getOrderNum());
        vo1.setVideoPointName(config.getVideoPointName());
        vo1.setDecodeFormat(config.getDecodeFormat());
        vo1.setPlayer(config.getPlayer());
        vo1.setVideoStream(config.getVideoStream());
        vo1.setExtendId(config.getExtendId());
        vo1.setDeviceId(config.getDeviceId());
        vo1.setChannelId(config.getChannelId());
        vo1.setVideoUrl(config.getVideoUrl());
        vo1.setDataSource(config.getDataSource());

        return vo1;
    }

    /**
     * 检查配置是否存在
     *
     * @param id 配置ID
     * @return 是否存在
     */
    @Override
    public boolean checkVideoAccessConfigExists(String id) {
        try {
            VideoAccessConfig config = selectVideoAccessConfigById(id);
            return config != null;
        } catch (Exception e) {
            log.error("检查配置是否存在异常，配置ID：{}", JSONObject.toJSONString(id), e.getMessage());
            return false;
        }
    }

    /**
     * 批量删除视频接入配置（通过VO2列表）
     *
     * @param videoAccessConfigVo2List 视频接入配置删除VO列表
     * @return 删除结果信息
     */
    @Override
    public String batchDeleteVideoAccessConfigByVo2List(List<VideoAccessConfigVo2> videoAccessConfigVo2List) {
        try {
            if (videoAccessConfigVo2List == null || videoAccessConfigVo2List.isEmpty()) {
                return "删除列表为空";
            }

            log.info("批量删除视频接入配置，删除数量：{}", JSONObject.toJSONString(videoAccessConfigVo2List.size()));

            // 校验配置是否都存在
            String validateResult = validateBatchDeleteConfigs(videoAccessConfigVo2List);
            if (!"0".equals(validateResult)) {
                return validateResult;
            }

            // 提取配置ID数组
            String[] ids = extractConfigIds(videoAccessConfigVo2List);

            // 执行批量删除
            int result = deleteVideoAccessConfigByIds(ids);

            if (result > 0) {
                log.info("批量删除视频接入配置成功，删除数量：{}", JSONObject.toJSONString(result));
                return "0"; // 成功
            } else {
                log.error("批量删除视频接入配置失败");
                return "批量删除失败";
            }

        } catch (Exception e) {
            log.error("批量删除视频接入配置异常", e.getMessage());
            return "批量删除异常";
        }
    }

    /**
     * 校验批量删除的配置是否都存在
     *
     * @param videoAccessConfigVo2List 配置删除VO列表
     * @return 校验结果信息
     */
    @Override
    public String validateBatchDeleteConfigs(List<VideoAccessConfigVo2> videoAccessConfigVo2List) {
        try {
            if (videoAccessConfigVo2List == null || videoAccessConfigVo2List.isEmpty()) {
                return "删除列表为空";
            }

            // 提取ID数组
            String[] ids = extractConfigIds(videoAccessConfigVo2List);

            // 批量检查配置是否存在
            List<String> nonExistentIds = checkBatchConfigExists(ids);

            if (!nonExistentIds.isEmpty()) {
                return "以下配置不存在：" + String.join(", ", nonExistentIds);
            }

            // 检查是否可以删除
            for (VideoAccessConfigVo2 vo2 : videoAccessConfigVo2List) {
                if (!checkConfigCanDelete(vo2.getId())) {
                    return "配置 " + vo2.getId() + "(" + vo2.getVideoPointName() + ") 不能删除";
                }
            }

            return "0"; // 校验通过

        } catch (Exception e) {
            log.error("校验批量删除配置异常", e.getMessage());
            return "校验异常";
        }
    }

    /**
     * 提取配置ID数组
     *
     * @param videoAccessConfigVo2List 配置删除VO列表
     * @return 配置ID数组
     */
    @Override
    public String[] extractConfigIds(List<VideoAccessConfigVo2> videoAccessConfigVo2List) {
        if (videoAccessConfigVo2List == null || videoAccessConfigVo2List.isEmpty()) {
            return new String[0];
        }

        return videoAccessConfigVo2List.stream()
                .map(VideoAccessConfigVo2::getId)
                .filter(StringUtils::isNotEmpty)
                .toArray(String[]::new);
    }

    /**
     * 检查配置是否可以删除
     *
     * @param id 配置ID
     * @return 是否可以删除
     */
    @Override
    public boolean checkConfigCanDelete(String id) {
        try {
            // 这里可以添加业务规则检查
            // 例如：检查是否有关联的播放记录、是否正在使用等

            VideoAccessConfig config = selectVideoAccessConfigById(id);
            if (config == null) {
                return false;
            }

            // TODO: 添加具体的业务规则检查
            // 例如：
            // 1. 检查是否有正在进行的视频流
            // 2. 检查是否有关联的播放记录
            // 3. 检查是否被其他模块引用

            return true; // 暂时允许删除

        } catch (Exception e) {
            log.error("检查配置是否可以删除异常，配置ID：{}", id, e);
            return false;
        }
    }

    /**
     * 批量检查配置是否存在
     *
     * @param ids 配置ID数组
     * @return 不存在的配置ID列表
     */
    @Override
    public List<String> checkBatchConfigExists(String[] ids) {
        List<String> nonExistentIds = new ArrayList<>();

        if (ids == null || ids.length == 0) {
            return nonExistentIds;
        }

        for (String id : ids) {
            if (StringUtils.isEmpty(id)) {
                continue;
            }

            if (!checkVideoAccessConfigExists(id)) {
                nonExistentIds.add(id);
            }
        }

        return nonExistentIds;
    }

    /**
     * Excel导入视频接入配置
     *
     * @param excelList Excel数据列表
     * @param videoMenuId 视频菜单ID
     * @param videoMenuName 视频菜单名称
     * @return 导入结果信息
     */
    @Override
    public String importVideoAccessConfigFromExcel(List<AccessVideoConfigExcel> excelList, String videoMenuId, String videoMenuName) {
        try {
            if (excelList == null || excelList.isEmpty()) {
                return "导入数据为空";
            }

            log.info("Excel导入视频接入配置，数据量：{}，菜单ID：{}，菜单名称：{}",
                    excelList.size(), videoMenuId, videoMenuName);

            // 校验导入数据
            String validateResult = validateExcelImportData(excelList, videoMenuId, videoMenuName);
            if (!"0".equals(validateResult)) {
                return validateResult;
            }

            // 转换为实体列表
            List<VideoAccessConfig> configList = convertExcelListToEntityList(excelList, videoMenuId, videoMenuName);

            // 批量插入
            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            //批量入库，按照videoMenuId，deviceId，channelId三个字段为唯一标识，数据库有就更新，没有就插入，进行批量入库操作
            try {
                // 使用批量Upsert，一次数据库操作完成所有插入/更新
                int result = videoAccessConfigMapper.batchUpsertVideoAccessConfig(configList);

                if (result > 0) {
                    successCount = configList.size(); // 批量操作成功，所有记录都处理成功
                    log.info("批量Upsert成功，处理记录数：{}", result);
                } else {
                    failCount = configList.size();
                    errorMessages.append("批量Upsert失败");
                }

            } catch (Exception e) {
                failCount = configList.size();
                errorMessages.append("批量Upsert异常：").append(e.getMessage());
                log.error("批量Upsert异常：{}", e.getMessage(), e);
            }
            String result = String.format("导入完成，成功：%d条，失败：%d条", successCount, failCount);

            log.info("Excel导入视频接入配置完成，{}", result);
            return failCount == 0 ? "0" : result;

        } catch (Exception e) {
            log.error("Excel导入视频接入配置异常", e.getMessage());
            return "导入异常";
        }
    }

    /**
     * 校验Excel导入数据
     *
     * @param excelList Excel数据列表
     * @param videoMenuId 视频菜单ID
     * @param videoMenuName 视频菜单名称
     * @return 校验结果信息
     */
    @Override
    public String validateExcelImportData(List<AccessVideoConfigExcel> excelList, String videoMenuId, String videoMenuName) {
        try {
            if (excelList == null || excelList.isEmpty()) {
                return "导入数据为空";
            }

            // 校验菜单ID和名称
            if (StringUtils.isEmpty(videoMenuId)) {
                return "视频菜单ID不能为空";
            }
            if (StringUtils.isEmpty(videoMenuName)) {
                return "视频菜单名称不能为空";
            }

            // 校验每行数据
            for (int i = 0; i < excelList.size(); i++) {
                AccessVideoConfigExcel excel = excelList.get(i);

                // 检查excel对象是否为null
                if (excel == null) {
                    return String.format("第%d行：数据解析失败，请检查Excel格式", i + 1);
                }

                String rowError = validateExcelRow(excel, i + 1);
                if (!"0".equals(rowError)) {
                    return rowError;
                }
            }

            return "0"; // 校验通过

        } catch (Exception e) {
            log.error("校验Excel导入数据异常", e);
            return "校验异常：" + e.getMessage();
        }
    }

    /**
     * 校验Excel单行数据
     *
     * @param excel Excel数据
     * @param rowNum 行号
     * @return 校验结果
     */
    private String validateExcelRow(AccessVideoConfigExcel excel, int rowNum) {
        // 检查excel对象是否为null
        if (excel == null) {
            return String.format("第%d行：数据对象为空", rowNum);
        }

        // 校验必填字段
        if (excel.getOrderNum() == null) {
            return String.format("第%d行：排序不能为空", rowNum);
        }
        if (excel.getOrderNum() > 999999999) {
            return String.format("第%d行：排序不能超过999999999", rowNum);
        }
        if (StringUtils.isEmpty(excel.getVideoPointName())||excel.getVideoPointName().contains(" ")) {
            return String.format("第%d行：点位名称不能为空", rowNum);
        }
        if (excel.getVideoPointName().length() > 20) {
            return String.format("第%d行：点位名称不能超过20个字符", rowNum);
        }
        if (StringUtils.isEmpty(excel.getDeviceId())) {
            return String.format("第%d行：视频平台设备Id不能为空", rowNum);
        }
        if (excel.getDeviceId().length() > 255) {
            return String.format("第%d行：视频平台设备Id不能超过255个字符", rowNum);
        }
        if (StringUtils.isEmpty(excel.getChannelId())) {
            return String.format("第%d行：视频平台通道Id不能为空", rowNum);
        }
        if (excel.getChannelId().length() > 255) {
            return String.format("第%d行：视频平台通道Id不能超过255个字符", rowNum);
        }
        if (StringUtils.isEmpty(excel.getVideoUrl())) {
            return String.format("第%d行：视频地址不能为空", rowNum);
        }
        if (excel.getVideoUrl().length() > 150) {
            return String.format("第%d行：视频地址不能超过150个字符", rowNum);
        }
        if (StringUtils.isEmpty(excel.getDataSource())||excel.getDataSource().contains(" ")) {
            return String.format("第%d行：数据来源不能为空", rowNum);
        }
        if (excel.getDataSource().length() > 20) {
            return String.format("第%d行：数据来源不能超过20个字符", rowNum);
        }

        // 校验数据格式
        if (excel.getOrderNum() <= 0) {
            return String.format("第%d行：排序必须大于0", rowNum);
        }
        if (StringUtils.isEmpty(excel.getPlayer())) {
            return String.format("第%d行：播放器不能为空", rowNum);
        }
        if (StringUtils.isNotEmpty(excel.getPlayer()) &&
            !Arrays.asList("jessibuca", "EasyWasmPlayer").contains(excel.getPlayer())) {
            return String.format("第%d行：播放器只能是jessibuca, EasyWasmPlayer", rowNum);
        }
        if (StringUtils.isEmpty(excel.getVideoStream())) {
            return String.format("第%d行：视频流不能为空", rowNum);
        }
        if (StringUtils.isNotEmpty(excel.getVideoStream()) &&
            !Arrays.asList("flv", "ws_flv", "encode_flv", "encode_ws_flv","lan_flv").contains(excel.getVideoStream())) {
            return String.format("第%d行：视频流只能是flv, ws_flv, encode_flv, encode_ws_flv,lan_flv", rowNum);
        }

        return "0"; // 校验通过
    }

    /**
     * 将Excel数据转换为VideoAccessConfig实体
     *
     * @param excel Excel数据
     * @param videoMenuId 视频菜单ID
     * @param videoMenuName 视频菜单名称
     * @return VideoAccessConfig实体
     */
    @Override
    public VideoAccessConfig convertExcelToEntity(AccessVideoConfigExcel excel, String videoMenuId, String videoMenuName) {
        if (excel == null) {
            return null;
        }

        VideoAccessConfig config = new VideoAccessConfig();

        // 生成UUID作为主键ID（用于插入时的ID）
        config.setId(UUID.randomUUID().toString().replace("-", ""));

        config.setVideoMenuId(videoMenuId);
        config.setVideoMenuName(videoMenuName);
        config.setOrderNum(excel.getOrderNum());
        config.setVideoPointName(excel.getVideoPointName());
        config.setDecodeFormat(excel.getDecodeFormat());
        config.setPlayer(excel.getPlayer());
        config.setVideoStream(excel.getVideoStream());
        config.setDeviceId(excel.getDeviceId());
        config.setChannelId(excel.getChannelId());
        config.setVideoUrl(excel.getVideoUrl());
        config.setDataSource(excel.getDataSource());

        // 设置创建信息（用于插入时）
        config.setCreater(SecurityUtils.getUsername());
        config.setCreateTime(DateUtils.getNowDate());

        return config;
    }

    /**
     * 批量转换Excel数据为实体列表
     *
     * @param excelList Excel数据列表
     * @param videoMenuId 视频菜单ID
     * @param videoMenuName 视频菜单名称
     * @return 实体列表
     */
    @Override
    public List<VideoAccessConfig> convertExcelListToEntityList(List<AccessVideoConfigExcel> excelList, String videoMenuId, String videoMenuName) {
        if (excelList == null || excelList.isEmpty()) {
            return new ArrayList<>();
        }

        List<VideoAccessConfig> configList = new ArrayList<>();
        for (AccessVideoConfigExcel excel : excelList) {
            VideoAccessConfig config = convertExcelToEntity(excel, videoMenuId, videoMenuName);
            if (config != null) {
                configList.add(config);
            }
        }

        return configList;
    }

    /**
     * 生成Excel导入模板
     *
     * @return Excel模板数据
     */
    @Override
    public List<AccessVideoConfigExcel> generateExcelTemplate() {
        List<AccessVideoConfigExcel> templateList = new ArrayList<>();
        // 添加示例数据
        AccessVideoConfigExcel example1 = new AccessVideoConfigExcel();
        example1.setOrderNum(1);
        example1.setVideoPointName("大门监控点位");
        example1.setPlayer("jessibuca");
        example1.setVideoStream("flv");
        example1.setDeviceId("99080300001320000004");
        example1.setChannelId("99080300001310000402");
        example1.setVideoUrl("http://192.168.108.61:18082/rtp/live_99080300001320000004_99080300001310000402.flv");
        example1.setDataSource("视频云平台");
        templateList.add(example1);

        return templateList;
    }

    /**
     * 根据菜单ID获取视频配置列表（包含在线状态）
     * 需请求视频云平台接口获取设备的在线状态
     *
     * @param videoMenuId 视频菜单ID
     * @return 视频配置列表
     */
    @Override
    public List<VideoAccessConfigPo> listAllVideoConfig(String videoMenuId) {
        try {

            log.info("根据菜单ID获取视频配置列表：{}", JSONObject.toJSONString(videoMenuId));

            // 参数校验
            if (videoMenuId == null || videoMenuId.trim().isEmpty()) {
                log.warn("视频菜单ID为空，返回空列表");
                return new ArrayList<>();
            }

            // 查询视频配置列表（包含菜单名称）
            List<VideoAccessConfigPo> configList = videoAccessConfigMapper.selectVideoConfigsByMenuId(videoMenuId);

            if (configList == null || configList.isEmpty()) {
                log.info("未查询到视频配置数据：{}", JSONObject.toJSONString(videoMenuId));
                return new ArrayList<>();
            }

            // 提取所有设备ID和channelId，用于批量查询在线状态
            // 视频是通过deviceId和channelId确定唯一视频的
            List<VideoAccessConfigPo1> validConfigs = configList.stream()
                    .filter(config -> config.getDeviceId() != null && !config.getDeviceId().trim().isEmpty())
                    .map(this::convertToVideoAccessConfigPo1)
                    .collect(Collectors.toList());

            // 批量获取设备在线状态（性能优化，使用新版本接口支持deviceId+channelId）
            Map<String, Boolean> deviceOnlineStatusMap = new HashMap<>();
            if (!validConfigs.isEmpty()) {
                try {
                    deviceOnlineStatusMap = videoCloudPlatformService.batchGetDeviceOnlineStatusByConfigs(validConfigs);

                    int onlineCount = (int) deviceOnlineStatusMap.values().stream().mapToInt(status -> status ? 1 : 0).sum();
                } catch (Exception e) {
                    log.error("批量获取设备在线状态异常：{}", e.getMessage());

                    // 继续执行，设备状态默认为离线
                    deviceOnlineStatusMap = new HashMap<>();
                    for (VideoAccessConfigPo1 config : validConfigs) {
                        String uniqueKey = buildDeviceUniqueKey(config.getDeviceId(), config.getChannelId());
                        deviceOnlineStatusMap.put(uniqueKey, false);
                    }
                }
            }

            // 设置在线状态（性能优化：避免多次查询，使用deviceId+channelId唯一标识）
            int onlineConfigCount = 0;
            int offlineConfigCount = 0;

            for (VideoAccessConfigPo config : configList) {
                try {
                    String deviceId = config.getDeviceId();
                    String channelId = config.getChannelId();

                    if (deviceId != null && !deviceId.trim().isEmpty()) {
                        // 构建唯一标识，因为视频是通过deviceId和channelId确定唯一视频的
                        String uniqueKey = buildDeviceUniqueKey(deviceId, channelId);
                        Boolean isOnline = deviceOnlineStatusMap.get(uniqueKey);
                        String onlineFlag = Boolean.TRUE.equals(isOnline) ? "1" : "0";
                        config.setVideoOnlineFlag(onlineFlag);

                        // 设置视频封面字段
                        String thumbnail = videoCloudPlatformService.getVideoThumbnail(uniqueKey);
                        if (thumbnail != null && !thumbnail.trim().isEmpty()) {
                            config.setThumbnail(thumbnail);
                        }

                        if ("1".equals(onlineFlag)) {
                            onlineConfigCount++;
                        } else {
                            offlineConfigCount++;
                        }
                    } else {
                        config.setVideoOnlineFlag("0"); // 无设备ID默认离线
                        offlineConfigCount++;
                    }
                } catch (Exception e) {
                    log.error("设置配置在线状态异常：{}", e.getMessage());
                    config.setVideoOnlineFlag("0"); // 异常时默认离线
                    offlineConfigCount++;
                }
            }


            return configList;

        } catch (Exception e) {
            log.error("根据菜单ID获取视频配置列表异常：{}", e.getMessage(), e);
            throw new RuntimeException("获取视频配置列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据菜单ID获取其下的视频配置（分页）
     * 需请求视频云平台接口获取设备的在线状态
     *
     * @param videoMenuId 视频菜单ID
     * @return 视频配置列表
     */
    @Override
    public List<VideoAccessConfigPo1> listVideoMenuConfigWithPage(String videoMenuId) {
        try {

            log.info("根据菜单ID分页获取视频配置：{}", JSONObject.toJSONString(videoMenuId));

            // 参数校验和默认值设置
            if (videoMenuId == null || videoMenuId.trim().isEmpty()) {
                log.warn("视频菜单ID为空，返回空列表");
                return new ArrayList<>();
            }

            // 分页查询视频配置列表
            List<VideoAccessConfigPo1> configList = videoAccessConfigMapper
                    .selectVideoConfigsByMenuIdWithPage(videoMenuId);

            if (configList == null || configList.isEmpty()) {
                log.info("未查询到视频配置数据：{}", JSONObject.toJSONString(videoMenuId));
                return new ArrayList<>();
            }

            // 提取所有设备ID和通道Id，用于批量查询在线状态
            List<VideoAccessConfigPo1> validConfigs = configList.stream()
                    .filter(config -> config.getDeviceId() != null && !config.getDeviceId().trim().isEmpty())
                    .collect(Collectors.toList());

            log.info("准备批量查询设备在线状态：{}", JSONObject.toJSONString(configList.size()));

            // 批量获取设备在线状态（性能优化，使用新版本接口）
            Map<String, Boolean> deviceOnlineStatusMap = new HashMap<>();
            if (!validConfigs.isEmpty()) {
                try {
                    deviceOnlineStatusMap = videoCloudPlatformService.batchGetDeviceOnlineStatusByConfigs(validConfigs);

                    int onlineCount = (int) deviceOnlineStatusMap.values().stream().mapToInt(status -> status ? 1 : 0).sum();

                } catch (Exception e) {
                    log.error("批量获取设备在线状态异常：{}",e.getMessage());

                    // 继续执行，设备状态默认为离线
                    deviceOnlineStatusMap = new HashMap<>();
                    for (VideoAccessConfigPo1 config : validConfigs) {
                        deviceOnlineStatusMap.put(config.getDeviceId(), false);
                    }
                }
            }

            // 设置在线状态（性能优化：避免多次查询，使用设备ID和通道ID）
            int onlineConfigCount = 0;
            int offlineConfigCount = 0;

            for (VideoAccessConfigPo1 config : configList) {
                try {
                    String deviceId = config.getDeviceId();
                    String channelId = config.getChannelId();

                    if (deviceId != null && !deviceId.trim().isEmpty()) {
                        // 构建唯一标识，因为视频的唯一性由deviceId和channelId两个字段确定
                        String uniqueKey = buildDeviceUniqueKey(deviceId, channelId);
                        Boolean isOnline = deviceOnlineStatusMap.get(uniqueKey);
                        String onlineFlag = Boolean.TRUE.equals(isOnline) ? "1" : "0";
                        config.setVideoOnlineFlag(onlineFlag);

                        // 设置视频封面字段
                        String thumbnail = videoCloudPlatformService.getVideoThumbnail(uniqueKey);
                        if (thumbnail != null && !thumbnail.trim().isEmpty()) {
                            config.setThumbnail(thumbnail);
                        }

                        if ("1".equals(onlineFlag)) {
                            onlineConfigCount++;
                        } else {
                            offlineConfigCount++;
                        }
                    } else {
                        config.setVideoOnlineFlag("0"); // 无设备ID默认离线
                        offlineConfigCount++;
                    }
                } catch (Exception e) {
                    log.error("设置配置在线状态异常：{}", e.getMessage());
                    config.setVideoOnlineFlag("0"); // 异常时默认离线
                    offlineConfigCount++;
                }
            }

            log.info("设置配置在线状态完成：{}");

            return configList;

        } catch (Exception e) {
            log.error("根据菜单ID分页获取视频配置异常：{}", e.getMessage(), e);
            throw new RuntimeException("获取视频配置失败：" + e.getMessage());
        }
    }

    /**
     * 搜索视频配置接口
     * 根据视频点位名称搜索视频配置，返回包含菜单层级关系的结果
     *
     * @param videoPointName 视频点位名称
     * @return 搜索结果列表
     */
    @Override
    public List<VideoMenuAndConfigPo> findVideoMenuAndFirstConfig(String videoPointName) {
        try {
            // 参数校验
            if (videoPointName == null || videoPointName.trim().isEmpty()) {
                log.warn("搜索关键词为空，返回空列表");
                return new ArrayList<>();
            }

            // 安全处理：防止SQL注入和过长的搜索词
            String sanitizedPointName = sanitizeSearchKeyword(videoPointName.trim());
            if (sanitizedPointName.length() > 100) {
                sanitizedPointName = sanitizedPointName.substring(0, 100);
                log.warn("搜索关键词过长，已截取前100个字符");
            }

            // 搜索视频配置（包含菜单层级信息）
            List<VideoAccessConfigPo1> searchResults = videoAccessConfigMapper
                    .searchVideoConfigsByPointName(sanitizedPointName);

            if (searchResults == null || searchResults.isEmpty()) {
                log.info("未搜索到匹配的视频配置：{}", JSONObject.toJSONString(sanitizedPointName));
                return new ArrayList<>();
            }

            // 提取有效的设备配置信息（包含deviceId和channelId），用于批量查询在线状态
            List<VideoAccessConfigPo1> validConfigs = searchResults.stream()
                    .filter(config -> config.getDeviceId() != null && !config.getDeviceId().trim().isEmpty())
                    .collect(Collectors.toList());


            // 批量获取设备在线状态（性能优化，使用新版本接口）
            Map<String, Boolean> deviceOnlineStatusMap = new HashMap<>();
            Map<String, Boolean> uniqueKeyStatusMap = new HashMap<>(); // 扩大作用域
            if (!validConfigs.isEmpty()) {
                try {
                    // 直接使用搜索结果中的配置信息（包含完整的deviceId和channelId）
                    uniqueKeyStatusMap = videoCloudPlatformService.batchGetDeviceOnlineStatusByConfigs(validConfigs);

                    // 构建设备ID到状态的映射（兼容旧版本逻辑）
                    // 提取所有唯一的设备ID
                    Set<String> allDeviceIds = validConfigs.stream()
                            .map(VideoAccessConfigPo1::getDeviceId)
                            .collect(Collectors.toSet());

                    for (String deviceId : allDeviceIds) {
                        // 查找该设备ID对应的所有配置
                        List<VideoAccessConfigPo1> deviceConfigs = validConfigs.stream()
                                .filter(config -> deviceId.equals(config.getDeviceId()))
                                .collect(Collectors.toList());

                        // 如果任一配置在线，则设备在线
                        boolean isOnline = false;
                        for (VideoAccessConfigPo1 config : deviceConfigs) {
                            String uniqueKey = config.getDeviceId() + "|" +
                                    (config.getChannelId() != null ? config.getChannelId() : "");
                            Boolean status = uniqueKeyStatusMap.get(uniqueKey);
                            if (Boolean.TRUE.equals(status)) {
                                isOnline = true;
                                break;
                            }
                        }

                        deviceOnlineStatusMap.put(deviceId, isOnline);
                    }
                } catch (Exception e) {
                    log.error("批量获取设备在线状态异常：{}", e.getMessage());
                    // 继续执行，设备状态默认为离线
                    Set<String> allDeviceIds = validConfigs.stream()
                            .map(VideoAccessConfigPo1::getDeviceId)
                            .collect(Collectors.toSet());
                    for (String deviceId : allDeviceIds) {
                        deviceOnlineStatusMap.put(deviceId, false);
                    }
                }
            }

            // 性能优化：批量构建菜单层级名称，避免多次数据库查询
            Map<String, String> treeNameCache = batchBuildMenuTreeNames(searchResults);

            // 设置在线状态和菜单层级名称
            for (VideoAccessConfigPo1 config : searchResults) {
                try {
                    // 设置在线状态（根据deviceId和channelId两个字段唯一确定一个视频）
                    String deviceId = config.getDeviceId();
                    String channelId = config.getChannelId();
                    if (deviceId != null && !deviceId.trim().isEmpty()) {
                        // 构建唯一标识：deviceId + "|" + channelId
                        String uniqueKey = deviceId.trim() + "|" + (channelId != null ? channelId.trim() : "");

                        // 优先从uniqueKeyStatusMap获取精确的视频状态，如果没有则从deviceOnlineStatusMap获取设备状态
                        Boolean isOnline = null;
                        if (uniqueKeyStatusMap != null && uniqueKeyStatusMap.containsKey(uniqueKey)) {
                            isOnline = uniqueKeyStatusMap.get(uniqueKey);
                        } else {
                            isOnline = deviceOnlineStatusMap.get(deviceId);
                        }

                        config.setVideoOnlineFlag(Boolean.TRUE.equals(isOnline) ? "1" : "0");
                        // 设置视频封面字段
                        String thumbnail = videoCloudPlatformService.getVideoThumbnail(uniqueKey);
                        if (thumbnail != null && !thumbnail.trim().isEmpty()) {
                            config.setThumbnail(thumbnail);
                        }

                    } else {
                        config.setVideoOnlineFlag("0"); // 无设备ID默认离线
                        log.debug("设备ID为空，设置为离线状态");
                    }

                    // 从缓存中获取菜单层级名称（性能优化：避免重复查询）
                    String treeId = config.getVideoMenuId()+"," + config.getTreeId();
                    String parentTreeName = treeNameCache.getOrDefault(treeId, "");
                    config.setParentTreeName(parentTreeName);

                } catch (Exception e) {
                    log.error("设置配置在线状态和层级名称异常：{}", e.getMessage());
                    config.setVideoOnlineFlag("0"); // 异常时默认离线
                    config.setParentTreeName(""); // 异常时设置空层级名称
                }
            }

            // 按菜单层级关系分组（性能优化：使用Map进行分组）
            Map<String, List<VideoAccessConfigPo1>> groupedResults = searchResults.stream()
                    .collect(Collectors.groupingBy(
                            config -> config.getTreeId() != null ? config.getVideoMenuId()+","+config.getTreeId() : "unknown",
                            LinkedHashMap::new,
                            Collectors.toList()
                    ));

            // 构建返回结果，遍历groupedResults，这个map的key就是返回结果的treeId
            List<VideoMenuAndConfigPo> resultList = new ArrayList<>();
            for (Map.Entry<String, List<VideoAccessConfigPo1>> entry : groupedResults.entrySet()) {
                VideoMenuAndConfigPo menuAndConfig = new VideoMenuAndConfigPo();

                // 根据注释：这个map的key就是返回结果的treeId
                String treeIdFromKey = entry.getKey();
                List<VideoAccessConfigPo1> configs = entry.getValue();

                // 直接使用groupedResults的key作为treeId
                menuAndConfig.setTreeId(treeIdFromKey);

                // 设置parentTreeName
                menuAndConfig.setParentTreeName(treeNameCache.getOrDefault(treeIdFromKey, ""));

                menuAndConfig.setVideoAccessConfigPo1List(configs);
                resultList.add(menuAndConfig);
            }

            return resultList;

        } catch (Exception e) {
            log.error("搜索视频配置异常：{}", e.getMessage());
            throw new RuntimeException("搜索视频配置失败：" + e.getMessage(), e);
        }
    }

    /**
     * 安全处理搜索关键词，防止SQL注入和特殊字符攻击
     *
     * @param keyword 原始搜索关键词
     * @return 安全处理后的关键词
     */
    private String sanitizeSearchKeyword(String keyword) {
        if (keyword == null || keyword.isEmpty()) {
            return "";
        }

        // 移除潜在的SQL注入字符
        String sanitized = keyword
                .replaceAll("['\";\\\\]", "") // 移除引号、分号、反斜杠
                .replaceAll("--", "")         // 移除SQL注释
                .replaceAll("/\\*.*?\\*/", "") // 移除SQL块注释
                .replaceAll("(?i)(union|select|insert|update|delete|drop|create|alter|exec|execute)", ""); // 移除SQL关键词

        // 只保留字母、数字、中文、常见标点符号
        //保留%号
        sanitized = sanitized.replaceAll("[^\\w\\u4e00-\\u9fa5\\s\\-_\\.\\(\\)\\[\\]（）【】%]", "");

        return sanitized.trim();
    }

    /**
     * 批量构建菜单层级名称（性能优化：避免多次数据库查询）
     *
     * @param searchResults 搜索结果列表
     * @return tree_id -> 层级名称的映射
     */
    private Map<String, String> batchBuildMenuTreeNames(List<VideoAccessConfigPo1> searchResults) {
        Map<String, String> treeNameCache = new HashMap<>();

        if (searchResults == null || searchResults.isEmpty()) {
            return treeNameCache;
        }

        try {
            // 1. 收集所有唯一的tree_id，将searchResults中的菜单Id拼接到tree_id前面再进行收集所有唯一的tree_id
            Set<String> uniqueTreeIds = searchResults.stream()
                    .filter(config -> config.getTreeId() != null && !config.getTreeId().trim().isEmpty()
                            && config.getVideoMenuId() != null && !config.getVideoMenuId().trim().isEmpty())
                    .map(config -> {
                        // 将菜单ID拼接到tree_id前面
                        String menuId = config.getVideoMenuId().trim();
                        String treeId = config.getTreeId().trim();
                        return menuId + "," + treeId;
                    })
                    .collect(Collectors.toSet());

            if (uniqueTreeIds.isEmpty()) {
                return treeNameCache;
            }

            // 2. 解析所有tree_id，收集所有需要查询的菜单ID
            Set<String> allMenuIds = new HashSet<>();
            for (String treeId : uniqueTreeIds) {
                String[] menuIds = treeId.split(",");
                for (String menuId : menuIds) {
                    String trimmedId = menuId.trim();
                    if (!trimmedId.isEmpty()) {
                        allMenuIds.add(trimmedId);
                    }
                }
            }

            // 3. 一次性批量查询所有菜单名称（性能优化：只查询一次数据库）
            Map<String, String> allMenuNameMap = new HashMap<>();
            if (!allMenuIds.isEmpty()) {
                allMenuNameMap = getMenuNamesByIds(new ArrayList<>(allMenuIds));
            }

            // 4. 为每个tree_id构建层级名称
            for (String treeId : uniqueTreeIds) {
                String treeName = buildTreeNameFromCache(treeId, allMenuNameMap);
                treeNameCache.put(treeId, treeName);
            }

            log.info("批量构建菜单层级名称完成：{}", JSONObject.toJSONString(treeNameCache));

        } catch (Exception e) {
            log.error("批量构建菜单层级名称异常", e.getMessage());
        }

        return treeNameCache;
    }

    /**
     * 从缓存中构建tree_id的层级名称
     *
     * @param treeId 菜单树ID
     * @param menuNameCache 菜单名称缓存
     * @return 层级名称
     */
    private String buildTreeNameFromCache(String treeId, Map<String, String> menuNameCache) {
        if (treeId == null || treeId.trim().isEmpty()) {
            return "";
        }

        try {
            String[] menuIds = treeId.split(",");
            List<String> menuNames = new ArrayList<>();

            for (String menuId : menuIds) {
                String trimmedId = menuId.trim();
                if (!trimmedId.isEmpty()) {
                    String menuName = menuNameCache.get(trimmedId);
                    if (menuName != null && !menuName.trim().isEmpty()) {
                        menuNames.add(menuName);
                    }
                }
            }
            //将menuNames的顺序反转
            Collections.reverse(menuNames);
            return String.join(" > ", menuNames);

        } catch (Exception e) {
            log.error("从缓存构建tree_id层级名称异常：{}", e.getMessage());
            return "";
        }
    }

    /**
     * 根据tree_id构建菜单层级名称（保留原方法，用于其他地方调用）
     * tree_id格式：父级ID逗号分隔，如 "1,2,3" 表示 一级菜单ID=1, 二级菜单ID=2, 三级菜单ID=3
     *
     * @param treeId 菜单树ID
     * @return 菜单层级名称，如 "一级菜单 > 二级菜单 > 三级菜单"
     */
    private String buildMenuTreeName(String treeId) {
        if (treeId == null || treeId.trim().isEmpty()) {
            return "";
        }

        try {
            // 解析tree_id中的菜单ID列表
            String[] menuIds = treeId.split(",");
            if (menuIds.length == 0) {
                return "";
            }

            // 批量查询菜单名称（性能优化）
            List<String> menuIdList = Arrays.stream(menuIds)
                    .map(String::trim)
                    .filter(id -> !id.isEmpty())
                    .collect(Collectors.toList());

            if (menuIdList.isEmpty()) {
                return "";
            }

            // 查询菜单名称
            Map<String, String> menuNameMap = getMenuNamesByIds(menuIdList);

            // 按顺序构建层级名称
            List<String> menuNames = new ArrayList<>();
            for (String menuId : menuIdList) {
                String menuName = menuNameMap.get(menuId);
                if (menuName != null && !menuName.trim().isEmpty()) {
                    menuNames.add(menuName);
                }
            }

            // 用 " > " 连接菜单名称
            return String.join(" > ", menuNames);

        } catch (Exception e) {
            log.error("构建菜单层级名称异常：{}", e.getMessage());
            return "";
        }
    }

    /**
     * 批量获取菜单名称（性能优化）
     *
     * @param menuIds 菜单ID列表
     * @return 菜单ID -> 菜单名称的映射
     */
    private Map<String, String> getMenuNamesByIds(List<String> menuIds) {
        if (menuIds == null || menuIds.isEmpty()) {
            return new HashMap<>();
        }

        try {
            // 这里需要调用VideoMenuMapper的批量查询方法
            // 由于我们已经有了selectVideoMenusByIds方法，可以复用
            Map<String, VideoMenuVo1> menuMap = videoMenuService.selectVideoMenusByIds(menuIds);

            // 转换为ID -> 名称的映射
            Map<String, String> nameMap = new HashMap<>();
            for (Map.Entry<String, VideoMenuVo1> entry : menuMap.entrySet()) {
                VideoMenuVo1 menu = entry.getValue();
                if (menu != null && menu.getVideoMenuName() != null) {
                    nameMap.put(entry.getKey(), menu.getVideoMenuName());
                }
            }

            return nameMap;

        } catch (Exception e) {
            log.error("批量获取菜单名称异常：{}", e.getMessage());
            return new HashMap<>();
        }
    }

    /**
     * 构建设备唯一标识
     * 根据deviceId和channelId构建唯一标识，因为视频的唯一性由这两个字段确定
     *
     * @param deviceId 设备ID
     * @param channelId 通道ID
     * @return 设备唯一标识
     */
    private String buildDeviceUniqueKey(String deviceId, String channelId) {
        if (deviceId == null) {
            return "";
        }

        // 使用分隔符连接deviceId和channelId，确保唯一性
        String safeChannelId = channelId != null ? channelId : "";
        return deviceId + "|" + safeChannelId;
    }

    /**
     * 将VideoAccessConfigPo转换为VideoAccessConfigPo1
     * 用于新版本接口的调用
     *
     * @param config VideoAccessConfigPo对象
     * @return VideoAccessConfigPo1对象
     */
    private VideoAccessConfigPo1 convertToVideoAccessConfigPo1(VideoAccessConfigPo config) {
        if (config == null) {
            return null;
        }

        VideoAccessConfigPo1 config1 = new VideoAccessConfigPo1();
        config1.setId(config.getId());
        config1.setVideoMenuId(config.getVideoMenuId());
        config1.setOrderNum(config.getOrderNum());
        config1.setVideoPointName(config.getVideoPointName());
        config1.setDecodeFormat(config.getDecodeFormat());
        config1.setPlayer(config.getPlayer());
        config1.setVideoStream(config.getVideoStream());
        config1.setExtendId(config.getExtendId());
        config1.setDeviceId(config.getDeviceId());
        config1.setChannelId(config.getChannelId());
        config1.setVideoOnlineFlag(config.getVideoOnlineFlag());
        config1.setVideoUrl(config.getVideoUrl());
        config1.setDataSource(config.getDataSource());
        config1.setThumbnail(config.getThumbnail());

        return config1;
    }

}
