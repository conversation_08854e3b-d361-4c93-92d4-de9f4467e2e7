package com.onecity.os.video.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.onecity.os.common.core.utils.BeanHelper;
import com.onecity.os.common.core.utils.DateUtils;
import com.onecity.os.common.core.web.page.TableDataInfo;
import com.onecity.os.video.domain.VideoMenu;
import com.onecity.os.video.domain.dto.MenuDeviceDto;
import com.onecity.os.video.domain.po.VideoAccessConfigPo1;
import com.onecity.os.video.domain.po.VideoMenuAndConfig;
import com.onecity.os.video.domain.po.VideoMenuPo;
import com.onecity.os.video.domain.po.VideoMenuPo1;
import com.onecity.os.video.domain.po.VideoMenuPo2;
import com.onecity.os.video.domain.vo.VideoMenuAndConfigVo4;
import com.onecity.os.video.domain.vo.VideoMenuVo;
import com.onecity.os.video.domain.vo.VideoMenuVo1;
import com.onecity.os.video.mapper.VideoAccessConfigMapper;
import com.onecity.os.video.mapper.VideoMenuMapper;
import com.onecity.os.video.service.IVideoCloudPlatformService;
import com.onecity.os.video.service.IVideoMenuService;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.UUID;

/**
 * 视频菜单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Slf4j
@Service
public class VideoMenuServiceImpl implements IVideoMenuService {
    @Autowired
    private VideoMenuMapper videoMenuMapper;

    @Autowired
    private IVideoCloudPlatformService videoCloudPlatformService;
    @Autowired
    private VideoAccessConfigMapper videoAccessConfigMapper;

    /**
     * 查询视频菜单
     *
     * @param menuId 视频菜单主键
     * @return 视频菜单
     */
    @Override
    public VideoMenuVo1 selectVideoMenuByMenuId(String menuId) {
        return videoMenuMapper.selectVideoMenuByMenuId(menuId);
    }


    /**
     * 新增视频菜单
     *
     * @param videoMenuVo 视频菜单VO
     * @return 结果
     */
    @Override
    public int insertVideoMenu(VideoMenuVo videoMenuVo) {
        VideoMenu videoMenu = new VideoMenu();
        BeanUtils.copyProperties(videoMenuVo, videoMenu);
        
        // 设置默认值
        videoMenu.setIsDelete("0"); // 未删除
        
        // 设置创建信息
        videoMenu.setVideoMenuId(UUID.randomUUID().toString().replace("-", ""));

        // 安全获取用户信息
        try {
            String userName = SecurityUtils.getLoginUser().getSysUser().getNickName();
            videoMenu.setCreater(userName);
            videoMenu.setUpdater(userName);
        } catch (Exception e) {
            log.warn("获取当前用户信息失败，使用默认用户", e.getMessage());
            videoMenu.setCreater("system");
            videoMenu.setUpdater("system");
        }

        videoMenu.setCreateTime(DateUtils.getNowDate());
        videoMenu.setUpdateTime(DateUtils.getNowDate());
        videoMenu.setTreeId(getParentMenuIds(videoMenu.getParentId()));

        // 添加详细日志用于调试，使用JSONObject.toJSONString防止源码漏洞
        log.info("准备插入视频菜单，参数：{}", JSONObject.toJSONString(videoMenu));

        try {
            int result = videoMenuMapper.insertVideoMenu(videoMenu);
            log.info("视频菜单插入结果：{}", JSONObject.toJSONString(result));
            return result;
        } catch (Exception e) {
            log.error("插入视频菜单异常：{}", e.getMessage(), e);
        }
        return 0;
    }

    @Override
    public int selectVideoMenuCount(String parentId) {
        return videoMenuMapper.selectVideoMenuCount(parentId);
    }

    /**
     * 修改视频菜单
     *
     * @param videoMenuVo1 视频菜单
     * @return 结果
     */
    @Override
    public int updateVideoMenu(VideoMenuVo1 videoMenuVo1) {
        VideoMenu videoMenu = BeanHelper.copyProperties(videoMenuVo1, VideoMenu.class);
        //递归组装其父级关系，逗号分隔父级Id，存入treeId字段
        videoMenu.setTreeId(getParentMenuIds(videoMenu.getParentId()));
        videoMenu.setUpdater(SecurityUtils.getLoginUser().getSysUser().getNickName());
        videoMenu.setUpdateTime(DateUtils.getNowDate());
        return videoMenuMapper.updateVideoMenu(videoMenu);
    }

    /**
     * 递归获取一个菜单的所有父级菜单Id，逗号分隔parentId为0表示根节点
     * @param parentId 父级菜单Id     0 表示根节点
     */
    public String getParentMenuIds(String parentId) {
        try {
            // 如果是根节点，直接返回
            if ("0".equals(parentId) || parentId == null) {
                return "0";
            }

            String parentMenuIds = parentId;
            VideoMenuVo1 videoMenu = videoMenuMapper.selectVideoMenuByMenuId(parentId);
            if (videoMenu != null) {
//                parentMenuIds = parentMenuIds + "," + videoMenu.getParentId();
                if (!"0".equals(videoMenu.getParentId())) {
                    parentMenuIds = parentMenuIds + "," + getParentMenuIds(videoMenu.getParentId());
                }
            }
            return parentMenuIds;
        } catch (Exception e) {
            log.error("获取父级菜单ID异常，menuId：{}", JSONObject.toJSONString(parentId), e);
            // 异常时返回当前菜单ID，避免影响插入操作
            return parentId != null ? parentId : "0";
        }
    }


    /**
     * 批量删除视频菜单（物理删除）
     * 注意：物理删除会永久删除数据，无法恢复
     *
     * @param menuIds 需要删除的视频菜单主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteVideoMenuByMenuIds(List<String> menuIds) {
        try {
            // 参数校验
            if (menuIds == null || menuIds.isEmpty()) {
                log.warn("删除菜单ID列表为空，无需删除");
                return 0;
            }

            // 递归获取菜单所有子节点Id（使用更高效的递归查询）
            List<String> allMenuIdsToDelete = new ArrayList<>(menuIds);

            // 为每个要删除的菜单递归查找所有子菜单
            for (String menuId : menuIds) {
                try {
                    List<String> childMenuIds = getChildMenuIdsRecursive(menuId);
                    allMenuIdsToDelete.addAll(childMenuIds);
                } catch (Exception e) {
                    log.error("获取子菜单异常，菜单ID：{}", JSONObject.toJSONString(menuId), e);
                    // 继续处理其他菜单，不因为单个菜单的子菜单查询失败而中断整个删除操作
                }
            }

            // 去重
            allMenuIdsToDelete = allMenuIdsToDelete.stream().distinct().collect(Collectors.toList());

            // 物理删除：先删除菜单下的所有视频配置，再删除菜单（保证数据完整性）
            int configDeleteCount = 0;
            try {
                configDeleteCount = videoAccessConfigMapper.deleteVideoAccessConfigByMenuIds(allMenuIdsToDelete);

            } catch (Exception e) {
                log.error("删除视频配置异常，菜单ID列表：{}", JSONObject.toJSONString(allMenuIdsToDelete), e.getMessage());
                // 物理删除时，如果配置删除失败，不能继续删除菜单，否则会造成数据不一致
                throw new RuntimeException("删除视频配置失败，无法继续删除菜单：" + e.getMessage());
            }

            // 物理删除菜单
            int result = videoMenuMapper.deleteVideoMenuByMenuIds(allMenuIdsToDelete);


            return result;

        } catch (Exception e) {
            log.error("批量删除视频菜单异常：{}", JSONObject.toJSONString(menuIds), e.getMessage());
            throw new RuntimeException("删除视频菜单失败：" + e.getMessage(), e);
        }
    }


    /**
     * 查询视频菜单树列表（异步加载）
     *
     * @param parentId 父菜单ID（可选，为空时查询根节点）
     * @return 视频菜单PO列表
     */
    @Override
    public List<VideoMenuPo> listVideoMenu(String parentId) {
        try {
            log.info("查询视频菜单树列表，父菜单ID：{}", parentId);

            // 如果parentId为空，查询根节点（parentId = "0"）
            if (StringUtils.isEmpty(parentId)) {
                parentId = "0";
            }

            List<VideoMenuPo> result = videoMenuMapper.selectVideoMenuTreeList(parentId);

            log.info("查询到视频菜单数量：{}", JSONObject.toJSONString(result != null ? result.size() : 0));
            return result != null ? result : new ArrayList<>();

        } catch (Exception e) {
            log.error("查询视频菜单树列表异常，父菜单ID：{}", JSONObject.toJSONString(parentId), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询视频菜单列表（包含视频数量和在线状态统计）
     *
     * @param parentId 父菜单ID（可选，为空时查询根节点）
     * @return 视频菜单统计PO列表
     */
    @Override
    public List<VideoMenuPo1> listVideoMenuNumAndStatus(String parentId) {
        try {
            log.info("查询视频菜单列表（包含统计），父菜单ID：{}", JSONObject.toJSONString(parentId));

            // 1. 查询菜单列表
            List<VideoMenuPo1> menuList = videoMenuMapper.selectVideoMenuWithVideoCount(parentId);
            if (menuList == null || menuList.isEmpty()) {
                log.info("未查询到视频菜单");
                return new ArrayList<>();
            }

            // 2. 批量获取所有菜单的统计信息（性能优化关键）
            List<String> menuIds = menuList.stream()
                    .map(VideoMenuPo1::getVideoMenuId)
                    .collect(Collectors.toList());

            // 2.1 批量获取视频总数（一次查询获取所有菜单的递归统计）
            Map<String, Integer> videoCountMap = batchGetVideoCountRecursive(menuIds);

            // 2.2 批量获取所有设备ID（一次查询获取所有菜单的设备）
            Map<String, List<String>> menuDeviceMap = batchGetDeviceIdsByMenuIds(menuIds);

            // 2.3 性能优化：批量获取所有菜单的设备配置，并按菜单分组
            Map<String, List<MenuDeviceDto>> menuDeviceConfigMap = batchGetDeviceConfigsByMenuIds(menuIds);


            // 性能优化：批量获取所有设备的在线状态
            Map<String, Boolean> uniqueKeyStatusMap = batchGetDeviceOnlineStatus(menuDeviceConfigMap);

            // 3. 为每个菜单设置统计结果
            for (VideoMenuPo1 menu : menuList) {
                try {
                    // 设置视频总数
                    menu.setVideoTotalNum(videoCountMap.getOrDefault(menu.getVideoMenuId(), 0));

                    // 性能优化：直接从预计算的映射中获取在线数
                    int onlineVideoCount = calculateOnlineVideoCountFromMap(menu.getVideoMenuId(), menuDeviceConfigMap, uniqueKeyStatusMap);
                    menu.setVideoOnlineNum(onlineVideoCount);

                } catch (Exception e) {
                    log.error("设置菜单统计信息异常，菜单名称：{}", JSONObject.toJSONString(menu.getVideoMenuName()), e);
                    menu.setVideoTotalNum(0);
                    menu.setVideoOnlineNum(0);
                }
            }

            return menuList;

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("查询视频菜单列表异常：{}", JSONObject.toJSONString(parentId), e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 批量获取视频总数（递归统计）
     * 性能优化：使用批量SQL查询，大幅减少数据库交互次数
     *
     * @param menuIds 菜单ID列表
     * @return 菜单ID -> 视频总数的映射
     */
    private Map<String, Integer> batchGetVideoCountRecursive(List<String> menuIds) {
        Map<String, Integer> resultMap = new HashMap<>();

        if (menuIds == null || menuIds.isEmpty()) {
            return resultMap;
        }

        try {
            // 使用批量SQL查询，一次获取所有菜单的视频总数
            Map<String, Map<String, Object>> batchResults = videoMenuMapper.batchSelectVideoCountRecursive(menuIds);

            for (Map.Entry<String, Map<String, Object>> entry : batchResults.entrySet()) {
                String menuId = entry.getKey();
                Map<String, Object> result = entry.getValue();
                Object countObj = result.get("video_count");
                Integer count = 0;

                if (countObj instanceof Number) {
                    count = ((Number) countObj).intValue();
                }

                resultMap.put(menuId, count);
            }

            // 确保所有菜单都有结果，没有查询到的设为0
            for (String menuId : menuIds) {
                resultMap.putIfAbsent(menuId, 0);
            }

        } catch (Exception e) {
            log.error("批量获取视频总数异常", e);
            // 异常时为所有菜单设置默认值
            for (String menuId : menuIds) {
                resultMap.put(menuId, 0);
            }
        }

        return resultMap;
    }

    /**
     * 批量获取设备ID
     * 性能优化：使用批量SQL查询，大幅减少数据库交互次数
     *
     * @param menuIds 菜单ID列表
     * @return 菜单ID -> 设备ID列表的映射
     */
    private Map<String, List<String>> batchGetDeviceIdsByMenuIds(List<String> menuIds) {
        Map<String, List<String>> resultMap = new HashMap<>();

        if (menuIds == null || menuIds.isEmpty()) {
            return resultMap;
        }

        // 初始化所有菜单的设备列表
        for (String menuId : menuIds) {
            resultMap.put(menuId, new ArrayList<>());
        }

        try {
            // 使用批量SQL查询，一次获取所有菜单的设备ID
            List<MenuDeviceDto> batchResults = videoMenuMapper.batchSelectDeviceIdsByMenuIds(menuIds);

            for (MenuDeviceDto result : batchResults) {
                String menuId = result.getMenuId();
                String deviceId = result.getDeviceId();

                if (menuId != null && deviceId != null) {
                    resultMap.computeIfAbsent(menuId, k -> new ArrayList<>()).add(deviceId);
                }
            }

        } catch (Exception e) {
            log.error("批量获取设备ID列表异常", e);
            // 异常时保持空列表
        }

        return resultMap;
    }

    /**
     * 构建菜单Id-设备Id的映射关系
     */
    private Map<String, List<String>> batchGetDeviceIdsByMenuIds1(List<String> menuIds,List<VideoAccessConfigPo1> configList) {
        Map<String, List<String>> resultMap = new HashMap<>();

        if (menuIds == null || menuIds.isEmpty()) {
            return resultMap;
        }

        // 初始化所有菜单的设备列表
        for (String menuId : menuIds) {
            resultMap.put(menuId, new ArrayList<>());
        }

        try {
            // 一次转换所有菜单的设备ID
            List<MenuDeviceDto> batchResults = new ArrayList<>();
            for (VideoAccessConfigPo1 config : configList) {
                MenuDeviceDto dto = new MenuDeviceDto();
                dto.setMenuId(config.getVideoMenuId());
                dto.setDeviceId(config.getDeviceId());
                dto.setChannelId(config.getChannelId());
                batchResults.add(dto);
            }

            for (MenuDeviceDto result : batchResults) {
                String menuId = result.getMenuId();
                String deviceId = result.getDeviceId();

                if (menuId != null && deviceId != null) {
                    resultMap.computeIfAbsent(menuId, k -> new ArrayList<>()).add(deviceId);
                }
            }

        } catch (Exception e) {
            log.error("构建菜单Id-设备Id的映射关系异常", e);
            // 异常时保持空列表
        }

        return resultMap;
    }
    /**
     * 性能优化：批量获取所有菜单的设备配置，并按菜单分组
     * 一次查询获取所有菜单的设备配置，避免N次数据库查询
     *
     * @param menuIds 菜单ID列表
     * @return 菜单ID -> 设备配置列表的映射
     */
    private Map<String, List<MenuDeviceDto>> batchGetDeviceConfigsByMenuIds(List<String> menuIds) {
        Map<String, List<MenuDeviceDto>> resultMap = new HashMap<>();

        if (menuIds == null || menuIds.isEmpty()) {
            return resultMap;
        }

        try {
            // 批量查询所有菜单的设备配置
            List<MenuDeviceDto> allConfigs = videoMenuMapper.batchSelectDeviceIdsByMenuIds(menuIds);

            // 按菜单ID分组
            for (MenuDeviceDto config : allConfigs) {
                String menuId = config.getMenuId();
                resultMap.computeIfAbsent(menuId, k -> new ArrayList<>()).add(config);
            }

            // 确保所有菜单都有对应的列表（即使为空）
            for (String menuId : menuIds) {
                resultMap.computeIfAbsent(menuId, k -> new ArrayList<>());
            }

            log.debug("批量获取设备配置完成，菜单数：{}，总配置数：{}", JSONObject.toJSONString(menuIds.size()), JSONObject.toJSONString(allConfigs.size()));

        } catch (Exception e) {
            log.error("批量获取设备配置异常，菜单IDs：{}", JSONObject.toJSONString(menuIds), e);
            // 异常时确保所有菜单都有空列表
            for (String menuId : menuIds) {
                resultMap.put(menuId, new ArrayList<>());
            }
        }

        return resultMap;
    }

    /**
     * 性能优化：批量获取所有设备的在线状态
     *
     * @param menuDeviceConfigMap 菜单设备配置映射
     * @return 设备唯一键 -> 在线状态的映射
     */
    private Map<String, Boolean> batchGetDeviceOnlineStatus(Map<String, List<MenuDeviceDto>> menuDeviceConfigMap) {
        Map<String, Boolean> uniqueKeyStatusMap = new HashMap<>();

        try {
            // 收集所有唯一的设备配置
            Set<String> uniqueDeviceConfigs = new HashSet<>();
            List<VideoAccessConfigPo1> deviceConfigs = new ArrayList<>();

            for (List<MenuDeviceDto> configs : menuDeviceConfigMap.values()) {
                for (MenuDeviceDto dto : configs) {
                    String uniqueKey = dto.getDeviceId() + "|" + dto.getChannelId();
                    if (uniqueDeviceConfigs.add(uniqueKey)) {
                        VideoAccessConfigPo1 config = new VideoAccessConfigPo1();
                        config.setDeviceId(dto.getDeviceId());
                        config.setChannelId(dto.getChannelId());
                        deviceConfigs.add(config);
                    }
                }
            }

            // 批量查询设备在线状态
            if (!deviceConfigs.isEmpty()) {
                uniqueKeyStatusMap = videoCloudPlatformService.batchGetDeviceOnlineStatusByConfigs(deviceConfigs);
                log.debug("批量获取设备在线状态完成，设备数：{}", JSONObject.toJSONString(deviceConfigs.size()));
            }

        } catch (Exception e) {
            log.error("批量获取设备在线状态异常", e);
        }

        return uniqueKeyStatusMap;
    }

    /**
     * 性能优化：从预计算的映射中计算在线视频配置数量
     * 关键修复：正确处理相同设备在不同子菜单下的重复配置，每个配置记录都独立统计
     *
     * 业务逻辑说明：
     * 1. 相同的设备(deviceId+channelId)可能在不同子菜单下有多个配置记录
     * 2. 每个配置记录都应该独立统计，即使设备相同
     * 3. 如果设备在线，那么所有使用该设备的配置记录都应该被统计为在线
     *
     * @param menuId 菜单ID
     * @param menuDeviceConfigMap 菜单设备配置映射（已经是递归查询的结果，包含所有配置记录）
     * @param uniqueKeyStatusMap 设备状态映射
     * @return 在线视频配置数量（按配置记录数统计，不是按设备数统计）
     */
    private int calculateOnlineVideoCountFromMap(String menuId, Map<String, List<MenuDeviceDto>> menuDeviceConfigMap, Map<String, Boolean> uniqueKeyStatusMap) {
        int onlineCount = 0;

        try {
            // 关键修复：直接使用menuDeviceConfigMap中的数据
            // menuDeviceConfigMap包含了该菜单及所有子菜单的配置记录（递归查询结果）
            // 注意：相同设备可能在不同子菜单下有多个配置记录，这是正确的业务逻辑
            List<MenuDeviceDto> menuDeviceConfigs = menuDeviceConfigMap.get(menuId);
            if (menuDeviceConfigs != null) {
                for (MenuDeviceDto dto : menuDeviceConfigs) {
                    String uniqueKey = dto.getDeviceId() + "|" + dto.getChannelId();
                    Boolean status = uniqueKeyStatusMap.get(uniqueKey);
                    // 每个配置记录都独立统计：如果设备在线，该配置记录就算在线
                    // 即使相同设备在多个子菜单下配置，每个配置都会被统计
                    if (Boolean.TRUE.equals(status)) {
                        onlineCount++;
                    }
                }
            }

            log.debug("菜单{}的在线视频配置数量：{}，总配置数量：{}", menuId, onlineCount,
                     menuDeviceConfigs != null ? menuDeviceConfigs.size() : 0);

        } catch (Exception e) {
            log.error("计算菜单{}在线视频配置数量异常", JSONObject.toJSONString(menuId), e);
        }

        return onlineCount;
    }



    /**
     * 判断指定菜单是否为目标菜单或其子菜单
     *
     * @param checkMenuId 要检查的菜单ID
     * @param targetMenuId 目标菜单ID
     * @return 是否为目标菜单或其子菜单
     */
    private boolean isMenuOrSubMenu(String checkMenuId, String targetMenuId) {
        if (checkMenuId == null || targetMenuId == null) {
            return false;
        }

        // 如果是目标菜单本身
        if (targetMenuId.equals(checkMenuId)) {
            return true;
        }

        try {
            // 查询checkMenuId的父级路径，看是否包含targetMenuId
            Set<String> visitedMenus = new HashSet<>();
            return isSubMenuRecursive(checkMenuId, targetMenuId, visitedMenus);
        } catch (Exception e) {
            log.error("判断菜单层级关系异常，checkMenuId：{}，targetMenuId：{}", JSONObject.toJSONString(checkMenuId), JSONObject.toJSONString(targetMenuId), e);
            return false;
        }
    }

    /**
     * 递归判断是否为子菜单
     *
     * @param checkMenuId 要检查的菜单ID
     * @param targetMenuId 目标菜单ID
     * @param visitedMenus 已访问的菜单ID集合（防止循环引用）
     * @return 是否为子菜单
     */
    private boolean isSubMenuRecursive(String checkMenuId, String targetMenuId, Set<String> visitedMenus) {
        // 防止循环引用：如果当前菜单已经访问过，说明存在循环
        if (visitedMenus.contains(checkMenuId)) {
            log.warn("检测到菜单循环引用，停止递归：checkMenuId={}，visitedPath={}", JSONObject.toJSONString(checkMenuId), visitedMenus);
            return false;
        }

        // 将当前菜单加入已访问集合
        visitedMenus.add(checkMenuId);

        try {
            // 查询checkMenuId的父菜单
            VideoMenuVo1 menu = videoMenuMapper.selectVideoMenuByMenuId(checkMenuId);
            if (menu == null || menu.getParentId() == null || "0".equals(menu.getParentId())) {
                return false;
            }

            // 如果父菜单就是目标菜单
            if (targetMenuId.equals(menu.getParentId())) {
                return true;
            }

            // 递归检查父菜单
            return isSubMenuRecursive(menu.getParentId(), targetMenuId, visitedMenus);

        } catch (Exception e) {
            log.error("递归判断子菜单异常，checkMenuId：{}，targetMenuId：{}，visitedPath：{}", JSONObject.toJSONString(checkMenuId), JSONObject.toJSONString(targetMenuId), visitedMenus, e);
            return false;
        } finally {
            // 回溯时移除当前菜单（允许其他路径访问）
            visitedMenus.remove(checkMenuId);
        }
    }

    /**
     * 根据视频点位名称查询视频菜单（显示父级及父级的父级）
     * 需请求视频云平台接口获取设备的在线状态
     *
     * @param videoPointName 视频点位名称
     * @return 视频菜单查询结果列表
     */
    @Override
    public VideoMenuPo2 findVideoMenuNumAndStatusByName(String videoPointName) {
        long startTime = System.currentTimeMillis();

        try {
            // 安全性：输入参数验证和清理
            if (videoPointName == null || videoPointName.trim().isEmpty()) {
                log.warn("视频点位名称为空，返回空结果");
                return createEmptyVideoMenuPo2();
            }

            // 安全性：清理输入参数，防止SQL注入
            String sanitizedPointName = videoPointName.trim();
            if (sanitizedPointName.length() > 100) { // 限制输入长度
                log.warn("视频点位名称过长，截取前100个字符：{}", sanitizedPointName.substring(0, 100));
                sanitizedPointName = sanitizedPointName.substring(0, 100);
            }

            //根据视频点位名称模糊查询视频配置列表
            List<VideoAccessConfigPo1> configList = videoAccessConfigMapper.selectVideoConfigsByVideoPointNameAll(sanitizedPointName);
            //获取全部的菜单ID
            List<String> menuIds1 = configList.stream()
                    .map(VideoAccessConfigPo1::getVideoMenuId)
                    .collect(Collectors.toList());
            // 去重并转换为List
            List<String> uniqueMenuIds = menuIds1.stream()
                    .distinct()
                    .collect(Collectors.toList());
            //去重后的菜单Id列表查询出其父级关级treeId
            List<VideoMenu> videoMenuList = videoMenuMapper.selectVideoMenusByMenuIds(uniqueMenuIds);
            // 从videoMenuList中提取所有的treeId,
            List<String> treeIds = videoMenuList.stream()
                    .map(VideoMenu::getTreeId)
                    .collect(Collectors.toList());
            //并用逗号分割treeId，获取全部的父级菜单
            List<String> parentIds = treeIds.stream()
                    .flatMap(treeId -> Arrays.stream(treeId.split(",")))
                    .collect(Collectors.toList());
            //去重
            List<String> uniqueParentIds = parentIds.stream()
                    .distinct()
                    .collect(Collectors.toList());
            //获取搜索到的视频点位的菜单Id以及其全部上级菜单Id
            List<String> allMenuIds = new ArrayList<>();
            allMenuIds.addAll(uniqueMenuIds);
            allMenuIds.addAll(uniqueParentIds);


            List<VideoMenuPo2> menuList = new ArrayList<>();
            // 1. 根据所有的菜单Id获取菜单信息

            List<VideoMenu> videoMenuListAll = videoMenuMapper.selectVideoMenusByMenuIds(allMenuIds);
            for(VideoMenu videoMenu : videoMenuListAll){
                VideoMenuPo2 videoMenuPo2 = new VideoMenuPo2();
                videoMenuPo2.setVideoMenuId(videoMenu.getVideoMenuId());
                videoMenuPo2.setVideoMenuName(videoMenu.getVideoMenuName());
                videoMenuPo2.setParentId(videoMenu.getParentId());
                videoMenuPo2.setParentName(videoMenu.getParentName());
                videoMenuPo2.setVideoOnlineNum(0);
                videoMenuPo2.setVideoTotalNum(0);
                menuList.add(videoMenuPo2);
            }
            if (menuList == null || menuList.isEmpty()) {
                return createEmptyVideoMenuPo2();
            }

            // 2. 收集所有菜单ID，批量获取设备信息,获取逻辑错误，去掉
//            List<String> menuIds = menuList.stream()
//                    .map(VideoMenuPo2::getVideoMenuId)
//                    .collect(Collectors.toList());

            // 3. 批量获取每个菜单下的设备ID
            Map<String, List<String>> menuDeviceMap = batchGetDeviceIdsByMenuIds1(allMenuIds,configList);

            // 4. 获取所有菜单下的设备配置信息（包含deviceId和channelId）这里是搜索结果不是获取所有菜单下的设备配置信息
//            List<MenuDeviceDto> allDeviceConfigs = videoMenuMapper.batchSelectDeviceIdsByMenuIdsAndPointName(menuIds,videoPointName);
            List<MenuDeviceDto> allDeviceConfigs = new ArrayList<>();
            for(VideoAccessConfigPo1 videoAccessConfigPo1 : configList){
                MenuDeviceDto menuDeviceDto = new MenuDeviceDto();
                menuDeviceDto.setMenuId(videoAccessConfigPo1.getVideoMenuId());
                menuDeviceDto.setDeviceId(videoAccessConfigPo1.getDeviceId());
                menuDeviceDto.setChannelId(videoAccessConfigPo1.getChannelId());
                allDeviceConfigs.add(menuDeviceDto);
            }

            // 构建设备状态映射
            Map<String, Boolean> deviceStatusMap = new HashMap<>();
            Map<String, Boolean> uniqueKeyStatusMap = new HashMap<>(); // 提前声明，扩大作用域

            if (!allDeviceConfigs.isEmpty()) {
                // 将MenuDeviceDto转换为VideoAccessConfigPo1
                List<VideoAccessConfigPo1> deviceConfigs = allDeviceConfigs.stream()
                        .map(dto -> {
                            VideoAccessConfigPo1 config = new VideoAccessConfigPo1();
                            config.setDeviceId(dto.getDeviceId());
                            config.setChannelId(dto.getChannelId());
                            return config;
                        })
                        .collect(Collectors.toList());

                // 使用新版本接口，传递完整的deviceId和channelId
                uniqueKeyStatusMap = videoCloudPlatformService.batchGetDeviceOnlineStatusByConfigs(deviceConfigs);

                // 构建设备ID到状态的映射（兼容旧版本逻辑）
                Set<String> allDeviceIds = menuDeviceMap.values().stream()
                        .flatMap(List::stream)
                        .collect(Collectors.toSet());

                for (String deviceId : allDeviceIds) {
                    // 查找该设备ID对应的所有配置
                    List<MenuDeviceDto> deviceConfigs2 = allDeviceConfigs.stream()
                            .filter(dto -> deviceId.equals(dto.getDeviceId()))
                            .collect(Collectors.toList());

                    // 如果任一配置在线，则设备在线
                    boolean isOnline = false;
                    for (MenuDeviceDto dto : deviceConfigs2) {
                        String uniqueKey = dto.getDeviceId() + "|" + dto.getChannelId();
                        Boolean status = uniqueKeyStatusMap.get(uniqueKey);
                        if (Boolean.TRUE.equals(status)) {
                            isOnline = true;
                            break;
                        }
                    }

                    deviceStatusMap.put(deviceId, isOnline);
                }
            }

            // 5. 为每个菜单设置在线视频配置数（修复：统计视频配置数而不是设备数）
            for (VideoMenuPo2 menu : menuList) {
                try {
                    int onlineVideoCount = 0;

                    // 获取该菜单下的所有设备配置
                    List<MenuDeviceDto> menuDeviceConfigs = allDeviceConfigs.stream()
                            .filter(dto -> menu.getVideoMenuId().equals(dto.getMenuId()))
                            .collect(Collectors.toList());

                    // 统计在线的视频配置数量
                    for (MenuDeviceDto dto : menuDeviceConfigs) {
                        String uniqueKey = dto.getDeviceId() + "|" + dto.getChannelId();
                        Boolean status = uniqueKeyStatusMap.get(uniqueKey);
                        if (Boolean.TRUE.equals(status)) {
                            onlineVideoCount++;
                        }
                    }

                    menu.setVideoOnlineNum(onlineVideoCount);

                } catch (Exception e) {
                    log.error("设置菜单在线视频配置数异常，菜单名称：{}", JSONObject.toJSONString(menu.getVideoMenuName()), e);
                    menu.setVideoOnlineNum(0);
                }
            }

            // 6. 构建返回结果（性能优化：构建树形结构）
            VideoMenuPo2 result = buildVideoMenuTree(menuList,configList,uniqueKeyStatusMap);

            return result;

        } catch (Exception e) {
            log.error("根据视频点位名称查询视频菜单异常：{}", e.getMessage(),e);
            return createEmptyVideoMenuPo2();
        }
    }

    /**
     * 创建空的VideoMenuPo2对象
     *
     * @return 空的VideoMenuPo2对象
     */
    private VideoMenuPo2 createEmptyVideoMenuPo2() {
        VideoMenuPo2 emptyMenu = new VideoMenuPo2();
        emptyMenu.setVideoMenuId("root");
        emptyMenu.setVideoMenuName("搜索结果");
        emptyMenu.setParentId("");
        emptyMenu.setParentName("");
        emptyMenu.setVideoTotalNum(0);
        emptyMenu.setVideoOnlineNum(0);
        emptyMenu.setChild(new ArrayList<>());
        emptyMenu.setVideoAccessConfigPo1List(new ArrayList<>());
        return emptyMenu;
    }

    /**
     * 构建视频菜单树形结构（性能优化：减少嵌套循环）
     *
     * @param menuList 菜单列表
     * @return 根菜单对象
     */
    private VideoMenuPo2 buildVideoMenuTree(List<VideoMenuPo2> menuList, List<VideoAccessConfigPo1> configList, Map<String, Boolean> uniqueKeyStatusMap) {
        if (menuList == null || menuList.isEmpty()) {
            return createEmptyVideoMenuPo2();
        }

        Map<String, VideoMenuPo2> menuMap = menuList.stream().collect(Collectors.toMap(VideoMenuPo2::getVideoMenuId, Function.identity(), (k1, k2) -> k1));
        Map<String, List<VideoMenuPo2>> parentChildrenMap = new HashMap<>();

        // 1. 计算在线视频总数
        int totalOnlineNum = 0;
        for (VideoAccessConfigPo1 config : configList) {
            String uniqueKey = config.getDeviceId() + "|" + config.getChannelId();
            if (Boolean.TRUE.equals(uniqueKeyStatusMap.get(uniqueKey))) {
                config.setVideoOnlineFlag("1");
                totalOnlineNum++;
            }
        }

        // 2. 按 videoMenuId 对视频配置进行分组
        Map<String, List<VideoAccessConfigPo1>> configsByMenuId = configList.stream()
                .collect(Collectors.groupingBy(VideoAccessConfigPo1::getVideoMenuId));

        // 3. 构建父子关系，并为每个菜单关联其直接的视频配置
        List<VideoMenuPo2> rootMenus = new ArrayList<>();
        for (VideoMenuPo2 menu : menuList) {
            // 关联直接的视频配置
            List<VideoAccessConfigPo1> directConfigs = configsByMenuId.getOrDefault(menu.getVideoMenuId(), Collections.emptyList());
            menu.setVideoAccessConfigPo1List(directConfigs);

            // 构建父子关系
            String parentId = menu.getParentId();
            if (parentId == null || parentId.trim().isEmpty() || "0".equals(parentId) || !menuMap.containsKey(parentId)) {
                rootMenus.add(menu);
            } else {
                parentChildrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(menu);
            }
        }

        // 4. 将子菜单列表设置到对应的父菜单中
        for (VideoMenuPo2 menu : menuList) {
            List<VideoMenuPo2> children = parentChildrenMap.get(menu.getVideoMenuId());
            menu.setChild(children != null ? children : new ArrayList<>());
        }

        // 5. 递归计算每个根菜单分支的总视频数
        int totalVideoCountFromRoots = 0;
        for (VideoMenuPo2 rootMenu : rootMenus) {
            totalVideoCountFromRoots += calculateTotals(rootMenu);
        }


        // 6. 构建并返回最终结果
        VideoMenuPo2 result = createEmptyVideoMenuPo2();
        result.setVideoTotalNum(totalVideoCountFromRoots);
        //递归设置在线视频配置数
        for (VideoMenuPo2 rootMenu : menuList) {
            setOnlineVideoCountRecursive(rootMenu, uniqueKeyStatusMap);
        }

//        result.setVideoOnlineNum(videoOnlineNum);
        result.setChild(rootMenus);
        log.info("构建视频菜单树形结构成功：{}", JSONObject.toJSONString(result));
        return result;
    }

    private void setOnlineVideoCountRecursive(VideoMenuPo2 menu, Map<String, Boolean> uniqueKeyStatusMap) {
        int videoOnlineNum = 0;
        int totalOnlineNum = 0;

        if (!"0".equals(menu.getParentId())) {
            List<VideoMenuPo2> child = menu.getChild();
            for(VideoMenuPo2 videoMenuPo2:child){
                for(VideoAccessConfigPo1 videoMenuAndConfigPo1:videoMenuPo2.getVideoAccessConfigPo1List()){
                    if (Boolean.TRUE.equals(uniqueKeyStatusMap.get(videoMenuAndConfigPo1.getDeviceId() + "|" + videoMenuAndConfigPo1.getChannelId()))) {
                        videoOnlineNum++;
                    }
                }
            }
            for (VideoAccessConfigPo1 config : menu.getVideoAccessConfigPo1List()) {
                if (Boolean.TRUE.equals(uniqueKeyStatusMap.get(config.getDeviceId() + "|" + config.getChannelId()))) {
                    videoOnlineNum++;
                }
            }
            log.info("设置菜单在线视频配置数，菜单名称：{}，在线视频配置数：{}", menu.getVideoMenuName(), videoOnlineNum);
            menu.setVideoOnlineNum(videoOnlineNum);
        }else {
            List<VideoMenuPo2> child = menu.getChild();
            for(VideoMenuPo2 videoMenuPo2:child){
                for(VideoAccessConfigPo1 videoMenuAndConfigPo1:videoMenuPo2.getVideoAccessConfigPo1List()){
                    if (Boolean.TRUE.equals(uniqueKeyStatusMap.get(videoMenuAndConfigPo1.getDeviceId() + "|" + videoMenuAndConfigPo1.getChannelId()))) {
                        videoOnlineNum++;
                    }
                }
                for(VideoMenuPo2 videoMenuPo22:videoMenuPo2.getChild()){
                    for(VideoAccessConfigPo1 videoMenuAndConfigPo1:videoMenuPo22.getVideoAccessConfigPo1List()){
                        if (Boolean.TRUE.equals(uniqueKeyStatusMap.get(videoMenuAndConfigPo1.getDeviceId() + "|" + videoMenuAndConfigPo1.getChannelId()))) {
                            videoOnlineNum++;
                        }
                    }
                }
            }

            for (VideoAccessConfigPo1 config : menu.getVideoAccessConfigPo1List()) {
                if (Boolean.TRUE.equals(uniqueKeyStatusMap.get(config.getDeviceId() + "|" + config.getChannelId()))) {
                    videoOnlineNum++;
                }
            }
            menu.setVideoOnlineNum(videoOnlineNum);
        }


        // 递归处理子菜单 (假设 VideoMenuPo2 中有 getChildMenus() 方法)
        List<VideoMenuPo2> childMenus = menu.getChild(); // 获取子菜单列表
        if (childMenus != null) {
            for (VideoMenuPo2 childMenu : childMenus) {
                setOnlineVideoCountRecursive(childMenu, uniqueKeyStatusMap);
            }
        }

    }


    /**
     * 递归计算菜单及其所有子菜单的视频总数
     * @param menu 当前菜单节点
     * @return 当前菜单节点下的视频总数
     */
    private int calculateTotals(VideoMenuPo2 menu) {
        // 初始总数为当前菜单直接关联的视频数
        int currentTotal = menu.getVideoAccessConfigPo1List().size();

        // 递归累加所有子菜单的视频总数
        if (menu.getChild() != null) {
            for (VideoMenuPo2 child : menu.getChild()) {
                currentTotal += calculateTotals(child);
            }
        }

        // 更新当前菜单节点的视频总数
        menu.setVideoTotalNum(currentTotal);
        return currentTotal;
    }


    /**
     * 递归获取指定菜单的所有子菜单ID
     *
     * @param parentMenuId 父菜单ID
     * @return 所有子菜单ID列表
     */
    private List<String> getChildMenuIdsRecursive(String parentMenuId) {
        List<String> childMenuIds = new ArrayList<>();

        try {
            // 查询直接子菜单
            VideoMenu queryParam = new VideoMenu();
            queryParam.setParentId(parentMenuId);
            List<VideoMenu> directChildren = videoMenuMapper.selectVideoMenuList(queryParam);

            if (directChildren != null && !directChildren.isEmpty()) {
                for (VideoMenu child : directChildren) {
                    if (child.getVideoMenuId() != null) {
                        childMenuIds.add(child.getVideoMenuId());

                        // 递归查询子菜单的子菜单
                        List<String> grandChildren = getChildMenuIdsRecursive(child.getVideoMenuId());
                        childMenuIds.addAll(grandChildren);
                    }
                }
            }

        } catch (Exception e) {
            log.error("递归查询子菜单异常，父菜单ID：{}", JSONObject.toJSONString(parentMenuId), e);
        }

        return childMenuIds;
    }

    /**
     * 批量查询视频菜单（性能优化）
     *
     * @param menuIds 菜单ID列表
     * @return 菜单ID -> 菜单信息的映射
     */
    @Override
    public Map<String, VideoMenuVo1> selectVideoMenusByIds(List<String> menuIds) {
        try {

            if (menuIds == null || menuIds.isEmpty()) {
                log.warn("批量查询菜单ID列表为空");
                return new HashMap<>();
            }

            // 去重处理
            List<String> distinctMenuIds = menuIds.stream()
                    .filter(id -> id != null && !id.trim().isEmpty())
                    .distinct()
                    .collect(Collectors.toList());

            if (distinctMenuIds.isEmpty()) {
                log.warn("去重后菜单ID列表为空");
                return new HashMap<>();
            }

            // 批量查询
            List<VideoMenuVo1> menuList = videoMenuMapper.selectVideoMenusByIds(distinctMenuIds);

            // 转换为Map，便于快速查找
            Map<String, VideoMenuVo1> menuMap = new HashMap<>();
            if (menuList != null) {
                for (VideoMenuVo1 menu : menuList) {
                    if (menu != null && menu.getVideoMenuId() != null) {
                        menuMap.put(menu.getVideoMenuId(), menu);
                    }
                }
            }

            return menuMap;

        } catch (Exception e) {
            log.error("批量查询视频菜单异常：{}", e.getMessage());
            return new HashMap<>();
        }
    }

    /**
     * 获取全部菜单以及第一个三级菜单下的视频配置列表（分页）
     * 需请求视频云平台接口获取设备的在线状态
     *
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 菜单树和配置列表
     */
    @Override
    public List<VideoMenuAndConfig> listAllVideoMenuAndFirstConfig(Integer pageNum, Integer pageSize) {
        try {
            // 参数校验和默认值设置
            int validPageNum = pageNum != null && pageNum > 0 ? pageNum : 1;
            int validPageSize = pageSize != null && pageSize > 0 ? pageSize : 10;


            // 1. 查询所有菜单数据（性能优化：一次查询获取所有菜单）
            List<VideoMenuAndConfig> allMenus = videoMenuMapper.selectAllVideoMenuForTree();

            if (allMenus == null || allMenus.isEmpty()) {
                log.info("未查询到菜单数据");
                return new ArrayList<>();
            }

            // 2. 构建菜单树结构（性能优化：内存中构建，避免递归查询数据库）
            List<VideoMenuAndConfig> menuTree = buildMenuTree(allMenus);

            // 3. 为排序为第一个的三级菜单加载分页的视频配置
            loadPaginatedVideoConfigsForFirstThirdLevelMenu(menuTree, validPageNum, validPageSize);

            return menuTree;

        } catch (Exception e) {
            log.error("获取全部菜单和配置异常：{}", e.getMessage());

            // 异常时返回空列表
            return new ArrayList<>();
        }
    }

    /**
     * 构建菜单树结构（性能优化：内存中构建）
     *
     * @param allMenus 所有菜单列表
     * @return 菜单树
     */
    private List<VideoMenuAndConfig> buildMenuTree(List<VideoMenuAndConfig> allMenus) {
        try {
            // 按层级分组
            Map<String, List<VideoMenuAndConfig>> parentChildMap = new HashMap<>();
            List<VideoMenuAndConfig> rootMenus = new ArrayList<>();

            // 分类菜单
            for (VideoMenuAndConfig menu : allMenus) {
                if ("0".equals(menu.getParentId())) {
                    rootMenus.add(menu);
                } else {
                    parentChildMap.computeIfAbsent(menu.getParentId(), k -> new ArrayList<>()).add(menu);
                }
            }

            // 递归构建树结构
            buildChildMenus(rootMenus, parentChildMap);

            return rootMenus;

        } catch (Exception e) {
            log.error("构建菜单树异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 递归构建子菜单
     */
    private void buildChildMenus(List<VideoMenuAndConfig> parentMenus, Map<String, List<VideoMenuAndConfig>> parentChildMap) {
        for (VideoMenuAndConfig parent : parentMenus) {
            List<VideoMenuAndConfig> children = parentChildMap.get(parent.getVideoMenuId());
            if (children != null && !children.isEmpty()) {
                parent.setVideoMenuAndConfigListChild(children);
                buildChildMenus(children, parentChildMap);
            }
        }
    }

    /**
     * 查找第一个三级菜单
     */
    private VideoMenuAndConfig findFirstThirdLevelMenu(List<VideoMenuAndConfig> menuTree) {
        for (VideoMenuAndConfig firstLevel : menuTree) {
            if (firstLevel.getVideoMenuAndConfigListChild() != null) {
                for (VideoMenuAndConfig secondLevel : firstLevel.getVideoMenuAndConfigListChild()) {
                    if (secondLevel.getVideoMenuAndConfigListChild() != null) {
                        for (VideoMenuAndConfig thirdLevel : secondLevel.getVideoMenuAndConfigListChild()) {
                            return thirdLevel; // 返回第一个三级菜单
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * 批量设置设备在线状态（性能优化，使用新版本接口）
     */
    private void setDeviceOnlineStatus(List<VideoAccessConfigPo1> configs) {
        if (configs == null || configs.isEmpty()) {
            return;
        }

        try {
            long startTime = System.currentTimeMillis();

            // 过滤有效的配置（有设备ID的）
            List<VideoAccessConfigPo1> validConfigs = configs.stream()
                    .filter(config -> config.getDeviceId() != null && !config.getDeviceId().trim().isEmpty())
                    .collect(Collectors.toList());

            if (validConfigs.isEmpty()) {
                log.warn("未找到有效的设备ID，无法查询在线状态");
                // 设置所有配置为离线
                for (VideoAccessConfigPo1 config : configs) {
                    config.setVideoOnlineFlag("0");
                }
                return;
            }

            log.info("开始批量查询设备在线状态：totalConfigs={}", JSONObject.toJSONString(configs.size()));

            // 使用新版本接口批量查询设备在线状态
            Map<String, Boolean> deviceOnlineStatusMap = videoCloudPlatformService.batchGetDeviceOnlineStatusByConfigs(validConfigs);

            // 设置在线状态
            int onlineCount = 0;
            int offlineCount = 0;

            for (VideoAccessConfigPo1 config : configs) {
                try {
                    String deviceId = config.getDeviceId();
                    String channelId = config.getChannelId();

                    if (deviceId != null && !deviceId.trim().isEmpty()) {
                        // 构建唯一标识，因为视频的唯一性由deviceId和channelId两个字段确定
                        String uniqueKey = buildDeviceUniqueKey(deviceId, channelId);
                        Boolean isOnline = deviceOnlineStatusMap.get(uniqueKey);
                        String onlineFlag = Boolean.TRUE.equals(isOnline) ? "1" : "0";
                        config.setVideoOnlineFlag(onlineFlag);

                        // 设置视频封面字段
                        String thumbnail = videoCloudPlatformService.getVideoThumbnail(uniqueKey);
                        if (thumbnail != null && !thumbnail.trim().isEmpty()) {
                            config.setThumbnail(thumbnail);
                        }

                        if ("1".equals(onlineFlag)) {
                            onlineCount++;
                        } else {
                            offlineCount++;
                        }
                    } else {
                        config.setVideoOnlineFlag("0"); // 无设备ID默认离线
                        offlineCount++;
                    }
                } catch (Exception e) {
                    log.error("设置配置在线状态异常：configId={},error={}", JSONObject.toJSONString(config.getId()),e);
                    config.setVideoOnlineFlag("0"); // 异常时默认离线
                    offlineCount++;
                }
            }

        } catch (Exception e) {
            log.error("批量设置设备在线状态异常：{}", e.getMessage());

            // 异常时设置所有配置为离线
            for (VideoAccessConfigPo1 config : configs) {
                config.setVideoOnlineFlag("0");
            }
        }
    }

    /**
     * 为排序为第一个的三级菜单加载分页的视频配置
     *
     * @param menuTree 菜单树
     * @param pageNum 页码
     * @param pageSize 页大小
     */
    private void loadPaginatedVideoConfigsForFirstThirdLevelMenu(List<VideoMenuAndConfig> menuTree, int pageNum, int pageSize) {
        if (menuTree == null || menuTree.isEmpty()) {
            return;
        }

        try {

            // 找到排序为第一个的三级菜单
            VideoMenuAndConfig firstThirdLevelMenu = findFirstThirdLevelMenuByOrder(menuTree);

            if (firstThirdLevelMenu == null) {
                log.info("未找到三级菜单，无需加载视频配置");
                return;
            }

            log.info("开始为排序第一个的三级菜单加载分页视频配置：{}", JSONObject.toJSONString(firstThirdLevelMenu.getVideoMenuName()));

            // 计算分页参数
            int offset = (pageNum - 1) * pageSize;

            // 查询第一个三级菜单的分页配置
            List<VideoAccessConfigPo1> configs = videoAccessConfigMapper
                    .selectVideoConfigsByMenuIdWithPage1(firstThirdLevelMenu.getVideoMenuId(), offset, pageSize);

            if (configs == null || configs.isEmpty()) {
                log.info("第一个三级菜单未查询到视频配置数据");
                firstThirdLevelMenu.setVideoAccessConfigPo1List(new PageInfo<>());
                return;
            }

            // 批量获取设备在线状态（性能优化）
            setDeviceOnlineStatus(configs);

            // 为第一个三级菜单设置分页配置
            firstThirdLevelMenu.setVideoAccessConfigPo1List(new PageInfo<>(configs));

            log.info("第一个三级菜单视频配置加载完成：{}", JSONObject.toJSONString(firstThirdLevelMenu.getVideoMenuName()));

        } catch (Exception e) {
            log.error("为第一个三级菜单加载分页视频配置异常：{}", e.getMessage());

            // 异常时为第一个三级菜单设置空配置列表
            VideoMenuAndConfig firstThirdLevelMenu = findFirstThirdLevelMenuByOrder(menuTree);
            if (firstThirdLevelMenu != null) {
                firstThirdLevelMenu.setVideoAccessConfigPo1List(new PageInfo<>());
            }
        }
    }

    /**
     * 查找排序为第一个的三级菜单
     * 按照菜单的排序字段(order_num)找到排序最小的三级菜单
     *
     * @param menuTree 菜单树
     * @return 排序第一个的三级菜单，如果没有则返回null
     */
    private VideoMenuAndConfig findFirstThirdLevelMenuByOrder(List<VideoMenuAndConfig> menuTree) {
        if (menuTree == null || menuTree.isEmpty()) {
            return null;
        }

        VideoMenuAndConfig firstThirdLevelMenu = null;
        Integer minOrderNum = null;

        // 遍历所有三级菜单，找到排序最小的
        for (VideoMenuAndConfig firstLevel : menuTree) {
            if (firstLevel.getVideoMenuAndConfigListChild() != null) {
                for (VideoMenuAndConfig secondLevel : firstLevel.getVideoMenuAndConfigListChild()) {
                    if (secondLevel.getVideoMenuAndConfigListChild() != null) {
                        for (VideoMenuAndConfig thirdLevel : secondLevel.getVideoMenuAndConfigListChild()) {
                            Integer orderNum = thirdLevel.getOrderNum();

                            // 找到排序最小的三级菜单
                            if (minOrderNum == null || (orderNum != null && orderNum < minOrderNum)) {
                                minOrderNum = orderNum;
                                firstThirdLevelMenu = thirdLevel;
                            }
                        }
                    }
                }
            }
        }

        if (firstThirdLevelMenu != null) {
            log.info("找到排序第一个的三级菜单：{}", JSONObject.toJSONString(firstThirdLevelMenu.getVideoMenuName()));
        } else {
            log.info("未找到任何三级菜单");
        }

        return firstThirdLevelMenu;
    }




    /**
     * 构建设备唯一标识
     * 根据deviceId和channelId构建唯一标识，因为视频的唯一性由这两个字段确定
     *
     * @param deviceId 设备ID
     * @param channelId 通道ID
     * @return 设备唯一标识
     */
    private String buildDeviceUniqueKey(String deviceId, String channelId) {
        if (deviceId == null) {
            return "";
        }

        // 使用分隔符连接deviceId和channelId，确保唯一性
        String safeChannelId = channelId != null ? channelId : "";
        return deviceId + "|" + safeChannelId;
    }

    /**
     * 获取全部的菜单以及其下的视频配置
     *
     * @return 菜单和配置列表
     */
    @Override
    public List<VideoMenuAndConfigVo4> listAllVideoMenuAndConfig() {
        String methodId = java.util.UUID.randomUUID().toString().substring(0, 8);

        try {
            log.info("开始获取全部菜单和视频配置：{}", JSONObject.toJSONString(methodId));

            // 1. 查询所有菜单
            List<VideoMenuPo> allMenus = videoMenuMapper.selectAllVideoMenus();
            if (allMenus == null || allMenus.isEmpty()) {
                log.info("未查询到任何菜单数据：methodId: {}", methodId);
                return new ArrayList<>();
            }

            // 2. 查询所有视频配置
            List<VideoAccessConfigPo1> allConfigs = videoAccessConfigMapper.selectAllVideoConfigs();


            // 3. 批量查询设备在线状态
            Map<String, Boolean> deviceStatusMap = batchGetDeviceOnlineStatus(allConfigs);

            // 4. 设置配置的在线状态
            setConfigOnlineStatus(allConfigs, deviceStatusMap);

            // 5. 构建菜单树结构
            log.info("开始构建菜单树结构：methodId: {}", methodId);
            List<VideoMenuAndConfigVo4> result = buildMenuTreeWithAllConfigsVo4(
                    allMenus, allConfigs);
            log.info("菜单树结构构建完成：methodId: {}, resultSize: {}", methodId, result != null ? result.size() : 0);


            // 确保返回值不为null，避免Controller层异常
            return result != null ? result : new ArrayList<>();

        } catch (Exception e) {
            log.error("获取全部菜单和视频配置异常：", e);
            throw new RuntimeException("获取菜单配置失败：" + e.getMessage(), e);
        }
    }

    /**
     * 批量查询设备在线状态
     *
     * @param allConfigs 所有配置
     * @return 设备状态映射
     */
    private Map<String, Boolean> batchGetDeviceOnlineStatus(List<VideoAccessConfigPo1> allConfigs) {
        if (allConfigs == null || allConfigs.isEmpty()) {
            return new HashMap<>();
        }

        try {
            // 过滤有效的配置
            List<VideoAccessConfigPo1> validConfigs = allConfigs.stream()
                    .filter(config -> config.getDeviceId() != null && !config.getDeviceId().trim().isEmpty())
                    .collect(Collectors.toList());

            if (validConfigs.isEmpty()) {
                return new HashMap<>();
            }

            // 使用新版本接口批量查询设备在线状态
            return videoCloudPlatformService.batchGetDeviceOnlineStatusByConfigs(validConfigs);

        } catch (Exception e) {
            log.error("批量查询设备在线状态异常：{}", e.getMessage());
            return new HashMap<>();
        }
    }

    /**
     * 设置配置的在线状态
     *
     * @param allConfigs 所有配置
     * @param deviceStatusMap 设备状态映射
     */
    private void setConfigOnlineStatus(List<VideoAccessConfigPo1> allConfigs, Map<String, Boolean> deviceStatusMap) {
        if (allConfigs == null || allConfigs.isEmpty()) {
            return;
        }

        for (VideoAccessConfigPo1 config : allConfigs) {
            try {
                String deviceId = config.getDeviceId();
                String channelId = config.getChannelId();

                if (deviceId != null && !deviceId.trim().isEmpty()) {
                    // 构建唯一标识，因为视频的唯一性由deviceId和channelId两个字段确定
                    String uniqueKey = buildDeviceUniqueKey(deviceId, channelId);
                    Boolean isOnline = deviceStatusMap.get(uniqueKey);
                    config.setVideoOnlineFlag(Boolean.TRUE.equals(isOnline) ? "1" : "0");
                } else {
                    config.setVideoOnlineFlag("0"); // 无设备ID默认离线
                }
            } catch (Exception e) {
                log.error("设置配置在线状态异常：configId={}", config.getId(), e);
                config.setVideoOnlineFlag("0"); // 异常时默认离线
            }
        }
    }



    /**
     * 构建菜单树结构（包含所有配置，无过滤，返回VideoMenuAndConfigVo4）
     *
     * @param allMenus 所有菜单
     * @param allConfigs 所有配置
     * @return 菜单树
     */
    private List<VideoMenuAndConfigVo4> buildMenuTreeWithAllConfigsVo4(
            List<VideoMenuPo> allMenus, List<VideoAccessConfigPo1> allConfigs) {

        try {
            log.debug("开始构建菜单树结构：menuCount={}, configCount={}", allMenus.size(), allConfigs.size());

            // 1. 将菜单PO直接转换为VideoMenuAndConfigVo4
            log.debug("步骤1：转换菜单PO为VideoMenuAndConfigVo4");
            List<VideoMenuAndConfigVo4> menuVos = convertMenuPosToVo4s(allMenus);
            log.debug("转换完成：menuVoCount={}", menuVos.size());

            // 2. 按菜单ID分组配置
            log.debug("步骤2：按菜单ID分组配置");
            Map<String, List<VideoAccessConfigPo1>> configsByMenuId = allConfigs.stream()
                    .filter(config -> config.getVideoMenuId() != null)
                    .collect(Collectors.groupingBy(VideoAccessConfigPo1::getVideoMenuId));
            log.debug("分组完成：groupCount={}", configsByMenuId.size());

            // 3. 为菜单设置配置（不进行任何过滤）
            log.debug("步骤3：为菜单设置配置");
            for (VideoMenuAndConfigVo4 menuVo : menuVos) {
                List<VideoAccessConfigPo1> configs = configsByMenuId.get(menuVo.getVideoMenuId());
                if (configs != null) {
                    menuVo.setVideoAccessConfigPo1List(configs);
                    log.debug("菜单[{}]设置配置：configCount={}", menuVo.getVideoMenuId(), configs.size());
                }
            }

            // 4. 构建树形结构
            log.debug("步骤4：构建树形结构");
            List<VideoMenuAndConfigVo4> result = buildMenuTreeStructureVo4(menuVos);
            log.debug("树形结构构建完成：rootCount={}", result != null ? result.size() : 0);

            // 5. 计算统计信息
            log.debug("步骤5：计算统计信息");
            calculateMenuStatsVo4(result);
            log.debug("统计信息计算完成");

            return result;

        } catch (Exception e) {
            log.error("构建菜单树结构异常：", e);
            return new ArrayList<>();
        }
    }

    /**
     * 将菜单PO直接转换为VideoMenuAndConfigVo4
     *
     * @param allMenus 所有菜单PO
     * @return VideoMenuAndConfigVo4列表
     */
    private List<VideoMenuAndConfigVo4> convertMenuPosToVo4s(List<VideoMenuPo> allMenus) {
        List<VideoMenuAndConfigVo4> menuVos = new ArrayList<>();

        if (allMenus == null) {
            return menuVos;
        }

        for (VideoMenuPo menuPo : allMenus) {
            try {
                VideoMenuAndConfigVo4 menuVo = new VideoMenuAndConfigVo4();
                menuVo.setVideoMenuId(menuPo.getVideoMenuId());
                menuVo.setVideoMenuName(menuPo.getVideoMenuName());
                menuVo.setParentId(menuPo.getParentId());
                menuVo.setParentName(menuPo.getParentName());
                menuVo.setOrderNum(menuPo.getOrderNum());
                menuVos.add(menuVo);

                log.debug("转换菜单PO成功：menuId={}, menuName={}",
                        menuPo.getVideoMenuId(), menuPo.getVideoMenuName());

            } catch (Exception e) {
                log.error("转换菜单PO异常：menuId={}, error: {}", JSONObject.toJSONString(menuPo != null ? menuPo.getVideoMenuId() : "null"), e.getMessage());
            }
        }

        return menuVos;
    }

    /**
     * 构建VideoMenuAndConfigVo4树形结构
     *
     * @param menuVos 菜单VO列表
     * @return 树形结构的根节点列表
     */
    private List<VideoMenuAndConfigVo4> buildMenuTreeStructureVo4(List<VideoMenuAndConfigVo4> menuVos) {

        if (menuVos == null || menuVos.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            // 使用Map进行快速查找
            Map<String, VideoMenuAndConfigVo4> menuMap = new HashMap<>();
            Map<String, List<VideoMenuAndConfigVo4>> childrenMap = new HashMap<>();

            // 建立索引
            for (VideoMenuAndConfigVo4 menuVo : menuVos) {
                if (menuVo != null && menuVo.getVideoMenuId() != null) {
                    menuMap.put(menuVo.getVideoMenuId(), menuVo);
                }
            }

            // 构建父子关系
            List<VideoMenuAndConfigVo4> rootMenus = new ArrayList<>();
            for (VideoMenuAndConfigVo4 menuVo : menuVos) {
                if (menuVo == null) continue;

                String parentId = menuVo.getParentId();
                if (parentId == null || parentId.trim().isEmpty() || "0".equals(parentId)) {
                    // 根节点
                    rootMenus.add(menuVo);
                } else {
                    // 子节点
                    childrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(menuVo);
                }
            }

            // 设置子菜单
            for (VideoMenuAndConfigVo4 menuVo : menuVos) {
                if (menuVo == null || menuVo.getVideoMenuId() == null) continue;

                List<VideoMenuAndConfigVo4> children = childrenMap.get(menuVo.getVideoMenuId());
                if (children != null) {
                    // 按orderNum排序
                    children.sort((a, b) -> {
                        Integer orderA = a.getOrderNum() != null ? a.getOrderNum() : 0;
                        Integer orderB = b.getOrderNum() != null ? b.getOrderNum() : 0;
                        return orderA.compareTo(orderB);
                    });
                    menuVo.setVideoMenuAndConfigListChild(children);
                }
            }

            // 根节点排序
            rootMenus.sort((a, b) -> {
                Integer orderA = a.getOrderNum() != null ? a.getOrderNum() : 0;
                Integer orderB = b.getOrderNum() != null ? b.getOrderNum() : 0;
                return orderA.compareTo(orderB);
            });

            return rootMenus;

        } catch (Exception e) {
            log.error("构建菜单树形结构异常：", e);
            return new ArrayList<>();
        }
    }

    /**
     * 计算VideoMenuAndConfigVo4菜单统计信息
     *
     * @param menus 菜单列表
     */
    private void calculateMenuStatsVo4(List<VideoMenuAndConfigVo4> menus) {
        if (menus == null || menus.isEmpty()) {
            return;
        }

        for (VideoMenuAndConfigVo4 menu : menus) {
            try {
                // 递归计算子菜单统计
                if (menu.getVideoMenuAndConfigListChild() != null) {
                    calculateMenuStatsVo4(menu.getVideoMenuAndConfigListChild());
                }

                // 计算当前菜单的统计信息
                menu.calculateVideoStats();

                log.debug("菜单统计计算完成：menuId={}, totalNum={}, onlineNum={}",
                        menu.getVideoMenuId(), menu.getVideoTotalNum(), menu.getVideoOnlineNum());

            } catch (Exception e) {
                log.error("计算菜单统计信息异常：menuId={}", menu.getVideoMenuId(), e);
                // 设置默认值，避免null
                if (menu.getVideoTotalNum() == null) menu.setVideoTotalNum(0);
                if (menu.getVideoOnlineNum() == null) menu.setVideoOnlineNum(0);
                if (menu.getVideoOfflineNum() == null) menu.setVideoOfflineNum(0);
            }
        }
    }

}
