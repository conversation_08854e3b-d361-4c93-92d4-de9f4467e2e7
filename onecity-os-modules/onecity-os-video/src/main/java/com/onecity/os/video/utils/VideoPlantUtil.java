package com.onecity.os.video.utils;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

/**
 * @Author:hbs
 */
@Slf4j
public class VideoPlantUtil {

    private static String baseUrl = "http://onecity-cc-vp-test-30746.t.onecode.cmict.cloud";
    private static String applicationCode = "jsc_app_code";

    public static JSONObject publicInterface(JSONObject jsonBody,String url){
        String getApiUrl = baseUrl + url;
        //接口请求参数
        JSONObject getBody = new JSONObject();
        getBody.put("body",jsonBody);
        //head参数构造
        JSONObject headBody = new JSONObject();
        headBody.put("applicationCode",applicationCode);
        headBody.put("code","000000");
        //设置操作时间戳
        headBody.put("operationTime",System.currentTimeMillis());
        headBody.put("status","S");
        getBody.put("head",headBody);
        //发送请求
        log.info("调用视频云平台接口{}参数:--{}",JSONObject.toJSONString(getApiUrl),getBody.toJSONString());
        String apiResult = HttpRequest.post(getApiUrl).header("Content-Type", "application/json").body(getBody.toJSONString()).timeout(20000).execute().body();

        log.info("调用视频云平台接口{}返回数据:--{}",JSONObject.toJSONString(getApiUrl),JSONObject.toJSONString(apiResult));

        return JSON.parseObject(apiResult);
    }

    public static JSONObject publicInterfaceSimple(JSONObject jsonBody,String url){
        String getApiUrl = baseUrl + url;
        //发送请求
        String apiResult = HttpRequest.post(getApiUrl).header("Content-Type", "application/json").body(jsonBody.toJSONString()).timeout(20000).execute().body();
        return JSON.parseObject(apiResult);
    }
}