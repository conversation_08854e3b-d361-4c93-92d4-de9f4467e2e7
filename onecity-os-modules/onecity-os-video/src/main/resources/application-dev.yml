server:
  port: 9206
#  servlet:
#    context-path: /api
#    compression:
#      enabled: true
#      mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*
# spring配置
spring:
  servlet:
    multipart:
      # 根据实际需求作调整
      max-file-size: 50MB
      # 默认最大请求大小为10M，总上传的数据大小
      max-request-size: 100MB
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  redis:
    host: onecity-os-redis
    port: 6379
#    host: *************
#    prot: 30425
    password: DONT(QkOmrw81EnDa0CDf9y1hywwU2sWxykwRkIHa8ZXhBvSUmcPtXUJerFvBv/77k4AxY40uLtxHEcjv86VWFXHNoPxPcGIgG6n10aMBOlsrWLwWZAjlOWr3N4JrG04kk+fPG+57s7x6Zu6algBVF8FWVJfIxkMWMIhU0IZGZ5njtok=)
  datasource:
    druid:
      stat-view-servlet:
        enabled: false
        loginUsername: admin
        loginPassword: 123456
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
    dynamic:
      druid:
        initial-size: 5
        min-idle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j
      datasource:
        # 主库数据源
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ********************************************************************************************************************************************************
          #          url: ******************************************************************************************************************************************************
          username: root
          password: DONT(QkOmrw81EnDa0CDf9y1hywwU2sWxykwRkIHa8ZXhBvSUmcPtXUJerFvBv/77k4AxY40uLtxHEcjv86VWFXHNoPxPcGIgG6n10aMBOlsrWLwWZAjlOWr3N4JrG04kk+fPG+57s7x6Zu6algBVF8FWVJfIxkMWMIhU0IZGZ5njtok=)

# seata配置
seata:
  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启
  enabled: false
  # Seata 应用编号，默认为 ${spring.application.name}
  application-id: ${spring.application.name}
  # Seata 事务组编号，用于 TC 集群名
  tx-service-group: ${spring.application.name}-group
  # 关闭自动代理
  enable-auto-data-source-proxy: false
  # 服务配置项
  service:
    # 虚拟组和分组的映射
    vgroup-mapping:
      ruoyi-system-group: default
  config:
    type: nacos
    nacos:
      serverAddr: onecity-os-nacos:8848
#      serverAddr: *************:30309
      group: SEATA_GROUP
      namespace:
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: onecity-os-nacos:8848
#      server-addr: *************:30309
      namespace:

# mybatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.onecity.os.supervise
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath:mapper/**/*.xml
    configuration:
      map-underscore-to-camel-case: true

# swagger配置
swagger:
  title: large-screen模块接口文档
  license: Powered By ruoyi
  licenseUrl: https://ruoyi.vip
springfox:
  documentation:
    # 总开关（同时设置auto-startup=false，否则/v3/api-docs等接口仍能继续访问）
    enabled: false
    auto-startup: false
    swagger-ui:
      enabled: false

indicator:
  template:
    file:
      path: C://模板
#feign配置
feign:
  client:
    config:
      #default 默认所有服务。如果需要更改，直接换成调用方的服务名
      default:
        connectTimeout: 60000
        readTimeout: 60000
  compression:
    request:
      enabled: false
      min-request-size: 3000000
    response:
      enabled: false
  hystrix:
    enabled: true
  httpclient:
    connection-timeout: 60000
    read-timeout: 60000


