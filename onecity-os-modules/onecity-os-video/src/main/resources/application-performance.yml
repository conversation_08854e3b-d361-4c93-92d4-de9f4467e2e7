# 高性能配置文件
# 支持10000设备，响应时间<500ms


# 数据库连接池优化
spring:
  datasource:
    hikari:
      maximum-pool-size: 50            # 最大连接池大小
      minimum-idle: 10                 # 最小空闲连接数
      connection-timeout: 30000        # 连接超时30秒
      idle-timeout: 600000             # 空闲超时10分钟
      max-lifetime: 1800000            # 连接最大生命周期30分钟
      leak-detection-threshold: 60000  # 连接泄漏检测阈值1分钟

# MyBatis配置优化
mybatis:
  configuration:
    cache-enabled: true                # 启用二级缓存
    lazy-loading-enabled: true         # 启用延迟加载
    multiple-result-sets-enabled: true # 允许多结果集
    use-column-label: true             # 使用列标签
    use-generated-keys: true           # 使用生成的键
    auto-mapping-behavior: partial     # 自动映射行为
    default-executor-type: reuse       # 默认执行器类型
    default-statement-timeout: 25      # 默认语句超时时间

# JVM优化建议（在启动参数中配置）
# -Xms2g -Xmx4g                       # 堆内存大小
# -XX:+UseG1GC                        # 使用G1垃圾收集器
# -XX:MaxGCPauseMillis=200            # 最大GC暂停时间
# -XX:+UseStringDeduplication         # 字符串去重
# -XX:+OptimizeStringConcat           # 优化字符串连接

# 线程池配置
server:
  tomcat:
    threads:
      max: 200                         # 最大线程数
      min-spare: 20                    # 最小空闲线程数
    max-connections: 8192              # 最大连接数
    accept-count: 100                  # 等待队列长度
    connection-timeout: 20000          # 连接超时20秒

# 日志配置优化
logging:
  level:
    com.onecity.os.video: INFO        # 业务日志级别
    org.springframework: WARN         # Spring框架日志级别
    com.zaxxer.hikari: WARN           # 连接池日志级别
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 缓存配置（如果使用Redis）
# spring:
#   redis:
#     host: localhost
#     port: 6379
#     timeout: 2000
#     jedis:
#       pool:
#         max-active: 50
#         max-idle: 20
#         min-idle: 5
#         max-wait: 3000

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 性能监控指标
performance:
  monitoring:
    enabled: true
    thresholds:
      response-time: 500               # 响应时间阈值(ms)
      device-count: 10000              # 支持设备数量
      concurrent-users: 100            # 并发用户数
      memory-usage: 80                 # 内存使用率阈值(%)
      cpu-usage: 70                    # CPU使用率阈值(%)

# 异步处理配置
async:
  core-pool-size: 10                 # 核心线程数
  max-pool-size: 50                  # 最大线程数
  queue-capacity: 1000               # 队列容量
  thread-name-prefix: "VideoAsync-"  # 线程名前缀
  keep-alive-seconds: 60             # 线程保活时间
