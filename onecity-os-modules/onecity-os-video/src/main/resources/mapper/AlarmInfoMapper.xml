<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.video.mapper.AlarmInfoMapper">
    
    <resultMap type="com.onecity.os.video.domain.AlarmInfo" id="AlarmInfoResult">
        <result property="id"    column="id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="coreVideoId"    column="core_video_id"    />
        <result property="analysisId"    column="analysis_id"    />
        <result property="alarmCode"    column="alarm_code"    />
        <result property="alarmType"    column="alarm_type"    />
        <result property="alarmTypeName"    column="alarm_type_name"    />
        <result property="alarmId"    column="alarm_id"    />
        <result property="alarmTime"    column="alarm_time"    />
        <result property="alarmUrl"    column="alarm_url"    />
        <result property="alarmLocation"    column="alarm_location"    />
        <result property="alarmAddress"    column="alarm_address"    />
        <result property="captureType"    column="capture_type"    />
        <result property="channelId"    column="channel_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectAlarmInfoVo">
        select id, device_id, device_name, core_video_id, analysis_id, alarm_code, alarm_type, alarm_type_name, alarm_id, alarm_time, alarm_url, alarm_location, alarm_address, capture_type, channel_id, create_time, create_by, update_time, update_by from tb_alarm_info
    </sql>

    <!-- 高性能查询：根据类型和时间区间查询报警信息列表 -->
    <select id="selectAlarmInfoByTypeAndTime" parameterType="com.onecity.os.video.domain.vo.AlarmInfoQueryVo" resultMap="AlarmInfoResult">
        <include refid="selectAlarmInfoVo"/>
        <where>
            <!-- 时间区间查询（必须条件，使用索引） -->
            <if test="startTime != null and endTime != null">
                and alarm_time >= #{startTime} and alarm_time &lt;= #{endTime}
            </if>
            <!-- 报警类型查询（可选条件） -->
            <if test="alarmTypeName != null and alarmTypeName != ''">
                and alarm_type_name = #{alarmTypeName}
            </if>
        </where>
        <!-- 按报警时间倒序排列，最新的在前面 -->
        order by alarm_time desc
    </select>

</mapper>
