<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.onecity.os.video.mapper.VideoAccessConfigMapper">
    
    <resultMap type="com.onecity.os.video.domain.VideoAccessConfig" id="VideoAccessConfigResult">
        <result property="id"                   column="id"                     />
        <result property="videoMenuId"          column="video_menu_id"          />
        <result property="videoMenuName"        column="video_menu_name"        />
        <result property="orderNum"             column="order_num"              />
        <result property="videoPointName"       column="video_point_name"       />
        <result property="decodeFormat"         column="decode_format"          />
        <result property="player"               column="player"                 />
        <result property="videoStream"          column="video_stream"           />
        <result property="extendId"             column="extend_id"              />
        <result property="deviceId"             column="device_id"              />
        <result property="channelId"            column="channel_id"             />
        <result property="videoUrl"             column="video_url"              />
        <result property="dataSource"           column="data_source"            />
        <result property="isDelete"             column="is_delete"              />
        <result property="updater"              column="updater"                />
        <result property="updateTime"           column="update_time"            />
        <result property="creater"              column="creater"                />
        <result property="createTime"           column="create_time"            />
    </resultMap>

    <sql id="selectVideoAccessConfigVo">
        select id, video_menu_id, video_menu_name, order_num, video_point_name, decode_format,
               player, video_stream, extend_id, device_id, channel_id, video_url, data_source, 
               is_delete, updater, update_time, creater, create_time
        from video_access_config
    </sql>

    <select id="selectVideoAccessConfigList" parameterType="com.onecity.os.video.domain.VideoAccessConfig" resultMap="VideoAccessConfigResult">
        <include refid="selectVideoAccessConfigVo"/>
        <where>
            <if test="videoMenuId != null and videoMenuId != ''">
                AND video_menu_id = #{videoMenuId}
            </if>
            <if test="videoMenuName != null and videoMenuName != ''">
                AND video_menu_name like concat('%', #{videoMenuName}, '%')
            </if>
            <if test="videoPointName != null and videoPointName != ''">
                AND video_point_name like concat('%', #{videoPointName}, '%')
            </if>
            <if test="decodeFormat != null and decodeFormat != ''">
                AND decode_format = #{decodeFormat}
            </if>
            <if test="player != null and player != ''">
                AND player = #{player}
            </if>
            <if test="deviceId != null and deviceId != ''">
                AND device_id = #{deviceId}
            </if>
            <if test="channelId != null and channelId != ''">
                AND channel_id = #{channelId}
            </if>
            <if test="dataSource != null and dataSource != ''">
                AND data_source = #{dataSource}
            </if>
            AND is_delete = '0'
        </where>
        order by order_num asc, create_time desc
    </select>

    <select id="selectVideoAccessConfigById" parameterType="String" resultMap="VideoAccessConfigResult">
        <include refid="selectVideoAccessConfigVo"/>
        where id = #{id} and is_delete = '0'
    </select>

    <select id="selectVideoAccessConfigByMenuId" parameterType="String" resultMap="VideoAccessConfigResult">
        <include refid="selectVideoAccessConfigVo"/>
        where video_menu_id = #{videoMenuId} and is_delete = '0'
        order by order_num asc, create_time desc
    </select>

    <select id="selectVideoAccessConfigVo3ByMenuId" parameterType="String" resultType="com.onecity.os.video.domain.vo.VideoAccessConfigVo3">
        select id, video_menu_id, video_menu_name, order_num, video_point_name, decode_format,
               player, video_stream, extend_id, device_id, channel_id, video_url, data_source
        from video_access_config
        where video_menu_id = #{videoMenuId} and is_delete = '0'
        order by order_num asc, create_time desc
    </select>

    <select id="checkDeviceChannelUnique" resultMap="VideoAccessConfigResult">
        <include refid="selectVideoAccessConfigVo"/>
        where device_id = #{deviceId} and channel_id = #{channelId} and is_delete = '0'
        <if test="id != null and id != ''">
            and id != #{id}
        </if>
        limit 1
    </select>

    <select id="checkExtendIdUnique" resultMap="VideoAccessConfigResult">
        <include refid="selectVideoAccessConfigVo"/>
        where extend_id = #{extendId} and is_delete = '0'
        <if test="id != null and id != ''">
            and id != #{id}
        </if>
        limit 1
    </select>

    <select id="countVideoAccessConfigByMenuId" parameterType="String" resultType="int">
        select count(1) from video_access_config 
        where video_menu_id = #{videoMenuId} and is_delete = '0'
    </select>


    <insert id="insertVideoAccessConfig" parameterType="com.onecity.os.video.domain.VideoAccessConfig">
        insert into video_access_config(
            <if test="id != null and id != ''">id,</if>
            <if test="videoMenuId != null and videoMenuId != ''">video_menu_id,</if>
            <if test="videoMenuName != null and videoMenuName != ''">video_menu_name,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="videoPointName != null and videoPointName != ''">video_point_name,</if>
            <if test="decodeFormat != null and decodeFormat != ''">decode_format,</if>
            <if test="player != null and player != ''">player,</if>
            <if test="videoStream != null and videoStream != ''">video_stream,</if>
            <if test="extendId != null and extendId != ''">extend_id,</if>
            <if test="deviceId != null and deviceId != ''">device_id,</if>
            <if test="channelId != null and channelId != ''">channel_id,</if>
            <if test="videoUrl != null and videoUrl != ''">video_url,</if>
            <if test="dataSource != null and dataSource != ''">data_source,</if>
            <if test="creater != null and creater != ''">creater,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null and updater != ''">updater,</if>
            <if test="updateTime != null ">update_time,</if>
            is_delete
        )values(
            <if test="id != null and id != ''">#{id},</if>
            <if test="videoMenuId != null and videoMenuId != ''">#{videoMenuId},</if>
            <if test="videoMenuName != null and videoMenuName != ''">#{videoMenuName},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="videoPointName != null and videoPointName != ''">#{videoPointName},</if>
            <if test="decodeFormat != null and decodeFormat != ''">#{decodeFormat},</if>
            <if test="player != null and player != ''">#{player},</if>
            <if test="videoStream != null and videoStream != ''">#{videoStream},</if>
            <if test="extendId != null and extendId != ''">#{extendId},</if>
            <if test="deviceId != null and deviceId != ''">#{deviceId},</if>
            <if test="channelId != null and channelId != ''">#{channelId},</if>
            <if test="videoUrl != null and videoUrl != ''">#{videoUrl},</if>
            <if test="dataSource != null and dataSource != ''">#{dataSource},</if>
            <if test="creater != null and creater != ''">#{creater},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null and updater != ''">#{updater},</if>
            <if test="updateTime != null ">#{updateTime},</if>
            '0'
        )
    </insert>

    <update id="updateVideoAccessConfig" parameterType="com.onecity.os.video.domain.VideoAccessConfig">
        update video_access_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="videoMenuId != null and videoMenuId != ''">video_menu_id = #{videoMenuId},</if>
            <if test="videoMenuName != null and videoMenuName != ''">video_menu_name = #{videoMenuName},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="videoPointName != null and videoPointName != ''">video_point_name = #{videoPointName},</if>
            <if test="decodeFormat != null">decode_format = #{decodeFormat},</if>
            <if test="player != null">player = #{player},</if>
            <if test="videoStream != null">video_stream = #{videoStream},</if>
            <if test="extendId != null">extend_id = #{extendId},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="channelId != null">channel_id = #{channelId},</if>
            <if test="videoUrl != null">video_url = #{videoUrl},</if>
            <if test="dataSource != null">data_source = #{dataSource},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateVideoMenuNameByMenuId">
        update video_access_config 
        set video_menu_name = #{videoMenuName}, update_time = sysdate()
        where video_menu_id = #{videoMenuId} and is_delete = '0'
    </update>

    <delete id="deleteVideoAccessConfigById" parameterType="String">
        delete from video_access_config where id = #{id}
    </delete>

    <delete id="deleteVideoAccessConfigByMenuIds" parameterType="java.util.List">
        delete from video_access_config where video_menu_id in
        <foreach item="videoMenuId" collection="list" open="(" separator="," close=")">
            #{videoMenuId}
        </foreach>
    </delete>

    <delete id="deleteVideoAccessConfigByIds" parameterType="String">
        delete from video_access_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 批量Upsert：根据videoMenuId+deviceId+channelId组合判断，存在则更新，不存在则插入 -->
    <insert id="batchUpsertVideoAccessConfig" parameterType="java.util.List">
        INSERT INTO video_access_config (
            id, video_menu_id, video_menu_name, order_num, video_point_name,
            decode_format, player, video_stream, extend_id, device_id, channel_id,
            video_url, data_source, creater, create_time, updater, update_time, is_delete
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.videoMenuId}, #{item.videoMenuName}, #{item.orderNum}, #{item.videoPointName},
                #{item.decodeFormat}, #{item.player}, #{item.videoStream}, #{item.extendId}, #{item.deviceId}, #{item.channelId},
                #{item.videoUrl}, #{item.dataSource}, #{item.creater}, #{item.createTime}, #{item.updater}, #{item.updateTime}, '0'
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            video_menu_name = VALUES(video_menu_name),
            order_num = VALUES(order_num),
            video_point_name = VALUES(video_point_name),
            decode_format = VALUES(decode_format),
            player = VALUES(player),
            video_stream = VALUES(video_stream),
            extend_id = VALUES(extend_id),
            video_url = VALUES(video_url),
            data_source = VALUES(data_source),
            updater = VALUES(updater),
            update_time = NOW(),
            is_delete = '0'
    </insert>


    <!-- 根据菜单ID获取视频配置列表（包含菜单名称） -->
    <resultMap type="com.onecity.os.video.domain.po.VideoAccessConfigPo" id="VideoAccessConfigPoResult">
        <result property="id" column="id"/>
        <result property="videoMenuId" column="video_menu_id"/>
        <result property="videoMenuName" column="video_menu_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="videoPointName" column="video_point_name"/>
        <result property="decodeFormat" column="decode_format"/>
        <result property="player" column="player"/>
        <result property="videoStream" column="video_stream"/>
        <result property="extendId" column="extend_id"/>
        <result property="deviceId" column="device_id"/>
        <result property="channelId" column="channel_id"/>
        <result property="videoOnlineFlag" column="video_online_flag"/>
        <result property="videoUrl" column="video_url"/>
        <result property="dataSource" column="data_source"/>
    </resultMap>

    <select id="selectVideoConfigsByMenuId" parameterType="String" resultMap="VideoAccessConfigPoResult">
        SELECT
            vac.id,
            vac.video_menu_id,
            vm.video_menu_name,
            vac.order_num,
            vac.video_point_name,
            vac.decode_format,
            vac.player,
            vac.video_stream,
            vac.extend_id,
            vac.device_id,
            vac.channel_id,
            '0' as video_online_flag,  <!-- 默认离线，在线状态将在Service层设置 -->
            vac.video_url,
            vac.data_source
        FROM video_access_config vac
        LEFT JOIN video_menu vm ON vac.video_menu_id = vm.video_menu_id AND vm.is_delete = '0'
        WHERE vac.video_menu_id = #{videoMenuId}
        AND vac.is_delete = '0'
        ORDER BY vac.order_num ASC, vac.id ASC
    </select>

    <!-- 根据菜单ID分页获取视频配置列表（用于三级菜单） -->
    <resultMap type="com.onecity.os.video.domain.po.VideoAccessConfigPo1" id="VideoAccessConfigPo1Result">
        <result property="id" column="id"/>
        <result property="videoMenuId" column="video_menu_id"/>
        <result property="videoMenuName" column="video_menu_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="videoPointName" column="video_point_name"/>
        <result property="decodeFormat" column="decode_format"/>
        <result property="player" column="player"/>
        <result property="videoStream" column="video_stream"/>
        <result property="extendId" column="extend_id"/>
        <result property="deviceId" column="device_id"/>
        <result property="channelId" column="channel_id"/>
        <result property="videoOnlineFlag" column="video_online_flag"/>
        <result property="videoUrl" column="video_url"/>
        <result property="dataSource" column="data_source"/>
    </resultMap>

    <select id="selectVideoConfigsByMenuIdWithPage" resultMap="VideoAccessConfigPo1Result">
        SELECT
            vac.id,
            vac.video_menu_id,
            vm.video_menu_name,
            vac.order_num,
            vac.video_point_name,
            vac.decode_format,
            vac.player,
            vac.video_stream,
            vac.extend_id,
            vac.device_id,
            vac.channel_id,
            '0' as video_online_flag,  <!-- 默认离线，在线状态将在Service层设置  -->
            vac.video_url,
            vac.data_source
        FROM video_access_config vac
        LEFT JOIN video_menu vm ON vac.video_menu_id = vm.video_menu_id AND vm.is_delete = '0'
        WHERE vac.video_menu_id = #{videoMenuId}
        AND vac.is_delete = '0'
        ORDER BY vac.order_num ASC, vac.create_time ASC
    </select>

    <select id="selectVideoConfigsByVideoPointName" resultMap="VideoAccessConfigPo1Result">
        SELECT
        vac.id,
        vac.video_menu_id,
        vm.video_menu_name,
        vac.order_num,
        vac.video_point_name,
        vac.decode_format,
        vac.player,
        vac.video_stream,
        vac.extend_id,
        vac.device_id,
        vac.channel_id,
        '0' as video_online_flag,  <!-- 默认离线，在线状态将在Service层设置  -->
        vac.video_url,
        vac.data_source
        FROM video_access_config vac
        LEFT JOIN video_menu vm ON vac.video_menu_id = vm.video_menu_id AND vm.is_delete = '0'
        WHERE vac.video_point_name LIKE CONCAT('%', #{videoPointName}, '%') and vac.video_menu_id = #{videoMenuId}
        AND vac.is_delete = '0'
        ORDER BY vac.order_num ASC, vac.create_time ASC
    </select>

    <select id="selectVideoConfigsByVideoPointNameAll" resultMap="VideoAccessConfigPo1Result">
        SELECT
        vac.id,
        vac.video_menu_id,
        vac.order_num,
        vac.video_point_name,
        vac.decode_format,
        vac.player,
        vac.video_stream,
        vac.extend_id,
        vac.device_id,
        vac.channel_id,
        '0' as video_online_flag,  <!-- 默认离线，在线状态将在Service层设置  -->
        vac.video_url,
        vac.data_source
        FROM video_access_config vac
        WHERE
        vac.is_delete = '0' and Instr(vac.video_point_name, #{videoPointName})
        ORDER BY vac.order_num ASC, vac.create_time ASC
    </select>



    <select id="selectVideoConfigsByMenuIdWithPage1" resultMap="VideoAccessConfigPo1Result">
        SELECT
        vac.id,
        vac.video_menu_id,
        vm.video_menu_name,
        vac.order_num,
        vac.video_point_name,
        vac.decode_format,
        vac.player,
        vac.video_stream,
        vac.extend_id,
        vac.device_id,
        vac.channel_id,
        '0' as video_online_flag,  <!-- 默认离线，在线状态将在Service层设置  -->
        vac.video_url,
        vac.data_source
        FROM video_access_config vac
        LEFT JOIN video_menu vm ON vac.video_menu_id = vm.video_menu_id AND vm.is_delete = '0'
        WHERE vac.video_menu_id = #{videoMenuId}
        AND vac.is_delete = '0'
        ORDER BY vac.order_num ASC, vac.create_time ASC
        LIMIT #{offset}, #{limit}
    </select>
    <!-- 批量根据菜单ID列表获取第一页视频配置（性能优化） -->
    <select id="selectFirstPageConfigsByMenuIds" resultMap="VideoAccessConfigPo1Result">
        SELECT
            vac.id,
            vac.video_menu_id,
            vm.video_menu_name,
            vac.order_num,
            vac.video_point_name,
            vac.decode_format,
            vac.player,
            vac.video_stream,
            vac.extend_id,
            vac.device_id,
            vac.channel_id,
            '0' as video_online_flag,  <!-- 默认离线，在线状态将在Service层设置 -->
            vac.video_url,
            vac.data_source
        FROM video_access_config vac
        LEFT JOIN video_menu vm ON vac.video_menu_id = vm.video_menu_id AND vm.is_delete = '0'
        WHERE vac.video_menu_id IN
        <foreach item="menuId" collection="list" open="(" separator="," close=")">
            #{menuId}
        </foreach>
        AND vac.is_delete = '0'
        AND vac.id IN (
            SELECT id FROM (
                SELECT id, ROW_NUMBER() OVER (PARTITION BY video_menu_id ORDER BY order_num ASC, id ASC) as rn
                FROM video_access_config
                WHERE video_menu_id IN
                <foreach item="menuId" collection="list" open="(" separator="," close=")">
                    #{menuId}
                </foreach>
                AND is_delete = '0'
            ) ranked
            WHERE rn <![CDATA[ <= ]]> #{limit}
        )
        ORDER BY vac.video_menu_id ASC, vac.order_num ASC, vac.id ASC
    </select>

    <!-- 根据视频点位名称搜索视频配置（包含菜单层级信息） -->
    <resultMap type="com.onecity.os.video.domain.po.VideoAccessConfigPo1" id="VideoAccessConfigPo1SearchResult" extends="VideoAccessConfigPo1Result">
        <result property="parentTreeName" column="parent_tree_name"/>
        <result property="treeId" column="tree_id"/>
    </resultMap>

    <select id="searchVideoConfigsByPointName" parameterType="String" resultMap="VideoAccessConfigPo1SearchResult">
        SELECT
            vac.id,
            vac.video_menu_id,
            vm.video_menu_name,
            vac.order_num,
            vac.video_point_name,
            vac.decode_format,
            vac.player,
            vac.video_stream,
            vac.extend_id,
            vac.device_id,
            vac.channel_id,
            '0' as video_online_flag,  /* 默认离线，在线状态将在Service层设置 */
            vac.video_url,
            vac.data_source,
            vm.tree_id,
            -- 直接返回tree_id，菜单层级名称将在Service层构建
            '' as parent_tree_name
        FROM video_access_config vac
        LEFT JOIN video_menu vm ON vac.video_menu_id = vm.video_menu_id AND vm.is_delete = '0'
        WHERE vac.is_delete = '0'
        AND instr(vac.video_point_name, #{videoPointName})
        ORDER BY
            vm.tree_id ASC,
            vac.order_num ASC,
            vac.id ASC
    </select>

    <!-- 根据菜单ID统计视频配置数量 -->
    <select id="countVideoConfigsByMenuId" parameterType="String" resultType="long">
        SELECT COUNT(1)
        FROM video_access_config vac
        WHERE vac.video_menu_id = #{videoMenuId}
        AND vac.is_delete = '0'
    </select>

    <!-- 批量根据菜单ID列表分页获取视频配置 -->
    <select id="selectVideoConfigsByMenuIdsWithPage" resultMap="VideoAccessConfigPo1Result">
        SELECT
            vac.id,
            vac.video_menu_id,
            vm.video_menu_name,
            vac.order_num,
            vac.video_point_name,
            vac.decode_format,
            vac.player,
            vac.video_stream,
            vac.extend_id,
            vac.device_id,
            vac.channel_id,
            '0' as video_online_flag,  /* 默认离线，在线状态将在Service层设置 */
            vac.video_url,
            vac.data_source
        FROM video_access_config vac
        LEFT JOIN video_menu vm ON vac.video_menu_id = vm.video_menu_id AND vm.is_delete = '0'
        WHERE vac.is_delete = '0'
        AND vac.video_menu_id IN
        <foreach collection="list" item="menuId" open="(" separator="," close=")">
            #{menuId}
        </foreach>
        ORDER BY vac.video_menu_id ASC, vac.order_num ASC, vac.id ASC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询所有视频配置 -->
    <select id="selectAllVideoConfigs" resultMap="VideoAccessConfigPo1Result">
        SELECT
            vac.id,
            vac.video_menu_id,
            vm.video_menu_name,
            vac.order_num,
            vac.video_point_name,
            vac.decode_format,
            vac.player,
            vac.video_stream,
            vac.extend_id,
            vac.device_id,
            vac.channel_id,
            '0' as video_online_flag,  /* 默认离线，在线状态将在Service层设置 */
            vac.video_url,
            vac.data_source
        FROM video_access_config vac
        LEFT JOIN video_menu vm ON vac.video_menu_id = vm.video_menu_id AND vm.is_delete = '0'
        WHERE vac.is_delete = '0'
        ORDER BY vac.video_menu_id ASC, vac.order_num ASC, vac.id ASC
    </select>

</mapper>
