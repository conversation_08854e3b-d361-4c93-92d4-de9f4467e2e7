<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.onecity.os</groupId>
    <artifactId>onecity-os</artifactId>
    <version>3.2.0</version>

    <name>onecity-os</name>
    <description>领导驾驶舱</description>

    <properties>
        <aliyun-java-sdk-dysmsapi.version>1.0.0</aliyun-java-sdk-dysmsapi.version>
        <ruoyi.version>3.2.0</ruoyi.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring-boot.version>2.6.6</spring-boot.version>
        <spring-cloud.version>2021.0.2</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.1</spring-cloud-alibaba.version>
        <alibaba.nacos.version>2.0.1</alibaba.nacos.version>
        <spring-boot-admin.version>2.5.2</spring-boot-admin.version>
        <springfox-boot.version>3.0.0</springfox-boot.version>
        <spring-boot.mybatis>2.2.0</spring-boot.mybatis>
        <spring-boot.mybatisPlus>3.5.1</spring-boot.mybatisPlus>
        <mybatisPlus.extension>3.5.1</mybatisPlus.extension>
        <spring-boot.annotation>3.4.1</spring-boot.annotation>
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <swagger.core.version>1.6.2</swagger.core.version>
        <swagger.bootstrap.ui.version>1.9.6</swagger.bootstrap.ui.version>
        <tobato.version>1.27.2</tobato.version>
        <kaptcha.version>2.3.2</kaptcha.version>
        <pagehelper.boot.version>1.4.0</pagehelper.boot.version>
        <pagehelper.version>5.3.0</pagehelper.version>
        <druid.version>1.2.8</druid.version>
        <dynamic-ds.version>3.4.1</dynamic-ds.version>
        <commons.io.version>2.11.0</commons.io.version>
        <commons.fileupload.version>1.4</commons.fileupload.version>
        <commons.beanutils.version>1.9.4</commons.beanutils.version>
        <commons.lang.version>2.6</commons.lang.version>
        <com.googlecode.aviator.aviator.version>5.4.3</com.googlecode.aviator.aviator.version>
        <commons.lang3.version>3.12.0</commons.lang3.version>
        <autopoi.web.version>1.3.6</autopoi.web.version>
        <fastdfs-client.version>1.27.2</fastdfs-client.version>
        <velocity.version>1.7</velocity.version>
<!--        <fastjson.version>1.2.83</fastjson.version>-->
        <fastjson.version>2.0.51</fastjson.version>
        <jjwt.version>0.9.1</jjwt.version>
        <minio.version>8.2.2</minio.version>
        <poi.version>4.1.2</poi.version>
        <common-pool.version>2.11.0</common-pool.version>
        <jackson-datatype.version>2.9.2</jackson-datatype.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <transmittable-thread-local.version>2.12.2</transmittable-thread-local.version>
        <lombok.version>1.18.22</lombok.version>
        <hutool.version>5.8.18</hutool.version>
        <xxljob.version>2.3.0</xxljob.version>
        <mapper.starter.version>2.1.5</mapper.starter.version>
        <spring-boot-dependencies.version>2.1.12.RELEASE</spring-boot-dependencies.version>
        <docker.repository>local.harbor.com</docker.repository>
        <docker.registry.name>onecity-os</docker.registry.name>
        <docker.maven.plugin.version>1.4.10</docker.maven.plugin.version>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <java.version>8</java.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>

    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Alibaba Nacos 配置 -->
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${alibaba.nacos.version}</version>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--  SpringBoot 监控客户端 -->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${springfox-boot.version}</version>
            </dependency>

            <!-- FastDFS 分布式文件系统 -->
            <dependency>
                <groupId>com.github.tobato</groupId>
                <artifactId>fastdfs-client</artifactId>
            </dependency>

            <!-- Mybatis 依赖配置 -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${spring-boot.mybatis}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${spring-boot.mybatisPlus}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${spring-boot.annotation}</version>
            </dependency>
            <!-- Swagger 依赖配置 -->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
                <version>${aliyun-java-sdk-dysmsapi.version}</version>
            </dependency>
            <!-- 验证码 -->
            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- 文件上传工具类 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.tobato</groupId>
                <artifactId>fastdfs-client</artifactId>
                <version>${fastdfs-client.version}</version>
            </dependency>
            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity</artifactId>
                <version>${velocity.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-collections</groupId>
                        <artifactId>commons-collections</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson-datatype.version}</version>
            </dependency>
            <!-- Collection 增强Java集合框架 -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>

            <!-- JSON 解析器和生成器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- 线程传递值 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <!-- 公共资源池 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${common-pool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.onecity.os</groupId>
                <artifactId>onecity-os-auth</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.onecity.os</groupId>
                <artifactId>onecity-os-system</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>
            <!-- 核心模块 -->
            <dependency>
                <groupId>com.onecity.os</groupId>
                <artifactId>onecity-os-common-core</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.onecity.os</groupId>
                <artifactId>onecity-os-common-swagger</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.onecity.os</groupId>
                <artifactId>onecity-os-common-security</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 权限范围 -->
            <dependency>
                <groupId>com.onecity.os</groupId>
                <artifactId>onecity-os-common-datascope</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 多数据源 -->
            <dependency>
                <groupId>com.onecity.os</groupId>
                <artifactId>onecity-os-common-datasource</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.onecity.os</groupId>
                <artifactId>onecity-os-common-log</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.onecity.os</groupId>
                <artifactId>onecity-os-common-redis</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.onecity.os</groupId>
                <artifactId>onecity-os-api-system</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>
            <!-- xxljob -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxljob.version}</version>
            </dependency>

            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper-spring-boot-starter</artifactId>
                <version>${mapper.starter.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>onecity-os-common</module>
        <module>onecity-os-auth</module>
        <module>onecity-os-gateway</module>
        <module>onecity-os-visual</module>
        <module>onecity-os-modules</module>
        <module>onecity-os-api</module>
    </modules>
    <packaging>pom</packaging>

    <dependencies>
        <!-- bootstrap 启动器 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot-dependencies.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>onecity-server</id>
            <url>http://192.168.108.1:31605/repository/onecity-releases/</url>
        </repository>
    </distributionManagement>
</project>